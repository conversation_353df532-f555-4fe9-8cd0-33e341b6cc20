import{j as e,B as t,G as s,R as l,e as i,L as u,a1 as m,r as h,D as w,aT as o}from"./mui-libs-CfwFIaTD.js";import{r as y,b as x}from"./react-libs-Cr2nE3UY.js";import{H as g}from"./index-Bpan7Tbe.js";import{P as b}from"./PageTitle-DA3BXQ4x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const c=[{id:"tutorial-1",title:"1. Getting Started with DataStatPro",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn the basics of navigating and using DataStatPro."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/A_3nZtut4g0?si=Xqm60Vvk3jrYBc9J",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-2",title:"2. Importing Your Data",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"A step-by-step guide on importing your datasets into the platform."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-3",title:"3. Performing Descriptive Analysis",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn how to get summary statistics and visualize your data distribution."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-4",title:"4. Performing t-tests",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn how to perform t-tests in DataStatPro."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-5",title:"5. Performing ANOVA tests",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn how to perform ANOVA tests in DataStatPro."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-6",title:"6. Performing Regression Analysis",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn how to perform regression analysis in DataStatPro."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-7",title:"7. Creating Visualizations",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn how to create various visualizations in DataStatPro."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})},{id:"tutorial-8",title:"8. Pivot Analysis",icon:e.jsx(o,{}),content:e.jsxs(t,{children:[e.jsx(i,{variant:"body1",paragraph:!0,children:"Learn how to perform pivot analysis in DataStatPro."}),e.jsx("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/QNp-WLkbLq0",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0})]})}],k=()=>{const n=y.useRef([]),p=r=>{var a;(a=n.current[r])==null||a.scrollIntoView({behavior:"smooth",block:"start"})};return e.jsxs(e.Fragment,{children:[e.jsxs(g,{children:[e.jsx("title",{children:"Video Tutorials - DataStatPro"}),e.jsx("meta",{name:"description",content:"Watch video tutorials to learn how to use DataStatPro for data analysis and visualization."})]}),e.jsxs(t,{sx:{p:3},children:[e.jsx(b,{title:"Video Tutorials"}),e.jsxs(s,{container:!0,spacing:3,sx:{mt:2},children:[e.jsx(s,{item:!0,xs:12,md:3,children:e.jsxs(l,{sx:{p:2,position:"sticky",top:"80px"},children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Tutorial Sections"}),e.jsx(u,{component:"nav",dense:!0,children:c.map((r,a)=>e.jsxs(m,{onClick:()=>p(a),children:[r.icon&&e.jsx(t,{sx:{mr:1.5,display:"flex",alignItems:"center",color:"primary.main"},children:x.cloneElement(r.icon,{fontSize:"small"})}),e.jsx(h,{primary:r.title.replace(/^\d+\.\s*/,""),primaryTypographyProps:{variant:"body2"}})]},r.id))})]})}),e.jsx(s,{item:!0,xs:12,md:9,children:c.map((r,a)=>e.jsxs(l,{sx:{p:3,mb:3},ref:d=>n.current[a]=d,id:r.id,children:[e.jsxs(i,{variant:"h5",gutterBottom:!0,component:"div",sx:{display:"flex",alignItems:"center"},children:[r.icon&&e.jsx(t,{sx:{mr:1,display:"flex",alignItems:"center",color:"primary.main"},children:r.icon}),r.title]}),e.jsx(w,{sx:{mb:2}}),r.content]},r.id))})]})]})]})};export{k as default};
