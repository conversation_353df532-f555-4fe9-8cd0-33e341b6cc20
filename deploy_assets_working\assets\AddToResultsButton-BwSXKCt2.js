import{j as e,f as n,b7 as T,bS as F,bT as M,bU as W,e as j,ai as w,b9 as k,ba as L,bb as E,B as O,h as G,ab as x,g as N,bV as U,bB as m}from"./mui-libs-CfwFIaTD.js";import{r as f}from"./react-libs-Cr2nE3UY.js";import{f as V,b as Y}from"./index-Bpan7Tbe.js";const K=({resultData:g,onSuccess:i,onError:c,disabled:b=!1,variant:p="contained",size:y="medium",fullWidth:I=!1,sx:C={}})=>{const{addResult:P,projects:o,currentProjectId:d,getProjectResults:R}=V(),{canAccessProFeatures:u}=Y(),[A,l]=f.useState(!1),[r,v]=f.useState(d||"default"),S=()=>{u&&o.length>1?l(!0):h(d||"default")},h=t=>{try{const s={...g,projectId:t==="default"?void 0:t},a=P(s);i&&i(a),l(!1)}catch(s){const a=s instanceof Error?s.message:"Failed to add result";c&&c(a)}},z=()=>{h(r)},B=t=>{const s=o.find(a=>a.id===t);return s?s.isLocal?e.jsx(m,{fontSize:"small"}):e.jsx(x,{fontSize:"small",color:"primary"}):e.jsx(m,{fontSize:"small"})},D=t=>{const s=o.find(a=>a.id===t);return s?s.name:"Default Project"};return e.jsxs(e.Fragment,{children:[e.jsx(n,{variant:p,color:"secondary",startIcon:e.jsx(T,{}),onClick:S,disabled:b,size:y,fullWidth:I,sx:{px:3,borderRadius:2,textTransform:"none",...C},children:"Add to Results Manager"}),e.jsxs(F,{open:A,onClose:()=>l(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(M,{children:"Add to Results Manager"}),e.jsxs(W,{children:[e.jsx(j,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Choose which project to add this result to:"}),e.jsxs(w,{fullWidth:!0,sx:{mt:2},children:[e.jsx(k,{id:"project-select-label",children:"Project"}),e.jsx(L,{labelId:"project-select-label",value:r,label:"Project",onChange:t=>v(t.target.value),children:o.map(t=>e.jsx(E,{value:t.id,children:e.jsxs(O,{display:"flex",alignItems:"center",gap:1,width:"100%",children:[B(t.id),e.jsx(j,{sx:{flexGrow:1},children:t.name}),e.jsx(G,{label:R(t.id).length,size:"small",variant:"outlined",sx:{minWidth:"auto",height:20}}),!t.isLocal&&e.jsx(x,{fontSize:"small",color:"primary"})]})},t.id))})]}),u&&e.jsx(N,{severity:"info",sx:{mt:2},children:"Results are automatically organized by project. You can move results between projects later in the Results Manager."})]}),e.jsxs(U,{children:[e.jsx(n,{onClick:()=>l(!1),children:"Cancel"}),e.jsxs(n,{onClick:z,variant:"contained",color:"secondary",children:["Add to ",D(r)]})]})]})]})};export{K as A};
