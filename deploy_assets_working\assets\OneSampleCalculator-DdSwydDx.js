import{u as V,j as e,B as i,bL as ee,bM as F,G as n,R as I,e as o,bN as q,aE as j,I as g,bs as M,ah as z,f as te,bH as re,bO as ae,bP as oe,k as se,i as ie,l as ne}from"./mui-libs-CfwFIaTD.js";import{r as d}from"./react-libs-Cr2nE3UY.js";import{g as D}from"./sampleSize-nX7GCAZC.js";import{R,d as O,C as A,X as W,Y as L,T as N,e as G}from"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const he=()=>{const l=V(),[s,K]=d.useState("proportion"),[h,S]=d.useState(.95),[p,v]=d.useState(.05),[f,C]=d.useState(.5),[m,E]=d.useState(10),[a,T]=d.useState(null),[u,Y]=d.useState(null),[P,_]=d.useState([]);d.useEffect(()=>{H(),X()},[h,p,f,m,s,a]);const H=()=>{const t=D(1-(1-h)/2);let r;if(s==="proportion"){const $=f,k=1-$;r=Math.pow(t,2)*$*k/Math.pow(p,2)}else r=Math.pow(t,2)*Math.pow(m,2)/Math.pow(p,2);let x;a&&a>0?s==="proportion"?x=Math.ceil(r/(1+(r-1)/a)):x=Math.ceil(r/(1+r/a)):x=Math.ceil(r),Y(x)},X=()=>{const t=[],r=Math.max(10,Math.floor(u?u*.5:20)),x=Math.ceil(u?u*1.5:100),k=Math.max(1,Math.floor((x-r)/(7-1)));for(let c=r;c<=x;c+=k){const y=D(1-(1-h)/2);let b;if(s==="proportion"){const B=f,w=1-B;a&&a>0?b=y*Math.sqrt(B*w/c)*Math.sqrt((a-c)/(a-1)):b=y*Math.sqrt(B*w/c)}else a&&a>0?b=y*(m/Math.sqrt(c))*Math.sqrt((a-c)/(a-1)):b=y*(m/Math.sqrt(c));t.push({sampleSize:c,marginOfError:b})}_(t)},Z=()=>{if(u){let t="";const r=a&&a>0?` from a population of ${a}`:"";s==="proportion"?t=`Required Sample Size: ${u} for estimating a population proportion of ${Math.round(f*100)}% with a margin of error of ±${Math.round(p*100)}% at a ${Math.round(h*100)}% confidence level${r}.`:t=`Required Sample Size: ${u} for estimating a population mean with a standard deviation of ${m} and a margin of error of ±${p} at a ${Math.round(h*100)}% confidence level${r}.`,navigator.clipboard.writeText(t)}},J=()=>{console.log("Export PDF functionality would go here")},Q=()=>{S(.95),v(.05),C(.5),E(10),T(null)},U=(t,r)=>{r!==null&&(K(r),r==="proportion"?(S(.95),v(.05),C(.5)):r==="mean"&&(S(.95),v(1.5),E(10)))};return e.jsxs(e.Fragment,{children:[e.jsx(i,{sx:{mb:3},children:e.jsxs(ee,{value:s,exclusive:!0,onChange:U,"aria-label":"calculator type",fullWidth:!0,children:[e.jsx(F,{value:"proportion","aria-label":"based on proportion",sx:{"&.Mui-selected":{bgcolor:l.palette.primary.main,color:l.palette.primary.contrastText,"&:hover":{bgcolor:l.palette.primary.dark}}},children:"Based on Proportion"}),e.jsx(F,{value:"mean","aria-label":"based on mean",sx:{"&.Mui-selected":{bgcolor:l.palette.secondary.main,color:l.palette.secondary.contrastText,"&:hover":{bgcolor:l.palette.secondary.dark}}},children:"Based on Mean"})]})}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(I,{elevation:1,sx:{p:3},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Input Parameters"}),e.jsxs(i,{sx:{mb:4},children:[e.jsx(o,{gutterBottom:!0,children:"Confidence Level"}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(q,{value:h*100,onChange:(t,r)=>S(r/100),step:1,min:80,max:99,marks:[{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"},{value:99,label:"99%"}]})})})]}),s==="proportion"&&e.jsxs(i,{sx:{mb:4},children:[e.jsx(o,{gutterBottom:!0,children:"Margin of Error"}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(q,{value:p*100,onChange:(t,r)=>v(r/100),step:.5,min:1,max:10,marks:[{value:1,label:"1%"},{value:5,label:"5%"},{value:10,label:"10%"}]})})})]}),s==="mean"&&e.jsxs(i,{sx:{mb:4},children:[e.jsxs(o,{gutterBottom:!0,children:["Margin of Error (Absolute)",e.jsx(j,{title:"The maximum difference between the sample mean and the true population mean that you're willing to accept.",children:e.jsx(g,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsx(z,{type:"number",value:p,onChange:t=>v(Number(t.target.value)),inputProps:{min:.1,step:.1},fullWidth:!0})]}),s==="proportion"?e.jsxs(i,{sx:{mb:4},children:[e.jsxs(o,{gutterBottom:!0,children:["Expected Proportion",e.jsx(j,{title:"Your best guess of the proportion in the population. If unsure, use 50% for the most conservative estimate.",children:e.jsx(g,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsxs(n,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(n,{item:!0,xs:!0,children:e.jsx(z,{value:Math.round(f*100),onChange:t=>C(Number(t.target.value)/100),type:"number",inputProps:{min:1,max:99,step:1},fullWidth:!0})}),e.jsx(n,{item:!0,children:e.jsx(o,{children:"%"})})]})]}):e.jsxs(i,{sx:{mb:4},children:[e.jsxs(o,{gutterBottom:!0,children:["Standard Deviation",e.jsx(j,{title:"Your best estimate of the population standard deviation. This is a measure of how spread out the values are.",children:e.jsx(g,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsx(z,{type:"number",value:m,onChange:t=>E(Number(t.target.value)),inputProps:{min:.1,step:.1},fullWidth:!0})]}),e.jsxs(i,{sx:{mb:4},children:[e.jsxs(o,{gutterBottom:!0,children:["Population Size (N)",e.jsx(j,{title:"The total number of individuals or items in the population. Leave blank for an infinite population assumption.",children:e.jsx(g,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsx(z,{type:"number",value:a===null?"":a,onChange:t=>T(t.target.value===""?null:Number(t.target.value)),inputProps:{min:1,step:1},fullWidth:!0,placeholder:"Optional"})]}),e.jsx(te,{variant:"outlined",startIcon:e.jsx(re,{}),onClick:Q,fullWidth:!0,sx:{mt:2},children:"Reset"})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(I,{elevation:1,sx:{p:3},children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(o,{variant:"h6",children:"Results"}),e.jsxs(i,{children:[e.jsx(j,{title:"Copy results",children:e.jsx(g,{onClick:Z,children:e.jsx(ae,{})})}),e.jsx(j,{title:"Export as PDF",children:e.jsx(g,{onClick:J,children:e.jsx(oe,{})})})]})]}),e.jsx(se,{sx:{mb:4,bgcolor:ie(l.palette.primary.main,.1)},children:e.jsxs(ne,{sx:{textAlign:"center"},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Required Sample Size"}),e.jsx(o,{variant:"h2",color:"primary",children:u})]})}),s==="proportion"&&e.jsxs(e.Fragment,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Margin of Error by Sample Size"}),e.jsx(i,{sx:{height:300,mb:2},children:e.jsx(R,{width:"100%",height:"100%",children:e.jsxs(O,{data:P,margin:{top:5,right:30,left:20,bottom:20},children:[e.jsx(A,{strokeDasharray:"3 3"}),e.jsx(W,{dataKey:"sampleSize",label:{value:"Sample Size",position:"outerBottom",offset:25,style:{fontSize:"12px"}},tickCount:7,interval:"preserveStartEnd",height:70}),e.jsx(L,{label:{value:"Margin of Error",angle:-90,position:"outside",offset:-40,style:{fontSize:"12px"}},tickFormatter:t=>`${Math.round(t*100)}%`,width:100}),e.jsx(N,{formatter:t=>[`${(t*100).toFixed(2)}%`,"Margin of Error"],labelFormatter:t=>`Sample Size: ${t}`}),e.jsx(G,{type:"monotone",dataKey:"marginOfError",stroke:l.palette.primary.main,activeDot:{r:8},strokeWidth:2})]})})})]}),s==="mean"&&e.jsxs(e.Fragment,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Margin of Error (Absolute) by Sample Size"}),e.jsx(i,{sx:{height:300,mb:2},children:e.jsx(R,{width:"100%",height:"100%",children:e.jsxs(O,{data:P,margin:{top:5,right:30,left:20,bottom:20},children:[e.jsx(A,{strokeDasharray:"3 3"}),e.jsx(W,{dataKey:"sampleSize",label:{value:"Sample Size",position:"outerBottom",offset:15,style:{fontSize:"12px"}},tickCount:7,interval:"preserveStartEnd",height:70}),e.jsx(L,{label:{value:"Margin of Error (Absolute)",angle:-90,position:"outside",offset:-60,style:{fontSize:"12px"}},tickFormatter:t=>`${t.toFixed(2)}`,width:100}),e.jsx(N,{formatter:t=>[`${t.toFixed(2)}`,"Margin of Error (Absolute)"],labelFormatter:t=>`Sample Size: ${t}`}),e.jsx(G,{type:"monotone",dataKey:"marginOfError",stroke:l.palette.primary.main,activeDot:{r:8},strokeWidth:2})]})})})]}),e.jsxs(i,{sx:{mt:3},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Interpretation"}),e.jsxs(o,{variant:"body1",children:["A sample size of ",u," is needed to estimate a population ",s==="proportion"?"proportion":"mean",s==="proportion"?` of ${Math.round(f*100)}%`:` with a standard deviation of ${m}`," with a margin of error of ",s==="proportion"?`±${Math.round(p*100)}%`:`±${p}`," at a ",Math.round(h*100),"% confidence level",a&&a>0?` from a population of ${a}.`:"."]})]})]})})]})]})};export{he as default};
