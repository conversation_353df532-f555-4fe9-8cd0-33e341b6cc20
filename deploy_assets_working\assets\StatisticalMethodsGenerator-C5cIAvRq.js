import{j as s,B as d,X as Z,e as a,g as z,G as h,k as C,l as A,ai as E,b9 as L,ba as O,bb as S,ah as m,D as F,f as v,Q as D,b_ as ee,L as se,m as ne,r as ie,I as te,bj as ae,bO as le,bw as re,R as G}from"./mui-libs-CfwFIaTD.js";import{r as y}from"./react-libs-Cr2nE3UY.js";import{b as oe,a as de,D as b}from"./index-Bpan7Tbe.js";import{A as ce}from"./AddToResultsButton-BwSXKCt2.js";import{V as I}from"./VariableSelector-CPdlCsJ2.js";import{P as pe}from"./PublicationReadyGate-BGFbKbJc.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const he="All statistical analyses were performed using DataStatPro statistical software (DataStatPro, 2024).",ue="Relevant statistical assumptions were assessed prior to conducting each analysis, with appropriate alternative methods employed when assumptions were violated.",me=["Descriptive Statistics","Independent samples t-test","Paired samples t-test","One-sample t-test","One-way ANOVA","Two-way ANOVA","Repeated Measures ANOVA","Post-Hoc Tests","Linear Regression","Logistic Regression","Correlation Analysis","Chi-Square Test","Mann-Whitney U Test","Wilcoxon Signed-Rank Test","Kruskal-Wallis Test","Friedman Test","Reliability Analysis","Factor Analysis","Cluster Analysis","Survival Analysis","Log-Rank Test","Cox Regression","Kaplan Meier Curves","Meta-Analysis","Standardized Mean Difference (SMD)"],be=[{value:"descriptive",label:"Descriptive"},{value:"comparison",label:"Comparison"},{value:"relationship",label:"Relationship"},{value:"prediction",label:"Prediction"},{value:"other",label:"Other"}],Me=()=>{const{canAccessProFeatures:xe}=oe(),{datasets:j,currentDataset:T,setCurrentDataset:U}=de(),[i,g]=y.useState({analysisType:"",analysisPurpose:"",dependentVariable:"",independentVariables:[],covariates:[],demographics:[]}),[l,N]=y.useState({selectedDatasetId:(T==null?void 0:T.id)||"",sampleSize:"",significanceLevel:"0.05",additionalNotes:""}),[c,M]=y.useState([]),[w,$]=y.useState(""),[R,W]=y.useState(!1),[x,V]=y.useState(""),p=j.find(e=>e.id===l.selectedDatasetId)||T,f=(()=>{const e=i.analysisType.includes("Descriptive"),n=i.analysisType.includes("t-test")||i.analysisType.includes("ANOVA")||i.analysisType.includes("Post-Hoc")||i.analysisType.includes("Chi-Square")||i.analysisType.includes("Mann-Whitney")||i.analysisType.includes("Wilcoxon")||i.analysisType.includes("Kruskal-Wallis")||i.analysisType.includes("Friedman"),t=i.analysisType.includes("Regression"),r=i.analysisType.includes("Log-Rank")||i.analysisType.includes("Cox Regression")||i.analysisType.includes("Kaplan Meier")||i.analysisType.includes("Survival Analysis"),o=i.analysisType.includes("Meta-Analysis")||i.analysisType.includes("SMD"),k=i.analysisType.includes("Correlation");return i.analysisType.includes("Reliability"),i.analysisType.includes("Factor"),i.analysisType.includes("Cluster"),{showDemographics:e||n||t||r||o||k,showDependent:n||t||r,showIndependent:n||t||r||k||o,showCovariates:t||r}})(),H=e=>{const n=e.target.value;N(r=>({...r,selectedDatasetId:n}));const t=j.find(r=>r.id===n);t&&U(t),g({analysisType:"",analysisPurpose:"",dependentVariable:"",independentVariables:[],covariates:[],demographics:[]})},K=e=>{g(n=>({...n,analysisType:e.target.value,dependentVariable:"",independentVariables:[],covariates:[],demographics:[]}))},B=e=>{g(n=>({...n,analysisPurpose:e.target.value}))},u=(e,n)=>{g(t=>({...t,[e]:n}))},P=(e,n)=>{N(t=>({...t,[e]:n}))},Y=e=>{let n="";if(e.analysisType.includes("Descriptive"))n="Descriptive statistics including measures of central tendency and variability were calculated",e.demographics.length>0&&(n+=` for ${e.demographics.join(", ")}`),n+=".";else if(e.analysisType.includes("Independent samples t-test")){if(n=`Independent samples t-tests were conducted to compare means of ${e.dependentVariable}`,e.independentVariables.length>0||e.demographics.length>0){const t=[...e.independentVariables,...e.demographics];n+=` between various groups of ${t.join(", ")}`}n+="."}else e.analysisType.includes("Paired samples t-test")?n=`Paired samples t-tests were performed to compare pre- and post-intervention measurements of ${e.dependentVariable}.`:e.analysisType.includes("One-sample t-test")?n=`One-sample t-tests were used to compare sample means of ${e.dependentVariable} against known population values.`:e.analysisType.includes("One-way ANOVA")?(n=`One-way analysis of variance (ANOVA) was conducted to compare means of ${e.dependentVariable}`,e.independentVariables.length>0&&(n+=` across groups of ${e.independentVariables.join(", ")}`),n+="."):e.analysisType.includes("Two-way ANOVA")?n=`Two-way ANOVA was performed to examine main effects and interactions between factors on ${e.dependentVariable}.`:e.analysisType.includes("Repeated Measures ANOVA")?n=`Repeated measures ANOVA was used to analyze within-subjects changes in ${e.dependentVariable} over time.`:e.analysisType.includes("Linear Regression")?(n=`Multiple linear regression analysis was conducted to examine relationships between variables and predict ${e.dependentVariable}`,e.independentVariables.length>0&&(n+=` using ${e.independentVariables.join(", ")} as predictors`),e.covariates.length>0&&(n+=` while controlling for ${e.covariates.join(", ")}`),n+="."):e.analysisType.includes("Logistic Regression")?(n=`Logistic regression analysis was performed to model ${e.dependentVariable} and identify predictors`,e.independentVariables.length>0&&(n+=` including ${e.independentVariables.join(", ")}`),n+="."):e.analysisType.includes("Correlation")?(n="Correlation analysis was conducted to examine relationships between continuous variables",e.independentVariables.length>0&&(n+=` including ${e.independentVariables.join(", ")}`),n+="."):e.analysisType.includes("Chi-Square")?(n="Chi-square tests of independence were performed to examine associations between categorical variables",e.independentVariables.length>0&&(n+=` including ${e.independentVariables.join(", ")}`),n+="."):e.analysisType.includes("Mann-Whitney")?n=`Mann-Whitney U tests were conducted as non-parametric alternatives to compare ${e.dependentVariable} between groups.`:e.analysisType.includes("Wilcoxon")?n=`Wilcoxon signed-rank tests were performed as non-parametric alternatives to compare ${e.dependentVariable} measurements.`:e.analysisType.includes("Kruskal-Wallis")?n=`Kruskal-Wallis tests were conducted as non-parametric alternatives to compare ${e.dependentVariable} across multiple groups.`:e.analysisType.includes("Friedman")?n=`Friedman tests were performed as non-parametric alternatives to analyze changes in ${e.dependentVariable} over time.`:e.analysisType.includes("Post-Hoc")?(n="Post-hoc tests were conducted to examine pairwise comparisons following significant ANOVA results",e.dependentVariable&&(n+=` for ${e.dependentVariable}`),e.independentVariables.length>0&&(n+=` across groups of ${e.independentVariables.join(", ")}`),n+=". Multiple comparison corrections were applied to control for Type I error."):e.analysisType.includes("Log-Rank")?(n="Log-rank tests were performed to compare survival distributions",e.dependentVariable&&(n+=` for ${e.dependentVariable}`),e.independentVariables.length>0&&(n+=` between groups defined by ${e.independentVariables.join(", ")}`),n+="."):e.analysisType.includes("Cox Regression")?(n="Cox proportional hazards regression analysis was conducted to examine the relationship between survival time",e.dependentVariable&&(n+=` (${e.dependentVariable})`),n+=" and predictor variables",e.independentVariables.length>0&&(n+=` including ${e.independentVariables.join(", ")}`),e.covariates.length>0&&(n+=` while controlling for ${e.covariates.join(", ")}`),n+=". The proportional hazards assumption was assessed prior to analysis."):e.analysisType.includes("Kaplan Meier")?(n="Kaplan-Meier survival curves were generated to estimate survival probabilities",e.dependentVariable&&(n+=` for ${e.dependentVariable}`),e.independentVariables.length>0&&(n+=` stratified by ${e.independentVariables.join(", ")}`),n+=". Median survival times and confidence intervals were calculated."):e.analysisType.includes("SMD")?(n="Standardized mean differences (SMD) were calculated to assess effect sizes",e.dependentVariable&&(n+=` for ${e.dependentVariable}`),e.independentVariables.length>0&&(n+=` between groups defined by ${e.independentVariables.join(", ")}`),n+=". Cohen's conventions were used to interpret effect size magnitudes (small: 0.2, medium: 0.5, large: 0.8)."):e.analysisType.includes("Reliability")?n="Reliability analysis was conducted to assess internal consistency of measurement scales.":e.analysisType.includes("Factor")?n="Factor analysis was performed to identify underlying constructs and reduce dimensionality.":e.analysisType.includes("Cluster")?n="Cluster analysis was conducted to identify distinct groups within the data.":e.analysisType.includes("Survival")?n="Survival analysis methods were employed to analyze time-to-event data.":e.analysisType.includes("Meta-Analysis")&&(n="Meta-analysis was conducted to synthesize effect sizes across multiple studies",e.independentVariables.length>0&&(n+=` examining ${e.independentVariables.join(", ")}`),n+=". Random-effects models were used to account for heterogeneity between studies.");return n},_=()=>{if(!i.analysisType){console.warn("Please select an analysis type.");return}if(!i.analysisPurpose){console.warn("Please select an analysis purpose.");return}if(f.showDependent&&!i.dependentVariable){console.warn("Please select a dependent variable for this analysis type.");return}const e=Y(i),n={id:Date.now().toString(),analysisType:i.analysisType,analysisPurpose:i.analysisPurpose,dependentVariable:i.dependentVariable,independentVariables:[...i.independentVariables],covariates:[...i.covariates],demographics:[...i.demographics],generatedText:e};M(t=>[...t,n]),g({analysisType:"",analysisPurpose:"",dependentVariable:"",independentVariables:[],covariates:[],demographics:[]}),console.log("Analysis added to methods section!")},q=e=>{M(n=>n.filter(t=>t.id!==e)),console.log("Analysis removed from methods section.")},Q=()=>{M([]),$(""),V(""),console.log("All analyses cleared.")};y.useEffect(()=>{if(c.length===0){$(""),V("");return}let e="";l.sampleSize&&(e+=`The study included ${l.sampleSize} participants. `);const n=c.map(t=>t.generatedText);e+=n.join(" "),e+=` ${ue}`,e+=` Statistical significance was set at p < ${l.significanceLevel} for all analyses.`,e+=` ${he}`,l.additionalNotes.trim()&&(e+=` ${l.additionalNotes.trim()}`),$(e),V(e),W(!1)},[c,l.sampleSize,l.significanceLevel,l.additionalNotes]);const X=async()=>{try{await navigator.clipboard.writeText(x),console.log("Text copied to clipboard!")}catch(e){console.error("Failed to copy text to clipboard.",e)}},J=()=>{const e=`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Statistical Methods Section</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
          .methods-section { max-width: 800px; margin: 0 auto; }
          h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
          p { text-align: justify; margin-bottom: 15px; }
          .analysis-list { margin-bottom: 20px; }
          .analysis-item { margin-bottom: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="methods-section">
          <h1>Statistical Methods</h1>
          ${c.length>0?`
            <div class="analysis-list">
              <h2>Analyses Performed:</h2>
              ${c.map(o=>`
                <div class="analysis-item">
                  <strong>${o.analysisType}</strong> (${o.analysisPurpose})
                  ${o.dependentVariable?`<br>Dependent Variable: ${o.dependentVariable}`:""}
                  ${o.independentVariables.length>0?`<br>Independent Variables: ${o.independentVariables.join(", ")}`:""}
                  ${o.covariates.length>0?`<br>Covariates: ${o.covariates.join(", ")}`:""}
                  ${o.demographics.length>0?`<br>Demographics: ${o.demographics.join(", ")}`:""}
                </div>
              `).join("")}
            </div>
          `:""}
          <h2>Methods Text:</h2>
          <p>${x}</p>
        </div>
      </body>
      </html>
    `,n=new Blob([e],{type:"text/html"}),t=URL.createObjectURL(n),r=document.createElement("a");r.href=t,r.download="statistical-methods.html",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t),console.log("Methods section exported as HTML!")};return s.jsx(pe,{children:s.jsxs(d,{sx:{p:3,maxWidth:1200,mx:"auto"},children:[s.jsxs(d,{sx:{mb:3},children:[s.jsxs(d,{sx:{display:"flex",alignItems:"center",mb:2},children:[s.jsx(Z,{sx:{mr:2,color:"primary.main",fontSize:32}}),s.jsx(a,{variant:"h4",component:"h1",children:"Statistical Methods Generator"})]}),s.jsx(a,{variant:"body1",color:"text.secondary",sx:{mb:2},children:"Generate publication-ready Statistical Methods sections using an iterative approach. Add one analysis at a time to build a comprehensive methods section."}),j.length===0&&s.jsxs(z,{severity:"warning",sx:{mb:2},children:[s.jsx(a,{variant:"subtitle2",component:"span",sx:{fontWeight:600,mb:1},children:"No Datasets Available"}),s.jsx(a,{variant:"body2",component:"span",children:"Please load a dataset first to access variable selection features. You can still use the tool with manual variable entry."})]}),s.jsxs(z,{severity:"info",sx:{mb:2},children:[s.jsx(a,{variant:"subtitle2",component:"span",sx:{fontWeight:600,mb:1},children:"How to use this tool:"}),s.jsxs(d,{component:"ol",sx:{m:0,pl:2},children:[s.jsx("li",{children:"Select ONE analysis type you performed"}),s.jsx("li",{children:"Choose the purpose of your analysis"}),s.jsx("li",{children:"Select variables from your dataset (or type manually)"}),s.jsx("li",{children:'Click "Add to Methods" to add this analysis'}),s.jsx("li",{children:"Repeat for each additional analysis"}),s.jsx("li",{children:"Review and customize the combined methods text"}),s.jsx("li",{children:"Export or save to Results Manager"})]})]})]}),s.jsxs(h,{container:!0,spacing:3,children:[s.jsx(h,{item:!0,xs:12,children:s.jsx(C,{children:s.jsxs(A,{children:[s.jsx(a,{variant:"h6",sx:{mb:2},children:"Dataset Selection & Study Parameters"}),s.jsxs(h,{container:!0,spacing:2,children:[s.jsx(h,{item:!0,xs:12,md:4,children:s.jsxs(E,{fullWidth:!0,children:[s.jsx(L,{children:"Select Dataset"}),s.jsxs(O,{value:l.selectedDatasetId,onChange:H,children:[s.jsx(S,{value:"",children:s.jsx("em",{children:"No dataset selected"})}),j.map(e=>s.jsx(S,{value:e.id,children:e.name},e.id))]})]})}),s.jsx(h,{item:!0,xs:12,md:3,children:s.jsx(m,{fullWidth:!0,label:"Sample Size",value:l.sampleSize,onChange:e=>P("sampleSize",e.target.value),placeholder:"e.g., N = 150"})}),s.jsx(h,{item:!0,xs:12,md:2,children:s.jsx(m,{fullWidth:!0,label:"Significance Level",value:l.significanceLevel,onChange:e=>P("significanceLevel",e.target.value),placeholder:"0.05"})}),s.jsx(h,{item:!0,xs:12,md:3,children:s.jsx(m,{fullWidth:!0,label:"Additional Notes",value:l.additionalNotes,onChange:e=>P("additionalNotes",e.target.value),placeholder:"Study-wide notes..."})})]})]})})}),s.jsxs(h,{item:!0,xs:12,md:6,children:[s.jsx(C,{children:s.jsxs(A,{children:[s.jsx(a,{variant:"h6",sx:{mb:2},children:"Add Single Analysis"}),s.jsxs(E,{fullWidth:!0,sx:{mb:2},children:[s.jsx(L,{children:"Type of Analysis *"}),s.jsx(O,{value:i.analysisType,onChange:K,children:me.map(e=>s.jsx(S,{value:e,children:e},e))})]}),s.jsxs(E,{fullWidth:!0,sx:{mb:2},children:[s.jsx(L,{children:"Analysis Purpose *"}),s.jsx(O,{value:i.analysisPurpose,onChange:B,children:be.map(e=>s.jsx(S,{value:e.value,children:e.label},e.value))})]}),s.jsx(F,{sx:{my:2}}),f.showDependent&&s.jsxs(d,{sx:{mb:2},children:[s.jsx(a,{variant:"subtitle2",sx:{mb:1},children:"Dependent Variable *"}),p?s.jsx(I,{datasetId:p.id,value:i.dependentVariable,onChange:e=>u("dependentVariable",e),allowedTypes:[b.NUMERIC,b.CATEGORICAL],placeholder:"Select dependent variable",multiple:!1}):s.jsx(m,{fullWidth:!0,value:i.dependentVariable,onChange:e=>u("dependentVariable",e.target.value),placeholder:"Enter dependent variable name"})]}),f.showIndependent&&s.jsxs(d,{sx:{mb:2},children:[s.jsx(a,{variant:"subtitle2",sx:{mb:1},children:"Independent Variables"}),p?s.jsx(I,{datasetId:p.id,value:i.independentVariables,onChange:e=>u("independentVariables",e),allowedTypes:[b.NUMERIC,b.CATEGORICAL],placeholder:"Select independent variables",multiple:!0}):s.jsx(m,{fullWidth:!0,value:i.independentVariables.join(", "),onChange:e=>u("independentVariables",e.target.value.split(",").map(n=>n.trim()).filter(n=>n)),placeholder:"Enter independent variable names (comma-separated)"})]}),f.showCovariates&&s.jsxs(d,{sx:{mb:2},children:[s.jsx(a,{variant:"subtitle2",sx:{mb:1},children:"Covariates"}),p?s.jsx(I,{datasetId:p.id,value:i.covariates,onChange:e=>u("covariates",e),allowedTypes:[b.NUMERIC,b.CATEGORICAL],placeholder:"Select covariates",multiple:!0}):s.jsx(m,{fullWidth:!0,value:i.covariates.join(", "),onChange:e=>u("covariates",e.target.value.split(",").map(n=>n.trim()).filter(n=>n)),placeholder:"Enter covariate names (comma-separated)"})]}),f.showDemographics&&s.jsxs(d,{sx:{mb:2},children:[s.jsx(a,{variant:"subtitle2",sx:{mb:1},children:"Demographics Variables"}),p?s.jsx(I,{datasetId:p.id,value:i.demographics,onChange:e=>u("demographics",e),allowedTypes:[b.NUMERIC,b.CATEGORICAL],placeholder:"Select demographic variables",multiple:!0}):s.jsx(m,{fullWidth:!0,value:i.demographics.join(", "),onChange:e=>u("demographics",e.target.value.split(",").map(n=>n.trim()).filter(n=>n)),placeholder:"Enter demographic variable names (comma-separated)"})]}),s.jsx(F,{sx:{my:2}}),s.jsx(v,{variant:"contained",startIcon:s.jsx(D,{}),onClick:_,disabled:!i.analysisType||!i.analysisPurpose,fullWidth:!0,sx:{mb:2},children:"Add to Methods"}),c.length>0&&s.jsx(v,{variant:"outlined",startIcon:s.jsx(ee,{}),onClick:Q,fullWidth:!0,color:"warning",children:"Clear All Analyses"})]})}),c.length>0&&s.jsx(C,{sx:{mt:2},children:s.jsxs(A,{children:[s.jsxs(a,{variant:"h6",sx:{mb:2},children:["Added Analyses (",c.length,")"]}),s.jsx(se,{children:c.map(e=>s.jsx(ne,{sx:{border:"1px solid",borderColor:"divider",borderRadius:1,mb:1,bgcolor:"background.paper"},secondaryAction:s.jsx(te,{edge:"end",onClick:()=>q(e.id),color:"error",children:s.jsx(ae,{})}),children:s.jsx(ie,{primary:e.analysisType,primaryTypographyProps:{component:"div"},secondary:s.jsxs(d,{children:[s.jsxs(a,{variant:"body2",component:"span",color:"text.secondary",sx:{display:"block"},children:["Purpose: ",e.analysisPurpose]}),e.dependentVariable&&s.jsxs(a,{variant:"body2",component:"span",color:"text.secondary",sx:{display:"block"},children:["Dependent: ",e.dependentVariable]}),e.independentVariables.length>0&&s.jsxs(a,{variant:"body2",component:"span",color:"text.secondary",sx:{display:"block"},children:["Independent: ",e.independentVariables.join(", ")]}),e.covariates.length>0&&s.jsxs(a,{variant:"body2",component:"span",color:"text.secondary",sx:{display:"block"},children:["Covariates: ",e.covariates.join(", ")]}),e.demographics.length>0&&s.jsxs(a,{variant:"body2",component:"span",color:"text.secondary",sx:{display:"block"},children:["Demographics: ",e.demographics.join(", ")]})]}),secondaryTypographyProps:{component:"div"}})},e.id))})]})})]}),s.jsx(h,{item:!0,xs:12,md:6,children:s.jsx(C,{children:s.jsxs(A,{children:[s.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[s.jsx(a,{variant:"h6",children:"Combined Methods Text"}),w&&s.jsxs(d,{children:[s.jsx(v,{size:"small",onClick:()=>W(!R),sx:{mr:1},children:R?"Preview":"Edit"}),s.jsx(v,{size:"small",startIcon:s.jsx(le,{}),onClick:X,sx:{mr:1},children:"Copy"}),s.jsx(v,{size:"small",startIcon:s.jsx(re,{}),onClick:J,children:"Export HTML"})]})]}),w?s.jsx(G,{variant:"outlined",sx:{p:2,minHeight:300},children:R?s.jsx(m,{fullWidth:!0,multiline:!0,rows:12,value:x,onChange:e=>V(e.target.value),variant:"outlined"}):s.jsx(a,{variant:"body1",sx:{lineHeight:1.8,textAlign:"justify"},children:x})}):s.jsx(G,{variant:"outlined",sx:{p:2,minHeight:300,display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx(a,{variant:"body2",color:"text.secondary",sx:{textAlign:"center"},children:"Add analyses using the form to build your comprehensive methods section. Each analysis will be combined into a cohesive methods paragraph."})}),w&&s.jsxs(a,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:["Word count: ",x.trim().split(/\s+/).filter(e=>e.length>0).length]})]})})})]}),w&&s.jsx(d,{sx:{display:"flex",justifyContent:"center",mt:3},children:s.jsx(ce,{resultData:{title:`Statistical Methods Section (${c.length} analyses)`,type:"other",component:"StatisticalMethodsGenerator",data:{methodsText:x,analysisEntries:c,studyParameters:l,selectedDataset:(p==null?void 0:p.name)||"Manual entry",wordCount:x.trim().split(/\s+/).filter(e=>e.length>0).length,timestamp:new Date().toISOString()}},onSuccess:()=>{console.log("Methods section successfully added to Results Manager!")}})})]})})};export{Me as default};
