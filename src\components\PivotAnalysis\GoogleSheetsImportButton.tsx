import React, { useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Box,
  FormControlLabel,
  Checkbox,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  CloudDownload as CloudDownloadIcon,
  Help as HelpIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { Dataset, DataType, VariableRole } from '../../types';
import { generateUUID } from '../../utils/uuid';

interface GoogleSheetsImportButtonProps {
  variant?: 'text' | 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  size?: 'small' | 'medium' | 'large';
}

const GoogleSheetsImportButton: React.FC<GoogleSheetsImportButtonProps> = ({
  variant = 'outlined',
  color = 'primary',
  size = 'medium'
}) => {
  const { addDataset, setCurrentDataset } = useData();
  const [open, setOpen] = useState(false);
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [importOptions, setImportOptions] = useState({
    hasHeader: true,
    skipEmptyLines: true,
    dynamicTyping: true,
  });

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    if (!loading) {
      setOpen(false);
      setUrl('');
      setError(null);
    }
  };

  // Helper function to extract the sheet ID from a Google Sheets URL
  const extractSheetId = (url: string): string | null => {
    // Match patterns like:
    // https://docs.google.com/spreadsheets/d/SHEET_ID/edit
    // https://docs.google.com/spreadsheets/d/SHEET_ID/edit#gid=0
    const match = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
  };

  // Function to determine data types based on values
  const inferDataType = (values: string[]): DataType => {
    const nonEmptyValues = values.filter(val => val !== '' && val !== null && val !== undefined);

    if (nonEmptyValues.length === 0) return DataType.TEXT;

    // Check if values are strictly 0 or 1 (should be numeric)
    const zeroOneValues = ['0', '1'];
    if (nonEmptyValues.every(v => zeroOneValues.includes(v))) {
      return DataType.NUMERIC;
    }

    // Check if all values are Yes/No (should be categorical)
    const yesNoValues = ['yes', 'no'];
    if (nonEmptyValues.every(v => yesNoValues.includes(v.toLowerCase()))) {
      return DataType.CATEGORICAL;
    }

    // Check if all values are boolean (true/false only)
    const booleanValues = ['true', 'false'];
    if (nonEmptyValues.every(v => booleanValues.includes(v.toLowerCase()))) {
      return DataType.BOOLEAN;
    }

    // Check if all values can be parsed as numbers
    const allNumeric = nonEmptyValues.every(val => {
      return !isNaN(Number(val)) && val.trim() !== '';
    });

    if (allNumeric) return DataType.NUMERIC;

    // Check if values might be dates
    const possibleDatePattern = /^\d{1,4}[\/\-]\d{1,2}[\/\-]\d{1,4}$|^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/;
    const mightBeDates = nonEmptyValues.some(val => {
      return possibleDatePattern.test(val) || !isNaN(Date.parse(val));
    });

    if (mightBeDates) return DataType.DATE;

    // Default to categorical
    return DataType.CATEGORICAL;
  };

  const handleImportFromGoogleSheets = async () => {
    setLoading(true);
    setError(null);

    try {
      const sheetId = extractSheetId(url);
      if (!sheetId) {
        throw new Error('Invalid Google Sheets URL. Please provide a valid URL.');
      }

      // Construct the Google Sheets API URL to fetch CSV data
      // This uses the public export feature which doesn't require authentication
      // but the sheet must be publicly accessible ("Anyone with the link can view")
      const apiUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv`;

      // Fetch the CSV data
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(
          'Failed to fetch data from Google Sheets. ' +
          'Make sure the sheet is publicly accessible ("Anyone with the link can view")'
        );
      }

      const csvText = await response.text();
      
      // Parse CSV
      const rows = csvText.split('\n').map(row => row.split(',').map(cell => {
        // Remove quotes if present
        const trimmed = cell.trim();
        if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
          return trimmed.substring(1, trimmed.length - 1);
        }
        return trimmed;
      }));

      if (rows.length === 0) {
        throw new Error('The spreadsheet appears to be empty.');
      }

      // Extract headers and data
      let headers: string[] = [];
      let data: any[] = [];

      if (importOptions.hasHeader && rows.length > 0) {
        headers = rows[0];
        data = rows.slice(1);
      } else {
        // Generate column names if no headers
        headers = rows[0].map((_, index) => `Column ${index + 1}`);
        data = rows;
      }

      // Skip empty lines if option is selected
      if (importOptions.skipEmptyLines) {
        data = data.filter((row: string[]) => row.some((cell: string) => cell.trim() !== ''));
      }

      // Infer column types
      const columns = headers.map((header, index) => {
        const columnValues = data.map(row => row[index] || '');
        const dataType = inferDataType(columnValues);

        return {
          id: generateUUID(),
          name: header,
          type: dataType,
          role: VariableRole.NONE
        };
      });

      // Convert data to objects with column names as keys
      const formattedData = data.map(row => {
        const rowData: Record<string, any> = {};
        headers.forEach((header, index) => {
          const value = row[index] || '';
          if (importOptions.dynamicTyping) {
            const columnType = columns[index].type;
            if (columnType === DataType.NUMERIC && value !== '') {
              rowData[header] = Number(value);
            } else if (columnType === DataType.DATE && value !== '') {
              try {
                rowData[header] = new Date(value);
              } catch (e) {
                rowData[header] = value;
              }
            } else {
              rowData[header] = value;
            }
          } else {
            rowData[header] = value;
          }
        });
        return rowData;
      });

      // Create dataset
      const dataset: Dataset = {
        id: generateUUID(),
        name: `Google Sheet Import ${new Date().toLocaleDateString()}`,
        description: `Imported from Google Sheets: ${url}`,
        dateCreated: new Date(),
        dateModified: new Date(),
        columns,
        data: formattedData,
      };

      const status = await addDataset(dataset);
      setLoading(false);

      if (typeof status === 'string' && (status === 'added' || status === 'selected')) {
        setUrl(''); // Reset URL field
        setOpen(false); // Close dialog
        // Set as current dataset
        setCurrentDataset(dataset);
      }
    } catch (err) {
      setError(`Failed to import data: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };

  const handleOptionChange = (option: keyof typeof importOptions) => {
    setImportOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  return (
    <>
      <Button
        variant={variant}
        color={color}
        size={size}
        startIcon={<CloudDownloadIcon />}
        onClick={handleOpen}
      >
        Import from Google Sheets
      </Button>

      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center">
              <CloudDownloadIcon sx={{ mr: 1, color: 'primary.main' }} />
              Import from Google Sheets
              <Tooltip title="The Google Sheet must be publicly accessible (set to 'Anyone with the link can view')">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <IconButton edge="end" color="inherit" onClick={handleClose} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Import data directly from a Google Sheets document. Paste the URL of your Google Sheet below.
          </Typography>

          <TextField
            fullWidth
            label="Google Sheets URL"
            placeholder="https://docs.google.com/spreadsheets/d/..."
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            variant="outlined"
            sx={{ mb: 2 }}
            disabled={loading}
          />

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Import Options
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={importOptions.hasHeader}
                  onChange={() => handleOptionChange('hasHeader')}
                  disabled={loading}
                />
              }
              label="First row contains headers"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={importOptions.skipEmptyLines}
                  onChange={() => handleOptionChange('skipEmptyLines')}
                  disabled={loading}
                />
              }
              label="Skip empty rows"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={importOptions.dynamicTyping}
                  onChange={() => handleOptionChange('dynamicTyping')}
                  disabled={loading}
                />
              }
              label="Automatically detect data types"
            />
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleImportFromGoogleSheets}
            disabled={!url.trim() || loading}
            startIcon={loading ? <CircularProgress size={20} /> : <CloudDownloadIcon />}
          >
            {loading ? 'Importing...' : 'Import'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default GoogleSheetsImportButton;