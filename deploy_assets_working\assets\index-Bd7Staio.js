const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./BarChart-mU2G4olq.js","./mui-libs-CfwFIaTD.js","./react-libs-Cr2nE3UY.js","./index-Bpan7Tbe.js","./supabase-lib-B3goak-P.js","./ml-tensorflow-D19WVUQh.js","./charts-plotly-BhN4fPIu.js","./charts-recharts-d3-BEF1Y_jn.js","./charts-plotly-CuCRB34y.css","./other-utils-CR9xr_gI.js","./index-DyRZcmzc.css","./descriptive-Djo0s6H4.js","./math-setup-BTRs7Kau.js","./math-lib-BOZ-XUok.js","./PieChart-BIcQw2j3.js","./Histogram-Cb7VnHyR.js","./BoxPlot-ZMiXdUEt.js","./ScatterPlot-Crke6vGe.js","./RainCloudPlot-DH85uixJ.js","./SankeyDiagram-D49V6zuF.js","./ErrorBarChart-SBYEVPSL.js"])))=>i.map(i=>d[i]);
import{_ as x}from"./supabase-lib-B3goak-P.js";import{u as p,j as e,B as t,R as m,a6 as _,a7 as l,W as b,a8 as y,a9 as f,aa as v,a2 as C,ab as g,ac as I,ad as P,ae as i}from"./mui-libs-CfwFIaTD.js";import{r as s}from"./react-libs-Cr2nE3UY.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";const E=s.lazy(()=>x(()=>import("./BarChart-mU2G4olq.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),S=s.lazy(()=>x(()=>import("./PieChart-BIcQw2j3.js"),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),k=s.lazy(()=>x(()=>import("./Histogram-Cb7VnHyR.js"),__vite__mapDeps([15,1,2,6,7,8,3,4,5,9,10,11,12,13]),import.meta.url)),z=s.lazy(()=>x(()=>import("./BoxPlot-ZMiXdUEt.js"),__vite__mapDeps([16,4,2,5,6,1,7,8,3,9,10,11,12,13]),import.meta.url)),T=s.lazy(()=>x(()=>import("./ScatterPlot-Crke6vGe.js"),__vite__mapDeps([17,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),B=s.lazy(()=>x(()=>import("./RainCloudPlot-DH85uixJ.js"),__vite__mapDeps([18,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),R=s.lazy(()=>x(()=>import("./SankeyDiagram-D49V6zuF.js"),__vite__mapDeps([19,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),V=s.lazy(()=>x(()=>import("./ErrorBarChart-SBYEVPSL.js"),__vite__mapDeps([20,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),n=r=>{const{children:d,value:a,index:c,...j}=r;return e.jsx("div",{role:"tabpanel",hidden:a!==c,id:`visualization-tabpanel-${c}`,"aria-labelledby":`visualization-tab-${c}`,...j,style:{width:"100%"},children:a===c&&e.jsx(t,{sx:{pt:2},children:d})})},o=r=>({id:`visualization-tab-${r}`,"aria-controls":`visualization-tabpanel-${r}`}),u={bar:0,pie:1,histogram:2,boxplot:3,scatter:4,raincloud:5,sankey:6,errorbar:7},M=({initialTab:r=""})=>{const d=p(),[a,c]=s.useState(0);s.useEffect(()=>{r&&u[r]!==void 0&&c(u[r])},[r]);const j=(D,h)=>{c(h)};return e.jsx(t,{sx:{width:"100%"},children:e.jsxs(m,{elevation:1,sx:{width:"100%"},children:[e.jsxs(_,{value:a,onChange:j,"aria-label":"Visualization Tabs",variant:"scrollable",scrollButtons:"auto",allowScrollButtonsMobile:!0,sx:{borderBottom:1,borderColor:"divider",backgroundColor:d.palette.background.paper},children:[e.jsx(l,{icon:e.jsx(b,{}),label:"Bar Chart",...o(0),sx:{py:2}}),e.jsx(l,{icon:e.jsx(y,{}),label:"Pie Chart",...o(1),sx:{py:2}}),e.jsx(l,{icon:e.jsx(f,{}),label:"Histogram",...o(2),sx:{py:2}}),e.jsx(l,{icon:e.jsx(v,{}),label:"Box Plot",...o(3),sx:{py:2}}),e.jsx(l,{icon:e.jsx(C,{}),label:"Scatter Plot",...o(4),sx:{py:2}}),e.jsx(l,{icon:e.jsx(g,{}),label:"Rain Cloud Plot",...o(5),sx:{py:2}}),e.jsx(l,{icon:e.jsx(I,{}),label:"Sankey Diagram",...o(6),sx:{py:2}}),e.jsx(l,{icon:e.jsx(P,{}),label:"Error Bar Chart",...o(7),sx:{py:2}})]}),e.jsxs(t,{sx:{p:0},children:[e.jsx(n,{value:a,index:0,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(E,{})})}),e.jsx(n,{value:a,index:1,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(S,{})})}),e.jsx(n,{value:a,index:2,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(k,{})})}),e.jsx(n,{value:a,index:3,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(z,{})})}),e.jsx(n,{value:a,index:4,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(T,{})})}),e.jsx(n,{value:a,index:5,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(B,{})})}),e.jsx(n,{value:a,index:6,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(R,{})})}),e.jsx(n,{value:a,index:7,children:e.jsx(s.Suspense,{fallback:e.jsx(t,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:200},children:e.jsx(i,{})}),children:e.jsx(V,{})})})]})]})})};export{M as default};
