import{j as o,B as F,ai as Bn,b9 as $n,ba as Wn,bb as Ne,e as se,h as ht,aE as Z,ab as Xe,I as Le,Q as Xn,dn as Yn,bB as at,bS as _t,bT as Qt,cm as wr,bU as Zt,g as Ve,f as Q,ah as Cr,L as vn,m as Ht,n as lt,r as ct,aG as jr,bj as Et,D as Un,ae as en,bu as It,dp as Sr,aH as Hn,bV as tn,o as Kn,aW as Dr,R as Rr,G as ke,dq as Er,dr as Ir,bW as Mr,bX as Ar,aD as Or,bY as Tr,ds as Pr,cs as kr,bR as Kt,dt as Gn,k as Nr,l as Lr,bc as zr,cr as Fr,s as Br,W as mn,X as $r,ad as xn,a2 as Wr,Z as Xr,a0 as Yr,C as Ur}from"./mui-libs-CfwFIaTD.js";import{r as c,b as B,d as it}from"./react-libs-Cr2nE3UY.js";import{f as on,b as an}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";function Hr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.useMemo(()=>r=>{t.forEach(s=>s(r))},t)}const Lt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function Je(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function ln(e){return"nodeType"in e}function ee(e){var t,n;return e?Je(e)?e:ln(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}function cn(e){const{Document:t}=ee(e);return e instanceof t}function mt(e){return Je(e)?!1:e instanceof ee(e).HTMLElement}function Vn(e){return e instanceof ee(e).SVGElement}function _e(e){return e?Je(e)?e.document:ln(e)?cn(e)?e:mt(e)||Vn(e)?e.ownerDocument:document:document:document}const ge=Lt?c.useLayoutEffect:c.useEffect;function zt(e){const t=c.useRef(e);return ge(()=>{t.current=e}),c.useCallback(function(){for(var n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t.current==null?void 0:t.current(...r)},[])}function Kr(){const e=c.useRef(null),t=c.useCallback((r,s)=>{e.current=setInterval(r,s)},[]),n=c.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,n]}function gt(e,t){t===void 0&&(t=[e]);const n=c.useRef(e);return ge(()=>{n.current!==e&&(n.current=e)},t),n}function xt(e,t){const n=c.useRef();return c.useMemo(()=>{const r=e(n.current);return n.current=r,r},[...t])}function Mt(e){const t=zt(e),n=c.useRef(null),r=c.useCallback(s=>{s!==n.current&&(t==null||t(s,n.current)),n.current=s},[]);return[n,r]}function At(e){const t=c.useRef();return c.useEffect(()=>{t.current=e},[e]),t.current}let Gt={};function bt(e,t){return c.useMemo(()=>{if(t)return t;const n=Gt[e]==null?0:Gt[e]+1;return Gt[e]=n,e+"-"+n},[e,t])}function qn(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];return r.reduce((i,a)=>{const l=Object.entries(a);for(const[u,d]of l){const h=i[u];h!=null&&(i[u]=h+e*d)}return i},{...t})}}const qe=qn(1),pt=qn(-1);function Gr(e){return"clientX"in e&&"clientY"in e}function Ft(e){if(!e)return!1;const{KeyboardEvent:t}=ee(e.target);return t&&e instanceof t}function Vr(e){if(!e)return!1;const{TouchEvent:t}=ee(e.target);return t&&e instanceof t}function Ot(e){if(Vr(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return Gr(e)?{x:e.clientX,y:e.clientY}:null}const Fe=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Fe.Translate.toString(e),Fe.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),bn="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function qr(e){return e.matches(bn)?e:e.querySelector(bn)}const Jr={display:"none"};function _r(e){let{id:t,value:n}=e;return B.createElement("div",{id:t,style:Jr},n)}function Qr(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const s={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return B.createElement("div",{id:t,style:s,role:"status","aria-live":r,"aria-atomic":!0},n)}function Zr(){const[e,t]=c.useState("");return{announce:c.useCallback(r=>{r!=null&&t(r)},[]),announcement:e}}const Jn=c.createContext(null);function es(e){const t=c.useContext(Jn);c.useEffect(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function ts(){const[e]=c.useState(()=>new Set),t=c.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[c.useCallback(r=>{let{type:s,event:i}=r;e.forEach(a=>{var l;return(l=a[s])==null?void 0:l.call(a,i)})},[e]),t]}const ns={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},rs={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function ss(e){let{announcements:t=rs,container:n,hiddenTextDescribedById:r,screenReaderInstructions:s=ns}=e;const{announce:i,announcement:a}=Zr(),l=bt("DndLiveRegion"),[u,d]=c.useState(!1);if(c.useEffect(()=>{d(!0)},[]),es(c.useMemo(()=>({onDragStart(f){let{active:p}=f;i(t.onDragStart({active:p}))},onDragMove(f){let{active:p,over:g}=f;t.onDragMove&&i(t.onDragMove({active:p,over:g}))},onDragOver(f){let{active:p,over:g}=f;i(t.onDragOver({active:p,over:g}))},onDragEnd(f){let{active:p,over:g}=f;i(t.onDragEnd({active:p,over:g}))},onDragCancel(f){let{active:p,over:g}=f;i(t.onDragCancel({active:p,over:g}))}}),[i,t])),!u)return null;const h=B.createElement(B.Fragment,null,B.createElement(_r,{id:r,value:s.draggable}),B.createElement(Qr,{id:l,announcement:a}));return n?it.createPortal(h,n):h}var X;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(X||(X={}));function Tt(){}function yn(e,t){return c.useMemo(()=>({sensor:e,options:t??{}}),[e,t])}function os(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.useMemo(()=>[...t].filter(r=>r!=null),[...t])}const pe=Object.freeze({x:0,y:0});function _n(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function is(e,t){const n=Ot(e);if(!n)return"0 0";const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function Qn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function as(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function wn(e){let{left:t,top:n,height:r,width:s}=e;return[{x:t,y:n},{x:t+s,y:n},{x:t,y:n+r},{x:t+s,y:n+r}]}function Zn(e,t){if(!e||e.length===0)return null;const[n]=e;return n[t]}function Cn(e,t,n){return t===void 0&&(t=e.left),n===void 0&&(n=e.top),{x:t+e.width*.5,y:n+e.height*.5}}const jn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=Cn(t,t.left,t.top),i=[];for(const a of r){const{id:l}=a,u=n.get(l);if(u){const d=_n(Cn(u),s);i.push({id:l,data:{droppableContainer:a,value:d}})}}return i.sort(Qn)},ls=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=wn(t),i=[];for(const a of r){const{id:l}=a,u=n.get(l);if(u){const d=wn(u),h=s.reduce((p,g,S)=>p+_n(d[S],g),0),f=Number((h/4).toFixed(4));i.push({id:l,data:{droppableContainer:a,value:f}})}}return i.sort(Qn)};function cs(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),s=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=s-r,l=i-n;if(r<s&&n<i){const u=t.width*t.height,d=e.width*e.height,h=a*l,f=h/(u+d-h);return Number(f.toFixed(4))}return 0}const us=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=[];for(const i of r){const{id:a}=i,l=n.get(a);if(l){const u=cs(l,t);u>0&&s.push({id:a,data:{droppableContainer:i,value:u}})}}return s.sort(as)};function ds(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function er(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:pe}function fs(e){return function(n){for(var r=arguments.length,s=new Array(r>1?r-1:0),i=1;i<r;i++)s[i-1]=arguments[i];return s.reduce((a,l)=>({...a,top:a.top+e*l.y,bottom:a.bottom+e*l.y,left:a.left+e*l.x,right:a.right+e*l.x}),{...n})}}const hs=fs(1);function tr(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function gs(e,t,n){const r=tr(t);if(!r)return e;const{scaleX:s,scaleY:i,x:a,y:l}=r,u=e.left-a-(1-s)*parseFloat(n),d=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),h=s?e.width/s:e.width,f=i?e.height/i:e.height;return{width:h,height:f,top:d,right:u+h,bottom:d+f,left:u}}const ps={ignoreTransform:!1};function Qe(e,t){t===void 0&&(t=ps);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:d,transformOrigin:h}=ee(e).getComputedStyle(e);d&&(n=gs(n,d,h))}const{top:r,left:s,width:i,height:a,bottom:l,right:u}=n;return{top:r,left:s,width:i,height:a,bottom:l,right:u}}function Sn(e){return Qe(e,{ignoreTransform:!0})}function vs(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function ms(e,t){return t===void 0&&(t=ee(e).getComputedStyle(e)),t.position==="fixed"}function xs(e,t){t===void 0&&(t=ee(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(s=>{const i=t[s];return typeof i=="string"?n.test(i):!1})}function Bt(e,t){const n=[];function r(s){if(t!=null&&n.length>=t||!s)return n;if(cn(s)&&s.scrollingElement!=null&&!n.includes(s.scrollingElement))return n.push(s.scrollingElement),n;if(!mt(s)||Vn(s)||n.includes(s))return n;const i=ee(e).getComputedStyle(s);return s!==e&&xs(s,i)&&n.push(s),ms(s,i)?n:r(s.parentNode)}return e?r(e):n}function nr(e){const[t]=Bt(e,1);return t??null}function Vt(e){return!Lt||!e?null:Je(e)?e:ln(e)?cn(e)||e===_e(e).scrollingElement?window:mt(e)?e:null:null}function rr(e){return Je(e)?e.scrollX:e.scrollLeft}function sr(e){return Je(e)?e.scrollY:e.scrollTop}function nn(e){return{x:rr(e),y:sr(e)}}var H;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(H||(H={}));function or(e){return!Lt||!e?!1:e===document.scrollingElement}function ir(e){const t={x:0,y:0},n=or(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},s=e.scrollTop<=t.y,i=e.scrollLeft<=t.x,a=e.scrollTop>=r.y,l=e.scrollLeft>=r.x;return{isTop:s,isLeft:i,isBottom:a,isRight:l,maxScroll:r,minScroll:t}}const bs={x:.2,y:.2};function ys(e,t,n,r,s){let{top:i,left:a,right:l,bottom:u}=n;r===void 0&&(r=10),s===void 0&&(s=bs);const{isTop:d,isBottom:h,isLeft:f,isRight:p}=ir(e),g={x:0,y:0},S={x:0,y:0},b={height:t.height*s.y,width:t.width*s.x};return!d&&i<=t.top+b.height?(g.y=H.Backward,S.y=r*Math.abs((t.top+b.height-i)/b.height)):!h&&u>=t.bottom-b.height&&(g.y=H.Forward,S.y=r*Math.abs((t.bottom-b.height-u)/b.height)),!p&&l>=t.right-b.width?(g.x=H.Forward,S.x=r*Math.abs((t.right-b.width-l)/b.width)):!f&&a<=t.left+b.width&&(g.x=H.Backward,S.x=r*Math.abs((t.left+b.width-a)/b.width)),{direction:g,speed:S}}function ws(e){if(e===document.scrollingElement){const{innerWidth:i,innerHeight:a}=window;return{top:0,left:0,right:i,bottom:a,width:i,height:a}}const{top:t,left:n,right:r,bottom:s}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:s,width:e.clientWidth,height:e.clientHeight}}function ar(e){return e.reduce((t,n)=>qe(t,nn(n)),pe)}function Cs(e){return e.reduce((t,n)=>t+rr(n),0)}function js(e){return e.reduce((t,n)=>t+sr(n),0)}function lr(e,t){if(t===void 0&&(t=Qe),!e)return;const{top:n,left:r,bottom:s,right:i}=t(e);nr(e)&&(s<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const Ss=[["x",["left","right"],Cs],["y",["top","bottom"],js]];class un{constructor(t,n){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=Bt(n),s=ar(r);this.rect={...t},this.width=t.width,this.height=t.height;for(const[i,a,l]of Ss)for(const u of a)Object.defineProperty(this,u,{get:()=>{const d=l(r),h=s[i]-d;return this.rect[u]+h},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ut{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(n=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...n)})},this.target=t}add(t,n,r){var s;(s=this.target)==null||s.addEventListener(t,n,r),this.listeners.push([t,n,r])}}function Ds(e){const{EventTarget:t}=ee(e);return e instanceof t?e:_e(e)}function qt(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t?r>t.y:!1}var ce;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(ce||(ce={}));function Dn(e){e.preventDefault()}function Rs(e){e.stopPropagation()}var O;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(O||(O={}));const cr={start:[O.Space,O.Enter],cancel:[O.Esc],end:[O.Space,O.Enter,O.Tab]},Es=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case O.Right:return{...n,x:n.x+25};case O.Left:return{...n,x:n.x-25};case O.Down:return{...n,y:n.y+25};case O.Up:return{...n,y:n.y-25}}};class dn{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;const{event:{target:n}}=t;this.props=t,this.listeners=new ut(_e(n)),this.windowListeners=new ut(ee(n)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(ce.Resize,this.handleCancel),this.windowListeners.add(ce.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(ce.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:t,onStart:n}=this.props,r=t.node.current;r&&lr(r),n(pe)}handleKeyDown(t){if(Ft(t)){const{active:n,context:r,options:s}=this.props,{keyboardCodes:i=cr,coordinateGetter:a=Es,scrollBehavior:l="smooth"}=s,{code:u}=t;if(i.end.includes(u)){this.handleEnd(t);return}if(i.cancel.includes(u)){this.handleCancel(t);return}const{collisionRect:d}=r.current,h=d?{x:d.left,y:d.top}:pe;this.referenceCoordinates||(this.referenceCoordinates=h);const f=a(t,{active:n,context:r.current,currentCoordinates:h});if(f){const p=pt(f,h),g={x:0,y:0},{scrollableAncestors:S}=r.current;for(const b of S){const v=t.code,{isTop:w,isRight:C,isLeft:y,isBottom:R,maxScroll:E,minScroll:A}=ir(b),j=ws(b),I={x:Math.min(v===O.Right?j.right-j.width/2:j.right,Math.max(v===O.Right?j.left:j.left+j.width/2,f.x)),y:Math.min(v===O.Down?j.bottom-j.height/2:j.bottom,Math.max(v===O.Down?j.top:j.top+j.height/2,f.y))},N=v===O.Right&&!C||v===O.Left&&!y,z=v===O.Down&&!R||v===O.Up&&!w;if(N&&I.x!==f.x){const M=b.scrollLeft+p.x,V=v===O.Right&&M<=E.x||v===O.Left&&M>=A.x;if(V&&!p.y){b.scrollTo({left:M,behavior:l});return}V?g.x=b.scrollLeft-M:g.x=v===O.Right?b.scrollLeft-E.x:b.scrollLeft-A.x,g.x&&b.scrollBy({left:-g.x,behavior:l});break}else if(z&&I.y!==f.y){const M=b.scrollTop+p.y,V=v===O.Down&&M<=E.y||v===O.Up&&M>=A.y;if(V&&!p.x){b.scrollTo({top:M,behavior:l});return}V?g.y=b.scrollTop-M:g.y=v===O.Down?b.scrollTop-E.y:b.scrollTop-A.y,g.y&&b.scrollBy({top:-g.y,behavior:l});break}}this.handleMove(t,qe(pt(f,this.referenceCoordinates),g))}}}handleMove(t,n){const{onMove:r}=this.props;t.preventDefault(),r(n)}handleEnd(t){const{onEnd:n}=this.props;t.preventDefault(),this.detach(),n()}handleCancel(t){const{onCancel:n}=this.props;t.preventDefault(),this.detach(),n()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}dn.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=cr,onActivation:s}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const l=i.activatorNode.current;return l&&e.target!==l?!1:(e.preventDefault(),s==null||s({event:e.nativeEvent}),!0)}return!1}}];function Rn(e){return!!(e&&"distance"in e)}function En(e){return!!(e&&"delay"in e)}class fn{constructor(t,n,r){var s;r===void 0&&(r=Ds(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=n;const{event:i}=t,{target:a}=i;this.props=t,this.events=n,this.document=_e(a),this.documentListeners=new ut(this.document),this.listeners=new ut(r),this.windowListeners=new ut(ee(a)),this.initialCoordinates=(s=Ot(i))!=null?s:pe,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:t,props:{options:{activationConstraint:n,bypassActivationConstraint:r}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(ce.Resize,this.handleCancel),this.windowListeners.add(ce.DragStart,Dn),this.windowListeners.add(ce.VisibilityChange,this.handleCancel),this.windowListeners.add(ce.ContextMenu,Dn),this.documentListeners.add(ce.Keydown,this.handleKeydown),n){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(En(n)){this.timeoutId=setTimeout(this.handleStart,n.delay),this.handlePending(n);return}if(Rn(n)){this.handlePending(n);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(t,n){const{active:r,onPending:s}=this.props;s(r,t,this.initialCoordinates,n)}handleStart(){const{initialCoordinates:t}=this,{onStart:n}=this.props;t&&(this.activated=!0,this.documentListeners.add(ce.Click,Rs,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(ce.SelectionChange,this.removeTextSelection),n(t))}handleMove(t){var n;const{activated:r,initialCoordinates:s,props:i}=this,{onMove:a,options:{activationConstraint:l}}=i;if(!s)return;const u=(n=Ot(t))!=null?n:pe,d=pt(s,u);if(!r&&l){if(Rn(l)){if(l.tolerance!=null&&qt(d,l.tolerance))return this.handleCancel();if(qt(d,l.distance))return this.handleStart()}if(En(l)&&qt(d,l.tolerance))return this.handleCancel();this.handlePending(l,d);return}t.cancelable&&t.preventDefault(),a(u)}handleEnd(){const{onAbort:t,onEnd:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleCancel(){const{onAbort:t,onCancel:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleKeydown(t){t.code===O.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}}const Is={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class hn extends fn{constructor(t){const{event:n}=t,r=_e(n.target);super(t,Is,r)}}hn.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!n.isPrimary||n.button!==0?!1:(r==null||r({event:n}),!0)}}];const Ms={move:{name:"mousemove"},end:{name:"mouseup"}};var rn;(function(e){e[e.RightClick=2]="RightClick"})(rn||(rn={}));class As extends fn{constructor(t){super(t,Ms,_e(t.event.target))}}As.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button===rn.RightClick?!1:(r==null||r({event:n}),!0)}}];const Jt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class Os extends fn{constructor(t){super(t,Jt)}static setup(){return window.addEventListener(Jt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(Jt.move.name,t)};function t(){}}}Os.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:s}=n;return s.length>1?!1:(r==null||r({event:n}),!0)}}];var dt;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(dt||(dt={}));var Pt;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(Pt||(Pt={}));function Ts(e){let{acceleration:t,activator:n=dt.Pointer,canScroll:r,draggingRect:s,enabled:i,interval:a=5,order:l=Pt.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:h,delta:f,threshold:p}=e;const g=ks({delta:f,disabled:!i}),[S,b]=Kr(),v=c.useRef({x:0,y:0}),w=c.useRef({x:0,y:0}),C=c.useMemo(()=>{switch(n){case dt.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case dt.DraggableRect:return s}},[n,s,u]),y=c.useRef(null),R=c.useCallback(()=>{const A=y.current;if(!A)return;const j=v.current.x*w.current.x,I=v.current.y*w.current.y;A.scrollBy(j,I)},[]),E=c.useMemo(()=>l===Pt.TreeOrder?[...d].reverse():d,[l,d]);c.useEffect(()=>{if(!i||!d.length||!C){b();return}for(const A of E){if((r==null?void 0:r(A))===!1)continue;const j=d.indexOf(A),I=h[j];if(!I)continue;const{direction:N,speed:z}=ys(A,I,C,t,p);for(const M of["x","y"])g[M][N[M]]||(z[M]=0,N[M]=0);if(z.x>0||z.y>0){b(),y.current=A,S(R,a),v.current=z,w.current=N;return}}v.current={x:0,y:0},w.current={x:0,y:0},b()},[t,R,r,b,i,a,JSON.stringify(C),JSON.stringify(g),S,d,E,h,JSON.stringify(p)])}const Ps={x:{[H.Backward]:!1,[H.Forward]:!1},y:{[H.Backward]:!1,[H.Forward]:!1}};function ks(e){let{delta:t,disabled:n}=e;const r=At(t);return xt(s=>{if(n||!r||!s)return Ps;const i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[H.Backward]:s.x[H.Backward]||i.x===-1,[H.Forward]:s.x[H.Forward]||i.x===1},y:{[H.Backward]:s.y[H.Backward]||i.y===-1,[H.Forward]:s.y[H.Forward]||i.y===1}}},[n,t,r])}function Ns(e,t){const n=t!=null?e.get(t):void 0,r=n?n.node.current:null;return xt(s=>{var i;return t==null?null:(i=r??s)!=null?i:null},[r,t])}function Ls(e,t){return c.useMemo(()=>e.reduce((n,r)=>{const{sensor:s}=r,i=s.activators.map(a=>({eventName:a.eventName,handler:t(a.handler,r)}));return[...n,...i]},[]),[e,t])}var vt;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(vt||(vt={}));var sn;(function(e){e.Optimized="optimized"})(sn||(sn={}));const In=new Map;function zs(e,t){let{dragging:n,dependencies:r,config:s}=t;const[i,a]=c.useState(null),{frequency:l,measure:u,strategy:d}=s,h=c.useRef(e),f=v(),p=gt(f),g=c.useCallback(function(w){w===void 0&&(w=[]),!p.current&&a(C=>C===null?w:C.concat(w.filter(y=>!C.includes(y))))},[p]),S=c.useRef(null),b=xt(w=>{if(f&&!n)return In;if(!w||w===In||h.current!==e||i!=null){const C=new Map;for(let y of e){if(!y)continue;if(i&&i.length>0&&!i.includes(y.id)&&y.rect.current){C.set(y.id,y.rect.current);continue}const R=y.node.current,E=R?new un(u(R),R):null;y.rect.current=E,E&&C.set(y.id,E)}return C}return w},[e,i,n,f,u]);return c.useEffect(()=>{h.current=e},[e]),c.useEffect(()=>{f||g()},[n,f]),c.useEffect(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),c.useEffect(()=>{f||typeof l!="number"||S.current!==null||(S.current=setTimeout(()=>{g(),S.current=null},l))},[l,f,g,...r]),{droppableRects:b,measureDroppableContainers:g,measuringScheduled:i!=null};function v(){switch(d){case vt.Always:return!1;case vt.BeforeDragging:return n;default:return!n}}}function gn(e,t){return xt(n=>e?n||(typeof t=="function"?t(e):e):null,[t,e])}function Fs(e,t){return gn(e,t)}function Bs(e){let{callback:t,disabled:n}=e;const r=zt(t),s=c.useMemo(()=>{if(n||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:i}=window;return new i(r)},[r,n]);return c.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function $t(e){let{callback:t,disabled:n}=e;const r=zt(t),s=c.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:i}=window;return new i(r)},[n]);return c.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function $s(e){return new un(Qe(e),e)}function Mn(e,t,n){t===void 0&&(t=$s);const[r,s]=c.useState(null);function i(){s(u=>{if(!e)return null;if(e.isConnected===!1){var d;return(d=u??n)!=null?d:null}const h=t(e);return JSON.stringify(u)===JSON.stringify(h)?u:h})}const a=Bs({callback(u){if(e)for(const d of u){const{type:h,target:f}=d;if(h==="childList"&&f instanceof HTMLElement&&f.contains(e)){i();break}}}}),l=$t({callback:i});return ge(()=>{i(),e?(l==null||l.observe(e),a==null||a.observe(document.body,{childList:!0,subtree:!0})):(l==null||l.disconnect(),a==null||a.disconnect())},[e]),r}function Ws(e){const t=gn(e);return er(e,t)}const An=[];function Xs(e){const t=c.useRef(e),n=xt(r=>e?r&&r!==An&&e&&t.current&&e.parentNode===t.current.parentNode?r:Bt(e):An,[e]);return c.useEffect(()=>{t.current=e},[e]),n}function Ys(e){const[t,n]=c.useState(null),r=c.useRef(e),s=c.useCallback(i=>{const a=Vt(i.target);a&&n(l=>l?(l.set(a,nn(a)),new Map(l)):null)},[]);return c.useEffect(()=>{const i=r.current;if(e!==i){a(i);const l=e.map(u=>{const d=Vt(u);return d?(d.addEventListener("scroll",s,{passive:!0}),[d,nn(d)]):null}).filter(u=>u!=null);n(l.length?new Map(l):null),r.current=e}return()=>{a(e),a(i)};function a(l){l.forEach(u=>{const d=Vt(u);d==null||d.removeEventListener("scroll",s)})}},[s,e]),c.useMemo(()=>e.length?t?Array.from(t.values()).reduce((i,a)=>qe(i,a),pe):ar(e):pe,[e,t])}function On(e,t){t===void 0&&(t=[]);const n=c.useRef(null);return c.useEffect(()=>{n.current=null},t),c.useEffect(()=>{const r=e!==pe;r&&!n.current&&(n.current=e),!r&&n.current&&(n.current=null)},[e]),n.current?pt(e,n.current):pe}function Us(e){c.useEffect(()=>{if(!Lt)return;const t=e.map(n=>{let{sensor:r}=n;return r.setup==null?void 0:r.setup()});return()=>{for(const n of t)n==null||n()}},e.map(t=>{let{sensor:n}=t;return n}))}function Hs(e,t){return c.useMemo(()=>e.reduce((n,r)=>{let{eventName:s,handler:i}=r;return n[s]=a=>{i(a,t)},n},{}),[e,t])}function ur(e){return c.useMemo(()=>e?vs(e):null,[e])}const Tn=[];function Ks(e,t){t===void 0&&(t=Qe);const[n]=e,r=ur(n?ee(n):null),[s,i]=c.useState(Tn);function a(){i(()=>e.length?e.map(u=>or(u)?r:new un(t(u),u)):Tn)}const l=$t({callback:a});return ge(()=>{l==null||l.disconnect(),a(),e.forEach(u=>l==null?void 0:l.observe(u))},[e]),s}function dr(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return mt(t)?t:e}function Gs(e){let{measure:t}=e;const[n,r]=c.useState(null),s=c.useCallback(d=>{for(const{target:h}of d)if(mt(h)){r(f=>{const p=t(h);return f?{...f,width:p.width,height:p.height}:p});break}},[t]),i=$t({callback:s}),a=c.useCallback(d=>{const h=dr(d);i==null||i.disconnect(),h&&(i==null||i.observe(h)),r(h?t(h):null)},[t,i]),[l,u]=Mt(a);return c.useMemo(()=>({nodeRef:l,rect:n,setRef:u}),[n,l,u])}const Vs=[{sensor:hn,options:{}},{sensor:dn,options:{}}],qs={current:{}},Rt={draggable:{measure:Sn},droppable:{measure:Sn,strategy:vt.WhileDragging,frequency:sn.Optimized},dragOverlay:{measure:Qe}};class ft extends Map{get(t){var n;return t!=null&&(n=super.get(t))!=null?n:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:n}=t;return!n})}getNodeFor(t){var n,r;return(n=(r=this.get(t))==null?void 0:r.node.current)!=null?n:void 0}}const Js={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ft,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:Tt},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Rt,measureDroppableContainers:Tt,windowRect:null,measuringScheduled:!1},fr={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:Tt,draggableNodes:new Map,over:null,measureDroppableContainers:Tt},yt=c.createContext(fr),hr=c.createContext(Js);function _s(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ft}}}function Qs(e,t){switch(t.type){case X.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case X.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case X.DragEnd:case X.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case X.RegisterDroppable:{const{element:n}=t,{id:r}=n,s=new ft(e.droppable.containers);return s.set(r,n),{...e,droppable:{...e.droppable,containers:s}}}case X.SetDroppableDisabled:{const{id:n,key:r,disabled:s}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new ft(e.droppable.containers);return a.set(n,{...i,disabled:s}),{...e,droppable:{...e.droppable,containers:a}}}case X.UnregisterDroppable:{const{id:n,key:r}=t,s=e.droppable.containers.get(n);if(!s||r!==s.key)return e;const i=new ft(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function Zs(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:s}=c.useContext(yt),i=At(r),a=At(n==null?void 0:n.id);return c.useEffect(()=>{if(!t&&!r&&i&&a!=null){if(!Ft(i)||document.activeElement===i.target)return;const l=s.get(a);if(!l)return;const{activatorNode:u,node:d}=l;if(!u.current&&!d.current)return;requestAnimationFrame(()=>{for(const h of[u.current,d.current]){if(!h)continue;const f=qr(h);if(f){f.focus();break}}})}},[r,t,s,a,i]),null}function gr(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce((s,i)=>i({transform:s,...r}),n):n}function eo(e){return c.useMemo(()=>({draggable:{...Rt.draggable,...e==null?void 0:e.draggable},droppable:{...Rt.droppable,...e==null?void 0:e.droppable},dragOverlay:{...Rt.dragOverlay,...e==null?void 0:e.dragOverlay}}),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function to(e){let{activeNode:t,measure:n,initialRect:r,config:s=!0}=e;const i=c.useRef(!1),{x:a,y:l}=typeof s=="boolean"?{x:s,y:s}:s;ge(()=>{if(!a&&!l||!t){i.current=!1;return}if(i.current||!r)return;const d=t==null?void 0:t.node.current;if(!d||d.isConnected===!1)return;const h=n(d),f=er(h,r);if(a||(f.x=0),l||(f.y=0),i.current=!0,Math.abs(f.x)>0||Math.abs(f.y)>0){const p=nr(d);p&&p.scrollBy({top:f.y,left:f.x})}},[t,a,l,r,n])}const Wt=c.createContext({...pe,scaleX:1,scaleY:1});var ze;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(ze||(ze={}));const Pn=c.memo(function(t){var n,r,s,i;let{id:a,accessibility:l,autoScroll:u=!0,children:d,sensors:h=Vs,collisionDetection:f=us,measuring:p,modifiers:g,...S}=t;const b=c.useReducer(Qs,void 0,_s),[v,w]=b,[C,y]=ts(),[R,E]=c.useState(ze.Uninitialized),A=R===ze.Initialized,{draggable:{active:j,nodes:I,translate:N},droppable:{containers:z}}=v,M=j!=null?I.get(j):null,V=c.useRef({initial:null,translated:null}),$=c.useMemo(()=>{var U;return j!=null?{id:j,data:(U=M==null?void 0:M.data)!=null?U:qs,rect:V}:null},[j,M]),q=c.useRef(null),[ue,De]=c.useState(null),[Y,x]=c.useState(null),P=gt(S,Object.values(S)),de=bt("DndDescribedBy",a),Ye=c.useMemo(()=>z.getEnabled(),[z]),G=eo(p),{droppableRects:ve,measureDroppableContainers:me,measuringScheduled:Re}=zs(Ye,{dragging:A,dependencies:[N.x,N.y],config:G.droppable}),J=Ns(I,j),Be=c.useMemo(()=>Y?Ot(Y):null,[Y]),fe=Yt(),oe=Fs(J,G.draggable.measure);to({activeNode:j!=null?I.get(j):null,config:fe.layoutShiftCompensation,initialRect:oe,measure:G.draggable.measure});const T=Mn(J,G.draggable.measure,oe),Ee=Mn(J?J.parentElement:null),ie=c.useRef({activatorEvent:null,active:null,activeNode:J,collisionRect:null,collisions:null,droppableRects:ve,draggableNodes:I,draggingNode:null,draggingNodeRect:null,droppableContainers:z,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),we=z.getNodeFor((n=ie.current.over)==null?void 0:n.id),ae=Gs({measure:G.dragOverlay.measure}),Ie=(r=ae.nodeRef.current)!=null?r:J,xe=A?(s=ae.rect)!=null?s:T:null,wt=!!(ae.nodeRef.current&&ae.rect),Ze=Ws(wt?null:T),Ue=ur(Ie?ee(Ie):null),he=Xs(A?we??J:null),He=Ks(he),Ce=gr(g,{transform:{x:N.x-Ze.x,y:N.y-Ze.y,scaleX:1,scaleY:1},activatorEvent:Y,active:$,activeNodeRect:T,containerNodeRect:Ee,draggingNodeRect:xe,over:ie.current.over,overlayNodeRect:ae.rect,scrollableAncestors:he,scrollableAncestorRects:He,windowRect:Ue}),et=Be?qe(Be,N):null,tt=Ys(he),Ke=On(tt),Xt=On(tt,[T]),Me=qe(Ce,Ke),Ae=xe?hs(xe,Ce):null,$e=$&&Ae?f({active:$,collisionRect:Ae,droppableRects:ve,droppableContainers:Ye,pointerCoordinates:et}):null,Ct=Zn($e,"id"),[m,D]=c.useState(null),L=wt?Ce:qe(Ce,Xt),k=ds(L,(i=m==null?void 0:m.rect)!=null?i:null,T),_=c.useRef(null),W=c.useCallback((U,te)=>{let{sensor:ne,options:Oe}=te;if(q.current==null)return;const le=I.get(q.current);if(!le)return;const re=U.nativeEvent,be=new ne({active:q.current,activeNode:le,event:re,options:Oe,context:ie,onAbort(K){if(!I.get(K))return;const{onDragAbort:ye}=P.current,Se={id:K};ye==null||ye(Se),C({type:"onDragAbort",event:Se})},onPending(K,Te,ye,Se){if(!I.get(K))return;const{onDragPending:st}=P.current,Pe={id:K,constraint:Te,initialCoordinates:ye,offset:Se};st==null||st(Pe),C({type:"onDragPending",event:Pe})},onStart(K){const Te=q.current;if(Te==null)return;const ye=I.get(Te);if(!ye)return;const{onDragStart:Se}=P.current,rt={activatorEvent:re,active:{id:Te,data:ye.data,rect:V}};it.unstable_batchedUpdates(()=>{Se==null||Se(rt),E(ze.Initializing),w({type:X.DragStart,initialCoordinates:K,active:Te}),C({type:"onDragStart",event:rt}),De(_.current),x(re)})},onMove(K){w({type:X.DragMove,coordinates:K})},onEnd:Ge(X.DragEnd),onCancel:Ge(X.DragCancel)});_.current=be;function Ge(K){return async function(){const{active:ye,collisions:Se,over:rt,scrollAdjustedTranslate:st}=ie.current;let Pe=null;if(ye&&st){const{cancelDrop:ot}=P.current;Pe={activatorEvent:re,active:ye,collisions:Se,delta:st,over:rt},K===X.DragEnd&&typeof ot=="function"&&await Promise.resolve(ot(Pe))&&(K=X.DragCancel)}q.current=null,it.unstable_batchedUpdates(()=>{w({type:K}),E(ze.Uninitialized),D(null),De(null),x(null),_.current=null;const ot=K===X.DragEnd?"onDragEnd":"onDragCancel";if(Pe){const Ut=P.current[ot];Ut==null||Ut(Pe),C({type:ot,event:Pe})}})}}},[I]),je=c.useCallback((U,te)=>(ne,Oe)=>{const le=ne.nativeEvent,re=I.get(Oe);if(q.current!==null||!re||le.dndKit||le.defaultPrevented)return;const be={active:re};U(ne,te.options,be)===!0&&(le.dndKit={capturedBy:te.sensor},q.current=Oe,W(ne,te))},[I,W]),nt=Ls(h,je);Us(h),ge(()=>{T&&R===ze.Initializing&&E(ze.Initialized)},[T,R]),c.useEffect(()=>{const{onDragMove:U}=P.current,{active:te,activatorEvent:ne,collisions:Oe,over:le}=ie.current;if(!te||!ne)return;const re={active:te,activatorEvent:ne,collisions:Oe,delta:{x:Me.x,y:Me.y},over:le};it.unstable_batchedUpdates(()=>{U==null||U(re),C({type:"onDragMove",event:re})})},[Me.x,Me.y]),c.useEffect(()=>{const{active:U,activatorEvent:te,collisions:ne,droppableContainers:Oe,scrollAdjustedTranslate:le}=ie.current;if(!U||q.current==null||!te||!le)return;const{onDragOver:re}=P.current,be=Oe.get(Ct),Ge=be&&be.rect.current?{id:be.id,rect:be.rect.current,data:be.data,disabled:be.disabled}:null,K={active:U,activatorEvent:te,collisions:ne,delta:{x:le.x,y:le.y},over:Ge};it.unstable_batchedUpdates(()=>{D(Ge),re==null||re(K),C({type:"onDragOver",event:K})})},[Ct]),ge(()=>{ie.current={activatorEvent:Y,active:$,activeNode:J,collisionRect:Ae,collisions:$e,droppableRects:ve,draggableNodes:I,draggingNode:Ie,draggingNodeRect:xe,droppableContainers:z,over:m,scrollableAncestors:he,scrollAdjustedTranslate:Me},V.current={initial:xe,translated:Ae}},[$,J,$e,Ae,I,Ie,xe,ve,z,m,he,Me]),Ts({...fe,delta:N,draggingRect:Ae,pointerCoordinates:et,scrollableAncestors:he,scrollableAncestorRects:He});const We=c.useMemo(()=>({active:$,activeNode:J,activeNodeRect:T,activatorEvent:Y,collisions:$e,containerNodeRect:Ee,dragOverlay:ae,draggableNodes:I,droppableContainers:z,droppableRects:ve,over:m,measureDroppableContainers:me,scrollableAncestors:he,scrollableAncestorRects:He,measuringConfiguration:G,measuringScheduled:Re,windowRect:Ue}),[$,J,T,Y,$e,Ee,ae,I,z,ve,m,me,he,He,G,Re,Ue]),pn=c.useMemo(()=>({activatorEvent:Y,activators:nt,active:$,activeNodeRect:T,ariaDescribedById:{draggable:de},dispatch:w,draggableNodes:I,over:m,measureDroppableContainers:me}),[Y,nt,$,T,w,de,I,m,me]);return B.createElement(Jn.Provider,{value:y},B.createElement(yt.Provider,{value:pn},B.createElement(hr.Provider,{value:We},B.createElement(Wt.Provider,{value:k},d)),B.createElement(Zs,{disabled:(l==null?void 0:l.restoreFocus)===!1})),B.createElement(ss,{...l,hiddenTextDescribedById:de}));function Yt(){const U=(ue==null?void 0:ue.autoScrollEnabled)===!1,te=typeof u=="object"?u.enabled===!1:u===!1,ne=A&&!U&&!te;return typeof u=="object"?{...u,enabled:ne}:{enabled:ne}}}),no=c.createContext(null),kn="button",ro="Draggable";function so(e){let{id:t,data:n,disabled:r=!1,attributes:s}=e;const i=bt(ro),{activators:a,activatorEvent:l,active:u,activeNodeRect:d,ariaDescribedById:h,draggableNodes:f,over:p}=c.useContext(yt),{role:g=kn,roleDescription:S="draggable",tabIndex:b=0}=s??{},v=(u==null?void 0:u.id)===t,w=c.useContext(v?Wt:no),[C,y]=Mt(),[R,E]=Mt(),A=Hs(a,t),j=gt(n);ge(()=>(f.set(t,{id:t,key:i,node:C,activatorNode:R,data:j}),()=>{const N=f.get(t);N&&N.key===i&&f.delete(t)}),[f,t]);const I=c.useMemo(()=>({role:g,tabIndex:b,"aria-disabled":r,"aria-pressed":v&&g===kn?!0:void 0,"aria-roledescription":S,"aria-describedby":h.draggable}),[r,g,b,v,S,h.draggable]);return{active:u,activatorEvent:l,activeNodeRect:d,attributes:I,isDragging:v,listeners:r?void 0:A,node:C,over:p,setNodeRef:y,setActivatorNodeRef:E,transform:w}}function pr(){return c.useContext(hr)}const oo="Droppable",io={timeout:25};function ao(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:s}=e;const i=bt(oo),{active:a,dispatch:l,over:u,measureDroppableContainers:d}=c.useContext(yt),h=c.useRef({disabled:n}),f=c.useRef(!1),p=c.useRef(null),g=c.useRef(null),{disabled:S,updateMeasurementsFor:b,timeout:v}={...io,...s},w=gt(b??r),C=c.useCallback(()=>{if(!f.current){f.current=!0;return}g.current!=null&&clearTimeout(g.current),g.current=setTimeout(()=>{d(Array.isArray(w.current)?w.current:[w.current]),g.current=null},v)},[v]),y=$t({callback:C,disabled:S||!a}),R=c.useCallback((I,N)=>{y&&(N&&(y.unobserve(N),f.current=!1),I&&y.observe(I))},[y]),[E,A]=Mt(R),j=gt(t);return c.useEffect(()=>{!y||!E.current||(y.disconnect(),f.current=!1,y.observe(E.current))},[E,y]),c.useEffect(()=>(l({type:X.RegisterDroppable,element:{id:r,key:i,disabled:n,node:E,rect:p,data:j}}),()=>l({type:X.UnregisterDroppable,key:i,id:r})),[r]),c.useEffect(()=>{n!==h.current.disabled&&(l({type:X.SetDroppableDisabled,id:r,key:i,disabled:n}),h.current.disabled=n)},[r,i,n,l]),{active:a,rect:p,isOver:(u==null?void 0:u.id)===r,node:E,over:u,setNodeRef:A}}function lo(e){let{animation:t,children:n}=e;const[r,s]=c.useState(null),[i,a]=c.useState(null),l=At(n);return!n&&!r&&l&&s(l),ge(()=>{if(!i)return;const u=r==null?void 0:r.key,d=r==null?void 0:r.props.id;if(u==null||d==null){s(null);return}Promise.resolve(t(d,i)).then(()=>{s(null)})},[t,r,i]),B.createElement(B.Fragment,null,n,r?c.cloneElement(r,{ref:a}):null)}const co={x:0,y:0,scaleX:1,scaleY:1};function uo(e){let{children:t}=e;return B.createElement(yt.Provider,{value:fr},B.createElement(Wt.Provider,{value:co},t))}const fo={position:"fixed",touchAction:"none"},ho=e=>Ft(e)?"transform 250ms ease":void 0,go=c.forwardRef((e,t)=>{let{as:n,activatorEvent:r,adjustScale:s,children:i,className:a,rect:l,style:u,transform:d,transition:h=ho}=e;if(!l)return null;const f=s?d:{...d,scaleX:1,scaleY:1},p={...fo,width:l.width,height:l.height,top:l.top,left:l.left,transform:Fe.Transform.toString(f),transformOrigin:s&&r?is(r,l):void 0,transition:typeof h=="function"?h(r):h,...u};return B.createElement(n,{className:a,style:p,ref:t},i)}),po=e=>t=>{let{active:n,dragOverlay:r}=t;const s={},{styles:i,className:a}=e;if(i!=null&&i.active)for(const[l,u]of Object.entries(i.active))u!==void 0&&(s[l]=n.node.style.getPropertyValue(l),n.node.style.setProperty(l,u));if(i!=null&&i.dragOverlay)for(const[l,u]of Object.entries(i.dragOverlay))u!==void 0&&r.node.style.setProperty(l,u);return a!=null&&a.active&&n.node.classList.add(a.active),a!=null&&a.dragOverlay&&r.node.classList.add(a.dragOverlay),function(){for(const[u,d]of Object.entries(s))n.node.style.setProperty(u,d);a!=null&&a.active&&n.node.classList.remove(a.active)}},vo=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:Fe.Transform.toString(t)},{transform:Fe.Transform.toString(n)}]},mo={duration:250,easing:"ease",keyframes:vo,sideEffects:po({styles:{active:{opacity:"0"}}})};function xo(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:s}=e;return zt((i,a)=>{if(t===null)return;const l=n.get(i);if(!l)return;const u=l.node.current;if(!u)return;const d=dr(a);if(!d)return;const{transform:h}=ee(a).getComputedStyle(a),f=tr(h);if(!f)return;const p=typeof t=="function"?t:bo(t);return lr(u,s.draggable.measure),p({active:{id:i,data:l.data,node:u,rect:s.draggable.measure(u)},draggableNodes:n,dragOverlay:{node:a,rect:s.dragOverlay.measure(d)},droppableContainers:r,measuringConfiguration:s,transform:f})})}function bo(e){const{duration:t,easing:n,sideEffects:r,keyframes:s}={...mo,...e};return i=>{let{active:a,dragOverlay:l,transform:u,...d}=i;if(!t)return;const h={x:l.rect.left-a.rect.left,y:l.rect.top-a.rect.top},f={scaleX:u.scaleX!==1?a.rect.width*u.scaleX/l.rect.width:1,scaleY:u.scaleY!==1?a.rect.height*u.scaleY/l.rect.height:1},p={x:u.x-h.x,y:u.y-h.y,...f},g=s({...d,active:a,dragOverlay:l,transform:{initial:u,final:p}}),[S]=g,b=g[g.length-1];if(JSON.stringify(S)===JSON.stringify(b))return;const v=r==null?void 0:r({active:a,dragOverlay:l,...d}),w=l.node.animate(g,{duration:t,easing:n,fill:"forwards"});return new Promise(C=>{w.onfinish=()=>{v==null||v(),C()}})}}let Nn=0;function yo(e){return c.useMemo(()=>{if(e!=null)return Nn++,Nn},[e])}const Ln=B.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:s,transition:i,modifiers:a,wrapperElement:l="div",className:u,zIndex:d=999}=e;const{activatorEvent:h,active:f,activeNodeRect:p,containerNodeRect:g,draggableNodes:S,droppableContainers:b,dragOverlay:v,over:w,measuringConfiguration:C,scrollableAncestors:y,scrollableAncestorRects:R,windowRect:E}=pr(),A=c.useContext(Wt),j=yo(f==null?void 0:f.id),I=gr(a,{activatorEvent:h,active:f,activeNodeRect:p,containerNodeRect:g,draggingNodeRect:v.rect,over:w,overlayNodeRect:v.rect,scrollableAncestors:y,scrollableAncestorRects:R,transform:A,windowRect:E}),N=gn(p),z=xo({config:r,draggableNodes:S,droppableContainers:b,measuringConfiguration:C}),M=N?v.setRef:void 0;return B.createElement(uo,null,B.createElement(lo,{animation:z},f&&j?B.createElement(go,{key:j,id:f.id,ref:M,as:l,activatorEvent:h,adjustScale:t,className:u,transition:i,rect:N,style:{zIndex:d,...s},transform:I},n):null))});function kt(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function wo(e,t){return e.reduce((n,r,s)=>{const i=t.get(r);return i&&(n[s]=i),n},Array(e.length))}function jt(e){return e!==null&&e>=0}function Co(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function jo(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const vr=e=>{let{rects:t,activeIndex:n,overIndex:r,index:s}=e;const i=kt(t,r,n),a=t[s],l=i[s];return!l||!a?null:{x:l.left-a.left,y:l.top-a.top,scaleX:l.width/a.width,scaleY:l.height/a.height}},St={scaleX:1,scaleY:1},zn=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:s,rects:i,overIndex:a}=e;const l=(t=i[n])!=null?t:r;if(!l)return null;if(s===n){const d=i[a];return d?{x:0,y:n<a?d.top+d.height-(l.top+l.height):d.top-l.top,...St}:null}const u=So(i,s,n);return s>n&&s<=a?{x:0,y:-l.height-u,...St}:s<n&&s>=a?{x:0,y:l.height+u,...St}:{x:0,y:0,...St}};function So(e,t,n){const r=e[t],s=e[t-1],i=e[t+1];return r?n<t?s?r.top-(s.top+s.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):s?r.top-(s.top+s.height):0:0}const mr="Sortable",xr=B.createContext({activeIndex:-1,containerId:mr,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:vr,disabled:{draggable:!1,droppable:!1}});function Fn(e){let{children:t,id:n,items:r,strategy:s=vr,disabled:i=!1}=e;const{active:a,dragOverlay:l,droppableRects:u,over:d,measureDroppableContainers:h}=pr(),f=bt(mr,n),p=l.rect!==null,g=c.useMemo(()=>r.map(A=>typeof A=="object"&&"id"in A?A.id:A),[r]),S=a!=null,b=a?g.indexOf(a.id):-1,v=d?g.indexOf(d.id):-1,w=c.useRef(g),C=!Co(g,w.current),y=v!==-1&&b===-1||C,R=jo(i);ge(()=>{C&&S&&h(g)},[C,g,S,h]),c.useEffect(()=>{w.current=g},[g]);const E=c.useMemo(()=>({activeIndex:b,containerId:f,disabled:R,disableTransforms:y,items:g,overIndex:v,useDragOverlay:p,sortedRects:wo(g,u),strategy:s}),[b,f,R.draggable,R.droppable,y,g,v,u,p,s]);return B.createElement(xr.Provider,{value:E},t)}const Do=e=>{let{id:t,items:n,activeIndex:r,overIndex:s}=e;return kt(n,r,s).indexOf(t)},Ro=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:s,items:i,newIndex:a,previousItems:l,previousContainerId:u,transition:d}=e;return!d||!r||l!==i&&s===a?!1:n?!0:a!==s&&t===u},Eo={duration:200,easing:"ease"},br="transform",Io=Fe.Transition.toString({property:br,duration:0,easing:"linear"}),Mo={roleDescription:"sortable"};function Ao(e){let{disabled:t,index:n,node:r,rect:s}=e;const[i,a]=c.useState(null),l=c.useRef(n);return ge(()=>{if(!t&&n!==l.current&&r.current){const u=s.current;if(u){const d=Qe(r.current,{ignoreTransform:!0}),h={x:u.left-d.left,y:u.top-d.top,scaleX:u.width/d.width,scaleY:u.height/d.height};(h.x||h.y)&&a(h)}}n!==l.current&&(l.current=n)},[t,n,r,s]),c.useEffect(()=>{i&&a(null)},[i]),i}function Oo(e){let{animateLayoutChanges:t=Ro,attributes:n,disabled:r,data:s,getNewIndex:i=Do,id:a,strategy:l,resizeObserverConfig:u,transition:d=Eo}=e;const{items:h,containerId:f,activeIndex:p,disabled:g,disableTransforms:S,sortedRects:b,overIndex:v,useDragOverlay:w,strategy:C}=c.useContext(xr),y=To(r,g),R=h.indexOf(a),E=c.useMemo(()=>({sortable:{containerId:f,index:R,items:h},...s}),[f,s,R,h]),A=c.useMemo(()=>h.slice(h.indexOf(a)),[h,a]),{rect:j,node:I,isOver:N,setNodeRef:z}=ao({id:a,data:E,disabled:y.droppable,resizeObserverConfig:{updateMeasurementsFor:A,...u}}),{active:M,activatorEvent:V,activeNodeRect:$,attributes:q,setNodeRef:ue,listeners:De,isDragging:Y,over:x,setActivatorNodeRef:P,transform:de}=so({id:a,data:E,attributes:{...Mo,...n},disabled:y.draggable}),Ye=Hr(z,ue),G=!!M,ve=G&&!S&&jt(p)&&jt(v),me=!w&&Y,Re=me&&ve?de:null,Be=ve?Re??(l??C)({rects:b,activeNodeRect:$,activeIndex:p,overIndex:v,index:R}):null,fe=jt(p)&&jt(v)?i({id:a,items:h,activeIndex:p,overIndex:v}):R,oe=M==null?void 0:M.id,T=c.useRef({activeId:oe,items:h,newIndex:fe,containerId:f}),Ee=h!==T.current.items,ie=t({active:M,containerId:f,isDragging:Y,isSorting:G,id:a,index:R,items:h,newIndex:T.current.newIndex,previousItems:T.current.items,previousContainerId:T.current.containerId,transition:d,wasDragging:T.current.activeId!=null}),we=Ao({disabled:!ie,index:R,node:I,rect:j});return c.useEffect(()=>{G&&T.current.newIndex!==fe&&(T.current.newIndex=fe),f!==T.current.containerId&&(T.current.containerId=f),h!==T.current.items&&(T.current.items=h)},[G,fe,f,h]),c.useEffect(()=>{if(oe===T.current.activeId)return;if(oe!=null&&T.current.activeId==null){T.current.activeId=oe;return}const Ie=setTimeout(()=>{T.current.activeId=oe},50);return()=>clearTimeout(Ie)},[oe]),{active:M,activeIndex:p,attributes:q,data:E,rect:j,index:R,newIndex:fe,items:h,isOver:N,isSorting:G,isDragging:Y,listeners:De,node:I,overIndex:v,over:x,setNodeRef:Ye,setActivatorNodeRef:P,setDroppableNodeRef:z,setDraggableNodeRef:ue,transform:we??Be,transition:ae()};function ae(){if(we||Ee&&T.current.newIndex===R)return Io;if(!(me&&!Ft(V)||!d)&&(G||ie))return Fe.Transition.toString({...d,property:br})}}function To(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function Nt(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&typeof t.sortable=="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const Po=[O.Down,O.Right,O.Up,O.Left],ko=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:s,droppableContainers:i,over:a,scrollableAncestors:l}}=t;if(Po.includes(e.code)){if(e.preventDefault(),!n||!r)return;const u=[];i.getEnabled().forEach(f=>{if(!f||f!=null&&f.disabled)return;const p=s.get(f.id);if(p)switch(e.code){case O.Down:r.top<p.top&&u.push(f);break;case O.Up:r.top>p.top&&u.push(f);break;case O.Left:r.left>p.left&&u.push(f);break;case O.Right:r.left<p.left&&u.push(f);break}});const d=ls({collisionRect:r,droppableRects:s,droppableContainers:u});let h=Zn(d,"id");if(h===(a==null?void 0:a.id)&&d.length>1&&(h=d[1].id),h!=null){const f=i.get(n.id),p=i.get(h),g=p?s.get(p.id):null,S=p==null?void 0:p.node.current;if(S&&g&&f&&p){const v=Bt(S).some((A,j)=>l[j]!==A),w=yr(f,p),C=No(f,p),y=v||!w?{x:0,y:0}:{x:C?r.width-g.width:0,y:C?r.height-g.height:0},R={x:g.left,y:g.top};return y.x&&y.y?R:pt(R,y)}}}};function yr(e,t){return!Nt(e)||!Nt(t)?!1:e.data.current.sortable.containerId===t.data.current.sortable.containerId}function No(e,t){return!Nt(e)||!Nt(t)||!yr(e,t)?!1:e.data.current.sortable.index<t.data.current.sortable.index}const Lo=({onCreateProject:e,showCreateButton:t=!0,size:n="medium",fullWidth:r=!0})=>{const{projects:s,currentProjectId:i,setCurrentProject:a,getProjectResults:l}=on(),{canAccessProFeatures:u}=an(),d=f=>{const p=f.target.value;a(p==="default"?null:p)},h=f=>f.id===i?o.jsx(Yn,{fontSize:"small"}):f.isLocal?o.jsx(at,{fontSize:"small"}):o.jsx(Xe,{fontSize:"small"});return!u&&s.length<=1?null:o.jsxs(F,{display:"flex",alignItems:"center",gap:1,children:[o.jsxs(Bn,{size:n,fullWidth:r,children:[o.jsx($n,{id:"project-selector-label",children:"Project"}),o.jsx(Wn,{labelId:"project-selector-label",id:"project-selector",value:i||"default",label:"Project",onChange:d,children:s.map(f=>o.jsx(Ne,{value:f.id,children:o.jsxs(F,{display:"flex",alignItems:"center",gap:1,width:"100%",children:[h(f),o.jsx(se,{variant:"body2",sx:{flexGrow:1},children:f.name}),o.jsx(ht,{label:l(f.id).length,size:"small",variant:"outlined",sx:{minWidth:"auto",height:20}}),!f.isLocal&&o.jsx(Z,{title:"Cloud Project",children:o.jsx(Xe,{fontSize:"small",color:"primary"})})]})},f.id))})]}),t&&u&&e&&o.jsx(Z,{title:"Create New Project",children:o.jsx(Le,{color:"primary",onClick:e,size:n,children:o.jsx(Xn,{})})})]})},zo=({open:e,onClose:t})=>{const{projects:n,createProject:r,deleteProject:s,getProjectResults:i,saveProjectToCloud:a,loadProjectFromCloud:l,listCloudProjects:u,syncProjects:d,syncStatus:h,syncErrors:f,manualSyncProject:p}=on(),{canAccessProFeatures:g}=an(),[S,b]=c.useState(""),[v,w]=c.useState(!1),[C,y]=c.useState(null),[R,E]=c.useState([]),[A,j]=c.useState(!1);c.useEffect(()=>{e&&g&&(I(),d())},[e,g]);const I=async()=>{try{w(!0);const x=await u();E(x)}catch(x){y(x instanceof Error?x.message:"Failed to load cloud projects")}finally{w(!1)}},N=async()=>{if(!S.trim()){y("Project name is required");return}try{w(!0),y(null),await r(S.trim()),b(""),j(!1)}catch(x){y(x instanceof Error?x.message:"Failed to create project")}finally{w(!1)}},z=async x=>{if(x==="default"){y("Cannot delete the default project");return}try{w(!0),y(null),await s(x)}catch(P){y(P instanceof Error?P.message:"Failed to delete project")}finally{w(!1)}},M=async x=>{try{w(!0),y(null),await a(x),await I()}catch(P){y(P instanceof Error?P.message:"Failed to save project to cloud")}finally{w(!1)}},V=async x=>{try{w(!0),y(null),await l(x)}catch(P){y(P instanceof Error?P.message:"Failed to load project from cloud")}finally{w(!1)}},$=async x=>{try{y(null),await p(x)}catch(P){y(P instanceof Error?P.message:"Failed to sync project")}},q=x=>x.isLocal?o.jsx(at,{}):o.jsx(Xe,{color:"primary"}),ue=x=>{switch(h[x]){case"syncing":return o.jsx(en,{size:16});case"success":return o.jsx(Kn,{color:"success",fontSize:"small"});case"error":return o.jsx(It,{color:"error",fontSize:"small"});default:return null}},De=n.filter(x=>x.isLocal),Y=n.filter(x=>!x.isLocal);return o.jsxs(_t,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,children:[o.jsx(Qt,{children:o.jsxs(F,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[o.jsx(se,{variant:"h6",children:"Project Management"}),o.jsx(Le,{onClick:t,size:"small",children:o.jsx(wr,{})})]})}),o.jsxs(Zt,{children:[C&&o.jsx(Ve,{severity:"error",sx:{mb:2},onClose:()=>y(null),children:C}),!g&&o.jsx(Ve,{severity:"info",sx:{mb:2},children:"Project organization is available for Pro users. Upgrade to organize your results into projects."}),o.jsxs(F,{mb:3,children:[o.jsxs(F,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[o.jsx(se,{variant:"h6",children:"Local Projects"}),g&&o.jsx(Q,{startIcon:o.jsx(Xn,{}),onClick:()=>j(!0),variant:"outlined",size:"small",children:"New Project"})]}),A&&o.jsxs(F,{mb:2,p:2,border:1,borderColor:"divider",borderRadius:1,children:[o.jsx(Cr,{fullWidth:!0,label:"Project Name",value:S,onChange:x=>b(x.target.value),onKeyPress:x=>x.key==="Enter"&&N(),disabled:v,size:"small",sx:{mb:2}}),o.jsxs(F,{display:"flex",gap:1,children:[o.jsx(Q,{onClick:N,variant:"contained",size:"small",disabled:v||!S.trim(),children:"Create"}),o.jsx(Q,{onClick:()=>{j(!1),b("")},size:"small",children:"Cancel"})]})]}),o.jsx(vn,{dense:!0,children:De.map(x=>o.jsxs(Ht,{children:[o.jsx(lt,{children:q(x)}),o.jsx(ct,{primary:x.name,secondary:`${i(x.id).length} results • ${x.lastModified.toLocaleDateString()}`}),o.jsxs(F,{display:"flex",alignItems:"center",gap:1,children:[o.jsx(ht,{label:i(x.id).length,size:"small",variant:"outlined"}),g&&x.id!=="default"&&o.jsxs(o.Fragment,{children:[o.jsx(Z,{title:"Save to Cloud",children:o.jsx(Le,{size:"small",onClick:()=>M(x.id),disabled:v,children:o.jsx(jr,{})})}),o.jsx(Z,{title:"Delete Project",children:o.jsx(Le,{size:"small",color:"error",onClick:()=>z(x.id),disabled:v,children:o.jsx(Et,{})})})]})]})]},x.id))})]}),g&&o.jsxs(o.Fragment,{children:[o.jsx(Un,{sx:{my:2}}),o.jsxs(F,{children:[o.jsx(se,{variant:"h6",mb:2,children:"Cloud Projects"}),v&&o.jsx(F,{display:"flex",justifyContent:"center",p:2,children:o.jsx(en,{size:24})}),o.jsxs(vn,{dense:!0,children:[Y.map(x=>o.jsxs(Ht,{children:[o.jsx(lt,{children:q(x)}),o.jsx(ct,{primary:x.name,secondary:`${i(x.id).length} results • Cloud`}),o.jsxs(F,{display:"flex",alignItems:"center",gap:1,children:[o.jsx(ht,{label:i(x.id).length,size:"small",variant:"outlined",color:"primary"}),ue(x.id),f[x.id]&&o.jsx(Z,{title:f[x.id],children:o.jsx(It,{color:"error",fontSize:"small"})}),o.jsx(Z,{title:"Sync Project",children:o.jsx(Le,{size:"small",color:"primary",onClick:()=>$(x.id),disabled:v||h[x.id]==="syncing",children:o.jsx(Sr,{})})}),o.jsx(Z,{title:"Delete Project",children:o.jsx(Le,{size:"small",color:"error",onClick:()=>z(x.id),disabled:v,children:o.jsx(Et,{})})})]})]},x.id)),R.filter(x=>!Y.some(P=>{var de;return((de=P.cloudProject)==null?void 0:de.id)===x.id})).map(x=>o.jsxs(Ht,{children:[o.jsx(lt,{children:o.jsx(Xe,{color:"action"})}),o.jsx(ct,{primary:x.project_name,secondary:`Available in cloud • ${new Date(x.created_at).toLocaleDateString()}`}),o.jsx(Z,{title:"Load from Cloud",children:o.jsx(Le,{size:"small",color:"primary",onClick:()=>V(x.id),disabled:v,children:o.jsx(Hn,{})})})]},x.id))]}),R.length===0&&!v&&o.jsx(se,{variant:"body2",color:"text.secondary",textAlign:"center",py:2,children:"No cloud projects found. Save a local project to cloud to get started."})]})]})]}),o.jsx(tn,{children:o.jsx(Q,{onClick:t,children:"Close"})})]})},Dt=({result:e,position:t,onToggleSelection:n,onMenuOpen:r,onRemove:s,getResultTypeIcon:i,canAccessProFeatures:a,isDragging:l=!1})=>{const{attributes:u,listeners:d,setNodeRef:h,transform:f,transition:p,isDragging:g}=Oo({id:e.id}),S={transform:Fe.Transform.toString(f),transition:p||"transform 200ms ease",opacity:l||g?.5:1,cursor:l?"grabbing":"grab",zIndex:l||g?1e3:"auto"},b=v=>new Date(v).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return o.jsxs(Nr,{ref:h,style:S,variant:"outlined",sx:{"&:hover":{boxShadow:2,borderColor:"primary.main",transform:"translateY(-2px)"},transition:"all 0.2s ease-in-out",...l||g?{boxShadow:4,borderColor:"primary.main",backgroundColor:"action.hover"}:{}},children:[o.jsxs(Lr,{sx:{position:"relative"},children:[t>0&&!l&&o.jsx(Z,{title:`Position ${t} in current order`,placement:"top",children:o.jsx(F,{sx:{position:"absolute",top:8,right:8,backgroundColor:"primary.main",color:"primary.contrastText",borderRadius:"50%",width:28,height:28,display:"flex",alignItems:"center",justifyContent:"center",fontSize:"0.8rem",fontWeight:"bold",zIndex:1,boxShadow:2,transition:"all 0.2s ease","&:hover":{transform:"scale(1.1)",boxShadow:3}},children:t})}),o.jsxs(F,{display:"flex",alignItems:"center",mb:1,children:[o.jsx(zr,{checked:e.selected||!1,onChange:()=>n(e.id)}),o.jsxs(F,{display:"flex",alignItems:"center",ml:1,sx:{flexGrow:1},children:[o.jsx(Z,{title:"Drag to reorder",placement:"top",children:o.jsx(F,{...u,...d,sx:{cursor:"grab",display:"flex",alignItems:"center","&:active":{cursor:"grabbing"}},children:o.jsx(Gn,{sx:{color:"text.secondary",mr:1,transition:"color 0.2s ease","&:hover":{color:"primary.main",transform:"scale(1.1)"},"&:active":{color:"primary.dark"}}})})}),i(e.type),o.jsx(se,{variant:"subtitle1",ml:1,sx:{flexGrow:1},children:e.title}),a&&o.jsx(Le,{size:"small",onClick:v=>r(v,e.id),children:o.jsx(Fr,{fontSize:"small"})})]})]}),o.jsx(Un,{sx:{my:1}}),o.jsxs(F,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[o.jsx(ht,{label:e.type.charAt(0).toUpperCase()+e.type.slice(1),size:"small",color:"primary",variant:"outlined"}),o.jsx(se,{variant:"caption",color:"text.secondary",children:b(e.timestamp)})]}),o.jsxs(se,{variant:"body2",color:"text.secondary",mt:1,children:["From: ",e.component]})]}),o.jsx(Br,{children:o.jsx(Q,{size:"small",color:"error",startIcon:o.jsx(Et,{}),onClick:()=>s&&s(e.id),children:"Remove"})})]})},Fo=()=>{const{results:e,projects:t,currentProjectId:n,addResult:r,removeResult:s,clearResults:i,toggleResultSelection:a,selectAllResults:l,deselectAllResults:u,getSelectedResults:d,exportToHTML:h,getProjectResults:f,moveResultToProject:p,setCurrentProject:g,syncStatus:S,syncErrors:b,reorderResults:v}=on(),{canAccessProFeatures:w}=an(),[C,y]=c.useState("all"),[R,E]=c.useState(!1),[A,j]=c.useState(!1),[I,N]=c.useState("html"),[z,M]=c.useState(!1),[V,$]=c.useState(""),[q,ue]=c.useState(!1),[De,Y]=c.useState(""),[x,P]=c.useState(!1),[de,Ye]=c.useState(new Set(["default"])),[G,ve]=c.useState("projects"),[me,Re]=c.useState(null),[J,Be]=c.useState(null),[fe,oe]=c.useState(null),[T,Ee]=c.useState(null),[ie,we]=c.useState(!1),ae=os(yn(hn,{activationConstraint:{distance:5}}),yn(dn,{coordinateGetter:ko}));c.useEffect(()=>{const m=Object.entries(b).filter(([D,L])=>L);if(m.length>0){const[D,L]=m[0],k=t.find(W=>W.id===D),_=(k==null?void 0:k.name)||"Unknown Project";Y(`Failed to sync "${_}": ${L}`),ue(!0)}},[b,t]);const Ie=m=>{y(m.target.value)},xe=e.filter(m=>{if(C==="all")return!0;const D=new Date(m.timestamp),k=Math.floor((new Date().getTime()-D.getTime())/(1e3*60*60*24));switch(C){case"today":return k===0;case"week":return k<=7;case"month":return k<=30;case"older":return k>30;default:return!0}}),wt=m=>{const D=new Set(de);D.has(m)?D.delete(m):D.add(m),Ye(D)},Ze=(m,D)=>{Re(m.currentTarget),Be(D)},Ue=()=>{Re(null),Be(null)},he=m=>{J&&(p(J,m),$("Result moved successfully"),M(!0)),Ue()},He=(m,D)=>D?o.jsx(Yn,{}):m.isLocal?o.jsx(at,{}):o.jsx(Xe,{color:"primary"}),Ce=m=>{switch(m){case"descriptive":return o.jsx(mn,{});case"normality":return o.jsx(xn,{});case"ttest":return o.jsx(Yr,{});case"anova":return o.jsx(Xr,{});case"correlation":return o.jsx(Wr,{});case"regression":return o.jsx(xn,{});case"nonparametric":return o.jsx($r,{});default:return o.jsx(mn,{})}},et=m=>{const{active:D}=m;oe(D.id),we(!0);const L=e.find(k=>k.id===D.id);Ee(L||null)},tt=m=>{const{active:D,over:L}=m;if(D.id!==(L==null?void 0:L.id))if(w&&G==="projects"){const k=e.find(W=>W.id===D.id),_=e.find(W=>W.id===(L==null?void 0:L.id));if(k&&_&&k.projectId===_.projectId){const W=f(k.projectId||"default"),je=W.findIndex(We=>We.id===D.id),nt=W.findIndex(We=>We.id===(L==null?void 0:L.id));if(je!==-1&&nt!==-1){const We=kt(W,je,nt),Yt=[...e.filter(U=>U.projectId!==k.projectId),...We];v(Yt)}}}else{const k=e.findIndex(W=>W.id===D.id),_=e.findIndex(W=>W.id===(L==null?void 0:L.id));if(k!==-1&&_!==-1){const W=kt(e,k,_);v(W)}}oe(null),Ee(null),we(!1)},Ke=m=>{s(m),$("Result removed"),M(!0)},Xt=m=>{const D=S[m],L=b[m];if(L)return o.jsx(Z,{title:`Sync Error: ${L}`,children:o.jsx(It,{fontSize:"small",color:"error"})});switch(D){case"syncing":return o.jsx(Z,{title:"Syncing...",children:o.jsx(en,{size:16})});case"success":return o.jsx(Z,{title:"Synced successfully",children:o.jsx(Kn,{fontSize:"small",color:"success"})});case"error":return o.jsx(Z,{title:"Sync failed",children:o.jsx(It,{fontSize:"small",color:"error"})});default:return null}},Me=async()=>{if(d().length===0){$("Please select at least one result to export"),M(!0);return}try{h(),$("Results successfully exported to HTML format")}catch(D){console.error("Export error:",D),$(`Error exporting results: ${D instanceof Error?D.message:String(D)}`)}finally{M(!0),j(!1)}},Ae=()=>{if(d().length===0){$("No results selected"),M(!0);return}E(!0)},$e=()=>{const m=d();m.forEach(D=>s(D.id)),E(!1),$(`${m.length} result(s) deleted`),M(!0)},Ct=()=>{if(d().length===0){$("No results selected"),M(!0);return}j(!0)};return o.jsxs(F,{p:3,children:[o.jsxs(F,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[o.jsx(se,{variant:"h5",children:"Results Manager"}),o.jsxs(F,{display:"flex",gap:2,children:[!1,w&&o.jsx(Q,{startIcon:o.jsx(Dr,{}),onClick:()=>P(!0),variant:"outlined",children:"Manage Projects"})]})]}),o.jsx(Rr,{elevation:2,sx:{p:2,mb:3},children:o.jsxs(ke,{container:!0,spacing:2,alignItems:"center",children:[w&&o.jsx(ke,{item:!0,xs:12,sm:6,md:4,children:o.jsx(Lo,{onCreateProject:()=>P(!0),showCreateButton:!1})}),o.jsx(ke,{item:!0,xs:12,sm:6,md:4,children:o.jsxs(Bn,{fullWidth:!0,children:[o.jsx($n,{id:"date-filter-select-label",children:"Filter by Date"}),o.jsxs(Wn,{labelId:"date-filter-select-label",id:"date-filter-select",value:C,label:"Filter by Date",onChange:Ie,children:[o.jsx(Ne,{value:"all",children:"All Results"}),o.jsx(Ne,{value:"today",children:"Today"}),o.jsx(Ne,{value:"week",children:"Past Week"}),o.jsx(Ne,{value:"month",children:"Past Month"}),o.jsx(Ne,{value:"older",children:"Older than Month"})]})]})}),o.jsx(ke,{item:!0,xs:12,children:o.jsxs(F,{display:"flex",justifyContent:"flex-end",gap:1,flexWrap:"wrap",sx:{"& > *":{minWidth:{xs:"120px",sm:"auto"}}},children:[o.jsx(Q,{variant:"outlined",startIcon:o.jsx(Er,{}),onClick:l,disabled:e.length===0,size:"small",children:"Select All"}),o.jsx(Q,{variant:"outlined",startIcon:o.jsx(Ir,{}),onClick:u,disabled:e.length===0,size:"small",children:"Deselect All"}),o.jsx(Q,{variant:"contained",color:"primary",startIcon:o.jsx(Hn,{}),onClick:Ct,disabled:d().length===0,size:"small",children:"Export Selected"}),o.jsx(Q,{variant:"outlined",color:"error",startIcon:o.jsx(Et,{}),onClick:Ae,disabled:d().length===0,size:"small",children:"Delete Selected"})]})})]})}),e.length===0?o.jsx(Ve,{severity:"info",sx:{mt:2},children:"No analysis results found. Run some analyses to collect results."}):xe.length===0?o.jsx(Ve,{severity:"info",sx:{mt:2},children:"No results match the selected filter."}):w?o.jsx(F,{children:t.map(m=>{const D=f(m.id).filter(k=>{if(C==="all")return!0;const _=new Date(k.timestamp),je=Math.floor((new Date().getTime()-_.getTime())/(1e3*60*60*24));switch(C){case"today":return je===0;case"week":return je<=7;case"month":return je<=30;case"older":return je>30;default:return!0}});if(D.length===0)return null;const L=de.has(m.id);return o.jsxs(Mr,{expanded:L,onChange:()=>wt(m.id),sx:{mb:1},children:[o.jsx(Ar,{expandIcon:o.jsx(Or,{}),children:o.jsxs(F,{display:"flex",alignItems:"center",gap:1,width:"100%",children:[He(m,L),o.jsx(se,{variant:"h6",sx:{flexGrow:1},children:m.name}),o.jsx(ht,{label:D.length,size:"small",variant:"outlined",color:m.isLocal?"default":"primary"}),!m.isLocal&&o.jsxs(o.Fragment,{children:[o.jsx(Z,{title:"Cloud Project",children:o.jsx(Xe,{fontSize:"small",color:"primary"})}),Xt(m.id)]})]})}),o.jsx(Tr,{children:o.jsxs(Pn,{sensors:ae,collisionDetection:jn,onDragStart:et,onDragEnd:tt,children:[o.jsx(Fn,{items:D.map(k=>k.id),strategy:zn,children:o.jsx(ke,{container:!0,spacing:2,children:D.map((k,_)=>o.jsx(ke,{item:!0,xs:12,sm:6,md:4,children:o.jsx(Dt,{result:k,position:_+1,onToggleSelection:a,onMenuOpen:Ze,onRemove:Ke,getResultTypeIcon:Ce,canAccessProFeatures:w,isDragging:fe===k.id})},k.id))})}),o.jsx(Ln,{children:T?o.jsx(Dt,{result:T,position:0,onToggleSelection:a,onMenuOpen:Ze,onRemove:Ke,getResultTypeIcon:Ce,canAccessProFeatures:w,isDragging:!0}):null})]})})]},m.id)})}):o.jsxs(Pn,{sensors:ae,collisionDetection:jn,onDragStart:et,onDragEnd:tt,children:[o.jsx(Fn,{items:xe.map(m=>m.id),strategy:zn,children:o.jsx(ke,{container:!0,spacing:2,children:xe.map((m,D)=>o.jsx(ke,{item:!0,xs:12,sm:6,md:4,children:o.jsx(Dt,{result:m,position:D+1,onToggleSelection:a,onMenuOpen:()=>{},onRemove:Ke,getResultTypeIcon:Ce,canAccessProFeatures:!1,isDragging:fe===m.id})},m.id))})}),o.jsx(Ln,{children:T?o.jsx(Dt,{result:T,position:0,onToggleSelection:a,onMenuOpen:()=>{},onRemove:Ke,getResultTypeIcon:Ce,canAccessProFeatures:!1,isDragging:!0}):null})]}),o.jsxs(_t,{open:A,onClose:()=>j(!1),children:[o.jsx(Qt,{children:"Export Results"}),o.jsxs(Zt,{children:[o.jsx(se,{variant:"body1",gutterBottom:!0,children:"Select export format:"}),o.jsx(F,{display:"flex",justifyContent:"center",mt:2,children:o.jsx(Q,{variant:"contained",startIcon:o.jsx(Pr,{}),disabled:!0,fullWidth:!0,children:"HTML Export"})}),o.jsxs(se,{variant:"body2",color:"text.secondary",mt:2,children:[d().length," result(s) selected for export"]})]}),o.jsxs(tn,{children:[o.jsx(Q,{onClick:()=>j(!1),children:"Cancel"}),o.jsx(Q,{onClick:Me,variant:"contained",color:"primary",children:"Export"})]})]}),o.jsxs(_t,{open:R,onClose:()=>E(!1),children:[o.jsx(Qt,{children:"Delete Selected Results"}),o.jsx(Zt,{children:o.jsxs(se,{variant:"body1",children:["Are you sure you want to delete ",d().length," selected result(s)? This action cannot be undone."]})}),o.jsxs(tn,{children:[o.jsx(Q,{onClick:()=>E(!1),children:"Cancel"}),o.jsx(Q,{onClick:$e,color:"error",variant:"contained",children:"Delete Selected"})]})]}),o.jsx(zo,{open:x,onClose:()=>P(!1)}),o.jsxs(kr,{anchorEl:me,open:!!me,onClose:Ue,children:[o.jsxs(Ne,{onClick:()=>he("default"),children:[o.jsx(lt,{children:o.jsx(at,{fontSize:"small"})}),o.jsx(ct,{primary:"Default Project"})]}),t.filter(m=>m.id!=="default").map(m=>o.jsxs(Ne,{onClick:()=>he(m.id),children:[o.jsx(lt,{children:m.isLocal?o.jsx(at,{fontSize:"small"}):o.jsx(Xe,{fontSize:"small",color:"primary"})}),o.jsx(ct,{primary:m.name})]},m.id))]}),o.jsx(Kt,{open:z,autoHideDuration:6e3,onClose:()=>M(!1),message:V}),o.jsx(Kt,{open:q,autoHideDuration:8e3,onClose:()=>ue(!1),anchorOrigin:{vertical:"top",horizontal:"center"},children:o.jsx(Ve,{onClose:()=>ue(!1),severity:"error",sx:{width:"100%"},children:De})}),o.jsx(Kt,{open:ie,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:o.jsx(Ve,{severity:"info",sx:{width:"100%"},icon:o.jsx(Gn,{}),children:"Reordering results... Drop to place in new position"})})]})},Go=()=>o.jsx(Ur,{maxWidth:"lg",children:o.jsx(F,{sx:{mt:4,mb:4},children:o.jsx(Fo,{})})});export{Go as default};
