import{u as z,j as e,aG as B,aH as E,aI as M,aJ as F,aK as H,aL as y,aM as O,X as V,a0 as G,aN as L,aO as f,ad as U,aP as $,aQ as j,aR as Q,aS as Y,ax as J,aT as K,J as X,B as r,aU as c,aV as _,aW as Z,G as o,e as i,f as g,aX as N,i as l,R as p,l as x,K as u,M as ee,k,I as te}from"./mui-libs-CfwFIaTD.js";import{b as d}from"./react-libs-Cr2nE3UY.js";import{b as ae,d as se}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const ie=c(p)(({theme:a})=>({background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.primary.dark} 100%)`,color:"white",padding:a.spacing(4),borderRadius:a.spacing(2),marginBottom:a.spacing(4),position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:"-50%",right:"-10%",width:"300px",height:"300px",borderRadius:"50%",background:l(a.palette.common.white,.1)}})),A=c(r)(({theme:a})=>({marginBottom:a.spacing(2),marginTop:a.spacing(4)})),v=c(k)(({theme:a})=>({height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out",borderRadius:a.spacing(2),border:`1px solid ${l(a.palette.primary.main,.1)}`,"&:hover":{transform:"translateY(-8px)",boxShadow:`0 12px 20px ${l(a.palette.primary.main,.15)}`,borderColor:a.palette.primary.main}})),w=c(p)(({theme:a})=>({padding:a.spacing(3),height:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",transition:"all 0.3s ease",cursor:"pointer",borderRadius:a.spacing(2),backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,textDecoration:"none","&:hover":{transform:"translateY(-4px)",boxShadow:a.shadows[8],borderColor:a.palette.primary.main,"& .icon-wrapper":{transform:"scale(1.1)"}}})),m=c(r)(({theme:a,color:s})=>({width:56,height:56,borderRadius:a.spacing(2),display:"flex",alignItems:"center",justifyContent:"center",background:l(s||a.palette.primary.main,.1),marginBottom:a.spacing(2)})),I=c(r)(({theme:a,color:s})=>({width:64,height:64,borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",background:l(s||a.palette.primary.main,.1),marginBottom:a.spacing(1.5),transition:"transform 0.3s ease"})),C=c(g)(({theme:a})=>({justifyContent:"flex-start",paddingLeft:a.spacing(1),paddingRight:a.spacing(1),marginBottom:a.spacing(.5),textTransform:"none",color:a.palette.text.secondary,"&:hover":{backgroundColor:l(a.palette.primary.main,.08),color:a.palette.primary.main}})),q=c(te)(({theme:a})=>({color:"white","&:hover":{backgroundColor:l(a.palette.common.white,.1)}})),xe=({onNavigate:a})=>{const s=z(),{canAccessPublicationReady:b,canAccessAdvancedAnalysis:S}=ae(),P=[{title:"Import Data",icon:e.jsx(B,{}),path:"/data-management/import",color:s.palette.info.main},{title:"Export Data",icon:e.jsx(E,{}),path:"/data-management/export",color:s.palette.success.main},{title:"Data Editor",icon:e.jsx(M,{}),path:"/data-management/editor",color:s.palette.warning.main},{title:"Variable Editor",icon:e.jsx(F,{}),path:"/data-management/variables",color:s.palette.error.main},{title:"Transform Data",icon:e.jsx(H,{}),path:"/data-management/transform",color:s.palette.secondary.main},{title:"Sample Datasets",icon:e.jsx(y,{}),path:"/data-management/samples",color:s.palette.primary.main}],R=[{title:"Descriptive Statistics",icon:e.jsx(O,{}),color:s.palette.success.main,path:"/stats/descriptives",navigationPath:"/app#stats",description:"Explore and summarize your data",subItems:[{title:"Descriptives",path:"/stats/descriptives"},{title:"Frequencies",path:"/stats/frequencies"},{title:"Cross Tabulation",path:"/stats/crosstabs"},{title:"Normality Test",path:"/stats/normality"}],requiresAccess:!1,hasAccess:!0},{title:"Inferential Statistics",icon:e.jsx(V,{}),color:s.palette.warning.main,path:"/inference/ttest",navigationPath:"/app#inferential-stats",description:"Test hypotheses and make inferences",subItems:[{title:"t-Tests",path:"/inference/ttest"},{title:"ANOVA",path:"/inference/anova"},{title:"Non-parametric Tests",path:"/inference/nonparametric"},{title:"Assumption Checker",path:"/inference/assumptions"}],requiresAccess:!1,hasAccess:!0},{title:"Correlation Analysis",icon:e.jsx(G,{}),color:s.palette.error.main,path:"/correlation/pearson",navigationPath:"/app#correlation-analysis",description:"Explore relationships between variables",subItems:[{title:"Correlation",path:"/correlation/pearson"},{title:"Linear Regression",path:"/correlation/linear"},{title:"Logistic Regression",path:"/correlation/logistic"}],requiresAccess:!1,hasAccess:!0},{title:"Advanced Analysis",icon:e.jsx(L,{}),color:s.palette.secondary.main,path:"/advanced/survival",navigationPath:"/app#advanced-analysis",description:"Sophisticated statistical methods",subItems:[{title:"Survival Analysis",path:"/advanced/survival"},{title:"Reliability Analysis",path:"/advanced/reliability"},{title:"Mediation/Moderation",path:"/advanced/mediation"},{title:"Exploratory Factor Analysis",path:"/advanced/efa"}],requiresAccess:!0,hasAccess:S}],D=[{title:"Publication Ready",icon:e.jsx(f,{}),color:s.palette.primary.main,path:"/publication-ready/table1",navigationPath:"/app#publication-ready",description:"Craft Publication-Ready Tables/Figures + Expert Write-Ups",subItems:[{title:"Table 1",path:"/publication-ready/table1"},{title:"Table 1a",path:"/publication-ready/table1a"},{title:"SMD Table",path:"/publication-ready/smd-table"},{title:"Table 2",path:"/publication-ready/table2"},{title:"Flow Diagram",path:"/publication-ready/flow-diagram"},{title:"Regression Table",path:"/publication-ready/regression-table"}],requiresAccess:!0,hasAccess:b},{title:"Data Visualization",icon:e.jsx(U,{}),color:s.palette.info.main,path:"/charts/bar",navigationPath:"/app#charts",description:"Create compelling visual representations",subItems:[{title:"Bar Charts",path:"/charts/bar"},{title:"Pie Charts",path:"/charts/pie"},{title:"Histograms",path:"/charts/histogram"},{title:"Box Plots",path:"/charts/boxplot"},{title:"Scatter Plots",path:"/charts/scatter"},{title:"Sankey Diagrams",path:"/charts/sankey"},{title:"Pivot Charts",path:"/pivot/tables"}],requiresAccess:!1,hasAccess:!0}],W=[{title:"Dataset Manager",icon:e.jsx(y,{}),path:"data-management/datasets",color:s.palette.primary.main},{title:"Sample Size Calculators",icon:e.jsx($,{}),path:"/samplesize",color:s.palette.warning.main},{title:"Epi Calculators",icon:e.jsx(se,{}),path:"/epicalc",color:s.palette.error.main},{title:"Pivot Analysis",icon:e.jsx(j,{}),path:"/pivot",color:s.palette.success.main},{title:"AI Assistant",icon:e.jsx(Q,{}),path:"/assistant",color:s.palette.secondary.main},{title:"Publication Tools",icon:e.jsx(f,{}),path:"/publication-ready",color:s.palette.success.main,requiresAccess:!0,hasAccess:b}],T=[{title:"Guided Workflows",icon:e.jsx(Y,{}),path:"/inference/workflow",description:"Step-by-step analysis guides",color:s.palette.primary.main},{title:"Which Analysis?",icon:e.jsx(J,{}),path:"/assistant",description:"AI Powered, find the right Analysis",color:s.palette.success.main},{title:"Video Tutorials",icon:e.jsx(K,{}),path:"/video-tutorials",description:"Learn with video guides",color:s.palette.warning.main},{title:"Methods Guide",icon:e.jsx(X,{}),path:"/statisticalmethods",description:"Statistical methods explained",color:s.palette.info.main}];return e.jsxs(r,{sx:{p:3,maxWidth:1400,margin:"0 auto"},children:[e.jsxs(ie,{elevation:0,children:[e.jsxs(r,{sx:{position:"absolute",top:24,right:24,display:"flex",gap:1},children:[e.jsx(q,{onClick:()=>a("/profile"),children:e.jsx(_,{})}),e.jsx(q,{onClick:()=>a("/settings"),children:e.jsx(Z,{})})]}),e.jsxs(o,{container:!0,alignItems:"center",spacing:3,children:[e.jsxs(o,{item:!0,xs:12,md:8,children:[e.jsx(i,{variant:"h3",fontWeight:"bold",gutterBottom:!0,children:"Welcome to DataStatPro"}),e.jsx(i,{variant:"h6",sx:{opacity:.9,mb:3},children:"Powerful statistical analysis made simple. DataStatPro provides powerful tools for data analysis, visualization, and statistical inference. Use the intuitive interface to explore your data, run statistical tests, and generate publication-quality tables and visualizations."}),e.jsx(i,{variant:"h6",sx:{opacity:.9,mb:3},children:"Save time with AI-crafted narratives that transform results into ready-to-publish insights instantly."}),e.jsxs(r,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(g,{variant:"contained",color:"secondary",size:"large",startIcon:e.jsx(N,{}),onClick:()=>a("/stats/descriptives"),sx:{borderRadius:3,textTransform:"none",fontWeight:"bold"},children:"Start Analysis"}),e.jsx(g,{variant:"outlined",size:"large",sx:{borderRadius:3,textTransform:"none",color:"white",borderColor:"white","&:hover":{borderColor:"white",backgroundColor:l(s.palette.common.white,.1)}},onClick:()=>a("data-management/import"),children:"Import Data"})]})]}),e.jsx(o,{item:!0,xs:12,md:4,sx:{textAlign:"center"},children:e.jsx(r,{sx:{position:"relative",zIndex:1},children:e.jsx(j,{sx:{fontSize:120,opacity:.3}})})})]})]}),e.jsxs(p,{sx:{p:3,mb:4,borderRadius:2},elevation:0,variant:"outlined",children:[e.jsx(i,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:"Quick Actions"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Frequently used tools for quick access"}),e.jsx(o,{container:!0,spacing:3,children:W.map(t=>e.jsx(o,{item:!0,xs:6,sm:4,md:2,children:e.jsxs(w,{onClick:()=>{(!t.requiresAccess||t.hasAccess)&&a(t.path)},elevation:0,sx:{...t.requiresAccess&&!t.hasAccess&&{opacity:.6,cursor:"not-allowed"}},children:[e.jsx(I,{className:"icon-wrapper",color:t.color,children:d.cloneElement(t.icon,{sx:{fontSize:32,color:t.color}})}),e.jsx(r,{sx:{display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:e.jsxs(r,{sx:{display:"flex",alignItems:"center",mb:t.requiresAccess&&!t.hasAccess?.5:0},children:[e.jsx(i,{variant:"subtitle1",fontWeight:"medium",align:"center",children:t.title}),t.requiresAccess&&!t.hasAccess&&e.jsx(r,{sx:{ml:.5,bgcolor:s.palette.warning.main,color:"white",fontSize:"0.5rem",px:.5,borderRadius:.5,display:"inline-flex",alignItems:"center",height:12},children:"PRO"})]})})]})},t.title))})]}),e.jsxs(p,{sx:{p:3,mb:4,borderRadius:2},elevation:0,variant:"outlined",children:[e.jsx(i,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:"Data Management"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Import, export, and manage your data"}),e.jsx(o,{container:!0,spacing:3,children:P.map(t=>e.jsx(o,{item:!0,xs:6,sm:4,md:2,children:e.jsxs(w,{onClick:()=>a(t.path),elevation:0,children:[e.jsx(I,{className:"icon-wrapper",color:t.color,children:d.cloneElement(t.icon,{sx:{fontSize:32,color:t.color}})}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",align:"center",children:t.title})]})},t.title))})]}),e.jsxs(A,{children:[e.jsx(i,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:"Statistical Analysis"}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Comprehensive statistical tools for every research need"})]}),e.jsx(o,{container:!0,spacing:3,sx:{mb:4},children:R.map(t=>e.jsx(o,{item:!0,xs:12,sm:6,md:3,children:e.jsx(v,{elevation:0,sx:{cursor:!t.requiresAccess||t.hasAccess?"pointer":"default",...t.requiresAccess&&!t.hasAccess&&{opacity:.7}},onClick:()=>{(!t.requiresAccess||t.hasAccess)&&(window.location.href=t.navigationPath)},children:e.jsxs(x,{sx:{p:3},children:[e.jsx(m,{color:t.color,children:d.cloneElement(t.icon,{sx:{color:t.color}})}),e.jsxs(r,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(i,{variant:"h6",fontWeight:"bold",children:t.title}),t.requiresAccess&&!t.hasAccess&&e.jsx(r,{sx:{ml:1,bgcolor:s.palette.warning.main,color:"white",fontSize:"0.65rem",px:.7,borderRadius:1,display:"inline-flex",alignItems:"center",height:16},children:"PRO"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",gutterBottom:!0,sx:{mb:2,minHeight:40},children:t.description}),e.jsxs(r,{children:[t.subItems.map(n=>e.jsx(C,{onClick:h=>{h.stopPropagation(),(!t.requiresAccess||t.hasAccess)&&a(n.path)},fullWidth:!0,size:"small",disabled:t.requiresAccess&&!t.hasAccess,sx:{...t.requiresAccess&&!t.hasAccess&&{opacity:.6,cursor:"not-allowed"}},children:n.title},n.title)),e.jsx(r,{sx:{mt:1,textAlign:"center"},children:e.jsx(u,{component:"button",variant:"body2",onClick:n=>{n.stopPropagation(),(!t.requiresAccess||t.hasAccess)&&(window.location.href=t.navigationPath)},sx:{color:t.color,textDecoration:"none",fontWeight:"medium",cursor:!t.requiresAccess||t.hasAccess?"pointer":"not-allowed",opacity:t.requiresAccess&&!t.hasAccess?.6:1,"&:hover":{textDecoration:!t.requiresAccess||t.hasAccess?"underline":"none"}},disabled:t.requiresAccess&&!t.hasAccess,children:"more →"})})]})]})})},t.title))}),e.jsxs(A,{children:[e.jsx(i,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:"Visualization & Reporting"}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Create stunning visualizations and publication-ready outputs"})]}),e.jsx(o,{container:!0,spacing:3,sx:{mb:4},children:D.map(t=>e.jsx(o,{item:!0,xs:12,md:6,children:e.jsx(v,{elevation:0,sx:{cursor:!t.requiresAccess||t.hasAccess?"pointer":"default",...t.requiresAccess&&!t.hasAccess&&{opacity:.7}},onClick:()=>{(!t.requiresAccess||t.hasAccess)&&(window.location.href=t.navigationPath)},children:e.jsxs(x,{sx:{p:3},children:[e.jsxs(r,{display:"flex",alignItems:"flex-start",mb:2,children:[e.jsx(m,{color:t.color,children:d.cloneElement(t.icon,{sx:{color:t.color}})}),e.jsxs(r,{sx:{ml:2,flex:1},children:[e.jsxs(r,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(i,{variant:"h6",fontWeight:"bold",children:t.title}),t.requiresAccess&&!t.hasAccess&&e.jsx(r,{sx:{ml:1,bgcolor:s.palette.warning.main,color:"white",fontSize:"0.65rem",px:.7,borderRadius:1,display:"inline-flex",alignItems:"center",height:16},children:"PRO"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:t.description})]})]}),e.jsxs(r,{sx:{ml:9},children:[t.subItems.map(n=>e.jsx(C,{onClick:h=>{h.stopPropagation(),(!t.requiresAccess||t.hasAccess)&&a(n.path)},fullWidth:!0,startIcon:e.jsx(ee,{sx:{fontSize:16}}),disabled:t.requiresAccess&&!t.hasAccess,sx:{...t.requiresAccess&&!t.hasAccess&&{opacity:.6,cursor:"not-allowed"}},children:n.title},n.title)),e.jsx(r,{sx:{mt:1,textAlign:"center"},children:e.jsx(u,{component:"button",variant:"body2",onClick:n=>{n.stopPropagation(),(!t.requiresAccess||t.hasAccess)&&(window.location.href=t.navigationPath)},sx:{color:t.color,textDecoration:"none",fontWeight:"medium",cursor:!t.requiresAccess||t.hasAccess?"pointer":"not-allowed",opacity:t.requiresAccess&&!t.hasAccess?.6:1,"&:hover":{textDecoration:!t.requiresAccess||t.hasAccess?"underline":"none"}},disabled:t.requiresAccess&&!t.hasAccess,children:"more →"})})]})]})})},t.title))}),e.jsxs(p,{sx:{p:3,borderRadius:2,backgroundColor:l(s.palette.primary.main,.02)},elevation:0,children:[e.jsx(i,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:"Help & Resources"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Get help and learn how to make the most of our platform"}),e.jsx(o,{container:!0,spacing:2,sx:{mb:3},children:T.map(t=>e.jsx(o,{item:!0,xs:12,sm:6,md:3,children:e.jsx(k,{onClick:()=>a(t.path),sx:{height:"100%",textDecoration:"none",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:4}},elevation:0,variant:"outlined",children:e.jsxs(x,{sx:{textAlign:"center",p:3},children:[e.jsx(m,{color:t.color,sx:{margin:"0 auto",mb:2},children:d.cloneElement(t.icon,{sx:{fontSize:28,color:t.color}})}),e.jsx(i,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:t.title}),e.jsx(i,{variant:"body2",color:"text.secondary",children:t.description})]})})},t.title))}),e.jsx(r,{sx:{mt:3,pt:3,borderTop:`1px solid ${s.palette.divider}`,textAlign:"center"},children:e.jsxs(i,{variant:"body1",color:"text.secondary",sx:{lineHeight:1.8},children:["Explore powerful tools for guided analysis and insightful interpretations. For any bugs or feature requests, please email us at"," ",e.jsx(u,{href:"mailto:<EMAIL>",sx:{color:s.palette.primary.main,textDecoration:"none",fontWeight:"medium","&:hover":{textDecoration:"underline"}},children:"<EMAIL>"}),". We're here to help enhance your data analysis journey!"]})})]})]})};export{xe as default};
