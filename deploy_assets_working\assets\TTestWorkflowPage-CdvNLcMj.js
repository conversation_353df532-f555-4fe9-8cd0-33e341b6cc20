import{u as fe,j as e,B as o,e as n,aE as Se,I as $e,bs as Te,R,i as f,cc as Ce,M as Fe,O as Ve,cd as De,N as Ie,Z as ge,X as ke,a0 as Me,W as Ae,a2 as Ne,aF as Re,G as m,ag as ue,g as ne,af as re,ah as pe,ai as me,c6 as he,a_ as xe,aj as G,ak as L,a5 as Ee,f as qe,al as ze}from"./mui-libs-CfwFIaTD.js";import{b as Z,r as F}from"./react-libs-Cr2nE3UY.js";import{a as Pe,D as ie}from"./index-Bpan7Tbe.js";import{B as We}from"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import"./StatsCard-op8tGQ0a.js";import{D as Be}from"./DatasetSelector-G08QHuaN.js";import{V as oe}from"./VariableSelector-CPdlCsJ2.js";import{C as Ge,E as Le,G as Oe}from"./GuidedWorkflow-MMwzUT8W.js";import"./other-utils-CR9xr_gI.js";import{i as He}from"./t-tests-DXw1R1jD.js";import"./math-setup-BTRs7Kau.js";import{i as le}from"./normality-CwHD6Rjl.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./descriptive-Djo0s6H4.js";import"./math-lib-BOZ-XUok.js";const Ke=({steps:A,connections:E,title:h,description:V,variant:_="boxed",showLabels:s=!0,showIcons:q=!0,compact:r=!1,interactive:T=!0,onClick:g})=>{const x=fe(),b=t=>t.includes("data")||t.includes("import")?e.jsx(Ve,{}):t.includes("transform")||t.includes("process")?e.jsx(De,{}):t.includes("table")||t.includes("view")?e.jsx(Ie,{}):t.includes("stats")||t.includes("descriptive")?e.jsx(ge,{}):t.includes("test")||t.includes("inferential")?e.jsx(ke,{}):t.includes("correlation")||t.includes("relation")?e.jsx(Me,{}):t.includes("chart")||t.includes("plot")||t.includes("visual")?e.jsx(Ae,{}):t.includes("result")||t.includes("output")?e.jsx(Ne,{}):e.jsx(Re,{}),u=(t,a)=>{if(a)return a;switch(t){case"active":return x.palette.primary.main;case"completed":return x.palette.success.main;case"warning":return x.palette.warning.main;case"error":return x.palette.error.main;case"disabled":return x.palette.action.disabled;default:return x.palette.grey[700]}},v=(t,a)=>{const $=a||u(t);switch(t){case"active":return f($,.1);case"completed":return f($,.1);case"warning":return f($,.1);case"error":return f($,.1);case"disabled":return f(x.palette.action.disabled,.1);default:return f(x.palette.grey[700],.05)}},O=t=>{T&&t.state!=="disabled"&&(t.onClick?t.onClick():g&&g(t.id))},j=t=>{const a=u(t.state);return t.direction==="vertical"?e.jsxs(o,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",my:r?.5:1,position:"relative",height:r?16:24},children:[e.jsx(Ce,{fontSize:r?"small":"medium",sx:{color:a}}),t.label&&e.jsx(n,{variant:"caption",sx:{position:"absolute",right:-5,transform:"translateX(100%)",color:x.palette.text.secondary},children:t.label})]}):e.jsxs(o,{sx:{display:"flex",alignItems:"center",justifyContent:"center",mx:r?.5:1,position:"relative",width:r?16:24},children:[e.jsx(Fe,{fontSize:r?"small":"medium",sx:{color:a}}),t.label&&e.jsx(n,{variant:"caption",sx:{position:"absolute",bottom:-5,transform:"translateY(100%)",color:x.palette.text.secondary},children:t.label})]})},J=()=>e.jsx(o,{sx:{display:"flex",flexDirection:"row",flexWrap:"wrap",alignItems:"center",justifyContent:"center"},children:A.map((t,a)=>{const $=a===A.length-1,l=E.find(D=>D.from===t.id);return e.jsxs(Z.Fragment,{children:[e.jsxs(R,{sx:{p:r?1:2,display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",borderRadius:1,backgroundColor:v(t.state,t.color),border:`1px solid ${f(u(t.state,t.color),.3)}`,width:r?100:150,cursor:T&&t.state!=="disabled"?"pointer":"default",opacity:t.state==="disabled"?.6:1,transition:"all 0.2s","&:hover":{boxShadow:T&&t.state!=="disabled"?x.shadows[2]:"none",backgroundColor:T&&t.state!=="disabled"?f(u(t.state,t.color),.15):v(t.state,t.color)}},onClick:()=>O(t),children:[q&&e.jsx(o,{sx:{color:u(t.state,t.color),mb:1},children:t.icon||b(t.id)}),e.jsx(n,{variant:r?"body2":"subtitle2",sx:{fontWeight:500,color:u(t.state,t.color)},children:t.label}),t.description&&e.jsx(n,{variant:"caption",color:"text.secondary",children:t.description})]}),!$&&l&&j(l)]},t.id)})}),Q=()=>e.jsx(o,{sx:{display:"flex",flexDirection:"row",alignItems:"center",overflowX:"auto",pb:2},children:A.map((t,a)=>{const $=a===A.length-1,l=E.find(D=>D.from===t.id);return e.jsxs(Z.Fragment,{children:[e.jsxs(R,{sx:{px:r?2:3,py:r?1.5:2,display:"flex",alignItems:"center",borderRadius:1,backgroundColor:v(t.state,t.color),border:`1px solid ${f(u(t.state,t.color),.3)}`,minWidth:r?"auto":120,cursor:T&&t.state!=="disabled"?"pointer":"default",opacity:t.state==="disabled"?.6:1,transition:"all 0.2s","&:hover":{boxShadow:T&&t.state!=="disabled"?x.shadows[2]:"none",backgroundColor:T&&t.state!=="disabled"?f(u(t.state,t.color),.15):v(t.state,t.color)}},onClick:()=>O(t),children:[q&&e.jsx(o,{sx:{color:u(t.state,t.color),mr:1.5,display:"flex"},children:t.icon||b(t.id)}),e.jsxs(o,{children:[e.jsx(n,{variant:r?"body2":"subtitle2",sx:{fontWeight:500,color:u(t.state,t.color)},children:t.label}),t.description&&!r&&e.jsx(n,{variant:"caption",color:"text.secondary",display:"block",children:t.description})]})]}),!$&&l&&j(l)]},t.id)})}),ee=()=>e.jsx(o,{sx:{display:"flex",flexDirection:"column",alignItems:"stretch"},children:A.map((t,a)=>{const $=a===A.length-1,l=E.find(D=>D.from===t.id);return e.jsxs(Z.Fragment,{children:[e.jsxs(o,{sx:{display:"flex",alignItems:"flex-start",position:"relative"},children:[e.jsx(o,{sx:{width:24,height:24,borderRadius:"50%",backgroundColor:v(t.state,t.color),border:`2px solid ${u(t.state,t.color)}`,display:"flex",alignItems:"center",justifyContent:"center",zIndex:1},children:q&&t.icon&&Z.cloneElement(t.icon,{style:{fontSize:14,color:u(t.state,t.color)}})}),e.jsxs(o,{sx:{ml:2,flex:1,cursor:T&&t.state!=="disabled"?"pointer":"default",opacity:t.state==="disabled"?.6:1},onClick:()=>O(t),children:[e.jsx(n,{variant:"subtitle2",sx:{fontWeight:500,color:u(t.state,t.color)},children:t.label}),t.description&&e.jsx(n,{variant:"body2",color:"text.secondary",children:t.description})]})]}),!$&&e.jsx(o,{sx:{ml:"12px",height:24,borderLeft:`2px solid ${u(l==null?void 0:l.state)}`,my:.5}})]},t.id)})}),W=()=>{switch(_){case"process":return Q();case"timeline":return ee();case"boxed":default:return J()}};return e.jsxs(o,{children:[h&&e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(n,{variant:"subtitle1",fontWeight:"medium",children:h}),V&&e.jsx(Se,{title:V,arrow:!0,children:e.jsx($e,{size:"small",sx:{ml:.5},children:e.jsx(Te,{fontSize:"small"})})})]}),e.jsx(R,{sx:{p:r?1.5:3,backgroundColor:f(x.palette.background.default,.5),borderRadius:1},children:W()})]})},mt=()=>{const{datasets:A,currentDataset:E}=Pe(),h=fe(),[V,_]=F.useState((E==null?void 0:E.id)||""),[s,q]=F.useState("independent"),[r,T]=F.useState(""),[g,x]=F.useState(""),[b,u]=F.useState(""),[v,O]=F.useState(0),[j,J]=F.useState(.05),[Q,ee]=F.useState("two-sided"),[W,t]=F.useState("auto"),[a,$]=F.useState(null),l=A.find(i=>i.id===V),D=()=>{if(!l)return!1;switch(s){case"independent":return!!r&&!!g;case"paired":return!!r&&!!b;case"onesample":return!!r;default:return!1}},N=l&&g?[...new Set(l.data.map(i=>i[g]))]:[],H=N.length===2,te=()=>!l||!r?[]:l.data.map(i=>Number(i[r])).filter(i=>!isNaN(i)),de=i=>!l||!r||!g?[]:l.data.filter(d=>d[g]===i).map(d=>Number(d[r])).filter(d=>!isNaN(d)),be=()=>!l||!b?[]:l.data.map(i=>Number(i[b])).filter(i=>!isNaN(i)),ve=()=>{if(!(!l||!D()))try{let i,d=[],y=[],I=[];switch(s){case"independent":if(!H||(d=de(N[0]),y=de(N[1]),d.length<2||y.length<2))return;i=He(d,y);break;case"paired":if(d=te(),y=be(),d.length<2||d.length!==y.length)return;I=d.map((k,M)=>k-y[M]);const c=I.reduce((k,M)=>k+M,0)/I.length,p=Math.sqrt(I.reduce((k,M)=>k+Math.pow(M-c,2),0)/(I.length-1))/Math.sqrt(I.length),K=c/p,ae=I.length-1,se=2*(1-Math.min(.99,Math.abs(K)/10));i={t:K,df:ae,pValue:se,meanDifference:c,stdErrorDifference:p,confidenceInterval:[c-1.96*p,c+1.96*p]};break;case"onesample":if(d=te(),d.length<2)return;const S=d.reduce((k,M)=>k+M,0)/d.length,C=Math.sqrt(d.reduce((k,M)=>k+Math.pow(M-S,2),0)/(d.length-1)),Y=C/Math.sqrt(d.length),ce=(S-v)/Y,je=d.length-1,we=2*(1-Math.min(.99,Math.abs(ce)/10));i={t:ce,df:je,pValue:we,mean:S,stdDev:C,stdError:Y,testValue:v,confidenceInterval:[S-1.96*Y,S+1.96*Y]};break}if(s==="independent"){const c=d.reduce((S,C)=>S+C,0)/d.length,w=y.reduce((S,C)=>S+C,0)/y.length,p=Math.sqrt(d.reduce((S,C)=>S+Math.pow(C-c,2),0)/(d.length-1)),K=Math.sqrt(y.reduce((S,C)=>S+Math.pow(C-w,2),0)/(y.length-1)),ae=Math.sqrt(((d.length-1)*Math.pow(p,2)+(y.length-1)*Math.pow(K,2))/(d.length+y.length-2)),se=Math.abs(c-w)/ae;i={...i,group1:{name:String(N[0]),n:d.length,mean:c,sd:p},group2:{name:String(N[1]),n:y.length,mean:w,sd:K},effectSize:se}}let z="warning",P="Normality check could not be performed (e.g., insufficient data).";const Qe=.05,U=te();if(s==="independent"){if(U.length>1){const c=le(U),w=c.pValue<.001?"< 0.001":c.pValue.toFixed(3),p=c.statistic.toFixed(3);c.isNormal?(z="passed",P=`Dependent variable '${r}' appears normally distributed (Kolmogorov-Smirnov D=${p}, p=${w}).`):(z="failed",P=`Dependent variable '${r}' may not be normally distributed (Kolmogorov-Smirnov D=${p}, p=${w}). Consider Q-Q plots or alternative tests.`)}}else if(s==="paired"){if(I.length>1){const c=le(I),w=c.pValue<.001?"< 0.001":c.pValue.toFixed(3),p=c.statistic.toFixed(3);c.isNormal?(z="passed",P=`Differences between paired observations appear normally distributed (Kolmogorov-Smirnov D=${p}, p=${w}).`):(z="failed",P=`Differences between paired observations may not be normally distributed (Kolmogorov-Smirnov D=${p}, p=${w}). Consider Q-Q plots or alternative tests.`)}}else if(s==="onesample"&&U.length>1){const c=le(U),w=c.pValue<.001?"< 0.001":c.pValue.toFixed(3),p=c.statistic.toFixed(3);c.isNormal?(z="passed",P=`Sample data for '${r}' appears normally distributed (Kolmogorov-Smirnov D=${p}, p=${w}).`):(z="failed",P=`Sample data for '${r}' may not be normally distributed (Kolmogorov-Smirnov D=${p}, p=${w}). Consider Q-Q plots or alternative tests.`)}let X={name:"Homogeneity of variances",status:"warning",message:"Homogeneity of variances not applicable for this test type or not automatically checked."};s==="independent"&&(W==="auto"&&d.length>1&&y.length>1?X={name:"Homogeneity of variances",status:"warning",message:"Levene's test (auto-detect) temporarily disabled. Assuming equal variances for now if 'auto' selected."}:W==="assumed"?X={name:"Homogeneity of variances",status:"passed",message:"Equal variances assumed by user (Student's t-test used)."}:W==="not-assumed"&&(X={name:"Homogeneity of variances",status:"failed",message:"Equal variances not assumed by user (Welch's t-test used)."})),$({...i,testType:s,dependentVariable:r,groupingVariable:s==="independent"?g:void 0,secondVariable:s==="paired"?b:void 0,testValue:s==="onesample"?v:void 0,significanceLevel:j,timestamp:new Date,normalityAssumption:{name:"Normality",status:z,message:P},homogeneityAssumption:X})}catch(i){console.error("Error running analysis:",i)}},B=i=>i<.001?"p < 0.001":`p = ${i.toFixed(3)}`,ye=[{id:"select-dataset",title:"Select Dataset",description:"Choose the dataset for your analysis",content:e.jsx(Be,{value:V,onChange:i=>_(i),variant:"card",showEmpty:!1,required:!0,minRows:4,helperText:"Select a dataset with sufficient data for the analysis"}),validation:()=>V?!0:"Please select a dataset"},{id:"select-test-type",title:"Choose Test Type",description:"Select the appropriate t-test for your analysis question",content:e.jsxs(o,{children:[e.jsx(n,{variant:"body2",color:"text.secondary",paragraph:!0,children:"The type of t-test you choose depends on your research question and data structure:"}),e.jsxs(m,{container:!0,spacing:3,children:[e.jsx(m,{item:!0,xs:12,md:4,children:e.jsxs(R,{sx:{p:2,height:"100%",border:s==="independent"?`2px solid ${h.palette.primary.main}`:void 0,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:h.shadows[3],borderColor:f(h.palette.primary.main,.5)}},onClick:()=>q("independent"),children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(ue,{color:"primary",sx:{mr:1}}),e.jsx(n,{variant:"subtitle1",fontWeight:s==="independent"?"bold":"normal",children:"Independent Samples"})]}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Compare means between two unrelated groups"}),e.jsxs(n,{variant:"body2",sx:{mt:1},children:[e.jsx("strong",{children:"Example:"})," Comparing test scores between two different classes"]})]})}),e.jsx(m,{item:!0,xs:12,md:4,children:e.jsxs(R,{sx:{p:2,height:"100%",border:s==="paired"?`2px solid ${h.palette.primary.main}`:void 0,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:h.shadows[3],borderColor:f(h.palette.primary.main,.5)}},onClick:()=>q("paired"),children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(ue,{color:"secondary",sx:{mr:1}}),e.jsx(n,{variant:"subtitle1",fontWeight:s==="paired"?"bold":"normal",children:"Paired Samples"})]}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Compare means between two related measurements"}),e.jsxs(n,{variant:"body2",sx:{mt:1},children:[e.jsx("strong",{children:"Example:"})," Comparing pre-test and post-test scores for the same individuals"]})]})}),e.jsx(m,{item:!0,xs:12,md:4,children:e.jsxs(R,{sx:{p:2,height:"100%",border:s==="onesample"?`2px solid ${h.palette.primary.main}`:void 0,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:h.shadows[3],borderColor:f(h.palette.primary.main,.5)}},onClick:()=>q("onesample"),children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(ge,{color:"info",sx:{mr:1}}),e.jsx(n,{variant:"subtitle1",fontWeight:s==="onesample"?"bold":"normal",children:"One Sample"})]}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Compare a sample mean to a known or hypothesized value"}),e.jsxs(n,{variant:"body2",sx:{mt:1},children:[e.jsx("strong",{children:"Example:"})," Testing if average weight differs from a reference value"]})]})})]})]}),validation:()=>s?!0:"Please select a test type",helpContent:e.jsxs(o,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Choosing the Right t-Test"}),e.jsx(n,{variant:"body1",paragraph:!0,children:"A t-test is used to determine if there is a significant difference between means. The type of t-test you choose depends on your research question:"}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Independent Samples t-Test"}),e.jsx(n,{variant:"body1",paragraph:!0,children:"Use when comparing means between two unrelated groups. The groups should be independent of each other with no relationship between the members of each group."}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Paired Samples t-Test"}),e.jsx(n,{variant:"body1",paragraph:!0,children:"Use when comparing means between two related measurements, such as before-and-after measurements on the same subjects, or when subjects are matched pairs."}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"One Sample t-Test"}),e.jsx(n,{variant:"body1",paragraph:!0,children:"Use when comparing a sample mean to a known or hypothesized population value."})]})},{id:"select-variables",title:"Select Variables",description:"Choose the variables for your analysis based on the selected test type",content:e.jsx(o,{children:l?e.jsxs(m,{container:!0,spacing:3,children:[e.jsx(m,{item:!0,xs:12,children:e.jsx(oe,{label:"Dependent Variable (Numeric)",helperText:"Select the continuous variable you want to analyze",value:r,onChange:i=>T(i),datasetId:V,required:!0,allowedTypes:[ie.NUMERIC],variant:"autocomplete",placeholder:"Select a numeric variable"})}),s==="independent"&&e.jsxs(m,{item:!0,xs:12,children:[e.jsx(oe,{label:"Grouping Variable (Categorical)",helperText:H?`Groups found: ${N.join(", ")}`:"Select a categorical variable with exactly 2 groups",value:g,onChange:i=>x(i),datasetId:V,required:!0,allowedTypes:[ie.CATEGORICAL],variant:"autocomplete",placeholder:"Select a categorical variable",error:!!g&&!H}),g&&!H&&e.jsxs(ne,{severity:"warning",sx:{mt:2},children:[e.jsx(re,{children:"Variable Issue"}),"The grouping variable must have exactly 2 groups, but it has ",N.length," groups. Please select a different variable."]})]}),s==="paired"&&e.jsx(m,{item:!0,xs:12,children:e.jsx(oe,{label:"Second Variable (Numeric)",helperText:"Select the second numeric variable to compare with the dependent variable",value:b,onChange:i=>u(i),datasetId:V,required:!0,allowedTypes:[ie.NUMERIC],variant:"autocomplete",placeholder:"Select a numeric variable"})}),s==="onesample"&&e.jsx(m,{item:!0,xs:12,children:e.jsx(pe,{label:"Test Value",type:"number",value:v,onChange:i=>O(parseFloat(i.target.value)||0),fullWidth:!0,helperText:"Enter the value to compare your sample mean against",sx:{mt:2}})})]}):e.jsxs(ne,{severity:"warning",children:[e.jsx(re,{children:"No Dataset Selected"}),"Please go back and select a dataset first."]})}),validation:()=>{if(!l)return"Please select a dataset first";if(!r)return"Please select a dependent variable";if(s==="independent"){if(!g)return"Please select a grouping variable";if(!H)return"The grouping variable must have exactly 2 groups"}else if(s==="paired"){if(!b)return"Please select a second variable";if(b===r)return"The second variable must be different from the dependent variable"}return!0}},{id:"set-options",title:"Set Analysis Options",description:"Configure additional options for the t-test",content:e.jsxs(o,{children:[e.jsxs(m,{container:!0,spacing:3,children:[e.jsx(m,{item:!0,xs:12,sm:6,children:e.jsx(pe,{label:"Significance Level (α)",type:"number",value:j,onChange:i=>J(parseFloat(i.target.value)),inputProps:{min:.001,max:.999,step:.01},fullWidth:!0,size:"small",helperText:"Typical values: 0.05, 0.01, or 0.001"})}),e.jsx(m,{item:!0,xs:12,sm:6,children:e.jsxs(me,{component:"fieldset",children:[e.jsx(he,{component:"legend",children:"Alternative Hypothesis"}),e.jsxs(xe,{value:Q,onChange:i=>ee(i.target.value),row:!0,children:[e.jsx(G,{value:"two-sided",control:e.jsx(L,{size:"small"}),label:"Two-sided (≠)"}),e.jsx(G,{value:"less",control:e.jsx(L,{size:"small"}),label:"One-sided (<)"}),e.jsx(G,{value:"greater",control:e.jsx(L,{size:"small"}),label:"One-sided (>)"})]})]})}),s==="independent"&&e.jsx(m,{item:!0,xs:12,children:e.jsxs(me,{component:"fieldset",children:[e.jsx(he,{component:"legend",children:"Equal Variances"}),e.jsxs(xe,{value:W,onChange:i=>t(i.target.value),children:[e.jsx(G,{value:"auto",control:e.jsx(L,{size:"small"}),label:"Auto-detect (Levene's Test)"}),e.jsx(G,{value:"assumed",control:e.jsx(L,{size:"small"}),label:"Assumed (Student's t-test)"}),e.jsx(G,{value:"not-assumed",control:e.jsx(L,{size:"small"}),label:"Not Assumed (Welch's t-test)"})]})]})})]}),e.jsx(Ge,{title:"About Statistical Options",items:[{title:"Significance Level (α)",content:"The significance level is the probability of rejecting the null hypothesis when it is true. A lower value makes the test more conservative.",type:"info"},{title:"Alternative Hypothesis",content:"Two-sided tests check if there's any difference, while one-sided tests check if one mean is specifically greater than or less than the other.",type:"info"},{title:"Equal Variances",content:"For independent t-tests, you can choose whether to assume equal variances between groups. When in doubt, use 'Auto-detect'.",type:"tip"}],initiallyExpanded:!0,variant:"panel",showIcons:!0,width:"100%"})]}),validation:()=>isNaN(j)||j<=0||j>=1?"Significance level must be between 0 and 1":!0},{id:"review-analysis",title:"Review & Run Analysis",description:"Review your selections and run the t-test",content:e.jsxs(o,{children:[e.jsxs(R,{sx:{p:2,mb:3,backgroundColor:f(h.palette.background.default,.5)},children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Analysis Summary"}),e.jsxs(m,{container:!0,spacing:2,children:[e.jsxs(m,{item:!0,xs:12,sm:6,children:[e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Test Type:"})," ",s==="independent"?"Independent Samples t-Test":s==="paired"?"Paired Samples t-Test":"One Sample t-Test"]}),e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Dataset:"})," ",(l==null?void 0:l.name)||"None selected"]}),e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Dependent Variable:"})," ",r||"None selected"]})]}),e.jsxs(m,{item:!0,xs:12,sm:6,children:[s==="independent"&&e.jsxs(e.Fragment,{children:[e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Grouping Variable:"})," ",g||"None selected"]}),e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Groups:"})," ",N.join(", ")||"None available"]})]}),s==="paired"&&e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Second Variable:"})," ",b||"None selected"]}),s==="onesample"&&e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Test Value:"})," ",v]}),e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Significance Level:"})," ",j]}),e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Alternative Hypothesis:"})," ",Q]})]})]})]}),e.jsx(Ke,{title:"Analysis Process",steps:[{id:"data",label:"Dataset",description:(l==null?void 0:l.name)||"No dataset",state:l?"completed":"error"},{id:"variables",label:"Variables",description:r||"Not selected",state:r?"completed":"error"},{id:"test",label:"T-Test",description:`${s} test`,state:"active"},{id:"results",label:"Results",description:"Statistical analysis",state:void 0}],connections:[{from:"data",to:"variables",direction:"horizontal"},{from:"variables",to:"test",direction:"horizontal"},{from:"test",to:"results",direction:"horizontal"}],variant:"process",interactive:!1}),e.jsx(o,{sx:{textAlign:"center",mt:4},children:e.jsx(We,{gradient:!0,rounded:!0,startIcon:e.jsx(Ee,{}),onClick:ve,disabled:!D(),size:"large",children:"Run t-Test Analysis"})}),e.jsx(o,{sx:{mt:3},children:a?e.jsx(Le,{title:`${s==="independent"?"Independent Samples":s==="paired"?"Paired Samples":"One Sample"} t-Test Results`,description:s==="independent"?`Comparing ${r} between ${a.group1.name} and ${a.group2.name} groups`:s==="paired"?`Comparing ${r} and ${b}`:`Comparing ${r} to test value ${v}`,timestamp:a.timestamp,pValue:a.pValue,significance:j,stats:s==="independent"?[{label:"Group 1 Mean",value:a.group1.mean.toFixed(2)},{label:"Group 2 Mean",value:a.group2.mean.toFixed(2)},{label:"Mean Difference",value:(a.group1.mean-a.group2.mean).toFixed(2)},{label:"Effect Size",value:a.effectSize.toFixed(2),tooltip:"Cohen's d"}]:s==="paired"?[{label:"Mean Difference",value:a.meanDifference.toFixed(2)},{label:"Std. Error",value:a.stdErrorDifference.toFixed(2)}]:[{label:"Sample Mean",value:a.mean.toFixed(2)},{label:"Test Value",value:a.testValue},{label:"Difference",value:(a.mean-a.testValue).toFixed(2)},{label:"Std. Error",value:a.stdError.toFixed(2)}],statisticalTests:[{name:"t-value",value:a.t,description:"The t-statistic measures the size of the difference relative to the variation in your sample data"},{name:"degrees of freedom",value:a.df},{name:"p-value",value:a.pValue,pValue:a.pValue,significant:a.pValue<=j}],confidenceInterval:s==="independent"?void 0:a.confidenceInterval,chart:s==="independent"?e.jsx(o,{sx:{height:300,display:"flex",justifyContent:"center",alignItems:"flex-end",p:2},children:e.jsxs(o,{sx:{display:"flex",alignItems:"flex-end",height:"100%"},children:[e.jsxs(o,{sx:{display:"flex",flexDirection:"column",alignItems:"center",mx:3},children:[e.jsx(o,{sx:{width:60,height:`${a.group1.mean/Math.max(a.group1.mean,a.group2.mean)*200}px`,bgcolor:h.palette.primary.main,display:"flex",justifyContent:"center",alignItems:"flex-start",pt:1,color:"white",fontWeight:"bold",borderRadius:"4px 4px 0 0"},children:a.group1.mean.toFixed(1)}),e.jsx(n,{variant:"body2",sx:{mt:1},children:a.group1.name})]}),e.jsxs(o,{sx:{display:"flex",flexDirection:"column",alignItems:"center",mx:3},children:[e.jsx(o,{sx:{width:60,height:`${a.group2.mean/Math.max(a.group1.mean,a.group2.mean)*200}px`,bgcolor:h.palette.secondary.main,display:"flex",justifyContent:"center",alignItems:"flex-start",pt:1,color:"white",fontWeight:"bold",borderRadius:"4px 4px 0 0"},children:a.group2.mean.toFixed(1)}),e.jsx(n,{variant:"body2",sx:{mt:1},children:a.group2.name})]})]})}):e.jsx(n,{variant:"body2",color:"text.secondary",sx:{p:3,textAlign:"center"},children:"Chart visualization not available for this test type in the demo."}),chartTitle:s==="independent"?"Comparison of Group Means":void 0,table:s==="independent"?{columns:["Group","N","Mean","SD","SE Mean"],rows:[[a.group1.name,a.group1.n,a.group1.mean.toFixed(2),a.group1.sd.toFixed(2),(a.group1.sd/Math.sqrt(a.group1.n)).toFixed(2)],[a.group2.name,a.group2.n,a.group2.mean.toFixed(2),a.group2.sd.toFixed(2),(a.group2.sd/Math.sqrt(a.group2.n)).toFixed(2)]]}:void 0,interpretations:[a.pValue<=j?s==="independent"?`There is a statistically significant difference in ${r} between ${a.group1.name} (M = ${a.group1.mean.toFixed(2)}, SD = ${a.group1.sd.toFixed(2)}) and ${a.group2.name} (M = ${a.group2.mean.toFixed(2)}, SD = ${a.group2.sd.toFixed(2)}); t(${a.df}) = ${a.t.toFixed(2)}, ${B(a.pValue)}.`:s==="paired"?`There is a statistically significant difference between ${r} and ${b}; t(${a.df}) = ${a.t.toFixed(2)}, ${B(a.pValue)}.`:`The sample mean (M = ${a.mean.toFixed(2)}, SD = ${a.stdDev.toFixed(2)}) is significantly different from the test value (${v}); t(${a.df}) = ${a.t.toFixed(2)}, ${B(a.pValue)}.`:s==="independent"?`There is no statistically significant difference in ${r} between ${a.group1.name} (M = ${a.group1.mean.toFixed(2)}, SD = ${a.group1.sd.toFixed(2)}) and ${a.group2.name} (M = ${a.group2.mean.toFixed(2)}, SD = ${a.group2.sd.toFixed(2)}); t(${a.df}) = ${a.t.toFixed(2)}, ${B(a.pValue)}.`:s==="paired"?`There is no statistically significant difference between ${r} and ${b}; t(${a.df}) = ${a.t.toFixed(2)}, ${B(a.pValue)}.`:`The sample mean (M = ${a.mean.toFixed(2)}, SD = ${a.stdDev.toFixed(2)}) is not significantly different from the test value (${v}); t(${a.df}) = ${a.t.toFixed(2)}, ${B(a.pValue)}.`],assumptions:[{name:"Independence of observations",status:"passed",message:"Assumed based on study design"},{name:"No significant outliers",status:"warning",message:"Check your data for outliers using box plots"},a.normalityAssumption,...s==="independent"&&a.homogeneityAssumption?[a.homogeneityAssumption]:[]],footnotes:[`The significance level (α) was set at ${j}.`],variant:"default"}):e.jsxs(ne,{severity:"info",children:[e.jsx(re,{children:"Analysis Results"}),'Results will appear here after clicking "Run t-Test Analysis".']})})]}),validation:()=>D()?!0:"Please complete all previous steps before running the analysis"}];return e.jsxs(o,{sx:{p:3,maxWidth:1200,margin:"0 auto"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(qe,{startIcon:e.jsx(ze,{}),sx:{mr:2},component:"a",href:"#/",children:"Back to Home"}),e.jsx(n,{variant:"h5",component:"h1",children:"t-Test Analysis Workflow"})]}),e.jsx(n,{variant:"body1",paragraph:!0,color:"text.secondary",children:"This guided workflow will help you perform a t-test analysis on your data. Follow the steps below to select your data, choose the appropriate test type, and interpret the results."}),e.jsx(R,{sx:{p:3,mb:3},children:e.jsx(Oe,{steps:ye,title:"t-Test Analysis",description:"Follow these steps to conduct a t-test analysis on your data",variant:"vertical",saveProgress:!0,persistenceKey:"ttest-workflow",enableBookmarking:!0,showStepNavigation:!0,allowSkipSteps:!1})})]})};export{mt as default};
