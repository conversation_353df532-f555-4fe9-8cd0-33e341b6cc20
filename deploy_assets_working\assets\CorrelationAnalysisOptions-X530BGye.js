import{j as e,u as y,C as f,R as l,i as t,e as a,B as s,h as c,G as d,k as v,bz as C,aE as w,I,aF as h,d as A,l as R,f as k,bA as D,a0 as n,aX as p,Z as B}from"./mui-libs-CfwFIaTD.js";import{r as F}from"./react-libs-Cr2nE3UY.js";const x=[{name:"Correlation Matrix",shortDescription:"Calculate and visualize correlations between variables",detailedDescription:"Generate a correlation matrix to explore the relationships between multiple variables. Includes options for <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlations, as well as significance testing and visualization.",path:"correlation/pearson",icon:e.jsx(n,{}),category:"Correlation",color:"#FF9800"},{name:"Linear Regression",shortDescription:"Model the linear relationship between variables",detailedDescription:"Perform simple and multiple linear regression to model the relationship between a dependent variable and one or more independent variables. Includes assumption checks, model diagnostics, and interpretation of results.",path:"correlation/linear",icon:e.jsx(p,{}),category:"Regression",color:"#4CAF50"},{name:"Logistic Regression",shortDescription:"Model the relationship with a binary outcome",detailedDescription:"Conduct logistic regression analysis to model the probability of a binary outcome based on one or more predictor variables. Includes odds ratios, confidence intervals, and model fit statistics.",path:"correlation/logistic",icon:e.jsx(B,{}),category:"Regression",color:"#2196F3"}],E=({onNavigate:u})=>{const o=y(),[i,m]=F.useState("All"),g=["All","Correlation","Regression"],b=i==="All"?x:x.filter(r=>r.category===i),j=r=>{switch(r){case"Correlation":return e.jsx(n,{});case"Regression":return e.jsx(p,{});default:return e.jsx(n,{})}};return e.jsxs(f,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(l,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${t(o.palette.primary.main,.1)} 0%, ${t(o.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(a,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Correlation Analysis Tools"}),e.jsx(a,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Explore relationships and build predictive models"}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Utilize a range of tools to understand the associations between your variables and build linear or logistic regression models."})]}),e.jsxs(s,{sx:{mb:4},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),e.jsx(s,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:g.map(r=>e.jsx(c,{label:r,onClick:()=>m(r),variant:i===r?"filled":"outlined",color:i===r?"primary":"default",icon:r!=="All"?j(r):void 0,sx:{"&:hover":{backgroundColor:i===r?o.palette.primary.dark:t(o.palette.primary.main,.1)}}},r))})]}),e.jsx(d,{container:!0,spacing:3,children:b.map((r,W)=>e.jsx(d,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(v,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:o.shadows[8],"& .launch-button":{backgroundColor:r.color,color:"white"}}},children:[e.jsx(C,{avatar:e.jsx(A,{sx:{bgcolor:r.color,width:48,height:48},children:r.icon}),title:e.jsx(a,{variant:"h6",fontWeight:"bold",children:r.name}),subheader:e.jsx(s,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:e.jsx(c,{label:r.category,size:"small",variant:"outlined",color:"primary"})}),action:e.jsx(w,{title:"More information",children:e.jsx(I,{size:"small",children:e.jsx(h,{})})})}),e.jsxs(R,{sx:{flexGrow:1,pt:0},children:[e.jsx(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:r.shortDescription}),e.jsx(a,{variant:"body2",paragraph:!0,children:r.detailedDescription})]}),e.jsx(s,{sx:{p:2,pt:0},children:e.jsxs(k,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>u(r.path),endIcon:e.jsx(D,{}),sx:{borderColor:r.color,color:r.color,fontWeight:"bold","&:hover":{borderColor:r.color}},children:["Launch ",r.name]})})]})},r.name))}),e.jsx(l,{elevation:1,sx:{p:3,mt:4,backgroundColor:t(o.palette.info.main,.05),border:`1px solid ${t(o.palette.info.main,.2)}`},children:e.jsxs(s,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[e.jsx(h,{color:"info"}),e.jsxs(s,{children:[e.jsx(a,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),e.jsxs(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Exploring relationships?"})," Start with Correlation Matrix"]}),e.jsxs(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Predicting a continuous outcome?"})," Use Linear Regression"]}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Predicting a binary outcome?"})," Use Logistic Regression"]})]})]})})]})};export{E as C,x as c};
