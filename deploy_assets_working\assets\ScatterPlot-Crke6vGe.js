import{u as Ye,j as e,B as o,e as d,G as re,R as Z,a6 as Ze,aE as _,a7 as pe,c7 as _e,aW as Fe,F as fe,ai as D,b9 as k,ba as w,bb as b,f as ve,ae as je,bD as ye,I as Ce,b7 as Oe,bH as Xe,ah as F,bN as ze,c8 as He,aj as se,b2 as ne}from"./mui-libs-CfwFIaTD.js";import{r as c,b as O}from"./react-libs-Cr2nE3UY.js";import{a as $e,D as Se}from"./index-Bpan7Tbe.js";import{l as X,P as qe}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./other-utils-CR9xr_gI.js";import"./charts-recharts-d3-BEF1Y_jn.js";const oe={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],sequential:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],diverging:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"]},De={title:"Scatter Plot",xAxisLabel:"X Variable",yAxisLabel:"Y Variable",zAxisLabel:"Z Variable",showGrid:!0,colorScheme:"default",pointSize:6,labelPoints:!1,use3D:!1,jitter:0},de="plotlyScatterDiv",it=()=>{const{datasets:E,currentDataset:j}=$e(),l=Ye(),A=c.useRef(null),[R,ce]=c.useState((j==null?void 0:j.id)||""),[V,H]=c.useState(""),[P,$]=c.useState(""),[L,q]=c.useState(""),[p,K]=c.useState(""),[W,U]=c.useState(""),[N,J]=c.useState(""),[a,ue]=c.useState(De),[C,Q]=c.useState("variables"),[z,G]=c.useState(null),[S,be]=c.useState(!1),[he,I]=c.useState(null),n=O.useMemo(()=>R&&E.find(t=>t.id===R)||null,[E,R]),y=O.useMemo(()=>(n==null?void 0:n.columns.filter(t=>t.type===Se.NUMERIC))||[],[n]),ke=O.useMemo(()=>(n==null?void 0:n.columns.filter(t=>t.type===Se.CATEGORICAL))||[],[n]),we=O.useMemo(()=>(n==null?void 0:n.columns)||[],[n]);c.useEffect(()=>{j!=null&&j.id&&j.id!==R&&(ce(j.id),H(""),$(""),q(""),K(""),U(""),J(""),G(null),I(null))},[j]),c.useEffect(()=>{if(z&&A.current){const t={responsive:!0,displayModeBar:!0,displaylogo:!1,modeBarButtonsToRemove:["pan2d","lasso2d","select2d","autoScale2d"]},r={...z.layout,height:500,width:void 0,autosize:!0,margin:{t:50,b:50,l:60,r:30}};X.newPlot(de,z.data,r,t)}return()=>{A.current&&typeof qe<"u"&&X.purge&&X.purge(A.current)}},[z]),c.useEffect(()=>{const t=r=>{r.altKey&&!r.ctrlKey&&!r.shiftKey&&(r.key==="1"?(r.preventDefault(),Q("variables")):r.key==="2"&&(r.preventDefault(),Q("settings")))};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)},[]);const Ae=t=>{ce(t.target.value),H(""),$(""),q(""),K(""),U(""),J(""),G(null),I(null)},Ve=t=>H(t.target.value),Pe=t=>$(t.target.value),Le=t=>q(t.target.value),Ie=t=>K(t.target.value),Re=t=>U(t.target.value),Ee=t=>J(t.target.value),v=(t,r)=>{t==="colorScheme"&&(r=r),ue(M=>({...M,[t]:r}))},xe=()=>{if(!n||!V||!P){I("Please select at least X and Y variables.");return}if(a.use3D&&!L){I("Please select a Z variable for 3D plot.");return}be(!0),I(null),G(null);try{const t=n.columns.find(i=>i.id===V),r=n.columns.find(i=>i.id===P),M=L?n.columns.find(i=>i.id===L):null,B=p?n.columns.find(i=>i.id===p):null,T=W?n.columns.find(i=>i.id===W):null,ee=N?n.columns.find(i=>i.id===N):null;if(!t||!r||L&&!M||p&&!B||W&&!T||N&&!ee)throw new Error("One or more selected columns not found.");let x=[];const te=[],ae=[];if(n.data.forEach((i,m)=>{const s=i[t.name],h=i[r.name],f=M?i[M.name]:void 0;if(typeof s!="number"||isNaN(s)||typeof h!="number"||isNaN(h)||a.use3D&&(typeof f!="number"||isNaN(f)))return;te.push(s),ae.push(h);const g={x:s,y:h};a.use3D&&typeof f=="number"&&(g.z=f),B&&typeof i[B.name]=="number"&&!isNaN(i[B.name])&&(g.size=i[B.name]),T&&(g.category=String(i[T.name])),g.label=ee?String(i[ee.name]):`Point ${m+1}`,x.push(g)}),x.length===0)throw new Error("No valid data points found for selected variables.");if(a.jitter>0){const i=a.jitter/100,m=Math.max(...te)-Math.min(...te),s=Math.max(...ae)-Math.min(...ae),h=m>0?i*m:0,f=s>0?i*s:0;x=x.map(g=>({...g,x:g.x+(Math.random()-.5)*h,y:g.y+(Math.random()-.5)*f}))}const le=[],ie=Te(),me=a.use3D?"scatter3d":"scattergl",ge=a.labelPoints?"markers+text":"markers";if(T)[...new Set(x.map(m=>m.category))].forEach((m,s)=>{const h=x.filter(u=>u.category===m),f=a.use3D?h.map(u=>u.z).filter(u=>typeof u=="number"):void 0;a.use3D&&(f==null?void 0:f.length)!==h.length&&console.warn("Some points filtered out for category",m,"due to missing Z value in 3D mode.");const g={x:h.map(u=>u.x),y:h.map(u=>u.y),z:f,type:me,mode:ge,name:m||"Undefined",text:a.labelPoints?h.map(u=>u.label??""):void 0,textposition:"top center",marker:{color:ie[s%ie.length],size:p?h.map(u=>u.size??a.pointSize):a.pointSize,sizemode:p?"diameter":void 0,sizeref:p?2*Math.max(...h.map(u=>u.size||0))/40**2:void 0,sizemin:4,opacity:.7},hoverinfo:"all"};a.use3D||delete g.z,le.push(g)});else{const i=a.use3D?x.map(s=>s.z).filter(s=>typeof s=="number"):void 0;a.use3D&&(i==null?void 0:i.length)!==x.length&&console.warn("Some points filtered out due to missing Z value in 3D mode.");const m={x:x.map(s=>s.x),y:x.map(s=>s.y),z:i,type:me,mode:ge,name:(r==null?void 0:r.name)||"Y",text:a.labelPoints?x.map(s=>s.label??""):void 0,textposition:"top center",marker:{color:ie[0],size:p?x.map(s=>s.size??a.pointSize):a.pointSize,sizemode:p?"diameter":void 0,sizeref:p?2*Math.max(...x.map(s=>s.size||0))/40**2:void 0,sizemin:4,opacity:.7},hoverinfo:"all"};a.use3D||delete m.z,le.push(m)}const Y={title:{text:a.title},margin:{t:50,b:50,l:60,r:30},paper_bgcolor:l.palette.mode==="dark"?l.palette.background.paper:"#fff",plot_bgcolor:l.palette.mode==="dark"?l.palette.background.default:"#fff",font:{color:l.palette.text.primary},hovermode:"closest",showlegend:!!T,legend:{bgcolor:l.palette.mode==="dark"?"rgba(51,51,51,0.8)":"rgba(255,255,255,0.8)",bordercolor:l.palette.divider,borderwidth:1},autosize:!0};a.use3D?Y.scene={xaxis:{title:{text:a.xAxisLabel},showgrid:a.showGrid,zeroline:!1,backgroundcolor:l.palette.mode==="dark"?"#222":"#fff",gridcolor:l.palette.divider,zerolinecolor:l.palette.divider},yaxis:{title:{text:a.yAxisLabel},showgrid:a.showGrid,zeroline:!1,backgroundcolor:l.palette.mode==="dark"?"#222":"#fff",gridcolor:l.palette.divider,zerolinecolor:l.palette.divider},zaxis:{title:{text:a.zAxisLabel},showgrid:a.showGrid,zeroline:!1,backgroundcolor:l.palette.mode==="dark"?"#222":"#fff",gridcolor:l.palette.divider,zerolinecolor:l.palette.divider},camera:{eye:{x:1.25,y:1.25,z:1.25}}}:(Y.xaxis={title:{text:a.xAxisLabel},showgrid:a.showGrid,zeroline:!1},Y.yaxis={title:{text:a.yAxisLabel},showgrid:a.showGrid,zeroline:!1}),G({data:le,layout:Y})}catch(t){I(`Error generating chart data: ${t instanceof Error?t.message:String(t)}`),G(null)}finally{be(!1)}},Ge=()=>{ue(De)},Me=()=>{if(z&&A.current){const t={format:"svg",filename:a.title.replace(/\s+/g,"_")||"scatterplot",width:A.current.offsetWidth||800,height:A.current.offsetHeight||600};X.downloadImage(de,t)}else I("Chart data not available for download.")},Be=()=>{xe()},Te=()=>oe[a.colorScheme]||oe.default,We=!V||!P||a.use3D&&!L||S,Ne=!z||S;return e.jsxs(o,{p:3,children:[e.jsx(d,{variant:"h5",gutterBottom:!0,children:"Scatter Plot Generator"}),e.jsx(o,{mb:2,p:2,sx:{backgroundColor:l.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)",borderRadius:1},children:e.jsxs(d,{variant:"body2",color:"text.secondary",children:["Generate scatter plots to explore relationships between variables. Supports 2D/3D plots with optional size, color, and label mappings. Use keyboard shortcuts: ",e.jsx("strong",{children:"Alt+1"})," for Variables panel, ",e.jsx("strong",{children:"Alt+2"})," for Settings panel."]})}),e.jsxs(re,{container:!0,spacing:2,children:[e.jsxs(re,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(Z,{elevation:1,sx:{mb:1,backgroundColor:l.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(Ze,{value:C,onChange:(t,r)=>Q(r),variant:"fullWidth",sx:{minHeight:48,"& .MuiTab-root":{minHeight:48,textTransform:"none",fontSize:"0.875rem",fontWeight:500,color:l.palette.text.secondary,"&.Mui-selected":{color:l.palette.primary.main},"&:hover":{color:l.palette.primary.main,backgroundColor:l.palette.action.hover}},"& .MuiTabs-indicator":{backgroundColor:l.palette.primary.main}},children:[e.jsx(_,{title:"Variable Selection Panel",placement:"top",children:e.jsx(pe,{value:"variables",label:"Variables",icon:e.jsx(_e,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(_,{title:"Chart Settings Panel",placement:"top",children:e.jsx(pe,{value:"settings",label:"Settings",icon:e.jsx(Fe,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(fe,{in:C==="variables",timeout:300,children:e.jsx(o,{sx:{display:C==="variables"?"block":"none"},children:e.jsxs(Z,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:l.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(d,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Data Selection"}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"dataset-select-label",children:"Dataset"}),e.jsx(w,{labelId:"dataset-select-label",value:R,label:"Dataset",onChange:Ae,disabled:E.length===0,children:E.length===0?e.jsx(b,{value:"",disabled:!0,children:"No datasets available"}):E.map(t=>e.jsxs(b,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]}),e.jsx(d,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,sx:{mt:2},children:"Required Variables"}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"x-variable-label",children:"X-Axis Variable"}),e.jsx(w,{labelId:"x-variable-label",value:V,label:"X-Axis Variable",onChange:Ve,disabled:y.length===0,children:y.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables"}):y.map(t=>e.jsx(b,{value:t.id,children:t.name},t.id))})]}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"y-variable-label",children:"Y-Axis Variable"}),e.jsx(w,{labelId:"y-variable-label",value:P,label:"Y-Axis Variable",onChange:Pe,disabled:y.length===0,children:y.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables"}):y.map(t=>e.jsx(b,{value:t.id,children:t.name},t.id))})]}),e.jsx(d,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,sx:{mt:2},children:"Additional Dimensions (Optional)"}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",disabled:!a.use3D,children:[e.jsx(k,{id:"z-variable-label",children:"Z-Axis Variable (3D)"}),e.jsxs(w,{labelId:"z-variable-label",value:L,label:"Z-Axis Variable (3D)",onChange:Le,disabled:!a.use3D||y.length===0,children:[e.jsx(b,{value:"",children:"None"}),y.map(t=>e.jsx(b,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"size-variable-label",children:"Point Size Variable"}),e.jsxs(w,{labelId:"size-variable-label",value:p,label:"Point Size Variable",onChange:Ie,children:[e.jsx(b,{value:"",children:"Default Size"}),y.map(t=>e.jsx(b,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"color-variable-label",children:"Color Category"}),e.jsxs(w,{labelId:"color-variable-label",value:W,label:"Color Category",onChange:Re,children:[e.jsx(b,{value:"",children:"Single Color"}),ke.map(t=>e.jsx(b,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"label-variable-label",children:"Point Labels"}),e.jsxs(w,{labelId:"label-variable-label",value:N,label:"Point Labels",onChange:Ee,children:[e.jsx(b,{value:"",children:"Index"}),we.map(t=>e.jsx(b,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(o,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(ve,{variant:"contained",onClick:xe,disabled:We,startIcon:S?e.jsx(je,{size:20}):e.jsx(ye,{}),fullWidth:!0,children:S?"Generating...":"Generate Chart"}),e.jsxs(o,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(_,{title:"Download Chart",children:e.jsx(Ce,{onClick:Me,disabled:Ne,children:e.jsx(Oe,{})})}),e.jsx(_,{title:"Reset Settings",children:e.jsx(Ce,{onClick:Ge,children:e.jsx(Xe,{})})})]})]})]})})}),e.jsx(fe,{in:C==="settings",timeout:300,children:e.jsx(o,{sx:{display:C==="settings"?"block":"none"},children:e.jsxs(Z,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:l.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(d,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Chart Settings"}),e.jsxs(o,{mb:3,children:[e.jsx(d,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Labels & Title"}),e.jsx(F,{fullWidth:!0,label:"Chart Title",value:a.title,onChange:t=>v("title",t.target.value),margin:"normal",variant:"outlined",size:"small"}),e.jsx(F,{fullWidth:!0,label:"X-Axis Label",value:a.xAxisLabel,onChange:t=>v("xAxisLabel",t.target.value),margin:"normal",variant:"outlined",size:"small"}),e.jsx(F,{fullWidth:!0,label:"Y-Axis Label",value:a.yAxisLabel,onChange:t=>v("yAxisLabel",t.target.value),margin:"normal",variant:"outlined",size:"small"}),a.use3D&&e.jsx(F,{fullWidth:!0,label:"Z-Axis Label",value:a.zAxisLabel,onChange:t=>v("zAxisLabel",t.target.value),margin:"normal",variant:"outlined",size:"small"})]}),e.jsxs(o,{mb:3,children:[e.jsx(d,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(D,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(k,{id:"color-scheme-label",children:"Color Scheme"}),e.jsx(w,{labelId:"color-scheme-label",value:a.colorScheme,label:"Color Scheme",onChange:t=>v("colorScheme",t.target.value),children:Object.keys(oe).map(t=>e.jsx(b,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})]}),e.jsxs(o,{mb:3,children:[e.jsx(d,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Size & Styling"}),e.jsxs(o,{sx:{mt:2},children:[e.jsx(d,{gutterBottom:!0,children:"Default Point Size"}),e.jsx(ze,{value:a.pointSize,min:2,max:20,step:1,onChange:(t,r)=>v("pointSize",r),valueLabelDisplay:"auto",disabled:!!p,size:"small"})]}),e.jsxs(o,{sx:{mt:2},children:[e.jsx(d,{gutterBottom:!0,children:"Jitter Amount (%)"}),e.jsx(ze,{value:a.jitter,min:0,max:20,step:1,onChange:(t,r)=>v("jitter",r),valueLabelDisplay:"auto",size:"small"})]})]}),e.jsxs(o,{mb:3,children:[e.jsx(d,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(He,{children:[e.jsx(se,{control:e.jsx(ne,{size:"small",checked:a.showGrid,onChange:t=>v("showGrid",t.target.checked)}),label:"Show Grid Lines"}),e.jsx(se,{control:e.jsx(ne,{size:"small",checked:a.labelPoints,onChange:t=>v("labelPoints",t.target.checked)}),label:"Label Points"}),e.jsx(se,{control:e.jsx(ne,{size:"small",checked:a.use3D,onChange:t=>v("use3D",t.target.checked)}),label:"Enable 3D Plot"})]})]}),e.jsx(ve,{variant:"outlined",color:"secondary",onClick:Be,disabled:!V||!P||S||!R,fullWidth:!0,sx:{mt:2},children:"Apply Customizations & Regenerate"})]})})})]}),e.jsx(re,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(Z,{elevation:2,sx:{p:2},children:[e.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(d,{variant:"h6",children:"Chart Preview"}),e.jsxs(o,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(d,{variant:"body2",color:"text.secondary",children:["Active: ",C==="variables"?"Variables":"Settings"]}),e.jsx(o,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:C==="variables"?l.palette.primary.main:l.palette.warning.main,boxShadow:`0 0 0 2px ${C==="variables"?l.palette.primary.main+"20":l.palette.warning.main+"20"}`}})]})]}),he&&e.jsx(o,{sx:{mb:2,p:2,backgroundColor:l.palette.error.light+"20",borderRadius:1,border:`1px solid ${l.palette.error.light}`},children:e.jsx(d,{color:"error",children:he})}),e.jsxs(o,{sx:{minHeight:500,height:500,width:"100%",border:`1px solid ${l.palette.divider}`,borderRadius:1,backgroundColor:l.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)",position:"relative",overflow:"hidden"},children:[S&&e.jsxs(o,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:10},children:[e.jsx(je,{}),e.jsx(d,{color:"text.secondary",children:"Generating scatter plot..."})]}),!S&&!z&&e.jsxs(o,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",textAlign:"center",zIndex:9},children:[e.jsx(ye,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(d,{color:"text.secondary",textAlign:"center",children:n?!V||!P?"Select X and Y variables to generate the scatter plot":a.use3D&&!L?"Select a Z variable for 3D plot":"Chart will appear here once generated":"Select a dataset to begin"}),n&&(!V||!P)&&e.jsx(d,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"})]}),e.jsx("div",{ref:A,id:de,style:{width:"100%",height:"100%",visibility:z&&!S?"visible":"hidden"}})]})]})})]})]})};export{it as default};
