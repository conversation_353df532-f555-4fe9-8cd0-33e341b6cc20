import{u as Ae,j as e,B as l,R as f,br as ze,X as xe,e as i,g as C,aF as R,G as c,ai as z,b9 as _,ba as G,bb as H,ah as Be,aj as he,bc as me,f as Oe,ae as De,bp as Ee,aE as Le,I as Re,bs as _e,ad as ue,ao as M,ap as $,aq as q,ar as j,as as a,at as K,h as I,a2 as Ge,aP as ge,Z as Q,aX as He,bq as Me,bt as $e,W as qe}from"./mui-libs-CfwFIaTD.js";import{r as m}from"./react-libs-Cr2nE3UY.js";import{a as Ke,D as P,g as Pe}from"./index-Bpan7Tbe.js";import{S as V}from"./StatsCard-op8tGQ0a.js";import{f as Je,b as Ue,c as Xe}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import{p as Ye,o as Ze}from"./anova-DbTY6dHK.js";import"./math-setup-BTRs7Kau.js";import{k as Qe}from"./non-parametric-Cf6Ds91x.js";import{i as et}from"./normality-CwHD6Rjl.js";import{R as tt,B as st,C as at,X as nt,Y as it,T as rt,L as lt,a as ot,E as pe,c as ct}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./math-lib-BOZ-XUok.js";const Nt=()=>{var oe;const{datasets:J,currentDataset:B,setCurrentDataset:fe}=Ke(),v=Ae(),[ee,je]=m.useState((B==null?void 0:B.id)||""),d=J.find(t=>t.id===ee)||B,[O,te]=m.useState(""),[b,se]=m.useState(""),[U,ae]=m.useState(!1),[ne,ye]=m.useState(.95),[W,X]=m.useState([]),[D,ve]=m.useState(!0),[Y,ie]=m.useState(!1),[re,E]=m.useState(null),[s,S]=m.useState(null),[r,F]=m.useState(null),[le,Z]=m.useState([]);m.useEffect(()=>{const t=localStorage.getItem("oneway_anova_results"),n=localStorage.getItem("oneway_anova_assumptions"),y=localStorage.getItem("oneway_anova_group_data");if(t)try{S(JSON.parse(t))}catch(o){console.error("Error parsing saved ANOVA results:",o)}if(n)try{F(JSON.parse(n))}catch(o){console.error("Error parsing saved assumption results:",o)}if(y)try{Z(JSON.parse(y))}catch(o){console.error("Error parsing saved group data:",o)}},[]);const be=(d==null?void 0:d.columns.filter(t=>t.type===P.NUMERIC))||[],Se=(d==null?void 0:d.columns.filter(t=>t.type===P.CATEGORICAL||t.type===P.ORDINAL||t.type===P.BOOLEAN))||[],L=!d||!b?[]:d.columns.find(n=>n.id===b)?Pe(b,d):[],Te=t=>{const n=t.target.value;je(n);const y=J.find(o=>o.id===n);y&&fe(y),Fe()},Fe=()=>{te(""),se(""),X([]),E(null),localStorage.removeItem("oneway_anova_results"),localStorage.removeItem("oneway_anova_assumptions"),localStorage.removeItem("oneway_anova_group_data"),S(null),F(null),Z([])},Ne=t=>{te(t.target.value),S(null),F(null)},we=t=>{se(t.target.value),X([]),S(null),F(null)},Ce=t=>{const n=t.target.value;X(typeof n=="string"?n.split(","):n),S(null),F(null)},Ie=t=>{const n=parseFloat(t.target.value);!isNaN(n)&&n>0&&n<1&&ye(n)},Ve=()=>!d||!O||!b?!1:W.length>=3||L.length>=3&&W.length===0,k=t=>t<.01?"Very Small":t<.06?"Small":t<.14?"Medium":"Large",We=()=>{if(!d||!O||!b){E("Please select variable and grouping factor.");return}ie(!0),E(null),S(null),F(null);try{const t=d.columns.find(x=>x.id===O),n=d.columns.find(x=>x.id===b);if(!t||!n)throw new Error("Selected variables not found.");const y=W.length>0?W:L;if(y.length<3)throw new Error("ANOVA requires at least 3 groups. For 2 groups, use t-test instead.");const o=[],N=[],T=[];if(y.forEach(x=>{const u=d.data.filter(h=>String(h[n.name])===x).map(h=>h[t.name]),g=Je(u);if(g.length>0){o.push(g),N.push(x);const h=Ue(g),A=Xe(g),p=g.length,w=A/Math.sqrt(p),de=[h-1.96*w,h+1.96*w];T.push({name:x,n:p,mean:h,sd:A,se:w,ci95Low:de[0],ci95High:de[1],data:g})}}),Z(T),o.length<3)throw new Error("At least 3 groups with valid data are required for ANOVA.");if(D){const x=o.map((p,w)=>({group:N[w],isNormal:et(p),n:p.length})),u=x.some(p=>!p.isNormal.isNormal);let g=null,h=!1;try{o.every(p=>p.length>1)?(g=Ye(...o),h=g.rejected):(g={error:"Not all groups have sufficient data (>1 obs) for Levene's test."},h=!0)}catch(p){g={error:p instanceof Error?p.message:"Error during Levene's test."},h=!0}const A={normalityResults:x,violatesNormality:u,leveneTest:g,homogeneityViolated:h};F(A),localStorage.setItem("oneway_anova_assumptions",JSON.stringify(A)),(u||h)&&!U&&ae(!0)}if(U||D&&(r==null?void 0:r.violatesNormality)||D&&(r==null?void 0:r.homogeneityViolated)){const u={...Qe(o),testType:"kruskal-wallis",groupNames:N,variableName:t.name,factorName:n.name,groups:T};S(u),localStorage.setItem("oneway_anova_results",JSON.stringify(u)),localStorage.setItem("oneway_anova_group_data",JSON.stringify(T))}else{const u={...Ze(o),testType:"anova",groupNames:N,variableName:t.name,factorName:n.name,groups:T};S(u),localStorage.setItem("oneway_anova_results",JSON.stringify(u)),localStorage.setItem("oneway_anova_group_data",JSON.stringify(T))}}catch(t){const n=t instanceof Error?t.message:"An error occurred while running the test.";E(n)}finally{ie(!1)}},ke=le.map(t=>({name:t.name,mean:t.mean,errorHigh:t.ci95High-t.mean,errorLow:t.mean-t.ci95Low}));return le.map(t=>{const n=[...t.data].sort((x,u)=>x-u),y=n[Math.floor(n.length*.25)],o=n[Math.floor(n.length*.5)],N=n[Math.floor(n.length*.75)],T=n[0],ce=n[n.length-1];return{name:t.name,min:T,q1:y,median:o,q3:N,max:ce}}),e.jsxs(l,{children:[e.jsxs(f,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:`1px solid ${v.palette.divider}`},children:[e.jsxs(ze,{direction:"row",alignItems:"center",spacing:2,mb:3,children:[e.jsx(xe,{sx:{color:v.palette.primary.main,fontSize:28}}),e.jsx(i,{variant:"h6",fontWeight:"bold",children:"One-Way ANOVA Configuration"})]}),e.jsx(C,{severity:"info",icon:e.jsx(R,{}),sx:{mb:3},children:e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"One-Way ANOVA:"})," This component performs a complete one-way analysis of variance to compare means across three or more independent groups, calculating F-statistics, p-values, and effect sizes."]})}),e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Test Options"}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsxs(z,{fullWidth:!0,sx:{mb:2},children:[e.jsx(_,{id:"dataset-select-label",children:"Dataset"}),e.jsx(G,{labelId:"dataset-select-label",value:ee,label:"Dataset",onChange:Te,children:J.map(t=>e.jsx(H,{value:t.id,children:t.name},t.id))})]}),e.jsxs(z,{fullWidth:!0,sx:{mb:2},children:[e.jsx(_,{id:"variable-select-label",children:"Dependent Variable (Numeric)"}),e.jsx(G,{labelId:"variable-select-label",value:O,label:"Dependent Variable (Numeric)",onChange:Ne,children:be.map(t=>e.jsx(H,{value:t.id,children:t.name},t.id))})]}),e.jsxs(z,{fullWidth:!0,sx:{mb:2},children:[e.jsx(_,{id:"factor-select-label",children:"Grouping Factor (Categorical)"}),e.jsx(G,{labelId:"factor-select-label",value:b,label:"Grouping Factor (Categorical)",onChange:we,children:Se.map(t=>e.jsx(H,{value:t.id,children:t.name},t.id))})]})]}),e.jsxs(c,{item:!0,xs:12,md:6,children:[b&&L.length>0&&e.jsxs(z,{fullWidth:!0,sx:{mb:2},children:[e.jsx(_,{id:"groups-select-label",children:"Groups to Include (min 3)"}),e.jsx(G,{labelId:"groups-select-label",multiple:!0,value:W,label:"Groups to Include (min 3)",onChange:Ce,renderValue:t=>e.jsx(l,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:t.map(n=>e.jsxs(i,{variant:"body2",component:"span",children:[n,t.indexOf(n)<t.length-1?", ":""]},n))}),children:L.map(t=>e.jsx(H,{value:t,children:t},t))})]}),e.jsx(z,{fullWidth:!0,sx:{mb:2},children:e.jsx(Be,{label:"Confidence Level",type:"number",value:ne,onChange:Ie,inputProps:{step:.01,min:.8,max:.99},helperText:`${ne*100}% confidence interval`})}),e.jsx(he,{control:e.jsx(me,{checked:D,onChange:t=>ve(t.target.checked)}),label:"Check test assumptions"}),e.jsx(he,{control:e.jsx(me,{checked:U,onChange:t=>ae(t.target.checked)}),label:"Use non-parametric test (Kruskal-Wallis)"})]})]}),e.jsx(l,{sx:{mt:2,display:"flex",justifyContent:"flex-end"},children:e.jsx(Oe,{variant:"contained",onClick:We,disabled:!Ve()||Y,startIcon:Y?e.jsx(De,{size:20}):null,children:Y?"Running...":"Run Test"})})]}),re&&e.jsx(C,{severity:"error",sx:{mb:3},children:re}),r&&e.jsxs(f,{elevation:2,sx:{p:3,mb:3,bgcolor:"background.paper"},children:[e.jsxs(l,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[e.jsxs(i,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ee,{color:"primary"}),"Assumption Checks"]}),e.jsx(Le,{title:"ANOVA assumes normality of data within each group and homogeneity of variances across groups.",children:e.jsx(Re,{size:"small",children:e.jsx(_e,{fontSize:"small"})})})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(ue,{fontSize:"small"}),"Normality Check (Shapiro-Wilk Test)"]}),e.jsxs(f,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(M,{children:e.jsxs($,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(q,{children:e.jsxs(j,{children:[e.jsx(a,{children:"Group"}),e.jsx(a,{align:"right",children:"N"}),e.jsx(a,{align:"right",children:"p-value"}),e.jsx(a,{align:"right",children:"Status"})]})}),e.jsx(K,{children:r.normalityResults.map((t,n)=>e.jsxs(j,{sx:{"&:nth-of-type(odd)":{bgcolor:"grey.25"}},children:[e.jsx(a,{sx:{fontWeight:500},children:t.group}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:t.n}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:t.isNormal.pValue.toFixed(4)}),e.jsx(a,{align:"right",children:e.jsx(I,{label:t.isNormal.isNormal?"Normal":"Non-normal",color:t.isNormal.isNormal?"success":"error",size:"small",variant:"outlined"})})]},t.group))})]})}),e.jsx(C,{severity:r.violatesNormality?"warning":"success",sx:{mt:2},children:e.jsx(i,{variant:"body2",children:r.violatesNormality?"Normality assumption is violated for one or more groups. Consider using Kruskal-Wallis test.":"Normality assumption is met for all groups."})})]})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Ge,{fontSize:"small"}),"Homogeneity of Variances (Levene's Test)"]}),e.jsx(f,{elevation:0,variant:"outlined",sx:{p:2},children:r.leveneTest&&!r.leveneTest.error?e.jsxs(e.Fragment,{children:[e.jsx(M,{children:e.jsxs($,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(q,{children:e.jsxs(j,{children:[e.jsx(a,{children:"Test"}),e.jsx(a,{align:"right",children:"F-Statistic"}),e.jsx(a,{align:"right",children:"df1"}),e.jsx(a,{align:"right",children:"df2"}),e.jsx(a,{align:"right",children:"p-value"}),e.jsx(a,{align:"right",children:"Status"})]})}),e.jsx(K,{children:e.jsxs(j,{children:[e.jsx(a,{sx:{fontWeight:500},children:"Levene's Test"}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:r.leveneTest.statistic.toFixed(3)}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:r.leveneTest.df[0]}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:r.leveneTest.df[1]}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:r.leveneTest.pValue.toFixed(3)}),e.jsx(a,{align:"right",children:e.jsx(I,{label:r.leveneTest.rejected?"Unequal Variances":"Equal Variances",color:r.leveneTest.rejected?"error":"success",size:"small",variant:"outlined"})})]})})]})}),e.jsx(C,{severity:r.leveneTest.rejected?"warning":"success",sx:{mt:2},children:e.jsx(i,{variant:"body2",children:r.leveneTest.rejected?"Homogeneity of variances assumption is violated. Consider using Welch's ANOVA or Kruskal-Wallis test.":"Homogeneity of variances assumption is met."})})]}):e.jsx(C,{severity:"warning",children:e.jsx(i,{variant:"body2",children:((oe=r.leveneTest)==null?void 0:oe.error)||"Homogeneity of variances assumption may be violated."})})})]}),(r.violatesNormality||r.homogeneityViolated)&&e.jsxs(f,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"warning.50"},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(R,{fontSize:"small"}),"Overall Assessment"]}),e.jsx(C,{severity:"info",children:e.jsx(i,{variant:"body2",children:"Due to assumption violations, the Kruskal-Wallis test is recommended or was automatically selected as a non-parametric alternative."})})]})]}),s&&e.jsxs(f,{elevation:2,sx:{p:3,mb:3,bgcolor:"background.paper"},children:[e.jsx(l,{display:"flex",justifyContent:"flex-start",alignItems:"center",mb:3,children:e.jsxs(i,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(xe,{color:"primary"}),s.testType==="anova"?"One-Way ANOVA Results":"Kruskal-Wallis Test Results"]})}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(ge,{fontSize:"small"}),"Key Statistics"]}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(V,{title:s.testType==="anova"?"F-statistic":"H-statistic",value:s.testType==="anova"?s.F.toFixed(4):s.H.toFixed(4),description:"Test statistic value",color:"primary",variant:"outlined",icon:e.jsx(ge,{})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(V,{title:"p-value",value:s.pValue<.001?"< 0.001":s.pValue.toFixed(4),description:s.pValue<.05?"Statistically significant":"Not significant",color:s.pValue<.05?"success":"warning",variant:"outlined",icon:e.jsx(ue,{})})}),s.testType==="anova"&&e.jsxs(e.Fragment,{children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(V,{title:"Degrees of Freedom",value:`${s.dfBetween}, ${s.dfWithin}`,description:"Between, Within groups",color:"info",variant:"outlined",icon:e.jsx(Q,{})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(V,{title:"Effect Size (η²)",value:s.etaSquared.toFixed(4),description:k(s.etaSquared),color:"secondary",variant:"outlined",icon:e.jsx(He,{})})})]}),s.testType==="kruskal-wallis"&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(V,{title:"Degrees of Freedom",value:s.df.toString(),description:"Test degrees of freedom",color:"info",variant:"outlined",icon:e.jsx(Q,{})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(V,{title:"Number of Groups",value:s.groupNames.length.toString(),description:"Groups compared",color:"info",variant:"outlined",icon:e.jsx(Me,{})})})]})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(R,{fontSize:"small"}),"Test Information"]}),e.jsx(f,{elevation:0,variant:"outlined",sx:{p:2},children:e.jsxs(c,{container:!0,spacing:2,children:[e.jsxs(c,{item:!0,xs:12,sm:6,children:[e.jsx(i,{variant:"body2",color:"text.secondary",children:"Dependent Variable"}),e.jsx(i,{variant:"body1",fontWeight:500,children:s.variableName})]}),e.jsxs(c,{item:!0,xs:12,sm:6,children:[e.jsx(i,{variant:"body2",color:"text.secondary",children:"Grouping Factor"}),e.jsx(i,{variant:"body1",fontWeight:500,children:s.factorName})]}),e.jsxs(c,{item:!0,xs:12,sm:6,children:[e.jsx(i,{variant:"body2",color:"text.secondary",children:"Test Type"}),e.jsx(i,{variant:"body1",fontWeight:500,children:s.testType==="anova"?"One-Way ANOVA":"Kruskal-Wallis Test"})]}),e.jsxs(c,{item:!0,xs:12,sm:6,children:[e.jsx(i,{variant:"body2",color:"text.secondary",children:"Groups"}),e.jsx(i,{variant:"body1",fontWeight:500,children:s.groupNames.join(", ")})]})]})})]}),s.testType==="anova"&&e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Q,{fontSize:"small"}),"ANOVA Summary"]}),e.jsx(f,{elevation:0,variant:"outlined",sx:{p:2},children:e.jsx(M,{children:e.jsxs($,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(q,{children:e.jsxs(j,{children:[e.jsx(a,{children:"Statistic"}),e.jsx(a,{align:"right",children:"Value"}),e.jsx(a,{align:"right",children:"Interpretation"})]})}),e.jsxs(K,{children:[e.jsxs(j,{children:[e.jsx(a,{sx:{fontWeight:500},children:"F-statistic"}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:s.F.toFixed(4)}),e.jsx(a,{align:"right",children:e.jsx(I,{label:s.pValue<.05?"Significant":"Not Significant",color:s.pValue<.05?"success":"default",size:"small",variant:"outlined"})})]}),e.jsxs(j,{children:[e.jsx(a,{sx:{fontWeight:500},children:"Degrees of Freedom"}),e.jsxs(a,{align:"right",sx:{fontFamily:"monospace"},children:[s.dfBetween,", ",s.dfWithin]}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace",fontSize:"0.875rem"},children:"Between, Within"})]}),e.jsxs(j,{children:[e.jsx(a,{sx:{fontWeight:500},children:"p-value"}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace",color:s.pValue<.05?"success.main":"text.primary"},children:s.pValue<.001?"< 0.001":s.pValue.toFixed(4)}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace",fontSize:"0.875rem"},children:"α = 0.05"})]}),e.jsxs(j,{children:[e.jsx(a,{sx:{fontWeight:500},children:"Effect Size (η²)"}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:s.etaSquared.toFixed(4)}),e.jsx(a,{align:"right",children:e.jsx(I,{label:k(s.etaSquared),color:"secondary",size:"small",variant:"outlined"})})]})]})]})})})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx($e,{fontSize:"small"}),"Group Descriptive Statistics"]}),e.jsx(f,{elevation:0,variant:"outlined",sx:{p:2},children:e.jsx(M,{children:e.jsxs($,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(q,{children:e.jsxs(j,{children:[e.jsx(a,{children:"Group"}),e.jsx(a,{align:"right",children:"N"}),e.jsx(a,{align:"right",children:"Mean"}),e.jsx(a,{align:"right",children:"SD"}),e.jsx(a,{align:"right",children:"SE"}),e.jsx(a,{align:"right",children:"95% CI"})]})}),e.jsx(K,{children:s.groups.map((t,n)=>e.jsxs(j,{sx:{"&:nth-of-type(odd)":{bgcolor:"grey.25"}},children:[e.jsx(a,{sx:{fontWeight:500},children:t.name}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:t.n}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:t.mean.toFixed(3)}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:t.sd.toFixed(3)}),e.jsx(a,{align:"right",sx:{fontFamily:"monospace"},children:t.se.toFixed(3)}),e.jsxs(a,{align:"right",sx:{fontFamily:"monospace"},children:["[",t.ci95Low.toFixed(3),", ",t.ci95High.toFixed(3),"]"]})]},t.name))})]})})})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(R,{fontSize:"small"}),"Statistical Interpretation"]}),e.jsxs(f,{elevation:0,variant:"outlined",sx:{p:3,bgcolor:"grey.50"},children:[e.jsxs(l,{sx:{mb:2},children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Summary"}),e.jsx(i,{variant:"body2",sx:{lineHeight:1.6},children:s.pValue<.05?e.jsxs(e.Fragment,{children:["The ",s.testType==="anova"?"one-way ANOVA":"Kruskal-Wallis test"," revealed a statistically significant difference between groups (",s.testType==="anova"?`F(${s.dfBetween}, ${s.dfWithin}) = ${s.F.toFixed(2)}`:`H(${s.df}) = ${s.H.toFixed(2)}`,", p ",s.pValue<.001?"< 0.001":`= ${s.pValue.toFixed(3)}`,").",s.testType==="anova"&&` The effect size (η² = ${s.etaSquared.toFixed(3)}) indicates a ${k(s.etaSquared).toLowerCase()} effect.`]}):e.jsxs(e.Fragment,{children:["The ",s.testType==="anova"?"one-way ANOVA":"Kruskal-Wallis test"," did not reveal a statistically significant difference between groups (",s.testType==="anova"?`F(${s.dfBetween}, ${s.dfWithin}) = ${s.F.toFixed(2)}`:`H(${s.df}) = ${s.H.toFixed(2)}`,", p = ",s.pValue.toFixed(3),")."]})})]}),e.jsxs(l,{sx:{mb:2},children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Statistical Significance"}),e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[e.jsx(I,{label:s.pValue<.05?"Significant":"Not Significant",color:s.pValue<.05?"success":"default",size:"small"}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"(α = 0.05)"})]}),e.jsx(i,{variant:"body2",sx:{lineHeight:1.6},children:s.pValue<.05?`The p-value (${s.pValue<.001?"< 0.001":s.pValue.toFixed(4)}) is less than the significance level of 0.05, indicating a statistically significant result.`:`The p-value (${s.pValue.toFixed(4)}) is greater than the significance level of 0.05, indicating no statistically significant difference between groups.`})]}),s.testType==="anova"&&e.jsxs(l,{sx:{mb:2},children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Effect Size"}),e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[e.jsx(I,{label:k(s.etaSquared),color:"secondary",size:"small"}),e.jsxs(i,{variant:"body2",color:"text.secondary",children:["(η² = ",s.etaSquared.toFixed(4),")"]})]}),e.jsxs(i,{variant:"body2",sx:{lineHeight:1.6},children:["The effect size indicates a ",k(s.etaSquared).toLowerCase()," practical significance of the observed differences between groups."]})]}),e.jsxs(l,{children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Next Steps"}),e.jsx(i,{variant:"body2",sx:{lineHeight:1.6},children:s.pValue<.05?"Post-hoc tests (e.g., Tukey HSD, Bonferroni) would be needed to determine which specific groups differ from each other.":"No further analysis is needed as there are no significant differences between groups to explore."})]})]})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(qe,{fontSize:"small"}),"Visualizations"]}),e.jsxs(f,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,children:"Group Means Comparison"}),e.jsx(i,{variant:"caption",color:"text.secondary",sx:{mb:2,display:"block"},children:"Error bars represent 95% confidence intervals"}),e.jsx(l,{sx:{height:400,mt:2},children:e.jsx(tt,{width:"100%",height:"100%",children:e.jsxs(st,{data:ke,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(at,{strokeDasharray:"3 3",stroke:v.palette.grey[300]}),e.jsx(nt,{dataKey:"name",tick:{fontSize:12},angle:-45,textAnchor:"end",height:80}),e.jsx(it,{tick:{fontSize:12}}),e.jsx(rt,{contentStyle:{backgroundColor:v.palette.background.paper,border:`1px solid ${v.palette.grey[300]}`,borderRadius:4}}),e.jsx(lt,{}),e.jsxs(ot,{dataKey:"mean",fill:v.palette.primary.main,name:"Mean",radius:[4,4,0,0],children:[e.jsx(pe,{dataKey:"errorHigh",direction:"y",stroke:v.palette.error.main,strokeWidth:2,width:4}),e.jsx(pe,{dataKey:"errorLow",direction:"y",stroke:v.palette.error.main,strokeWidth:2,width:4})]}),s.testType==="anova"&&s.grandMean&&e.jsx(ct,{y:s.grandMean,stroke:v.palette.secondary.main,strokeDasharray:"5 5",label:{value:"Grand Mean",position:"topRight"}})]})})})]})]})]})]})};export{Nt as default};
