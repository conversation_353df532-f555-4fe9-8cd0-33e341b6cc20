import{u as M,a as E,j as e,B as t,i as d,e as i,K as G,M as P,N as q,V as H,O as S,G as r,D as u,Q,J as O,R as g,U,W as f,X as z,Y as K,Z as b,$ as X,a0 as D,L as v,a1 as o,r as l,a2 as Y,a3 as J,F as Z,a4 as _,a5 as ee,d as C}from"./mui-libs-CfwFIaTD.js";import{r as W}from"./react-libs-Cr2nE3UY.js";import{a as te,H as ie}from"./index-Bpan7Tbe.js";import{C as x,B as m,S as k,a as se}from"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{S as A}from"./StatsCard-op8tGQ0a.js";import"./VariableSelector-CPdlCsJ2.js";import"./other-utils-CR9xr_gI.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";const je=({onNavigate:T})=>{const{datasets:h,setCurrentDataset:R,currentDataset:p}=te(),a=M(),y=E(a.breakpoints.down("sm"));E(a.breakpoints.between("sm","md"));const[I,L]=W.useState(!0),[V,$]=W.useState(!!p||h.length>0);W.useEffect(()=>{$(!!p||h.length>0)},[h,p]);const w=(n,c)=>{R(n),T(c)},s=(n,c=!1)=>{c&&!p&&h.length>0&&R(h[0]),T(n.startsWith("#")?n.substring(1):n)},B=()=>{L(!I)},F=()=>{const n=[{title:"Import & Clean Data",description:"Upload your data and prepare it for analysis by cleaning, transforming, and validating.",icon:e.jsx(S,{}),path:"/data-management/import",time:"5-10 min",color:a.palette.primary.main},{title:"Exploratory Analysis",description:"Explore your data with descriptive statistics and initial visualizations.",icon:e.jsx(b,{}),path:"/stats/descriptives",time:"10-15 min",color:a.palette.secondary.main},{title:"Statistical Tests",description:"Run appropriate statistical tests based on your research questions.",icon:e.jsx(z,{}),path:"/inference/ttest",time:"15-20 min",color:a.palette.warning.main},{title:"Visualize Results",description:"Create compelling visualizations to communicate your findings.",icon:e.jsx(f,{}),path:"/charts/bar",time:"10-15 min",color:a.palette.info.main}];return e.jsx(t,{sx:{flexShrink:0},children:e.jsx(J,{in:I,children:e.jsxs(g,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:`1px solid ${a.palette.divider}`,bgcolor:a.palette.background.paper},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(H,{color:"primary",sx:{mr:1.5}}),e.jsx(i,{variant:"h6",fontWeight:"medium",children:"Recommended Analysis Workflow"})]}),e.jsx(u,{sx:{mb:3}}),e.jsx(r,{container:!0,spacing:2,children:n.map((c,j)=>e.jsx(r,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(t,{sx:{display:"flex",flexDirection:"column",height:"100%",position:"relative",pt:2},children:[e.jsx(t,{sx:{position:"absolute",top:0,left:"50%",transform:"translateX(-50%)",width:36,height:36,borderRadius:"50%",bgcolor:d(c.color,.1),display:"flex",alignItems:"center",justifyContent:"center",color:c.color,border:`2px solid ${c.color}`,zIndex:2},children:e.jsx(i,{variant:"body1",fontWeight:"bold",children:j+1})}),j<n.length-1&&e.jsx(t,{sx:{position:"absolute",top:18,left:"50%",width:"100%",height:2,bgcolor:d(c.color,.2),zIndex:1}}),e.jsxs(x,{sx:{p:2,mt:2,display:"flex",flexDirection:"column",height:"100%",borderTop:`3px solid ${c.color}`},hoverEffect:!0,children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1.5},children:[e.jsx(t,{sx:{color:c.color,mr:1.5},children:c.icon}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:c.title})]}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2,flex:1},children:c.description}),e.jsx(t,{sx:{display:"flex",alignItems:"center",justifyContent:"flex-end",mt:"auto"},children:e.jsx(m,{size:"small",endIcon:e.jsx(P,{}),onClick:()=>s(c.path,j>0),disabled:j>0&&!p,children:"Start"})})]})]})},j))})]})})})},N=()=>h.length===0?null:e.jsx(Z,{in:V,children:e.jsxs(t,{sx:{mb:4},children:[e.jsx(k,{title:"Quick Start Analysis",icon:e.jsx(ee,{}),action:e.jsx(m,{variant:"outlined",startIcon:e.jsx(_,{}),size:"small",color:"primary",onClick:()=>s("/stats/descriptives",!0),children:"Start Analysis"})}),e.jsxs(r,{container:!0,spacing:2,children:[e.jsx(r,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x,{hoverEffect:!0,borderHighlight:!0,onClick:()=>s("/stats/descriptives",!0),sx:{display:"block",textDecoration:"none",cursor:"pointer"},children:e.jsxs(t,{sx:{p:2.5},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(C,{sx:{bgcolor:d(a.palette.primary.main,.1),mr:1.5},children:e.jsx(b,{color:"primary"})}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Descriptive Statistics"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Summarize your data with means, medians, and more"})]})})}),e.jsx(r,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x,{hoverEffect:!0,borderHighlight:!0,onClick:()=>s("/inference/ttest",!0),sx:{display:"block",textDecoration:"none",cursor:"pointer"},children:e.jsxs(t,{sx:{p:2.5},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(C,{sx:{bgcolor:d(a.palette.secondary.main,.1),mr:1.5},children:e.jsx(z,{color:"secondary"})}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Statistical Tests"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Compare groups with t-tests, ANOVA, and non-parametric tests"})]})})}),e.jsx(r,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x,{hoverEffect:!0,borderHighlight:!0,onClick:()=>s("/correlation/pearson",!0),sx:{display:"block",textDecoration:"none",cursor:"pointer"},children:e.jsxs(t,{sx:{p:2.5},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(C,{sx:{bgcolor:d(a.palette.success.main,.1),mr:1.5},children:e.jsx(D,{color:"success"})}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Correlation Analysis"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Measure relationships between variables"})]})})}),e.jsx(r,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x,{hoverEffect:!0,borderHighlight:!0,onClick:()=>s("/charts/bar",!0),sx:{display:"block",textDecoration:"none",cursor:"pointer"},children:e.jsxs(t,{sx:{p:2.5},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(C,{sx:{bgcolor:d(a.palette.info.main,.1),mr:1.5},children:e.jsx(f,{color:"info"})}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Data Visualization"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Create beautiful charts and graphs"})]})})})]})]})});return e.jsxs(e.Fragment,{children:[e.jsxs(ie,{children:[e.jsx("title",{children:"DataStatPro - Advanced Statistical Analysis"}),e.jsx("meta",{name:"description",content:"Welcome to DataStatPro, your advanced web application for statistical analysis, data visualization, and insightful interpretations. Get started with your data journey."})]}),e.jsxs(t,{sx:{width:"100%",overflow:"auto",display:"flex",flexDirection:"column",px:{xs:1,md:2},py:2},children:[e.jsxs(x,{hoverEffect:!1,gradient:!0,sx:{p:{xs:3,sm:4},mb:3,backgroundImage:`linear-gradient(135deg, ${d(a.palette.primary.light,.1)} 0%, ${d(a.palette.primary.main,.05)} 100%)`,position:"relative",minHeight:"auto",flexShrink:0},children:[e.jsx(i,{variant:y?"h5":"h4",gutterBottom:!0,color:"primary",sx:{fontWeight:"bold",position:"relative",zIndex:1},children:"Welcome to DataStatPro"}),e.jsx(i,{variant:y?"subtitle1":"h6",color:"text.secondary",paragraph:!0,sx:{mb:2,position:"relative",zIndex:1},children:"Advanced Statistical Analysis Web Application"}),e.jsx(i,{variant:"body1",paragraph:!0,sx:{position:"relative",zIndex:1,maxWidth:800},children:"DataStatPro provides powerful tools for data analysis, visualization, and statistical inference. Use the intuitive interface to explore your data, run statistical tests, and generate publication-quality visualizations."}),e.jsxs(i,{variant:"body1",color:"text.primary",paragraph:!0,sx:{mt:1,mb:2,position:"relative",zIndex:1,maxWidth:800},children:["Explore powerful tools for guided analysis and insightful interpretations. For any bugs or feature requests, please email us at"," ",e.jsx(G,{href:"mailto:<EMAIL>",color:"primary",underline:"hover",children:"<EMAIL>"})," or ",e.jsx(G,{href:"mailto:<EMAIL>",color:"primary",underline:"hover",children:"<EMAIL>"}),". We're here to help enhance your data analysis journey!"]}),e.jsxs(t,{sx:{display:"flex",flexWrap:"wrap",gap:2,mt:4,position:"relative",zIndex:1},children:[e.jsx(m,{gradient:!0,rounded:!0,startIcon:e.jsx(q,{}),endIcon:e.jsx(P,{}),onClick:()=>s("/data-management/import"),size:y?"medium":"large",children:"Get Started"}),e.jsx(m,{variant:"outlined",rounded:!0,startIcon:e.jsx(H,{}),onClick:B,size:y?"medium":"large",sx:{borderWidth:2},children:I?"Hide Workflow Guide":"Show Workflow Guide"})]})]}),F(),N(),h.length>0&&e.jsxs(t,{sx:{mb:4},children:[e.jsx(k,{title:"Your Datasets",icon:e.jsx(S,{}),action:e.jsx(m,{variant:"outlined",size:"small",onClick:()=>s("/data-management/import"),children:"Import New Data"})}),e.jsxs(r,{container:!0,spacing:2,children:[h.slice(0,3).map(n=>e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsx(x,{hoverEffect:!0,borderHighlight:!0,children:e.jsxs(t,{sx:{p:2},children:[e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(i,{variant:"h6",noWrap:!0,sx:{maxWidth:"70%"},children:n.name}),e.jsx(se,{label:`${n.columns.length} cols`,color:"primary",variant:"light",size:"small"})]}),e.jsxs(i,{variant:"body2",color:"text.secondary",children:[n.data.length," rows, ",n.columns.length," columns"]}),e.jsxs(i,{variant:"caption",display:"block",color:"text.secondary",children:["Created: ",new Date(n.dateCreated).toLocaleDateString()]}),e.jsx(u,{sx:{my:1.5}}),e.jsxs(t,{sx:{display:"flex",gap:1},children:[e.jsx(m,{size:"small",onClick:()=>w(n,"/data-management/editor"),variant:"outlined",children:"Edit"}),e.jsx(m,{size:"small",onClick:()=>w(n,"/stats/descriptives"),variant:"outlined",color:"secondary",children:"Analyze"}),e.jsx(m,{size:"small",onClick:()=>w(n,"/charts/bar"),children:"Visualize"})]})]})})},n.id)),e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(x,{hoverEffect:!0,onClick:()=>s("/data-management/import"),sx:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",p:3,textDecoration:"none",border:`2px dashed ${a.palette.divider}`,bgcolor:"transparent",cursor:"pointer"},children:[e.jsx(Q,{sx:{fontSize:40,color:a.palette.primary.main,mb:1}}),e.jsx(i,{variant:"subtitle1",color:"primary",align:"center",children:"Import New Dataset"}),e.jsx(i,{variant:"body2",color:"text.secondary",align:"center",sx:{mt:1},children:"Add your own data or try sample datasets"})]})})]})]}),e.jsx(x,{sx:{mb:4},children:e.jsxs(t,{sx:{p:2},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(O,{color:"primary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Statistical Learning Resources"})]}),e.jsx(u,{sx:{mb:2}}),e.jsxs(r,{container:!0,spacing:2,children:[e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(g,{elevation:0,sx:{p:2,bgcolor:d(a.palette.info.main,.05),border:`1px solid ${d(a.palette.info.main,.1)}`,borderRadius:1,height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(U,{color:"info",sx:{mr:1}}),e.jsx(i,{variant:"subtitle2",children:"Which Test Should I Use?"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2,flex:1},children:"Guide to selecting the appropriate statistical test based on your research question and data type."}),e.jsx(m,{size:"small",variant:"outlined",color:"info",onClick:()=>s("/whichtest"),children:"Learn More"})]})}),e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(g,{elevation:0,sx:{p:2,bgcolor:d(a.palette.warning.main,.05),border:`1px solid ${d(a.palette.warning.main,.1)}`,borderRadius:1,height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(f,{color:"warning",sx:{mr:1}}),e.jsx(i,{variant:"subtitle2",children:"Visualization Guide"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2,flex:1},children:"Tips for choosing the right chart type to effectively communicate your data insights."}),e.jsx(m,{size:"small",variant:"outlined",color:"warning",onClick:()=>s("/visualizationguide"),children:"View Guide"})]})}),e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(g,{elevation:0,sx:{p:2,bgcolor:d(a.palette.success.main,.05),border:`1px solid ${d(a.palette.success.main,.1)}`,borderRadius:1,height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(z,{color:"success",sx:{mr:1}}),e.jsx(i,{variant:"subtitle2",children:"Statistical Methods"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2,flex:1},children:"Reference guide with common statistical methods and their interpretations."}),e.jsx(m,{size:"small",variant:"outlined",color:"success",onClick:()=>s("/statisticalmethods"),children:"View Reference"})]})})]})]})}),e.jsx(k,{title:"Key Features",icon:e.jsx(K,{}),divider:!0}),e.jsxs(r,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsx(t,{onClick:()=>s("/data-management/import"),sx:{cursor:"pointer",height:"100%"},children:e.jsx(A,{title:"Data Management",value:"20+ Tools",description:"Import, clean, and transform your data.",icon:e.jsx(S,{fontSize:"inherit"}),color:"primary",variant:"default"})})}),e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsx(t,{onClick:()=>s("/stats/descriptives",!0),sx:{cursor:"pointer",height:"100%"},children:e.jsx(A,{title:"Statistical Analysis",value:"30+ Analysis Tools",description:"Comprehensive suite of statistical tests.",icon:e.jsx(b,{fontSize:"inherit"}),color:"secondary",variant:"default"})})}),e.jsx(r,{item:!0,xs:12,sm:6,md:4,children:e.jsx(t,{onClick:()=>s("/charts/bar",!0),sx:{cursor:"pointer",height:"100%"},children:e.jsx(A,{title:"Data Visualization",value:"15+ Charts",description:"Create publication-quality charts and graphs.",icon:e.jsx(X,{fontSize:"inherit"}),color:"info",variant:"default"})})})]}),e.jsx(k,{title:"Analysis Tools",icon:e.jsx(D,{}),divider:!0}),e.jsxs(r,{container:!0,spacing:2,sx:{mb:3},children:[e.jsx(r,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(x,{children:e.jsxs(t,{sx:{p:2},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(f,{color:"primary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Charts & Graphs"})]}),e.jsx(u,{sx:{my:1}}),e.jsxs(v,{dense:!0,children:[e.jsx(o,{onClick:()=>s("/charts/bar",!0),children:e.jsx(l,{primary:"Bar Charts"})}),e.jsx(o,{onClick:()=>s("/charts/pie",!0),children:e.jsx(l,{primary:"Pie Charts"})}),e.jsx(o,{onClick:()=>s("/charts/histogram",!0),children:e.jsx(l,{primary:"Histograms"})}),e.jsx(o,{onClick:()=>s("/charts/boxplot",!0),children:e.jsx(l,{primary:"Box Plots"})}),e.jsx(o,{onClick:()=>s("/charts/scatter",!0),children:e.jsx(l,{primary:"Scatter Plots"})})]})]})})}),e.jsx(r,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(x,{children:e.jsxs(t,{sx:{p:2},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(b,{color:"primary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Descriptive Statistics"})]}),e.jsx(u,{sx:{my:1}}),e.jsxs(v,{dense:!0,children:[e.jsx(o,{onClick:()=>s("/stats/descriptives",!0),children:e.jsx(l,{primary:"Numeric Summaries"})}),e.jsx(o,{onClick:()=>s("/stats/frequencies",!0),children:e.jsx(l,{primary:"Frequency Tables"})}),e.jsx(o,{onClick:()=>s("/stats/crosstabs",!0),children:e.jsx(l,{primary:"Cross Tabulations"})}),e.jsx(o,{onClick:()=>s("/stats/normality",!0),children:e.jsx(l,{primary:"Normality Tests"})})]})]})})}),e.jsx(r,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(x,{children:e.jsxs(t,{sx:{p:2},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(D,{color:"primary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Inferential Statistics"})]}),e.jsx(u,{sx:{my:1}}),e.jsxs(v,{dense:!0,children:[e.jsx(o,{onClick:()=>s("/inference/ttest",!0),children:e.jsx(l,{primary:"Statistical Tests"})}),e.jsx(o,{onClick:()=>s("/inference/anova",!0),children:e.jsx(l,{primary:"ANOVA"})}),e.jsx(o,{onClick:()=>s("/inference/nonparametric",!0),children:e.jsx(l,{primary:"Non-parametric Tests"})}),e.jsx(o,{onClick:()=>s("/inference/assumptions",!0),children:e.jsx(l,{primary:"Assumption Checks"})})]})]})})}),e.jsx(r,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(x,{children:e.jsxs(t,{sx:{p:2},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(Y,{color:"primary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:"medium",children:"Correlation & Regression"})]}),e.jsx(u,{sx:{my:1}}),e.jsxs(v,{dense:!0,children:[e.jsx(o,{onClick:()=>s("/correlation/pearson",!0),children:e.jsx(l,{primary:"Correlation Matrix"})}),e.jsx(o,{onClick:()=>s("/correlation/linear",!0),children:e.jsx(l,{primary:"Linear Regression"})}),e.jsx(o,{onClick:()=>s("/correlation/logistic",!0),children:e.jsx(l,{primary:"Logistic Regression"})})]})]})})})]})]})]})};export{je as default};
