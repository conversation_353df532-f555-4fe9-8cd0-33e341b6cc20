import{j as t,B as r,e as s,R as o,f as a}from"./mui-libs-CfwFIaTD.js";import{getNavigationRoutes as p,getRoutesByCategory as j}from"./routeConfig-DN2aHLM5.js";import{r as u}from"./index-Bpan7Tbe.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const T=({onNavigate:e})=>{const x=u.getAllRoutes(),m=p(),l=j();return t.jsxs(r,{p:3,children:[t.jsx(s,{variant:"h4",gutterBottom:!0,children:"🚀 Router System Test"}),t.jsxs(o,{sx:{p:2,mb:3},children:[t.jsx(s,{variant:"h6",gutterBottom:!0,children:"Router Statistics"}),t.jsxs(s,{children:["Total Routes Registered: ",x.length]}),t.jsxs(s,{children:["Navigation Routes: ",m.length]}),t.jsxs(s,{children:["Categories: ",Object.keys(l).length]})]}),t.jsxs(o,{sx:{p:2,mb:3},children:[t.jsx(s,{variant:"h6",gutterBottom:!0,children:"Routes by Category"}),Object.entries(l).map(([c,n])=>t.jsxs(r,{sx:{mb:2},children:[t.jsxs(s,{variant:"subtitle1",sx:{fontWeight:"bold",textTransform:"capitalize"},children:[c," (",n.length," routes)"]}),t.jsxs(r,{sx:{ml:2},children:[n.slice(0,5).map(i=>{var d,h;return t.jsxs(r,{sx:{mb:1},children:[t.jsx(a,{variant:"outlined",size:"small",onClick:()=>e(i.path),sx:{mr:1,mb:1},children:((d=i.metadata)==null?void 0:d.title)||i.path}),t.jsx(s,{variant:"caption",color:"text.secondary",children:(h=i.metadata)==null?void 0:h.description})]},i.path)}),n.length>5&&t.jsxs(s,{variant:"caption",color:"text.secondary",children:["... and ",n.length-5," more"]})]})]},c))]}),t.jsxs(o,{sx:{p:2},children:[t.jsx(s,{variant:"h6",gutterBottom:!0,children:"Quick Navigation Test"}),t.jsxs(r,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:[t.jsx(a,{variant:"contained",onClick:()=>e("dashboard"),children:"Dashboard"}),t.jsx(a,{variant:"contained",onClick:()=>e("data-management"),children:"Data Management"}),t.jsx(a,{variant:"contained",onClick:()=>e("stats"),children:"Statistics"}),t.jsx(a,{variant:"contained",onClick:()=>e("charts"),children:"Charts"}),t.jsx(a,{variant:"contained",onClick:()=>e("correlation-analysis"),children:"Correlation"}),t.jsx(a,{variant:"contained",onClick:()=>e("assistant"),children:"Assistant"})]})]})]})};export{T as RouterTest,T as default};
