import{j as e,u as f,C as v,R as l,i as o,e as t,B as s,h as c,G as d,k as C,bz as w,aE as D,I as k,aF as h,d as I,l as A,f as S,bA as z,ad as n,W as x,c4 as T,N as p}from"./mui-libs-CfwFIaTD.js";import{r as q}from"./react-libs-Cr2nE3UY.js";const u=[{name:"Descriptive Analysis",shortDescription:"Summarize quantitative data",detailedDescription:"Calculate key statistics like mean, median, mode, standard deviation, variance, range, quartiles, skewness, and kurtosis for numeric variables. Includes histograms and box plots.",path:"stats/descriptives",icon:e.jsx(n,{}),category:"Quantitative",color:"#4CAF50"},{name:"Frequency Tables",shortDescription:"Summarize categorical data",detailedDescription:"Generate frequency and percentage tables for categorical, ordinal, or boolean variables. Includes options for cumulative frequencies and sorting. Visualized with bar or pie charts.",path:"stats/frequencies",icon:e.jsx(x,{}),category:"Categorical",color:"#2196F3"},{name:"Cross Tabulation",shortDescription:"Analyze relationships between categorical variables",detailedDescription:"Create two-way tables showing the joint frequency distribution of two categorical variables. Includes row, column, and total percentages, and optionally performs a Chi-Square test of independence.",path:"stats/crosstabs",icon:e.jsx(T,{}),category:"Categorical",color:"#FF9800"},{name:"Normality Test",shortDescription:"Assess if data is normally distributed",detailedDescription:"Perform statistical tests (like Kolmogorov-Smirnov) and visualize with Q-Q plots and histograms with normal curve overlays to check if a numeric variable follows a normal distribution.",path:"stats/normality",icon:e.jsx(p,{}),category:"Distribution",color:"#9C27B0"}],G=({onNavigate:m})=>{const r=f(),[i,g]=q.useState("All"),b=["All","Quantitative","Categorical","Distribution"],j=i==="All"?u:u.filter(a=>a.category===i),y=a=>{switch(a){case"Quantitative":return e.jsx(n,{});case"Categorical":return e.jsx(x,{});case"Distribution":return e.jsx(p,{});default:return e.jsx(n,{})}};return e.jsxs(v,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(l,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${o(r.palette.success.main,.1)} 0%, ${o(r.palette.info.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(t,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Descriptive Statistics Tools"}),e.jsx(t,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Summarize, organize, and visualize your data"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Access fundamental tools to understand the basic features of your dataset, including central tendency, dispersion, frequency distributions, and relationships between categorical variables."})]}),e.jsxs(s,{sx:{mb:4},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),e.jsx(s,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:b.map(a=>e.jsx(c,{label:a,onClick:()=>g(a),variant:i===a?"filled":"outlined",color:i===a?"primary":"default",icon:a!=="All"?y(a):void 0,sx:{"&:hover":{backgroundColor:i===a?r.palette.primary.dark:o(r.palette.primary.main,.1)}}},a))})]}),e.jsx(d,{container:!0,spacing:3,children:j.map((a,B)=>e.jsx(d,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(C,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:r.shadows[8],"& .launch-button":{backgroundColor:a.color,color:"white"}}},children:[e.jsx(w,{avatar:e.jsx(I,{sx:{bgcolor:a.color,width:48,height:48},children:a.icon}),title:e.jsx(t,{variant:"h6",fontWeight:"bold",children:a.name}),subheader:e.jsx(s,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:e.jsx(c,{label:a.category,size:"small",variant:"outlined",color:"primary"})}),action:e.jsx(D,{title:"More information",children:e.jsx(k,{size:"small",children:e.jsx(h,{})})})}),e.jsxs(A,{sx:{flexGrow:1,pt:0},children:[e.jsx(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:a.shortDescription}),e.jsx(t,{variant:"body2",paragraph:!0,children:a.detailedDescription})]}),e.jsx(s,{sx:{p:2,pt:0},children:e.jsxs(S,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>m(a.path),endIcon:e.jsx(z,{}),sx:{borderColor:a.color,color:a.color,fontWeight:"bold","&:hover":{borderColor:a.color}},children:["Launch ",a.name]})})]})},a.name))}),e.jsx(l,{elevation:1,sx:{p:3,mt:4,backgroundColor:o(r.palette.info.main,.05),border:`1px solid ${o(r.palette.info.main,.2)}`},children:e.jsxs(s,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[e.jsx(h,{color:"info"}),e.jsxs(s,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),e.jsxs(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Summarizing numeric data?"})," Try Descriptive Analysis"]}),e.jsxs(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Counting categories?"})," Use Frequency Tables"]}),e.jsxs(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Comparing two categories?"})," Assess with Cross Tabulation"]}),e.jsxs(t,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Checking for normal distribution?"})," Normality Test is the tool for you"]})]})]})})]})};export{G as D,u as d};
