import{u as Ie,j as e,B as o,e as n,G as v,R as W,a6 as Le,aE as H,a7 as de,c7 as Ve,aW as ze,F as ce,ai as _,b9 as q,ba as $,bb as k,bN as De,c8 as he,aj as P,b2 as E,f as ue,ae as me,W as xe,I as ge,b7 as Ae,bH as Be,ah as Z}from"./mui-libs-CfwFIaTD.js";import{r as u,b as ee}from"./react-libs-Cr2nE3UY.js";import{i as Ne,j as Pe}from"./charts-plotly-BhN4fPIu.js";import{a as Ee,D as be}from"./index-Bpan7Tbe.js";import{b as te,c as ae,a as Ge}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./math-lib-BOZ-XUok.js";const pe={title:"Histogram",xAxisLabel:"Value",yAxisLabel:"Frequency",showNormalCurve:!0,showMeanLine:!0,showMedianLine:!1,bins:10,colorScheme:"default",histnorm:"",showOutliers:!0},le={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999","#66c2a5"]},Ye=()=>{const{datasets:D,currentDataset:b}=Ee(),s=Ie(),[f,se]=u.useState((b==null?void 0:b.id)||""),[j,K]=u.useState(""),[G,U]=u.useState(""),[a,re]=u.useState(pe),[y,Y]=u.useState("variables"),[A,B]=u.useState([]),[fe,R]=u.useState({}),[C,J]=u.useState(null),[N,Q]=u.useState(!1),[ne,M]=u.useState(null),i=ee.useMemo(()=>f&&D.find(t=>t.id===f)||null,[D,f]),ie=ee.useMemo(()=>(i==null?void 0:i.columns.filter(t=>t.type===be.CATEGORICAL))||[],[i]),X=ee.useMemo(()=>(i==null?void 0:i.columns.filter(t=>t.type===be.NUMERIC))||[],[i]);u.useEffect(()=>{b!=null&&b.id&&b.id!==f&&(se(b.id),K(""),U(""),B([]),R({}),J(null))},[b]);const je=t=>{const l=t.target.value;se(l),K(""),U(""),B([]),R({}),J(null)},ye=t=>{K(t.target.value)},ve=t=>{U(t.target.value)},x=(t,l)=>{re(d=>({...d,[t]:l}))},Ce=(t,l,d,I,c,g)=>{const L=[],S=[],O=(I-d)/100;for(let V=0;V<=100;V++){const w=d+V*O;let r=1/(l*Math.sqrt(2*Math.PI))*Math.exp(-.5*Math.pow((w-t)/l,2));a.histnorm===""&&g&&c>0&&(r=r*c*g),L.push(w),S.push(r)}return{x:L,y:S,type:"scatter",mode:"lines",name:"Normal Curve",yaxis:a.histnorm===""?"y1":"y2",line:{color:"red",width:2}}},oe=()=>{var t,l;if(!i||!j){M("Please select a dataset and a value variable.");return}Q(!0),M(null);try{const d=i.columns.find(r=>r.id===j),I=G?i.columns.find(r=>r.id===G):null;if(!d)throw new Error("Value column not found.");let c=[];const g=[],L=le[a.colorScheme]||le.default;if(I)Array.from(new Set(i.data.map(h=>String(h[I.name])))).forEach((h,z)=>{let m=i.data.filter(p=>String(p[I.name])===h).map(p=>p[d.name]).filter(p=>typeof p=="number"&&!isNaN(p));if(!a.showOutliers&&m.length>0){const p=te(m),ke=ae(m);m=m.filter(Me=>Math.abs((Me-p)/ke)<=3)}c.push(...m),m.length>0&&g.push({x:m,type:"histogram",name:h,nbinsx:a.bins,histnorm:a.histnorm,marker:{color:L[z%L.length]},opacity:.75})});else{let r=i.data.map(h=>h[d.name]).filter(h=>typeof h=="number"&&!isNaN(h));if(!a.showOutliers&&r.length>0){const h=te(r),z=ae(r);r=r.filter(m=>Math.abs((m-h)/z)<=3)}c.push(...r),r.length>0&&g.push({x:r,type:"histogram",name:d.name,nbinsx:a.bins,histnorm:a.histnorm,marker:{color:L[0]}})}if(c.length===0){M("No valid numeric data to plot after filtering."),Q(!1),B([]);return}const S=te(c),T=Ge(c),F=ae(c),O=Math.min(...c),V=Math.max(...c);J({mean:S,median:T,stdDev:F,min:O,max:V,totalCount:c.length});const w={title:{text:a.title},xaxis:{title:{text:a.xAxisLabel},autorange:!0},yaxis:{title:{text:a.histnorm===""?"Frequency":a.histnorm.charAt(0).toUpperCase()+a.histnorm.slice(1)},autorange:!0},barmode:I?"overlay":"stack",bargap:.05,legend:{traceorder:"normal"},shapes:[]};if(a.histnorm!==""&&a.showNormalCurve&&(w.yaxis2={title:{text:"Density"},overlaying:"y",side:"right",showgrid:!1,autorange:!0}),a.showMeanLine&&C&&((t=w.shapes)==null||t.push({type:"line",x0:S,x1:S,y0:0,y1:1,yref:"paper",line:{color:"red",width:2,dash:"dash"},name:"Mean"})),a.showMedianLine&&C&&((l=w.shapes)==null||l.push({type:"line",x0:T,x1:T,y0:0,y1:1,yref:"paper",line:{color:"green",width:2,dash:"dashdot"},name:"Median"})),a.showNormalCurve&&F>0){let r;if(a.histnorm===""&&g.length>0&&g[0].x){const z=g[0].x,m=Math.min(...z);r=(Math.max(...z)-m)/a.bins}const h=Ce(S,F,O,V,c.length,r);g.push(h)}B(g),R(w)}catch(d){M(`Error generating histogram: ${d instanceof Error?d.message:String(d)}`),B([]),R({})}finally{Q(!1)}};u.useEffect(()=>{},[a,j,G,i==null?void 0:i.id]),u.useEffect(()=>{const t=l=>{l.altKey&&!l.ctrlKey&&!l.shiftKey&&(l.key==="1"?(l.preventDefault(),Y("variables")):l.key==="2"&&(l.preventDefault(),Y("settings")))};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)},[]);const Se=()=>{re(pe)},we=()=>{const t=document.getElementById("plotly-histogram");t&&A.length>0?Pe.downloadImage(t,{format:"svg",filename:`${a.title.replace(/\s+/g,"_")}_histogram`,width:800,height:600}).catch(l=>{M(`Error downloading chart: ${l.message}`)}):M("Chart not available for download.")};return e.jsxs(o,{p:3,children:[e.jsx(n,{variant:"h5",gutterBottom:!0,children:"Histogram Generator"}),e.jsx(o,{mb:2,p:2,sx:{backgroundColor:s.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)",borderRadius:1},children:e.jsxs(n,{variant:"body2",color:"text.secondary",children:["Generate histograms to visualize the distribution of numerical data. Use keyboard shortcuts: ",e.jsx("strong",{children:"Alt+1"})," for Variables panel, ",e.jsx("strong",{children:"Alt+2"})," for Settings panel."]})}),e.jsxs(v,{container:!0,spacing:2,children:[e.jsxs(v,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(W,{elevation:1,sx:{mb:1,backgroundColor:s.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(Le,{value:y,onChange:(t,l)=>Y(l),variant:"fullWidth",sx:{minHeight:48,"& .MuiTab-root":{minHeight:48,textTransform:"none",fontSize:"0.875rem",fontWeight:500,color:s.palette.text.secondary,"&.Mui-selected":{color:s.palette.primary.main},"&:hover":{color:s.palette.primary.main,backgroundColor:s.palette.action.hover}},"& .MuiTabs-indicator":{backgroundColor:s.palette.primary.main}},children:[e.jsx(H,{title:"Variable Selection Panel",placement:"top",children:e.jsx(de,{value:"variables",label:"Variables",icon:e.jsx(Ve,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(H,{title:"Chart Settings Panel",placement:"top",children:e.jsx(de,{value:"settings",label:"Settings",icon:e.jsx(ze,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(ce,{in:y==="variables",timeout:300,children:e.jsx(o,{sx:{display:y==="variables"?"block":"none"},children:e.jsxs(W,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:s.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Data Selection"}),e.jsxs(_,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"dataset-select-label",children:"Dataset"}),e.jsx($,{labelId:"dataset-select-label",value:f,label:"Dataset",onChange:je,disabled:D.length===0,children:D.length===0?e.jsx(k,{value:"",disabled:!0,children:"No datasets available"}):D.map(t=>e.jsxs(k,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]}),e.jsxs(_,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"value-variable-label",children:"Value Variable"}),e.jsx($,{labelId:"value-variable-label",value:j,label:"Value Variable",onChange:ye,disabled:X.length===0||!f,children:X.length===0?e.jsx(k,{value:"",disabled:!0,children:"No numeric variables"}):X.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))})]}),e.jsxs(_,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"group-variable-label",children:"Group By (Optional)"}),e.jsxs($,{labelId:"group-variable-label",value:G,label:"Group By (Optional)",onChange:ve,disabled:ie.length===0||!f,children:[e.jsx(k,{value:"",children:"No Grouping"}),ie.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(o,{sx:{mt:2},children:[e.jsx(n,{gutterBottom:!0,children:"Number of Bins"}),e.jsx(De,{value:a.bins,min:5,max:100,step:1,marks:[{value:5,label:"5"},{value:25,label:"25"},{value:50,label:"50"},{value:100,label:"100"}],onChange:(t,l)=>x("bins",l),valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(he,{sx:{mt:2},children:[e.jsx(P,{control:e.jsx(E,{size:"small",checked:a.histnorm==="density"||a.histnorm==="probability density",onChange:t=>x("histnorm",t.target.checked?"density":"")}),label:"Density Plot"}),e.jsx(n,{variant:"caption",color:"text.secondary",sx:{ml:4},children:"(vs. Frequency)"}),e.jsx(P,{control:e.jsx(E,{size:"small",checked:a.showOutliers,onChange:t=>x("showOutliers",t.target.checked)}),label:"Include Outliers"})]}),e.jsxs(o,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(ue,{variant:"contained",onClick:oe,disabled:!j||N,startIcon:N?e.jsx(me,{size:20}):e.jsx(xe,{}),fullWidth:!0,children:N?"Generating...":"Generate Chart"}),e.jsxs(o,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(H,{title:"Download Chart",children:e.jsx(ge,{onClick:we,disabled:A.length===0,children:e.jsx(Ae,{})})}),e.jsx(H,{title:"Reset Settings",children:e.jsx(ge,{onClick:Se,children:e.jsx(Be,{})})})]})]})]})})}),e.jsx(ce,{in:y==="settings",timeout:300,children:e.jsx(o,{sx:{display:y==="settings"?"block":"none"},children:e.jsxs(W,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:s.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Chart Settings"}),e.jsxs(o,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Labels & Title"}),e.jsx(Z,{fullWidth:!0,label:"Chart Title",value:a.title,onChange:t=>x("title",t.target.value),margin:"normal",variant:"outlined",size:"small"}),e.jsx(Z,{fullWidth:!0,label:"X-Axis Label",value:a.xAxisLabel,onChange:t=>x("xAxisLabel",t.target.value),margin:"normal",variant:"outlined",size:"small"}),e.jsx(Z,{fullWidth:!0,label:"Y-Axis Label",value:a.yAxisLabel,onChange:t=>x("yAxisLabel",t.target.value),margin:"normal",variant:"outlined",size:"small"})]}),e.jsxs(o,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(_,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"color-scheme-label",children:"Color Scheme"}),e.jsx($,{labelId:"color-scheme-label",value:a.colorScheme,label:"Color Scheme",onChange:t=>x("colorScheme",t.target.value),children:Object.keys(le).map(t=>e.jsx(k,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})]}),e.jsxs(o,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(he,{children:[e.jsx(P,{control:e.jsx(E,{size:"small",checked:a.showNormalCurve,onChange:t=>x("showNormalCurve",t.target.checked)}),label:"Show Normal Curve"}),e.jsx(P,{control:e.jsx(E,{size:"small",checked:a.showMeanLine,onChange:t=>x("showMeanLine",t.target.checked)}),label:"Show Mean Line"}),e.jsx(P,{control:e.jsx(E,{size:"small",checked:a.showMedianLine,onChange:t=>x("showMedianLine",t.target.checked)}),label:"Show Median Line"})]})]}),e.jsx(ue,{variant:"outlined",color:"secondary",onClick:oe,disabled:!j||N||!f,fullWidth:!0,sx:{mt:2},children:"Apply Customizations & Regenerate"})]})})})]}),e.jsx(v,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(W,{elevation:2,sx:{p:2},children:[e.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(n,{variant:"h6",children:"Chart Preview"}),e.jsxs(o,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(n,{variant:"body2",color:"text.secondary",children:["Active: ",y==="variables"?"Variables":"Settings"]}),e.jsx(o,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:y==="variables"?s.palette.primary.main:s.palette.warning.main,boxShadow:`0 0 0 2px ${y==="variables"?s.palette.primary.main+"20":s.palette.warning.main+"20"}`}})]})]}),ne&&e.jsx(o,{sx:{mb:2,p:2,backgroundColor:s.palette.error.light+"20",borderRadius:1,border:`1px solid ${s.palette.error.light}`},children:e.jsx(n,{color:"error",children:ne})}),e.jsx(o,{id:"plotly-histogram-container",sx:{minHeight:500,display:"flex",justifyContent:"center",alignItems:"center",border:`1px solid ${s.palette.divider}`,borderRadius:1,backgroundColor:s.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)"},children:N?e.jsxs(o,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(me,{}),e.jsx(n,{color:"text.secondary",children:"Generating histogram..."})]}):A.length>0?e.jsx(Ne,{divId:"plotly-histogram",data:A,layout:{...fe,height:500,autosize:!0,paper_bgcolor:s.palette.mode==="dark"?s.palette.background.paper:"#fff",plot_bgcolor:s.palette.mode==="dark"?s.palette.background.default:"#fff",font:{color:s.palette.text.primary}},useResizeHandler:!0,style:{width:"100%",height:"500px"},config:{displayModeBar:!0,displaylogo:!1,modeBarButtonsToRemove:["pan2d","lasso2d","select2d","autoScale2d"],responsive:!0,toImageButtonOptions:{format:"svg",filename:a.title.replace(/\s+/g,"_")||"histogram",width:800,height:600,scale:1}}}):e.jsxs(o,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,children:[e.jsx(xe,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(n,{color:"text.secondary",textAlign:"center",children:i?j?"Chart will appear here once generated":"Select a value variable to generate the histogram":"Select a dataset to begin"}),i&&!j&&e.jsx(n,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"})]})}),C&&A.length>0&&e.jsxs(o,{mt:2,p:2,bgcolor:"background.paper",borderRadius:1,boxShadow:1,children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"Summary Statistics"}),e.jsxs(v,{container:!0,spacing:1,children:[e.jsx(v,{item:!0,xs:6,sm:3,children:e.jsxs(n,{variant:"body2",children:["Count: ",e.jsx("strong",{children:C.totalCount})]})}),e.jsx(v,{item:!0,xs:6,sm:3,children:e.jsxs(n,{variant:"body2",children:["Mean: ",e.jsx("strong",{children:C.mean.toFixed(2)})]})}),e.jsx(v,{item:!0,xs:6,sm:3,children:e.jsxs(n,{variant:"body2",children:["Median: ",e.jsx("strong",{children:C.median.toFixed(2)})]})}),e.jsx(v,{item:!0,xs:6,sm:3,children:e.jsxs(n,{variant:"body2",children:["Std. Dev: ",e.jsx("strong",{children:C.stdDev.toFixed(2)})]})})]})]})]})})]})]})};export{Ye as default};
