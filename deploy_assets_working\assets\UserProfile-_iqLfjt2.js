import{j as e,k as M,l as Y,B as r,e as s,h as k,f as b,cf as le,o as F,J as V,cm as oe,ce,G as h,D as J,g as q,aF as z,L as K,m as B,n as O,r as L,s as de,E as ue,R as E,cN as Q,cO as xe,ae as _,bA as me,by as pe,C as he,a6 as ye,a7 as H,y as X,d as je,cP as ge,ah as N,ai as be,b9 as fe,ba as ve,bb as Z,be as Ce,b7 as Se}from"./mui-libs-CfwFIaTD.js";import{r as o}from"./react-libs-Cr2nE3UY.js";import{b as G}from"./index-Bpan7Tbe.js";import{c as Ie}from"./countries-BGaMXFfG.js";import{c as Ae}from"./supabase-lib-B3goak-P.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";import"./ml-tensorflow-D19WVUQh.js";const Pe=({showUpgradeOptions:c=!0,compact:d=!1,showQuickActions:u=!1})=>{const{user:f,accountType:n,subscriptionData:x,subscriptionStatus:P,hasActiveSubscription:C,nextPaymentDate:y,billingCycle:U,canUpgradeAccount:w,isEducationalUser:D,educationalTier:W,canAccessAdvancedAnalysis:R,canAccessPublicationReady:j}=G(),p=D&&n==="edu"?[{id:"pro",name:"Educational Pro",monthlyPrice:"$10",annualPrice:"$96",features:["Keep all current Advanced Analysis features","Add Publication Ready tools","Add Cloud Storage","Priority Support","Perfect for academic papers & research"],icon:e.jsx(V,{}),color:"#1976d2"}]:n==="standard"?[{id:"pro",name:"Pro Account",monthlyPrice:"$10",annualPrice:"$96",features:["Advanced Analysis","Publication Ready","Cloud Storage","Priority Support"],icon:e.jsx(ue,{}),color:"#1976d2"}]:[],i=()=>{switch(n){case"pro":return{label:"PRO",color:"primary",description:"Full Pro features"};case"edu_pro":return{label:"EDUCATIONAL PRO",color:"primary",description:"Full Pro features for education"};case"edu":return{label:"EDUCATIONAL FREE",color:"secondary",description:"Advanced Analysis included"};case"standard":return{label:"STANDARD",color:"default",description:"Basic features"};default:return{label:"GUEST",color:"default",description:"Sample data access"}}},S=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),v=t=>{const I=`Account Upgrade Request - ${t}`,g=`Hi DataStatPro Team,

I would like to upgrade my account to ${t}.

Account Details:
- Email: ${f==null?void 0:f.email}
- Current Plan: ${n==null?void 0:n.toUpperCase()}
- Requested Plan: ${t}

Please provide instructions for upgrading my account.

Thank you!`,l=`mailto:<EMAIL>?subject=${encodeURIComponent(I)}&body=${encodeURIComponent(g)}`;window.open(l)};return d?e.jsx(M,{sx:{minWidth:275},children:e.jsxs(Y,{sx:{pb:1},children:[e.jsxs(r,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(r,{children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Current Plan"}),e.jsx(k,{label:i().label,color:i().color,size:"small"})]}),w&&u&&e.jsx(b,{size:"small",variant:"outlined",startIcon:e.jsx(le,{}),onClick:()=>v("Pro Account"),children:"Upgrade"})]}),C&&y&&e.jsxs(s,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:1},children:["Next payment: ",S(y)]})]})}):e.jsx(M,{children:e.jsxs(Y,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Account Status"}),e.jsxs(r,{sx:{mb:3},children:[e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:2,mb:1},children:[e.jsx(k,{label:i().label,color:i().color,size:"medium"}),C&&e.jsx(k,{label:"ACTIVE",color:"success",size:"small",icon:e.jsx(F,{})})]}),D&&e.jsxs(r,{sx:{mt:2,p:2,bgcolor:"background.default",borderRadius:1},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(V,{fontSize:"small"}),"Educational Account Features"]}),e.jsxs(r,{sx:{display:"flex",flexDirection:"column",gap:1},children:[e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1},children:[R?e.jsx(F,{fontSize:"small",color:"success"}):e.jsx(oe,{fontSize:"small",color:"error"}),e.jsxs(s,{variant:"body2",children:["Advanced Analysis ",R?"(Included Free)":"(Not Available)"]})]}),e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1},children:[j?e.jsx(F,{fontSize:"small",color:"success"}):e.jsx(ce,{fontSize:"small",color:"warning"}),e.jsxs(s,{variant:"body2",children:["Publication Ready ",j?"(Pro Subscription)":"(Upgrade Required)"]})]})]})]}),x&&e.jsx(r,{sx:{mt:2,p:2,bgcolor:"background.default",borderRadius:1},children:e.jsxs(h,{container:!0,spacing:2,children:[e.jsxs(h,{item:!0,xs:12,sm:6,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Billing Cycle"}),e.jsx(s,{variant:"body1",sx:{textTransform:"capitalize"},children:U})]}),y&&e.jsxs(h,{item:!0,xs:12,sm:6,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Next Payment"}),e.jsx(s,{variant:"body1",children:S(y)})]})]})})]}),w&&c&&e.jsxs(e.Fragment,{children:[e.jsx(J,{sx:{mb:3}}),e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Upgrade Your Account"}),e.jsx(q,{severity:"info",sx:{mb:2},children:e.jsx(s,{variant:"body2",children:"Payment processing is currently in development. Click upgrade buttons to contact us via email."})}),e.jsx(r,{sx:{display:"flex",flexDirection:"column",gap:2},children:p.map(t=>e.jsxs(M,{variant:"outlined",sx:{p:2},children:[e.jsxs(r,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[e.jsx(r,{sx:{width:40,height:40,borderRadius:"50%",bgcolor:`${t.color}20`,color:t.color,display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0},children:t.icon}),e.jsxs(r,{sx:{flexGrow:1},children:[e.jsx(s,{variant:"subtitle1",fontWeight:"bold",children:t.name}),e.jsxs(s,{variant:"body2",color:"text.secondary",sx:{mb:1},children:[t.monthlyPrice,"/month or ",t.annualPrice,"/year (save 20%)"]}),t.requiresEduEmail&&e.jsxs(s,{variant:"caption",color:"warning.main",sx:{display:"block",mb:1},children:[e.jsx(z,{sx:{fontSize:14,mr:.5}}),"Requires educational email (.edu domain)"]}),e.jsx(K,{dense:!0,sx:{py:0},children:t.features.slice(0,3).map((I,g)=>e.jsxs(B,{sx:{px:0,py:.25},children:[e.jsx(O,{sx:{minWidth:20},children:e.jsx(F,{sx:{fontSize:16,color:"success.main"}})}),e.jsx(L,{primary:I,primaryTypographyProps:{variant:"body2"}})]},g))})]})]}),e.jsx(de,{sx:{px:0,pt:2},children:e.jsxs(b,{variant:"contained",color:"primary",onClick:()=>v(t.name),sx:{bgcolor:t.color},children:["Upgrade to ",t.name]})})]},t.id))})]})]})})};Ae("https://ghzibvkqmdlpyaidfbah.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.OCeU38SWqhgeprIekN8qoaPdCzu04RaU-ktdNOci4rs");const Ue=()=>{const{user:c,session:d,accountType:u,subscriptionData:f,subscriptionStatus:n,hasActiveSubscription:x,nextPaymentDate:P,billingCycle:C,canUpgradeAccount:y,refreshSubscription:U,isEducationalUser:w,educationalTier:D,canAccessAdvancedAnalysis:W,canAccessPublicationReady:R}=G(),[j,m]=o.useState(!1),[p,i]=o.useState(null),S=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),v=async()=>{{const l="Billing Management Request",$=`Hi DataStatPro Team,

I would like to manage my billing and subscription settings.

Account Details:
- Email: ${c==null?void 0:c.email}
- Current Plan: ${u==null?void 0:u.toUpperCase()}
- Subscription Status: ${n==null?void 0:n.toUpperCase()}

Please provide assistance with billing management.

Thank you!`,T=`mailto:<EMAIL>?subject=${encodeURIComponent(l)}&body=${encodeURIComponent($)}`;window.open(T);return}},t=async()=>{m(!0);try{await U()}catch(l){console.error("Error refreshing subscription:",l)}finally{m(!1)}},I=l=>{switch(l){case"active":return"success";case"past_due":return"warning";case"canceled":return"error";default:return"default"}},g=l=>{switch(l){case"active":return e.jsx(F,{});case"past_due":return e.jsx(pe,{});default:return e.jsx(z,{})}};return e.jsxs(r,{children:[e.jsxs(q,{severity:"info",sx:{mb:3},children:[e.jsx(s,{variant:"body1",fontWeight:"medium",children:"🚧 Billing Management in Development"}),e.jsx(s,{variant:"body2",sx:{mt:1},children:"Full billing management features are currently in development. For billing inquiries, please contact us via email using the buttons below."})]}),e.jsxs(E,{sx:{p:3,mb:3},children:[e.jsxs(s,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Q,{}),"Current Plan"]}),e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[e.jsx(k,{label:(u==null?void 0:u.toUpperCase())||"STANDARD",color:u==="standard"?"default":"primary",size:"large"}),x&&n&&e.jsx(k,{label:n.toUpperCase(),color:I(n),size:"small",icon:g(n)})]}),f&&e.jsxs(h,{container:!0,spacing:2,sx:{mt:1},children:[e.jsxs(h,{item:!0,xs:12,sm:6,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Billing Cycle"}),e.jsxs(s,{variant:"body1",sx:{textTransform:"capitalize"},children:[C," billing"]})]}),P&&e.jsxs(h,{item:!0,xs:12,sm:6,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Next Payment Date"}),e.jsx(s,{variant:"body1",children:S(P)})]}),e.jsxs(h,{item:!0,xs:12,sm:6,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Subscription ID"}),e.jsx(s,{variant:"body2",sx:{fontFamily:"monospace"},children:f.stripe_subscription_id||"N/A"})]})]})]}),x&&e.jsxs(E,{sx:{p:3,mb:3},children:[e.jsxs(s,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(xe,{}),"Manage Subscription"]}),e.jsx(s,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Update payment method, download invoices, and manage your subscription settings."}),e.jsxs(r,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(b,{variant:"contained",onClick:v,disabled:j,startIcon:j?e.jsx(_,{size:20}):e.jsx(me,{}),children:"Contact Support"}),e.jsx(b,{variant:"outlined",onClick:t,disabled:j,children:"Refresh Status"})]}),p&&e.jsx(q,{severity:"error",sx:{mt:2},children:p})]}),y&&e.jsxs(E,{sx:{p:3,mb:3},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Upgrade Your Account"}),e.jsx(Pe,{showUpgradeOptions:!0,compact:!1})]}),e.jsxs(E,{sx:{p:3},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Billing Information"}),e.jsxs(K,{children:[e.jsxs(B,{children:[e.jsx(O,{children:e.jsx(z,{color:"primary"})}),e.jsx(L,{primary:"Secure Payment Processing",secondary:"All payments are processed securely through Stripe. We never store your payment information."})]}),e.jsxs(B,{children:[e.jsx(O,{children:e.jsx(z,{color:"primary"})}),e.jsx(L,{primary:"Billing Cycle",secondary:"You can choose between monthly and annual billing. Annual billing includes a 20% discount."})]}),e.jsxs(B,{children:[e.jsx(O,{children:e.jsx(z,{color:"primary"})}),e.jsx(L,{primary:"Cancellation Policy",secondary:"You can cancel your subscription at any time. Your access will continue until the end of your current billing period."})]}),u==="edu"&&e.jsxs(B,{children:[e.jsx(O,{children:e.jsx(z,{color:"secondary"})}),e.jsx(L,{primary:"Educational Discount",secondary:"Your educational discount is automatically applied based on your .edu email address."})]})]}),e.jsx(J,{sx:{my:2}}),e.jsxs(s,{variant:"body2",color:"text.secondary",children:["For billing questions or support, contact us at"," ",e.jsx(b,{variant:"text",size:"small",onClick:()=>window.open("mailto:<EMAIL>"),sx:{textTransform:"none",p:0,minWidth:"auto"},children:"<EMAIL>"})]})]})]})},Oe=()=>{const{user:c,userProfile:d,signOut:u,updateProfile:f,uploadAvatar:n,accountType:x,refreshProfile:P}=G(),[C,y]=o.useState(""),[U,w]=o.useState(""),[D,W]=o.useState(""),[R,j]=o.useState(""),[m,p]=o.useState(!1),[i,S]=o.useState(!1),[v,t]=o.useState(null),[I,g]=o.useState(null),[l,$]=o.useState(null),[T,ee]=o.useState(0),se=(a,A)=>{ee(A)};o.useEffect(()=>{d?(console.log("📋 Loading profile data from AuthContext:",d),y(d.username||""),g(d.avatar_url||null),w(d.full_name||""),W(d.institution||""),j(d.country||""),p(!1)):c&&!d&&(console.log("🔄 User exists but no profile data, refreshing..."),p(!0),P().finally(()=>p(!1)))},[c,d,P]);const te=async()=>{try{p(!0),t(null);const a={username:C,full_name:U,institution:D,country:R},{error:A}=await f(a);if(A)throw A;t({type:"success",text:"Profile updated successfully!"})}catch(a){t({type:"error",text:a.message})}finally{p(!1)}},ae=a=>{a.target.files&&a.target.files[0]&&($(a.target.files[0]),g(URL.createObjectURL(a.target.files[0])))},re=async()=>{if(!l){t({type:"error",text:"Please select an image file first."});return}S(!0),t(null);try{const{error:a,publicUrl:A}=await n(l);if(a)throw a;A&&(g(A),t({type:"success",text:"Avatar uploaded successfully!"}))}catch(a){t({type:"error",text:`Avatar upload failed: ${a.message}`})}finally{S(!1),$(null)}},ne=async()=>{try{await u()}catch(a){console.error("Error signing out:",a.message)}};if(!c)return e.jsx(r,{sx:{p:3,textAlign:"center"},children:e.jsx(s,{children:"Please sign in to view your profile."})});const ie=()=>e.jsx(r,{children:e.jsxs(E,{elevation:3,sx:{p:3,mt:3},children:[v&&e.jsx(q,{severity:v.type,sx:{mb:3},children:v.text}),e.jsxs(h,{container:!0,spacing:3,children:[e.jsxs(h,{item:!0,xs:12,md:4,sx:{display:"flex",flexDirection:"column",alignItems:"center"},children:[e.jsx(je,{sx:{width:100,height:100,mb:2},src:I||void 0,children:e.jsx(X,{fontSize:"large"})}),e.jsx("input",{accept:"image/*",style:{display:"none"},id:"avatar-upload-input",type:"file",onChange:ae,disabled:i}),e.jsx("label",{htmlFor:"avatar-upload-input",children:e.jsx(b,{variant:"outlined",component:"span",size:"small",sx:{mt:1},startIcon:e.jsx(ge,{}),disabled:i,children:"Change Avatar"})}),l&&e.jsx(b,{variant:"contained",size:"small",sx:{mt:1,ml:1},onClick:re,disabled:i||m,children:i?e.jsx(_,{size:20}):"Upload"}),e.jsx(s,{variant:"subtitle1",gutterBottom:!0,sx:{mt:2},children:c.email}),e.jsxs(s,{variant:"body2",color:"textSecondary",children:["Member since: ",new Date(c.created_at||"").toLocaleDateString()]}),x&&e.jsx(k,{label:x==="standard"?"Standard":x==="pro"?"Pro":x==="edu"?"Educational":"Standard",color:x==="pro"?"primary":x==="edu"?"secondary":"default",variant:"outlined",size:"small",sx:{mt:1,fontWeight:"bold",borderRadius:"12px","& .MuiChip-label":{px:1.5}}})]}),e.jsxs(h,{item:!0,xs:12,md:8,children:[e.jsx(M,{variant:"outlined",sx:{mb:3},children:e.jsxs(Y,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Account Information"}),e.jsx(N,{label:"Username",fullWidth:!0,margin:"normal",value:C,onChange:a=>y(a.target.value),disabled:m||i}),e.jsx(N,{label:"Full Name",fullWidth:!0,margin:"normal",value:U,onChange:a=>w(a.target.value),disabled:m||i}),e.jsx(N,{label:"Email",fullWidth:!0,margin:"normal",value:c.email,disabled:!0,helperText:"Email cannot be changed"}),e.jsx(N,{label:"Institution",fullWidth:!0,margin:"normal",value:D,onChange:a=>W(a.target.value),disabled:m||i}),e.jsxs(be,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"country-select-label",children:"Country"}),e.jsxs(ve,{labelId:"country-select-label",id:"country-select",value:R,label:"Country",onChange:a=>j(a.target.value),disabled:m||i,children:[e.jsx(Z,{value:"",children:e.jsx("em",{children:"Select a country"})}),Ie.map(a=>e.jsx(Z,{value:a,children:a},a))]}),e.jsx(Ce,{children:"Select your country from the list"})]}),e.jsx(r,{sx:{mt:3,display:"flex",justifyContent:"flex-end"},children:e.jsx(b,{variant:"contained",color:"primary",startIcon:e.jsx(Se,{}),onClick:te,disabled:m||i,children:m?e.jsx(_,{size:24}):"Save Profile Changes"})})]})}),e.jsxs(r,{sx:{mt:4},children:[e.jsx(J,{sx:{mb:2}}),e.jsxs(r,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(s,{variant:"subtitle2",color:"textSecondary",children:"Need to leave?"}),e.jsx(b,{variant:"outlined",color:"error",onClick:ne,children:"Sign Out"})]})]})]})]})]})});return e.jsxs(he,{maxWidth:"lg",sx:{py:3},children:[e.jsx(s,{variant:"h4",gutterBottom:!0,children:"Account Settings"}),e.jsxs(E,{sx:{mt:3},children:[e.jsxs(ye,{value:T,onChange:se,sx:{borderBottom:1,borderColor:"divider"},children:[e.jsx(H,{icon:e.jsx(X,{}),label:"Profile",iconPosition:"start"}),e.jsx(H,{icon:e.jsx(Q,{}),label:"Billing",iconPosition:"start"})]}),e.jsxs(r,{sx:{p:3},children:[T===0&&ie(),T===1&&e.jsx(Ue,{})]})]})]})};export{Oe as default};
