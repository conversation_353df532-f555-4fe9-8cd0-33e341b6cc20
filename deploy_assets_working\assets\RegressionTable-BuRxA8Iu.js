import{j as t,R as xe,B as D,e as j,g as qe,G as B,ai as X,b9 as Y,ba as Z,bb as T,be as je,h as ot,cg as at,f as ct,ae as lt,ao as dt,ap as ut,aq as ht,ar as Fe,as as p,at as gt,D as He,bR as pt}from"./mui-libs-CfwFIaTD.js";import{r as L}from"./react-libs-Cr2nE3UY.js";import{a as xt,D as u,g as ft}from"./index-Bpan7Tbe.js";import{A as mt}from"./AddToResultsButton-BwSXKCt2.js";import{P as yt}from"./PublicationReadyGate-BGFbKbJc.js";import{m as bt,a as jt}from"./regression-BKqSTW2N.js";import{c as vt}from"./coxRegressionService-CqIkroID.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import"./math-lib-BOZ-XUok.js";import"./descriptive-Djo0s6H4.js";const Ct=({regressionType:J,results:M})=>{if(!M||!J)return null;const ee=G=>G===void 0?"N/A":G<.001?"< .001":`= ${G.toFixed(3)}`,te=()=>{switch(J){case"linear":if(!M)return null;const{coefficients:G,intercept:r,p_values:W,rSquared:E,fStatistic:ne,dfModel:q,dfError:de,pValue:U}=M;let K=`A linear regression was conducted to examine the relationship between the independent variables and the dependent variable. The model explained ${(E==null?void 0:E.toFixed(2))||"N/A"}% of the variance in the dependent variable, F(${q||"N/A"}, ${de||"N/A"}) = ${(ne==null?void 0:ne.toFixed(2))||"N/A"}, p ${ee(U)}. `;if(Object.keys(G||{}).forEach(v=>{const y=G==null?void 0:G[v],S=W==null?void 0:W[v],w=ee(S),P=S!==void 0&&S<.05?"significantly":"not significantly",be=y>0?"positively":"negatively";K+=`The variable ${v} was ${P} ${be} associated with the dependent variable (b = ${(y==null?void 0:y.toFixed(2))||"N/A"}, p ${w}). `}),r!==void 0&&(W==null?void 0:W["(Intercept)"])!==void 0){const v=W["(Intercept)"],y=ee(v),S=v<.05?"significantly":"not significantly";K+=`The intercept was ${S} different from zero (b = ${(r==null?void 0:r.toFixed(2))||"N/A"}, p ${y}).`}return t.jsxs(D,{children:[t.jsx(j,{variant:"h6",gutterBottom:!0,children:"Linear Regression Interpretation"}),t.jsx(j,{variant:"body1",children:K})]});case"logistic":if(!M)return null;const ae=M,{coefficients:ie,hazard_ratios:N,p_values:se,pseudoRSquared:Q,logLikelihood:H,aic:n,positiveCategory:ce}=ae;let le=`A logistic regression was conducted to predict the likelihood of the outcome variable (coded as 1 for ${ce||"N/A"}). The model fit statistics were as follows: Log-Likelihood = ${(H==null?void 0:H.toFixed(2))||"N/A"}, AIC = ${(n==null?void 0:n.toFixed(2))||"N/A"}, Pseudo R² = ${(Q==null?void 0:Q.toFixed(2))||"N/A"}. `;return Object.keys(ie||{}).forEach(v=>{const y=N==null?void 0:N[v],S=se==null?void 0:se[v],w=ee(S),P=S!==void 0&&S<.05?"significantly":"not significantly";le+=`The variable ${v} was ${P} associated with the outcome. The odds ratio for ${v} was ${(y==null?void 0:y.toFixed(2))||"N/A"} (p ${w}), indicating that for a one-unit increase in ${v}, the odds of the outcome occurring are multiplied by ${(y==null?void 0:y.toFixed(2))||"N/A"}, holding other variables constant. `}),t.jsxs(D,{children:[t.jsx(j,{variant:"h6",gutterBottom:!0,children:"Logistic Regression Interpretation"}),t.jsx(j,{variant:"body1",children:le})]});case"cox":if(!M)return null;const $=M,{coefficients:fe,hazard_ratios:m,p_values:ue,concordance:re,log_likelihood:he,n_observations:me,n_events:a,eventCategory:ve}=$;let ye=`A Cox proportional hazards regression was conducted to examine the time to event (event defined as ${ve||"N/A"}). The model fit statistics were as follows: Log-Likelihood = ${(he==null?void 0:he.toFixed(2))||"N/A"}, Concordance Index = ${(re==null?void 0:re.toFixed(2))||"N/A"}. The analysis included ${me||"N/A"} observations and ${a||"N/A"} events. `;return Object.keys(fe||{}).forEach(v=>{const y=m==null?void 0:m[v],S=ue==null?void 0:ue[v],w=ee(S),P=S!==void 0&&S<.05?"significantly":"not significantly";ye+=`The variable ${v} was ${P} associated with the hazard of the event. The hazard ratio for ${v} was ${(y==null?void 0:y.toFixed(2))||"N/A"} (p ${w}), indicating that for a one-unit increase in ${v}, the hazard of the event is multiplied by ${(y==null?void 0:y.toFixed(2))||"N/A"}, holding other covariates constant. `}),t.jsxs(D,{children:[t.jsx(j,{variant:"h6",gutterBottom:!0,children:"Cox Regression Interpretation"}),t.jsx(j,{variant:"body1",children:ye})]});default:return null}};return t.jsx(xe,{elevation:2,sx:{p:2,mt:3},children:te()})},kt=()=>{var Le,Me,Ne,Pe,ke,_e,Be,Ge,Ue,ze,De;const{datasets:J,currentDataset:M,setCurrentDataset:ee}=xt(),[te,G]=L.useState((M==null?void 0:M.id)||""),[r,W]=L.useState(""),[E,ne]=L.useState(""),[q,de]=L.useState([]),[U,K]=L.useState({}),[ae,ie]=L.useState(""),[N,se]=L.useState(""),[Q,H]=L.useState(""),[n,ce]=L.useState(null),[le,$]=L.useState(!1),[fe,m]=L.useState(null),[ue,re]=L.useState(!1),[he,me]=L.useState(""),a=J.find(e=>e.id===te),ve=e=>{const i=e.target.value;G(i),ne(""),de([]),K({}),ie(""),se(""),H(""),ce(null),m(null);const s=J.find(o=>o.id===i);s&&ee(s)},ye=e=>{W(e.target.value),ne(""),de([]),K({}),ie(""),se(""),H(""),ce(null),m(null)},v=e=>{const i=e.target.value;if(ne(i),r==="logistic"&&a){const s=a.columns.find(o=>o.name===i);if(s&&s.type===u.CATEGORICAL){const o=w(i);o.length===2&&ie(o[1])}else s&&s.type===u.NUMERIC&&ie("")}if(r==="cox"&&a){const s=a.columns.find(o=>o.name===i);if(s&&s.type===u.CATEGORICAL){const o=w(i);o.length===2&&H(o[1])}else s&&s.type===u.NUMERIC&&H("")}},y=e=>{const i=e.target.value,s=typeof i=="string"?i.split(","):i;if(de(s),a){const o={...U};s.forEach(l=>{const d=a.columns.find(b=>b.name===l);if(d&&d.type===u.CATEGORICAL&&!o[l]){const b=w(l);b.length>0&&(o[l]=b[0])}}),Object.keys(o).forEach(l=>{s.includes(l)||delete o[l]}),K(o)}},S=(e,i)=>{K(s=>({...s,[e]:i}))},w=e=>{if(!a)return[];const i=a.columns.find(s=>s.name===e);return!i||i.type!==u.CATEGORICAL?[]:ft(i.id,a)},P=e=>{if(!a)return!1;const i=a.columns.find(l=>l.name===e);if(!i||i.type!==u.NUMERIC)return!1;const s=new Set;a.data.forEach(l=>{const d=l[i.name];typeof d=="number"&&s.add(d)});const o=Array.from(s).sort();return o.length<=2&&o.every(l=>l===0||l===1)},be=e=>{if(!a)return!1;const i=a.columns.find(s=>s.name===e);return i?i.type===u.NUMERIC?P(e):i.type===u.CATEGORICAL?w(e).length===2:!1:!1},Je=()=>a?a.columns.filter(e=>be(e.name)).map(e=>e.name):[],Ce=(e,i,s)=>{const o=w(i.name),l={};return o.filter(d=>d!==s).forEach(d=>{const b=`${i.name}_${d}`;l[b]=e.map(c=>c[i.name]===d?1:0)}),l},Ke=async()=>{if(!a||q.length===0||!E){m("Please select a dataset, at least one independent variable, and a dependent variable.");return}$(!0),m(null),ce(null);const e=a.columns.find(o=>o.name===E),i=a.columns.filter(o=>q.includes(o.name));if(!e||i.length!==q.length){m("Selected variables not found in the dataset."),$(!1);return}const s=a.data;try{let o;if(r==="linear"){if(e.type!==u.NUMERIC){m("Linear regression requires a numeric dependent variable."),$(!1);return}const l=[],d=[];i.forEach(g=>{if(g.type===u.NUMERIC)l.push(s.map(C=>C[g.name])),d.push(g.name);else if(g.type===u.CATEGORICAL){const C=U[g.name];if(!C){m(`No base category selected for ${g.name}`),$(!1);return}const z=Ce(s,g,C);Object.entries(z).forEach(([oe,Ae])=>{l.push(Ae),d.push(oe)})}});const b=s.map(g=>g[e.name]),c=bt(l[0].map((g,C)=>l.map(z=>z[C])),b),k={},f={},F={},V={};d.forEach((g,C)=>{k[g]=c.coefficients[C],f[g]=c.stdErrors[C],F[g]=c.pValues[C];const z=c.coefficients[C],oe=c.stdErrors[C];V[g]=[z-1.96*oe,z+1.96*oe]});const _=[0,...c.coefficients],h=d.length,x=c.n-h-1,R=c.rSquared/h/((1-c.rSquared)/x);o={coefficients:k,intercept:c.intercept,interceptStdError:c.interceptStdError,interceptPValue:c.interceptPValue,stdErrors:f,p_values:F,confidence_intervals:V,rSquared:c.rSquared,pValue:c.pValue,beta:_,dfModel:h,dfError:x,fStatistic:R,n:c.n,baseCategories:U}}else if(r==="logistic"){if(!be(e.name)){m("Logistic regression requires a binary dependent variable (numeric 0/1 or categorical with exactly 2 values)."),$(!1);return}if(e.type===u.CATEGORICAL&&!ae){m("Please select which category to map to 1."),$(!1);return}const l=[],d=[];i.forEach(h=>{if(h.type===u.NUMERIC)l.push(s.map(x=>x[h.name])),d.push(h.name);else if(h.type===u.CATEGORICAL){const x=U[h.name];if(!x){m(`No base category selected for ${h.name}`),$(!1);return}const R=Ce(s,h,x);Object.entries(R).forEach(([g,C])=>{l.push(C),d.push(g)})}});const b=s.map(h=>{const x=h[e.name];return e.type===u.NUMERIC?typeof x=="number"?x:0:x===ae?1:0}),c=jt(l[0].map((h,x)=>l.map(R=>R[x])),b),k={},f={},F={},V={},_={};d.forEach((h,x)=>{k[h]=c.coefficients[x],f[h]=c.stdErrors[x],F[h]=c.pValues[x];const R=c.coefficients[x],g=c.stdErrors[x];if(r==="logistic"||r==="cox"){const C=Math.exp(R-1.96*g),z=Math.exp(R+1.96*g);V[h]=[C,z]}else V[h]=[R-1.96*g,R+1.96*g];_[h]=Math.exp(R)}),o={coefficients:k,intercept:c.intercept,interceptStdError:c.interceptStdError,interceptPValue:c.interceptPValue,stdErrors:f,p_values:F,confidence_intervals:V,hazard_ratios:_,logLikelihood:c.logLikelihood,aic:c.aic,pseudoRSquared:c.pseudoRSquared,accuracy:c.accuracy,precision:c.precision,recall:c.recall,f1Score:c.f1Score,auc:c.auc,n:c.n,baseCategories:U,positiveCategory:e.type===u.CATEGORICAL?ae:"1",dependentVariableType:e.type}}else if(r==="cox"){if(!N){m("Please select a time variable for Cox regression."),$(!1);return}const l=a.columns.find(f=>f.name===N);if(!l||l.type!==u.NUMERIC){m("Cox regression requires a numeric time variable."),$(!1);return}let d;if(e.type===u.CATEGORICAL){if(w(e.name).length!==2){m("Cox regression requires a binary categorical event variable."),$(!1);return}if(!Q){m("Please select which category represents the event."),$(!1);return}d=s.map(F=>F[e.name]===Q?1:0)}else if(e.type===u.NUMERIC){if(!P(e.name)){m("Numeric event variable must contain only 0 and 1 values."),$(!1);return}d=s.map(f=>f[e.name])}else{m("Event variable must be binary categorical or numeric (0/1)."),$(!1);return}const b={};i.forEach(f=>{if(f.type===u.NUMERIC)b[f.name]=s.map(F=>F[f.name]);else if(f.type===u.CATEGORICAL){const F=U[f.name];if(!F){m(`No base category selected for ${f.name}`),$(!1);return}const V=Ce(s,f,F);Object.entries(V).forEach(([_,h])=>{b[_]=h})}});const c={time:s.map(f=>f[l.name]),event:d,covariates:b};o={...await vt.runCoxRegression(c),baseCategories:U,eventCategory:e.type===u.CATEGORICAL?Q:"1"}}else{m("Invalid regression type selected."),$(!1);return}ce(o)}catch(o){m(`Error running regression analysis: ${o instanceof Error?o.message:String(o)}`)}finally{$(!1)}},Qe=()=>{re(!1)},Te=a?a.columns.map(e=>e.name):[],Xe=a?a.columns.filter(e=>e.type===u.NUMERIC).map(e=>e.name):[],Ye=()=>{switch(r){case"linear":return"Linear";case"logistic":return"Logistic";case"cox":return"Cox";default:return""}},Ze=()=>{if(!te)return"Please select a dataset to begin your regression analysis.";switch(r){case"linear":return"Linear regression analyzes the relationship between variables. Supports both numeric and categorical independent variables.";case"logistic":return"Logistic regression predicts binary outcomes. Requires a binary dependent variable (numeric 0/1 or categorical with exactly 2 values) and supports both numeric and categorical independent variables.";case"cox":return"Cox regression analyzes time-to-event data. Requires a time variable, binary event variable (categorical or 0/1 numeric), and supports both numeric and categorical covariates.";default:return"Select a regression type to continue with your analysis."}},Se=e=>!(n!=null&&n.beta)||!Array.isArray(n.beta)||e<0||e>=n.beta.length?"N/A":n.beta[e].toFixed(2),et=(e,i)=>{if(!i||!e)return"";const s=o=>o===void 0?"N/A":o<.001?"< .001":`= ${o.toFixed(3)}`;switch(e){case"linear":const{coefficients:o,intercept:l,p_values:d,rSquared:b,fStatistic:c,dfModel:k,dfError:f,pValue:F}=i;let V=`A linear regression was conducted to examine the relationship between the independent variables and the dependent variable. The model explained ${(b==null?void 0:b.toFixed(2))||"N/A"}% of the variance in the dependent variable, F(${k||"N/A"}, ${f||"N/A"}) = ${(c==null?void 0:c.toFixed(2))||"N/A"}, p ${s(F)}. `;if(Object.keys(o||{}).forEach(I=>{const A=o==null?void 0:o[I],O=d==null?void 0:d[I],ge=s(O),pe=O!==void 0&&O<.05?"significantly":"not significantly",rt=A>0?"positively":"negatively";V+=`The variable ${I} was ${pe} ${rt} associated with the dependent variable (b = ${(A==null?void 0:A.toFixed(2))||"N/A"}, p ${ge}). `}),l!==void 0&&(d==null?void 0:d["(Intercept)"])!==void 0){const I=d["(Intercept)"],A=s(I),O=I<.05?"significantly":"not significantly";V+=`The intercept was ${O} different from zero (b = ${(l==null?void 0:l.toFixed(2))||"N/A"}, p ${A}).`}return V;case"logistic":const{coefficients:_,hazard_ratios:h,p_values:x,pseudoRSquared:R,logLikelihood:g,aic:C,positiveCategory:z}=i;let oe=`A logistic regression was conducted to predict the likelihood of the outcome variable (coded as 1 for ${z||"N/A"}). The model fit statistics were as follows: Log-Likelihood = ${(g==null?void 0:g.toFixed(2))||"N/A"}, AIC = ${(C==null?void 0:C.toFixed(2))||"N/A"}, Pseudo R² = ${(R==null?void 0:R.toFixed(2))||"N/A"}. `;return Object.keys(_||{}).forEach(I=>{const A=h==null?void 0:h[I],O=x==null?void 0:x[I],ge=s(O),pe=O!==void 0&&O<.05?"significantly":"not significantly";oe+=`The variable ${I} was ${pe} associated with the outcome. The odds ratio for ${I} was ${(A==null?void 0:A.toFixed(2))||"N/A"} (p ${ge}), indicating that for a one-unit increase in ${I}, the odds of the outcome occurring are multiplied by ${(A==null?void 0:A.toFixed(2))||"N/A"}, holding other variables constant. `}),oe;case"cox":const{coefficients:Ae,hazard_ratios:Ee,p_values:$e,concordance:Re,log_likelihood:Ie,n_observations:nt,n_events:it,eventCategory:st}=i;let We=`A Cox proportional hazards regression was conducted to examine the time to event (event defined as ${st||"N/A"}). The model fit statistics were as follows: Log-Likelihood = ${(Ie==null?void 0:Ie.toFixed(2))||"N/A"}, Concordance Index = ${(Re==null?void 0:Re.toFixed(2))||"N/A"}. The analysis included ${nt||"N/A"} observations and ${it||"N/A"} events. `;return Object.keys(Ae||{}).forEach(I=>{const A=Ee==null?void 0:Ee[I],O=$e==null?void 0:$e[I],ge=s(O),pe=O!==void 0&&O<.05?"significantly":"not significantly";We+=`The variable ${I} was ${pe} associated with the hazard of the event. The hazard ratio for ${I} was ${(A==null?void 0:A.toFixed(2))||"N/A"} (p ${ge}), indicating that for a one-unit increase in ${I}, the hazard of the event is multiplied by ${(A==null?void 0:A.toFixed(2))||"N/A"}, holding other covariates constant. `}),We;default:return""}},we=q.filter(e=>{const i=a==null?void 0:a.columns.find(s=>s.name===e);return(i==null?void 0:i.type)===u.CATEGORICAL}),Ve=r==="logistic"&&E&&a?(()=>{const e=a.columns.find(i=>i.name===E);return(e==null?void 0:e.type)===u.CATEGORICAL?w(E):[]})():[],Oe=r==="cox"&&E&&a?(()=>{const e=a.columns.find(i=>i.name===E);return(e==null?void 0:e.type)===u.CATEGORICAL?w(E):[]})():[],tt=a&&r==="cox"?a.columns.filter(e=>e.name!==N&&(e.type===u.CATEGORICAL||e.type===u.NUMERIC&&P(e.name))).map(e=>e.name):[];return t.jsx(yt,{children:t.jsxs(D,{p:3,children:[t.jsx(j,{variant:"h5",gutterBottom:!0,children:"Regression Analysis"}),t.jsxs(xe,{elevation:2,sx:{p:2,mb:3},children:[t.jsx(j,{variant:"subtitle1",gutterBottom:!0,children:"Configure Regression Model"}),t.jsx(qe,{severity:"info",sx:{mb:2},children:Ze()}),t.jsxs(B,{container:!0,spacing:2,children:[t.jsx(B,{item:!0,xs:12,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsx(Y,{id:"dataset-select-label",children:"Dataset"}),t.jsx(Z,{labelId:"dataset-select-label",id:"dataset-select",value:te,label:"Dataset",onChange:ve,disabled:J.length===0,children:J.length===0?t.jsx(T,{value:"",disabled:!0,children:"No datasets available"}):J.map(e=>t.jsxs(T,{value:e.id,children:[e.name," (",e.data.length," rows)"]},e.id))})]})}),te&&t.jsx(B,{item:!0,xs:12,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsx(Y,{id:"regression-type-label",children:"Regression Type"}),t.jsxs(Z,{labelId:"regression-type-label",id:"regression-type",value:r,label:"Regression Type",onChange:ye,children:[t.jsx(T,{value:"",children:t.jsx("em",{children:"-- Select Type --"})}),t.jsx(T,{value:"linear",children:"Linear Regression"}),t.jsx(T,{value:"logistic",children:"Logistic Regression"}),t.jsx(T,{value:"cox",children:"Cox Regression"})]})]})}),r&&a&&t.jsxs(t.Fragment,{children:[r==="cox"&&t.jsx(B,{item:!0,xs:12,md:6,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsx(Y,{id:"time-variable-label",children:"Time Variable"}),t.jsxs(Z,{labelId:"time-variable-label",id:"time-variable",value:N,label:"Time Variable",onChange:e=>se(e.target.value),children:[t.jsx(T,{value:"",children:t.jsx("em",{children:"-- Select Variable --"})}),Xe.map(e=>t.jsx(T,{value:e,children:e},e))]}),t.jsx(je,{children:"Select the time-to-event variable"})]})}),t.jsx(B,{item:!0,xs:12,md:6,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsxs(Y,{id:"dependent-variable-label",children:["Dependent Variable ",r==="cox"?"(Event)":""]}),t.jsxs(Z,{labelId:"dependent-variable-label",id:"dependent-variable",value:E,label:`Dependent Variable ${r==="cox"?"(Event)":""}`,onChange:v,children:[t.jsx(T,{value:"",children:t.jsx("em",{children:"-- Select Variable --"})}),r==="cox"?tt.map(e=>{const i=a.columns.find(o=>o.name===e),s=(i==null?void 0:i.type)===u.NUMERIC&&P(e)?"NUMERIC (0/1)":i==null?void 0:i.type;return t.jsxs(T,{value:e,children:[e," (",s,")"]},e)}):r==="logistic"?Je().map(e=>{const i=a.columns.find(o=>o.name===e),s=(i==null?void 0:i.type)===u.NUMERIC&&P(e)?"NUMERIC (0/1)":(i==null?void 0:i.type)===u.CATEGORICAL?"CATEGORICAL (Binary)":i==null?void 0:i.type;return t.jsxs(T,{value:e,children:[e," (",s,")"]},e)}):Te.filter(e=>r!=="cox"||e!==N).map(e=>{const i=a.columns.find(s=>s.name===e);return t.jsxs(T,{value:e,children:[e," (",i==null?void 0:i.type,")"]},e)})]})]})}),r==="cox"&&Oe.length===2&&t.jsx(B,{item:!0,xs:12,md:6,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsx(Y,{id:"cox-event-category-label",children:"Event Category"}),t.jsx(Z,{labelId:"cox-event-category-label",id:"cox-event-category",value:Q,label:"Event Category",onChange:e=>H(e.target.value),children:Oe.map(e=>t.jsx(T,{value:e,children:e},e))}),t.jsx(je,{children:"Select which category represents the event occurrence"})]})}),r==="logistic"&&Ve.length===2&&t.jsx(B,{item:!0,xs:12,md:6,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsx(Y,{id:"positive-category-label",children:"Map to 1 (Positive Outcome)"}),t.jsx(Z,{labelId:"positive-category-label",id:"positive-category",value:ae,label:"Map to 1 (Positive Outcome)",onChange:e=>ie(e.target.value),children:Ve.map(e=>t.jsx(T,{value:e,children:e},e))}),t.jsx(je,{children:"Select which category represents the positive outcome (will be coded as 1)"})]})}),t.jsx(B,{item:!0,xs:12,md:12,children:t.jsxs(X,{fullWidth:!0,margin:"normal",children:[t.jsxs(Y,{id:"independent-variables-label",children:["Independent Variables ",r==="cox"?"(Covariates)":""]}),t.jsx(Z,{labelId:"independent-variables-label",id:"independent-variables",multiple:!0,value:q,onChange:y,input:t.jsx(at,{label:`Independent Variables ${r==="cox"?"(Covariates)":""}`}),renderValue:e=>t.jsx(D,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:e.map(i=>t.jsx(ot,{label:i,size:"small"},i))}),children:Te.filter(e=>e!==E&&(r!=="cox"||e!==N)).map(e=>{const i=a.columns.find(s=>s.name===e);return t.jsxs(T,{value:e,children:[e," (",i==null?void 0:i.type,")"]},e)})})]})}),we.length>0&&t.jsx(B,{item:!0,xs:12,children:t.jsxs(xe,{variant:"outlined",sx:{p:2,mt:1},children:[t.jsx(j,{variant:"subtitle2",gutterBottom:!0,children:"Select Base Categories for Categorical Variables"}),t.jsx(B,{container:!0,spacing:2,children:we.map(e=>{const i=w(e);return t.jsx(B,{item:!0,xs:12,md:6,children:t.jsxs(X,{fullWidth:!0,size:"small",children:[t.jsxs(Y,{id:`base-${e}-label`,children:["Base Category for ",e]}),t.jsx(Z,{labelId:`base-${e}-label`,id:`base-${e}`,value:U[e]||"",label:`Base Category for ${e}`,onChange:s=>S(e,s.target.value),children:i.map(s=>t.jsx(T,{value:s,children:s},s))}),t.jsx(je,{children:"Reference category for dummy variables"})]})},e)})})]})})]})]}),t.jsx(D,{mt:2,children:t.jsx(ct,{variant:"contained",color:"primary",onClick:Ke,disabled:le||!te||!r||!E||q.length===0||r==="cox"&&!N,children:"Run Regression Analysis"})})]}),le&&t.jsx(D,{display:"flex",justifyContent:"center",my:4,children:t.jsx(lt,{})}),fe&&t.jsx(qe,{severity:"error",sx:{mb:3},children:fe}),n&&!le&&t.jsx(D,{children:t.jsxs(xe,{elevation:2,sx:{p:2,mb:3},children:[t.jsxs(j,{variant:"h6",gutterBottom:!0,children:[Ye()," Regression Results"]}),t.jsx(dt,{component:xe,variant:"outlined",children:t.jsxs(ut,{size:"small",children:[t.jsx(ht,{children:t.jsxs(Fe,{children:[t.jsx(p,{sx:{fontWeight:"bold"},children:"Variable"}),t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"B"}),t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"SE"}),r==="linear"&&t.jsxs(t.Fragment,{children:[t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"95% CI"}),t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"β"}),t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"t"})]}),(r==="logistic"||r==="cox")&&t.jsxs(t.Fragment,{children:[t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:r==="logistic"?"OR":"HR"}),t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"95% CI"})]}),t.jsx(p,{align:"right",sx:{fontWeight:"bold"},children:"p"})]})}),t.jsxs(gt,{children:[r!=="cox"&&n.intercept!==void 0&&t.jsxs(Fe,{children:[t.jsx(p,{children:"(Intercept)"}),t.jsx(p,{align:"right",children:n.intercept.toFixed(2)}),t.jsx(p,{align:"right",children:((Le=n.interceptStdError)==null?void 0:Le.toFixed(2))||"N/A"}),r==="linear"&&t.jsxs(t.Fragment,{children:[t.jsx(p,{align:"right",children:n.interceptStdError?`[${(n.intercept-1.96*n.interceptStdError).toFixed(2)}, ${(n.intercept+1.96*n.interceptStdError).toFixed(2)}]`:"N/A"}),t.jsx(p,{align:"right",children:Se(0)}),t.jsx(p,{align:"right",children:n.interceptStdError?(n.intercept/n.interceptStdError).toFixed(2):"N/A"})]}),r==="logistic"&&t.jsxs(t.Fragment,{children:[t.jsx(p,{align:"right",children:"—"}),t.jsx(p,{align:"right",children:n.interceptStdError?`[${(n.intercept-1.96*n.interceptStdError).toFixed(2)}, ${(n.intercept+1.96*n.interceptStdError).toFixed(2)}]`:"N/A"})]}),t.jsx(p,{align:"right",children:n.interceptPValue!==void 0?n.interceptPValue<.001?"< .001":n.interceptPValue.toFixed(3):"N/A"})]}),n.coefficients&&Object.keys(n.coefficients).map((e,i)=>{var o,l,d,b,c,k,f,F,V,_,h,x,R,g;const s=e.includes("_")?e.replace("_"," = "):e;return t.jsxs(Fe,{children:[t.jsx(p,{children:s}),t.jsx(p,{align:"right",children:((o=n.coefficients[e])==null?void 0:o.toFixed(2))||"N/A"}),t.jsx(p,{align:"right",children:((d=(l=n.std_errors)==null?void 0:l[e])==null?void 0:d.toFixed(2))||((c=(b=n.stdErrors)==null?void 0:b[e])==null?void 0:c.toFixed(2))||"N/A"}),r==="linear"&&t.jsxs(t.Fragment,{children:[t.jsx(p,{align:"right",children:(k=n.confidence_intervals)!=null&&k[e]?`[${n.confidence_intervals[e][0].toFixed(2)}, ${n.confidence_intervals[e][1].toFixed(2)}]`:"N/A"}),t.jsx(p,{align:"right",children:Se(i+1)}),t.jsx(p,{align:"right",children:n.coefficients[e]&&((f=n.std_errors)!=null&&f[e]||(F=n.stdErrors)!=null&&F[e])?(n.coefficients[e]/(((V=n.std_errors)==null?void 0:V[e])||((_=n.stdErrors)==null?void 0:_[e]))).toFixed(2):"N/A"})]}),(r==="logistic"||r==="cox")&&t.jsxs(t.Fragment,{children:[t.jsx(p,{align:"right",children:((x=(h=n.hazard_ratios)==null?void 0:h[e])==null?void 0:x.toFixed(2))||"N/A"}),t.jsx(p,{align:"right",children:(R=n.confidence_intervals)!=null&&R[e]?`[${n.confidence_intervals[e][0].toFixed(2)}, ${n.confidence_intervals[e][1].toFixed(2)}]`:"N/A"})]}),t.jsx(p,{align:"right",children:((g=n.p_values)==null?void 0:g[e])!==void 0?n.p_values[e]<.001?"< .001":n.p_values[e].toFixed(3):"N/A"})]},e)})]})]})}),t.jsx(He,{sx:{my:2}}),t.jsxs(D,{mt:2,children:[t.jsx(j,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:"bold"},children:"Model Fit Statistics"}),r==="linear"&&n.rSquared!==void 0&&t.jsxs(j,{variant:"body2",sx:{mt:1},children:["F(",n.dfModel||"N/A",", ",n.dfError||"N/A",") = ",((Me=n.fStatistic)==null?void 0:Me.toFixed(2))||"N/A",", p ",n.pValue!==void 0?n.pValue<.001?"< .001":`= ${n.pValue.toFixed(3)}`:"= N/A",", R² = ",n.rSquared.toFixed(2)]}),r==="logistic"&&n.pseudoRSquared!==void 0&&t.jsxs(t.Fragment,{children:[t.jsxs(j,{variant:"body2",sx:{mt:1},children:["Log-Likelihood: ",((Ne=n.logLikelihood)==null?void 0:Ne.toFixed(2))||"N/A",", AIC: ",((Pe=n.aic)==null?void 0:Pe.toFixed(2))||"N/A",", Pseudo R²: ",n.pseudoRSquared.toFixed(2)]}),t.jsxs(j,{variant:"body2",sx:{mt:1},children:["Accuracy: ",((ke=n.accuracy)==null?void 0:ke.toFixed(2))||"N/A",", Precision: ",((_e=n.precision)==null?void 0:_e.toFixed(2))||"N/A",", Recall: ",((Be=n.recall)==null?void 0:Be.toFixed(2))||"N/A",", F1-Score: ",((Ge=n.f1Score)==null?void 0:Ge.toFixed(2))||"N/A",", AUC: ",((Ue=n.auc)==null?void 0:Ue.toFixed(2))||"N/A"]})]}),r==="cox"&&n.concordance!==void 0&&t.jsxs(t.Fragment,{children:[t.jsxs(j,{variant:"body2",sx:{mt:1},children:["Log-Likelihood: ",((ze=n.log_likelihood)==null?void 0:ze.toFixed(2))||"N/A",", AIC: ",((De=n.aic)==null?void 0:De.toFixed(2))||"N/A",", Concordance Index: ",n.concordance.toFixed(2)]}),t.jsxs(j,{variant:"body2",sx:{mt:1},children:["N Observations: ",n.n_observations||"N/A",", N Events: ",n.n_events||"N/A"]})]}),n.baseCategories&&Object.keys(n.baseCategories).length>0||n.positiveCategory||n.eventCategory?t.jsxs(t.Fragment,{children:[t.jsx(He,{sx:{my:1}}),t.jsx(j,{variant:"subtitle2",sx:{mt:1,fontStyle:"italic"},children:"Model Configuration:"}),n.positiveCategory&&r==="logistic"&&t.jsx(j,{variant:"body2",sx:{fontSize:"0.875rem",color:"text.secondary"},children:n.dependentVariableType===u.CATEGORICAL?`Positive outcome (${E} = 1): ${n.positiveCategory}`:`Positive outcome: ${E} = 1 (numeric)`}),n.eventCategory&&t.jsxs(j,{variant:"body2",sx:{fontSize:"0.875rem",color:"text.secondary"},children:["Event (",E," = 1): ",n.eventCategory]}),n.baseCategories&&Object.keys(n.baseCategories).length>0&&t.jsxs(t.Fragment,{children:[t.jsx(j,{variant:"body2",sx:{fontSize:"0.875rem",color:"text.secondary",mt:.5},children:"Reference categories:"}),Object.entries(n.baseCategories).map(([e,i])=>t.jsxs(j,{variant:"body2",sx:{fontSize:"0.875rem",color:"text.secondary",ml:2},children:[e,": ",i]},e))]})]}):null]})]})}),n&&t.jsx(Ct,{regressionType:r,results:n}),n&&t.jsx(D,{sx:{display:"flex",justifyContent:"center",mt:2},children:t.jsx(mt,{resultData:{title:`${r.charAt(0).toUpperCase()+r.slice(1)} Regression Table (${(a==null?void 0:a.name)||"Unknown"})`,type:"regression",component:"RegressionTable",data:{dataset:(a==null?void 0:a.name)||"Unknown",regressionType:r,dependentVariable:E,independentVariables:q,results:n,interpretation:et(r,n),timestamp:new Date().toISOString(),totalSampleSize:(a==null?void 0:a.data.length)||0}},onSuccess:()=>{me("Results successfully added to Results Manager!"),re(!0)},onError:e=>{me(`Error adding results to Results Manager: ${e}`),re(!0)}})}),t.jsx(pt,{open:ue,autoHideDuration:4e3,onClose:Qe,message:he})]})})};export{kt as default};
