<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="./favicon.png" />
    <link rel="manifest" href="./manifest.json" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Primary Meta Tags -->
    <title>DataStatPro: Free Statistical Software for Teaching & Research | Affordable Alternative to Premium Analytics Tools</title>
    <meta name="title" content="DataStatPro: Free Statistical Software for Teaching & Research | Affordable Alternative to Premium Analytics Tools" />
    <meta name="description" content="DataStatPro: Free Statistical Software for Educators & Students | T-Tests, ANOVA, Regression & Advanced Analysis | AI-Powered Analysis Assistant | Cloud-Integrated SPSS Alternative | Publication-ready Tables and Visualizations" />
    <meta name="keywords" content="free statistical software, SPSS alternative, online data analysis, statistical analysis free, data management platform, t-tests, ANOVA, regression analysis, correlation analysis, non-parametric tests, data visualization, statistical graphs, AI analysis assistant, publication ready tables, APA formatting" />
    <meta name="author" content="DataStatPro Team" />
    <meta name="generator" content="DataStatPro v2.0" />
    <meta name="application-name" content="DataStatPro" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.datastatpro.com/" />
    <meta property="og:title" content="DataStatPro: Free Statistical Software for Teaching & Research | Affordable Alternative to Premium Analytics Tools" />
    <meta property="og:description" content="DataStatPro: Free Statistical Software for Educators & Students | T-Tests, ANOVA, Regression & Advanced Analysis | AI-Powered Analysis Assistant | Cloud-Integrated SPSS Alternative | Publication-ready Tables and Visualizations" />
    <meta property="og:image" content="https://www.datastatpro.com/logo.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="DataStatPro" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://www.datastatpro.com/" />
    <meta property="twitter:title" content="DataStatPro: Free Statistical Software for Teaching & Research | Affordable Alternative to Premium Analytics Tools" />
    <meta property="twitter:description" content="DataStatPro: Free Statistical Software for Educators & Students | T-Tests, ANOVA, Regression & Advanced Analysis | AI-Powered Analysis Assistant | Cloud-Integrated SPSS Alternative | Publication-ready Tables and Visualizations" />
    <meta property="twitter:image" content="https://www.datastatpro.com/logo.png" />
    <meta property="twitter:creator" content="@DataStatPro" />
    <meta property="twitter:site" content="@DataStatPro" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#667eea" />
    
    <!-- Canonical URL -->
    <meta name="robots" content="index, follow, max-image-preview:large" />
    <link rel="canonical" href="https://www.datastatpro.com/" />
    <link rel="alternate" hreflang="en" href="https://www.datastatpro.com/" />
    <link rel="alternate" hreflang="x-default" href="https://www.datastatpro.com/" />
    
    <!-- Preconnect to external domains for faster loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- Load fonts asynchronously to prevent render blocking -->
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript>
      <link
        rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
      />
    </noscript>

    <!-- Load Material Icons asynchronously -->
    <link
      rel="preload"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript>
      <link
        rel="stylesheet"
        href="https://fonts.googleapis.com/icon?family=Material+Icons"
      />
    </noscript>
    
    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "DataStatPro",
      "applicationCategory": "StatisticalApplication",
      "operatingSystem": "Web Browser",
      "url": "https://www.datastatpro.com",
      "downloadUrl": "https://www.datastatpro.com/app",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "description": "DataStatPro: Free Statistical Software for Educators & Students | T-Tests, ANOVA, Regression & Advanced Analysis | AI-Powered Analysis Assistant | Cloud-Integrated SPSS Alternative | Publication-ready Tables and Visualizations",
      "featureList": [
        "Data Import/Export (CSV, Excel, JSON)",
        "Descriptive Statistics",
        "Statistical Graphs and Visualizations",
        "T-Tests (One-sample, Independent, Paired)",
        "ANOVA (One-way, Two-way, Repeated Measures)",
        "Non-parametric Tests",
        "Correlation and Regression Analysis",
        "AI-Powered Analysis Assistant",
        "Publication-ready Tables",
        "APA Style Formatting"
      ],
      "screenshot": "https://www.datastatpro.com/logo.png",
      "softwareHelp": "https://www.datastatpro.com/app#help",
      "keywords": "free statistical software, SPSS alternative, online data analysis, statistical analysis free, data management platform, t-tests, ANOVA, regression analysis, correlation analysis, non-parametric tests, data visualization, AI analysis assistant, publication ready tables",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "150"
      },
      "author": {
        "@type": "Organization",
        "name": "DataStatPro Team"
      },
      "datePublished": "2024-01-01",
      "dateModified": "2025-06-25"
    }
    </script>

    <!-- Load KaTeX CSS asynchronously to prevent render blocking -->
    <link
      rel="preload"
      href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css"
      as="style"
      integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV"
      crossorigin="anonymous"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript>
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css"
        integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV"
        crossorigin="anonymous"
      />
    </noscript>
    <script type="module" crossorigin src="./assets/index-Bpan7Tbe.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/react-libs-Cr2nE3UY.js">
    <link rel="modulepreload" crossorigin href="./assets/mui-libs-CfwFIaTD.js">
    <link rel="modulepreload" crossorigin href="./assets/charts-recharts-d3-BEF1Y_jn.js">
    <link rel="modulepreload" crossorigin href="./assets/charts-plotly-BhN4fPIu.js">
    <link rel="modulepreload" crossorigin href="./assets/ml-tensorflow-D19WVUQh.js">
    <link rel="modulepreload" crossorigin href="./assets/supabase-lib-B3goak-P.js">
    <link rel="modulepreload" crossorigin href="./assets/other-utils-CR9xr_gI.js">
    <link rel="stylesheet" crossorigin href="./assets/charts-plotly-CuCRB34y.css">
    <link rel="stylesheet" crossorigin href="./assets/index-DyRZcmzc.css">
  <link rel="manifest" href="./manifest.webmanifest"></head>
  <body>
    <div id="root"></div>
    
    <noscript>
      <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
        <h2>JavaScript is Required</h2>
        <p>Sorry, but DataStatPro needs JavaScript to run. Please enable JavaScript in your browser and refresh the page.</p>
      </div>
    </noscript>
    

  </body>
</html>
