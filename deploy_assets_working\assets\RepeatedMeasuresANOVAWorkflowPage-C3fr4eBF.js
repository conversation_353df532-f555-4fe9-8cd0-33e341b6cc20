import{j as e,C as t,B as r}from"./mui-libs-CfwFIaTD.js";import"./AnalysisSteps-CmfAFU5C.js";import{P as s}from"./PageTitle-DA3BXQ4x.js";import"./StatsCard-op8tGQ0a.js";import"./react-libs-Cr2nE3UY.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import"./other-utils-CR9xr_gI.js";import{RepeatedMeasuresANOVA as o}from"./RepeatedMeasuresANOVA-DwrjssMW.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./DatasetSelector-G08QHuaN.js";import"./repeatedMeasuresANOVA-B8bv3NGM.js";const g=()=>e.jsxs(t,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsx(s,{title:"Repeated Measures ANOVA",description:"Analyze differences across multiple measurements on the same subjects."}),e.jsx(r,{sx:{mt:3},children:e.jsx(o,{})})]});export{g as default};
