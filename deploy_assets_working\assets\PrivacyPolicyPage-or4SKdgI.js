import{u as a,j as t,C as i,B as o,e}from"./mui-libs-CfwFIaTD.js";import{H as s}from"./index-Bpan7Tbe.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const x=()=>{const r=a();return t.jsxs(i,{maxWidth:"md",sx:{py:4},children:[t.jsxs(s,{children:[t.jsx("title",{children:"Privacy Policy - DataStatPro"}),t.jsx("meta",{name:"description",content:"DataStatPro Privacy Policy"})]}),t.jsxs(o,{sx:{mb:4},children:[t.jsx(e,{variant:"h3",component:"h1",gutterBottom:!0,sx:{fontWeight:"bold",color:r.palette.primary.main},children:"DataStatPro Privacy Policy"}),t.jsx(e,{variant:"body2",color:"text.secondary",children:"Last updated: June 20, 2025"})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Introduction"}),t.jsx(e,{variant:"body1",children:"This policy applies to DataStatPro (www.datastatpro.com) and outlines how we handle your data."})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Data Collection"}),t.jsx(e,{variant:"body1",children:"We do not collect or store personal information. Use DataStatPro anonymously—no accounts, tracking, or identifiable data required."})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Analytics"}),t.jsx(e,{variant:"body1",children:"Google Analytics collects anonymized technical data (e.g., device type, browser) to improve user experience. IP addresses are anonymized; no personal identification is possible. Details in our Cookie Policy."})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Cloud Integration"}),t.jsx(e,{variant:"body1",children:"You may import datasets from services like Google Drive or Dropbox. Login occurs via their secure portals; we only access files you explicitly select (e.g., spreadsheets). Data is processed locally—never stored on our servers."})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Data Security"}),t.jsx(e,{variant:"body1",children:"All analyses occur on your device. No data leaves your browser or interacts with external servers, ensuring full confidentiality. Suitable for sensitive/regulated information."})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"GDPR Compliance"}),t.jsx(e,{variant:"body1",children:"DataStatPro adheres to GDPR principles. Data retrieved from cloud platforms flows directly to your device—no intermediary storage or processing by us."})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Policy Updates"}),t.jsx(e,{variant:"body1",children:'We may update this policy periodically. Check the "Last updated" date for changes.'})]}),t.jsxs(o,{sx:{mb:3},children:[t.jsx(e,{variant:"h5",component:"h2",gutterBottom:!0,children:"Contact"}),t.jsx(e,{variant:"body1",children:"Questions? Reach <NAME_EMAIL>."})]})]})};export{x as default};
