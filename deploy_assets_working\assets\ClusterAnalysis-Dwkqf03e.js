import{u as ut,j as e,B,e as p,R as se,G as b,ai as G,b9 as J,ba as Q,bb as V,bW as dt,bX as ht,aD as mt,bY as xt,a_ as pt,aj as xe,aE as gt,I as ft,bs as bt,ak as jt,bN as yt,f as pe,ah as Ee,bc as Ae,c1 as vt,bw as St,ae as Ct,g as ge,a6 as wt,a7 as X,k as re,l as ae,D as fe,h as le,o as Dt,by as It,ao as Mt,ap as kt,aq as Et,ar as ze,as as W,at as At}from"./mui-libs-CfwFIaTD.js";import{r as E}from"./react-libs-Cr2nE3UY.js";import{a as zt,D as Bt}from"./index-Bpan7Tbe.js";import{i as Y}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./other-utils-CR9xr_gI.js";import"./charts-recharts-d3-BEF1Y_jn.js";const Vt=()=>{var Me,ke;const{datasets:Z,currentDataset:w,setCurrentDataset:Be}=zt(),f=ut(),[_e,$e]=E.useState((w==null?void 0:w.id)||""),[q,be]=E.useState([]),[ee,je]=E.useState(""),[M,ye]=E.useState("kmeans"),[$,ie]=E.useState(3),[ne,ve]=E.useState("euclidean"),[U,Oe]=E.useState("ward"),[oe,Pe]=E.useState(.5),[ce,Fe]=E.useState(5),[Se,Ne]=E.useState(!0),[K,Le]=E.useState(0),[A,Re]=E.useState({showScatterPlot:!0,showClusterProfiles:!0,showSilhouetteAnalysis:!0,showElbowMethod:!0,showDendrogram:!0,showClusterSizes:!0,showValidationMetrics:!0}),[ue,de]=E.useState(!1),[he,L]=E.useState(null),[i,O]=E.useState(null),[Ve,Ce]=E.useState(!1);E.useEffect(()=>{var s;const t=localStorage.getItem("cluster_analysis_results");if(t)try{const r=JSON.parse(t);O(r),r.method&&ye(r.method),(s=r.parameters)!=null&&s.distance&&ve(r.parameters.distance)}catch(r){console.error("Error parsing saved cluster analysis results:",r),localStorage.removeItem("cluster_analysis_results")}},[]);const me=(w==null?void 0:w.columns.filter(t=>t.type===Bt.NUMERIC))||[],Te=(w==null?void 0:w.columns)||[],qe=t=>{const s=t.target.value;$e(s),be([]),je(""),O(null),localStorage.removeItem("cluster_analysis_results");const r=Z.find(n=>n.id===s);r&&Be(r)},Ke=t=>{const s=t.target.value;be(typeof s=="string"?s.split(","):s),O(null),localStorage.removeItem("cluster_analysis_results")},We=t=>{je(t.target.value)},Ue=t=>{ye(t.target.value),O(null),localStorage.removeItem("cluster_analysis_results")},He=(t,s)=>{ie(s),O(null),localStorage.removeItem("cluster_analysis_results")},Ge=t=>{ve(t.target.value),O(null),localStorage.removeItem("cluster_analysis_results")},Je=t=>{Oe(t.target.value),O(null),localStorage.removeItem("cluster_analysis_results")},Qe=t=>{Pe(parseFloat(t.target.value)),O(null),localStorage.removeItem("cluster_analysis_results")},Xe=t=>{Fe(parseInt(t.target.value,10)),O(null),localStorage.removeItem("cluster_analysis_results")},Ye=t=>{Re(s=>({...s,[t]:!s[t]}))},Ze=(t,s)=>Math.sqrt(t.reduce((r,n,l)=>r+Math.pow(n-s[l],2),0)),et=(t,s)=>t.reduce((r,n,l)=>r+Math.abs(n-s[l]),0),tt=(t,s)=>{const r=t.reduce((o,a,c)=>o+a*s[c],0),n=Math.sqrt(t.reduce((o,a)=>o+a*a,0)),l=Math.sqrt(s.reduce((o,a)=>o+a*a,0));return n===0||l===0?1:1-r/(n*l)},P=(t,s)=>{switch(ne){case"manhattan":return et(t,s);case"cosine":return tt(t,s);default:return Ze(t,s)}},we=(t,s,r=100)=>{const n=t.length,l=t[0].length;let o=[];const a=new Set;for(;o.length<s&&o.length<n;){const u=Math.floor(Math.random()*n);a.has(u)||(o.push([...t[u]]),a.add(u))}if(o.length<s)for(let u=o.length;u<s;u++)o.push([...t[Math.floor(Math.random()*n)]]);let c=new Array(n).fill(0);for(let u=0;u<r;u++){let y=!1;for(let x=0;x<n;x++){let h=1/0,m=0;for(let S=0;S<s;S++){const D=P(t[x],o[S]);D<h&&(h=D,m=S)}c[x]!==m&&(c[x]=m,y=!0)}if(!y)break;for(let x=0;x<s;x++){const h=t.filter((m,S)=>c[S]===x);if(h.length>0)for(let m=0;m<l;m++)o[x][m]=h.reduce((S,D)=>S+D[m],0)/h.length;else o[x]=[...t[Math.floor(Math.random()*n)]]}}let j=0;for(let u=0;u<n;u++)j+=Math.pow(P(t[u],o[c[u]]),2);return{clusters:c,centroids:o,inertia:j}},st=(t,s)=>{const r=t.length;if(r===0)return[];if(s>=r)return t.map((h,m)=>m);t.map(h=>[h[0]]);let n=Array.from({length:r},(h,m)=>m),l=Array.from({length:r},(h,m)=>m);const o=Array(r).fill(0).map(()=>Array(r).fill(1/0));for(let h=0;h<r;h++)for(let m=h+1;m<r;m++)o[h][m]=o[m][h]=P(t[h],t[m]);const a=(h,m)=>{let S=1/0,D=-1/0,N=0,d=0;for(const g of h)for(const v of m){const C=P(t[g],t[v]);S=Math.min(S,C),D=Math.max(D,C),N+=C,d++}return U==="single"?S:U==="complete"?D:N/d};let c=r;const j=new Map(Array.from({length:r},(h,m)=>[m,[m]]));for(;c>s&&!(c<=1);){let h=1/0,m=-1,S=-1;for(let g=0;g<l.length;g++)for(let v=g+1;v<l.length;v++){const C=l[g],_=l[v],I=j.get(C),F=j.get(_),k=a(I,F);k<h&&(h=k,m=g,S=v)}if(m===-1||S===-1)break;const D=l[m],N=l[S],d=j.get(N);j.get(D).push(...d),j.delete(N),d.forEach(g=>{n[g]=D}),l.splice(S,1),l.splice(m,1),l.push(D),l=l.filter(g=>g!==N),c--}const u=Array.from(j.keys()),y={};u.forEach((h,m)=>{y[h]=m});const x=new Array(r).fill(0);for(let h=0;h<r;h++)x[h]=y[n[h]];return x},rt=(t,s,r)=>{const n=t.length,l=-1,o=-2,a=new Array(n).fill(o);let c=0;const j=u=>{const y=[];for(let x=0;x<n;x++)u!==x&&P(t[u],t[x])<=s&&y.push(x);return y};for(let u=0;u<n;u++){if(a[u]!==o)continue;const y=j(u);if(y.length<r-1){a[u]=l;continue}a[u]=c;let x=[...y],h=0;for(;h<x.length;){const m=x[h++];if(a[m]===l&&(a[m]=c),a[m]!==o)continue;a[m]=c;const S=j(m);S.length>=r-1&&x.push(...S.filter(D=>a[D]===o||a[D]===l))}c++}return a},De=(t,s)=>{const r=t.length;if(r===0)return 0;const n=[...new Set(s)].filter(c=>c!==-1);if(n.length<=1)return 0;let o=0,a=0;for(let c=0;c<r;c++){if(s[c]===-1)continue;const j=s[c];let u=0,y=0;for(let h=0;h<r;h++)c!==h&&s[h]===j&&(u+=P(t[c],t[h]),y++);u=y>0?u/y:0;let x=1/0;for(const h of n){if(h===j)continue;let m=0,S=0;for(let D=0;D<r;D++)s[D]===h&&(m+=P(t[c],t[D]),S++);S>0&&(x=Math.min(x,m/S))}y===0||x===1/0?o+=0:o+=(x-u)/Math.max(u,x),a++}return a>0?o/a:0},at=(t,s,r)=>{const n=[...new Set(s)].filter(u=>u!==-1),l=n.length;if(l<=1)return 0;const o=n.map(u=>r[u]),a=new Array(l).fill(0),c=new Array(l).fill(0);for(let u=0;u<t.length;u++){const y=s[u];if(y===-1)continue;const x=n.indexOf(y);x!==-1&&(a[x]+=P(t[u],o[x]),c[x]++)}for(let u=0;u<l;u++)a[u]=c[u]>0?a[u]/c[u]:0;let j=0;for(let u=0;u<l;u++){let y=0;for(let x=0;x<l;x++){if(u===x)continue;const h=P(o[u],o[x]);if(h>0){const m=(a[u]+a[x])/h;y=Math.max(y,m)}}j+=y}return l>0?j/l:0},lt=t=>{if(!Se||t.length===0)return t;const s=t.length,r=t[0].length,n=Array(s).fill(0).map(()=>Array(r).fill(0)),l=new Array(r).fill(0),o=new Array(r).fill(0);for(let a=0;a<r;a++){for(let c=0;c<s;c++)l[a]+=t[c][a];l[a]/=s;for(let c=0;c<s;c++)o[a]+=Math.pow(t[c][a]-l[a],2);o[a]=Math.sqrt(o[a]/s),o[a]===0&&(o[a]=1)}for(let a=0;a<s;a++)for(let c=0;c<r;c++)n[a][c]=(t[a][c]-l[c])/o[c];return n},it=()=>{if(!w||q.length<1){L("Please select at least 1 variable for clustering (2 for scatter plot).");return}de(!0),L(null),O(null),setTimeout(()=>{try{const t=q.map(d=>w.columns.find(g=>g.id===d)).filter(Boolean),s=[],r=[];if(w.data.forEach((d,g)=>{const v=t.map(C=>d[C.name]);v.every(C=>typeof C=="number"&&!isNaN(C))&&(s.push(v),r.push(g))}),s.length<Math.max($,5))throw new Error(`Insufficient data for clustering. Need at least ${Math.max($,5)} valid data points.`);const n=lt(s);let l,o,a,c=$;if(M==="kmeans"){if(s.length<$)throw new Error(`Not enough data points (${s.length}) for ${$} clusters in K-Means.`);const d=we(n,$);l=d.clusters,o=d.centroids,a=d.inertia}else if(M==="hierarchical"){if(s.length<$)throw new Error(`Not enough data points (${s.length}) for ${$} clusters in Hierarchical.`);l=st(n,$)}else l=rt(n,oe,ce),c=[...new Set(l.filter(d=>d!==-1))].length,c===0&&l.some(d=>d===-1)&&L("DBSCAN resulted in all points being classified as noise. Try adjusting Epsilon or Min Samples.");if(!o||M!=="kmeans"){const d=[...new Set(l.filter(v=>v!==-1))].sort((v,C)=>v-C);c=d.length,o=new Array(c).fill(0).map(()=>new Array(n[0].length).fill(0));const g=new Array(c).fill(0);c>0&&(n.forEach((v,C)=>{const _=l[C];if(_!==-1){const I=d.indexOf(_);I!==-1&&(v.forEach((F,k)=>o[I][k]+=F),g[I]++)}}),o.forEach((v,C)=>{g[C]>0&&v.forEach((_,I)=>v[I]/=g[C])}))}const j=c>1?De(n,l):0,u=c>1&&o&&o.length>0?at(n,l,o):0,y={};l.forEach(d=>{d!==-1&&(y[d]=(y[d]||0)+1)});const x={},h=[...new Set(l.filter(d=>d!==-1))].sort((d,g)=>d-g);h.forEach(d=>{const v=l.map((I,F)=>I===d?r[F]:-1).filter(I=>I!==-1).map(I=>{const F=r.indexOf(I);return s[F]});if(v.length===0)return;const C={},_={};t.forEach((I,F)=>{const k=v.map(R=>R[F]);C[I.name]=k.reduce((R,T)=>R+T,0)/k.length,_[I.name]=Math.sqrt(k.reduce((R,T)=>R+Math.pow(T-C[I.name],2),0)/k.length)||0}),x[d]={means:C,stds:_,size:y[d],percentage:y[d]/s.length*100}});const m=n.map((d,g)=>({x:d[0],y:d.length>1?d[1]:0,cluster:l[g],label:ee&&w.columns.find(v=>v.id===ee)?String(w.data[r[g]][w.columns.find(v=>v.id===ee).name]):`Point ${r[g]}`,index:r[g]}));let S;if(M==="kmeans"&&A.showElbowMethod&&s.length>1){S=[];const d=Math.min(10,s.length-1,n.length-1);if(d>=1)for(let g=1;g<=d&&!(n.length<g);g++){const v=we(n,g);S.push({k:g,inertia:v.inertia,silhouette:g>1?De(n,v.clusters):0})}}const D=[];A.showSilhouetteAnalysis&&c>1&&(l.forEach((d,g)=>{if(d!==-1){const v=n[g];let C=0,_=0,I=1/0;for(let k=0;k<n.length;k++)g!==k&&l[k]===d&&(C+=P(v,n[k]),_++);C=_>0?C/_:0;for(const k of h){if(k===d)continue;let R=0,T=0;for(let te=0;te<n.length;te++)l[te]===k&&(R+=P(v,n[te]),T++);T>0&&(I=Math.min(I,R/T))}const F=_===0||I===1/0?0:(I-C)/Math.max(C,I);D.push({index:g,originalIndex:r[g],cluster:d,silhouette:F,label:`C${d}`})}}),D.sort((d,g)=>d.cluster!==g.cluster?d.cluster-g.cluster:g.silhouette-d.silhouette));const N={method:M,clusters:l,centroids:o,numClusters:c,silhouetteScore:j,daviesBouldinScore:u,inertia:a,clusterSizes:y,clusterProfiles:x,scatterData:m,elbowData:S,silhouetteData:D,dendrogramData:void 0,variables:t,normalizedData:n,originalData:s,parameters:{distance:ne,linkage:M==="hierarchical"?U:void 0,epsilon:M==="dbscan"?oe:void 0,minSamples:M==="dbscan"?ce:void 0}};O(N),localStorage.setItem("cluster_analysis_results",JSON.stringify(N)),de(!1)}catch(t){L(`Error in cluster analysis: ${t instanceof Error?t.message:String(t)}`),de(!1)}},50)},nt=()=>{if(!w||q.length<1){L("Please select at least 1 variable for clustering.");return}if(M!=="kmeans"){L("Optimal cluster search is primarily designed for K-Means using the Elbow method.");return}if(Ce(!0),i!=null&&i.elbowData&&i.elbowData.length>1){const t=i.elbowData;let s=$;const r=t.map(l=>l.inertia);if(r.length>2){let l=-1/0;for(let o=1;o<r.length-1;o++){const a=r[o-1]-r[o],c=r[o]-r[o+1],j=a-c;j>l&&(l=j,s=t[o].k)}}else t.length>0&&(s=t[0].k);const n=t.filter(l=>l.k>1).map(l=>({k:l.k,score:l.silhouette}));if(n.length>0){const l=n.reduce((o,a)=>a.score>o.score?a:o,n[0]).k;L(`Suggested optimal K (Elbow): ${s}. Max Silhouette K: ${l}.`),ie(s)}else L(`Suggested optimal K (Elbow): ${s}.`),ie(s)}else L('Run analysis with "Show Elbow Method" enabled to find optimal K.');Ce(!1)},ot=()=>{if(!i)return"";const{method:t,numClusters:s,silhouetteScore:r,daviesBouldinScore:n,clusterSizes:l,clusterProfiles:o}=i;let a=`A ${t} clustering analysis was performed on ${i.originalData.length} data points using ${i.variables.length} variable(s): ${i.variables.map(c=>c.name).join(", ")}. `;if(t==="dbscan"){const c=i.clusters.filter(j=>j===-1).length;a+=`The algorithm identified ${s} clusters and ${c} noise points. `}else a+=`The data was partitioned into ${s} clusters. `;return a+=`

Validation Metrics:
- Silhouette Score: ${r.toFixed(3)} `,r>.7?a+=`(Strong structure)
`:r>.5?a+=`(Reasonable structure)
`:r>.25?a+=`(Weak structure)
`:a+=`(No substantial structure)
`,a+=`- Davies-Bouldin Score: ${n.toFixed(3)} (Lower is better)
`,a+=`
Cluster Distribution:
`,Object.entries(l).forEach(([c,j])=>{const u=(j/i.originalData.length*100).toFixed(1);a+=`- Cluster ${parseInt(c)+1}: ${j} points (${u}%)
`}),a+=`
Cluster Profiles (mean values for original data):
`,Object.entries(o).forEach(([c,j])=>{a+=`
Cluster ${parseInt(c)+1}:
`,Object.entries(j.means).forEach(([u,y])=>{a+=`  - ${u}: ${y.toFixed(2)} (Std: ${j.stds[u].toFixed(2)})
`})}),a},ct=()=>{if(!i||!w)return;const t=[...w.columns.map(a=>a.name),"Cluster_Assignment"],s=new Array(w.data.length).fill("N/A");i.scatterData.forEach(a=>{s[a.index]=a.cluster===-1?"Noise":`Cluster_${a.cluster+1}`});const r=w.data.map((a,c)=>[...Object.values(a),s[c]]),n=[t,...r].map(a=>a.join(",")).join(`
`),l=new Blob([n],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a");if(o.download!==void 0){const a=URL.createObjectURL(l);o.setAttribute("href",a),o.setAttribute("download",`${w.name}_clusters.csv`),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(a)}},z=[f.palette.primary.main,f.palette.secondary.main,f.palette.error.main,f.palette.warning.main,f.palette.success.main,f.palette.info.main,"#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFA07A","#20B2AA"],Ie=f.palette.grey[500],H={autosize:!0,paper_bgcolor:f.palette.background.paper,plot_bgcolor:f.palette.mode==="dark"?f.palette.grey[800]:f.palette.grey[200],font:{color:f.palette.text.primary},legend:{bgcolor:f.palette.background.default,bordercolor:f.palette.divider,font:{color:f.palette.text.secondary}},xaxis:{gridcolor:f.palette.divider,linecolor:f.palette.divider,zerolinecolor:f.palette.divider,tickfont:{color:f.palette.text.secondary}},yaxis:{gridcolor:f.palette.divider,linecolor:f.palette.divider,zerolinecolor:f.palette.divider,tickfont:{color:f.palette.text.secondary}}};return e.jsxs(B,{p:3,children:[e.jsx(p,{variant:"h5",gutterBottom:!0,children:"Cluster Analysis"}),e.jsxs(se,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(p,{variant:"subtitle1",gutterBottom:!0,children:"Data Selection"}),e.jsxs(b,{container:!0,spacing:2,children:[e.jsx(b,{item:!0,xs:12,md:4,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{id:"dataset-select-label",children:"Dataset"}),e.jsx(Q,{labelId:"dataset-select-label",value:_e,label:"Dataset",onChange:qe,disabled:Z.length===0,children:Z.length===0?e.jsx(V,{value:"",disabled:!0,children:"No datasets available"}):Z.map(t=>e.jsxs(V,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]})}),e.jsx(b,{item:!0,xs:12,md:4,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{id:"variables-label",children:"Variables for Clustering"}),e.jsx(Q,{labelId:"variables-label",multiple:!0,value:q,label:"Variables for Clustering",onChange:Ke,disabled:!w,renderValue:t=>t.map(s=>{var r;return(r=me.find(n=>n.id===s))==null?void 0:r.name}).filter(Boolean).join(", "),children:me.length===0?e.jsx(V,{value:"",disabled:!0,children:"No numeric variables"}):me.map(t=>e.jsx(V,{value:t.id,children:t.name},t.id))}),e.jsx(p,{variant:"caption",color:"text.secondary",children:"Select at least 1 numeric variable (2 for scatter plot)"})]})}),e.jsx(b,{item:!0,xs:12,md:4,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{id:"label-variable-label",children:"Label Variable (Optional)"}),e.jsxs(Q,{labelId:"label-variable-label",value:ee,label:"Label Variable (Optional)",onChange:We,disabled:!w,children:[e.jsx(V,{value:"",children:"None"}),Te.map(t=>e.jsx(V,{value:t.id,children:t.name},t.id))]})]})})]}),e.jsxs(dt,{sx:{mt:2},defaultExpanded:!0,children:[e.jsx(ht,{expandIcon:e.jsx(mt,{}),children:e.jsx(p,{children:"Clustering Configuration"})}),e.jsx(xt,{children:e.jsxs(b,{container:!0,spacing:2,children:[e.jsxs(b,{item:!0,xs:12,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Clustering Method"}),e.jsx(pt,{row:!0,value:M,onChange:Ue,children:["kmeans","hierarchical","dbscan"].map(t=>e.jsx(xe,{value:t,control:e.jsx(jt,{}),label:e.jsxs(B,{display:"flex",alignItems:"center",children:[t.toUpperCase(),e.jsx(gt,{title:t==="kmeans"?"Partitions data into K spherical clusters":t==="hierarchical"?"Builds a hierarchy of clusters":"Density-based, finds arbitrarily shaped clusters",children:e.jsx(ft,{size:"small",children:e.jsx(bt,{fontSize:"small"})})})]})},t))})]}),(M==="kmeans"||M==="hierarchical")&&e.jsxs(b,{item:!0,xs:12,md:6,children:[e.jsxs(p,{gutterBottom:!0,children:["Number of Clusters: ",$]}),e.jsxs(B,{display:"flex",alignItems:"center",gap:2,children:[e.jsx(yt,{value:$,onChange:He,min:2,max:Math.max(2,Math.min(15,Math.floor(((w==null?void 0:w.data.length)||0)/3))),step:1,marks:!0,valueLabelDisplay:"auto"}),M==="kmeans"&&e.jsx(pe,{size:"small",variant:"outlined",onClick:nt,disabled:Ve||!(i!=null&&i.elbowData),children:"Find Optimal K"})]})]}),e.jsx(b,{item:!0,xs:12,md:6,children:e.jsxs(G,{fullWidth:!0,children:[e.jsx(J,{id:"distance-metric-label",children:"Distance Metric"}),e.jsx(Q,{labelId:"distance-metric-label",value:ne,label:"Distance Metric",onChange:Ge,children:["euclidean","manhattan","cosine"].map(t=>e.jsx(V,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})}),M==="hierarchical"&&e.jsx(b,{item:!0,xs:12,md:6,children:e.jsxs(G,{fullWidth:!0,children:[e.jsx(J,{id:"linkage-method-label",children:"Linkage Method"}),e.jsx(Q,{labelId:"linkage-method-label",value:U,label:"Linkage Method",onChange:Je,children:["single","complete","average","ward"].map(t=>e.jsx(V,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))}),e.jsx(p,{variant:"caption",color:"text.secondary",children:"Note: 'Ward' linkage is complex and might be approximated if not fully supported by the simplified algorithm."})]})}),M==="dbscan"&&e.jsxs(e.Fragment,{children:[e.jsx(b,{item:!0,xs:12,md:6,children:e.jsx(Ee,{label:"Epsilon (ε)",type:"number",value:oe,onChange:Qe,fullWidth:!0,inputProps:{min:.01,step:.01},helperText:"Max distance for neighborhood"})}),e.jsx(b,{item:!0,xs:12,md:6,children:e.jsx(Ee,{label:"Min Samples",type:"number",value:ce,onChange:Xe,fullWidth:!0,inputProps:{min:1,step:1},helperText:"Min points for core point"})})]}),e.jsx(b,{item:!0,xs:12,children:e.jsx(xe,{control:e.jsx(Ae,{checked:Se,onChange:t=>{Ne(t.target.checked),O(null),localStorage.removeItem("cluster_analysis_results")}}),label:"Standardize variables (recommended)"})})]})})]}),e.jsxs(B,{mt:2,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsx(b,{container:!0,spacing:1,children:Object.keys(A).map(t=>e.jsx(b,{item:!0,xs:6,sm:4,md:3,children:e.jsx(xe,{control:e.jsx(Ae,{checked:A[t],onChange:()=>Ye(t),disabled:t==="showDendrogram"&&M!=="hierarchical"||t==="showElbowMethod"&&M!=="kmeans"}),label:t.replace(/([A-Z])/g," $1").replace(/^./,s=>s.toUpperCase())})},t))})]}),e.jsxs(B,{mt:2,display:"flex",gap:2,children:[e.jsx(pe,{variant:"contained",color:"primary",startIcon:e.jsx(vt,{}),onClick:it,disabled:ue||q.length===0||q.length<2&&A.showScatterPlot,children:"Run Cluster Analysis"}),i&&e.jsx(pe,{variant:"outlined",startIcon:e.jsx(St,{}),onClick:ct,children:"Export Clusters"})]})]}),ue&&e.jsx(B,{display:"flex",justifyContent:"center",my:4,children:e.jsx(Ct,{})}),he&&e.jsx(ge,{severity:he.startsWith("Suggested optimal K")?"info":"error",sx:{mb:3},children:he}),i&&!ue&&e.jsxs(se,{elevation:2,sx:{p:2,mt:3},children:[e.jsx(p,{variant:"h6",gutterBottom:!0,children:"Clustering Results"}),e.jsxs(wt,{value:K,onChange:(t,s)=>Le(s),sx:{mb:2},indicatorColor:"primary",textColor:"primary",variant:"scrollable",scrollButtons:"auto",children:[e.jsx(X,{label:"Overview"}),e.jsx(X,{label:"Visualization"}),e.jsx(X,{label:"Cluster Profiles"}),e.jsx(X,{label:"Validation"}),e.jsx(X,{label:"Interpretation"})]}),K===0&&e.jsxs(b,{container:!0,spacing:3,children:[e.jsx(b,{item:!0,xs:12,md:6,children:e.jsx(re,{children:e.jsxs(ae,{children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"Clustering Summary"}),e.jsxs(B,{mb:2,children:[e.jsxs(p,{variant:"h4",children:[i.numClusters," Clusters"]}),e.jsxs(p,{variant:"body2",color:"text.secondary",children:["Using ",i.method," clustering"]})]}),e.jsx(fe,{sx:{my:1}}),e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Method:"})," ",i.method.toUpperCase()]}),e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Variables:"})," ",i.variables.map(t=>t.name).join(", ")]}),e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Distance:"})," ",i.parameters.distance]}),i.parameters.linkage&&e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Linkage:"})," ",i.parameters.linkage]}),i.parameters.epsilon!==void 0&&e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Epsilon:"})," ",i.parameters.epsilon]}),i.parameters.minSamples!==void 0&&e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Min Samples:"})," ",i.parameters.minSamples]})]})})}),A.showValidationMetrics&&e.jsx(b,{item:!0,xs:12,md:6,children:e.jsx(re,{children:e.jsxs(ae,{children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"Validation Metrics"}),e.jsxs(B,{mb:2,children:[e.jsx(p,{variant:"h4",children:i.silhouetteScore.toFixed(3)}),e.jsx(p,{variant:"body2",color:"text.secondary",children:"Silhouette Score"})]}),e.jsx(fe,{sx:{my:1}}),e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Davies-Bouldin:"})," ",i.daviesBouldinScore.toFixed(3)]}),i.inertia!==void 0&&e.jsxs(p,{variant:"body2",children:[e.jsx("strong",{children:"Inertia:"})," ",i.inertia.toFixed(2)]}),i.silhouetteScore>.7?e.jsx(le,{icon:e.jsx(Dt,{}),label:"Strong Clustering",color:"success",size:"small",sx:{mt:1}}):i.silhouetteScore>.5?e.jsx(le,{label:"Reasonable Clustering",color:"primary",size:"small",sx:{mt:1}}):e.jsx(le,{icon:e.jsx(It,{}),label:"Weak Clustering",color:"warning",size:"small",sx:{mt:1}})]})})}),A.showClusterSizes&&i.clusterSizes&&Object.keys(i.clusterSizes).length>0&&e.jsxs(b,{item:!0,xs:12,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Cluster Distribution"}),e.jsx(Y,{data:[{values:Object.values(i.clusterSizes),labels:Object.keys(i.clusterSizes).map(t=>`Cluster ${parseInt(t)+1}`),type:"pie",marker:{colors:Object.keys(i.clusterSizes).map(t=>z[parseInt(t)%z.length])},hoverinfo:"label+percent+name",textinfo:"label+percent",automargin:!0}],layout:{...H,title:{text:"Cluster Sizes"},height:350,showlegend:!1},style:{width:"100%",height:"350px"},useResizeHandler:!0})]})]}),K===1&&e.jsxs(b,{container:!0,spacing:3,children:[A.showScatterPlot&&i.scatterData&&i.variables.length>=2&&e.jsxs(b,{item:!0,xs:12,md:i.method==="kmeans"&&A.showElbowMethod?6:12,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Cluster Scatter Plot (Normalized Data)"}),e.jsx(Y,{data:(()=>{const t=[];return[...new Set(i.scatterData.map(r=>r.cluster))].sort((r,n)=>r-n).forEach(r=>{const n=i.scatterData.filter(l=>l.cluster===r);t.push({x:n.map(l=>l.x),y:n.map(l=>l.y),text:n.map(l=>l.label),mode:"markers",type:"scatter",name:r===-1?"Noise":`Cluster ${r+1}`,marker:{color:r===-1?Ie:z[r%z.length],size:8,opacity:.8}})}),t})(),layout:{...H,title:{text:"Clusters"},xaxis:{title:{text:((Me=i.variables[0])==null?void 0:Me.name)||"Variable 1"}},yaxis:{title:{text:((ke=i.variables[1])==null?void 0:ke.name)||"Variable 2"}},height:450,hovermode:"closest"},style:{width:"100%",height:"450px"},useResizeHandler:!0})]}),A.showScatterPlot&&i.variables.length<2&&e.jsx(b,{item:!0,xs:12,children:e.jsx(ge,{severity:"info",children:"Scatter plot requires at least 2 variables for clustering."})}),i.method==="kmeans"&&A.showElbowMethod&&i.elbowData&&e.jsxs(b,{item:!0,xs:12,md:A.showScatterPlot&&i.variables.length>=2?6:12,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Elbow Method & Silhouette Score by K"}),e.jsx(Y,{data:[{x:i.elbowData.map(t=>t.k),y:i.elbowData.map(t=>t.inertia),type:"scatter",mode:"lines+markers",name:"Inertia (SSE)",line:{color:f.palette.primary.main}},{x:i.elbowData.map(t=>t.k),y:i.elbowData.map(t=>t.silhouette),type:"scatter",mode:"lines+markers",name:"Silhouette Score",yaxis:"y2",line:{color:f.palette.secondary.main}}],layout:{...H,title:{text:"Elbow Method"},xaxis:{title:{text:"Number of Clusters (K)"}},yaxis:{title:{text:"Inertia (SSE)",font:{color:f.palette.primary.main}},tickfont:{color:f.palette.primary.main}},yaxis2:{title:{text:"Silhouette Score",font:{color:f.palette.secondary.main}},overlaying:"y",side:"right",tickfont:{color:f.palette.secondary.main}},height:450,legend:{yanchor:"top",y:.99,xanchor:"right",x:.99}},style:{width:"100%",height:"450px"},useResizeHandler:!0})]}),A.showSilhouetteAnalysis&&i.silhouetteData&&i.silhouetteData.length>0&&e.jsxs(b,{item:!0,xs:12,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Silhouette Analysis per Sample"}),e.jsx(Y,{data:[{x:i.silhouetteData.map((t,s)=>s),y:i.silhouetteData.map(t=>t.silhouette),text:i.silhouetteData.map(t=>`Original Index: ${t.originalIndex}<br>Cluster: ${t.cluster===-1?"Noise":t.cluster+1}<br>Silhouette: ${t.silhouette.toFixed(3)}`),hoverinfo:"text",type:"bar",marker:{color:i.silhouetteData.map(t=>t.cluster===-1?Ie:z[t.cluster%z.length]),line:{width:0}},width:1}],layout:{...H,title:{text:"Silhouette Coefficients"},xaxis:{title:{text:"Samples (Sorted by Cluster, then Silhouette Score)"},showticklabels:!1,zeroline:!1},yaxis:{title:{text:"Silhouette Coefficient"},range:[-1,1],zerolinecolor:f.palette.text.secondary},height:450,bargap:.05,shapes:[{type:"line",x0:0,y0:i.silhouetteScore,x1:1,y1:i.silhouetteScore,xref:"paper",line:{color:f.palette.error.main,width:2,dash:"dash"}},...(()=>{const t=[];let s=-100;return i.silhouetteData.forEach((r,n)=>{r.cluster!==s&&n>0&&t.push({type:"line",x0:n-.5,y0:-1,x1:n-.5,y1:1,line:{color:f.palette.divider,width:1,dash:"dot"}}),s=r.cluster}),t})()],annotations:[{x:.98,y:i.silhouetteScore,xref:"paper",yref:"y",text:`Avg: ${i.silhouetteScore.toFixed(3)}`,showarrow:!1,font:{color:f.palette.error.main},bgcolor:f.palette.background.paper,opacity:.8}]},style:{width:"100%",height:"450px"},useResizeHandler:!0})]}),M==="hierarchical"&&A.showDendrogram&&e.jsx(b,{item:!0,xs:12,children:e.jsx(ge,{severity:"info",children:"Dendrogram visualization is complex. The current simplified hierarchical clustering provides assignments but not full dendrogram data. Consider specialized libraries for detailed dendrograms."})})]}),K===2&&A.showClusterProfiles&&e.jsxs(b,{container:!0,spacing:3,children:[Object.entries(i.clusterProfiles).sort(([t],[s])=>parseInt(t)-parseInt(s)).map(([t,s])=>e.jsx(b,{item:!0,xs:12,md:6,lg:4,children:e.jsx(re,{children:e.jsxs(ae,{children:[e.jsxs(B,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2,children:[e.jsxs(p,{variant:"h6",children:["Cluster ",parseInt(t)+1]}),e.jsx(le,{label:`${s.size} points (${s.percentage.toFixed(1)}%)`,sx:{backgroundColor:z[parseInt(t)%z.length],color:f.palette.getContrastText(z[parseInt(t)%z.length])},size:"small"})]}),e.jsx(Mt,{component:se,variant:"outlined",sx:{maxHeight:300},children:e.jsxs(kt,{size:"small",stickyHeader:!0,children:[e.jsx(Et,{children:e.jsxs(ze,{children:[e.jsx(W,{children:"Variable"}),e.jsx(W,{align:"right",children:"Mean"}),e.jsx(W,{align:"right",children:"Std Dev"})]})}),e.jsx(At,{children:Object.entries(s.means).map(([r,n])=>e.jsxs(ze,{hover:!0,children:[e.jsx(W,{children:r}),e.jsx(W,{align:"right",children:n.toFixed(3)}),e.jsx(W,{align:"right",children:s.stds[r].toFixed(3)})]},r))})]})})]})})},t)),Object.keys(i.clusterProfiles).length>0&&i.variables.length>0&&e.jsxs(b,{item:!0,xs:12,children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,children:"Cluster Comparison (Mean Normalized Values if Standardized)"}),e.jsx(Y,{data:(()=>{const t=[],s=i.variables.map(n=>n.name);let r=[];return Object.values(i.clusterProfiles).forEach(n=>{s.forEach(l=>{r.push(n.means[l])})}),Object.entries(i.clusterProfiles).sort(([n],[l])=>parseInt(n)-parseInt(l)).forEach(([n,l],o)=>{const a=parseInt(n);t.push({type:"scatterpolar",r:s.map(c=>l.means[c]),theta:s,fill:"toself",name:`Cluster ${a+1}`,marker:{color:z[a%z.length]},fillcolor:`${z[a%z.length]}66`})}),t})(),layout:{...H,title:{text:"Mean Variable Values by Cluster"},polar:{radialaxis:{visible:!0,side:"counterclockwise"},angularaxis:{tickfont:{size:10}}},height:500,legend:{orientation:"h",yanchor:"bottom",y:1.02,xanchor:"right",x:1}},style:{width:"100%",height:"500px"},useResizeHandler:!0})]})]}),K===3&&e.jsx(b,{container:!0,spacing:3,children:e.jsx(b,{item:!0,xs:12,children:e.jsx(re,{children:e.jsxs(ae,{children:[e.jsx(p,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"Validation Summary"}),e.jsxs(b,{container:!0,spacing:2,children:[e.jsx(b,{item:!0,xs:12,sm:4,children:e.jsxs(B,{textAlign:"center",p:2,children:[e.jsx(p,{variant:"h3",color:"primary",children:i.silhouetteScore.toFixed(3)}),e.jsx(p,{variant:"body2",color:"text.secondary",children:"Silhouette Score"}),e.jsx(p,{variant:"caption",children:"Similarity to own cluster vs others. Range [-1, 1], higher is better."})]})}),e.jsx(b,{item:!0,xs:12,sm:4,children:e.jsxs(B,{textAlign:"center",p:2,children:[e.jsx(p,{variant:"h3",color:"secondary",children:i.daviesBouldinScore.toFixed(3)}),e.jsx(p,{variant:"body2",color:"text.secondary",children:"Davies-Bouldin Score"}),e.jsx(p,{variant:"caption",children:"Ratio of within-cluster scatter to between-cluster separation. Lower is better (min 0)."})]})}),i.inertia!==void 0&&e.jsx(b,{item:!0,xs:12,sm:4,children:e.jsxs(B,{textAlign:"center",p:2,children:[e.jsx(p,{variant:"h3",color:"error",children:i.inertia.toFixed(0)}),e.jsx(p,{variant:"body2",color:"text.secondary",children:"Inertia (SSE)"}),e.jsx(p,{variant:"caption",children:"Sum of squared distances to closest centroid (K-Means). Lower is better."})]})})]}),e.jsx(fe,{sx:{my:2}}),e.jsx(p,{variant:"body1",paragraph:!0,children:e.jsx("strong",{children:"Quality Assessment:"})}),e.jsxs(B,{pl:2,children:[e.jsxs(p,{variant:"body2",paragraph:!0,children:["• Silhouette: ",i.silhouetteScore>.7?"Excellent":i.silhouetteScore>.5?"Good":i.silhouetteScore>.25?"Fair":"Poor"]}),e.jsxs(p,{variant:"body2",paragraph:!0,children:["• Davies-Bouldin: ",i.daviesBouldinScore<.5?"Excellent":i.daviesBouldinScore<1?"Good":"Fair/Poor"]})]})]})})})}),K===4&&e.jsxs(B,{mt:2,children:[e.jsx(p,{variant:"h6",gutterBottom:!0,children:"Automated Interpretation"}),e.jsx(se,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.default",whiteSpace:"pre-line"},children:e.jsx(p,{variant:"body2",children:ot()})})]})]})]})};export{Vt as default};
