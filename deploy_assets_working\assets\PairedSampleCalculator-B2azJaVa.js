import{u as J,j as e,B as i,bL as Q,bM as F,G as s,R as G,e as a,bN as q,aE as b,I as S,bs as M,ah as z,f as U,bH as V,bO as ee,bP as te,k as ae,i as re,l as se}from"./mui-libs-CfwFIaTD.js";import{r as l}from"./react-libs-Cr2nE3UY.js";import{j as A}from"./other-utils-CR9xr_gI.js";import{g as R}from"./sampleSize-nX7GCAZC.js";import{R as oe,d as ie,C as ne,X as le,Y as ce,T as de,e as pe}from"./charts-recharts-d3-BEF1Y_jn.js";const je=()=>{const c=J(),[d,E]=l.useState("proportion"),[p,$]=l.useState(.95),[x,k]=l.useState(.8),[h,C]=l.useState(.2),[u,T]=l.useState(.1),[f,B]=l.useState(5),[j,I]=l.useState(10),[n,W]=l.useState(null),[L,N]=l.useState([]);l.useEffect(()=>{_(),H()},[p,x,h,u,f,j,d]);const _=()=>{const t=R(1-(1-p)/2),r=R(x);let m;if(d==="proportion"){const v=h+u,o=h-u;if(o===0)m=1/0;else{const g=Math.pow(t*Math.sqrt(v)+r*Math.sqrt(v-Math.pow(o,2)),2),P=Math.pow(o,2);m=Math.ceil(g/P)}}else if(f===0)m=1/0;else{const v=Math.pow(t+r,2)*Math.pow(j,2),o=Math.pow(f,2);m=Math.ceil(v/o)}W(m)},H=()=>{const t=[],r=Math.max(10,Math.floor(n?n*.5:20)),m=Math.ceil(n?n*1.5:100),v=Math.max(1,Math.floor((m-r)/20));for(let o=r;o<=m;o+=v){let g;const P=R(1-(1-p)/2);if(d==="proportion"){const w=h+u,y=h-u;if(w===0)g=0;else{const D=Math.sqrt((w-Math.pow(y,2))/o),Z=(Math.abs(y)-P*Math.sqrt(w/o))/D;g=A.normal.cdf(Z,0,1)}}else{const w=Math.abs(f);if(j===0)g=1;else{const y=j/Math.sqrt(o),D=(w-P*y)/y;g=A.normal.cdf(D,0,1)}}t.push({sampleSize:o,power:Math.max(0,Math.min(1,g))})}N(t)},K=(t,r)=>{r!==null&&(E(r),r==="proportion"?(C(.2),T(.1)):r==="mean"&&(B(5),I(10)))},X=()=>{if(n){let t="";d==="proportion"?t=`Required Sample Size: ${n} pairs for comparing two matched proportions (p01=${h}, p10=${u}) with ${Math.round(x*100)}% power at a ${Math.round(p*100)}% confidence level.`:t=`Required Sample Size: ${n} pairs for comparing two paired means (Delta=${f}, Sigma_d=${j}) with ${Math.round(x*100)}% power at a ${Math.round(p*100)}% confidence level.`,navigator.clipboard.writeText(t)}},Y=()=>{console.log("Export PDF functionality would go here")},O=()=>{$(.95),k(.8),C(.2),T(.1),B(5),I(10)};return e.jsxs(e.Fragment,{children:[e.jsx(i,{sx:{mb:3},children:e.jsxs(Q,{value:d,exclusive:!0,onChange:K,"aria-label":"paired sample calculator type",fullWidth:!0,children:[e.jsx(F,{value:"proportion","aria-label":"two matched proportions",sx:{"&.Mui-selected":{bgcolor:c.palette.primary.main,color:c.palette.primary.contrastText,"&:hover":{bgcolor:c.palette.primary.dark}}},children:"Two Matched Proportions"}),e.jsx(F,{value:"mean","aria-label":"two paired means",sx:{"&.Mui-selected":{bgcolor:c.palette.secondary.main,color:c.palette.secondary.contrastText,"&:hover":{bgcolor:c.palette.secondary.dark}}},children:"Two Paired Means"})]})}),e.jsxs(s,{container:!0,spacing:3,children:[e.jsx(s,{item:!0,xs:12,md:6,children:e.jsxs(G,{elevation:1,sx:{p:3},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,children:"Input Parameters"}),e.jsxs(i,{sx:{mb:4},children:[e.jsx(a,{gutterBottom:!0,children:"Confidence Level (α)"}),e.jsx(s,{container:!0,spacing:2,alignItems:"center",children:e.jsx(s,{item:!0,xs:12,children:e.jsx(q,{value:p*100,onChange:(t,r)=>$(r/100),step:1,min:80,max:99,marks:[{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"},{value:99,label:"99%"}]})})})]}),e.jsxs(i,{sx:{mb:4},children:[e.jsxs(a,{gutterBottom:!0,children:["Statistical Power (1-β)",e.jsx(b,{title:"The probability of correctly detecting a true difference between groups.",children:e.jsx(S,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsx(s,{container:!0,spacing:2,alignItems:"center",children:e.jsx(s,{item:!0,xs:12,children:e.jsx(q,{value:x*100,onChange:(t,r)=>k(r/100),step:5,min:70,max:95,marks:[{value:70,label:"70%"},{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"}]})})})]}),d==="proportion"&&e.jsxs(e.Fragment,{children:[e.jsxs(i,{sx:{mb:4},children:[e.jsxs(a,{gutterBottom:!0,children:["Proportion of (Group 1 Success, Group 2 Failure) - p₀₁",e.jsx(b,{title:"The expected proportion of discordant pairs where Group 1 has success and Group 2 has failure.",children:e.jsx(S,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsxs(s,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(s,{item:!0,xs:!0,children:e.jsx(z,{value:Math.round(h*100),onChange:t=>C(Number(t.target.value)/100),type:"number",inputProps:{min:0,max:100,step:1},fullWidth:!0})}),e.jsx(s,{item:!0,children:e.jsx(a,{children:"%"})})]})]}),e.jsxs(i,{sx:{mb:4},children:[e.jsxs(a,{gutterBottom:!0,children:["Proportion of (Group 1 Failure, Group 2 Success) - p₁₀",e.jsx(b,{title:"The expected proportion of discordant pairs where Group 1 has failure and Group 2 has success.",children:e.jsx(S,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsxs(s,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(s,{item:!0,xs:!0,children:e.jsx(z,{value:Math.round(u*100),onChange:t=>T(Number(t.target.value)/100),type:"number",inputProps:{min:0,max:100,step:1},fullWidth:!0})}),e.jsx(s,{item:!0,children:e.jsx(a,{children:"%"})})]})]})]}),d==="mean"&&e.jsxs(e.Fragment,{children:[e.jsxs(i,{sx:{mb:4},children:[e.jsxs(a,{gutterBottom:!0,children:["Expected Mean Difference (Δ)",e.jsx(b,{title:"The expected mean difference between paired observations.",children:e.jsx(S,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsx(z,{type:"number",value:f,onChange:t=>B(Number(t.target.value)),inputProps:{step:.1},fullWidth:!0})]}),e.jsxs(i,{sx:{mb:4},children:[e.jsxs(a,{gutterBottom:!0,children:["Standard Deviation of Differences (σd)",e.jsx(b,{title:"The standard deviation of the paired differences.",children:e.jsx(S,{size:"small",sx:{ml:1},children:e.jsx(M,{fontSize:"small"})})})]}),e.jsx(z,{type:"number",value:j,onChange:t=>I(Number(t.target.value)),inputProps:{min:.1,step:.1},fullWidth:!0})]})]}),e.jsx(U,{variant:"outlined",startIcon:e.jsx(V,{}),onClick:O,fullWidth:!0,sx:{mt:2},children:"Reset"})]})}),e.jsx(s,{item:!0,xs:12,md:6,children:e.jsxs(G,{elevation:1,sx:{p:3},children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(a,{variant:"h6",children:"Results"}),e.jsxs(i,{children:[e.jsx(b,{title:"Copy results",children:e.jsx(S,{onClick:X,children:e.jsx(ee,{})})}),e.jsx(b,{title:"Export as PDF",children:e.jsx(S,{onClick:Y,children:e.jsx(te,{})})})]})]}),e.jsx(ae,{sx:{mb:4,bgcolor:re(c.palette.primary.main,.1)},children:e.jsxs(se,{sx:{textAlign:"center"},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,children:"Required Sample Size"}),e.jsx(a,{variant:"h2",color:"primary",children:n}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"pairs"})]})}),e.jsx(a,{variant:"h6",gutterBottom:!0,children:"Power by Sample Size"}),e.jsx(i,{sx:{height:300,mb:2},children:e.jsx(oe,{width:"100%",height:"100%",children:e.jsxs(ie,{data:L,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(ne,{strokeDasharray:"3 3"}),e.jsx(le,{dataKey:"sampleSize",label:{value:"Sample Size (pairs)",position:"outerBottom",offset:15,style:{fontSize:"12px"}},height:70}),e.jsx(ce,{label:{value:"Statistical Power",angle:-90,position:"outside",offset:-60,style:{fontSize:"12px"}},tickFormatter:t=>`${Math.round(t*100)}%`,domain:[0,1],width:100}),e.jsx(de,{formatter:t=>[`${(t*100).toFixed(1)}%`,"Power"],labelFormatter:t=>`Sample Size: ${t} pairs`}),e.jsx(pe,{type:"monotone",dataKey:"power",stroke:c.palette.primary.main,activeDot:{r:8},strokeWidth:2})]})})}),e.jsxs(i,{sx:{mt:3},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,children:"Interpretation"}),d==="proportion"?e.jsxs(a,{variant:"body1",children:["A sample size of ",n," pairs is needed to detect a difference between matched proportions (p₀₁=",h," and p₁₀=",u,") with ",Math.round(x*100),"% power at a ",Math.round(p*100),"% confidence level."]}):e.jsxs(a,{variant:"body1",children:["A sample size of ",n," pairs is needed to detect a mean difference of ",f," (with a standard deviation of differences of ",j,") with ",Math.round(x*100),"% power at a ",Math.round(p*100),"% confidence level."]})]})]})})]})]})};export{je as default};
