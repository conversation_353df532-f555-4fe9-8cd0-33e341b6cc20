import{ck as g,j as e,aU as u,c5 as h,e as l,K as f,B as s,R as m}from"./mui-libs-CfwFIaTD.js";import{l as j}from"./index-Bpan7Tbe.js";var o={},v=g;Object.defineProperty(o,"__esModule",{value:!0});var x=o.default=void 0,y=v(j()),b=e;x=o.default=(0,y.default)((0,b.jsx)("path",{d:"M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"}),"NavigateNext");const P=u(m,{shouldForwardProp:a=>a!=="variant"})(({theme:a,variant:r})=>({padding:r==="compact"?a.spacing(2):a.spacing(3),marginBottom:a.spacing(3),borderRadius:a.spacing(1.5),...r==="transparent"&&{backgroundColor:"transparent",boxShadow:"none",padding:a.spacing(1,0,2,0)}})),I=({title:a,description:r,breadcrumbs:t,action:d,variant:c="default",icon:p})=>e.jsxs(P,{variant:c,children:[t&&t.length>0&&e.jsx(h,{separator:e.jsx(x,{fontSize:"small"}),"aria-label":"breadcrumb",sx:{mb:1.5,"& .MuiBreadcrumbs-ol":{flexWrap:"nowrap"}},children:t.map((n,i)=>i===t.length-1?e.jsx(l,{color:"text.primary",variant:"body2",children:n.label},i):e.jsx(f,{color:"inherit",href:n.href,variant:"body2",underline:"hover",sx:{display:"flex",alignItems:"center"},children:n.label},i))}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center"},children:[p&&e.jsx(s,{sx:{mr:1.5,display:"flex",color:"primary.main"},children:p}),e.jsx(l,{variant:"h5",component:"h1",fontWeight:"medium",children:a})]}),d&&e.jsx(s,{children:d})]}),r&&e.jsx(l,{variant:"body2",color:"text.secondary",sx:{mt:1,maxWidth:"800px",lineHeight:1.6},children:r})]});export{I as P};
