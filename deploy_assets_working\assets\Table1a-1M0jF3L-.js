import{j as e,B as v,e as I,R as A,g as W,G as P,ai as G,b9 as O,ba as F,bb as C,f as U,ae as X,ao as Z,ap as _,aq as ee,ar as V,as as d,at as ae,bR as te}from"./mui-libs-CfwFIaTD.js";import{r as u,b as L}from"./react-libs-Cr2nE3UY.js";import{a as se,D as M,g as le}from"./index-Bpan7Tbe.js";import{A as re}from"./AddToResultsButton-BwSXKCt2.js";import{P as ne}from"./PublicationReadyGate-BGFbKbJc.js";import{h as ie,i as oe}from"./descriptive-Djo0s6H4.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const fe=()=>{const{datasets:g,currentDataset:S,setCurrentDataset:H}=se(),[B,q]=u.useState((S==null?void 0:S.id)||""),[m,E]=u.useState([]),[y,k]=u.useState(!1),[x,o]=u.useState(null),[T,f]=u.useState(null),[h,b]=u.useState([]),[z,D]=u.useState(!1),[$,N]=u.useState(""),l=g.find(a=>a.id===B),p=(l==null?void 0:l.columns)||[],R=p.filter(a=>a.type===M.CATEGORICAL),Y=a=>{const s=a.target.value;q(s),E([]),f(null),o(null),b([]);const t=g.find(r=>r.id===s);t&&H(t)},J=a=>{const s=a.target.value,t=typeof s=="string"?s.split(","):s;if(E(t),f(null),o(null),b([]),l&&t.length>0){const r=p.filter(n=>t.includes(n.id));if(r.every(n=>n.type===M.CATEGORICAL)){const n=Array.from(new Set(l.data.map(c=>String(c[r[0].name]))));if(!r.every(c=>{const i=Array.from(new Set(l.data.map(w=>String(w[c.name]))));return i.length===n.length&&i.every(w=>n.includes(w))}))o("Please select only categorical variables that have the exact same categories."),b([]);else{o(null);const c=r[0],i=le(c.id,l);b(i)}}else o("Please select only categorical variables."),b([])}else o(null),b([])},K=()=>{if(!l||m.length===0||x){o(x||"Please select a dataset and at least one row variable to analyze."),f(null);return}k(!0),o(null);const a=[];p.filter(t=>m.includes(t.id)).forEach(t=>{const r=l.data.map(i=>String(i[t.name])),n=ie(r),j=oe(r),c={};h.forEach(i=>{c[i]={n:n[i]||0,percentage:(j[i]||0)*100}}),a.push({variableName:t.name,categoryData:c})}),f(a),k(!1)},Q=()=>{D(!1)};return e.jsx(ne,{children:e.jsxs(v,{p:3,children:[e.jsx(I,{variant:"h5",gutterBottom:!0,children:"Publication Ready Table 1a"}),e.jsxs(A,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(I,{variant:"subtitle1",gutterBottom:!0,children:"Select Data and Variables"}),e.jsx(W,{severity:"info",sx:{mb:2},children:"This table is applicable for several categorical variables that have the exact same categories (e.g., multiple variables measured on the same Likert scale or multiple Yes/No variables). Please select only such variables."}),e.jsxs(P,{container:!0,spacing:2,children:[e.jsx(P,{item:!0,xs:12,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(O,{id:"dataset-select-label",children:"Dataset"}),e.jsx(F,{labelId:"dataset-select-label",id:"dataset-select",value:B,label:"Dataset",onChange:Y,disabled:g.length===0,children:g.length===0?e.jsx(C,{value:"",disabled:!0,children:"No datasets available"}):g.map(a=>e.jsxs(C,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]})}),e.jsx(P,{item:!0,xs:12,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(O,{id:"row-variables-select-label",children:"Row Variables (Categorical with Same Categories)"}),e.jsx(F,{labelId:"row-variables-select-label",id:"row-variables-select",multiple:!0,value:m,onChange:J,label:"Row Variables (Categorical with Same Categories)",disabled:R.length===0||!l,renderValue:a=>a.map(s=>{var t;return((t=p.find(r=>r.id===s))==null?void 0:t.name)||""}).join(", "),children:R.length===0?e.jsx(C,{value:"",disabled:!0,children:"No categorical variables available"}):R.map(a=>e.jsx(C,{value:a.id,children:a.name},a.id))})]})})]}),e.jsx(v,{mt:2,children:e.jsx(U,{variant:"contained",color:"primary",onClick:K,disabled:y||!l||m.length===0||x!==null,children:"Generate Table 1a"})})]}),y&&e.jsx(v,{display:"flex",justifyContent:"center",my:4,children:e.jsx(X,{})}),x&&e.jsx(W,{severity:"error",sx:{mb:3},children:x}),T&&!y&&l&&m.length>0&&h.length>0&&e.jsxs(v,{children:[e.jsxs(A,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(I,{variant:"h6",gutterBottom:!0,children:"Table 1a. Descriptive Statistics for Categorical Variables with Same Categories"}),e.jsx(Z,{component:A,variant:"outlined",children:e.jsxs(_,{size:"small",children:[e.jsxs(ee,{children:[e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:"bold"},children:"Variable"}),h.map((a,s)=>e.jsx(d,{sx:{fontWeight:"bold"},align:"center",colSpan:2,children:a},s))]}),e.jsxs(V,{children:[e.jsx(d,{})," ",h.map((a,s)=>e.jsxs(L.Fragment,{children:[e.jsx(d,{sx:{fontWeight:"bold"},align:"center",children:"n"}),e.jsx(d,{sx:{fontWeight:"bold"},align:"center",children:"%"})]},s))]})]}),e.jsx(ae,{children:T.map((a,s)=>e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:"bold"},children:a.variableName}),h.map((t,r)=>{var n,j;return e.jsxs(L.Fragment,{children:[e.jsx(d,{align:"center",children:((n=a.categoryData[t])==null?void 0:n.n)!==void 0?a.categoryData[t].n:"N/A"}),e.jsx(d,{align:"center",children:((j=a.categoryData[t])==null?void 0:j.percentage)!==void 0?`${a.categoryData[t].percentage.toFixed(1)}%`:"N/A"})]},r)})]},s))})]})})]}),e.jsx(v,{sx:{display:"flex",justifyContent:"center",mt:2},children:e.jsx(re,{resultData:{title:`Table 1a - Advanced Descriptive Statistics (${l.name})`,type:"descriptive",component:"Table1a",data:{dataset:l.name,variables:m.map(a=>{var s;return((s=p.find(t=>t.id===a))==null?void 0:s.name)||a}),results:T,commonCategories:h,timestamp:new Date().toISOString(),totalSampleSize:l.data.length}},onSuccess:()=>{N("Results successfully added to Results Manager!"),D(!0)},onError:a=>{N(`Error adding results to Results Manager: ${a}`),D(!0)}})})]}),e.jsx(te,{open:z,autoHideDuration:4e3,onClose:Q,message:$})]})})};export{fe as default};
