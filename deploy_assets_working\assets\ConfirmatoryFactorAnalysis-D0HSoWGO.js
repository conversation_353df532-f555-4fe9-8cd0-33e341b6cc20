var zi=Object.defineProperty;var Ei=(M,c,f)=>c in M?zi(M,c,{enumerable:!0,configurable:!0,writable:!0,value:f}):M[c]=f;var me=(M,c,f)=>Ei(M,typeof c!="symbol"?c+"":c,f);import{u as Mi,j as e,B as x,e as o,g as $,bv as Ri,f as P,bH as ki,R as W,G as y,ai as fe,b9 as He,ba as Fe,bb as q,Q as Pi,ac as Je,k as ee,l as ie,ah as Ai,h as D,I as Ke,c9 as Qe,D as Ze,c6 as ei,c8 as _e,aj as F,bc as I,bN as Li,aN as Ti,ae as $i,a6 as Di,a7 as ae,bt as Bi,a2 as qi,aX as Oi,aA as Ni,aF as Vi,ao as te,ap as re,at as se,ar as g,as as r,bw as ii,aq as he,bW as Ie,bX as ze,aD as Ee,bY as Me,bS as Re,bT as ke,bU as Pe,L as ai,m as ti,r as ri,n as Ui,bV as Ae}from"./mui-libs-CfwFIaTD.js";import{r as p}from"./react-libs-Cr2nE3UY.js";import{a as Wi,D as Xi}from"./index-Bpan7Tbe.js";import{i as Yi}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./other-utils-CR9xr_gI.js";import"./charts-recharts-d3-BEF1Y_jn.js";class Gi{constructor(){me(this,"pyodide",null);me(this,"isInitialized",!1);me(this,"initializationPromise",null)}async initialize(){if(!this.isInitialized){if(this.initializationPromise)return this.initializationPromise;this.initializationPromise=this.doInitialize(),await this.initializationPromise}}async doInitialize(){try{if(typeof window<"u"&&!window.loadPyodide){const c=document.createElement("script");c.src="https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js",document.head.appendChild(c),await new Promise((f,C)=>{c.onload=f,c.onerror=C})}this.pyodide=await window.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.25.0/full/"}),await this.pyodide.loadPackage(["numpy","scipy"]),console.log("Pyodide initialized for Confirmatory Factor Analysis"),this.setupCFAImplementation(),this.isInitialized=!0}catch(c){throw console.error("Failed to initialize Pyodide:",c),new Error("Failed to initialize Python environment for confirmatory factor analysis")}}setupCFAImplementation(){this.pyodide.runPython(`
import numpy as np
import scipy.stats as stats
import scipy.linalg as linalg
from scipy.optimize import minimize
import json
import warnings
warnings.filterwarnings('ignore')

def create_model_matrices(model_spec, variable_names):
    """Create model matrices for CFA"""
    n_vars = len(variable_names)
    factors = model_spec['factors']
    n_factors = len(factors)
    
    # Create variable name to index mapping
    var_to_idx = {name: idx for idx, name in enumerate(variable_names)}
    
    # Factor loading matrix (Lambda)
    factor_loading_matrix = np.zeros((n_vars, n_factors))
    
    # Track which parameters are free
    free_loading_params = []
    
    for f_idx, factor in enumerate(factors):
        for indicator in factor['indicators']:
            if indicator in var_to_idx:
                var_idx = var_to_idx[indicator]
                factor_loading_matrix[var_idx, f_idx] = 1.0  # Start with 1, will be estimated
                free_loading_params.append((var_idx, f_idx))
    
    # Factor correlation matrix (Phi)
    factor_corr_matrix = np.eye(n_factors)
    free_factor_corr_params = []
    
    if model_spec['constraints']['allowFactorCorrelations'] and n_factors > 1:
        for i in range(n_factors):
            for j in range(i + 1, n_factors):
                free_factor_corr_params.append((i, j))
    
    # Error correlation matrix (Theta)
    error_corr_matrix = np.eye(n_vars)
    free_error_corr_params = []
    
    if model_spec['constraints']['allowCorrelatedErrors']:
        # For now, don't add any correlated errors by default
        # They would be added based on modification indices
        pass
    
    return {
        'factor_loading_matrix': factor_loading_matrix,
        'factor_corr_matrix': factor_corr_matrix,
        'error_corr_matrix': error_corr_matrix,
        'free_loading_params': free_loading_params,
        'free_factor_corr_params': free_factor_corr_params,
        'free_error_corr_params': free_error_corr_params,
        'n_vars': n_vars,
        'n_factors': n_factors
    }

def calculate_covariance_matrix(params, model_matrices):
    """Calculate implied covariance matrix from parameters"""
    # Unpack parameters
    param_idx = 0
    
    # Factor loadings
    lambda_matrix = model_matrices['factor_loading_matrix'].copy()
    for var_idx, factor_idx in model_matrices['free_loading_params']:
        lambda_matrix[var_idx, factor_idx] = params[param_idx]
        param_idx += 1
    
    # Factor correlations
    phi_matrix = model_matrices['factor_corr_matrix'].copy()
    for i, j in model_matrices['free_factor_corr_params']:
        phi_matrix[i, j] = phi_matrix[j, i] = params[param_idx]
        param_idx += 1
    
    # Error variances (diagonal of Theta)
    theta_matrix = np.eye(model_matrices['n_vars'])
    for i in range(model_matrices['n_vars']):
        theta_matrix[i, i] = params[param_idx]
        param_idx += 1
    
    # Error correlations
    for i, j in model_matrices['free_error_corr_params']:
        theta_matrix[i, j] = theta_matrix[j, i] = params[param_idx]
        param_idx += 1
    
    # Calculate implied covariance matrix: Sigma = Lambda * Phi * Lambda' + Theta
    sigma = lambda_matrix @ phi_matrix @ lambda_matrix.T + theta_matrix
    
    return sigma, lambda_matrix, phi_matrix, theta_matrix

def ml_fit_function(params, sample_cov, model_matrices, n_obs):
    """Maximum likelihood fit function"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
        
        # Ensure positive definiteness
        eigenvals = np.linalg.eigvals(sigma)
        if np.any(eigenvals <= 1e-10):
            return 1e10
        
        # ML fit function: log|Sigma| + tr(S * Sigma^-1) - log|S| - p
        log_det_sigma = np.linalg.slogdet(sigma)[1]
        inv_sigma = np.linalg.inv(sigma)
        trace_term = np.trace(sample_cov @ inv_sigma)
        
        fit = log_det_sigma + trace_term
        
        return fit
        
    except:
        return 1e10

def gls_fit_function(params, sample_cov, model_matrices, n_obs):
    """Generalized least squares fit function"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
        
        # GLS fit function: 0.5 * tr((S - Sigma) * S^-1)^2
        inv_sample_cov = np.linalg.inv(sample_cov)
        diff = sample_cov - sigma
        fit = 0.5 * np.trace((diff @ inv_sample_cov) @ (diff @ inv_sample_cov))
        
        return fit
        
    except:
        return 1e10

def wls_fit_function(params, sample_cov, model_matrices, n_obs):
    """Weighted least squares fit function"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
        
        # WLS fit function: (s - sigma)' * W * (s - sigma)
        # where s and sigma are vectorized lower triangular parts
        s_vec = sample_cov[np.tril_indices_from(sample_cov)]
        sigma_vec = sigma[np.tril_indices_from(sigma)]
        diff = s_vec - sigma_vec
        
        # Use identity weight matrix for simplicity
        W = np.eye(len(diff))
        fit = diff.T @ W @ diff
        
        return fit
        
    except:
        return 1e10

def estimate_cfa_model(sample_cov, model_matrices, estimation_method='ml', n_obs=100):
    """Estimate CFA model parameters"""
    # Initialize parameters
    initial_params = []
    
    # Factor loadings (start with 0.7)
    for _ in model_matrices['free_loading_params']:
        initial_params.append(0.7)
    
    # Factor correlations (start with 0.3)
    for _ in model_matrices['free_factor_corr_params']:
        initial_params.append(0.3)
    
    # Error variances (start with 0.5)
    for _ in range(model_matrices['n_vars']):
        initial_params.append(0.5)
    
    # Error correlations (start with 0.0)
    for _ in model_matrices['free_error_corr_params']:
        initial_params.append(0.0)
    
    # Parameter bounds
    bounds = []
    
    # Factor loadings bounds
    for _ in model_matrices['free_loading_params']:
        bounds.append((-5.0, 5.0))
    
    # Factor correlations bounds
    for _ in model_matrices['free_factor_corr_params']:
        bounds.append((-0.99, 0.99))
    
    # Error variances bounds (must be positive)
    for _ in range(model_matrices['n_vars']):
        bounds.append((0.001, 10.0))
    
    # Error correlations bounds
    for _ in model_matrices['free_error_corr_params']:
        bounds.append((-0.99, 0.99))
    
    # Choose fit function
    if estimation_method == 'gls':
        fit_func = gls_fit_function
    elif estimation_method == 'wls':
        fit_func = wls_fit_function
    else:  # ml or mlr
        fit_func = ml_fit_function
    
    # Optimize
    try:
        result = minimize(
            fit_func,
            initial_params,
            args=(sample_cov, model_matrices, n_obs),
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': 1000, 'ftol': 1e-9}
        )
        
        if result.success:
            return result.x, result.fun, True, result.nit
        else:
            return initial_params, 1e10, False, 0
            
    except:
        return initial_params, 1e10, False, 0

def calculate_fit_indices(sample_cov, fitted_params, model_matrices, n_obs, estimation_method='ml'):
    """Calculate model fit indices"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(fitted_params, model_matrices)
        n_vars = model_matrices['n_vars']
        n_params = len(fitted_params)
        df = int(n_vars * (n_vars + 1) / 2 - n_params)
        
        # Chi-square test statistic
        if estimation_method == 'ml':
            log_det_sigma = np.linalg.slogdet(sigma)[1]
            log_det_sample = np.linalg.slogdet(sample_cov)[1]
            inv_sigma = np.linalg.inv(sigma)
            trace_term = np.trace(sample_cov @ inv_sigma)
            chi_square = (n_obs - 1) * (log_det_sigma + trace_term - log_det_sample - n_vars)
        else:
            # For other methods, use a simpler approximation
            diff = sample_cov - sigma
            chi_square = (n_obs - 1) * np.trace(diff @ np.linalg.inv(sample_cov) @ diff)
        
        # p-value
        p_value = 1 - stats.chi2.cdf(chi_square, df) if df > 0 else 1.0
        
        # Baseline model (independence model)
        baseline_cov = np.diag(np.diag(sample_cov))
        if estimation_method == 'ml':
            log_det_baseline = np.linalg.slogdet(baseline_cov)[1]
            inv_baseline = np.linalg.inv(baseline_cov)
            baseline_chi = (n_obs - 1) * (log_det_baseline + np.trace(sample_cov @ inv_baseline) - log_det_sample - n_vars)
        else:
            diff_baseline = sample_cov - baseline_cov
            baseline_chi = (n_obs - 1) * np.trace(diff_baseline @ np.linalg.inv(sample_cov) @ diff_baseline)
        
        baseline_df = int(n_vars * (n_vars - 1) / 2)
        
        # CFI (Comparative Fit Index)
        if baseline_df > 0 and baseline_chi > 0:
            cfi = max(0, 1 - max(0, chi_square - df) / max(0, baseline_chi - baseline_df))
        else:
            cfi = 1.0
        
        # TLI (Tucker-Lewis Index)
        if baseline_df > 0 and df > 0:
            tli = 1 - (chi_square / df) / (baseline_chi / baseline_df)
        else:
            tli = 1.0
        
        # RMSEA (Root Mean Square Error of Approximation)
        if df > 0:
            rmsea = np.sqrt(max(0, (chi_square - df) / (df * (n_obs - 1))))
            
            # RMSEA confidence interval (approximate)
            alpha = 0.1  # 90% CI
            chi_lower = stats.chi2.ppf(alpha/2, df)
            chi_upper = stats.chi2.ppf(1 - alpha/2, df)
            rmsea_ci_lower = np.sqrt(max(0, (chi_lower - df) / (df * (n_obs - 1))))
            rmsea_ci_upper = np.sqrt(max(0, (chi_upper - df) / (df * (n_obs - 1))))
        else:
            rmsea = 0.0
            rmsea_ci_lower = 0.0
            rmsea_ci_upper = 0.0
        
        # SRMR (Standardized Root Mean Square Residual)
        residuals = sample_cov - sigma
        diag_sample = np.sqrt(np.diag(sample_cov))
        standardized_residuals = residuals / np.outer(diag_sample, diag_sample)
        srmr = np.sqrt(np.mean(standardized_residuals[np.tril_indices_from(standardized_residuals, k=-1)]**2))
        
        # Information criteria
        log_likelihood = -(n_obs - 1) * chi_square / 2
        aic = -2 * log_likelihood + 2 * n_params
        bic = -2 * log_likelihood + np.log(n_obs) * n_params
        
        return {
            'chi_square': float(chi_square),
            'p_value': float(p_value),
            'degrees_of_freedom': int(df),
            'cfi': float(cfi),
            'tli': float(tli),
            'rmsea': float(rmsea),
            'rmsea_ci_lower': float(rmsea_ci_lower),
            'rmsea_ci_upper': float(rmsea_ci_upper),
            'srmr': float(srmr),
            'aic': float(aic),
            'bic': float(bic)
        }
        
    except Exception as e:
        print(f"Error calculating fit indices: {e}")
        return {
            'chi_square': 0.0,
            'p_value': 1.0,
            'degrees_of_freedom': 1,
            'cfi': 0.0,
            'tli': 0.0,
            'rmsea': 1.0,
            'rmsea_ci_lower': 0.0,
            'rmsea_ci_upper': 1.0,
            'srmr': 1.0,
            'aic': 1e10,
            'bic': 1e10
        }

def calculate_standard_errors(fitted_params, sample_cov, model_matrices, n_obs):
    """Calculate standard errors using numerical derivatives"""
    def fit_func(params):
        try:
            sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
            log_det_sigma = np.linalg.slogdet(sigma)[1]
            log_det_sample = np.linalg.slogdet(sample_cov)[1]
            inv_sigma = np.linalg.inv(sigma)
            trace_term = np.trace(sample_cov @ inv_sigma)
            return log_det_sigma + trace_term - log_det_sample - model_matrices['n_vars']
        except:
            return 1e10
    
    # Calculate Hessian using finite differences
    h = 1e-6
    n_params = len(fitted_params)
    hessian = np.zeros((n_params, n_params))
    
    f0 = fit_func(fitted_params)
    
    for i in range(n_params):
        for j in range(i, n_params):
            params_ij = fitted_params.copy()
            params_ij[i] += h
            params_ij[j] += h
            
            params_i = fitted_params.copy()
            params_i[i] += h
            
            params_j = fitted_params.copy()
            params_j[j] += h
            
            fij = fit_func(params_ij)
            fi = fit_func(params_i)
            fj = fit_func(params_j)
            
            hessian[i, j] = (fij - fi - fj + f0) / (h * h)
            if i != j:
                hessian[j, i] = hessian[i, j]
    
    try:
        # Information matrix is (n-1) * Hessian
        info_matrix = (n_obs - 1) * hessian
        
        # Standard errors are sqrt of diagonal of inverse information matrix
        inv_info = np.linalg.inv(info_matrix)
        standard_errors = np.sqrt(np.diag(inv_info))
        
        return standard_errors, inv_info
    except:
        return np.ones(n_params) * 0.1, np.eye(n_params)

def extract_path_coefficients(fitted_params, standard_errors, model_matrices, model_spec, variable_names):
    """Extract path coefficients from fitted parameters"""
    param_idx = 0
    path_coefficients = []
    
    # Factor loadings
    sigma, lambda_matrix, phi_matrix, theta_matrix = calculate_covariance_matrix(fitted_params, model_matrices)
    
    # Calculate standardization factors
    var_names_ordered = variable_names
    implied_variances = np.diag(sigma)
    factor_variances = np.diag(phi_matrix)
    
    # Factor loadings
    for var_idx, factor_idx in model_matrices['free_loading_params']:
        unstd_estimate = fitted_params[param_idx]
        se = standard_errors[param_idx]
        
        # Standardized loading
        if factor_variances[factor_idx] > 0 and implied_variances[var_idx] > 0:
            std_estimate = unstd_estimate * np.sqrt(factor_variances[factor_idx]) / np.sqrt(implied_variances[var_idx])
        else:
            std_estimate = unstd_estimate
        
        z_score = unstd_estimate / se if se > 0 else 0
        p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
        
        # 95% confidence interval
        ci_lower = unstd_estimate - 1.96 * se
        ci_upper = unstd_estimate + 1.96 * se
        
        factor_name = model_spec['factors'][factor_idx]['name']
        var_name = var_names_ordered[var_idx]
        
        path_coefficients.append({
            'from': factor_name,
            'to': var_name,
            'parameter_type': 'loading',
            'unstandardized_estimate': float(unstd_estimate),
            'standardized_estimate': float(std_estimate),
            'standard_error': float(se),
            'z_score': float(z_score),
            'p_value': float(p_value),
            'confidence_interval_lower': float(ci_lower),
            'confidence_interval_upper': float(ci_upper)
        })
        
        param_idx += 1
    
    # Factor correlations
    factor_correlations = []
    for i, j in model_matrices['free_factor_corr_params']:
        unstd_estimate = fitted_params[param_idx]
        se = standard_errors[param_idx]
        
        # For correlations, standardized = unstandardized
        std_estimate = unstd_estimate
        
        z_score = unstd_estimate / se if se > 0 else 0
        p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
        
        ci_lower = unstd_estimate - 1.96 * se
        ci_upper = unstd_estimate + 1.96 * se
        
        factor_name_i = model_spec['factors'][i]['name']
        factor_name_j = model_spec['factors'][j]['name']
        
        factor_correlations.append({
            'from': factor_name_i,
            'to': factor_name_j,
            'parameter_type': 'correlation',
            'unstandardized_estimate': float(unstd_estimate),
            'standardized_estimate': float(std_estimate),
            'standard_error': float(se),
            'z_score': float(z_score),
            'p_value': float(p_value),
            'confidence_interval_lower': float(ci_lower),
            'confidence_interval_upper': float(ci_upper)
        })
        
        param_idx += 1
    
    return path_coefficients, factor_correlations

def calculate_modification_indices(fitted_params, sample_cov, model_matrices, n_obs):
    """Calculate modification indices for potential model improvements using Lagrange multiplier test"""
    modification_indices = []

    try:
        n_vars = model_matrices['n_vars']
        n_factors = model_matrices['n_factors']

        # Get current model implied covariance and derivatives
        sigma, lambda_matrix, phi_matrix, theta_matrix = calculate_covariance_matrix(fitted_params, model_matrices)

        # Calculate residual matrix
        residual_matrix = sample_cov - sigma

        # Calculate weight matrix (for ML estimation, use simpler approach)
        try:
            inv_sigma = np.linalg.inv(sigma)
        except:
            # If sigma is singular, use pseudo-inverse
            inv_sigma = np.linalg.pinv(sigma)



        # Current free parameters
        current_loadings = set(model_matrices['free_loading_params'])
        current_error_corrs = set(model_matrices['free_error_corr_params'])

        # Check factor loadings not currently in model
        for var_idx in range(n_vars):
            for factor_idx in range(n_factors):
                if (var_idx, factor_idx) not in current_loadings:
                    # Calculate derivative of sigma with respect to this loading
                    d_sigma = np.zeros((n_vars, n_vars))

                    # d(sigma)/d(lambda_ij) = e_i * phi_jj * lambda_j^T + lambda_j * phi_jj * e_i^T
                    # where e_i is unit vector for variable i, lambda_j is column j of lambda matrix
                    e_i = np.zeros(n_vars)
                    e_i[var_idx] = 1.0
                    lambda_j = lambda_matrix[:, factor_idx]
                    phi_jj = phi_matrix[factor_idx, factor_idx]

                    d_sigma = phi_jj * (np.outer(e_i, lambda_j) + np.outer(lambda_j, e_i))

                    # Calculate modification index using simpler approach
                    # Score function: tr(inv_sigma * residual * inv_sigma * d_sigma)
                    score_matrix = inv_sigma @ residual_matrix @ inv_sigma @ d_sigma
                    score = np.trace(score_matrix)

                    # Information matrix: tr(inv_sigma * d_sigma * inv_sigma * d_sigma)
                    info_matrix = inv_sigma @ d_sigma @ inv_sigma @ d_sigma
                    information = np.trace(info_matrix)

                    if information > 1e-10:
                        mi = (n_obs - 1) * (score ** 2) / information
                        epc = score / information if information > 0 else 0.0
                    else:
                        mi = 0.0
                        epc = 0.0

                    # Include all modification indices above a very small threshold
                    if mi > 0.01:  # Lower threshold to capture more indices
                        modification_indices.append({
                            'parameter': f'Loading: Factor{factor_idx+1} -> Var{var_idx+1}',
                            'modification_index': float(mi),
                            'expected_parameter_change': float(epc),
                            'parameter_type': 'loading'
                        })


        # Check error correlations not currently in model
        for i in range(n_vars):
            for j in range(i + 1, n_vars):
                if (i, j) not in current_error_corrs:
                    # Calculate derivative of sigma with respect to error correlation
                    d_sigma = np.zeros((n_vars, n_vars))
                    d_sigma[i, j] = 1.0
                    d_sigma[j, i] = 1.0

                    # Calculate modification index using simpler approach
                    # Score function: tr(inv_sigma * residual * inv_sigma * d_sigma)
                    score_matrix = inv_sigma @ residual_matrix @ inv_sigma @ d_sigma
                    score = np.trace(score_matrix)

                    # Information matrix: tr(inv_sigma * d_sigma * inv_sigma * d_sigma)
                    info_matrix = inv_sigma @ d_sigma @ inv_sigma @ d_sigma
                    information = np.trace(info_matrix)

                    if information > 1e-10:
                        mi = (n_obs - 1) * (score ** 2) / information
                        epc = score / information if information > 0 else 0.0
                    else:
                        mi = 0.0
                        epc = 0.0

                    # Include all modification indices above a very small threshold
                    if mi > 0.01:  # Lower threshold to capture more indices
                        modification_indices.append({
                            'parameter': f'Error correlation: Var{i+1} <-> Var{j+1}',
                            'modification_index': float(mi),
                            'expected_parameter_change': float(epc),
                            'parameter_type': 'error_correlation'
                        })


        # Sort by modification index (descending)
        modification_indices.sort(key=lambda x: x['modification_index'], reverse=True)



        return modification_indices

    except Exception as e:
        print(f"Error calculating modification indices: {e}")
        import traceback
        traceback.print_exc()
        return []

def run_cfa_analysis(data_dict):
    """Main CFA analysis function"""
    try:
        # Extract parameters
        variables = data_dict.get('variables', {})
        variable_names = data_dict.get('variable_names', list(variables.keys()))
        model_spec = data_dict.get('model_specification', {})
        estimation_method = data_dict.get('estimation_method', 'ml')
        significance_level = data_dict.get('significance_level', 0.05)
        
        print(f"Running CFA with {len(variables)} variables and {len(model_spec.get('factors', []))} factors")
        
        # Create data matrix and calculate sample covariance
        n_vars = len(variable_names)
        n_obs = len(next(iter(variables.values())))
        
        X = np.array([variables[name] for name in variable_names]).T
        
        # Standardize data
        X_std = (X - np.mean(X, axis=0)) / np.std(X, axis=0, ddof=1)
        
        # Calculate sample covariance matrix
        sample_cov = np.cov(X_std.T, ddof=1)
        
        # Create model matrices
        model_matrices = create_model_matrices(model_spec, variable_names)
        
        # Estimate model
        fitted_params, fit_value, converged, iterations = estimate_cfa_model(
            sample_cov, model_matrices, estimation_method, n_obs
        )
        
        # Calculate standard errors
        standard_errors, info_matrix = calculate_standard_errors(
            fitted_params, sample_cov, model_matrices, n_obs
        )
        
        # Calculate fit indices
        fit_indices = calculate_fit_indices(
            sample_cov, fitted_params, model_matrices, n_obs, estimation_method
        )
        
        # Extract path coefficients
        factor_loadings, factor_correlations = extract_path_coefficients(
            fitted_params, standard_errors, model_matrices, model_spec, variable_names
        )
        
        # Calculate modification indices
        modification_indices = calculate_modification_indices(
            fitted_params, sample_cov, model_matrices, n_obs
        )
        
        # Calculate residuals
        sigma, _, _, _ = calculate_covariance_matrix(fitted_params, model_matrices)
        residuals = sample_cov - sigma
        
        # Standardized residuals
        diag_sample = np.sqrt(np.diag(sample_cov))
        standardized_residuals = residuals / np.outer(diag_sample, diag_sample)
        
        # Prepare results
        results = {
            'fit_indices': fit_indices,
            'factor_loadings': factor_loadings,
            'factor_correlations': factor_correlations,
            'modification_indices': modification_indices,
            'residual_correlations': residuals.tolist(),
            'standardized_residuals': standardized_residuals.tolist(),
            'n_factors': len(model_spec['factors']),
            'n_observed_variables': n_vars,
            'n_parameters': len(fitted_params),
            'n_observations': n_obs,
            'degrees_of_freedom': fit_indices['degrees_of_freedom'],
            'estimation_method': estimation_method,
            'converged': converged,
            'iterations': iterations
        }
        
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'CFA analysis failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})
    `)}async runCFA(c){var m;await this.initialize();const f={variables:c.variables,variable_names:c.variable_names,model_specification:c.model_specification,estimation_method:c.estimation_method||"ml",significance_level:c.significance_level||.05};console.log("Running CFA with:",{nVariables:Object.keys(c.variables).length,nObservations:((m=c.variables[Object.keys(c.variables)[0]])==null?void 0:m.length)||0,nFactors:c.model_specification.factors.length,estimationMethod:f.estimation_method}),this.pyodide.globals.set("cfa_data",this.pyodide.toPy(f));const C=this.pyodide.runPython("run_cfa_analysis(cfa_data)"),z=JSON.parse(C);if(z.error)throw new Error(`CFA analysis failed: ${z.error}`);return z}async calculateModificationIndices(c,f){await this.initialize();try{return c.modification_indices||[]}catch(C){return console.error("Error calculating modification indices:",C),[]}}async evaluateModelFit(c){const f=[];let C=0,z=0;c.cfi>=.95?(f.push("CFI indicates excellent fit (≥ 0.95)"),C++):c.cfi>=.9?(f.push("CFI indicates acceptable fit (≥ 0.90)"),z++):f.push("CFI indicates poor fit (< 0.90)"),c.tli>=.95?(f.push("TLI indicates excellent fit (≥ 0.95)"),C++):c.tli>=.9?(f.push("TLI indicates acceptable fit (≥ 0.90)"),z++):f.push("TLI indicates poor fit (< 0.90)"),c.rmsea<=.05?(f.push("RMSEA indicates excellent fit (≤ 0.05)"),C++):c.rmsea<=.08?(f.push("RMSEA indicates acceptable fit (≤ 0.08)"),z++):c.rmsea<=.1?f.push("RMSEA indicates mediocre fit (≤ 0.10)"):f.push("RMSEA indicates poor fit (> 0.10)"),c.srmr<=.05?(f.push("SRMR indicates excellent fit (≤ 0.05)"),C++):c.srmr<=.08?(f.push("SRMR indicates acceptable fit (≤ 0.08)"),z++):f.push("SRMR indicates poor fit (> 0.08)"),c.p_value>.05?(f.push("Chi-square test indicates good fit (p > 0.05)"),C++):f.push("Chi-square test indicates poor fit (p ≤ 0.05), but may be due to large sample size");let m;return C>=3?m="excellent":C>=2||z>=3?m="good":z>=2?m="acceptable":m="poor",{overall:m,details:f}}isReady(){return this.isInitialized}}const si=new Gi,ca=()=>{var Xe,Ye;const{datasets:M,currentDataset:c,setCurrentDataset:f}=Wi();Mi();const[C,z]=p.useState((c==null?void 0:c.id)||""),[m,B]=p.useState([]),[Le,ni]=p.useState("ml"),[ne,oi]=p.useState({allowCorrelatedErrors:!1,allowFactorCorrelations:!0,constrainVariances:!1,meanStructure:!1}),[_,Te]=p.useState({showStandardized:!0,showUnstandardized:!1,showStandardErrors:!0,showPValues:!0,showModificationIndices:!0,showResidualCorrelations:!1,significanceLevel:.05}),[oe,$e]=p.useState(!1),[le,De]=p.useState(!1),[J,li]=p.useState(!1),[Be,K]=p.useState(null),[t,A]=p.useState(null),[X,ci]=p.useState(0),[di,pe]=p.useState(!1),[mi,ce]=p.useState(!1),[fi,xe]=p.useState(!1),[ue,qe]=p.useState({factors:{},indicators:{},errors:{}}),[O,Oe]=p.useState(!1),[Hi,Ne]=p.useState(null),[Ji,Ki]=p.useState({x:0,y:0}),Ve=p.useCallback((i,a,n,l)=>{const d=Math.max(.05,Math.min(.95,n)),E=Math.max(.1,Math.min(.9,l));qe(v=>({...v,[i==="factor"?"factors":i==="indicator"?"indicators":"errors"]:{...v[i==="factor"?"factors":i==="indicator"?"indicators":"errors"],[a]:{x:d,y:E}}}))},[]),ge=p.useRef(null),_i=p.useCallback(i=>{if(i.points&&i.points.length>0){const a=i.points[0];if(a.customdata&&typeof a.customdata=="string"){const[n,l]=a.customdata.split(":");Ne({type:n,id:l,traceIndex:a.curveNumber}),Oe(!0),document.body.style.cursor="grabbing";const d=v=>{if(ge.current){const S=ge.current.el.getBoundingClientRect(),L=(v.clientX-S.left)/S.width,T=1-(v.clientY-S.top)/S.height;Ve(n,l,L,T)}},E=()=>{Oe(!1),Ne(null),document.body.style.cursor="default",document.removeEventListener("mousemove",d),document.removeEventListener("mouseup",E)};document.addEventListener("mousemove",d),document.addEventListener("mouseup",E)}}},[Ve]),hi=p.useCallback(i=>{if(!O&&i.points&&i.points.length>0){const a=i.points[0];a.customdata&&typeof a.customdata=="string"&&(document.body.style.cursor="grab")}},[O]),pi=p.useCallback(()=>{O||(document.body.style.cursor="default")},[O]),xi=p.useCallback(()=>{qe({factors:{},indicators:{},errors:{}})},[]),[R,Y]=p.useState({summary:!0,fitIndices:!0,pathCoefficients:!0,modificationIndices:!0,residualCorrelations:!1,modelSpecification:!0}),ve=p.useCallback(async()=>{if(!(J||le)){De(!0);try{await si.initialize(),li(!0)}catch(i){console.error("Failed to initialize Python environment:",i),K("Failed to initialize Python environment. Please refresh the page and try again.")}finally{De(!1)}}},[J,le]);p.useEffect(()=>{ve()},[ve]),p.useEffect(()=>{const i=localStorage.getItem("cfa_results");if(i)try{const n=JSON.parse(i);A(n)}catch(n){console.error("Error parsing saved CFA results:",n),localStorage.removeItem("cfa_results")}const a=localStorage.getItem("cfa_model");if(a)try{const n=JSON.parse(a);B(n)}catch(n){console.error("Error parsing saved CFA model:",n),localStorage.removeItem("cfa_model")}},[]);const Q=(c==null?void 0:c.columns.filter(i=>i.type===Xi.NUMERIC))||[],ui=i=>{const a=i.target.value;z(a),B([]),A(null),localStorage.removeItem("cfa_results"),localStorage.removeItem("cfa_model");const n=M.find(l=>l.id===a);n&&f(n)},gi=i=>{ni(i.target.value),A(null),localStorage.removeItem("cfa_results")},je=i=>a=>{oi(n=>({...n,[i]:a.target.checked})),A(null),localStorage.removeItem("cfa_results")},G=i=>(a,n)=>{if(i==="significanceLevel"){const l=typeof n=="number"?n:.05;Te(d=>({...d,significanceLevel:l}))}else{const l=typeof n=="boolean"?n:a.target.checked;Te(d=>({...d,[i]:l}))}},vi=(i,a)=>{ci(a)},ji=()=>{const i={id:`factor_${Date.now()}`,name:`Factor ${m.length+1}`,indicators:[]};B(a=>[...a,i])},bi=i=>{B(a=>a.filter(n=>n.id!==i)),A(null),localStorage.removeItem("cfa_results")},yi=(i,a)=>{B(n=>n.map(l=>l.id===i?{...l,name:a}:l)),localStorage.setItem("cfa_model",JSON.stringify(m))},Ci=(i,a)=>{B(n=>n.map(l=>({...l,indicators:l.indicators.filter(d=>d!==a)}))),B(n=>n.map(l=>l.id===i?{...l,indicators:[...l.indicators,a]}:l)),A(null),localStorage.removeItem("cfa_results"),localStorage.setItem("cfa_model",JSON.stringify(m))},Ue=(i,a)=>{B(n=>n.map(l=>l.id===i?{...l,indicators:l.indicators.filter(d=>d!==a)}:l)),A(null),localStorage.removeItem("cfa_results"),localStorage.setItem("cfa_model",JSON.stringify(m))},be=()=>{if(m.length===0)return!1;for(const i of m)if(i.indicators.length<2)return!1;return!(m.length===1&&m[0].indicators.length<3)},wi=async()=>{if(!c||!be()){K("Please specify a valid model. Each factor needs at least 2 indicators, and single-factor models need at least 3 indicators.");return}if(!J){K("Python environment is not ready. Please wait for initialization to complete.");return}$e(!0),K(null),A(null);try{const a=m.flatMap(j=>j.indicators).map(j=>c.columns.find(S=>S.id===j)).filter(Boolean),n={};a.forEach(j=>{const S=[];c.data.forEach(L=>{const T=L[j.name];typeof T=="number"&&!isNaN(T)&&S.push(T)}),n[j.name]=S});const l=Object.values(n).map(j=>j.length);if(new Set(l).size>1)throw new Error("Variables have different numbers of valid observations.");if(l[0]<50)throw new Error("Insufficient data for CFA. Need at least 50 valid observations.");const d={factors:m.map(j=>({name:j.name,indicators:j.indicators.map(S=>{const L=a.find(T=>T.id===S);return L?L.name:S})})),constraints:ne},E={variables:n,variable_names:a.map(j=>j.name),model_specification:d,estimation_method:Le,significance_level:_.significanceLevel},v=await si.runCFA(E);A(v),localStorage.setItem("cfa_results",JSON.stringify(v))}catch(i){console.error("CFA error:",i),K(`Error in confirmatory factor analysis: ${i instanceof Error?i.message:String(i)}`)}finally{$e(!1)}},Si=()=>{if(!t)return;let i=`CONFIRMATORY FACTOR ANALYSIS RESULTS
`;i+=`=====================================

`,R.summary&&(i+=`SUMMARY
`,i+=`-------
`,i+=`Estimation Method: ${t.estimation_method.toUpperCase()}
`,i+=`Number of Factors: ${t.n_factors}
`,i+=`Number of Observed Variables: ${t.n_observed_variables}
`,i+=`Number of Parameters: ${t.n_parameters}
`,i+=`Number of Observations: ${t.n_observations}
`,i+=`Degrees of Freedom: ${t.degrees_of_freedom}
`,i+=`Converged: ${t.converged?"Yes":"No"}
`,i+=`Iterations: ${t.iterations}

`),R.modelSpecification&&(i+=`MODEL SPECIFICATION
`,i+=`-------------------
`,m.forEach(d=>{i+=`${d.name}:
`,d.indicators.forEach(E=>{const v=Q.find(j=>j.id===E);i+=`  - ${(v==null?void 0:v.name)||E}
`})}),i+=`
`),R.fitIndices&&(i+=`FIT INDICES
`,i+=`-----------
`,i+=`Chi-square: ${t.fit_indices.chi_square.toFixed(3)} (df = ${t.fit_indices.degrees_of_freedom})
`,i+=`p-value: ${t.fit_indices.p_value.toFixed(4)}
`,i+=`CFI: ${t.fit_indices.cfi.toFixed(3)}
`,i+=`TLI: ${t.fit_indices.tli.toFixed(3)}
`,i+=`RMSEA: ${t.fit_indices.rmsea.toFixed(3)} [${t.fit_indices.rmsea_ci_lower.toFixed(3)}, ${t.fit_indices.rmsea_ci_upper.toFixed(3)}]
`,i+=`SRMR: ${t.fit_indices.srmr.toFixed(3)}
`,i+=`AIC: ${t.fit_indices.aic.toFixed(1)}
`,i+=`BIC: ${t.fit_indices.bic.toFixed(1)}

`),R.pathCoefficients&&(i+=`FACTOR LOADINGS
`,i+=`---------------
`,t.factor_loadings.forEach(d=>{i+=`${d.from} -> ${d.to}: `,_.showStandardized&&(i+=`β = ${d.standardized_estimate.toFixed(3)}`),_.showUnstandardized&&(i+=` (B = ${d.unstandardized_estimate.toFixed(3)})`),_.showStandardErrors&&(i+=` SE = ${d.standard_error.toFixed(3)}`),_.showPValues&&(i+=` p = ${d.p_value<.001?"< 0.001":d.p_value.toFixed(4)}`),i+=`
`}),i+=`
`,t.factor_correlations&&t.factor_correlations.length>0&&(i+=`FACTOR CORRELATIONS
`,i+=`-------------------
`,t.factor_correlations.forEach(d=>{i+=`${d.from} <-> ${d.to}: r = ${d.standardized_estimate.toFixed(3)}`,_.showPValues&&(i+=` p = ${d.p_value<.001?"< 0.001":d.p_value.toFixed(4)}`),i+=`
`}),i+=`
`)),R.modificationIndices&&t.modification_indices&&(i+=`MODIFICATION INDICES
`,i+=`--------------------
`,t.modification_indices.filter(d=>d.modification_index>3.84).forEach(d=>{i+=`${d.parameter}: MI = ${d.modification_index.toFixed(3)}, EPC = ${d.expected_parameter_change.toFixed(3)}
`}),i+=`
`);const a=new Blob([i],{type:"text/plain"}),n=URL.createObjectURL(a),l=document.createElement("a");l.href=n,l.download="cfa_results.txt",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(n),ce(!1)},Fi=()=>{if(!t)return"";const i=t.fit_indices;let a="";a+=`Chi-square Test: χ²(${t.fit_indices.degrees_of_freedom}) = ${i.chi_square.toFixed(2)}, p = ${i.p_value.toFixed(4)}
`,i.p_value>.05?a+=`The chi-square test is non-significant, suggesting good model fit.
`:a+=`The chi-square test is significant, but this may be due to large sample size. Consider other fit indices.
`,a+=`
Comparative Fit Index (CFI): ${i.cfi.toFixed(3)}
`,i.cfi>=.95?a+=`CFI indicates excellent fit.
`:i.cfi>=.9?a+=`CFI indicates acceptable fit.
`:a+=`CFI indicates poor fit. Consider model modifications.
`,a+=`
Tucker-Lewis Index (TLI): ${i.tli.toFixed(3)}
`,i.tli>=.95?a+=`TLI indicates excellent fit.
`:i.tli>=.9?a+=`TLI indicates acceptable fit.
`:a+=`TLI indicates poor fit.
`,a+=`
Root Mean Square Error of Approximation (RMSEA): ${i.rmsea.toFixed(3)} [${i.rmsea_ci_lower.toFixed(3)}, ${i.rmsea_ci_upper.toFixed(3)}]
`,i.rmsea<=.05?a+=`RMSEA indicates excellent fit.
`:i.rmsea<=.08?a+=`RMSEA indicates acceptable fit.
`:i.rmsea<=.1?a+=`RMSEA indicates mediocre fit.
`:a+=`RMSEA indicates poor fit.
`,a+=`
Standardized Root Mean Square Residual (SRMR): ${i.srmr.toFixed(3)}
`,i.srmr<=.05?a+=`SRMR indicates excellent fit.
`:i.srmr<=.08?a+=`SRMR indicates acceptable fit.
`:a+=`SRMR indicates poor fit.
`,a+=`
Overall Model Fit Assessment:
`;const n=[i.cfi>=.95,i.tli>=.95,i.rmsea<=.05,i.srmr<=.05].filter(Boolean).length,l=[i.cfi>=.9,i.tli>=.9,i.rmsea<=.08,i.srmr<=.08].filter(Boolean).length;return n>=3?a+=`The model demonstrates excellent fit to the data.
`:l>=3?a+=`The model demonstrates acceptable fit to the data.
`:a+=`The model fit is questionable. Consider examining modification indices for potential improvements.
`,a},We=()=>{if(!t)return null;const i=[],a=[],n=m.length,l={},d={},E=m.reduce((s,h)=>s+h.indicators.length,0);Math.max(...m.map(s=>s.indicators.length));const v=Math.max(1400,E*100),j=600,S=.8,L=Math.min(200,v*.8/Math.max(n-1,1)),T=(v-(n-1)*L)/2;m.forEach((s,h)=>{const b={x:(n===1?v/2:T+h*L)/v,y:S};l[s.name]=ue.factors[s.name]||b});let ye=[];m.forEach(s=>{const h=l[s.name],u=s.indicators.length,b=140,w=(u-1)*b,N=h.x*v-w/2;s.indicators.forEach((V,U)=>{const k=Q.find(Se=>Se.id===V),H=(k==null?void 0:k.name)||V,de=u===1?h.x*v:N+U*b,Ce=.4*j,we={x:de/v,y:Ce/j},Z=ue.indicators[H]||we;d[H]=Z,ye.push({name:H,factorName:s.name,x:Z.x,y:Z.y})})}),Object.keys(l).forEach(s=>{const h=l[s];let u=s,b=14;s.length>10?(u=s.substring(0,10)+"...",b=12):s.length>6&&(b=13),i.push({x:[h.x],y:[h.y],mode:"markers",type:"scatter",marker:{size:90,color:O?"rgba(33, 150, 243, 0.3)":"rgba(33, 150, 243, 0.1)",line:{color:"#1976d2",width:3},symbol:"circle"},hoverinfo:"text",hovertext:`Latent Factor: ${s} (Drag to move)`,name:`Factor_${s}`,showlegend:!1,customdata:[`factor:${s}`]}),i.push({x:[h.x],y:[h.y],mode:"text",type:"scatter",text:[u],textposition:"middle center",textfont:{size:b,color:"#1976d2",family:"Arial, sans-serif"},hoverinfo:"skip",showlegend:!1})}),ye.forEach(s=>{let h=s.name,u=10;s.name.length>12?(h=s.name.substring(0,12)+"...",u=9):s.name.length>8&&(u=9),i.push({x:[s.x],y:[s.y],mode:"markers",type:"scatter",marker:{size:60,color:O?"rgba(76, 175, 80, 0.3)":"rgba(76, 175, 80, 0.15)",line:{color:"#388e3c",width:2},symbol:"square"},hoverinfo:"text",hovertext:`Observed Variable: ${s.name} (Drag to move)`,name:`Indicator_${s.name}`,showlegend:!1,customdata:[`indicator:${s.name}`]}),i.push({x:[s.x],y:[s.y],mode:"text",type:"scatter",text:[h],textposition:"middle center",textfont:{size:u,color:"#2e7d32",family:"Arial, sans-serif"},hoverinfo:"skip",showlegend:!1})});const Ge=[];t.factor_loadings.forEach(s=>{const h=l[s.from],u=d[s.to];h&&u&&Ge.push({x:[h.x,u.x],y:[h.y,u.y],loading:s.standardized_estimate,from:s.from,to:s.to})}),Ge.forEach((s,h)=>{const u=Math.abs(s.loading)*3+1.5,b=s.loading>0?"#1976d2":"#d32f2f";i.push({x:s.x,y:s.y,mode:"lines",type:"scatter",line:{color:b,width:u},hoverinfo:"text",hovertext:`${s.from} → ${s.to}<br>Loading: ${s.loading.toFixed(3)}`,showlegend:!1,name:`Loading ${h+1}`});const w=s.x[1]-s.x[0],N=s.y[1]-s.y[0],V=Math.sqrt(w*w+N*N),U=w/V,k=N/V,H=s.x[1]-.03*U,de=s.y[1]-.03*k;a.push({x:H,y:de,text:"",showarrow:!0,arrowhead:2,arrowsize:1.2,arrowwidth:2,arrowcolor:b,ax:H-.02*U,ay:de-.02*k});const Ce=(s.x[0]+s.x[1])/2,we=(s.y[0]+s.y[1])/2,Z=-k*.025,Se=U*.025;a.push({x:Ce+Z,y:we+Se,text:s.loading.toFixed(2),showarrow:!1,font:{size:10,color:b,family:"Arial, sans-serif"},bgcolor:"rgba(255,255,255,0.9)",bordercolor:b,borderwidth:1,borderpad:1})}),t.factor_correlations&&t.factor_correlations.length>0&&t.factor_correlations.forEach(s=>{const h=l[s.from],u=l[s.to];if(h&&u){const b=(h.x+u.x)/2,w=Math.max(h.y,u.y)+.08,N=[h.x,b,u.x],V=[h.y,w,u.y],U=Math.abs(s.standardized_estimate)*3+1,k=s.standardized_estimate>0?"#7b1fa2":"#f57c00";i.push({x:N,y:V,mode:"lines",type:"scatter",line:{color:k,width:U,dash:"dash"},hoverinfo:"text",hovertext:`${s.from} ↔ ${s.to}<br>Correlation: ${s.standardized_estimate.toFixed(3)}`,showlegend:!1}),a.push({x:b,y:w+.01,text:s.standardized_estimate.toFixed(2),showarrow:!1,font:{size:10,color:k,family:"Arial, sans-serif"},bgcolor:"rgba(255,255,255,0.8)",bordercolor:k,borderwidth:1,borderpad:1})}}),ye.forEach((s,h)=>{const u=`error_${s.name}`,b={x:s.x,y:s.y-.15},w=ue.errors[u]||b;i.push({x:[w.x],y:[w.y],mode:"markers",type:"scatter",marker:{size:25,color:O?"rgba(244, 67, 54, 0.3)":"rgba(244, 67, 54, 0.15)",line:{color:"#d32f2f",width:1.5},symbol:"circle"},hoverinfo:"text",hovertext:`Error term for ${s.name} (Drag to move)`,showlegend:!1,customdata:[`error:${u}`]}),i.push({x:[w.x],y:[w.y],mode:"text",type:"scatter",text:[`e${h+1}`],textposition:"middle center",textfont:{size:9,color:"#d32f2f",family:"Arial, sans-serif"},hoverinfo:"skip",showlegend:!1}),i.push({x:[s.x,w.x],y:[s.y,w.y],mode:"lines",type:"scatter",line:{color:"#d32f2f",width:1.5},showlegend:!1,hoverinfo:"skip"})});const Ii=Math.max(1e3,E*100);return{data:i,layout:{title:{text:"Confirmatory Factor Analysis Path Diagram",font:{size:16,family:"Arial, sans-serif",color:"#333"},x:.5,xanchor:"center"},showlegend:!1,xaxis:{showgrid:!1,zeroline:!1,showticklabels:!1,range:[0,1],fixedrange:!1},yaxis:{showgrid:!1,zeroline:!1,showticklabels:!1,range:[.1,.9],fixedrange:!1},height:600,width:Ii,annotations:a,plot_bgcolor:"white",paper_bgcolor:"white",margin:{l:60,r:60,t:80,b:60},font:{family:"Arial, sans-serif"},hovermode:"closest",dragmode:"pan",staticPlot:!1}}};return e.jsxs(x,{p:3,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,children:"Confirmatory Factor Analysis"}),le&&e.jsx($,{severity:"info",sx:{mb:2},children:e.jsxs(x,{children:[e.jsx(o,{variant:"body2",gutterBottom:!0,children:"Initializing Python environment for statistical analysis..."}),e.jsx(Ri,{sx:{mt:1}})]})}),!J&&!le&&e.jsx($,{severity:"warning",sx:{mb:2},action:e.jsx(P,{color:"inherit",size:"small",onClick:ve,startIcon:e.jsx(ki,{}),children:"Retry"}),children:"Python environment not ready. CFA requires Python libraries to be loaded."}),e.jsxs(W,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(o,{variant:"subtitle1",gutterBottom:!0,children:"Dataset Selection & Model Specification"}),e.jsxs(y,{container:!0,spacing:2,children:[e.jsx(y,{item:!0,xs:12,md:4,children:e.jsxs(fe,{fullWidth:!0,margin:"normal",children:[e.jsx(He,{id:"dataset-select-label",children:"Dataset"}),e.jsx(Fe,{labelId:"dataset-select-label",id:"dataset-select",value:C,label:"Dataset",onChange:ui,disabled:M.length===0,children:M.length===0?e.jsx(q,{value:"",disabled:!0,children:"No datasets available"}):M.map(i=>e.jsxs(q,{value:i.id,children:[i.name," (",i.data.length," rows)"]},i.id))})]})}),e.jsxs(y,{item:!0,xs:12,md:8,children:[e.jsxs(x,{display:"flex",alignItems:"center",gap:2,mt:2,children:[e.jsxs(o,{variant:"subtitle2",children:["Model Structure (",m.length," factors)"]}),e.jsx(P,{size:"small",startIcon:e.jsx(Pi,{}),onClick:ji,disabled:!c,children:"Add Factor"}),e.jsx(P,{size:"small",startIcon:e.jsx(Je,{}),onClick:()=>pe(!0),disabled:!c,children:"Model Builder"})]}),m.length>0&&e.jsx(x,{mt:2,children:m.map(i=>e.jsx(ee,{variant:"outlined",sx:{mb:1},children:e.jsx(ie,{sx:{py:1},children:e.jsxs(x,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[e.jsxs(x,{display:"flex",alignItems:"center",gap:2,flex:1,children:[e.jsx(Ai,{size:"small",label:"Factor Name",value:i.name,onChange:a=>yi(i.id,a.target.value),sx:{width:150}}),e.jsxs(x,{children:[e.jsxs(o,{variant:"body2",color:"text.secondary",children:["Indicators (",i.indicators.length,"):"]}),e.jsx(x,{sx:{display:"flex",flexWrap:"wrap",gap:.5,mt:.5},children:i.indicators.map(a=>{const n=Q.find(l=>l.id===a);return e.jsx(D,{label:(n==null?void 0:n.name)||a,size:"small",onDelete:()=>Ue(i.id,a)},a)})})]})]}),e.jsx(Ke,{size:"small",onClick:()=>bi(i.id),color:"error",children:e.jsx(Qe,{})})]})})},i.id))}),!be()&&m.length>0&&e.jsx($,{severity:"warning",sx:{mt:2},children:"Invalid model specification. Each factor needs at least 2 indicators, and single-factor models need at least 3 indicators for identification."})]})]}),e.jsx(Ze,{sx:{my:2}}),e.jsx(o,{variant:"subtitle1",gutterBottom:!0,children:"Analysis Configuration"}),e.jsxs(y,{container:!0,spacing:2,children:[e.jsx(y,{item:!0,xs:12,md:4,children:e.jsxs(fe,{fullWidth:!0,margin:"normal",children:[e.jsx(He,{id:"estimation-method-label",children:"Estimation Method"}),e.jsxs(Fe,{labelId:"estimation-method-label",id:"estimation-method",value:Le,label:"Estimation Method",onChange:gi,children:[e.jsx(q,{value:"ml",children:"Maximum Likelihood (ML)"}),e.jsx(q,{value:"gls",children:"Generalized Least Squares (GLS)"}),e.jsx(q,{value:"wls",children:"Weighted Least Squares (WLS)"}),e.jsx(q,{value:"mlr",children:"Robust Maximum Likelihood (MLR)"})]})]})}),e.jsx(y,{item:!0,xs:12,md:8,children:e.jsxs(fe,{component:"fieldset",margin:"normal",children:[e.jsx(ei,{component:"legend",children:"Model Constraints"}),e.jsxs(_e,{row:!0,children:[e.jsx(F,{control:e.jsx(I,{checked:ne.allowFactorCorrelations,onChange:je("allowFactorCorrelations")}),label:"Allow Factor Correlations"}),e.jsx(F,{control:e.jsx(I,{checked:ne.allowCorrelatedErrors,onChange:je("allowCorrelatedErrors")}),label:"Allow Correlated Errors"}),e.jsx(F,{control:e.jsx(I,{checked:ne.meanStructure,onChange:je("meanStructure")}),label:"Include Mean Structure"})]})]})})]}),e.jsx(Ze,{sx:{my:2}}),e.jsx(o,{variant:"subtitle1",gutterBottom:!0,children:"Display Options"}),e.jsxs(y,{container:!0,spacing:2,children:[e.jsx(y,{item:!0,xs:12,md:6,children:e.jsxs(_e,{children:[e.jsx(F,{control:e.jsx(I,{checked:_.showStandardized,onChange:G("showStandardized")}),label:"Show Standardized Estimates"}),e.jsx(F,{control:e.jsx(I,{checked:_.showUnstandardized,onChange:G("showUnstandardized")}),label:"Show Unstandardized Estimates"}),e.jsx(F,{control:e.jsx(I,{checked:_.showStandardErrors,onChange:G("showStandardErrors")}),label:"Show Standard Errors"})]})}),e.jsxs(y,{item:!0,xs:12,md:6,children:[e.jsxs(_e,{children:[e.jsx(F,{control:e.jsx(I,{checked:_.showPValues,onChange:G("showPValues")}),label:"Show p-values"}),e.jsx(F,{control:e.jsx(I,{checked:_.showModificationIndices,onChange:G("showModificationIndices")}),label:"Show Modification Indices"})]}),e.jsxs(x,{mt:2,children:[e.jsxs(o,{gutterBottom:!0,children:["Significance Level: ",_.significanceLevel]}),e.jsx(Li,{value:_.significanceLevel,onChange:(i,a)=>G("significanceLevel")(i,Array.isArray(a)?a[0]:a),min:.01,max:.1,step:.01,marks:[{value:.01,label:"0.01"},{value:.05,label:"0.05"},{value:.1,label:"0.10"}],valueLabelDisplay:"auto"})]})]})]}),e.jsx(x,{mt:2,children:e.jsx(P,{variant:"contained",color:"primary",startIcon:e.jsx(Ti,{}),onClick:wi,disabled:oe||!J||!be(),children:oe?"Running Analysis...":"Run Confirmatory Factor Analysis"})})]}),oe&&e.jsx(x,{display:"flex",justifyContent:"center",my:4,children:e.jsx($i,{})}),Be&&e.jsx($,{severity:"error",sx:{mb:3},children:Be}),t&&!oe&&e.jsx(e.Fragment,{children:e.jsxs(W,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(x,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:e.jsxs(Di,{value:X,onChange:vi,"aria-label":"cfa results tabs",children:[e.jsx(ae,{label:"Summary",icon:e.jsx(Bi,{}),iconPosition:"start"}),e.jsx(ae,{label:"Path Coefficients",icon:e.jsx(qi,{}),iconPosition:"start"}),e.jsx(ae,{label:"Model Fit",icon:e.jsx(Oi,{}),iconPosition:"start"}),e.jsx(ae,{label:"Modification Indices",icon:e.jsx(Ni,{}),iconPosition:"start"}),e.jsx(ae,{label:"Interpretation",icon:e.jsx(Vi,{}),iconPosition:"start"})]})}),X===0&&e.jsxs(x,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Analysis Summary"}),e.jsxs(y,{container:!0,spacing:3,children:[e.jsx(y,{item:!0,xs:12,md:6,children:e.jsx(ee,{variant:"outlined",children:e.jsxs(ie,{children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Model Information"}),e.jsx(te,{children:e.jsx(re,{size:"small",children:e.jsxs(se,{children:[e.jsxs(g,{children:[e.jsx(r,{children:"Estimation Method"}),e.jsx(r,{children:t.estimation_method.toUpperCase()})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Number of Factors"}),e.jsx(r,{children:t.n_factors})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Observed Variables"}),e.jsx(r,{children:t.n_observed_variables})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Parameters Estimated"}),e.jsx(r,{children:t.n_parameters})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Degrees of Freedom"}),e.jsx(r,{children:t.degrees_of_freedom})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Sample Size"}),e.jsx(r,{children:t.n_observations})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Converged"}),e.jsx(r,{children:t.converged?"Yes":"No"})]}),e.jsxs(g,{children:[e.jsx(r,{children:"Iterations"}),e.jsx(r,{children:t.iterations})]})]})})})]})})}),e.jsx(y,{item:!0,xs:12,md:6,children:e.jsx(ee,{variant:"outlined",children:e.jsxs(ie,{children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Key Fit Indices"}),e.jsxs(x,{mb:2,children:[e.jsx(o,{variant:"body2",gutterBottom:!0,children:"Comparative Fit Index (CFI)"}),e.jsx(o,{variant:"h5",color:t.fit_indices.cfi>=.95?"success.main":t.fit_indices.cfi>=.9?"warning.main":"error.main",children:t.fit_indices.cfi.toFixed(3)})]}),e.jsxs(x,{mb:2,children:[e.jsx(o,{variant:"body2",gutterBottom:!0,children:"RMSEA [90% CI]"}),e.jsx(o,{variant:"h5",color:t.fit_indices.rmsea<=.05?"success.main":t.fit_indices.rmsea<=.08?"warning.main":"error.main",children:t.fit_indices.rmsea.toFixed(3)}),e.jsxs(o,{variant:"body2",color:"text.secondary",children:["[",t.fit_indices.rmsea_ci_lower.toFixed(3),", ",t.fit_indices.rmsea_ci_upper.toFixed(3),"]"]})]}),e.jsxs(x,{children:[e.jsx(o,{variant:"body2",gutterBottom:!0,children:"SRMR"}),e.jsx(o,{variant:"h5",color:t.fit_indices.srmr<=.05?"success.main":t.fit_indices.srmr<=.08?"warning.main":"error.main",children:t.fit_indices.srmr.toFixed(3)})]})]})})})]}),e.jsxs(x,{mt:3,children:[e.jsx(P,{variant:"outlined",startIcon:e.jsx(Je,{}),onClick:()=>xe(!0),sx:{mr:2},children:"View Path Diagram"}),e.jsx(P,{variant:"outlined",startIcon:e.jsx(ii,{}),onClick:()=>ce(!0),children:"Export Results"})]})]}),X===1&&e.jsxs(x,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Path Coefficients"}),e.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{mt:2},children:"Factor Loadings"}),e.jsx(te,{component:W,variant:"outlined",children:e.jsxs(re,{size:"small",children:[e.jsx(he,{children:e.jsxs(g,{children:[e.jsx(r,{children:"Factor"}),e.jsx(r,{children:"Indicator"}),_.showStandardized&&e.jsx(r,{align:"right",children:"Std. Loading"}),_.showUnstandardized&&e.jsx(r,{align:"right",children:"Unstd. Loading"}),_.showStandardErrors&&e.jsx(r,{align:"right",children:"SE"}),e.jsx(r,{align:"right",children:"z-score"}),_.showPValues&&e.jsx(r,{align:"right",children:"p-value"})]})}),e.jsx(se,{children:t.factor_loadings.map((i,a)=>e.jsxs(g,{children:[e.jsx(r,{children:i.from}),e.jsx(r,{children:i.to}),_.showStandardized&&e.jsx(r,{align:"right",sx:{fontWeight:Math.abs(i.standardized_estimate)>.7?"bold":"normal",color:Math.abs(i.standardized_estimate)>.7?"primary.main":"text.primary"},children:i.standardized_estimate.toFixed(3)}),_.showUnstandardized&&e.jsx(r,{align:"right",children:i.unstandardized_estimate.toFixed(3)}),_.showStandardErrors&&e.jsx(r,{align:"right",children:i.standard_error.toFixed(3)}),e.jsx(r,{align:"right",children:i.z_score.toFixed(2)}),_.showPValues&&e.jsx(r,{align:"right",sx:{color:i.p_value<_.significanceLevel?"success.main":"text.primary"},children:i.p_value<.001?"< 0.001":i.p_value.toFixed(4)})]},a))})]})}),t.factor_correlations&&t.factor_correlations.length>0&&e.jsxs(x,{mt:3,children:[e.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Factor Correlations"}),e.jsx(te,{component:W,variant:"outlined",children:e.jsxs(re,{size:"small",children:[e.jsx(he,{children:e.jsxs(g,{children:[e.jsx(r,{children:"Factor 1"}),e.jsx(r,{children:"Factor 2"}),_.showStandardized&&e.jsx(r,{align:"right",children:"Correlation"}),_.showStandardErrors&&e.jsx(r,{align:"right",children:"SE"}),e.jsx(r,{align:"right",children:"z-score"}),_.showPValues&&e.jsx(r,{align:"right",children:"p-value"})]})}),e.jsx(se,{children:t.factor_correlations.map((i,a)=>e.jsxs(g,{children:[e.jsx(r,{children:i.from}),e.jsx(r,{children:i.to}),_.showStandardized&&e.jsx(r,{align:"right",sx:{color:Math.abs(i.standardized_estimate)>.5?"warning.main":"text.primary"},children:i.standardized_estimate.toFixed(3)}),_.showStandardErrors&&e.jsx(r,{align:"right",children:i.standard_error.toFixed(3)}),e.jsx(r,{align:"right",children:i.z_score.toFixed(2)}),_.showPValues&&e.jsx(r,{align:"right",sx:{color:i.p_value<_.significanceLevel?"success.main":"text.primary"},children:i.p_value<.001?"< 0.001":i.p_value.toFixed(4)})]},a))})]})})]})]}),X===2&&e.jsxs(x,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Model Fit Assessment"}),e.jsxs(y,{container:!0,spacing:3,children:[e.jsx(y,{item:!0,xs:12,md:8,children:e.jsx(te,{component:W,variant:"outlined",children:e.jsxs(re,{children:[e.jsx(he,{children:e.jsxs(g,{children:[e.jsx(r,{children:"Fit Index"}),e.jsx(r,{align:"right",children:"Value"}),e.jsx(r,{children:"Interpretation"}),e.jsx(r,{align:"center",children:"Status"})]})}),e.jsxs(se,{children:[e.jsxs(g,{children:[e.jsx(r,{children:"Chi-square (df)"}),e.jsxs(r,{align:"right",children:[t.fit_indices.chi_square.toFixed(2)," (",t.fit_indices.degrees_of_freedom,")"]}),e.jsxs(r,{children:["p = ",t.fit_indices.p_value.toFixed(4)]}),e.jsx(r,{align:"center",children:e.jsx(D,{size:"small",label:t.fit_indices.p_value>.05?"Good":"Poor",color:t.fit_indices.p_value>.05?"success":"error"})})]}),e.jsxs(g,{children:[e.jsx(r,{children:"CFI"}),e.jsx(r,{align:"right",children:t.fit_indices.cfi.toFixed(3)}),e.jsx(r,{children:"≥ 0.95 excellent, ≥ 0.90 acceptable"}),e.jsx(r,{align:"center",children:e.jsx(D,{size:"small",label:t.fit_indices.cfi>=.95?"Excellent":t.fit_indices.cfi>=.9?"Acceptable":"Poor",color:t.fit_indices.cfi>=.95?"success":t.fit_indices.cfi>=.9?"warning":"error"})})]}),e.jsxs(g,{children:[e.jsx(r,{children:"TLI"}),e.jsx(r,{align:"right",children:t.fit_indices.tli.toFixed(3)}),e.jsx(r,{children:"≥ 0.95 excellent, ≥ 0.90 acceptable"}),e.jsx(r,{align:"center",children:e.jsx(D,{size:"small",label:t.fit_indices.tli>=.95?"Excellent":t.fit_indices.tli>=.9?"Acceptable":"Poor",color:t.fit_indices.tli>=.95?"success":t.fit_indices.tli>=.9?"warning":"error"})})]}),e.jsxs(g,{children:[e.jsx(r,{children:"RMSEA"}),e.jsxs(r,{align:"right",children:[t.fit_indices.rmsea.toFixed(3),e.jsx("br",{}),e.jsxs(o,{variant:"caption",color:"text.secondary",children:["[",t.fit_indices.rmsea_ci_lower.toFixed(3),", ",t.fit_indices.rmsea_ci_upper.toFixed(3),"]"]})]}),e.jsx(r,{children:"≤ 0.05 excellent, ≤ 0.08 acceptable"}),e.jsx(r,{align:"center",children:e.jsx(D,{size:"small",label:t.fit_indices.rmsea<=.05?"Excellent":t.fit_indices.rmsea<=.08?"Acceptable":t.fit_indices.rmsea<=.1?"Mediocre":"Poor",color:t.fit_indices.rmsea<=.05?"success":t.fit_indices.rmsea<=.08?"warning":"error"})})]}),e.jsxs(g,{children:[e.jsx(r,{children:"SRMR"}),e.jsx(r,{align:"right",children:t.fit_indices.srmr.toFixed(3)}),e.jsx(r,{children:"≤ 0.05 excellent, ≤ 0.08 acceptable"}),e.jsx(r,{align:"center",children:e.jsx(D,{size:"small",label:t.fit_indices.srmr<=.05?"Excellent":t.fit_indices.srmr<=.08?"Acceptable":"Poor",color:t.fit_indices.srmr<=.05?"success":t.fit_indices.srmr<=.08?"warning":"error"})})]}),e.jsxs(g,{children:[e.jsx(r,{children:"AIC"}),e.jsx(r,{align:"right",children:t.fit_indices.aic.toFixed(1)}),e.jsx(r,{children:"Lower values indicate better fit"}),e.jsx(r,{align:"center",children:"-"})]}),e.jsxs(g,{children:[e.jsx(r,{children:"BIC"}),e.jsx(r,{align:"right",children:t.fit_indices.bic.toFixed(1)}),e.jsx(r,{children:"Lower values indicate better fit"}),e.jsx(r,{align:"center",children:"-"})]})]})]})})}),e.jsx(y,{item:!0,xs:12,md:4,children:e.jsx(ee,{variant:"outlined",children:e.jsxs(ie,{children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Overall Fit Assessment"}),(()=>{const i=t.fit_indices,a=[i.cfi>=.95,i.tli>=.95,i.rmsea<=.05,i.srmr<=.05].filter(Boolean).length,n=[i.cfi>=.9,i.tli>=.9,i.rmsea<=.08,i.srmr<=.08].filter(Boolean).length;return a>=3?e.jsx($,{severity:"success",children:e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"Excellent Fit"}),e.jsx("br",{}),"The model demonstrates excellent fit to the data with ",a,"/4 indices meeting stringent criteria."]})}):n>=3?e.jsx($,{severity:"warning",children:e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"Acceptable Fit"}),e.jsx("br",{}),"The model demonstrates acceptable fit to the data with ",n,"/4 indices meeting minimum criteria."]})}):e.jsx($,{severity:"error",children:e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"Poor Fit"}),e.jsx("br",{}),"The model fit is questionable. Consider examining modification indices for potential improvements."]})})})()]})})})]})]}),X===3&&e.jsxs(x,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Modification Indices"}),t.modification_indices&&t.modification_indices.length>0?e.jsxs(e.Fragment,{children:[e.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Modification indices suggest parameter changes that would improve model fit. Values above 3.84 are significant at p < 0.05."}),e.jsx(te,{component:W,variant:"outlined",children:e.jsxs(re,{size:"small",children:[e.jsx(he,{children:e.jsxs(g,{children:[e.jsx(r,{children:"Parameter"}),e.jsx(r,{align:"right",children:"Modification Index"}),e.jsx(r,{align:"right",children:"Expected Parameter Change"}),e.jsx(r,{children:"Recommendation"})]})}),e.jsx(se,{children:t.modification_indices.sort((i,a)=>a.modification_index-i.modification_index).map((i,a)=>e.jsxs(g,{sx:{backgroundColor:i.modification_index>10?"error.light":i.modification_index>3.84?"warning.light":"transparent"},children:[e.jsx(r,{children:i.parameter}),e.jsx(r,{align:"right",sx:{fontWeight:i.modification_index>3.84?"bold":"normal"},children:i.modification_index.toFixed(3)}),e.jsx(r,{align:"right",children:i.expected_parameter_change.toFixed(3)}),e.jsx(r,{children:i.modification_index>10?e.jsx(D,{size:"small",label:"Consider strongly",color:"error"}):i.modification_index>3.84?e.jsx(D,{size:"small",label:"Consider",color:"warning"}):e.jsx(D,{size:"small",label:"Not significant",color:"default"})})]},a))})]})}),e.jsx(x,{mt:2,children:e.jsx($,{severity:"info",children:e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"Important:"})," Only make theoretically justified modifications. Adding parameters solely to improve fit can lead to overfitting and loss of model interpretability."]})})})]}):e.jsx($,{severity:"info",children:"No modification indices available. This may indicate excellent model fit or that modification indices were not calculated for this analysis."})]}),X===4&&e.jsxs(x,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Interpretation Guide"}),e.jsx(W,{elevation:0,variant:"outlined",sx:{p:2,mb:3,bgcolor:"background.paper"},children:e.jsx(o,{variant:"body2",sx:{whiteSpace:"pre-line"},children:Fi()})}),e.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{mt:3},children:"Understanding Confirmatory Factor Analysis"}),e.jsxs(Ie,{children:[e.jsx(ze,{expandIcon:e.jsx(Ee,{}),children:e.jsx(o,{variant:"subtitle2",children:"Model Fit Indices"})}),e.jsxs(Me,{children:[e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"Chi-square Test:"})," Tests exact fit hypothesis. Non-significant values (p > 0.05) suggest good fit, but this test is sensitive to sample size."]}),e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"CFI (Comparative Fit Index):"})," Compares your model to a baseline model. Values ≥ 0.95 indicate excellent fit, ≥ 0.90 acceptable fit."]}),e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"TLI (Tucker-Lewis Index):"})," Similar to CFI but penalizes complex models. Values ≥ 0.95 indicate excellent fit, ≥ 0.90 acceptable fit."]}),e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"RMSEA (Root Mean Square Error of Approximation):"})," Measures approximate fit. Values ≤ 0.05 excellent, ≤ 0.08 acceptable, ≤ 0.10 mediocre."]}),e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"SRMR (Standardized Root Mean Square Residual):"})," Average standardized residual. Values ≤ 0.05 excellent, ≤ 0.08 acceptable."]})]})]}),e.jsxs(Ie,{children:[e.jsx(ze,{expandIcon:e.jsx(Ee,{}),children:e.jsx(o,{variant:"subtitle2",children:"Parameter Interpretation"})}),e.jsxs(Me,{children:[e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"Factor Loadings:"})," Represent the relationship between latent factors and observed indicators. Standardized loadings > 0.7 are considered strong, > 0.5 moderate, > 0.3 weak."]}),e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"Factor Correlations:"})," Show relationships between latent factors. High correlations (> 0.85) may suggest factors are not distinct."]}),e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"Standard Errors and p-values:"})," Indicate precision and significance of parameter estimates. Small p-values (< 0.05) suggest the parameter is significantly different from zero."]})]})]}),e.jsxs(Ie,{children:[e.jsx(ze,{expandIcon:e.jsx(Ee,{}),children:e.jsx(o,{variant:"subtitle2",children:"Model Modification"})}),e.jsxs(Me,{children:[e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"Modification Indices:"})," Suggest parameter additions that would improve fit. Only consider modifications that are theoretically justified."]}),e.jsxs(o,{variant:"body2",paragraph:!0,children:[e.jsx("strong",{children:"Expected Parameter Change:"})," Shows the approximate value the parameter would take if added to the model."]}),e.jsxs(o,{variant:"body2",children:[e.jsx("strong",{children:"Guidelines:"})," Modifications should be theory-driven, not purely data-driven. Consider parsimony and avoid overfitting."]})]})]})]})]})}),e.jsxs(Re,{open:di,onClose:()=>pe(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(ke,{children:"Model Builder"}),e.jsxs(Pe,{children:[e.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Drag and drop variables to assign them to factors. Each factor should have at least 2 indicators."}),e.jsxs(y,{container:!0,spacing:2,children:[e.jsxs(y,{item:!0,xs:12,md:6,children:[e.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Available Variables"}),e.jsx(ai,{dense:!0,children:Q.filter(i=>!m.some(a=>a.indicators.includes(i.id))).map(i=>e.jsxs(ti,{children:[e.jsx(ri,{primary:i.name}),e.jsxs(Fe,{size:"small",value:"",displayEmpty:!0,onChange:a=>{a.target.value&&Ci(a.target.value,i.id)},children:[e.jsx(q,{value:"",disabled:!0,children:"Assign to factor"}),m.map(a=>e.jsx(q,{value:a.id,children:a.name},a.id))]})]},i.id))})]}),e.jsxs(y,{item:!0,xs:12,md:6,children:[e.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Factor Structure"}),m.map(i=>e.jsx(ee,{variant:"outlined",sx:{mb:2},children:e.jsxs(ie,{children:[e.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:i.name}),i.indicators.length===0?e.jsx(o,{variant:"body2",color:"text.secondary",fontStyle:"italic",children:"No indicators assigned"}):e.jsx(ai,{dense:!0,children:i.indicators.map(a=>{const n=Q.find(l=>l.id===a);return e.jsxs(ti,{children:[e.jsx(Ui,{children:e.jsx(Ke,{size:"small",onClick:()=>Ue(i.id,a),children:e.jsx(Qe,{})})}),e.jsx(ri,{primary:(n==null?void 0:n.name)||a})]},a)})})]})},i.id))]})]})]}),e.jsx(Ae,{children:e.jsx(P,{onClick:()=>pe(!1),children:"Done"})})]}),e.jsxs(Re,{open:fi,onClose:()=>xe(!1),maxWidth:"xl",fullWidth:!0,PaperProps:{sx:{height:"90vh"}},children:[e.jsxs(ke,{children:["Path Diagram",e.jsx(o,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:.5},children:"Drag elements to reposition. Use mouse wheel to zoom, shift+drag to pan. Double-click to reset view."}),e.jsx(x,{sx:{mt:1},children:e.jsx(P,{size:"small",onClick:xi,sx:{mr:1},children:"Reset Layout"})})]}),e.jsx(Pe,{sx:{height:"100%",overflow:"hidden",p:1},children:t&&e.jsx(x,{sx:{width:"100%",height:"100%",overflow:"auto",backgroundColor:"white",border:"1px solid #e0e0e0",borderRadius:1},children:e.jsx(Yi,{ref:ge,data:((Xe=We())==null?void 0:Xe.data)||[],layout:((Ye=We())==null?void 0:Ye.layout)||{},config:{displayModeBar:!0,modeBarButtonsToRemove:["select2d","lasso2d"],displaylogo:!1,responsive:!1,scrollZoom:!0,doubleClick:"reset+autosize",toImageButtonOptions:{format:"png",filename:"cfa_path_diagram",height:800,width:1200,scale:2}},style:{width:"100%",height:"100%"},useResizeHandler:!0,onHover:hi,onUnhover:pi,onClick:_i})})}),e.jsx(Ae,{children:e.jsx(P,{onClick:()=>xe(!1),children:"Close"})})]}),e.jsxs(Re,{open:mi,onClose:()=>ce(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(ke,{children:"Export Results"}),e.jsxs(Pe,{children:[e.jsxs(fe,{component:"fieldset",children:[e.jsx(ei,{component:"legend",children:"Select items to export:"}),e.jsxs(_e,{children:[e.jsx(F,{control:e.jsx(I,{checked:R.summary,onChange:i=>Y(a=>({...a,summary:i.target.checked}))}),label:"Summary Statistics"}),e.jsx(F,{control:e.jsx(I,{checked:R.modelSpecification,onChange:i=>Y(a=>({...a,modelSpecification:i.target.checked}))}),label:"Model Specification"}),e.jsx(F,{control:e.jsx(I,{checked:R.fitIndices,onChange:i=>Y(a=>({...a,fitIndices:i.target.checked}))}),label:"Fit Indices"}),e.jsx(F,{control:e.jsx(I,{checked:R.pathCoefficients,onChange:i=>Y(a=>({...a,pathCoefficients:i.target.checked}))}),label:"Path Coefficients"}),e.jsx(F,{control:e.jsx(I,{checked:R.modificationIndices,onChange:i=>Y(a=>({...a,modificationIndices:i.target.checked})),disabled:!(t!=null&&t.modification_indices)}),label:"Modification Indices"}),e.jsx(F,{control:e.jsx(I,{checked:R.residualCorrelations,onChange:i=>Y(a=>({...a,residualCorrelations:i.target.checked}))}),label:"Residual Correlations"})]})]}),e.jsx(x,{mt:2,children:e.jsx(o,{variant:"body2",color:"text.secondary",children:"Results will be exported as a formatted text file."})})]}),e.jsxs(Ae,{children:[e.jsx(P,{onClick:()=>ce(!1),children:"Cancel"}),e.jsx(P,{onClick:Si,variant:"contained",startIcon:e.jsx(ii,{}),children:"Export"})]})]})]})};export{ca as default};
