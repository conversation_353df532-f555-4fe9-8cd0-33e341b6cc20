var v=Object.defineProperty;var w=(o,e,t)=>e in o?v(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var p=(o,e,t)=>w(o,typeof e!="symbol"?e+"":e,t);const y=class y{constructor(){p(this,"questions",new Map);p(this,"answers",new Map);p(this,"sessions",new Map);p(this,"curatedSuggestions",[]);p(this,"stats");this.stats={totalQuestions:0,totalAnswers:0,validatedAnswers:0,accuracyScore:0,lastTrainingDate:new Date,sessionsCompleted:0},this.loadFromStorage()}static getInstance(){return y.instance||(y.instance=new y),y.instance}addQuestion(e){const t=this.generateId(),s={...e,id:t,createdAt:new Date,updatedAt:new Date};return this.questions.set(t,s),this.stats.totalQuestions++,this.saveToStorage(),t}getQuestion(e){return this.questions.get(e)}getAllQuestions(){return Array.from(this.questions.values())}updateQuestion(e,t){const s=this.questions.get(e);if(!s)return!1;const i={...s,...t,updatedAt:new Date};return this.questions.set(e,i),this.saveToStorage(),!0}addAnswer(e){const t=this.generateId(),s={...e,id:t,createdAt:new Date},i=this.answers.get(e.questionId)||[];return i.push(s),this.answers.set(e.questionId,i),this.stats.totalAnswers++,s.validated&&this.stats.validatedAnswers++,this.saveToStorage(),t}getAnswersForQuestion(e){return this.answers.get(e)||[]}updateAnswer(e,t){for(const[s,i]of this.answers.entries()){const a=i.findIndex(n=>n.id===e);if(a!==-1){const n={...i[a],...t,id:e,createdAt:i[a].createdAt};return i[a]=n,this.answers.set(s,i),this.saveToStorage(),!0}}return!1}deleteAnswer(e){for(const[t,s]of this.answers.entries()){const i=s.findIndex(a=>a.id===e);if(i!==-1){const a=s[i];return s.splice(i,1),this.stats.totalAnswers--,a.validated&&this.stats.validatedAnswers--,this.answers.set(t,s),this.saveToStorage(),!0}}return!1}validateAnswer(e,t){const s=this.answers.get(e);if(!s)return!1;const i=s.find(a=>a.id===t);return i?(i.validated||(i.validated=!0,this.stats.validatedAnswers++,this.saveToStorage()),!0):!1}createSession(e,t){const s=this.generateId(),i={id:s,name:e,description:t,questions:[],completedQuestions:[],createdAt:new Date,updatedAt:new Date,status:"active"};return this.sessions.set(s,i),this.saveToStorage(),s}getSession(e){return this.sessions.get(e)}getAllSessions(){return Array.from(this.sessions.values())}addQuestionToSession(e,t){const s=this.sessions.get(e);return s?(s.questions.includes(t)||(s.questions.push(t),s.updatedAt=new Date,this.saveToStorage()),!0):!1}markQuestionCompleted(e,t){const s=this.sessions.get(e);return s?(s.completedQuestions.includes(t)||(s.completedQuestions.push(t),s.updatedAt=new Date,s.completedQuestions.length===s.questions.length&&(s.status="completed",this.stats.sessionsCompleted++),this.saveToStorage()),!0):!1}addCuratedSuggestion(e){if(this.curatedSuggestions.find(i=>i.questionPattern===e.questionPattern)){console.log(`Curated suggestion already exists for: "${e.questionPattern}"`);return}const s={...e,usage_count:0,success_rate:0};this.curatedSuggestions.push(s),this.saveToStorage()}getCuratedSuggestions(){return this.curatedSuggestions}clearCuratedSuggestions(){this.curatedSuggestions=[],this.saveToStorage()}getDuplicateCuratedSuggestions(){const e={};this.curatedSuggestions.forEach(s=>{const i=s.questionPattern;e[i]=(e[i]||0)+1});const t={};return Object.entries(e).forEach(([s,i])=>{i>1&&(t[s]=i)}),t}removeDuplicateCuratedSuggestions(){const e=new Set,t=[];let s=0;return this.curatedSuggestions.forEach(i=>{e.has(i.questionPattern)?s++:(e.add(i.questionPattern),t.push(i))}),this.curatedSuggestions=t,this.saveToStorage(),s}findMatchingSuggestions(e){const t=e.toLowerCase(),s=[];return this.curatedSuggestions.forEach(i=>{let a=0,n="";i.keywords.forEach(l=>{const c=l.toLowerCase();t.includes(c)?(a+=3,n+=`Keyword match: ${l}; `):this.checkPartialMatch(t,c)?(a+=2,n+=`Partial match: ${l}; `):this.checkSynonymMatch(t,c)&&(a+=2,n+=`Synonym match: ${l}; `)}),i.regexPatterns.forEach(l=>{try{l&&typeof l.test=="function"&&l.test(e)&&(a+=4,n+="Pattern match; ")}catch(c){console.warn("Invalid regex pattern encountered:",l,c)}});const d=i.questionPattern.toLowerCase();t.includes(d)||d.includes(t)?(a+=2,n+="Question similarity; "):this.calculateSimilarity(t,d)>.6&&(a+=1,n+="Fuzzy match; ");const m=this.detectVariablePatterns(e);m>0&&(a+=m,n+="Variable pattern detected; ");const g=this.detectDataStructurePatterns(e);g>0&&(a+=g,n+="Data structure/assumption pattern detected; "),a=this.adjustScoreForContext(e,i,a),a>0&&s.push({suggestion:i,score:a,context:n})}),s.sort((i,a)=>a.score-i.score).slice(0,10).map(i=>i.suggestion)}adjustScoreForContext(e,t,s){let i=s;return/\b(non.?normal|not.?normal|skewed|non.?parametric)\b/i.test(e)&&(t.suggestions.some(a=>["NONPAR1","NONPAR2","NONPAR3","NONPAR4"].includes(a.analysisId))&&(i+=5),t.suggestions.some(a=>["TTEST1","TTEST2","TTEST3","ANOVA1","ANOVA2","ANOVA3"].includes(a.analysisId))&&(i-=2)),/\b(normal|gaussian|parametric|normally.?distributed)\b/i.test(e)&&t.suggestions.some(a=>["TTEST1","TTEST2","TTEST3","ANOVA1","ANOVA2","ANOVA3"].includes(a.analysisId))&&(i+=3),/\b(survival|time.?to.?event|kaplan.?meier|cox)\b/i.test(e)&&t.suggestions.some(a=>a.analysisId==="ADV4")&&(i+=4),/\b(factor.?analysis|reliability|cronbach|construct)\b/i.test(e)&&t.suggestions.some(a=>["ADV1","ADV3"].includes(a.analysisId))&&(i+=4),i}updateSuggestionUsage(e,t){const s=this.curatedSuggestions.find(i=>i.questionPattern===e);s&&(s.usage_count++,t?s.success_rate=(s.success_rate*(s.usage_count-1)+1)/s.usage_count:s.success_rate=s.success_rate*(s.usage_count-1)/s.usage_count,this.saveToStorage())}checkPartialMatch(e,t){const s=e.split(/\s+/);return t.split(/\s+/).some(a=>s.some(n=>n.length>3&&a.length>3&&(n.includes(a)||a.includes(n))))}checkSynonymMatch(e,t){const s={correlation:["relationship","association","connection"],comparison:["compare","difference","versus","vs"],prediction:["predict","forecast","model"],test:["analysis","examine","check"],categorical:["nominal","ordinal","factor"],continuous:["numeric","quantitative","interval","ratio"],missing:["na","null","empty","blank"],outlier:["extreme","anomaly","unusual"],"non normal":["not normal","non-normal","skewed","non-parametric","nonparametric"],"not normal":["non normal","non-normal","skewed","non-parametric","nonparametric"],skewed:["non normal","not normal","non-parametric","asymmetric"],parametric:["normal","gaussian","normally distributed"],"non-parametric":["non normal","not normal","skewed","rank-based"],survival:["time to event","kaplan meier","cox regression","hazard"],"factor analysis":["EFA","construct","latent variables","dimension reduction"],reliability:["cronbach","alpha","internal consistency","scale reliability"]};for(const[i,a]of Object.entries(s))if(t.includes(i)&&a.some(n=>e.includes(n))||e.includes(i)&&a.some(n=>t.includes(n)))return!0;return!1}calculateSimilarity(e,t){const s=new Set(e.split(/\s+/)),i=new Set(t.split(/\s+/)),a=new Set([...s].filter(d=>i.has(d))),n=new Set([...s,...i]);return a.size/n.size}detectVariablePatterns(e){let t=0;return/\b(item|q|question|var)\d+\b/i.test(e)&&(t+=2),/\b(pre|post|before|after|time[12]|t[12]|baseline|followup)\b/i.test(e)&&(t+=2),/\b(treatment|control|intervention|placebo|group[abc])\b/i.test(e)&&(t+=2),/\b(age|gender|sex|education|income|race|ethnicity)\b/i.test(e)&&(t+=1),t}detectDataStructurePatterns(e){let t=0;return/\b(randomized|controlled|trial|experiment|rct)\b/i.test(e)&&(t+=2),/\b(survey|questionnaire|likert|scale|rating)\b/i.test(e)&&(t+=2),/\b(longitudinal|repeated|panel|time series|cohort)\b/i.test(e)&&(t+=2),/\b(patient|clinical|medical|diagnosis|treatment|outcome)\b/i.test(e)&&(t+=1),/\b(non.?normal|not.?normal|skewed|non.?parametric|nonparametric)\b/i.test(e)&&(t+=3),/\b(normal|gaussian|parametric|normally.?distributed)\b/i.test(e)&&(t+=2),/\b(survival|time.?to.?event|kaplan.?meier|cox|hazard)\b/i.test(e)&&(t+=3),/\b(factor.?analysis|reliability|cronbach|alpha|construct)\b/i.test(e)&&(t+=3),/\b(cluster|grouping|k.?means|hierarchical)\b/i.test(e)&&(t+=2),t}getStats(){return this.stats.accuracyScore=this.stats.totalAnswers>0?this.stats.validatedAnswers/this.stats.totalAnswers*100:0,{...this.stats}}saveToStorage(){try{const e={questions:Array.from(this.questions.entries()),answers:Array.from(this.answers.entries()),sessions:Array.from(this.sessions.entries()),curatedSuggestions:this.curatedSuggestions,stats:this.stats};localStorage.setItem("analysisAssistantTrainingData",JSON.stringify(e))}catch(e){console.error("Failed to save training data:",e)}}loadFromStorage(){try{const e=localStorage.getItem("analysisAssistantTrainingData");if(e){const t=JSON.parse(e);this.questions=new Map(t.questions||[]),this.answers=new Map(t.answers||[]),this.sessions=new Map(t.sessions||[]),this.curatedSuggestions=(t.curatedSuggestions||[]).map(s=>({...s,regexPatterns:(s.regexPatterns||[]).map(i=>{try{return i instanceof RegExp?i:typeof i=="object"&&i.source?new RegExp(i.source,i.flags||"i"):typeof i=="string"?new RegExp(i,"i"):new RegExp("(?!)","i")}catch(a){return console.warn("Failed to reconstruct regex pattern:",i,a),new RegExp("(?!)","i")}})})),this.stats=t.stats||this.stats}}catch(e){console.error("Failed to load training data:",e)}}generateId(){return"train_"+Date.now().toString(36)+Math.random().toString(36).substring(2)}exportTrainingData(){const e={questions:Array.from(this.questions.entries()),answers:Array.from(this.answers.entries()),sessions:Array.from(this.sessions.entries()),curatedSuggestions:this.curatedSuggestions,stats:this.stats,exportDate:new Date().toISOString()};return JSON.stringify(e,null,2)}importTrainingData(e){try{const t=JSON.parse(e);return this.questions=new Map(t.questions||[]),this.answers=new Map(t.answers||[]),this.sessions=new Map(t.sessions||[]),this.curatedSuggestions=(t.curatedSuggestions||[]).map(s=>({...s,regexPatterns:(s.regexPatterns||[]).map(i=>{try{return i instanceof RegExp?i:typeof i=="object"&&i.source?new RegExp(i.source,i.flags||"i"):typeof i=="string"?new RegExp(i,"i"):new RegExp("(?!)","i")}catch(a){return console.warn("Failed to reconstruct regex pattern:",i,a),new RegExp("(?!)","i")}})})),this.stats=t.stats||this.stats,this.saveToStorage(),!0}catch(t){return console.error("Failed to import training data:",t),!1}}clearAllData(){this.questions.clear(),this.answers.clear(),this.sessions.clear(),this.curatedSuggestions=[],this.stats={totalQuestions:0,totalAnswers:0,validatedAnswers:0,accuracyScore:0,lastTrainingDate:new Date,sessionsCompleted:0},this.saveToStorage()}};p(y,"instance");let h=y;const r=h.getInstance(),f=[{question:{question:"How do I test if age is correlated with test scores?",keywords:["correlation","age","test scores","relationship","association"],patterns:["correlat.*age.*test.*scores","age.*correlat.*test","test.*scores.*correlat.*age"],category:"correlation",difficulty:"basic",context:"Testing correlation between two continuous variables"},answers:[{analysisId:"CORR1",priority:"high",reason:"Correlation Matrix is the primary tool for examining relationships between continuous variables like age and test scores. Use Pearson correlation for linear relationships.",validated:!0},{analysisId:"VIZ4",priority:"high",reason:"Scatter Plot provides visual representation of the relationship between age and test scores, helping identify linear or non-linear patterns.",validated:!0},{analysisId:"REG1",priority:"medium",reason:"Linear Regression can model the relationship and predict test scores based on age, providing additional insights beyond correlation.",validated:!0}]},{question:{question:"Test correlation between variables",keywords:["correlation","variables","relationship","association","pearson","spearman"],patterns:["test.*correlation","correlation.*test","correlation.*between.*variables"],category:"correlation",difficulty:"basic",context:"General correlation analysis between variables"},answers:[{analysisId:"CORR1",priority:"high",reason:"Correlation Matrix calculates Pearson, Spearman, or Kendall correlations between variables, providing comprehensive correlation analysis.",validated:!0},{analysisId:"VIZ4",priority:"high",reason:"Scatter Plot visualizes the relationship between two variables, helping identify the nature and strength of correlation.",validated:!0}]},{question:{question:"Which variables are related to each other?",keywords:["related","variables","relationships","associations","connected","linked"],patterns:["which.*variables.*related","variables.*related.*each.*other","find.*relationships.*variables"],category:"correlation",difficulty:"basic",context:"Exploring relationships between multiple variables"},answers:[{analysisId:"CORR1",priority:"high",reason:"Correlation Matrix shows relationships between all variables simultaneously, helping identify which variables are related.",validated:!0},{analysisId:"VIZ5",priority:"medium",reason:"Correlation Heatmap provides visual representation of relationships between multiple variables.",validated:!0}]},{question:{question:"What test for categorical data?",keywords:["categorical","test","nominal","ordinal","chi-square"],patterns:["categorical.*data.*test","test.*categorical","nominal.*test","ordinal.*test"],category:"categorical",difficulty:"basic",context:"Analyzing categorical variables and their relationships"},answers:[{analysisId:"CAT1",priority:"high",reason:"Chi-Square Test is the primary test for categorical data, used to test independence between categorical variables or goodness of fit.",validated:!0},{analysisId:"DESC2",priority:"high",reason:"Frequency Tables provide descriptive analysis of categorical data, showing counts and percentages for each category.",validated:!0},{analysisId:"DESC3",priority:"medium",reason:"Cross-Tabulation examines relationships between two categorical variables, often used before Chi-Square testing.",validated:!0}]},{question:{question:"How to test association between categorical variables?",keywords:["association","categorical","contingency","chi-square","independence"],patterns:["association.*categorical","categorical.*association","contingency.*table"],category:"categorical",difficulty:"intermediate",context:"Testing relationships between categorical variables"},answers:[{analysisId:"CAT1",priority:"high",reason:"Chi-Square Test of Independence tests whether two categorical variables are associated.",validated:!0},{analysisId:"DESC3",priority:"high",reason:"Cross-Tabulation creates contingency tables to examine the relationship between categorical variables.",validated:!0}]},{question:{question:"Analyze frequency distribution of categories",keywords:["frequency","distribution","categories","counts","percentages"],patterns:["frequency.*distribution","analyze.*frequency","category.*counts"],category:"categorical",difficulty:"basic",context:"Examining distribution of categorical variables"},answers:[{analysisId:"DESC2",priority:"high",reason:"Frequency Tables show the distribution of categorical variables with counts and percentages.",validated:!0},{analysisId:"VIZ1",priority:"high",reason:"Bar Charts provide visual representation of frequency distributions for categorical data.",validated:!0}]},{question:{question:"How do I compare means between two groups? My data is non normal",keywords:["compare","means","two groups","non normal","not normal","skewed","non-parametric"],patterns:["compare.*means.*two.*groups.*non.*normal","two.*groups.*not.*normal","compare.*groups.*skewed","non.*normal.*two.*groups"],category:"comparison",difficulty:"intermediate",context:"Comparing two groups when normality assumption is violated"},answers:[{analysisId:"NONPAR1",priority:"high",reason:"Mann-Whitney U Test is the non-parametric alternative to independent t-test when data is not normally distributed.",validated:!0},{analysisId:"VIZ2",priority:"medium",reason:"Box Plots help visualize the distribution differences between groups when data is non-normal.",validated:!0},{analysisId:"DESC4",priority:"low",reason:"Normality Test can confirm the non-normal distribution before choosing non-parametric alternatives.",validated:!0}]},{question:{question:"Compare paired data but my data is not normally distributed",keywords:["paired","pre post","before after","non normal","not normal","skewed","non-parametric"],patterns:["paired.*non.*normal","pre.*post.*not.*normal","before.*after.*skewed","paired.*skewed"],category:"comparison",difficulty:"intermediate",context:"Comparing paired measurements when normality assumption is violated"},answers:[{analysisId:"NONPAR2",priority:"high",reason:"Wilcoxon Signed-Rank Test is the non-parametric alternative to paired t-test when data is not normally distributed.",validated:!0},{analysisId:"VIZ2",priority:"medium",reason:"Box Plots can visualize the distribution of differences in paired data.",validated:!0}]},{question:{question:"Compare multiple groups but data is not normal",keywords:["multiple groups","three groups","non normal","not normal","skewed","kruskal wallis"],patterns:["multiple.*groups.*non.*normal","three.*groups.*not.*normal","groups.*skewed","non.*normal.*anova"],category:"comparison",difficulty:"intermediate",context:"Comparing multiple groups when normality assumption is violated"},answers:[{analysisId:"NONPAR3",priority:"high",reason:"Kruskal-Wallis Test is the non-parametric alternative to one-way ANOVA when data is not normally distributed.",validated:!0},{analysisId:"VIZ2",priority:"medium",reason:"Box Plots help visualize distribution differences across multiple groups when data is non-normal.",validated:!0}]},{question:{question:"How to compare means between two groups?",keywords:["compare","means","two groups","t-test","independent"],patterns:["compare.*means.*two.*groups","two.*groups.*means","means.*between.*groups"],category:"comparison",difficulty:"basic",context:"Comparing continuous outcomes between two independent groups"},answers:[{analysisId:"TTEST2",priority:"high",reason:"Independent T-Test is the standard test for comparing means between two independent groups when data is normally distributed.",validated:!0},{analysisId:"NONPAR1",priority:"medium",reason:"Mann-Whitney U Test is the non-parametric alternative when data is not normally distributed or when dealing with ordinal data.",validated:!0},{analysisId:"DESC1",priority:"low",reason:"Descriptive Analysis should be performed first to examine the distribution and characteristics of each group.",validated:!0}]},{question:{question:"Compare pre and post measurements",keywords:["pre","post","measurements","paired","before","after"],patterns:["pre.*post","before.*after","paired.*measurements"],category:"comparison",difficulty:"basic",context:"Comparing measurements from the same subjects at different time points"},answers:[{analysisId:"TTEST3",priority:"high",reason:"Paired T-Test is designed specifically for comparing measurements from the same subjects at two different time points.",validated:!0},{analysisId:"NONPAR2",priority:"medium",reason:"Wilcoxon Signed-Rank Test is the non-parametric alternative for paired data when normality assumptions are violated.",validated:!0}]},{question:{question:"Which variables should be predictors and which is the outcome?",keywords:["predictors","outcome","dependent","independent","variables","target"],patterns:["predictors.*outcome","dependent.*independent","target.*variable"],category:"other",difficulty:"intermediate",context:"Identifying variable roles for analysis"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis helps understand variable characteristics to identify potential predictors and outcomes.",validated:!0},{analysisId:"CORR1",priority:"high",reason:"Correlation Matrix shows relationships between variables, helping identify which variables might predict others.",validated:!0}]},{question:{question:"What analysis is appropriate for my data structure?",keywords:["data structure","appropriate analysis","what analysis","data type"],patterns:["appropriate.*analysis","what.*analysis.*data","data.*structure.*analysis"],category:"other",difficulty:"intermediate",context:"Selecting analysis based on data characteristics"},answers:[{analysisId:"DESC1",priority:"high",reason:"Start with Descriptive Analysis to understand your data structure, variable types, and distributions.",validated:!0},{analysisId:"CORR1",priority:"medium",reason:"Correlation Matrix helps understand relationships between variables in your dataset.",validated:!0}]},{question:{question:"Analyze scale reliability and factor structure",keywords:["scale","reliability","factor","cronbach","alpha","factor analysis"],patterns:["scale.*reliability","factor.*analysis","cronbach.*alpha"],category:"other",difficulty:"advanced",context:"Psychometric analysis of scales and questionnaires"},answers:[{analysisId:"REL1",priority:"high",reason:"Reliability Analysis calculates Cronbach's alpha and other reliability measures for scales.",validated:!0},{analysisId:"FACTOR1",priority:"high",reason:"Factor Analysis explores the underlying structure of scales and questionnaires.",validated:!0}]},{question:{question:"I have survey data with Likert scales",keywords:["survey","likert","scales","questionnaire","rating"],patterns:["survey.*data","likert.*scales","questionnaire.*data"],category:"other",difficulty:"intermediate",context:"Analyzing survey and questionnaire data"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides appropriate statistics for Likert scale data including medians and frequency distributions.",validated:!0},{analysisId:"REL1",priority:"high",reason:"Reliability Analysis assesses internal consistency of Likert scales using Cronbach's alpha.",validated:!0},{analysisId:"NONPAR1",priority:"medium",reason:"Non-parametric tests are often more appropriate for ordinal Likert scale data.",validated:!0}]},{question:{question:"I have experimental data with treatment groups",keywords:["experimental","treatment","groups","intervention","control"],patterns:["experimental.*data","treatment.*groups","intervention.*control"],category:"comparison",difficulty:"intermediate",context:"Analyzing experimental and intervention studies"},answers:[{analysisId:"TTEST2",priority:"high",reason:"Independent T-Test compares outcomes between treatment and control groups in experimental studies.",validated:!0},{analysisId:"ANOVA1",priority:"high",reason:"One-Way ANOVA compares outcomes across multiple treatment groups.",validated:!0},{analysisId:"DESC1",priority:"medium",reason:"Descriptive Analysis should be performed first to examine baseline characteristics of treatment groups.",validated:!0}]},{question:{question:"Is my data normally distributed?",keywords:["normal","distribution","normality","shapiro","kolmogorov","gaussian"],patterns:["normal.*distribut","normality.*test","data.*normal","gaussian.*distribut"],category:"test",difficulty:"basic",context:"Testing assumptions for parametric tests"},answers:[{analysisId:"DESC4",priority:"high",reason:"Normality Test (Shapiro-Wilk, Kolmogorov-Smirnov) formally tests whether data follows a normal distribution.",validated:!0},{analysisId:"VIZ3",priority:"high",reason:"Histograms provide visual assessment of distribution shape and normality.",validated:!0},{analysisId:"DESC1",priority:"medium",reason:"Descriptive statistics including skewness and kurtosis provide numerical indicators of distribution shape.",validated:!0}]},{question:{question:"Test assumptions for statistical tests",keywords:["assumptions","statistical tests","normality","homogeneity","independence"],patterns:["test.*assumptions","assumptions.*statistical","check.*assumptions","validate.*assumptions"],category:"test",difficulty:"intermediate",context:"Verifying statistical assumptions before analysis"},answers:[{analysisId:"DESC4",priority:"high",reason:"Normality Test checks if data meets the normality assumption required for parametric tests.",validated:!0},{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides comprehensive assumption checking including normality, outliers, and distribution characteristics.",validated:!0},{analysisId:"VIZ3",priority:"medium",reason:"Histograms help visually assess normality and other distributional assumptions.",validated:!0}]},{question:{question:"Check data quality and missing values",keywords:["data quality","missing values","missing data","quality assessment","data cleaning"],patterns:["data.*quality","missing.*values","missing.*data","quality.*assessment"],category:"descriptive",difficulty:"basic",context:"Assessing data quality and completeness"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides comprehensive data quality assessment including missing value patterns and basic statistics.",validated:!0},{analysisId:"VIZ2",priority:"medium",reason:"Histograms help identify data distribution issues and potential quality problems.",validated:!0}]},{question:{question:"Detect outliers in my data",keywords:["outliers","outlier detection","extreme values","anomalies"],patterns:["detect.*outliers","outlier.*detection","extreme.*values","find.*outliers"],category:"descriptive",difficulty:"intermediate",context:"Identifying unusual or extreme values in the dataset"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis includes outlier detection using IQR method and z-scores to identify extreme values.",validated:!0},{analysisId:"VIZ3",priority:"high",reason:"Box Plots are excellent for visualizing outliers and understanding data distribution.",validated:!0},{analysisId:"VIZ4",priority:"medium",reason:"Scatter Plots can help identify outliers in bivariate relationships.",validated:!0}]},{question:{question:"Summarize my data",keywords:["summarize","summary","descriptive","statistics","overview"],patterns:["summarize.*data","data.*summary","descriptive.*statistics"],category:"descriptive",difficulty:"basic",context:"Getting an overview of dataset characteristics"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides comprehensive summary statistics including central tendency, variability, and distribution shape.",validated:!0},{analysisId:"DESC2",priority:"medium",reason:"Frequency Tables summarize categorical variables with counts and percentages.",validated:!0},{analysisId:"VIZ2",priority:"medium",reason:"Histograms provide visual summary of data distributions.",validated:!0}]},{question:{question:"Explore data patterns and distributions",keywords:["explore","patterns","distributions","data exploration","understand data"],patterns:["explore.*data","data.*patterns","understand.*data","data.*exploration"],category:"descriptive",difficulty:"basic",context:"Initial data exploration and pattern identification"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis reveals data patterns, distributions, and potential issues in your dataset.",validated:!0},{analysisId:"VIZ2",priority:"high",reason:"Histograms show the shape and distribution of continuous variables.",validated:!0},{analysisId:"CORR1",priority:"medium",reason:"Correlation Matrix reveals relationships and patterns between variables.",validated:!0}]},{question:{question:"Compare pre and post measurements",keywords:["pre","post","measurements","paired","before","after"],patterns:["pre.*post","before.*after","paired.*measurements"],category:"comparison",difficulty:"basic",context:"Comparing measurements from the same subjects at different time points"},answers:[{analysisId:"TTEST3",priority:"high",reason:"Paired T-Test is designed specifically for comparing measurements from the same subjects at two different time points.",validated:!0},{analysisId:"NONPAR2",priority:"medium",reason:"Wilcoxon Signed-Rank Test is the non-parametric alternative for paired data when normality assumptions are violated.",validated:!0}]},{question:{question:"Predict binary outcome",keywords:["predict","binary","logistic","classification","yes no"],patterns:["predict.*binary","binary.*outcome","logistic.*regression"],category:"prediction",difficulty:"intermediate",context:"Predicting binary/dichotomous outcomes"},answers:[{analysisId:"REG2",priority:"high",reason:"Logistic Regression is specifically designed for predicting binary outcomes and provides odds ratios.",validated:!0}]},{question:{question:"Predict continuous outcome from multiple variables",keywords:["predict","continuous","multiple variables","linear regression","model"],patterns:["predict.*continuous","multiple.*variables.*predict","linear.*regression"],category:"prediction",difficulty:"intermediate",context:"Predicting continuous outcomes using multiple predictors"},answers:[{analysisId:"REG1",priority:"high",reason:"Linear Regression models continuous outcomes using multiple predictor variables.",validated:!0},{analysisId:"CORR1",priority:"medium",reason:"Correlation Matrix helps identify which variables are most strongly related to the outcome.",validated:!0}]},{question:{question:"Build predictive model",keywords:["build","predictive model","modeling","prediction","machine learning"],patterns:["build.*model","predictive.*model","machine.*learning"],category:"prediction",difficulty:"advanced",context:"Creating predictive models from data"},answers:[{analysisId:"REG1",priority:"high",reason:"Linear Regression is a fundamental predictive modeling technique for continuous outcomes.",validated:!0},{analysisId:"REG2",priority:"high",reason:"Logistic Regression is used for binary classification and prediction problems.",validated:!0}]},{question:{question:"Analyze survival data",keywords:["survival","time to event","kaplan","meier","hazard","cox regression","censored"],patterns:["survival.*data","time.*to.*event","kaplan.*meier","cox.*regression","hazard.*ratio"],category:"other",difficulty:"advanced",context:"Time-to-event analysis with censoring"},answers:[{analysisId:"ADV4",priority:"high",reason:"Survival Analysis provides comprehensive time-to-event analysis including Kaplan-Meier curves and Cox proportional hazards regression.",validated:!0}]},{question:{question:"Perform factor analysis on my questionnaire data",keywords:["factor analysis","questionnaire","scale","construct","latent variables","EFA"],patterns:["factor.*analysis","questionnaire.*factor","scale.*factor","construct.*validity"],category:"other",difficulty:"advanced",context:"Exploring underlying factor structure in questionnaire data"},answers:[{analysisId:"ADV1",priority:"high",reason:"Exploratory Factor Analysis identifies underlying factors and constructs in questionnaire or scale data.",validated:!0},{analysisId:"ADV3",priority:"medium",reason:"Reliability Analysis should be performed first to assess internal consistency of scales before factor analysis.",validated:!0}]},{question:{question:"Test reliability of my scale",keywords:["reliability","cronbach","alpha","internal consistency","scale reliability"],patterns:["reliability.*analysis","cronbach.*alpha","internal.*consistency","scale.*reliability"],category:"other",difficulty:"intermediate",context:"Assessing internal consistency and reliability of measurement scales"},answers:[{analysisId:"ADV3",priority:"high",reason:"Reliability Analysis calculates Cronbach's alpha and other reliability measures to assess internal consistency of scales.",validated:!0}]},{question:{question:"Perform cluster analysis to group my data",keywords:["cluster analysis","grouping","k-means","hierarchical","segmentation"],patterns:["cluster.*analysis","group.*data","k.*means","hierarchical.*cluster"],category:"other",difficulty:"advanced",context:"Grouping similar observations or cases"},answers:[{analysisId:"ADV5",priority:"high",reason:"Cluster Analysis groups similar data points using methods like K-means or hierarchical clustering.",validated:!0},{analysisId:"DESC1",priority:"medium",reason:"Descriptive Analysis should be performed first to understand variable distributions before clustering.",validated:!0}]},{question:{question:"Conduct meta-analysis of multiple studies",keywords:["meta analysis","meta-analysis","systematic review","effect size","forest plot"],patterns:["meta.*analysis","systematic.*review","effect.*size.*synthesis","forest.*plot"],category:"other",difficulty:"advanced",context:"Synthesizing findings from multiple research studies"},answers:[{analysisId:"ADV6",priority:"high",reason:"Meta Analysis synthesizes findings from multiple studies and provides pooled effect size estimates.",validated:!0}]},{question:{question:"What test should I use for multiple groups?",keywords:["multiple groups","anova","kruskal","wallis","three groups"],patterns:["multiple.*groups","three.*groups","more.*than.*two.*groups"],category:"comparison",difficulty:"intermediate",context:"Comparing outcomes across three or more groups"},answers:[{analysisId:"ANOVA1",priority:"high",reason:"One-Way ANOVA is used to compare means across three or more independent groups when data is normally distributed.",validated:!0},{analysisId:"NONPAR3",priority:"medium",reason:"Kruskal-Wallis Test is the non-parametric alternative for comparing multiple groups when normality assumptions are violated.",validated:!0},{analysisId:"POST1",priority:"medium",reason:"Post-hoc tests are needed after ANOVA to determine which specific groups differ from each other.",validated:!0}]},{question:{question:"Calculate odds ratio for case-control study",keywords:["odds ratio","case control","case-control","epidemiology","retrospective"],patterns:["odds.*ratio","case.*control","epidemiolog","retrospective.*study"],category:"other",difficulty:"intermediate",context:"Epidemiological analysis for case-control studies"},answers:[{analysisId:"EPI1",priority:"high",reason:"Case-Control Calculator computes odds ratios and confidence intervals for case-control study designs.",validated:!0}]},{question:{question:"Calculate risk ratio for cohort study",keywords:["risk ratio","cohort","prospective","incidence","follow-up"],patterns:["risk.*ratio","cohort.*study","prospective.*study","incidence.*rate"],category:"other",difficulty:"intermediate",context:"Epidemiological analysis for cohort studies"},answers:[{analysisId:"EPI2",priority:"high",reason:"Cohort Calculator computes risk ratios and rate ratios for prospective cohort studies.",validated:!0}]},{question:{question:"Calculate sample size for my study",keywords:["sample size","power analysis","study planning","power calculation"],patterns:["sample.*size","power.*analysis","study.*planning","power.*calculation"],category:"other",difficulty:"intermediate",context:"Planning study sample size and statistical power"},answers:[{analysisId:"SS1",priority:"high",reason:"One Sample Calculator determines appropriate sample size for single group studies.",validated:!0},{analysisId:"SS2",priority:"high",reason:"Two Sample Calculator determines sample size for comparing two groups.",validated:!0}]},{question:{question:"Power analysis for comparing two groups",keywords:["power analysis","two groups","sample size","effect size","comparison"],patterns:["power.*analysis.*two.*groups","sample.*size.*comparison","effect.*size.*power"],category:"other",difficulty:"intermediate",context:"Power analysis for group comparison studies"},answers:[{analysisId:"SS2",priority:"high",reason:"Two Sample Calculator provides power analysis and sample size calculations for comparing two groups.",validated:!0}]},{question:{question:"How to visualize my data?",keywords:["visualize","plot","chart","graph","visualization"],patterns:["visualize.*data","plot.*data","chart.*data","graph.*data"],category:"visualization",difficulty:"basic",context:"Creating appropriate visualizations for data"},answers:[{analysisId:"VIZ2",priority:"high",reason:"Histograms are excellent for visualizing the distribution of continuous variables.",validated:!0},{analysisId:"VIZ1",priority:"high",reason:"Bar Charts are ideal for displaying categorical data and frequency distributions.",validated:!0},{analysisId:"VIZ4",priority:"medium",reason:"Scatter Plots show relationships between two continuous variables.",validated:!0}]},{question:{question:"Create publication-ready tables and figures",keywords:["publication","tables","figures","manuscript","journal"],patterns:["publication.*ready","manuscript.*tables","journal.*figures"],category:"visualization",difficulty:"advanced",context:"Preparing results for academic publication"},answers:[{analysisId:"PUB1",priority:"high",reason:"Table 1 Generator creates publication-ready descriptive statistics tables.",validated:!0},{analysisId:"PUB2",priority:"high",reason:"Results tables provide formatted output suitable for manuscripts.",validated:!0}]},{question:{question:"Create Table 1 for my manuscript",keywords:["table 1","baseline characteristics","demographics","manuscript","publication"],patterns:["table.*1","baseline.*characteristics","demographics.*table","manuscript.*table"],category:"other",difficulty:"basic",context:"Creating publication-ready baseline characteristics table"},answers:[{analysisId:"PUB1",priority:"high",reason:"Table 1 Generator creates publication-ready baseline characteristics tables with proper formatting.",validated:!0}]},{question:{question:"Format regression results for publication",keywords:["regression table","publication","manuscript","coefficients","odds ratios"],patterns:["regression.*table","format.*regression","publication.*regression","manuscript.*results"],category:"other",difficulty:"intermediate",context:"Creating publication-ready regression results tables"},answers:[{analysisId:"PUB4",priority:"high",reason:"Regression Table formats regression coefficients, odds ratios, and confidence intervals for publication.",validated:!0}]},{question:{question:"Calculate standardized mean differences",keywords:["SMD","standardized mean difference","effect size","cohen's d","balance"],patterns:["standardized.*mean.*difference","SMD","effect.*size","cohen.*d"],category:"other",difficulty:"intermediate",context:"Calculating effect sizes and standardized mean differences"},answers:[{analysisId:"PUB3",priority:"high",reason:"SMD Table calculates standardized mean differences and effect sizes for group comparisons.",validated:!0}]},{question:{question:"I have variables with similar names like item1, item2, item3",keywords:["similar names","item1","item2","scale items","numbered variables"],patterns:["item\\d+","similar.*names","numbered.*variables","scale.*items"],category:"other",difficulty:"intermediate",context:"Analyzing variables with systematic naming patterns"},answers:[{analysisId:"REL1",priority:"high",reason:"Variables with similar names often represent scale items that should be analyzed for reliability using Cronbach's alpha.",validated:!0},{analysisId:"FACTOR1",priority:"medium",reason:"Factor Analysis can explore whether similarly named items measure the same underlying construct.",validated:!0},{analysisId:"DESC1",priority:"medium",reason:"Descriptive Analysis helps understand the distribution and characteristics of scale items.",validated:!0}]},{question:{question:"I have pre/post or before/after variables",keywords:["pre","post","before","after","time1","time2","baseline","followup"],patterns:["pre.*post","before.*after","time\\d+","baseline.*followup"],category:"comparison",difficulty:"basic",context:"Analyzing repeated measures or longitudinal data"},answers:[{analysisId:"TTEST3",priority:"high",reason:"Paired T-Test is specifically designed for comparing pre/post or before/after measurements.",validated:!0},{analysisId:"NONPAR2",priority:"medium",reason:"Wilcoxon Signed-Rank Test is the non-parametric alternative for paired comparisons.",validated:!0}]},{question:{question:"My data has many missing values",keywords:["missing values","missing data","incomplete data","na values"],patterns:["missing.*values","missing.*data","incomplete.*data","na.*values"],category:"descriptive",difficulty:"intermediate",context:"Handling datasets with substantial missing data"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides comprehensive missing data analysis including patterns and percentages.",validated:!0}]},{question:{question:"Check if my data meets assumptions for statistical tests",keywords:["assumptions","statistical tests","normality","homogeneity","independence"],patterns:["assumptions.*tests","test.*assumptions","normality.*assumptions"],category:"test",difficulty:"intermediate",context:"Verifying statistical assumptions before analysis"},answers:[{analysisId:"NORM1",priority:"high",reason:"Normality Tests check if data meets the normality assumption required for parametric tests.",validated:!0},{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides information about data distribution and potential assumption violations.",validated:!0},{analysisId:"VIZ2",priority:"medium",reason:"Histograms and Q-Q plots help visually assess normality assumptions.",validated:!0}]}],b=()=>{r.getAllQuestions().length===0&&(console.log("Initializing Analysis Assistant training data..."),f.forEach(e=>{const t=r.addQuestion(e.question);e.answers.forEach(s=>{r.addAnswer({...s,questionId:t})})}),console.log(`Initialized ${f.length} training questions with answers`))},q=()=>{if(r.getAllQuestions().some(s=>s.question==="Analyze longitudinal data with repeated measures"||s.question==="Compare multiple treatments with control group")){console.log("Specific training questions already exist, skipping addition");return}console.log("Adding specific training questions..."),[{question:{question:"Analyze longitudinal data with repeated measures",keywords:["longitudinal","repeated measures","time series","panel data","within subjects"],patterns:["longitudinal.*data","repeated.*measures","within.*subjects","time.*series"],category:"comparison",difficulty:"advanced",context:"Analyzing data with multiple time points or repeated observations"},answers:[{analysisId:"ANOVA3",priority:"high",reason:"Repeated Measures ANOVA is designed for analyzing data with multiple measurements from the same subjects over time.",validated:!0},{analysisId:"TTEST3",priority:"medium",reason:"Paired T-Test can be used for simple before/after comparisons within the longitudinal design.",validated:!0}]},{question:{question:"Compare multiple treatments with control group",keywords:["multiple treatments","control group","treatment comparison","intervention study"],patterns:["multiple.*treatments","treatment.*control","intervention.*study"],category:"comparison",difficulty:"intermediate",context:"Comparing several treatment conditions against a control"},answers:[{analysisId:"ANOVA1",priority:"high",reason:"One-Way ANOVA compares means across multiple treatment groups including control.",validated:!0},{analysisId:"POST1",priority:"high",reason:"Post-hoc tests identify which specific treatments differ from control and from each other.",validated:!0}]},{question:{question:"Analyze dose-response relationships",keywords:["dose response","dose effect","concentration","dosage","gradient"],patterns:["dose.*response","dose.*effect","concentration.*effect"],category:"correlation",difficulty:"advanced",context:"Examining relationships between dose/concentration and response"},answers:[{analysisId:"CORR1",priority:"high",reason:"Correlation analysis examines the strength and direction of dose-response relationships.",validated:!0},{analysisId:"REG1",priority:"high",reason:"Linear Regression models dose-response relationships and can predict responses at different doses.",validated:!0},{analysisId:"VIZ4",priority:"medium",reason:"Scatter plots visualize dose-response curves and help identify linear or non-linear relationships.",validated:!0}]},{question:{question:"Analyze diagnostic test performance",keywords:["diagnostic test","sensitivity","specificity","roc curve","accuracy"],patterns:["diagnostic.*test","sensitivity.*specificity","roc.*curve","test.*accuracy"],category:"test",difficulty:"advanced",context:"Evaluating the performance of diagnostic or screening tests"},answers:[{analysisId:"ROC1",priority:"high",reason:"ROC Analysis evaluates diagnostic test performance including sensitivity, specificity, and AUC.",validated:!0},{analysisId:"DESC3",priority:"medium",reason:"Cross-tabulation creates confusion matrices for diagnostic test evaluation.",validated:!0}]}].forEach(s=>{const i=r.addQuestion(s.question);s.answers.forEach(a=>{r.addAnswer({...a,questionId:i})})})},A=()=>{if(r.getAllQuestions().some(s=>s.question==="Assess overall data quality"||s.question==="Identify data inconsistencies")){console.log("Data quality training questions already exist, skipping addition");return}console.log("Adding data quality assessment training questions..."),[{question:{question:"Assess overall data quality",keywords:["data quality","quality assessment","data validation","data integrity"],patterns:["assess.*quality","data.*quality.*assessment","quality.*check"],category:"descriptive",difficulty:"intermediate",context:"Comprehensive evaluation of dataset quality and reliability"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides comprehensive data quality assessment including missing values, outliers, and distribution analysis.",validated:!0}]},{question:{question:"Identify data inconsistencies",keywords:["inconsistencies","data errors","data validation","consistency check"],patterns:["inconsistenc","data.*errors","validation.*check","consistency.*check"],category:"descriptive",difficulty:"intermediate",context:"Finding and documenting data inconsistencies and errors"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis includes consistency checks and identifies potential data entry errors and inconsistencies.",validated:!0}]},{question:{question:"Analyze missing data patterns",keywords:["missing data patterns","missing data analysis","missingness","data completeness"],patterns:["missing.*patterns","missing.*data.*analysis","data.*completeness"],category:"descriptive",difficulty:"intermediate",context:"Understanding patterns and mechanisms of missing data"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis provides detailed missing data analysis including patterns, percentages, and recommendations for handling missing values.",validated:!0}]},{question:{question:"Validate data types and formats",keywords:["data types","data validation","format validation","type checking"],patterns:["data.*types.*validation","format.*validation","type.*checking"],category:"descriptive",difficulty:"basic",context:"Ensuring data types are appropriate and consistent"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis includes data type validation and identifies variables that may need type conversion or cleaning.",validated:!0}]},{question:{question:"Check for duplicate records",keywords:["duplicate records","duplicates","duplicate detection","data deduplication"],patterns:["duplicate.*records","duplicate.*detection","data.*deduplication"],category:"descriptive",difficulty:"basic",context:"Identifying and handling duplicate observations in the dataset"},answers:[{analysisId:"DESC1",priority:"high",reason:"Descriptive Analysis includes duplicate detection and provides recommendations for handling duplicate records.",validated:!0}]}].forEach(s=>{const i=r.addQuestion(s.question);s.answers.forEach(a=>{r.addAnswer({...a,questionId:i})})})},S=()=>{const o=r.getCuratedSuggestions(),e=r.getAllQuestions();if(!(o.length===0||o.length<e.length*.8)){console.log(`Curated suggestions exist (${o.length}), skipping generation`);return}o.length>0&&(console.log(`Regenerating curated suggestions (${o.length} existing, ${e.length} questions)`),r.clearCuratedSuggestions()),console.log("Generating curated suggestions from training data..."),r.getAllQuestions().forEach(n=>{var g;const m=r.getAnswersForQuestion(n.id).filter(l=>l.validated);if(m.length>0){const l=m.map(u=>({analysisId:u.analysisId,priority:u.priority==="high"?9:u.priority==="medium"?6:3,reason:u.reason,confidence:.95})),c=((g=n.patterns)==null?void 0:g.map(u=>{try{return new RegExp(u,"i")}catch{return new RegExp(u.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"i")}}))||[];r.addCuratedSuggestion({questionPattern:n.question,keywords:n.keywords,regexPatterns:c,suggestions:l,category:n.category,validated:!0})}});const i=r.getCuratedSuggestions().length;console.log(`Generated ${i} curated suggestions`);const a=r.getCuratedSuggestions().filter(n=>n.questionPattern.toLowerCase().includes("survival")||n.keywords.some(d=>d.toLowerCase().includes("survival")));console.log(`Survival analysis suggestions: ${a.length}`),a.length>0&&a.forEach(n=>{console.log(`- Pattern: "${n.questionPattern}" -> Analysis IDs: ${n.suggestions.map(d=>d.analysisId).join(", ")}`)})},x=()=>{console.log("🚀 Initializing comprehensive Analysis Assistant training system..."),b(),console.log("✅ Core training data initialized"),q(),console.log("✅ Specific scenario training added"),A(),console.log("✅ Data quality assessment training added"),S(),console.log("✅ Curated suggestions generated");const o=r.getStats();console.log(`🎯 Analysis Assistant training system fully initialized with ${o.totalQuestions} questions and ${o.totalAnswers} answers`),console.log("🧪 Testing survival analysis query...");const e="Analyze survival data",t=r.findMatchingSuggestions(e);console.log(`Query "${e}" returned ${t.length} matches:`),t.forEach((s,i)=>{console.log(`  ${i+1}. Pattern: "${s.questionPattern}" -> ${s.suggestions.map(a=>a.analysisId).join(", ")}`)}),console.log("📊 Training Coverage Summary:"),console.log("- Basic Statistics: Descriptive, Frequency, Correlation"),console.log("- Inferential Statistics: T-tests, ANOVA, Chi-square"),console.log("- Non-parametric Tests: Mann-Whitney, Wilcoxon, Kruskal-Wallis"),console.log("- Advanced Analysis: Survival, Factor Analysis, Reliability"),console.log("- Epidemiology: Case-Control, Cohort Studies"),console.log("- Study Design: Sample Size, Power Analysis"),console.log("- Publication Tools: Table 1, Regression Tables, SMD"),console.log("- Data Quality: Missing Data, Outliers, Assumptions"),console.log("- Context-Aware: Assumption detection, Pattern recognition")};export{x as i,r as t};
