import{j as t,B as W,e as F,R as L,G as A,ai as _,b9 as H,ba as U,bb as E,ah as z,f as V,bG as Q,bH as X,D as Y}from"./mui-libs-CfwFIaTD.js";import{r as P}from"./react-libs-Cr2nE3UY.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{S as I}from"./StatsCard-op8tGQ0a.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import"./other-utils-CR9xr_gI.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";class O{static zAlpha(e){if(e===.05)return 1.96;if(e===.01)return 2.576;if(e===.1)return 1.645;const r=1-e/2;if(r<=0||r>=1)return 1.96;const l=-39.6968302866538,i=220.946098424521,m=-275.928510446969,d=138.357751867269,h=-30.6647980661472,u=2.50662827745924,b=-54.*************,v=161.585836858041,y=-155.698979859887,f=66.8013118877197,j=-13.2806815528857,x=-.00778489400243029,w=-.322396458041136,N=-2.**************,R=-2.54973253934373,C=4.37466414146497,G=2.93816398269878,M=.00778469570904146,B=.32246712907004,T=2.445134137143,q=3.75440866190742;let n=0;if(r<.02425){const o=Math.sqrt(-2*Math.log(r));n=(((((x*o+w)*o+N)*o+R)*o+C)*o+G)/((((M*o+B)*o+T)*o+q)*o+1)}else if(r<=.97575){const o=r-.5,g=o*o;n=(((((l*g+i)*g+m)*g+d)*g+h)*g+u)*o/(((((b*g+v)*g+y)*g+f)*g+j)*g+1)}else{const o=Math.sqrt(-2*Math.log(1-r));n=-(((((x*o+w)*o+N)*o+R)*o+C)*o+G)/((((M*o+B)*o+T)*o+q)*o+1)}return Math.abs(n)}static zBeta(e){if(e===.8)return .84;if(e===.9)return 1.28;if(e===.95)return 1.645;if(e===.75)return .67;if(e===.85)return 1.04;if(e<=0||e>=1)return .84;const r=-39.6968302866538,l=220.946098424521,i=-275.928510446969,m=138.357751867269,d=-30.6647980661472,h=2.50662827745924,u=-54.*************,b=161.585836858041,v=-155.698979859887,y=66.8013118877197,f=-13.2806815528857,j=-.00778489400243029,x=-.322396458041136,w=-2.**************,N=-2.54973253934373,R=4.37466414146497,C=2.93816398269878,G=.00778469570904146,M=.32246712907004,B=2.445134137143,T=3.75440866190742;let q=0;if(e<.02425){const n=Math.sqrt(-2*Math.log(e));q=(((((j*n+x)*n+w)*n+N)*n+R)*n+C)/((((G*n+M)*n+B)*n+T)*n+1)}else if(e<=.97575){const n=e-.5,o=n*n;q=(((((r*o+l)*o+i)*o+m)*o+d)*o+h)*n/(((((u*o+b)*o+v)*o+y)*o+f)*o+1)}else{const n=Math.sqrt(-2*Math.log(1-e));q=-(((((j*n+x)*n+w)*n+N)*n+R)*n+C)/((((G*n+M)*n+B)*n+T)*n+1)}return q}static sampleSizeTwoProportions(e,r,l,i,m=1){if(e===r||l<=0||l>=1||i<=0||i>=1)return;const d=this.zAlpha(l),h=this.zBeta(i),u=(e+r)/2,b=1-u,v=d*Math.sqrt(u*b*(1+1/m)),y=h*Math.sqrt(e*(1-e)+r*(1-r)/m),f=Math.pow(v+y,2),j=Math.pow(e-r,2),x=f/j,w=x*m;return{n1:Math.ceil(x),n2:Math.ceil(w),total:Math.ceil(x+w)}}static sampleSizeCaseControl(e,r,l,i,m=1){if(e<=0||e>=1||r<=0||l<=0||l>=1||i<=0||i>=1)return;const d=this.zAlpha(l),h=this.zBeta(i),u=r*e/(1+e*(r-1));if(u<=0||u>=1)return;const b=d*Math.sqrt(e*(1-e)*(1+1/m)),v=h*Math.sqrt(u*(1-u)+e*(1-e)/m),y=Math.pow(b+v,2),f=Math.pow(u-e,2),j=y/f,x=j*m;return{nCases:Math.ceil(j),nControls:Math.ceil(x),total:Math.ceil(j+x)}}static calculatePowerTwoProportions(e,r,l,i,m){if(e<=0||r<=0||l===i||m<=0||m>=1)return;const d=this.zAlpha(m),h=(l*e+i*r)/(e+r),u=1-h,b=Math.sqrt(l*(1-l)/e+i*(1-i)/r),v=(Math.abs(l-i)-d*Math.sqrt(h*u*(1/e+1/r)))/b;return this.normalCDF(v)}static calculatePowerCaseControl(e,r,l,i,m){if(e<=0||r<=0||l<=0||l>=1||i<=0||m<=0||m>=1)return;const d=this.zAlpha(m),h=i*l/(1+l*(i-1));if(h<=0||h>=1)return;const u=e+r,b=(e*h+r*l)/u,v=1-b,y=Math.sqrt(b*v*(1/e+1/r)),f=Math.sqrt(h*(1-h)/e+l*(1-l)/r),j=(Math.abs(h-l)-d*y)/f;return this.normalCDF(j)}static normalCDF(e){const r=1/(1+.2316419*Math.abs(e)),i=.3989423*Math.exp(-e*e/2)*r*(.3193815+r*(-.3565638+r*(1.781478+r*(-1.821256+r*1.330274))));return e>0?1-i:i}}const pt=()=>{const[S,e]=P.useState("twoProportions"),[r,l]=P.useState("sampleSize"),[i,m]=P.useState(.05),[d,h]=P.useState(.1),[u,b]=P.useState(.2),[v,y]=P.useState(50),[f,j]=P.useState(50),[x,w]=P.useState(1),[N,R]=P.useState(.1),[C,G]=P.useState(2),[M,B]=P.useState(50),[T,q]=P.useState(50),[n,o]=P.useState(1),[g,D]=P.useState(.8),[a,k]=P.useState(void 0),$=()=>{let s,c={alpha:i};r==="sampleSize"?(c.power=g,S==="twoProportions"?(c.p1=d,c.p2=u,c.ratio=x,s=O.sampleSizeTwoProportions(d,u,i,g,x)):S==="caseControl"&&(c.p0=N,c.oddsRatio=C,c.ratio=n,s=O.sampleSizeCaseControl(N,C,i,g,n))):S==="twoProportions"?(c.n1=v,c.n2=f,c.p1=d,c.p2=u,s=O.calculatePowerTwoProportions(v,f,d,u,i)):S==="caseControl"&&(c.nCases=M,c.nControls=T,c.p0=N,c.oddsRatio=C,s=O.calculatePowerCaseControl(M,T,N,C,i)),k({type:r,value:s,params:c})},J=()=>{e("twoProportions"),l("sampleSize"),m(.05),h(.1),b(.2),y(50),j(50),R(.1),G(2),B(50),q(50),D(.8),w(1),o(1),k(void 0)},p=(s,c=0)=>s===void 0||isNaN(s)?"N/A":s.toFixed(c),K=(s,c=1)=>s===void 0||isNaN(s)?"N/A":`${(s*100).toFixed(c)}%`;return t.jsxs(W,{children:[t.jsx(F,{variant:"h6",gutterBottom:!0,children:"Sample Size & Power Calculator for Epi Studies"}),t.jsx(F,{variant:"body1",paragraph:!0,children:"Determine the required sample size for your epidemiological study or calculate the power of an existing study."}),t.jsxs(L,{elevation:1,sx:{p:3,mb:3},children:[t.jsxs(A,{container:!0,spacing:3,children:[t.jsxs(A,{item:!0,xs:12,md:6,children:[t.jsxs(_,{fullWidth:!0,margin:"normal",children:[t.jsx(H,{id:"study-type-label",children:"Study Type"}),t.jsxs(U,{labelId:"study-type-label",value:S,label:"Study Type",onChange:s=>e(s.target.value),children:[t.jsx(E,{value:"twoProportions",children:"Comparing Two Proportions (Cohort/Cross-sectional)"}),t.jsx(E,{value:"caseControl",children:"Case-Control Study (Odds Ratio)"})]})]}),t.jsxs(_,{fullWidth:!0,margin:"normal",children:[t.jsx(H,{id:"solve-for-label",children:"Solve For"}),t.jsxs(U,{labelId:"solve-for-label",value:r,label:"Solve For",onChange:s=>l(s.target.value),children:[t.jsx(E,{value:"sampleSize",children:"Sample Size"}),t.jsx(E,{value:"power",children:"Power"})]})]}),t.jsx(z,{label:"Alpha (α) - Significance Level",type:"number",value:i,onChange:s=>m(parseFloat(s.target.value)),inputProps:{step:.01,min:.01,max:.99},fullWidth:!0,margin:"normal",helperText:"e.g., 0.05 for 95% confidence"}),r==="sampleSize"&&t.jsx(z,{label:"Desired Power (1-β)",type:"number",value:g,onChange:s=>D(parseFloat(s.target.value)),inputProps:{step:.01,min:.01,max:.99},fullWidth:!0,margin:"normal",helperText:"e.g., 0.80 for 80% power"})]}),t.jsxs(A,{item:!0,xs:12,md:6,children:[S==="twoProportions"&&t.jsxs(W,{children:[t.jsx(F,{variant:"subtitle1",gutterBottom:!0,sx:{mt:2},children:"Parameters for Comparing Two Proportions"}),t.jsx(z,{label:"Proportion in Group 1 (P1)",type:"number",value:d,onChange:s=>h(parseFloat(s.target.value)),inputProps:{step:.01,min:0,max:1},fullWidth:!0,margin:"normal",helperText:"Expected proportion in the first group (e.g., exposed)"}),t.jsx(z,{label:"Proportion in Group 2 (P2)",type:"number",value:u,onChange:s=>b(parseFloat(s.target.value)),inputProps:{step:.01,min:0,max:1},fullWidth:!0,margin:"normal",helperText:"Expected proportion in the second group (e.g., unexposed)"}),r==="power"&&t.jsxs(t.Fragment,{children:[t.jsx(z,{label:"Sample Size Group 1 (n1)",type:"number",value:v,onChange:s=>y(parseInt(s.target.value,10)),inputProps:{min:1},fullWidth:!0,margin:"normal",helperText:"Actual sample size in the first group"}),t.jsx(z,{label:"Sample Size Group 2 (n2)",type:"number",value:f,onChange:s=>j(parseInt(s.target.value,10)),inputProps:{min:1},fullWidth:!0,margin:"normal",helperText:"Actual sample size in the second group"})]}),r==="sampleSize"&&t.jsx(z,{label:"Ratio of Group 2 to Group 1 (n2/n1)",type:"number",value:x,onChange:s=>{const c=parseFloat(s.target.value);!isNaN(c)&&c>0&&w(c)},inputProps:{step:.1,min:.1},fullWidth:!0,margin:"normal",helperText:"e.g., 1 for equal groups, 2 for 2 controls per case"})]}),S==="caseControl"&&t.jsxs(W,{children:[t.jsx(F,{variant:"subtitle1",gutterBottom:!0,sx:{mt:2},children:"Parameters for Case-Control Study"}),t.jsx(z,{label:"Proportion Exposed Among Controls (P0)",type:"number",value:N,onChange:s=>R(parseFloat(s.target.value)),inputProps:{step:.01,min:0,max:1},fullWidth:!0,margin:"normal",helperText:"Expected proportion of exposure in the control group"}),t.jsx(z,{label:"Odds Ratio (OR)",type:"number",value:C,onChange:s=>G(parseFloat(s.target.value)),inputProps:{step:.1,min:.1},fullWidth:!0,margin:"normal",helperText:"Expected Odds Ratio to detect"}),r==="power"&&t.jsxs(t.Fragment,{children:[t.jsx(z,{label:"Number of Cases (nCases)",type:"number",value:M,onChange:s=>B(parseInt(s.target.value,10)),inputProps:{min:1},fullWidth:!0,margin:"normal",helperText:"Actual number of cases"}),t.jsx(z,{label:"Number of Controls (nControls)",type:"number",value:T,onChange:s=>q(parseInt(s.target.value,10)),inputProps:{min:1},fullWidth:!0,margin:"normal",helperText:"Actual number of controls"})]}),r==="sampleSize"&&t.jsx(z,{label:"Ratio of Controls to Cases",type:"number",value:n,onChange:s=>{const c=parseFloat(s.target.value);!isNaN(c)&&c>0&&o(c)},inputProps:{step:.1,min:.1},fullWidth:!0,margin:"normal",helperText:"e.g., 1 for equal groups, 2 for 2 controls per case"})]})]})]}),t.jsxs(W,{sx:{display:"flex",gap:2,mt:3},children:[t.jsx(V,{variant:"contained",color:"primary",startIcon:t.jsx(Q,{}),onClick:$,disabled:r==="sampleSize"&&(S==="twoProportions"&&(isNaN(d)||isNaN(u)||d===u||isNaN(g)||isNaN(x)||x<=0)||S==="caseControl"&&(isNaN(N)||isNaN(C)||C<=0||isNaN(g)||isNaN(n)||n<=0))||r==="power"&&(S==="twoProportions"&&(isNaN(d)||isNaN(u)||d===u||isNaN(v)||isNaN(f)||v<=0||f<=0)||S==="caseControl"&&(isNaN(N)||isNaN(C)||C<=0||isNaN(M)||isNaN(T)||M<=0||T<=0))||isNaN(i),children:"Calculate"}),t.jsx(V,{variant:"outlined",startIcon:t.jsx(X,{}),onClick:J,children:"Reset"})]})]}),a&&t.jsxs(L,{elevation:1,sx:{p:3,mt:3},children:[t.jsx(F,{variant:"h6",gutterBottom:!0,children:"Results"}),t.jsx(Y,{sx:{mb:2}}),a.type==="sampleSize"&&a.value&&t.jsxs(W,{children:[t.jsx(F,{variant:"subtitle1",gutterBottom:!0,children:"Required Sample Size:"}),S==="twoProportions"&&t.jsxs(A,{container:!0,spacing:2,children:[t.jsx(A,{item:!0,xs:12,sm:4,children:t.jsx(I,{title:"Group 1 (n1)",value:p(a.value.n1),color:"primary",variant:"outlined"})}),t.jsx(A,{item:!0,xs:12,sm:4,children:t.jsx(I,{title:"Group 2 (n2)",value:p(a.value.n2),color:"primary",variant:"outlined"})}),t.jsx(A,{item:!0,xs:12,sm:4,children:t.jsx(I,{title:"Total Sample Size",value:p(a.value.total),color:"primary",variant:"gradient"})})]}),S==="caseControl"&&t.jsxs(A,{container:!0,spacing:2,children:[t.jsx(A,{item:!0,xs:12,sm:4,children:t.jsx(I,{title:"Cases (n)",value:p(a.value.nCases),color:"primary",variant:"outlined"})}),t.jsx(A,{item:!0,xs:12,sm:4,children:t.jsx(I,{title:"Controls (n)",value:p(a.value.nControls),color:"primary",variant:"outlined"})}),t.jsx(A,{item:!0,xs:12,sm:4,children:t.jsx(I,{title:"Total Sample Size",value:p(a.value.total),color:"primary",variant:"gradient"})})]}),t.jsxs(F,{variant:"body2",color:"text.secondary",sx:{mt:2},children:["These are the minimum required sample sizes per group to achieve the specified power and significance level.",t.jsx("br",{}),"• Alpha (α): ",p(a.params.alpha,2),t.jsx("br",{}),"• Desired Power (1-β): ",p(a.params.power,2),a.params.p1!==void 0&&a.params.p2!==void 0&&t.jsxs(t.Fragment,{children:[t.jsx("br",{}),"• Proportion in Group 1 (P1): ",p(a.params.p1,2),t.jsx("br",{}),"• Proportion in Group 2 (P2): ",p(a.params.p2,2),t.jsx("br",{}),"• Ratio (n2/n1): ",p(a.params.ratio,1)]}),a.params.p0!==void 0&&a.params.oddsRatio!==void 0&&t.jsxs(t.Fragment,{children:[t.jsx("br",{}),"• Proportion Exposed Among Controls (P0): ",p(a.params.p0,2),t.jsx("br",{}),"• Odds Ratio (OR): ",p(a.params.oddsRatio,2),t.jsx("br",{}),"• Ratio (Controls/Cases): ",p(a.params.ratio,1)]})]})]}),a.type==="power"&&a.value&&t.jsxs(W,{sx:{mt:0},children:[t.jsx(F,{variant:"subtitle1",gutterBottom:!0,children:"Calculated Power:"}),t.jsx(I,{title:"Power",value:K(a.value),color:"secondary",variant:"gradient",tooltip:"The probability of correctly rejecting the null hypothesis when it is false."}),t.jsxs(F,{variant:"body2",color:"text.secondary",sx:{mt:2},children:["This is the statistical power of a study with the following parameters:",t.jsx("br",{}),"• Alpha (α): ",p(a.params.alpha,2),a.params.n1!==void 0&&a.params.n2!==void 0&&t.jsxs(t.Fragment,{children:[t.jsx("br",{}),"• Group 1 Sample Size (n1): ",p(a.params.n1,0),t.jsx("br",{}),"• Group 2 Sample Size (n2): ",p(a.params.n2,0),t.jsx("br",{}),"• Proportion in Group 1 (P1): ",p(a.params.p1,2),t.jsx("br",{}),"• Proportion in Group 2 (P2): ",p(a.params.p2,2)]}),a.params.nCases!==void 0&&a.params.nControls!==void 0&&t.jsxs(t.Fragment,{children:[t.jsx("br",{}),"• Number of Cases (nCases): ",p(a.params.nCases,0),t.jsx("br",{}),"• Number of Controls (nControls): ",p(a.params.nControls,0),t.jsx("br",{}),"• Proportion Exposed Among Controls (P0): ",p(a.params.p0,2),t.jsx("br",{}),"• Odds Ratio (OR): ",p(a.params.oddsRatio,2)]})]})]})]})]})};export{pt as default};
