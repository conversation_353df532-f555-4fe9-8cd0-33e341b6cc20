import{u as tt,j as e,B as y,e as i,g as D,af as M,G as v,R as G,i as U,ag as X,ah as at,ai as st,aj as rt,ak as it,a5 as nt,f as ot,al as lt}from"./mui-libs-CfwFIaTD.js";import{r as w,b as ct}from"./react-libs-Cr2nE3UY.js";import{a as ut,D as b,H as dt}from"./index-Bpan7Tbe.js";import{B as pt}from"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import"./StatsCard-op8tGQ0a.js";import{D as mt}from"./DatasetSelector-G08QHuaN.js";import{V as E}from"./VariableSelector-CPdlCsJ2.js";import{C as ht,E as ft,G as yt}from"./GuidedWorkflow-MMwzUT8W.js";import"./other-utils-CR9xr_gI.js";import{p as xt,o as gt}from"./anova-DbTY6dHK.js";import"./math-setup-BTRs7Kau.js";import{c as vt}from"./normality-CwHD6Rjl.js";import{c as bt}from"./repeatedMeasuresANOVA-B8bv3NGM.js";import{t as At}from"./twoWayANOVA-CIsn6bwj.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./descriptive-Djo0s6H4.js";import"./math-lib-BOZ-XUok.js";const Pt=()=>{var ee,te,ae,se,re,ie,ne,oe,le,ce,ue,de,pe,me,he,fe,ye,xe,ge,ve,be,Ae,je,we,Ne,Se,Ve,Fe,$e,Oe,Ce,Te,Be,ke,Ie,We,Re,Ee,qe,De;const{datasets:Me,currentDataset:H}=ut(),N=tt(),[F,Ge]=w.useState((H==null?void 0:H.id)||""),[s,K]=w.useState("oneway"),[x,Le]=w.useState(""),[l,Z]=w.useState(""),[A,Pe]=w.useState(""),[T,ze]=w.useState(""),[O,Ue]=w.useState([]),[p,He]=w.useState(.05),[S,Ke]=w.useState(!0),[a,B]=w.useState(null),[r,L]=w.useState(null),m=Me.find(t=>t.id===F);w.useEffect(()=>{Y()},[m,x,l,A,s,S]);const P=()=>{if(!m)return!1;switch(s){case"oneway":return!!x&&!!l;case"twoway":return!!x&&!!l&&!!A&&l!==A;case"repeated":return!!T&&O.length>=2;default:return!1}},Ye=()=>!m||!x?[]:m.data.map(t=>Number(t[x])).filter(t=>!isNaN(t)),_=(t,c)=>!m||!x||!t?[]:m.data.filter(u=>u[t]===c).map(u=>Number(u[x])).filter(u=>!isNaN(u)),Y=()=>{if(!m||!P()){L(null);return}try{let t=[],c=[],u=[];if(s==="oneway"||s==="twoway"){const h=[...new Set(m.data.map(d=>d[l]))];t=h.map(d=>{var g,o,$,I,W,C;const k=_(l,d),n=vt(k,.05,["auto"]);return{group:String(d),n:k.length,isNormal:n.overallAssessment.isNormal,pValue:((g=n.tests.shapiroWilk)==null?void 0:g.pValue)||((o=n.tests.kolmogorovSmirnov)==null?void 0:o.pValue)||(($=n.tests.jarqueBera)==null?void 0:$.pValue)||NaN,statistic:((I=n.tests.shapiroWilk)==null?void 0:I.statistic)||((W=n.tests.kolmogorovSmirnov)==null?void 0:W.statistic)||((C=n.tests.jarqueBera)==null?void 0:C.statistic)||NaN}}),c=t.filter(d=>d.n<5),u=t.filter(d=>!d.isNormal&&d.n>=5);const V=h.map(d=>_(l,d)),R=xt(...V.filter(d=>d.length>1)),j={name:"Homogeneity of Variances (Levene's Test)",status:R.pValue>p?"passed":"failed",message:`Levene's test statistic: ${R.statistic.toFixed(3)}, p-value: ${f(R.pValue)}. A p-value > ${p} suggests that the variances are equal.`};L({normalityResults:t,insufficientGroups:c,nonNormalGroups:u,allNormal:u.length===0,allSufficient:c.length===0,homogeneityAssumption:j})}else s==="repeated"&&L({normalityResults:[],insufficientGroups:[],nonNormalGroups:[],allNormal:!0,allSufficient:!0,sphericityAssumption:{name:"Sphericity",status:"warning",message:"Sphericity check not implemented in this demo."}})}catch(t){console.error("Error checking assumptions:",t),L(null)}},Je=async()=>{if(!m||!P()){B(null);return}Y();try{let t=null;switch(s){case"oneway":const c=Ye(),u=m.data.map(n=>n[l]),h={};m.data.forEach(n=>{const g=String(n[l]),o=Number(n[x]);isNaN(o)||(h[g]||(h[g]=[]),h[g].push(o))});const V=Object.keys(h);if(V.length<3||V.some(n=>h[n].length===0)){console.error("One-Way ANOVA requires at least 3 groups with data."),B({errorMessage:"One-Way ANOVA requires at least 3 groups with data."});return}const R=V.map(n=>h[n]);try{t=gt(R);const n=V.map(g=>{const o=h[g],$=o.reduce((W,C)=>W+C,0)/o.length,I=o.length>1?Math.sqrt(o.reduce((W,C)=>W+Math.pow(C-$,2),0)/(o.length-1)):0;return{name:g,n:o.length,mean:$,sd:I}});t.groupStats=n}catch(n){console.error("Error running One-Way ANOVA:",n),B({errorMessage:`Error running One-Way ANOVA: ${n.message||n}`});return}break;case"twoway":const j=[...new Set(m.data.map(n=>n[l]))],d=[...new Set(m.data.map(n=>n[A]))],k=j.map(n=>d.map(g=>m.data.filter(o=>o[l]===n&&o[A]===g).map(o=>Number(o[x])).filter(o=>!isNaN(o))));if(j.length<2||d.length<2){B({errorMessage:"Two-Way ANOVA requires at least 2 levels for each factor."});return}t=At(k,j.length,d.length),t.factorAName=l,t.factorBName=A,t.factorALevels=j,t.factorBLevels=d;break;case"repeated":if(!T||O.length<2){B({errorMessage:"Repeated Measures ANOVA requires a subject identifier and at least two within-subject variables."});return}t=await bt(m.data,T,O);break}B({...t,anovaType:s,dependentVariable:x,factorVariable:s==="oneway"||s==="twoway"?l:void 0,secondFactorVariable:s==="twoway"?A:void 0,subjectId:s==="repeated"?T:void 0,withinSubjectVariables:s==="repeated"?O:void 0,significanceLevel:p,timestamp:new Date,assumptionResults:S?r:void 0})}catch(t){console.error("Error running analysis:",t),B({errorMessage:`An unexpected error occurred: ${t.message||t}`})}},f=t=>t==null?"N/A":t<.001?"p < 0.001":`p = ${t.toFixed(3)}`,Qe=()=>{if(!a)return"Run analysis to see interpretation.";const{pValue:t,dfBetween:c,dfWithin:u,F:h,etaSquared:V,groupStats:R,interaction:j,factorAName:d,factorBName:k,factorA:n,factorB:g,error:o,summary:$,sphericity:I}=a;switch(s){case"oneway":return t===void 0?"Could not calculate p-value.":`The one-way ANOVA showed a statistically ${t<=p?"significant":"non-significant"} difference in ${x} between groups, F(${c}, ${u}) = ${h==null?void 0:h.toFixed(2)}, ${f(t)}.`;case"twoway":if(!j||!n||!g||!o)return"Incomplete results for Two-Way ANOVA.";const C=j.pValue<=p,Ze=n.pValue<=p,_e=g.pValue<=p;let z=`The two-way ANOVA was conducted to examine the effects of ${d} and ${k} on ${x}. `;return z+=`There was a ${C?"significant":"non-significant"} interaction between the two factors, F(${j.df}, ${o.df}) = ${j.F.toFixed(2)}, ${f(j.pValue)}. `,z+=`The main effect for ${d} was ${Ze?"significant":"not significant"}, F(${n.df}, ${o.df}) = ${n.F.toFixed(2)}, ${f(n.pValue)}. `,z+=`The main effect for ${k} was ${_e?"significant":"not significant"}, F(${g.df}, ${o.df}) = ${g.F.toFixed(2)}, ${f(g.pValue)}.`,z;case"repeated":if(!$||!I)return"Incomplete results for Repeated Measures ANOVA.";const q=$.find(Q=>Q.source.includes("Within-Subjects"));if(!q)return"Could not find within-subjects results.";const et=q.p<=p,J=$.find(Q=>Q.source.includes("Error"));return`The repeated measures ANOVA revealed a ${et?"significant":"non-significant"} effect of the within-subjects factor, F(${q.df}, ${J==null?void 0:J.df}) = ${q.F.toFixed(2)}, ${f(q.p)}. ${I.message}`;default:return"Run analysis to see interpretation."}},Xe=[{id:"select-dataset",title:"Select Dataset",description:"Choose the dataset for your analysis",content:e.jsx(mt,{value:F,onChange:t=>Ge(t),variant:"card",showEmpty:!1,required:!0,minRows:4,helperText:"Select a dataset with sufficient data for the analysis"}),validation:()=>F?!0:"Please select a dataset"},{id:"choose-anova-type",title:"Choose ANOVA Type",description:"Select the appropriate ANOVA for your analysis question",content:e.jsxs(y,{children:[e.jsx(i,{variant:"body2",color:"text.secondary",paragraph:!0,children:"The type of ANOVA you choose depends on your research question and data structure:"}),e.jsxs(v,{container:!0,spacing:3,children:[e.jsx(v,{item:!0,xs:12,md:4,children:e.jsxs(G,{sx:{p:2,height:"100%",border:s==="oneway"?`2px solid ${N.palette.primary.main}`:void 0,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:N.shadows[3],borderColor:U(N.palette.primary.main,.5)}},onClick:()=>K("oneway"),children:[e.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(X,{color:"primary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:s==="oneway"?"bold":"normal",children:"One-Way ANOVA"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Compare means across three or more independent groups with one factor"}),e.jsxs(i,{variant:"body2",sx:{mt:1},children:[e.jsx("strong",{children:"Example:"})," Comparing test scores across three different teaching methods"]})]})}),e.jsx(v,{item:!0,xs:12,md:4,children:e.jsxs(G,{sx:{p:2,height:"100%",border:s==="twoway"?`2px solid ${N.palette.primary.main}`:void 0,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:N.shadows[3],borderColor:U(N.palette.primary.main,.5)}},onClick:()=>K("twoway"),children:[e.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(X,{color:"secondary",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:s==="twoway"?"bold":"normal",children:"Two-Way ANOVA"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Examine the influence of two independent factors and their interaction"}),e.jsxs(i,{variant:"body2",sx:{mt:1},children:[e.jsx("strong",{children:"Example:"})," Analyzing how both fertilizer type and sunlight affect plant growth"]})]})}),e.jsx(v,{item:!0,xs:12,md:4,children:e.jsxs(G,{sx:{p:2,height:"100%",border:s==="repeated"?`2px solid ${N.palette.primary.main}`:void 0,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:N.shadows[3],borderColor:U(N.palette.primary.main,.5)}},onClick:()=>K("repeated"),children:[e.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(X,{color:"info",sx:{mr:1}}),e.jsx(i,{variant:"subtitle1",fontWeight:s==="repeated"?"bold":"normal",children:"Repeated Measures ANOVA"})]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Analyze differences across multiple measurements on the same subjects"}),e.jsxs(i,{variant:"body2",sx:{mt:1},children:[e.jsx("strong",{children:"Example:"})," Measuring performance at different time points after an intervention"]})]})})]})]}),validation:()=>s?!0:"Please select an ANOVA type",helpContent:e.jsxs(y,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Choosing the Right ANOVA Test"}),e.jsx(i,{variant:"body1",paragraph:!0,children:"Analysis of Variance (ANOVA) is used to determine if there are statistically significant differences between the means of three or more independent groups."}),e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"One-Way ANOVA"}),e.jsx(i,{variant:"body2",paragraph:!0,children:"Use when you have one categorical independent variable (factor) with three or more groups and want to compare their means on a continuous dependent variable."}),e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Two-Way ANOVA"}),e.jsx(i,{variant:"body2",paragraph:!0,children:"Use when you have two categorical independent variables (factors) and want to examine their individual and interactive effects on a continuous dependent variable."}),e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Repeated Measures ANOVA"}),e.jsx(i,{variant:"body2",paragraph:!0,children:"Use when you have multiple measurements of the same dependent variable from the same subjects or matched subjects across different conditions or time points."}),e.jsxs(D,{severity:"info",sx:{mt:2},children:[e.jsx(M,{children:"When to use ANOVA vs. t-test"}),e.jsx(i,{variant:"body2",children:"Use a t-test when comparing only two groups. Use ANOVA when comparing three or more groups, or when examining the effects of multiple factors."})]})]})},{id:"select-variables",title:"Select Variables",description:"Choose the variables for your analysis based on the selected ANOVA type",content:e.jsx(y,{children:m?e.jsxs(v,{container:!0,spacing:3,children:[e.jsx(v,{item:!0,xs:12,children:e.jsx(E,{label:"Dependent Variable (Outcome)",datasetId:F,value:x,onChange:t=>Le(t),allowedTypes:[b.NUMERIC],required:!0,helperText:"Select a continuous numeric variable to analyze",variant:"autocomplete",placeholder:"Select a numeric variable"})}),s==="oneway"&&e.jsx(v,{item:!0,xs:12,children:e.jsx(E,{label:"Factor Variable (Groups)",datasetId:F,value:l,onChange:t=>Z(t),allowedTypes:[b.CATEGORICAL,b.ORDINAL,b.BOOLEAN],required:!0,helperText:"Select a categorical variable with at least 3 groups",variant:"autocomplete",placeholder:"Select a categorical variable"})}),s==="twoway"&&e.jsxs(e.Fragment,{children:[e.jsx(v,{item:!0,xs:12,children:e.jsx(E,{label:"First Factor Variable",datasetId:F,value:l,onChange:t=>Z(t),allowedTypes:[b.CATEGORICAL,b.ORDINAL,b.BOOLEAN],required:!0,helperText:"Select the first categorical factor variable",variant:"autocomplete",placeholder:"Select a categorical variable"})}),e.jsx(v,{item:!0,xs:12,children:e.jsx(E,{label:"Second Factor Variable",datasetId:F,value:A,onChange:t=>Pe(t),allowedTypes:[b.CATEGORICAL,b.ORDINAL,b.BOOLEAN],required:!0,helperText:"Select a different categorical factor variable",disabledValues:[l],variant:"autocomplete",placeholder:"Select a categorical variable"})})]}),s==="repeated"&&e.jsxs(e.Fragment,{children:[e.jsx(v,{item:!0,xs:12,md:6,children:e.jsx(E,{label:"Subject Identifier",datasetId:F,value:T,onChange:t=>ze(t),allowedTypes:[b.CATEGORICAL,b.ORDINAL],required:!0,helperText:"Select the variable that uniquely identifies each subject.",variant:"autocomplete",placeholder:"Select a subject ID variable"})}),e.jsx(v,{item:!0,xs:12,md:6,children:e.jsx(E,{label:"Within-Subject Variables (Levels)",datasetId:F,value:O,onChange:t=>Ue(t),allowedTypes:[b.NUMERIC],required:!0,multiple:!0,minSelections:2,helperText:"Select at least two numeric variables for repeated measurements.",variant:"autocomplete",placeholder:"Select variables"})})]})]}):e.jsxs(D,{severity:"warning",children:[e.jsx(M,{children:"No Dataset Selected"}),"Please go back and select a dataset first."]})}),validation:()=>{if(!m)return"Please select a dataset first";if(!x)return"Please select a dependent variable";if(s==="oneway"){if(!l)return"Please select a factor variable"}else if(s==="twoway"){if(!l)return"Please select the first factor variable";if(!A)return"Please select the second factor variable";if(l===A)return"The two factor variables must be different"}else if(s==="repeated"){if(!T)return"Please select a subject identifier variable.";if(O.length<2)return"Please select at least two within-subject variables."}return!0}},{id:"set-options",title:"Set Analysis Options",description:"Configure additional options for the ANOVA test",content:e.jsxs(y,{children:[e.jsxs(v,{container:!0,spacing:3,children:[e.jsx(v,{item:!0,xs:12,sm:6,children:e.jsx(at,{label:"Significance Level (α)",type:"number",value:p,onChange:t=>He(parseFloat(t.target.value)),inputProps:{min:.001,max:.999,step:.01},fullWidth:!0,size:"small",helperText:"Typical values: 0.05, 0.01, or 0.001"})}),e.jsx(v,{item:!0,xs:12,children:e.jsx(st,{component:"fieldset",sx:{mt:2},children:e.jsx(rt,{control:e.jsx(it,{checked:S,onChange:t=>Ke(t.target.checked)}),label:"Check ANOVA assumptions before analysis"})})})]}),e.jsx(ht,{title:"About Statistical Options",items:[{title:"Significance Level (α)",content:"The significance level is the probability of rejecting the null hypothesis when it is true. A lower value makes the test more conservative.",type:"info"},{title:"Assumptions",content:"ANOVA tests have assumptions (e.g., normality, homogeneity of variances). Checking these helps ensure the validity of your results.",type:"tip"}],initiallyExpanded:!0,variant:"panel",showIcons:!0,width:"100%"})]}),validation:()=>isNaN(p)||p<=0||p>=1?"Significance level must be between 0 and 1":!0},{id:"review-and-run",title:"Review & Run Analysis",description:"Review your selections and run the ANOVA test",content:e.jsxs(y,{children:[e.jsxs(G,{sx:{p:2,mb:3,backgroundColor:U(N.palette.background.default,.5)},children:[e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Analysis Summary"}),e.jsxs(v,{container:!0,spacing:2,children:[e.jsxs(v,{item:!0,xs:12,sm:6,children:[e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"ANOVA Type:"})," ",s==="oneway"?"One-Way ANOVA":s==="twoway"?"Two-Way ANOVA":"Repeated Measures ANOVA"]}),e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Dataset:"})," ",(m==null?void 0:m.name)||"None selected"]}),e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Dependent Variable:"})," ",x||"None selected"]})]}),e.jsxs(v,{item:!0,xs:12,sm:6,children:[s==="oneway"&&e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Factor Variable:"})," ",l||"None selected"]}),s==="twoway"&&e.jsxs(e.Fragment,{children:[e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"First Factor:"})," ",l||"None selected"]}),e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Second Factor:"})," ",A||"None selected"]})]}),s==="repeated"&&e.jsxs(e.Fragment,{children:[e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Subject Identifier:"})," ",T||"None selected"]}),e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Within-Subject Variables:"})," ",O.join(", ")||"None selected"]})]}),e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Significance Level:"})," ",p]}),e.jsxs(i,{variant:"body2",children:[e.jsx("strong",{children:"Check Assumptions:"})," ",S?"Yes":"No"]})]})]})]}),S&&r&&e.jsxs(y,{mb:3,children:[e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Assumption Check Results"}),e.jsx(D,{severity:r.allNormal&&r.allSufficient&&(s!=="oneway"||((ee=r.homogeneityAssumption)==null?void 0:ee.status)!=="failed")?"success":"warning",sx:{mb:2},children:e.jsxs(ct.Fragment,{children:[e.jsx(M,{children:r.allNormal&&r.allSufficient&&(s!=="oneway"||((te=r.homogeneityAssumption)==null?void 0:te.status)!=="failed")?"Assumptions appear to be met":"Some assumptions may be violated"}),e.jsx(i,{variant:"body2",children:r.allNormal?"✓ Normality: All groups appear to be normally distributed.":`⚠️ Normality: ${r.nonNormalGroups.length} group(s) may not be normally distributed`}),e.jsx(i,{variant:"body2",children:r.allSufficient?"✓ Sample size: All groups have sufficient data":`⚠️ Sample size: ${r.insufficientGroups.length} group(s) have fewer than 5 observations`}),s==="oneway"&&r.homogeneityAssumption&&e.jsx(i,{variant:"body2",children:r.homogeneityAssumption.status==="passed"?`✓ ${r.homogeneityAssumption.name}: ${r.homogeneityAssumption.message}`:`⚠️ ${r.homogeneityAssumption.name}: ${r.homogeneityAssumption.message}`}),s==="repeated"&&r.sphericityAssumption&&e.jsx(i,{variant:"body2",children:r.sphericityAssumption.status==="passed"?`✓ ${r.sphericityAssumption.name}: ${r.sphericityAssumption.message}`:`⚠️ ${r.sphericityAssumption.name}: ${r.sphericityAssumption.message}`})]})}),r.nonNormalGroups.length>0&&e.jsx(y,{sx:{mt:1},children:e.jsxs(i,{variant:"body2",color:"text.secondary",children:["Groups potentially violating normality: ",r.nonNormalGroups.map(t=>t.group).join(", ")]})}),r.insufficientGroups.length>0&&e.jsx(y,{sx:{mt:1},children:e.jsxs(i,{variant:"body2",color:"text.secondary",children:["Groups with insufficient data (&lt 5 observations): ",r.insufficientGroups.map(t=>t.group).join(", ")]})})]}),e.jsx(y,{sx:{textAlign:"center",mt:4},children:e.jsx(pt,{gradient:!0,rounded:!0,startIcon:e.jsx(nt,{}),onClick:Je,disabled:!P(),size:"large",children:"Run ANOVA Analysis"})}),e.jsx(y,{sx:{mt:3},children:a?a.errorMessage?e.jsxs(D,{severity:"error",children:[e.jsx(M,{children:"Analysis Error"}),a.errorMessage]}):e.jsx(ft,{title:`${s==="oneway"?"One-Way ANOVA":s==="twoway"?"Two-Way ANOVA":"Repeated Measures ANOVA"} Results`,description:s==="oneway"?`Comparing ${x} across groups of ${l}`:s==="twoway"?`Examining effects of ${l} and ${A} on ${x}`:`Analyzing repeated measurements for ${O.join(", ")}`,timestamp:a.timestamp,pValue:s==="oneway"?a.pValue:s==="twoway"?(ae=a.interaction)==null?void 0:ae.pValue:s==="repeated"?(re=(se=a.summary)==null?void 0:se.find(t=>t.source.includes("Within-Subjects")))==null?void 0:re.p:void 0,significance:p,stats:s==="oneway"&&a.groupStats?[{label:"F-statistic",value:(ie=a.F)==null?void 0:ie.toFixed(2)},{label:"p-value",value:f(a.pValue)},{label:"Effect Size (η²)",value:(ne=a.etaSquared)==null?void 0:ne.toFixed(3)}]:s==="twoway"?[{label:`F-stat (${a.factorAName})`,value:(oe=a.factorA)==null?void 0:oe.F.toFixed(2)},{label:`p-value (${a.factorAName})`,value:f((le=a.factorA)==null?void 0:le.pValue)},{label:`F-stat (${a.factorBName})`,value:(ce=a.factorB)==null?void 0:ce.F.toFixed(2)},{label:`p-value (${a.factorBName})`,value:f((ue=a.factorB)==null?void 0:ue.pValue)},{label:"F-stat (Interaction)",value:(de=a.interaction)==null?void 0:de.F.toFixed(2)},{label:"p-value (Interaction)",value:f((pe=a.interaction)==null?void 0:pe.pValue)}]:s==="repeated"&&a.summary?[{label:"F-statistic (Within-Subject)",value:(me=a.summary.find(t=>t.source.includes("Within-Subjects")))==null?void 0:me.F.toFixed(2)},{label:"p-value (Within-Subject)",value:f((he=a.summary.find(t=>t.source.includes("Within-Subjects")))==null?void 0:he.p)},{label:"Mauchly's W",value:(fe=a.sphericity)==null?void 0:fe.mauchlyW.toFixed(3)},{label:"Sphericity p-value",value:f((ye=a.sphericity)==null?void 0:ye.pValue)}]:[],statisticalTests:s==="oneway"?[{name:"F-statistic",value:(xe=a.F)==null?void 0:xe.toFixed(3)},{name:"p-value",value:f(a.pValue),pValue:a.pValue,significant:a.pValue<=p},{name:"Effect Size (η²)",value:(ge=a.etaSquared)==null?void 0:ge.toFixed(3)},{name:"DF (between, within)",value:`${a.dfBetween}, ${a.dfWithin}`}]:s==="twoway"?[{name:`Factor A: ${a.factorAName}`,value:`F(${(ve=a.factorA)==null?void 0:ve.df}, ${(be=a.error)==null?void 0:be.df}) = ${typeof((Ae=a.factorA)==null?void 0:Ae.F)=="number"?a.factorA.F.toFixed(3):"N/A"}, p = ${f((je=a.factorA)==null?void 0:je.pValue)}, η² = ${typeof((we=a.factorA)==null?void 0:we.etaSquared)=="number"?a.factorA.etaSquared.toFixed(3):"N/A"}`,pValue:(Ne=a.factorA)==null?void 0:Ne.pValue,significant:((Se=a.factorA)==null?void 0:Se.pValue)<=p},{name:`Factor B: ${a.factorBName}`,value:`F(${(Ve=a.factorB)==null?void 0:Ve.df}, ${(Fe=a.error)==null?void 0:Fe.df}) = ${typeof(($e=a.factorB)==null?void 0:$e.F)=="number"?a.factorB.F.toFixed(3):"N/A"}, p = ${f((Oe=a.factorB)==null?void 0:Oe.pValue)}, η² = ${typeof((Ce=a.factorB)==null?void 0:Ce.etaSquared)=="number"?a.factorB.etaSquared.toFixed(3):"N/A"}`,pValue:(Te=a.factorB)==null?void 0:Te.pValue,significant:((Be=a.factorB)==null?void 0:Be.pValue)<=p},{name:"Interaction (A x B)",value:`F(${(ke=a.interaction)==null?void 0:ke.df}, ${(Ie=a.error)==null?void 0:Ie.df}) = ${typeof((We=a.interaction)==null?void 0:We.F)=="number"?a.interaction.F.toFixed(3):"N/A"}, p = ${f((Re=a.interaction)==null?void 0:Re.pValue)}, η² = ${typeof((Ee=a.interaction)==null?void 0:Ee.etaSquared)=="number"?a.interaction.etaSquared.toFixed(3):"N/A"}`,pValue:(qe=a.interaction)==null?void 0:qe.pValue,significant:((De=a.interaction)==null?void 0:De.pValue)<=p}]:s==="repeated"&&a.summary?a.summary.filter(t=>t.F&&isFinite(t.F)).map(t=>{var c;return{name:t.source,value:`F(${t.df}, ${(c=a.summary.find(u=>u.source.includes("Error")))==null?void 0:c.df}) = ${typeof t.F=="number"?t.F.toFixed(3):"N/A"}, p = ${f(t.p)}, η²p = ${typeof t.etaSquared=="number"?t.etaSquared.toFixed(3):"N/A"}`,pValue:t.p,significant:t.p<=p}}):[],confidenceInterval:void 0,chart:s==="oneway"&&a.groupStats||s==="repeated"&&a.descriptives?e.jsx(y,{sx:{height:300,display:"flex",justifyContent:"center",alignItems:"flex-end",p:2},children:e.jsx(y,{sx:{display:"flex",alignItems:"flex-end",height:"100%"},children:(s==="oneway"?a.groupStats:a.descriptives).map((t,c)=>e.jsxs(y,{sx:{display:"flex",flexDirection:"column",alignItems:"center",mx:1},children:[e.jsx(y,{sx:{width:40,height:`${t.mean/Math.max(...(s==="oneway"?a.groupStats:a.descriptives).map(u=>u.mean))*200}px`,bgcolor:N.palette.primary.main,display:"flex",justifyContent:"center",alignItems:"flex-start",pt:1,color:"white",fontWeight:"bold",borderRadius:"4px 4px 0 0"},children:t.mean.toFixed(1)}),e.jsx(i,{variant:"body2",sx:{mt:1},children:t.name||t.condition})]},c))})}):e.jsx(i,{variant:"body2",color:"text.secondary",sx:{p:3,textAlign:"center"},children:"Chart visualization not available for this ANOVA type."}),chartTitle:s==="oneway"||s==="repeated"?"Comparison of Means":void 0,table:s==="oneway"&&a.groupStats?{columns:["Group","N","Mean","SD"],rows:a.groupStats.map(t=>[t.name,t.n,t.mean.toFixed(2),t.sd.toFixed(2)])}:s==="twoway"&&a.cellMeans?{columns:[`${a.factorAName}`,`${a.factorBName}`,"N","Mean","SD"],rows:a.factorALevels.flatMap((t,c)=>a.factorBLevels.map((u,h)=>[t,u,a.cellCounts[c][h],a.cellMeans[c][h].toFixed(2),"N/A"]))}:s==="repeated"&&a.summary?{columns:["Source","SS","df","MS","F","p-value","η²p"],rows:a.summary.map(t=>{var c,u,h,V;return[String(t.source),String((c=t.SS)==null?void 0:c.toFixed(3)),String(t.df),String((u=t.MS)==null?void 0:u.toFixed(3)),String((h=t.F)==null?void 0:h.toFixed(3)),String(f(t.p)),String((V=t.etaSquared)==null?void 0:V.toFixed(3))]}).filter(t=>t.every(c=>c!=null))}:void 0,interpretations:[Qe()].filter(Boolean),assumptions:[{name:"Independence of observations",status:"passed",message:"Assumed based on study design"},{name:"No significant outliers",status:"warning",message:"Check your data for outliers using box plots"},...S&&(r!=null&&r.normalityResults)?[{name:"Normality",status:r.allNormal?"passed":"failed",message:r.allNormal?"All groups appear normally distributed.":`Normality assumption may be violated in ${r.nonNormalGroups.length} group(s). Details below:`},...r.normalityResults.map(t=>({name:`  - Group "${t.group}"`,status:t.n<5?"warning":t.isNormal?"passed":"failed",message:t.n<5?`Insufficient data (n=${t.n}) to reliably test normality.`:`Kolmogorov-Smirnov D=${t.statistic.toFixed(3)}, p=${f(t.pValue)}`}))]:[],...S&&s==="oneway"&&(r!=null&&r.homogeneityAssumption)?[r.homogeneityAssumption]:[],...S&&s==="repeated"&&(r!=null&&r.sphericityAssumption)?[r.sphericityAssumption]:[]].filter(Boolean),footnotes:[`The significance level (α) was set at ${p}.`,...s==="oneway"&&(a!=null&&a.groupStats)?[`Group means and standard deviations: ${a.groupStats.map(t=>`${t.name} (M=${t.mean.toFixed(2)}, SD=${t.sd.toFixed(2)})`).join(", ")}`]:[],...s==="oneway"&&(a==null?void 0:a.etaSquared)!==void 0?[`Effect size (Eta Squared, η²): ${a.etaSquared.toFixed(3)}`]:[]].filter(Boolean),variant:"default"},a.timestamp):e.jsxs(D,{severity:"info",children:[e.jsx(M,{children:"Analysis Results"}),'Results will appear here after clicking "Run ANOVA Analysis".']})})]}),validation:()=>{var t;return P()?S&&!r?'Please wait for assumption checks to complete or uncheck "Check ANOVA assumptions".':(S&&r&&(!r.allNormal||!r.allSufficient||s==="oneway"&&((t=r.homogeneityAssumption)==null||t.status)),!0):"Please complete all previous steps before running the analysis"},onStepEnter:Y}];return e.jsxs(e.Fragment,{children:[e.jsxs(dt,{children:[e.jsx("title",{children:"ANOVA Analysis Workflow | DataStatPro"}),e.jsx("meta",{name:"description",content:"Step-by-step guided workflow for performing One-Way and Two-Way ANOVA analyses to compare means across multiple groups."})]}),e.jsxs(y,{sx:{p:3,maxWidth:1200,margin:"0 auto"},children:[e.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(ot,{startIcon:e.jsx(lt,{}),sx:{mr:2},component:"a",href:"#/",children:"Back to Home"}),e.jsx(i,{variant:"h5",component:"h1",children:"ANOVA Analysis Workflow"})]}),e.jsx(i,{variant:"body1",paragraph:!0,color:"text.secondary",children:"This guided workflow will help you perform an Analysis of Variance (ANOVA) to compare means across multiple groups. Follow the steps below to select your data, choose the appropriate test type, and interpret the results."}),e.jsx(G,{sx:{p:3,mb:3},children:e.jsx(yt,{steps:Xe,title:"ANOVA Analysis",description:"Follow these steps to conduct an ANOVA analysis on your data",variant:"vertical",saveProgress:!0,persistenceKey:"anova-workflow",enableBookmarking:!0,showStepNavigation:!0,allowSkipSteps:!1})})]})]})};export{Pt as default};
