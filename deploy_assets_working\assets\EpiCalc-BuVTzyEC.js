import{j as o,B as e,a6 as m,a7 as r,W as x,a0 as p,a2 as d,aX as C,bG as j}from"./mui-libs-CfwFIaTD.js";import{r as u}from"./react-libs-Cr2nE3UY.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{T as t}from"./TabPanel-CVuv1-VX.js";import"./StatsCard-op8tGQ0a.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import"./other-utils-CR9xr_gI.js";import b from"./CrossSectionalCalculator-CiCHocwD.js";import h from"./CaseControlCalculator-BzLsSUO6.js";import v from"./CohortCalculator-Ceh0DWHw.js";import f from"./MatchedCaseControlCalculator-XX9Fsb_b.js";import T from"./SampleSizePowerCalculator-Czye5N6w.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./non-parametric-Cf6Ds91x.js";const D=({initialTab:s})=>{const i={cross_sectional:0,case_control:1,cohort:2,matched_case_control:3,sample_size_power:4,main:0},[a,l]=u.useState(s?i[s]:0),n=(P,c)=>{l(c)};return o.jsxs(e,{sx:{p:3},children:[o.jsx(e,{sx:{borderBottom:1,borderColor:"divider",mb:3},children:o.jsxs(m,{value:a,onChange:n,"aria-label":"epidemiological calculator tabs",variant:"scrollable",scrollButtons:"auto",allowScrollButtonsMobile:!0,textColor:"primary",indicatorColor:"primary",children:[o.jsx(r,{label:"Cross-Sectional",icon:o.jsx(x,{}),iconPosition:"start"}),o.jsx(r,{label:"Case-Control",icon:o.jsx(p,{}),iconPosition:"start"}),o.jsx(r,{label:"Cohort",icon:o.jsx(d,{}),iconPosition:"start"}),o.jsx(r,{label:"Matched Case-Control",icon:o.jsx(C,{}),iconPosition:"start"}),o.jsx(r,{label:"Sample Size & Power",icon:o.jsx(j,{}),iconPosition:"start"})]})}),o.jsx(t,{value:a,index:0,children:o.jsx(b,{})}),o.jsx(t,{value:a,index:1,children:o.jsx(h,{})}),o.jsx(t,{value:a,index:2,children:o.jsx(v,{})}),o.jsx(t,{value:a,index:3,children:o.jsx(f,{})}),o.jsx(t,{value:a,index:4,children:o.jsx(T,{})})]})};export{D as default};
