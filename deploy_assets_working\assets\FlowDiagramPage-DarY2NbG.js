import{j as r}from"./mui-libs-CfwFIaTD.js";import{F as o}from"./FlowDiagram-CHRbazj7.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./PublicationReadyGate-BGFbKbJc.js";import"./index-Bpan7Tbe.js";import"./other-utils-CR9xr_gI.js";const j=()=>r.jsxs("div",{children:[r.jsx("h1",{children:"Flow Diagram Generator"}),r.jsx(o,{})]});export{j as default};
