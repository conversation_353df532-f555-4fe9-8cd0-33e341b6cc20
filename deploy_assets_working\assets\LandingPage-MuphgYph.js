import{u as br,a as pt,j as i,B as d,A as yr,T as Sr,C as ee,d as Ne,e as v,br as F,K as te,I as xt,dU as wr,f as N,i as A,dy as jr,M as B,dV as Or,cm as kr,L as Pr,m as Cr,a1 as _r,r as Tr,D as Er,aV as zr,G as R,F as Be,S as ve,h as $e,dR as Ir,R as Fe,dW as Dr,dX as Mr,dY as Lr,dZ as Rr,W as vt,a8 as Rt,Z as gt,k as U,l as K,ch as Ar,d3 as Wr,d_ as Hr,a2 as Nr,aB as Br,c4 as $r,y as Fr,o as V,q as qr,s as be,z as Yr,E as Gr,J as Ur,b5 as Kr,d$ as Xr,e0 as Qr,e1 as Vr,w as Jr,O as Zr,cZ as en}from"./mui-libs-CfwFIaTD.js";import{e as ye,r as Q,a as tn,g as rn,b as nn}from"./react-libs-Cr2nE3UY.js";import{u as on,H as an}from"./index-Bpan7Tbe.js";import{g as qe}from"./ml-tensorflow-D19WVUQh.js";import"./supabase-lib-B3goak-P.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";var At={},Wt={},Me={},Ht={};(function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var e={animating:!1,autoplaying:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,dragging:!1,edgeDragged:!1,initialized:!1,lazyLoadedList:[],listHeight:null,listWidth:null,scrolling:!1,slideCount:null,slideHeight:null,slideWidth:null,swipeLeft:null,swiped:!1,swiping:!1,touchObject:{startX:0,startY:0,curX:0,curY:0},trackStyle:{},trackWidth:0,targetSlide:0};r.default=e})(Ht);var sn="Expected a function",mt=NaN,ln="[object Symbol]",cn=/^\s+|\s+$/g,un=/^[-+]0x[0-9a-f]+$/i,dn=/^0b[01]+$/i,fn=/^0o[0-7]+$/i,hn=parseInt,pn=typeof ye=="object"&&ye&&ye.Object===Object&&ye,xn=typeof self=="object"&&self&&self.Object===Object&&self,vn=pn||xn||Function("return this")(),gn=Object.prototype,mn=gn.toString,bn=Math.max,yn=Math.min,Ye=function(){return vn.Date.now()};function Sn(r,e,n){var o,t,s,a,l,c,f=0,b=!1,p=!1,w=!0;if(typeof r!="function")throw new TypeError(sn);e=bt(e)||0,tt(n)&&(b=!!n.leading,p="maxWait"in n,s=p?bn(bt(n.maxWait)||0,e):s,w="trailing"in n?!!n.trailing:w);function C(_){var W=o,q=t;return o=t=void 0,f=_,a=r.apply(q,W),a}function O(_){return f=_,l=setTimeout(k,e),b?C(_):a}function y(_){var W=_-c,q=_-f,g=e-W;return p?yn(g,s-q):g}function S(_){var W=_-c,q=_-f;return c===void 0||W>=e||W<0||p&&q>=s}function k(){var _=Ye();if(S(_))return u(_);l=setTimeout(k,y(_))}function u(_){return l=void 0,w&&o?C(_):(o=t=void 0,a)}function h(){l!==void 0&&clearTimeout(l),f=0,o=c=t=l=void 0}function T(){return l===void 0?a:u(Ye())}function E(){var _=Ye(),W=S(_);if(o=arguments,t=this,c=_,W){if(l===void 0)return O(c);if(p)return l=setTimeout(k,e),C(c)}return l===void 0&&(l=setTimeout(k,e)),a}return E.cancel=h,E.flush=T,E}function tt(r){var e=typeof r;return!!r&&(e=="object"||e=="function")}function wn(r){return!!r&&typeof r=="object"}function jn(r){return typeof r=="symbol"||wn(r)&&mn.call(r)==ln}function bt(r){if(typeof r=="number")return r;if(jn(r))return mt;if(tt(r)){var e=typeof r.valueOf=="function"?r.valueOf():r;r=tt(e)?e+"":e}if(typeof r!="string")return r===0?r:+r;r=r.replace(cn,"");var n=dn.test(r);return n||fn.test(r)?hn(r.slice(2),n?2:8):un.test(r)?mt:+r}var On=Sn,Nt={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(r){(function(){var e={}.hasOwnProperty;function n(){for(var s="",a=0;a<arguments.length;a++){var l=arguments[a];l&&(s=t(s,o(l)))}return s}function o(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return n.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var a="";for(var l in s)e.call(s,l)&&s[l]&&(a=t(a,l));return a}function t(s,a){return a?s?s+" "+a:s+a:s}r.exports?(n.default=n,r.exports=n):window.classNames=n})()})(Nt);var Le=Nt.exports,x={},dt={};(function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var e=n(Q);function n(t){return t&&t.__esModule?t:{default:t}}var o={accessibility:!0,adaptiveHeight:!1,afterChange:null,appendDots:function(s){return e.default.createElement("ul",{style:{display:"block"}},s)},arrows:!0,autoplay:!1,autoplaySpeed:3e3,beforeChange:null,centerMode:!1,centerPadding:"50px",className:"",cssEase:"ease",customPaging:function(s){return e.default.createElement("button",null,s+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,infinite:!0,initialSlide:0,lazyLoad:null,nextArrow:null,onEdge:null,onInit:null,onLazyLoadError:null,onReInit:null,pauseOnDotsHover:!1,pauseOnFocus:!1,pauseOnHover:!0,prevArrow:null,responsive:null,rows:1,rtl:!1,slide:"div",slidesPerRow:1,slidesToScroll:1,slidesToShow:1,speed:500,swipe:!0,swipeEvent:null,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,waitForAnimate:!0,asNavFor:null,unslick:!1};r.default=o})(dt);Object.defineProperty(x,"__esModule",{value:!0});x.checkSpecKeys=x.checkNavigable=x.changeSlide=x.canUseDOM=x.canGoNext=void 0;x.clamp=$t;x.extractObject=void 0;x.filterSettings=Hn;x.validSettings=x.swipeStart=x.swipeMove=x.swipeEnd=x.slidesOnRight=x.slidesOnLeft=x.slideHandler=x.siblingDirection=x.safePreventDefault=x.lazyStartIndex=x.lazySlidesOnRight=x.lazySlidesOnLeft=x.lazyEndIndex=x.keyHandler=x.initializedState=x.getWidth=x.getTrackLeft=x.getTrackCSS=x.getTrackAnimateCSS=x.getTotalSlides=x.getSwipeDirection=x.getSlideCount=x.getRequiredLazySlides=x.getPreClones=x.getPostClones=x.getOnDemandLazySlides=x.getNavigableIndexes=x.getHeight=void 0;var kn=Bt(Q),Pn=Bt(dt);function Bt(r){return r&&r.__esModule?r:{default:r}}function ge(r){"@babel/helpers - typeof";return ge=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ge(r)}function yt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})),n.push.apply(n,o)}return n}function H(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?yt(Object(n),!0).forEach(function(o){Cn(r,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):yt(Object(n)).forEach(function(o){Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(n,o))})}return r}function Cn(r,e,n){return e=_n(e),e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}function _n(r){var e=Tn(r,"string");return ge(e)=="symbol"?e:String(e)}function Tn(r,e){if(ge(r)!="object"||!r)return r;var n=r[Symbol.toPrimitive];if(n!==void 0){var o=n.call(r,e);if(ge(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function $t(r,e,n){return Math.max(e,Math.min(r,n))}var le=x.safePreventDefault=function(e){var n=["onTouchStart","onTouchMove","onWheel"];n.includes(e._reactName)||e.preventDefault()},Ft=x.getOnDemandLazySlides=function(e){for(var n=[],o=qt(e),t=Yt(e),s=o;s<t;s++)e.lazyLoadedList.indexOf(s)<0&&n.push(s);return n};x.getRequiredLazySlides=function(e){for(var n=[],o=qt(e),t=Yt(e),s=o;s<t;s++)n.push(s);return n};var qt=x.lazyStartIndex=function(e){return e.currentSlide-En(e)},Yt=x.lazyEndIndex=function(e){return e.currentSlide+zn(e)},En=x.lazySlidesOnLeft=function(e){return e.centerMode?Math.floor(e.slidesToShow/2)+(parseInt(e.centerPadding)>0?1:0):0},zn=x.lazySlidesOnRight=function(e){return e.centerMode?Math.floor((e.slidesToShow-1)/2)+1+(parseInt(e.centerPadding)>0?1:0):e.slidesToShow},rt=x.getWidth=function(e){return e&&e.offsetWidth||0},Gt=x.getHeight=function(e){return e&&e.offsetHeight||0},Ut=x.getSwipeDirection=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,o,t,s,a;return o=e.startX-e.curX,t=e.startY-e.curY,s=Math.atan2(t,o),a=Math.round(s*180/Math.PI),a<0&&(a=360-Math.abs(a)),a<=45&&a>=0||a<=360&&a>=315?"left":a>=135&&a<=225?"right":n===!0?a>=35&&a<=135?"up":"down":"vertical"},Kt=x.canGoNext=function(e){var n=!0;return e.infinite||(e.centerMode&&e.currentSlide>=e.slideCount-1||e.slideCount<=e.slidesToShow||e.currentSlide>=e.slideCount-e.slidesToShow)&&(n=!1),n};x.extractObject=function(e,n){var o={};return n.forEach(function(t){return o[t]=e[t]}),o};x.initializedState=function(e){var n=kn.default.Children.count(e.children),o=e.listRef,t=Math.ceil(rt(o)),s=e.trackRef&&e.trackRef.node,a=Math.ceil(rt(s)),l;if(e.vertical)l=t;else{var c=e.centerMode&&parseInt(e.centerPadding)*2;typeof e.centerPadding=="string"&&e.centerPadding.slice(-1)==="%"&&(c*=t/100),l=Math.ceil((t-c)/e.slidesToShow)}var f=o&&Gt(o.querySelector('[data-index="0"]')),b=f*e.slidesToShow,p=e.currentSlide===void 0?e.initialSlide:e.currentSlide;e.rtl&&e.currentSlide===void 0&&(p=n-1-e.initialSlide);var w=e.lazyLoadedList||[],C=Ft(H(H({},e),{},{currentSlide:p,lazyLoadedList:w}));w=w.concat(C);var O={slideCount:n,slideWidth:l,listWidth:t,trackWidth:a,currentSlide:p,slideHeight:f,listHeight:b,lazyLoadedList:w};return e.autoplaying===null&&e.autoplay&&(O.autoplaying="playing"),O};x.slideHandler=function(e){var n=e.waitForAnimate,o=e.animating,t=e.fade,s=e.infinite,a=e.index,l=e.slideCount,c=e.lazyLoad,f=e.currentSlide,b=e.centerMode,p=e.slidesToScroll,w=e.slidesToShow,C=e.useCSS,O=e.lazyLoadedList;if(n&&o)return{};var y=a,S,k,u,h={},T={},E=s?a:$t(a,0,l-1);if(t){if(!s&&(a<0||a>=l))return{};a<0?y=a+l:a>=l&&(y=a-l),c&&O.indexOf(y)<0&&(O=O.concat(y)),h={animating:!0,currentSlide:y,lazyLoadedList:O,targetSlide:y},T={animating:!1,targetSlide:y}}else S=y,y<0?(S=y+l,s?l%p!==0&&(S=l-l%p):S=0):!Kt(e)&&y>f?y=S=f:b&&y>=l?(y=s?l:l-1,S=s?0:l-1):y>=l&&(S=y-l,s?l%p!==0&&(S=0):S=l-w),!s&&y+w>=l&&(S=l-w),k=Oe(H(H({},e),{},{slideIndex:y})),u=Oe(H(H({},e),{},{slideIndex:S})),s||(k===u&&(y=S),k=u),c&&(O=O.concat(Ft(H(H({},e),{},{currentSlide:y})))),C?(h={animating:!0,currentSlide:S,trackStyle:Xt(H(H({},e),{},{left:k})),lazyLoadedList:O,targetSlide:E},T={animating:!1,currentSlide:S,trackStyle:je(H(H({},e),{},{left:u})),swipeLeft:null,targetSlide:E}):h={currentSlide:S,trackStyle:je(H(H({},e),{},{left:u})),lazyLoadedList:O,targetSlide:E};return{state:h,nextState:T}};x.changeSlide=function(e,n){var o,t,s,a,l,c=e.slidesToScroll,f=e.slidesToShow,b=e.slideCount,p=e.currentSlide,w=e.targetSlide,C=e.lazyLoad,O=e.infinite;if(a=b%c!==0,o=a?0:(b-p)%c,n.message==="previous")s=o===0?c:f-o,l=p-s,C&&!O&&(t=p-s,l=t===-1?b-1:t),O||(l=w-c);else if(n.message==="next")s=o===0?c:o,l=p+s,C&&!O&&(l=(p+c)%b+o),O||(l=w+c);else if(n.message==="dots")l=n.index*n.slidesToScroll;else if(n.message==="children"){if(l=n.index,O){var y=Ln(H(H({},e),{},{targetSlide:l}));l>n.currentSlide&&y==="left"?l=l-b:l<n.currentSlide&&y==="right"&&(l=l+b)}}else n.message==="index"&&(l=Number(n.index));return l};x.keyHandler=function(e,n,o){return e.target.tagName.match("TEXTAREA|INPUT|SELECT")||!n?"":e.keyCode===37?o?"next":"previous":e.keyCode===39?o?"previous":"next":""};x.swipeStart=function(e,n,o){return e.target.tagName==="IMG"&&le(e),!n||!o&&e.type.indexOf("mouse")!==-1?"":{dragging:!0,touchObject:{startX:e.touches?e.touches[0].pageX:e.clientX,startY:e.touches?e.touches[0].pageY:e.clientY,curX:e.touches?e.touches[0].pageX:e.clientX,curY:e.touches?e.touches[0].pageY:e.clientY}}};x.swipeMove=function(e,n){var o=n.scrolling,t=n.animating,s=n.vertical,a=n.swipeToSlide,l=n.verticalSwiping,c=n.rtl,f=n.currentSlide,b=n.edgeFriction,p=n.edgeDragged,w=n.onEdge,C=n.swiped,O=n.swiping,y=n.slideCount,S=n.slidesToScroll,k=n.infinite,u=n.touchObject,h=n.swipeEvent,T=n.listHeight,E=n.listWidth;if(!o){if(t)return le(e);s&&a&&l&&le(e);var _,W={},q=Oe(n);u.curX=e.touches?e.touches[0].pageX:e.clientX,u.curY=e.touches?e.touches[0].pageY:e.clientY,u.swipeLength=Math.round(Math.sqrt(Math.pow(u.curX-u.startX,2)));var g=Math.round(Math.sqrt(Math.pow(u.curY-u.startY,2)));if(!l&&!O&&g>10)return{scrolling:!0};l&&(u.swipeLength=g);var m=(c?-1:1)*(u.curX>u.startX?1:-1);l&&(m=u.curY>u.startY?1:-1);var D=Math.ceil(y/S),P=Ut(n.touchObject,l),z=u.swipeLength;return k||(f===0&&(P==="right"||P==="down")||f+1>=D&&(P==="left"||P==="up")||!Kt(n)&&(P==="left"||P==="up"))&&(z=u.swipeLength*b,p===!1&&w&&(w(P),W.edgeDragged=!0)),!C&&h&&(h(P),W.swiped=!0),s?_=q+z*(T/E)*m:c?_=q-z*m:_=q+z*m,l&&(_=q+z*m),W=H(H({},W),{},{touchObject:u,swipeLeft:_,trackStyle:je(H(H({},n),{},{left:_}))}),Math.abs(u.curX-u.startX)<Math.abs(u.curY-u.startY)*.8||u.swipeLength>10&&(W.swiping=!0,le(e)),W}};x.swipeEnd=function(e,n){var o=n.dragging,t=n.swipe,s=n.touchObject,a=n.listWidth,l=n.touchThreshold,c=n.verticalSwiping,f=n.listHeight,b=n.swipeToSlide,p=n.scrolling,w=n.onSwipe,C=n.targetSlide,O=n.currentSlide,y=n.infinite;if(!o)return t&&le(e),{};var S=c?f/l:a/l,k=Ut(s,c),u={dragging:!1,edgeDragged:!1,scrolling:!1,swiping:!1,swiped:!1,swipeLeft:null,touchObject:{}};if(p||!s.swipeLength)return u;if(s.swipeLength>S){le(e),w&&w(k);var h,T,E=y?O:C;switch(k){case"left":case"up":T=E+wt(n),h=b?St(n,T):T,u.currentDirection=0;break;case"right":case"down":T=E-wt(n),h=b?St(n,T):T,u.currentDirection=1;break;default:h=E}u.triggerSlideHandler=h}else{var _=Oe(n);u.trackStyle=Xt(H(H({},n),{},{left:_}))}return u};var In=x.getNavigableIndexes=function(e){for(var n=e.infinite?e.slideCount*2:e.slideCount,o=e.infinite?e.slidesToShow*-1:0,t=e.infinite?e.slidesToShow*-1:0,s=[];o<n;)s.push(o),o=t+e.slidesToScroll,t+=Math.min(e.slidesToScroll,e.slidesToShow);return s},St=x.checkNavigable=function(e,n){var o=In(e),t=0;if(n>o[o.length-1])n=o[o.length-1];else for(var s in o){if(n<o[s]){n=t;break}t=o[s]}return n},wt=x.getSlideCount=function(e){var n=e.centerMode?e.slideWidth*Math.floor(e.slidesToShow/2):0;if(e.swipeToSlide){var o,t=e.listRef,s=t.querySelectorAll&&t.querySelectorAll(".slick-slide")||[];if(Array.from(s).every(function(c){if(e.vertical){if(c.offsetTop+Gt(c)/2>e.swipeLeft*-1)return o=c,!1}else if(c.offsetLeft-n+rt(c)/2>e.swipeLeft*-1)return o=c,!1;return!0}),!o)return 0;var a=e.rtl===!0?e.slideCount-e.currentSlide:e.currentSlide,l=Math.abs(o.dataset.index-a)||1;return l}else return e.slidesToScroll},ft=x.checkSpecKeys=function(e,n){return n.reduce(function(o,t){return o&&e.hasOwnProperty(t)},!0)?null:console.error("Keys Missing:",e)},je=x.getTrackCSS=function(e){ft(e,["left","variableWidth","slideCount","slidesToShow","slideWidth"]);var n,o,t=e.slideCount+2*e.slidesToShow;e.vertical?o=t*e.slideHeight:n=Mn(e)*e.slideWidth;var s={opacity:1,transition:"",WebkitTransition:""};if(e.useTransform){var a=e.vertical?"translate3d(0px, "+e.left+"px, 0px)":"translate3d("+e.left+"px, 0px, 0px)",l=e.vertical?"translate3d(0px, "+e.left+"px, 0px)":"translate3d("+e.left+"px, 0px, 0px)",c=e.vertical?"translateY("+e.left+"px)":"translateX("+e.left+"px)";s=H(H({},s),{},{WebkitTransform:a,transform:l,msTransform:c})}else e.vertical?s.top=e.left:s.left=e.left;return e.fade&&(s={opacity:1}),n&&(s.width=n),o&&(s.height=o),window&&!window.addEventListener&&window.attachEvent&&(e.vertical?s.marginTop=e.left+"px":s.marginLeft=e.left+"px"),s},Xt=x.getTrackAnimateCSS=function(e){ft(e,["left","variableWidth","slideCount","slidesToShow","slideWidth","speed","cssEase"]);var n=je(e);return e.useTransform?(n.WebkitTransition="-webkit-transform "+e.speed+"ms "+e.cssEase,n.transition="transform "+e.speed+"ms "+e.cssEase):e.vertical?n.transition="top "+e.speed+"ms "+e.cssEase:n.transition="left "+e.speed+"ms "+e.cssEase,n},Oe=x.getTrackLeft=function(e){if(e.unslick)return 0;ft(e,["slideIndex","trackRef","infinite","centerMode","slideCount","slidesToShow","slidesToScroll","slideWidth","listWidth","variableWidth","slideHeight"]);var n=e.slideIndex,o=e.trackRef,t=e.infinite,s=e.centerMode,a=e.slideCount,l=e.slidesToShow,c=e.slidesToScroll,f=e.slideWidth,b=e.listWidth,p=e.variableWidth,w=e.slideHeight,C=e.fade,O=e.vertical,y=0,S,k,u=0;if(C||e.slideCount===1)return 0;var h=0;if(t?(h=-we(e),a%c!==0&&n+c>a&&(h=-(n>a?l-(n-a):a%c)),s&&(h+=parseInt(l/2))):(a%c!==0&&n+c>a&&(h=l-a%c),s&&(h=parseInt(l/2))),y=h*f,u=h*w,O?S=n*w*-1+u:S=n*f*-1+y,p===!0){var T,E=o&&o.node;if(T=n+we(e),k=E&&E.childNodes[T],S=k?k.offsetLeft*-1:0,s===!0){T=t?n+we(e):n,k=E&&E.children[T],S=0;for(var _=0;_<T;_++)S-=E&&E.children[_]&&E.children[_].offsetWidth;S-=parseInt(e.centerPadding),S+=k&&(b-k.offsetWidth)/2}}return S},we=x.getPreClones=function(e){return e.unslick||!e.infinite?0:e.variableWidth?e.slideCount:e.slidesToShow+(e.centerMode?1:0)},Dn=x.getPostClones=function(e){return e.unslick||!e.infinite?0:e.slideCount},Mn=x.getTotalSlides=function(e){return e.slideCount===1?1:we(e)+e.slideCount+Dn(e)},Ln=x.siblingDirection=function(e){return e.targetSlide>e.currentSlide?e.targetSlide>e.currentSlide+Rn(e)?"left":"right":e.targetSlide<e.currentSlide-An(e)?"right":"left"},Rn=x.slidesOnRight=function(e){var n=e.slidesToShow,o=e.centerMode,t=e.rtl,s=e.centerPadding;if(o){var a=(n-1)/2+1;return parseInt(s)>0&&(a+=1),t&&n%2===0&&(a+=1),a}return t?0:n-1},An=x.slidesOnLeft=function(e){var n=e.slidesToShow,o=e.centerMode,t=e.rtl,s=e.centerPadding;if(o){var a=(n-1)/2+1;return parseInt(s)>0&&(a+=1),!t&&n%2===0&&(a+=1),a}return t?n-1:0};x.canUseDOM=function(){return!!(typeof window<"u"&&window.document&&window.document.createElement)};var Wn=x.validSettings=Object.keys(Pn.default);function Hn(r){return Wn.reduce(function(e,n){return r.hasOwnProperty(n)&&(e[n]=r[n]),e},{})}var Re={};Object.defineProperty(Re,"__esModule",{value:!0});Re.Track=void 0;var re=Qt(Q),Ge=Qt(Le),Ue=x;function Qt(r){return r&&r.__esModule?r:{default:r}}function ue(r){"@babel/helpers - typeof";return ue=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ue(r)}function nt(){return nt=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])}return r},nt.apply(this,arguments)}function Nn(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Bn(r,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(r,Jt(o.key),o)}}function $n(r,e,n){return e&&Bn(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function Fn(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&it(r,e)}function it(r,e){return it=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,t){return o.__proto__=t,o},it(r,e)}function qn(r){var e=Vt();return function(){var o=ke(r),t;if(e){var s=ke(this).constructor;t=Reflect.construct(o,arguments,s)}else t=o.apply(this,arguments);return Yn(this,t)}}function Yn(r,e){if(e&&(ue(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ot(r)}function ot(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function Vt(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vt=function(){return!!r})()}function ke(r){return ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ke(r)}function jt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})),n.push.apply(n,o)}return n}function X(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?jt(Object(n),!0).forEach(function(o){at(r,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):jt(Object(n)).forEach(function(o){Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(n,o))})}return r}function at(r,e,n){return e=Jt(e),e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}function Jt(r){var e=Gn(r,"string");return ue(e)=="symbol"?e:String(e)}function Gn(r,e){if(ue(r)!="object"||!r)return r;var n=r[Symbol.toPrimitive];if(n!==void 0){var o=n.call(r,e);if(ue(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}var Ke=function(e){var n,o,t,s,a;e.rtl?a=e.slideCount-1-e.index:a=e.index,t=a<0||a>=e.slideCount,e.centerMode?(s=Math.floor(e.slidesToShow/2),o=(a-e.currentSlide)%e.slideCount===0,a>e.currentSlide-s-1&&a<=e.currentSlide+s&&(n=!0)):n=e.currentSlide<=a&&a<e.currentSlide+e.slidesToShow;var l;e.targetSlide<0?l=e.targetSlide+e.slideCount:e.targetSlide>=e.slideCount?l=e.targetSlide-e.slideCount:l=e.targetSlide;var c=a===l;return{"slick-slide":!0,"slick-active":n,"slick-center":o,"slick-cloned":t,"slick-current":c}},Un=function(e){var n={};return(e.variableWidth===void 0||e.variableWidth===!1)&&(n.width=e.slideWidth),e.fade&&(n.position="relative",e.vertical?n.top=-e.index*parseInt(e.slideHeight):n.left=-e.index*parseInt(e.slideWidth),n.opacity=e.currentSlide===e.index?1:0,n.zIndex=e.currentSlide===e.index?999:998,e.useCSS&&(n.transition="opacity "+e.speed+"ms "+e.cssEase+", visibility "+e.speed+"ms "+e.cssEase)),n},Xe=function(e,n){return e.key||n},Kn=function(e){var n,o=[],t=[],s=[],a=re.default.Children.count(e.children),l=(0,Ue.lazyStartIndex)(e),c=(0,Ue.lazyEndIndex)(e);return re.default.Children.forEach(e.children,function(f,b){var p,w={message:"children",index:b,slidesToScroll:e.slidesToScroll,currentSlide:e.currentSlide};!e.lazyLoad||e.lazyLoad&&e.lazyLoadedList.indexOf(b)>=0?p=f:p=re.default.createElement("div",null);var C=Un(X(X({},e),{},{index:b})),O=p.props.className||"",y=Ke(X(X({},e),{},{index:b}));if(o.push(re.default.cloneElement(p,{key:"original"+Xe(p,b),"data-index":b,className:(0,Ge.default)(y,O),tabIndex:"-1","aria-hidden":!y["slick-active"],style:X(X({outline:"none"},p.props.style||{}),C),onClick:function(u){p.props&&p.props.onClick&&p.props.onClick(u),e.focusOnSelect&&e.focusOnSelect(w)}})),e.infinite&&e.fade===!1){var S=a-b;S<=(0,Ue.getPreClones)(e)&&(n=-S,n>=l&&(p=f),y=Ke(X(X({},e),{},{index:n})),t.push(re.default.cloneElement(p,{key:"precloned"+Xe(p,n),"data-index":n,tabIndex:"-1",className:(0,Ge.default)(y,O),"aria-hidden":!y["slick-active"],style:X(X({},p.props.style||{}),C),onClick:function(u){p.props&&p.props.onClick&&p.props.onClick(u),e.focusOnSelect&&e.focusOnSelect(w)}}))),n=a+b,n<c&&(p=f),y=Ke(X(X({},e),{},{index:n})),s.push(re.default.cloneElement(p,{key:"postcloned"+Xe(p,n),"data-index":n,tabIndex:"-1",className:(0,Ge.default)(y,O),"aria-hidden":!y["slick-active"],style:X(X({},p.props.style||{}),C),onClick:function(u){p.props&&p.props.onClick&&p.props.onClick(u),e.focusOnSelect&&e.focusOnSelect(w)}}))}}),e.rtl?t.concat(o,s).reverse():t.concat(o,s)};Re.Track=function(r){Fn(n,r);var e=qn(n);function n(){var o;Nn(this,n);for(var t=arguments.length,s=new Array(t),a=0;a<t;a++)s[a]=arguments[a];return o=e.call.apply(e,[this].concat(s)),at(ot(o),"node",null),at(ot(o),"handleRef",function(l){o.node=l}),o}return $n(n,[{key:"render",value:function(){var t=Kn(this.props),s=this.props,a=s.onMouseEnter,l=s.onMouseOver,c=s.onMouseLeave,f={onMouseEnter:a,onMouseOver:l,onMouseLeave:c};return re.default.createElement("div",nt({ref:this.handleRef,className:"slick-track",style:this.props.trackStyle},f),t)}}]),n}(re.default.PureComponent);var Ae={};function de(r){"@babel/helpers - typeof";return de=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(r)}Object.defineProperty(Ae,"__esModule",{value:!0});Ae.Dots=void 0;var Se=Zt(Q),Xn=Zt(Le),Ot=x;function Zt(r){return r&&r.__esModule?r:{default:r}}function kt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})),n.push.apply(n,o)}return n}function Qn(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?kt(Object(n),!0).forEach(function(o){Vn(r,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):kt(Object(n)).forEach(function(o){Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(n,o))})}return r}function Vn(r,e,n){return e=er(e),e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}function Jn(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Zn(r,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(r,er(o.key),o)}}function ei(r,e,n){return e&&Zn(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function er(r){var e=ti(r,"string");return de(e)=="symbol"?e:String(e)}function ti(r,e){if(de(r)!="object"||!r)return r;var n=r[Symbol.toPrimitive];if(n!==void 0){var o=n.call(r,e);if(de(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}function ri(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&st(r,e)}function st(r,e){return st=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,t){return o.__proto__=t,o},st(r,e)}function ni(r){var e=tr();return function(){var o=Pe(r),t;if(e){var s=Pe(this).constructor;t=Reflect.construct(o,arguments,s)}else t=o.apply(this,arguments);return ii(this,t)}}function ii(r,e){if(e&&(de(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oi(r)}function oi(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function tr(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(tr=function(){return!!r})()}function Pe(r){return Pe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Pe(r)}var ai=function(e){var n;return e.infinite?n=Math.ceil(e.slideCount/e.slidesToScroll):n=Math.ceil((e.slideCount-e.slidesToShow)/e.slidesToScroll)+1,n};Ae.Dots=function(r){ri(n,r);var e=ni(n);function n(){return Jn(this,n),e.apply(this,arguments)}return ei(n,[{key:"clickHandler",value:function(t,s){s.preventDefault(),this.props.clickHandler(t)}},{key:"render",value:function(){for(var t=this.props,s=t.onMouseEnter,a=t.onMouseOver,l=t.onMouseLeave,c=t.infinite,f=t.slidesToScroll,b=t.slidesToShow,p=t.slideCount,w=t.currentSlide,C=ai({slideCount:p,slidesToScroll:f,slidesToShow:b,infinite:c}),O={onMouseEnter:s,onMouseOver:a,onMouseLeave:l},y=[],S=0;S<C;S++){var k=(S+1)*f-1,u=c?k:(0,Ot.clamp)(k,0,p-1),h=u-(f-1),T=c?h:(0,Ot.clamp)(h,0,p-1),E=(0,Xn.default)({"slick-active":c?w>=T&&w<=u:w===T}),_={message:"dots",index:S,slidesToScroll:f,currentSlide:w},W=this.clickHandler.bind(this,_);y=y.concat(Se.default.createElement("li",{key:S,className:E},Se.default.cloneElement(this.props.customPaging(S),{onClick:W})))}return Se.default.cloneElement(this.props.appendDots(y),Qn({className:this.props.dotsClass},O))}}]),n}(Se.default.PureComponent);var fe={};function he(r){"@babel/helpers - typeof";return he=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},he(r)}Object.defineProperty(fe,"__esModule",{value:!0});fe.PrevArrow=fe.NextArrow=void 0;var ce=nr(Q),rr=nr(Le),si=x;function nr(r){return r&&r.__esModule?r:{default:r}}function Ce(){return Ce=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])}return r},Ce.apply(this,arguments)}function Pt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})),n.push.apply(n,o)}return n}function _e(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Pt(Object(n),!0).forEach(function(o){li(r,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):Pt(Object(n)).forEach(function(o){Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(n,o))})}return r}function li(r,e,n){return e=ar(e),e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}function ir(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function ci(r,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(r,ar(o.key),o)}}function or(r,e,n){return e&&ci(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function ar(r){var e=ui(r,"string");return he(e)=="symbol"?e:String(e)}function ui(r,e){if(he(r)!="object"||!r)return r;var n=r[Symbol.toPrimitive];if(n!==void 0){var o=n.call(r,e);if(he(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}function sr(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&lt(r,e)}function lt(r,e){return lt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,t){return o.__proto__=t,o},lt(r,e)}function lr(r){var e=cr();return function(){var o=Te(r),t;if(e){var s=Te(this).constructor;t=Reflect.construct(o,arguments,s)}else t=o.apply(this,arguments);return di(this,t)}}function di(r,e){if(e&&(he(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fi(r)}function fi(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function cr(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(cr=function(){return!!r})()}function Te(r){return Te=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Te(r)}fe.PrevArrow=function(r){sr(n,r);var e=lr(n);function n(){return ir(this,n),e.apply(this,arguments)}return or(n,[{key:"clickHandler",value:function(t,s){s&&s.preventDefault(),this.props.clickHandler(t,s)}},{key:"render",value:function(){var t={"slick-arrow":!0,"slick-prev":!0},s=this.clickHandler.bind(this,{message:"previous"});!this.props.infinite&&(this.props.currentSlide===0||this.props.slideCount<=this.props.slidesToShow)&&(t["slick-disabled"]=!0,s=null);var a={key:"0","data-role":"none",className:(0,rr.default)(t),style:{display:"block"},onClick:s},l={currentSlide:this.props.currentSlide,slideCount:this.props.slideCount},c;return this.props.prevArrow?c=ce.default.cloneElement(this.props.prevArrow,_e(_e({},a),l)):c=ce.default.createElement("button",Ce({key:"0",type:"button"},a)," ","Previous"),c}}]),n}(ce.default.PureComponent);fe.NextArrow=function(r){sr(n,r);var e=lr(n);function n(){return ir(this,n),e.apply(this,arguments)}return or(n,[{key:"clickHandler",value:function(t,s){s&&s.preventDefault(),this.props.clickHandler(t,s)}},{key:"render",value:function(){var t={"slick-arrow":!0,"slick-next":!0},s=this.clickHandler.bind(this,{message:"next"});(0,si.canGoNext)(this.props)||(t["slick-disabled"]=!0,s=null);var a={key:"1","data-role":"none",className:(0,rr.default)(t),style:{display:"block"},onClick:s},l={currentSlide:this.props.currentSlide,slideCount:this.props.slideCount},c;return this.props.nextArrow?c=ce.default.cloneElement(this.props.nextArrow,_e(_e({},a),l)):c=ce.default.createElement("button",Ce({key:"1",type:"button"},a)," ","Next"),c}}]),n}(ce.default.PureComponent);var ur=function(){if(typeof Map<"u")return Map;function r(e,n){var o=-1;return e.some(function(t,s){return t[0]===n?(o=s,!0):!1}),o}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(n){var o=r(this.__entries__,n),t=this.__entries__[o];return t&&t[1]},e.prototype.set=function(n,o){var t=r(this.__entries__,n);~t?this.__entries__[t][1]=o:this.__entries__.push([n,o])},e.prototype.delete=function(n){var o=this.__entries__,t=r(o,n);~t&&o.splice(t,1)},e.prototype.has=function(n){return!!~r(this.__entries__,n)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(n,o){o===void 0&&(o=null);for(var t=0,s=this.__entries__;t<s.length;t++){var a=s[t];n.call(o,a[1],a[0])}},e}()}(),ct=typeof window<"u"&&typeof document<"u"&&window.document===document,Ee=function(){return typeof qe<"u"&&qe.Math===Math?qe:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),hi=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Ee):function(r){return setTimeout(function(){return r(Date.now())},1e3/60)}}(),pi=2;function xi(r,e){var n=!1,o=!1,t=0;function s(){n&&(n=!1,r()),o&&l()}function a(){hi(s)}function l(){var c=Date.now();if(n){if(c-t<pi)return;o=!0}else n=!0,o=!1,setTimeout(a,e);t=c}return l}var vi=20,gi=["top","right","bottom","left","width","height","size","weight"],mi=typeof MutationObserver<"u",bi=function(){function r(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=xi(this.refresh.bind(this),vi)}return r.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},r.prototype.removeObserver=function(e){var n=this.observers_,o=n.indexOf(e);~o&&n.splice(o,1),!n.length&&this.connected_&&this.disconnect_()},r.prototype.refresh=function(){var e=this.updateObservers_();e&&this.refresh()},r.prototype.updateObservers_=function(){var e=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return e.forEach(function(n){return n.broadcastActive()}),e.length>0},r.prototype.connect_=function(){!ct||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),mi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},r.prototype.disconnect_=function(){!ct||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},r.prototype.onTransitionEnd_=function(e){var n=e.propertyName,o=n===void 0?"":n,t=gi.some(function(s){return!!~o.indexOf(s)});t&&this.refresh()},r.getInstance=function(){return this.instance_||(this.instance_=new r),this.instance_},r.instance_=null,r}(),dr=function(r,e){for(var n=0,o=Object.keys(e);n<o.length;n++){var t=o[n];Object.defineProperty(r,t,{value:e[t],enumerable:!1,writable:!1,configurable:!0})}return r},pe=function(r){var e=r&&r.ownerDocument&&r.ownerDocument.defaultView;return e||Ee},fr=We(0,0,0,0);function ze(r){return parseFloat(r)||0}function Ct(r){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.reduce(function(o,t){var s=r["border-"+t+"-width"];return o+ze(s)},0)}function yi(r){for(var e=["top","right","bottom","left"],n={},o=0,t=e;o<t.length;o++){var s=t[o],a=r["padding-"+s];n[s]=ze(a)}return n}function Si(r){var e=r.getBBox();return We(0,0,e.width,e.height)}function wi(r){var e=r.clientWidth,n=r.clientHeight;if(!e&&!n)return fr;var o=pe(r).getComputedStyle(r),t=yi(o),s=t.left+t.right,a=t.top+t.bottom,l=ze(o.width),c=ze(o.height);if(o.boxSizing==="border-box"&&(Math.round(l+s)!==e&&(l-=Ct(o,"left","right")+s),Math.round(c+a)!==n&&(c-=Ct(o,"top","bottom")+a)),!Oi(r)){var f=Math.round(l+s)-e,b=Math.round(c+a)-n;Math.abs(f)!==1&&(l-=f),Math.abs(b)!==1&&(c-=b)}return We(t.left,t.top,l,c)}var ji=function(){return typeof SVGGraphicsElement<"u"?function(r){return r instanceof pe(r).SVGGraphicsElement}:function(r){return r instanceof pe(r).SVGElement&&typeof r.getBBox=="function"}}();function Oi(r){return r===pe(r).document.documentElement}function ki(r){return ct?ji(r)?Si(r):wi(r):fr}function Pi(r){var e=r.x,n=r.y,o=r.width,t=r.height,s=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,a=Object.create(s.prototype);return dr(a,{x:e,y:n,width:o,height:t,top:n,right:e+o,bottom:t+n,left:e}),a}function We(r,e,n,o){return{x:r,y:e,width:n,height:o}}var Ci=function(){function r(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=We(0,0,0,0),this.target=e}return r.prototype.isActive=function(){var e=ki(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},r.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},r}(),_i=function(){function r(e,n){var o=Pi(n);dr(this,{target:e,contentRect:o})}return r}(),Ti=function(){function r(e,n,o){if(this.activeObservations_=[],this.observations_=new ur,typeof e!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=n,this.callbackCtx_=o}return r.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof pe(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(e)||(n.set(e,new Ci(e)),this.controller_.addObserver(this),this.controller_.refresh())}},r.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof pe(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(e)&&(n.delete(e),n.size||this.controller_.removeObserver(this))}},r.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},r.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&e.activeObservations_.push(n)})},r.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,n=this.activeObservations_.map(function(o){return new _i(o.target,o.broadcastRect())});this.callback_.call(e,n,e),this.clearActive()}},r.prototype.clearActive=function(){this.activeObservations_.splice(0)},r.prototype.hasActive=function(){return this.activeObservations_.length>0},r}(),hr=typeof WeakMap<"u"?new WeakMap:new ur,pr=function(){function r(e){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=bi.getInstance(),o=new Ti(e,n,this);hr.set(this,o)}return r}();["observe","unobserve","disconnect"].forEach(function(r){pr.prototype[r]=function(){var e;return(e=hr.get(this))[r].apply(e,arguments)}});var Ei=function(){return typeof Ee.ResizeObserver<"u"?Ee.ResizeObserver:pr}();const zi=Object.freeze(Object.defineProperty({__proto__:null,default:Ei},Symbol.toStringTag,{value:"Module"})),Ii=tn(zi);Object.defineProperty(Me,"__esModule",{value:!0});Me.InnerSlider=void 0;var G=me(Q),Di=me(Ht),Mi=me(On),Li=me(Le),$=x,Ri=Re,Ai=Ae,_t=fe,Wi=me(Ii);function me(r){return r&&r.__esModule?r:{default:r}}function oe(r){"@babel/helpers - typeof";return oe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(r)}function Ie(){return Ie=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])}return r},Ie.apply(this,arguments)}function Hi(r,e){if(r==null)return{};var n=Ni(r,e),o,t;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(r);for(t=0;t<s.length;t++)o=s[t],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(r,o)&&(n[o]=r[o])}return n}function Ni(r,e){if(r==null)return{};var n={},o=Object.keys(r),t,s;for(s=0;s<o.length;s++)t=o[s],!(e.indexOf(t)>=0)&&(n[t]=r[t]);return n}function Tt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})),n.push.apply(n,o)}return n}function j(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Tt(Object(n),!0).forEach(function(o){L(r,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):Tt(Object(n)).forEach(function(o){Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(n,o))})}return r}function Bi(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function $i(r,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(r,vr(o.key),o)}}function Fi(r,e,n){return e&&$i(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function qi(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&ut(r,e)}function ut(r,e){return ut=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,t){return o.__proto__=t,o},ut(r,e)}function Yi(r){var e=xr();return function(){var o=De(r),t;if(e){var s=De(this).constructor;t=Reflect.construct(o,arguments,s)}else t=o.apply(this,arguments);return Gi(this,t)}}function Gi(r,e){if(e&&(oe(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return M(r)}function M(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function xr(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xr=function(){return!!r})()}function De(r){return De=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},De(r)}function L(r,e,n){return e=vr(e),e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}function vr(r){var e=Ui(r,"string");return oe(e)=="symbol"?e:String(e)}function Ui(r,e){if(oe(r)!="object"||!r)return r;var n=r[Symbol.toPrimitive];if(n!==void 0){var o=n.call(r,e);if(oe(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}Me.InnerSlider=function(r){qi(n,r);var e=Yi(n);function n(o){var t;Bi(this,n),t=e.call(this,o),L(M(t),"listRefHandler",function(a){return t.list=a}),L(M(t),"trackRefHandler",function(a){return t.track=a}),L(M(t),"adaptHeight",function(){if(t.props.adaptiveHeight&&t.list){var a=t.list.querySelector('[data-index="'.concat(t.state.currentSlide,'"]'));t.list.style.height=(0,$.getHeight)(a)+"px"}}),L(M(t),"componentDidMount",function(){if(t.props.onInit&&t.props.onInit(),t.props.lazyLoad){var a=(0,$.getOnDemandLazySlides)(j(j({},t.props),t.state));a.length>0&&(t.setState(function(c){return{lazyLoadedList:c.lazyLoadedList.concat(a)}}),t.props.onLazyLoad&&t.props.onLazyLoad(a))}var l=j({listRef:t.list,trackRef:t.track},t.props);t.updateState(l,!0,function(){t.adaptHeight(),t.props.autoplay&&t.autoPlay("update")}),t.props.lazyLoad==="progressive"&&(t.lazyLoadTimer=setInterval(t.progressiveLazyLoad,1e3)),t.ro=new Wi.default(function(){t.state.animating?(t.onWindowResized(!1),t.callbackTimers.push(setTimeout(function(){return t.onWindowResized()},t.props.speed))):t.onWindowResized()}),t.ro.observe(t.list),document.querySelectorAll&&Array.prototype.forEach.call(document.querySelectorAll(".slick-slide"),function(c){c.onfocus=t.props.pauseOnFocus?t.onSlideFocus:null,c.onblur=t.props.pauseOnFocus?t.onSlideBlur:null}),window.addEventListener?window.addEventListener("resize",t.onWindowResized):window.attachEvent("onresize",t.onWindowResized)}),L(M(t),"componentWillUnmount",function(){t.animationEndCallback&&clearTimeout(t.animationEndCallback),t.lazyLoadTimer&&clearInterval(t.lazyLoadTimer),t.callbackTimers.length&&(t.callbackTimers.forEach(function(a){return clearTimeout(a)}),t.callbackTimers=[]),window.addEventListener?window.removeEventListener("resize",t.onWindowResized):window.detachEvent("onresize",t.onWindowResized),t.autoplayTimer&&clearInterval(t.autoplayTimer),t.ro.disconnect()}),L(M(t),"componentDidUpdate",function(a){if(t.checkImagesLoad(),t.props.onReInit&&t.props.onReInit(),t.props.lazyLoad){var l=(0,$.getOnDemandLazySlides)(j(j({},t.props),t.state));l.length>0&&(t.setState(function(b){return{lazyLoadedList:b.lazyLoadedList.concat(l)}}),t.props.onLazyLoad&&t.props.onLazyLoad(l))}t.adaptHeight();var c=j(j({listRef:t.list,trackRef:t.track},t.props),t.state),f=t.didPropsChange(a);f&&t.updateState(c,f,function(){t.state.currentSlide>=G.default.Children.count(t.props.children)&&t.changeSlide({message:"index",index:G.default.Children.count(t.props.children)-t.props.slidesToShow,currentSlide:t.state.currentSlide}),t.props.autoplay?t.autoPlay("update"):t.pause("paused")})}),L(M(t),"onWindowResized",function(a){t.debouncedResize&&t.debouncedResize.cancel(),t.debouncedResize=(0,Mi.default)(function(){return t.resizeWindow(a)},50),t.debouncedResize()}),L(M(t),"resizeWindow",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,l=!!(t.track&&t.track.node);if(l){var c=j(j({listRef:t.list,trackRef:t.track},t.props),t.state);t.updateState(c,a,function(){t.props.autoplay?t.autoPlay("update"):t.pause("paused")}),t.setState({animating:!1}),clearTimeout(t.animationEndCallback),delete t.animationEndCallback}}),L(M(t),"updateState",function(a,l,c){var f=(0,$.initializedState)(a);a=j(j(j({},a),f),{},{slideIndex:f.currentSlide});var b=(0,$.getTrackLeft)(a);a=j(j({},a),{},{left:b});var p=(0,$.getTrackCSS)(a);(l||G.default.Children.count(t.props.children)!==G.default.Children.count(a.children))&&(f.trackStyle=p),t.setState(f,c)}),L(M(t),"ssrInit",function(){if(t.props.variableWidth){var a=0,l=0,c=[],f=(0,$.getPreClones)(j(j(j({},t.props),t.state),{},{slideCount:t.props.children.length})),b=(0,$.getPostClones)(j(j(j({},t.props),t.state),{},{slideCount:t.props.children.length}));t.props.children.forEach(function(W){c.push(W.props.style.width),a+=W.props.style.width});for(var p=0;p<f;p++)l+=c[c.length-1-p],a+=c[c.length-1-p];for(var w=0;w<b;w++)a+=c[w];for(var C=0;C<t.state.currentSlide;C++)l+=c[C];var O={width:a+"px",left:-l+"px"};if(t.props.centerMode){var y="".concat(c[t.state.currentSlide],"px");O.left="calc(".concat(O.left," + (100% - ").concat(y,") / 2 ) ")}return{trackStyle:O}}var S=G.default.Children.count(t.props.children),k=j(j(j({},t.props),t.state),{},{slideCount:S}),u=(0,$.getPreClones)(k)+(0,$.getPostClones)(k)+S,h=100/t.props.slidesToShow*u,T=100/u,E=-T*((0,$.getPreClones)(k)+t.state.currentSlide)*h/100;t.props.centerMode&&(E+=(100-T*h/100)/2);var _={width:h+"%",left:E+"%"};return{slideWidth:T+"%",trackStyle:_}}),L(M(t),"checkImagesLoad",function(){var a=t.list&&t.list.querySelectorAll&&t.list.querySelectorAll(".slick-slide img")||[],l=a.length,c=0;Array.prototype.forEach.call(a,function(f){var b=function(){return++c&&c>=l&&t.onWindowResized()};if(!f.onclick)f.onclick=function(){return f.parentNode.focus()};else{var p=f.onclick;f.onclick=function(w){p(w),f.parentNode.focus()}}f.onload||(t.props.lazyLoad?f.onload=function(){t.adaptHeight(),t.callbackTimers.push(setTimeout(t.onWindowResized,t.props.speed))}:(f.onload=b,f.onerror=function(){b(),t.props.onLazyLoadError&&t.props.onLazyLoadError()}))})}),L(M(t),"progressiveLazyLoad",function(){for(var a=[],l=j(j({},t.props),t.state),c=t.state.currentSlide;c<t.state.slideCount+(0,$.getPostClones)(l);c++)if(t.state.lazyLoadedList.indexOf(c)<0){a.push(c);break}for(var f=t.state.currentSlide-1;f>=-(0,$.getPreClones)(l);f--)if(t.state.lazyLoadedList.indexOf(f)<0){a.push(f);break}a.length>0?(t.setState(function(b){return{lazyLoadedList:b.lazyLoadedList.concat(a)}}),t.props.onLazyLoad&&t.props.onLazyLoad(a)):t.lazyLoadTimer&&(clearInterval(t.lazyLoadTimer),delete t.lazyLoadTimer)}),L(M(t),"slideHandler",function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=t.props,f=c.asNavFor,b=c.beforeChange,p=c.onLazyLoad,w=c.speed,C=c.afterChange,O=t.state.currentSlide,y=(0,$.slideHandler)(j(j(j({index:a},t.props),t.state),{},{trackRef:t.track,useCSS:t.props.useCSS&&!l})),S=y.state,k=y.nextState;if(S){b&&b(O,S.currentSlide);var u=S.lazyLoadedList.filter(function(h){return t.state.lazyLoadedList.indexOf(h)<0});p&&u.length>0&&p(u),!t.props.waitForAnimate&&t.animationEndCallback&&(clearTimeout(t.animationEndCallback),C&&C(O),delete t.animationEndCallback),t.setState(S,function(){f&&t.asNavForIndex!==a&&(t.asNavForIndex=a,f.innerSlider.slideHandler(a)),k&&(t.animationEndCallback=setTimeout(function(){var h=k.animating,T=Hi(k,["animating"]);t.setState(T,function(){t.callbackTimers.push(setTimeout(function(){return t.setState({animating:h})},10)),C&&C(S.currentSlide),delete t.animationEndCallback})},w))})}}),L(M(t),"changeSlide",function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=j(j({},t.props),t.state),f=(0,$.changeSlide)(c,a);if(!(f!==0&&!f)&&(l===!0?t.slideHandler(f,l):t.slideHandler(f),t.props.autoplay&&t.autoPlay("update"),t.props.focusOnSelect)){var b=t.list.querySelectorAll(".slick-current");b[0]&&b[0].focus()}}),L(M(t),"clickHandler",function(a){t.clickable===!1&&(a.stopPropagation(),a.preventDefault()),t.clickable=!0}),L(M(t),"keyHandler",function(a){var l=(0,$.keyHandler)(a,t.props.accessibility,t.props.rtl);l!==""&&t.changeSlide({message:l})}),L(M(t),"selectHandler",function(a){t.changeSlide(a)}),L(M(t),"disableBodyScroll",function(){var a=function(c){c=c||window.event,c.preventDefault&&c.preventDefault(),c.returnValue=!1};window.ontouchmove=a}),L(M(t),"enableBodyScroll",function(){window.ontouchmove=null}),L(M(t),"swipeStart",function(a){t.props.verticalSwiping&&t.disableBodyScroll();var l=(0,$.swipeStart)(a,t.props.swipe,t.props.draggable);l!==""&&t.setState(l)}),L(M(t),"swipeMove",function(a){var l=(0,$.swipeMove)(a,j(j(j({},t.props),t.state),{},{trackRef:t.track,listRef:t.list,slideIndex:t.state.currentSlide}));l&&(l.swiping&&(t.clickable=!1),t.setState(l))}),L(M(t),"swipeEnd",function(a){var l=(0,$.swipeEnd)(a,j(j(j({},t.props),t.state),{},{trackRef:t.track,listRef:t.list,slideIndex:t.state.currentSlide}));if(l){var c=l.triggerSlideHandler;delete l.triggerSlideHandler,t.setState(l),c!==void 0&&(t.slideHandler(c),t.props.verticalSwiping&&t.enableBodyScroll())}}),L(M(t),"touchEnd",function(a){t.swipeEnd(a),t.clickable=!0}),L(M(t),"slickPrev",function(){t.callbackTimers.push(setTimeout(function(){return t.changeSlide({message:"previous"})},0))}),L(M(t),"slickNext",function(){t.callbackTimers.push(setTimeout(function(){return t.changeSlide({message:"next"})},0))}),L(M(t),"slickGoTo",function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(a=Number(a),isNaN(a))return"";t.callbackTimers.push(setTimeout(function(){return t.changeSlide({message:"index",index:a,currentSlide:t.state.currentSlide},l)},0))}),L(M(t),"play",function(){var a;if(t.props.rtl)a=t.state.currentSlide-t.props.slidesToScroll;else if((0,$.canGoNext)(j(j({},t.props),t.state)))a=t.state.currentSlide+t.props.slidesToScroll;else return!1;t.slideHandler(a)}),L(M(t),"autoPlay",function(a){t.autoplayTimer&&clearInterval(t.autoplayTimer);var l=t.state.autoplaying;if(a==="update"){if(l==="hovered"||l==="focused"||l==="paused")return}else if(a==="leave"){if(l==="paused"||l==="focused")return}else if(a==="blur"&&(l==="paused"||l==="hovered"))return;t.autoplayTimer=setInterval(t.play,t.props.autoplaySpeed+50),t.setState({autoplaying:"playing"})}),L(M(t),"pause",function(a){t.autoplayTimer&&(clearInterval(t.autoplayTimer),t.autoplayTimer=null);var l=t.state.autoplaying;a==="paused"?t.setState({autoplaying:"paused"}):a==="focused"?(l==="hovered"||l==="playing")&&t.setState({autoplaying:"focused"}):l==="playing"&&t.setState({autoplaying:"hovered"})}),L(M(t),"onDotsOver",function(){return t.props.autoplay&&t.pause("hovered")}),L(M(t),"onDotsLeave",function(){return t.props.autoplay&&t.state.autoplaying==="hovered"&&t.autoPlay("leave")}),L(M(t),"onTrackOver",function(){return t.props.autoplay&&t.pause("hovered")}),L(M(t),"onTrackLeave",function(){return t.props.autoplay&&t.state.autoplaying==="hovered"&&t.autoPlay("leave")}),L(M(t),"onSlideFocus",function(){return t.props.autoplay&&t.pause("focused")}),L(M(t),"onSlideBlur",function(){return t.props.autoplay&&t.state.autoplaying==="focused"&&t.autoPlay("blur")}),L(M(t),"render",function(){var a=(0,Li.default)("slick-slider",t.props.className,{"slick-vertical":t.props.vertical,"slick-initialized":!0}),l=j(j({},t.props),t.state),c=(0,$.extractObject)(l,["fade","cssEase","speed","infinite","centerMode","focusOnSelect","currentSlide","lazyLoad","lazyLoadedList","rtl","slideWidth","slideHeight","listHeight","vertical","slidesToShow","slidesToScroll","slideCount","trackStyle","variableWidth","unslick","centerPadding","targetSlide","useCSS"]),f=t.props.pauseOnHover;c=j(j({},c),{},{onMouseEnter:f?t.onTrackOver:null,onMouseLeave:f?t.onTrackLeave:null,onMouseOver:f?t.onTrackOver:null,focusOnSelect:t.props.focusOnSelect&&t.clickable?t.selectHandler:null});var b;if(t.props.dots===!0&&t.state.slideCount>=t.props.slidesToShow){var p=(0,$.extractObject)(l,["dotsClass","slideCount","slidesToShow","currentSlide","slidesToScroll","clickHandler","children","customPaging","infinite","appendDots"]),w=t.props.pauseOnDotsHover;p=j(j({},p),{},{clickHandler:t.changeSlide,onMouseEnter:w?t.onDotsLeave:null,onMouseOver:w?t.onDotsOver:null,onMouseLeave:w?t.onDotsLeave:null}),b=G.default.createElement(Ai.Dots,p)}var C,O,y=(0,$.extractObject)(l,["infinite","centerMode","currentSlide","slideCount","slidesToShow","prevArrow","nextArrow"]);y.clickHandler=t.changeSlide,t.props.arrows&&(C=G.default.createElement(_t.PrevArrow,y),O=G.default.createElement(_t.NextArrow,y));var S=null;t.props.vertical&&(S={height:t.state.listHeight});var k=null;t.props.vertical===!1?t.props.centerMode===!0&&(k={padding:"0px "+t.props.centerPadding}):t.props.centerMode===!0&&(k={padding:t.props.centerPadding+" 0px"});var u=j(j({},S),k),h=t.props.touchMove,T={className:"slick-list",style:u,onClick:t.clickHandler,onMouseDown:h?t.swipeStart:null,onMouseMove:t.state.dragging&&h?t.swipeMove:null,onMouseUp:h?t.swipeEnd:null,onMouseLeave:t.state.dragging&&h?t.swipeEnd:null,onTouchStart:h?t.swipeStart:null,onTouchMove:t.state.dragging&&h?t.swipeMove:null,onTouchEnd:h?t.touchEnd:null,onTouchCancel:t.state.dragging&&h?t.swipeEnd:null,onKeyDown:t.props.accessibility?t.keyHandler:null},E={className:a,dir:"ltr",style:t.props.style};return t.props.unslick&&(T={className:"slick-list"},E={className:a}),G.default.createElement("div",E,t.props.unslick?"":C,G.default.createElement("div",Ie({ref:t.listRefHandler},T),G.default.createElement(Ri.Track,Ie({ref:t.trackRefHandler},c),t.props.children)),t.props.unslick?"":O,t.props.unslick?"":b)}),t.list=null,t.track=null,t.state=j(j({},Di.default),{},{currentSlide:t.props.initialSlide,targetSlide:t.props.initialSlide?t.props.initialSlide:0,slideCount:G.default.Children.count(t.props.children)}),t.callbackTimers=[],t.clickable=!0,t.debouncedResize=null;var s=t.ssrInit();return t.state=j(j({},t.state),s),t}return Fi(n,[{key:"didPropsChange",value:function(t){for(var s=!1,a=0,l=Object.keys(this.props);a<l.length;a++){var c=l[a];if(!t.hasOwnProperty(c)){s=!0;break}if(!(oe(t[c])==="object"||typeof t[c]=="function"||isNaN(t[c]))&&t[c]!==this.props[c]){s=!0;break}}return s||G.default.Children.count(this.props.children)!==G.default.Children.count(t.children)}}]),n}(G.default.Component);var Ki=function(r){return r.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}).toLowerCase()},Xi=Ki,Qi=Xi,Vi=function(r){var e=/[height|width]$/;return e.test(r)},Et=function(r){var e="",n=Object.keys(r);return n.forEach(function(o,t){var s=r[o];o=Qi(o),Vi(o)&&typeof s=="number"&&(s=s+"px"),s===!0?e+=o:s===!1?e+="not "+o:e+="("+o+": "+s+")",t<n.length-1&&(e+=" and ")}),e},Ji=function(r){var e="";return typeof r=="string"?r:r instanceof Array?(r.forEach(function(n,o){e+=Et(n),o<r.length-1&&(e+=", ")}),e):Et(r)},Zi=Ji,Qe,zt;function eo(){if(zt)return Qe;zt=1;function r(e){this.options=e,!e.deferSetup&&this.setup()}return r.prototype={constructor:r,setup:function(){this.options.setup&&this.options.setup(),this.initialised=!0},on:function(){!this.initialised&&this.setup(),this.options.match&&this.options.match()},off:function(){this.options.unmatch&&this.options.unmatch()},destroy:function(){this.options.destroy?this.options.destroy():this.off()},equals:function(e){return this.options===e||this.options.match===e}},Qe=r,Qe}var Ve,It;function gr(){if(It)return Ve;It=1;function r(o,t){var s=0,a=o.length,l;for(s;s<a&&(l=t(o[s],s),l!==!1);s++);}function e(o){return Object.prototype.toString.apply(o)==="[object Array]"}function n(o){return typeof o=="function"}return Ve={isFunction:n,isArray:e,each:r},Ve}var Je,Dt;function to(){if(Dt)return Je;Dt=1;var r=eo(),e=gr().each;function n(o,t){this.query=o,this.isUnconditional=t,this.handlers=[],this.mql=window.matchMedia(o);var s=this;this.listener=function(a){s.mql=a.currentTarget||a,s.assess()},this.mql.addListener(this.listener)}return n.prototype={constuctor:n,addHandler:function(o){var t=new r(o);this.handlers.push(t),this.matches()&&t.on()},removeHandler:function(o){var t=this.handlers;e(t,function(s,a){if(s.equals(o))return s.destroy(),!t.splice(a,1)})},matches:function(){return this.mql.matches||this.isUnconditional},clear:function(){e(this.handlers,function(o){o.destroy()}),this.mql.removeListener(this.listener),this.handlers.length=0},assess:function(){var o=this.matches()?"on":"off";e(this.handlers,function(t){t[o]()})}},Je=n,Je}var Ze,Mt;function ro(){if(Mt)return Ze;Mt=1;var r=to(),e=gr(),n=e.each,o=e.isFunction,t=e.isArray;function s(){if(!window.matchMedia)throw new Error("matchMedia not present, legacy browsers require a polyfill");this.queries={},this.browserIsIncapable=!window.matchMedia("only all").matches}return s.prototype={constructor:s,register:function(a,l,c){var f=this.queries,b=c&&this.browserIsIncapable;return f[a]||(f[a]=new r(a,b)),o(l)&&(l={match:l}),t(l)||(l=[l]),n(l,function(p){o(p)&&(p={match:p}),f[a].addHandler(p)}),this},unregister:function(a,l){var c=this.queries[a];return c&&(l?c.removeHandler(l):(c.clear(),delete this.queries[a])),this}},Ze=s,Ze}var et,Lt;function no(){if(Lt)return et;Lt=1;var r=ro();return et=new r,et}(function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var e=a(Q),n=Me,o=a(Zi),t=a(dt),s=x;function a(g){return g&&g.__esModule?g:{default:g}}function l(g){"@babel/helpers - typeof";return l=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(m){return typeof m}:function(m){return m&&typeof Symbol=="function"&&m.constructor===Symbol&&m!==Symbol.prototype?"symbol":typeof m},l(g)}function c(){return c=Object.assign?Object.assign.bind():function(g){for(var m=1;m<arguments.length;m++){var D=arguments[m];for(var P in D)Object.prototype.hasOwnProperty.call(D,P)&&(g[P]=D[P])}return g},c.apply(this,arguments)}function f(g,m){var D=Object.keys(g);if(Object.getOwnPropertySymbols){var P=Object.getOwnPropertySymbols(g);m&&(P=P.filter(function(z){return Object.getOwnPropertyDescriptor(g,z).enumerable})),D.push.apply(D,P)}return D}function b(g){for(var m=1;m<arguments.length;m++){var D=arguments[m]!=null?arguments[m]:{};m%2?f(Object(D),!0).forEach(function(P){E(g,P,D[P])}):Object.getOwnPropertyDescriptors?Object.defineProperties(g,Object.getOwnPropertyDescriptors(D)):f(Object(D)).forEach(function(P){Object.defineProperty(g,P,Object.getOwnPropertyDescriptor(D,P))})}return g}function p(g,m){if(!(g instanceof m))throw new TypeError("Cannot call a class as a function")}function w(g,m){for(var D=0;D<m.length;D++){var P=m[D];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(g,_(P.key),P)}}function C(g,m,D){return m&&w(g.prototype,m),Object.defineProperty(g,"prototype",{writable:!1}),g}function O(g,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(m&&m.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),m&&y(g,m)}function y(g,m){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(P,z){return P.__proto__=z,P},y(g,m)}function S(g){var m=h();return function(){var P=T(g),z;if(m){var I=T(this).constructor;z=Reflect.construct(P,arguments,I)}else z=P.apply(this,arguments);return k(this,z)}}function k(g,m){if(m&&(l(m)==="object"||typeof m=="function"))return m;if(m!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return u(g)}function u(g){if(g===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g}function h(){try{var g=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(h=function(){return!!g})()}function T(g){return T=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(D){return D.__proto__||Object.getPrototypeOf(D)},T(g)}function E(g,m,D){return m=_(m),m in g?Object.defineProperty(g,m,{value:D,enumerable:!0,configurable:!0,writable:!0}):g[m]=D,g}function _(g){var m=W(g,"string");return l(m)=="symbol"?m:String(m)}function W(g,m){if(l(g)!="object"||!g)return g;var D=g[Symbol.toPrimitive];if(D!==void 0){var P=D.call(g,m);if(l(P)!="object")return P;throw new TypeError("@@toPrimitive must return a primitive value.")}return(m==="string"?String:Number)(g)}var q=(0,s.canUseDOM)()&&no();r.default=function(g){O(D,g);var m=S(D);function D(P){var z;return p(this,D),z=m.call(this,P),E(u(z),"innerSliderRefHandler",function(I){return z.innerSlider=I}),E(u(z),"slickPrev",function(){return z.innerSlider.slickPrev()}),E(u(z),"slickNext",function(){return z.innerSlider.slickNext()}),E(u(z),"slickGoTo",function(I){var ne=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return z.innerSlider.slickGoTo(I,ne)}),E(u(z),"slickPause",function(){return z.innerSlider.pause("paused")}),E(u(z),"slickPlay",function(){return z.innerSlider.autoPlay("play")}),z.state={breakpoint:null},z._responsiveMediaHandlers=[],z}return C(D,[{key:"media",value:function(z,I){q.register(z,I),this._responsiveMediaHandlers.push({query:z,handler:I})}},{key:"componentDidMount",value:function(){var z=this;if(this.props.responsive){var I=this.props.responsive.map(function(Y){return Y.breakpoint});I.sort(function(Y,J){return Y-J}),I.forEach(function(Y,J){var ae;J===0?ae=(0,o.default)({minWidth:0,maxWidth:Y}):ae=(0,o.default)({minWidth:I[J-1]+1,maxWidth:Y}),(0,s.canUseDOM)()&&z.media(ae,function(){z.setState({breakpoint:Y})})});var ne=(0,o.default)({minWidth:I.slice(-1)[0]});(0,s.canUseDOM)()&&this.media(ne,function(){z.setState({breakpoint:null})})}}},{key:"componentWillUnmount",value:function(){this._responsiveMediaHandlers.forEach(function(z){q.unregister(z.query,z.handler)})}},{key:"render",value:function(){var z=this,I,ne;this.state.breakpoint?(ne=this.props.responsive.filter(function(xe){return xe.breakpoint===z.state.breakpoint}),I=ne[0].settings==="unslick"?"unslick":b(b(b({},t.default),this.props),ne[0].settings)):I=b(b({},t.default),this.props),I.centerMode&&(I.slidesToScroll>1,I.slidesToScroll=1),I.fade&&(I.slidesToShow>1,I.slidesToScroll>1,I.slidesToShow=1,I.slidesToScroll=1);var Y=e.default.Children.toArray(this.props.children);Y=Y.filter(function(xe){return typeof xe=="string"?!!xe.trim():!!xe}),I.variableWidth&&(I.rows>1||I.slidesPerRow>1)&&(console.warn("variableWidth is not supported in case of rows > 1 or slidesPerRow > 1"),I.variableWidth=!1);for(var J=[],ae=null,Z=0;Z<Y.length;Z+=I.rows*I.slidesPerRow){for(var He=[],se=Z;se<Z+I.rows*I.slidesPerRow;se+=I.slidesPerRow){for(var ht=[],ie=se;ie<se+I.slidesPerRow&&(I.variableWidth&&Y[ie].props.style&&(ae=Y[ie].props.style.width),!(ie>=Y.length));ie+=1)ht.push(e.default.cloneElement(Y[ie],{key:100*Z+10*se+ie,tabIndex:-1,style:{width:"".concat(100/I.slidesPerRow,"%"),display:"inline-block"}}));He.push(e.default.createElement("div",{key:10*Z+se},ht))}I.variableWidth?J.push(e.default.createElement("div",{key:Z,style:{width:ae}},He)):J.push(e.default.createElement("div",{key:Z},He))}if(I==="unslick"){var mr="regular slider "+(this.props.className||"");return e.default.createElement("div",{className:mr},Y)}else J.length<=I.slidesToShow&&!I.infinite&&(I.unslick=!0);return e.default.createElement(n.InnerSlider,c({style:this.props.style,ref:this.innerSliderRefHandler},(0,s.filterSettings)(I)),J)}}]),D}(e.default.Component)})(Wt);(function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var e=n(Wt);function n(o){return o&&o.__esModule?o:{default:o}}r.default=e.default})(At);const io=rn(At);function oo(r){let e=0,n;for(n=0;n<r.length;n++)e=r.charCodeAt(n)+((e<<5)-e);let o="#";for(n=0;n<3;n++){const t=e>>n*8&255;o+=("00"+t.toString(16)).substr(-2)}return o}function ao(r){return{sx:{bgcolor:oo(r)},children:r.split(" ").map(e=>e[0]).join("")}}const so={dots:!0,infinite:!0,speed:500,slidesToShow:2,slidesToScroll:1,adaptiveHeight:!0,arrows:!0,responsive:[{breakpoint:960,settings:{slidesToShow:1}}]},lo=[{quote:"The AI Analysis Assistant is a great feature for beginners. It helps understand the results easily.",name:"Muhammad Qaiser Shahbaz",title:"Professor at King Abdulaziz University, Saudi Arabia"},{quote:"DataStatPro has revolutionized my research workflow. It's intuitive, powerful, and the visualizations are stunning!",name:"Asif Hanif",title:"Professor at Sakarya University, Türkiye"},{quote:"As a student, DataStatPro's free access for education is a game-changer. It's easy to learn and has all the features I need.",name:"Abdullah Alzaidi",title:"Student at University of Qatar, Qatar"},{quote:"The cross-platform compatibility is fantastic. I can seamlessly switch between my desktop and tablet.",name:"Yasir Hassan",title:"Data Analyst at UoL, Pakistan"},{quote:"DataStatPro has revolutionized my research workflow. It's intuitive, powerful, and the visualizations are stunning!",name:"Rehan Ahmad Khan",title:"Professor at University of the Punjab, Pakistan"},{quote:"As a student, DataStatPro's free access for education is a game-changer. It's easy to learn and has all the features I need.",name:"Uzair Nadeem",title:"Student at PISJ"},{quote:"The cross-platform compatibility is fantastic. I can seamlessly switch between my desktop and tablet.",name:"Jasleen Kaur",title:"Data Analyst"},{quote:"The AI Analysis Assistant is a great feature for beginners. It helps understand the results easily.",name:"Muhammad Qaiser Shahbaz",title:"Professor at King Abdulaziz University, Saudi Arabia"},{quote:"With its advanced import/export features, DataStatPro made handling my large survey dataset both simple and secure.",name:"Dr. Anjali Mehra",title:"Associate Professor at Delhi University"},{quote:"I love the visualizations—presentation-ready graphs are just a click away. My publications have never looked better.",name:"Elena Petrova",title:"Biostatistician, Moscow State University"},{quote:"Switching devices is seamless. Whether I start on my laptop or mobile, my data and analyses follow me everywhere.",name:"Liam O'Connor",title:"Epidemiology Graduate Student, University College Dublin"},{quote:"The Risk Calculator and EpiCalc tools are essential for our lab planning. Highly recommended for medical researchers.",name:"Dr. Sarah Ibrahim",title:"Research Coordinator, Cairo Medical Institute"}],co=[{icon:i.jsx(Jr,{sx:{fontSize:48,color:"#4A90E2"}}),title:"Cross-Platform",description:"Works seamlessly across all devices and operating systems. Access your analysis anywhere, anytime.",image:"cross-platform.png"},{icon:i.jsx(Zr,{sx:{fontSize:48,color:"#7B68EE"}}),title:"Data Management",description:"Powerful data import, export, and management tools. Handle large datasets with ease.",image:"data-management.png"},{icon:i.jsx(en,{sx:{fontSize:48,color:"#50C878"}}),title:"Advanced Analysis",description:"Advanced statistical analysis tools for complex data sets. Get insights quickly.",image:"advanced-analysis.png"},{icon:i.jsx(Rt,{sx:{fontSize:48,color:"#FF6B6B"}}),title:"Beautiful Charts",description:"Create stunning visualizations with our advanced charting engine. Export in multiple formats.",image:"beautiful-charts.png"}],bo=()=>{const r=br(),e=pt(r.breakpoints.down("md")),n=pt(r.breakpoints.down("sm")),o=on(),[t,s]=Q.useState(!1),[a,l]=Q.useState(!1),[c,f]=Q.useState(!1),[b,p]=Q.useState(!1),[w,C]=Q.useState(!1),O=()=>{window.pageYOffset>300?p(!0):p(!1)},y=()=>{window.scrollTo({top:0,behavior:"smooth"})};Q.useEffect(()=>(window.addEventListener("scroll",O),()=>{window.removeEventListener("scroll",O)}),[]),Q.useEffect(()=>{const u={threshold:.1,rootMargin:"0px 0px -50px 0px"},h=new IntersectionObserver(W=>{W.forEach(q=>{if(q.isIntersecting){const g=q.target;g.id==="hero-section"&&s(!0),g.id==="features-section"&&l(!0),g.id==="cta-section"&&f(!0)}})},u),T=document.getElementById("hero-section"),E=document.getElementById("features-section"),_=document.getElementById("cta-section");return T&&h.observe(T),E&&h.observe(E),_&&h.observe(_),setTimeout(()=>s(!0),300),()=>h.disconnect()},[]);const S=[{label:"Statistical Toolkit",onClick:()=>{var u;(u=document.getElementById("statistical-toolkit-section"))==null||u.scrollIntoView({behavior:"smooth"}),C(!1)}},{label:"Features",onClick:()=>{var u;(u=document.getElementById("features-section"))==null||u.scrollIntoView({behavior:"smooth"}),C(!1)}},{label:"Pricing",onClick:()=>{o("/pricing"),C(!1)}},{label:"Help",onClick:()=>{o("/app#whichtest"),C(!1)}}],k=()=>{C(!w)};return i.jsxs(i.Fragment,{children:[i.jsxs(an,{children:[i.jsx("title",{children:"DataStatPro - Professional Statistical Analysis Made Simple"}),i.jsx("meta",{name:"description",content:"Transform your data with professional statistical analysis tools. DataStatPro is an alternative to SPSS with beautiful visualizations, cross-platform support, and powerful data management."})]}),i.jsxs(d,{sx:{minHeight:"100vh",bgcolor:"#FAFBFC",overflowX:"hidden"},children:[i.jsx(yr,{position:"sticky",sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",boxShadow:"0 4px 20px rgba(0,0,0,0.1)"},children:i.jsx(Sr,{sx:{px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5},minHeight:{xs:64,sm:70}},children:i.jsx(ee,{maxWidth:"lg",sx:{px:{xs:0}},children:i.jsxs(d,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%"},children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:{xs:1,sm:2},cursor:"pointer",flex:"0 0 auto"},onClick:()=>o("/app"),"aria-label":"Navigate to DataStatPro App",children:[i.jsx(Ne,{src:"/logo.png",alt:"DataStatPro",sx:{width:{xs:32,sm:36,md:40},height:{xs:32,sm:36,md:40}}}),i.jsx(v,{variant:"h6",component:"div",sx:{fontWeight:"bold",color:"white",fontSize:{xs:"1rem",sm:"1.1rem",md:"1.25rem"},display:{xs:n?"none":"block",sm:"block"}},children:"DataStatPro"})]}),!e&&i.jsx(F,{direction:"row",spacing:3,sx:{flex:"1 1 auto",justifyContent:"center"},children:S.map((u,h)=>i.jsx(te,{component:"button",onClick:u.onClick,sx:{color:"white",textDecoration:"none",fontWeight:500,fontSize:"0.95rem",transition:"opacity 0.2s","&:hover":{opacity:.8,textDecoration:"underline"}},children:u.label},h))}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1,md:2},flex:"0 0 auto"},children:[e&&i.jsx(xt,{edge:"start",color:"inherit","aria-label":"menu",onClick:k,sx:{ml:1,p:{xs:.5,sm:1}},children:i.jsx(wr,{})}),i.jsx(N,{variant:n?"text":"outlined",onClick:()=>o("/app#auth/login"),startIcon:n?i.jsx(jr,{}):void 0,sx:{color:"white",borderColor:n?"transparent":"white",fontWeight:"bold",px:{xs:n?1:2,sm:2.5,md:3},py:{xs:.75,sm:1},borderRadius:2,textTransform:"none",fontSize:{xs:"0.85rem",sm:"0.9rem",md:"1rem"},minWidth:n?"auto":{xs:100,sm:120},whiteSpace:"nowrap","&:hover":{bgcolor:A("#ffffff",.1),borderColor:n?"transparent":A("#ffffff",.8)},transition:"all 0.2s ease-in-out"},children:n?"":"Login / Sign Up"}),i.jsx(N,{variant:"contained",onClick:()=>o("/app"),endIcon:!n&&i.jsx(B,{}),sx:{bgcolor:"white",color:"#667eea",fontWeight:"bold",px:{xs:2,sm:2.5,md:3},py:{xs:.75,sm:1},borderRadius:2,textTransform:"none",fontSize:{xs:"0.85rem",sm:"0.9rem",md:"1rem"},minWidth:{xs:80,sm:100},whiteSpace:"nowrap","&:hover":{bgcolor:A("#ffffff",.9),transform:"translateY(-1px)",boxShadow:"0 6px 20px rgba(0,0,0,0.15)"},transition:"all 0.2s ease-in-out"},children:n?"Open":"Open App"})]})]})})})}),i.jsx(Or,{anchor:"right",open:w,onClose:k,sx:{"& .MuiDrawer-paper":{width:{xs:280,sm:320},background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"}},children:i.jsxs(d,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:2,borderBottom:"1px solid rgba(255,255,255,0.1)"},children:[i.jsx(v,{variant:"h6",sx:{color:"white",fontWeight:"bold"},children:"Menu"}),i.jsx(xt,{onClick:k,sx:{color:"white"},children:i.jsx(kr,{})})]}),i.jsx(Pr,{sx:{flex:1,py:2},children:S.map((u,h)=>i.jsx(Cr,{disablePadding:!0,children:i.jsx(_r,{onClick:u.onClick,sx:{py:2,px:3,"&:hover":{bgcolor:"rgba(255,255,255,0.1)"}},children:i.jsx(Tr,{primary:u.label,primaryTypographyProps:{sx:{color:"white",fontSize:"1.1rem",fontWeight:500}}})})},h))}),i.jsx(Er,{sx:{borderColor:"rgba(255,255,255,0.2)"}}),i.jsx(d,{sx:{p:3},children:i.jsxs(F,{spacing:2,children:[i.jsx(N,{fullWidth:!0,variant:"outlined",onClick:()=>{o("/app#auth/login"),C(!1)},startIcon:i.jsx(zr,{}),sx:{color:"white",borderColor:"white",py:1.5,fontSize:"1rem",fontWeight:"bold","&:hover":{bgcolor:"rgba(255,255,255,0.1)",borderColor:"white"}},children:"Login / Sign Up"}),i.jsx(N,{fullWidth:!0,variant:"contained",onClick:()=>{o("/app"),C(!1)},endIcon:i.jsx(B,{}),sx:{bgcolor:"white",color:"#667eea",py:1.5,fontSize:"1rem",fontWeight:"bold","&:hover":{bgcolor:A("#ffffff",.9)}},children:"Open App"})]})})]})}),i.jsxs(d,{id:"hero-section",sx:{background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",py:{xs:8,md:12},position:"relative",minHeight:"80vh",display:"flex",alignItems:"center"},children:[i.jsx(d,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',opacity:.5}}),i.jsx(ee,{maxWidth:"lg",children:i.jsxs(R,{container:!0,spacing:6,alignItems:"center",children:[i.jsx(R,{item:!0,xs:12,md:6,children:i.jsx(Be,{in:t,timeout:800,children:i.jsxs(F,{spacing:4,children:[i.jsx(ve,{direction:"up",in:t,timeout:1e3,children:i.jsx($e,{label:"🚀 Professional Statistical Analysis",sx:{bgcolor:A("#667eea",.1),color:"#667eea",fontWeight:"bold",alignSelf:"flex-start",px:2,py:1}})}),i.jsx(ve,{direction:"up",in:t,timeout:1200,children:i.jsxs(v,{variant:e?"h3":"h2",component:"h1",sx:{fontWeight:800,color:"#2D3748",lineHeight:1.2,fontSize:{xs:"2.5rem",md:"3.5rem",lg:"4rem"}},children:["DataStatPro",i.jsx(d,{component:"span",sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",backgroundClip:"text",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",display:"block",fontSize:"0.6em"},children:"Statistical Analysis Simplified"})]})}),i.jsx(ve,{direction:"up",in:t,timeout:1400,children:i.jsx(v,{variant:"h5",color:"text.secondary",sx:{maxWidth:500,lineHeight:1.6,fontSize:{xs:"1.1rem",md:"1.3rem"}},children:"An affordable alternative to expensive statistical softwares — absolutely free for teaching and learning purposes. Gain insights quickly with built-in interpretations, beautiful visualizations, and seamless cross-platform support."})}),i.jsx(ve,{direction:"up",in:t,timeout:1600,children:i.jsxs(F,{direction:e?"column":"row",spacing:2,sx:{width:"100%"},children:[i.jsx(N,{variant:"contained",size:"large",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),fullWidth:e,sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",py:{xs:1.5,sm:2},px:{xs:3,sm:4},fontSize:{xs:"1rem",sm:"1.1rem"},fontWeight:"bold",borderRadius:2,textTransform:"none",boxShadow:"0 8px 25px rgba(102, 126, 234, 0.3)","&:hover":{transform:"translateY(-2px)",boxShadow:"0 12px 35px rgba(102, 126, 234, 0.4)"},transition:"all 0.3s ease-in-out"},children:"Start Analyzing Now"}),i.jsx(N,{variant:"outlined",size:"large",onClick:()=>o("/app#assistant"),fullWidth:e,sx:{py:{xs:1.5,sm:2},px:{xs:3,sm:4},fontSize:{xs:"1rem",sm:"1.1rem"},fontWeight:"bold",borderRadius:2,textTransform:"none",borderColor:"#667eea",color:"#667eea","&:hover":{bgcolor:A("#667eea",.05),borderColor:"#667eea",transform:"translateY(-1px)"},transition:"all 0.2s ease-in-out"},children:"AI-Powered Analysis Assistant"})]})})]})})}),i.jsx(R,{item:!0,xs:12,md:6,children:i.jsx(Ir,{in:t,timeout:1800,children:i.jsx(d,{sx:{position:"relative",display:"flex",justifyContent:"center",alignItems:"center"},children:i.jsx(d,{sx:{position:"relative",width:{xs:"100%",md:"500px"},maxWidth:"500px","&::before":{content:'""',position:"absolute",top:-30,left:-30,right:-30,bottom:-30,background:"linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",borderRadius:4,zIndex:-1,filter:"blur(20px)"}},children:i.jsxs(Fe,{elevation:20,sx:{p:1,borderRadius:3,bgcolor:"#1a1a1a",position:"relative",overflow:"hidden",boxShadow:"0 25px 50px rgba(0,0,0,0.25)"},children:[i.jsx(d,{sx:{bgcolor:"#f8f9fa",borderRadius:2,p:2,minHeight:{xs:200,md:300},background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",color:"white",position:"relative"},children:i.jsxs(d,{sx:{textAlign:"center",width:"100%"},children:[i.jsxs(v,{variant:"h3",component:"p",sx:{mb:2,fontWeight:"bold",color:"white",lineHeight:1.3},children:["DataStatPro",i.jsx("br",{}),i.jsx(v,{variant:"h4",component:"span",sx:{fontWeight:"normal",opacity:.9},children:"Statistical Analysis on the Go"})]}),i.jsx(R,{container:!0,spacing:1,sx:{mb:2},children:[{icon:i.jsx(Dr,{}),label:"Desktop"},{icon:i.jsx(Mr,{}),label:"Laptop"},{icon:i.jsx(Lr,{}),label:"Tablet"},{icon:i.jsx(Rr,{}),label:"Mobile"}].map((u,h)=>i.jsx(R,{item:!0,xs:6,children:i.jsxs(Fe,{sx:{p:1,bgcolor:r.palette.background.paper,borderRadius:1,transition:"all 0.2s ease-in-out","&:hover":{bgcolor:r.palette.action.hover,transform:"translateY(-2px)"},textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",gap:1},children:[nn.cloneElement(u.icon,{sx:{color:r.palette.text.primary}})," ",i.jsxs(v,{variant:"caption",color:"text.primary",children:[" ",u.label]})]})},h))}),i.jsxs(d,{sx:{display:"flex",justifyContent:"center",gap:1},children:[i.jsx(vt,{sx:{fontSize:20}}),i.jsx(Rt,{sx:{fontSize:20}}),i.jsx(gt,{sx:{fontSize:20}})]})]})}),i.jsx(d,{sx:{height:20,bgcolor:"#1a1a1a",borderRadius:"0 0 8px 8px",position:"relative","&::after":{content:'""',position:"absolute",bottom:-10,left:"50%",transform:"translateX(-50%)",width:60,height:4,bgcolor:"#333",borderRadius:2}}})]})})})})})]})})]}),i.jsx(d,{id:"features-section",sx:{py:{xs:8,md:12},bgcolor:"background.default"},children:i.jsxs(ee,{maxWidth:"lg",children:[i.jsx(Be,{in:a,timeout:800,children:i.jsxs(d,{textAlign:"center",mb:8,children:[i.jsx(v,{variant:"h3",component:"h2",gutterBottom:!0,sx:{fontWeight:800,color:"text.primary",fontSize:{xs:"2.5rem",md:"3rem"}},children:"Streamlined Workflow, Publication-Ready Results"}),i.jsx(v,{variant:"h6",color:"text.secondary",sx:{maxWidth:700,mx:"auto",lineHeight:1.6},children:"From data import to publication-ready visualizations, DataStatPro provides everything you need for professional statistical analysis."})]})}),i.jsx(R,{container:!0,spacing:6,children:co.map((u,h)=>i.jsx(R,{item:!0,xs:12,md:6,children:i.jsx(ve,{direction:h%2===0?"right":"left",in:a,timeout:1e3+h*200,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,border:"none",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:4},children:i.jsxs(R,{container:!0,spacing:3,alignItems:"center",children:[i.jsx(R,{item:!0,xs:12,sm:6,children:i.jsxs(F,{spacing:2,children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2},children:[u.icon,i.jsx(v,{variant:"h5",fontWeight:"bold",color:"text.primary",children:u.title})]}),i.jsx(v,{variant:"body1",color:"text.secondary",sx:{lineHeight:1.6},children:u.description}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#667eea",fontWeight:"bold",textTransform:"none","&:hover":{bgcolor:A("#667eea",.05),textDecoration:"underline"}},children:"Learn More"})]})}),i.jsx(R,{item:!0,xs:12,sm:6,children:i.jsx(Fe,{sx:{p:2,borderRadius:2,bgcolor:r.palette.mode==="dark"?"grey.800":"#f8f9fa",minHeight:120,display:"flex",alignItems:"center",justifyContent:"center",border:`2px dashed ${r.palette.mode==="dark"?"grey.600":"#e2e8f0"}`,overflow:"hidden"},children:i.jsx("img",{src:u.image,alt:`${u.title} feature illustration`,style:{width:"100%",height:"auto",objectFit:"cover",borderRadius:"8px"}})})})]})})})})},h))})]})}),i.jsx(d,{id:"statistical-toolkit-section",sx:{py:{xs:8,md:12},bgcolor:"#f8fafc"},children:i.jsxs(ee,{maxWidth:"lg",children:[i.jsxs(d,{textAlign:"center",mb:8,children:[i.jsx(v,{variant:"h3",component:"h2",gutterBottom:!0,sx:{fontWeight:800,color:"#2D3748",fontSize:{xs:"2.5rem",md:"3rem"}},children:"Comprehensive Statistical Toolkit"}),i.jsx(v,{variant:"h6",color:"text.secondary",sx:{maxWidth:700,mx:"auto",lineHeight:1.6},children:"Everything you need for comprehensive data analysis in one place"})]}),i.jsxs(R,{container:!0,spacing:4,children:[i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsx(d,{sx:{mb:2,color:"#667eea"},children:i.jsx(gt,{sx:{fontSize:48}})}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Inferential Statistics"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Comprehensive suite of statistical tests"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["t-Tests","ANOVA","Non-parametric Tests","Repeated Measures","Assuptions Checks"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#667eea"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#667eea",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#667eea",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsx(d,{sx:{mb:2,color:"#FF6B6B"},children:i.jsx(vt,{sx:{fontSize:48}})}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Data Visualization"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Beautiful, interactive charts and plots"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["Bar & Pie Charts","Histograms","Box Plots","Scatter Plots","Rain Cloud Plots"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#FF6B6B"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#FF6B6B",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#FF6B6B",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsx(d,{sx:{mb:2,color:"#7B68EE"},children:i.jsx(Ar,{sx:{fontSize:48}})}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Correlation Analysis"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Explore relationships between variables"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["Correlation Matrix","Linear Regression","Logistic Regression","Pearson","Spearman"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#7B68EE"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#7B68EE",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#7B68EE",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsx(d,{sx:{mb:2,color:"#50C878"},children:i.jsx(Wr,{sx:{fontSize:48}})}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Sample Size / Power"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Tools for research planning and analysis"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["One Group Scenarios","Two Group Scenarios","Paired Sample Scenarios","More than two groups"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#50C878"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#50C878",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#50C878",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsx(d,{sx:{mb:2,color:"#4A90E2"},children:i.jsx(Hr,{sx:{fontSize:48}})}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Epidemiological Studies"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Analyze health-related states or events"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["Cross Sectional","Case Control","Cohort","Matched Case Control"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#4A90E2"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#4A90E2",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#4A90E2",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsxs(d,{sx:{mb:2,color:"#FF9800"},children:[" ",i.jsx(Nr,{sx:{fontSize:48}})]}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Survival Analysis"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Analyze time-to-event data"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["KM Survival Estimates","Kaplan Survival Curves","Log-Rank Test","Cox-Regression"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#FF9800"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#FF9800",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#FF9800",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsxs(d,{sx:{mb:2,color:"#9C27B0"},children:[" ",i.jsx(Br,{sx:{fontSize:48}})]}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Reliability Analysis"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Assess the consistency of measurements"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["Cronbach's Alpha","Cohen's Kappa/ Fleiss Kappa","Kendall's Tau/ Kendall's W","Intraclass Correlation Coefficient"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#9C27B0"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#9C27B0",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#9C27B0",.05),textDecoration:"underline"}},children:"Learn More"})]})})})}),i.jsx(R,{item:!0,xs:12,md:6,lg:3,children:i.jsx(U,{sx:{height:"100%",borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0 20px 40px rgba(0,0,0,0.15)"}},children:i.jsx(K,{sx:{p:3},children:i.jsxs(d,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[i.jsxs(d,{sx:{mb:2,color:"#00BCD4"},children:[" ",i.jsx($r,{sx:{fontSize:48}})]}),i.jsx(v,{variant:"h5",component:"h3",fontWeight:"bold",gutterBottom:!0,children:"Pivot Analysis"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Summarize and analyze data with pivot tables"}),i.jsx(d,{sx:{mt:"auto"},children:i.jsx(F,{spacing:1,children:["Pivot Tables","Pivot Charts","Google Sheet Integration","Hierarchical Grouping"].map((u,h)=>i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(B,{sx:{fontSize:16,color:"#00BCD4"}}),i.jsx(v,{variant:"body2",children:u})]},h))})}),i.jsx(N,{variant:"text",size:"small",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{alignSelf:"flex-start",color:"#00BCD4",fontWeight:"bold",textTransform:"none",mt:2,"&:hover":{bgcolor:A("#00BCD4",.05),textDecoration:"underline"}},children:"Learn More"})]})})})})]}),i.jsx(d,{sx:{textAlign:"center",mt:6},children:i.jsx(N,{variant:"outlined",size:"large",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{borderColor:"#667eea",color:"#667eea",py:1.5,px:4,fontSize:"1.1rem",fontWeight:"bold",borderRadius:2,textTransform:"none","&:hover":{bgcolor:A("#667eea",.05),borderColor:"#667eea",transform:"translateY(-1px)"},transition:"all 0.2s ease-in-out"},children:"Explore All Features"})})]})}),i.jsx(d,{sx:{py:{xs:8,md:12},bgcolor:"#e0e0e0"},children:i.jsxs(ee,{maxWidth:"lg",children:[i.jsxs(d,{textAlign:"center",mb:8,children:[i.jsx(v,{variant:"h3",component:"h2",gutterBottom:!0,sx:{fontWeight:800,color:"#2D3748",fontSize:{xs:"2.5rem",md:"3rem"}},children:"Trusted by Researchers and Professionals"}),i.jsx(v,{variant:"h6",color:"text.secondary",sx:{maxWidth:700,mx:"auto",lineHeight:1.6},children:"Hear from our satisfied users"})]}),i.jsx(d,{children:i.jsx(io,{...so,children:lo.map(({quote:u,name:h,title:T},E)=>i.jsx(d,{sx:{px:2},children:i.jsx(U,{sx:{borderRadius:3,boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},children:i.jsxs(K,{sx:{p:4},children:[i.jsxs(v,{variant:"body1",color:"text.secondary",sx:{fontStyle:"italic",mb:2},children:['"',u,'"']}),i.jsxs(F,{direction:"row",alignItems:"center",spacing:2,children:[i.jsx(Ne,{...ao(h)}),i.jsxs(d,{children:[i.jsx(v,{variant:"subtitle1",fontWeight:"bold",children:h}),i.jsx(v,{variant:"body2",color:"text.secondary",children:T})]})]})]})})},E))})})]})}),i.jsx(d,{sx:{py:{xs:8,md:12},bgcolor:"background.default"},children:i.jsxs(ee,{maxWidth:"lg",children:[i.jsxs(d,{textAlign:"center",mb:6,children:[i.jsx(v,{variant:"h3",component:"h2",fontWeight:"bold",sx:{mb:2,fontSize:{xs:"2rem",md:"2.5rem"},background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",backgroundClip:"text",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},children:"Choose Your Plan"}),i.jsx(v,{variant:"h6",color:"text.secondary",sx:{mb:4,maxWidth:"600px",mx:"auto"},children:"Start free and upgrade as your needs grow"})]}),i.jsxs(R,{container:!0,spacing:4,justifyContent:"center",children:[i.jsx(R,{item:!0,xs:12,sm:6,md:3,children:i.jsxs(U,{sx:{height:"100%",display:"flex",flexDirection:"column",borderRadius:3,transition:"all 0.3s ease","&:hover":{transform:"translateY(-8px)",boxShadow:"0 12px 40px rgba(76, 175, 80, 0.15)"}},children:[i.jsxs(K,{sx:{flexGrow:1,p:3,textAlign:"center"},children:[i.jsx(d,{sx:{width:48,height:48,borderRadius:"50%",bgcolor:A("#4caf50",.1),color:"#4caf50",display:"flex",alignItems:"center",justifyContent:"center",mx:"auto",mb:2},children:i.jsx(Fr,{sx:{fontSize:24}})}),i.jsx(v,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:"Guest Access"}),i.jsx(v,{variant:"h4",fontWeight:"bold",color:"#4caf50",sx:{mb:1},children:"Free"}),i.jsx(v,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Perfect for exploring and learning"}),i.jsxs(F,{spacing:1,sx:{mb:3},children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#4caf50"}}),i.jsx(v,{variant:"body2",children:"Full app exploration"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#4caf50"}}),i.jsx(v,{variant:"body2",children:"Sample datasets"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(qr,{sx:{fontSize:16,color:"text.disabled"}}),i.jsx(v,{variant:"body2",color:"text.disabled",children:"Personal data import"})]})]})]}),i.jsx(be,{sx:{p:3,pt:0},children:i.jsx(N,{variant:"outlined",fullWidth:!0,onClick:()=>o("/app"),sx:{borderColor:"#4caf50",color:"#4caf50","&:hover":{bgcolor:A("#4caf50",.1)}},children:"Start Exploring"})})]})}),i.jsx(R,{item:!0,xs:12,sm:6,md:3,children:i.jsxs(U,{sx:{height:"100%",display:"flex",flexDirection:"column",borderRadius:3,border:"2px solid #2196f3",position:"relative",transition:"all 0.3s ease","&:hover":{transform:"translateY(-8px)",boxShadow:"0 12px 40px rgba(33, 150, 243, 0.15)"}},children:[i.jsx($e,{label:"Most Popular",sx:{position:"absolute",top:16,right:16,bgcolor:"#2196f3",color:"white",fontWeight:"bold",fontSize:"0.75rem"}}),i.jsxs(K,{sx:{flexGrow:1,p:3,textAlign:"center"},children:[i.jsx(d,{sx:{width:48,height:48,borderRadius:"50%",bgcolor:A("#2196f3",.1),color:"#2196f3",display:"flex",alignItems:"center",justifyContent:"center",mx:"auto",mb:2},children:i.jsx(Yr,{sx:{fontSize:24}})}),i.jsx(v,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:"Standard Account"}),i.jsx(v,{variant:"h4",fontWeight:"bold",color:"#2196f3",sx:{mb:1},children:"Free"}),i.jsx(v,{variant:"caption",color:"text.secondary",sx:{mb:3,display:"block"},children:"Currently free during development"}),i.jsxs(F,{spacing:1,sx:{mb:3},children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#2196f3"}}),i.jsx(v,{variant:"body2",children:"All Guest features"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#2196f3"}}),i.jsx(v,{variant:"body2",children:"Personal data import"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#2196f3"}}),i.jsx(v,{variant:"body2",children:"Local data storage"})]})]})]}),i.jsx(be,{sx:{p:3,pt:0},children:i.jsx(N,{variant:"contained",fullWidth:!0,onClick:()=>o("/app#/auth/login"),sx:{bgcolor:"#2196f3","&:hover":{bgcolor:A("#2196f3",.8)}},children:"Create Account"})})]})}),i.jsx(R,{item:!0,xs:12,sm:6,md:3,children:i.jsxs(U,{sx:{height:"100%",display:"flex",flexDirection:"column",borderRadius:3,transition:"all 0.3s ease","&:hover":{transform:"translateY(-8px)",boxShadow:"0 12px 40px rgba(255, 152, 0, 0.15)"}},children:[i.jsxs(K,{sx:{flexGrow:1,p:3,textAlign:"center"},children:[i.jsx(d,{sx:{width:48,height:48,borderRadius:"50%",bgcolor:A("#ff9800",.1),color:"#ff9800",display:"flex",alignItems:"center",justifyContent:"center",mx:"auto",mb:2},children:i.jsx(Gr,{sx:{fontSize:24}})}),i.jsx(v,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:"Pro Account"}),i.jsx(v,{variant:"h4",fontWeight:"bold",color:"#ff9800",sx:{mb:1},children:"$10"}),i.jsx(v,{variant:"caption",color:"text.secondary",sx:{mb:3,display:"block"},children:"per month"}),i.jsxs(F,{spacing:1,sx:{mb:3},children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#ff9800"}}),i.jsx(v,{variant:"body2",children:"All Standard features"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#ff9800"}}),i.jsx(v,{variant:"body2",children:"Pro analysis features"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#ff9800"}}),i.jsx(v,{variant:"body2",children:"Cloud storage"})]})]})]}),i.jsx(be,{sx:{p:3,pt:0},children:i.jsx(N,{variant:"outlined",fullWidth:!0,onClick:()=>window.open("mailto:<EMAIL>?subject=Early Access Request - DataStatPro Pro Account&body=Hi DataStatPro Team,%0D%0A%0D%0AI would like to request early access to Pro account features during the development phase.%0D%0A%0D%0AMy use case:%0D%0A[Please describe how you plan to use DataStatPro]%0D%0A%0D%0ASpecific features I'm interested in:%0D%0A- Advanced statistical methods%0D%0A- Cloud data storage%0D%0A- Multi-device synchronization%0D%0A- [Add other features you're interested in]%0D%0A%0D%0AThank you for considering my request.%0D%0A%0D%0ABest regards"),sx:{borderColor:"#ff9800",color:"#ff9800","&:hover":{bgcolor:A("#ff9800",.1)}},children:"Request Early Access"})})]})}),i.jsx(R,{item:!0,xs:12,sm:6,md:3,children:i.jsxs(U,{sx:{height:"100%",display:"flex",flexDirection:"column",borderRadius:3,transition:"all 0.3s ease","&:hover":{transform:"translateY(-8px)",boxShadow:"0 12px 40px rgba(156, 39, 176, 0.15)"}},children:[i.jsx($e,{label:"Advanced Analysis Free",sx:{position:"absolute",top:16,right:16,bgcolor:"#9c27b0",color:"white",fontWeight:"bold",fontSize:"0.75rem"}}),i.jsxs(K,{sx:{flexGrow:1,p:3,textAlign:"center"},children:[i.jsx(d,{sx:{width:48,height:48,borderRadius:"50%",bgcolor:A("#9c27b0",.1),color:"#9c27b0",display:"flex",alignItems:"center",justifyContent:"center",mx:"auto",mb:2},children:i.jsx(Ur,{sx:{fontSize:24}})}),i.jsx(v,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:"Educational Account"}),i.jsx(v,{variant:"h4",fontWeight:"bold",color:"#9c27b0",sx:{mb:1},children:"Free"}),i.jsx(v,{variant:"caption",color:"text.secondary",sx:{mb:3,display:"block"},children:"for .edu emails"}),i.jsxs(F,{spacing:1,sx:{mb:3},children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#9c27b0"}}),i.jsx(v,{variant:"body2",children:"All Standard features"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#9c27b0"}}),i.jsx(v,{variant:"body2",children:"Advanced Analysis FREE"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#9c27b0"}}),i.jsx(v,{variant:"body2",children:"Advanced statistical tests"})]}),i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(V,{sx:{fontSize:16,color:"#9c27b0"}}),i.jsx(v,{variant:"body2",children:"Interactive visualizations"})]})]})]}),i.jsx(be,{sx:{p:3,pt:0},children:i.jsx(N,{variant:"outlined",fullWidth:!0,onClick:()=>o("/app#/auth/login"),sx:{borderColor:"#9c27b0",color:"#9c27b0","&:hover":{bgcolor:A("#9c27b0",.1)}},children:"Create Educational Account"})})]})})]}),i.jsx(d,{textAlign:"center",sx:{mt:6},children:i.jsx(N,{variant:"text",size:"large",onClick:()=>o("/pricing"),endIcon:i.jsx(B,{}),sx:{fontSize:"1.1rem",fontWeight:"bold",color:"#667eea","&:hover":{bgcolor:A("#667eea",.1)}},children:"View Detailed Pricing & Features"})})]})}),i.jsxs(d,{id:"cta-section",sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",py:{xs:8,md:12},position:"relative"},children:[i.jsx(d,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',opacity:.3}}),i.jsx(ee,{maxWidth:"md",sx:{textAlign:"center",position:"relative"},children:i.jsx(Be,{in:c,timeout:800,children:i.jsxs(F,{spacing:4,sx:{textAlign:"center"},children:[i.jsx(v,{variant:"h3",component:"h2",fontWeight:"bold",sx:{fontSize:{xs:"2.5rem",md:"3rem"}},children:"Ready to Transform Your Data?"}),i.jsx(v,{variant:"h6",sx:{opacity:.9,mx:"auto",lineHeight:1.6},children:"Join thousands of researchers, students, and professionals who trust DataStatPro for their statistical analysis needs. Start your journey today."}),i.jsx(d,{children:i.jsx(N,{variant:"contained",size:"large",onClick:()=>o("/app"),endIcon:i.jsx(B,{}),sx:{bgcolor:"white",color:"#667eea",py:2,px:6,fontSize:"1.2rem",fontWeight:"bold",borderRadius:2,textTransform:"none",boxShadow:"0 8px 25px rgba(0,0,0,0.2)","&:hover":{bgcolor:A("#ffffff",.95),transform:"translateY(-2px)",boxShadow:"0 12px 35px rgba(0,0,0,0.3)"},transition:"all 0.3s ease-in-out"},children:"Start Your Free Analysis"})})]})})})]}),b&&i.jsx(N,{variant:"contained",onClick:y,sx:{position:"fixed",bottom:20,right:20,bgcolor:"#667eea",color:"white",borderRadius:"50%",width:56,height:56,minWidth:0,boxShadow:"0 4px 10px rgba(0,0,0,0.2)","&:hover":{bgcolor:"#764ba2",boxShadow:"0 8px 15px rgba(0,0,0,0.3)"},zIndex:1e3},"aria-label":"scroll to top",children:"↑"}),i.jsx(d,{component:"footer",sx:{py:6,px:3,bgcolor:"#2D3748",color:"white"},children:i.jsx(ee,{maxWidth:"lg",children:i.jsxs(R,{container:!0,spacing:4,children:[i.jsxs(R,{item:!0,xs:12,md:6,children:[i.jsxs(d,{sx:{display:"flex",alignItems:"center",gap:2,mb:3},children:[i.jsx(Ne,{src:"/logo.png",alt:"DataStatPro",sx:{width:40,height:40}}),i.jsx(v,{variant:"h6",fontWeight:"bold",children:"DataStatPro"})]}),i.jsx(v,{variant:"body1",sx:{opacity:.8,lineHeight:1.6},children:"Making statistical analysis accessible to everyone. Professional tools, beautiful visualizations, completely free for educational purposes."})]}),i.jsx(R,{item:!0,xs:12,md:6,children:i.jsxs(F,{spacing:2,alignItems:e?"flex-start":"flex-end",children:[i.jsxs(F,{direction:"row",spacing:3,flexWrap:"wrap",children:[S.map((u,h)=>i.jsx(te,{component:"button",onClick:u.onClick,sx:{color:"white",textDecoration:"none",opacity:.8,"&:hover":{opacity:1,textDecoration:"underline"}},children:u.label},h)),i.jsx(te,{href:"/privacy",target:"_blank",rel:"noopener noreferrer",sx:{color:"white",textDecoration:"none",opacity:.8,"&:hover":{opacity:1,textDecoration:"underline"}},children:"Privacy Policy"}),i.jsx(te,{href:"/terms",target:"_blank",rel:"noopener noreferrer",sx:{color:"white",textDecoration:"none",opacity:.8,"&:hover":{opacity:1,textDecoration:"underline"}},children:"Terms of Service"})]}),i.jsxs(F,{direction:"row",spacing:2,sx:{color:"white",opacity:.8},children:[i.jsx(te,{href:"mailto:<EMAIL>",color:"inherit",sx:{"&:hover":{opacity:1}},children:i.jsx(Kr,{})}),i.jsx(te,{href:"https://www.facebook.com/nadeem.shafique.pk",target:"_blank",rel:"noopener noreferrer",color:"inherit",sx:{"&:hover":{opacity:1}},children:i.jsx(Xr,{})}),i.jsx(te,{href:"https://twitter.com/nadeemshafique",target:"_blank",rel:"noopener noreferrer",color:"inherit",sx:{"&:hover":{opacity:1}},children:i.jsx(Qr,{})}),i.jsx(te,{href:"https://www.linkedin.com/company/yourcompany",target:"_blank",rel:"noopener noreferrer",color:"inherit",sx:{"&:hover":{opacity:1}},children:i.jsx(Vr,{})})]}),i.jsx(v,{variant:"body2",sx:{opacity:.6},children:"© 2024 DataStatPro. All rights reserved."})]})})]})})})]})]})};export{bo as default};
