import{u as Te,j as e,B as s,e as l,bW as Ie,bX as Ve,aF as Ae,aD as We,bY as Pe,G as J,R as W,a6 as _e,aE as P,a7 as de,c7 as Ge,aW as Me,F as he,ai as k,b9 as B,ba as L,bb as C,ah as _,f as Re,ae as ue,ad as me,I as xe,b7 as Ne,bH as Oe,c8 as Fe,aj as G,b2 as M,bN as Q,g as He}from"./mui-libs-CfwFIaTD.js";import{r as d,b as Z}from"./react-libs-Cr2nE3UY.js";import{a as Ke,D as ge,g as ee}from"./index-Bpan7Tbe.js";import{b as R,k as pe,c as Ue}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{i as Ye,l as qe}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./math-lib-BOZ-XUok.js";import"./charts-recharts-d3-BEF1Y_jn.js";const be={title:"Error Bar Chart",xAxisLabel:"Categories",yAxisLabel:"Values",errorBarType:"se",customMultiplier:1.96,showDataPoints:!0,showConnectingLines:!1,colorScheme:"default",markerSize:8,errorBarWidth:4,errorBarThickness:2,showLegend:!0,showGrid:!0},re={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],muted:["#6b7280","#9ca3af","#d1d5db","#374151","#4b5563","#6b7280","#9ca3af","#d1d5db"],sequential:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],diverging:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"]},$e={se:"Standard Error (SE)",sd:"Standard Deviation (SD)",ci95:"95% Confidence Interval",ci90:"90% Confidence Interval",ci99:"99% Confidence Interval",custom:"Custom Multiplier"},fe="plotly-error-bar-chart",lr=()=>{const{datasets:N,currentDataset:p}=Ke(),n=Te(),ye=d.useRef(null),[z,te]=d.useState((p==null?void 0:p.id)||""),[f,O]=d.useState(""),[u,F]=d.useState(""),[x,H]=d.useState(""),[t,ae]=d.useState(be),[T,I]=d.useState([]),[je,K]=d.useState({}),[V,se]=d.useState(!1),[ne,w]=d.useState(""),[y,U]=d.useState("variables"),i=Z.useMemo(()=>z&&N.find(r=>r.id===z)||null,[N,z]),ve=Z.useMemo(()=>(i==null?void 0:i.columns.filter(r=>r.type===ge.NUMERIC))||[],[i]),ie=Z.useMemo(()=>(i==null?void 0:i.columns.filter(r=>r.type===ge.CATEGORICAL))||[],[i]);d.useEffect(()=>{p!=null&&p.id&&p.id!==z&&(te(p.id),O(""),F(""),H(""),I([]),K({}),w(""))},[p]);const Ce=r=>{switch(r){case"ci90":return 1.645;case"ci95":return 1.96;case"ci99":return 2.576;case"custom":return t.customMultiplier;default:return 1}},Y=(r,a)=>{if(r.length===0)return 0;switch(R(r),r.length,a){case"se":return pe(r);case"sd":return Ue(r);case"ci90":case"ci95":case"ci99":case"custom":const D=pe(r),o=Ce(a);return D*o;default:return 0}},le=()=>{if(!i||!f){w("Please select a dataset and a numerical variable.");return}se(!0),w("");try{const r=i.columns.find(c=>c.id===f),a=u?i.columns.find(c=>c.id===u):null,D=x?i.columns.find(c=>c.id===x):null;if(!r)throw new Error("Numerical variable not found.");const o=re[t.colorScheme]||re.default,A=[],q={title:{text:t.title,font:{size:16,color:n.palette.text.primary}},xaxis:{title:{text:t.xAxisLabel},showgrid:t.showGrid,gridcolor:n.palette.divider,color:n.palette.text.primary,zeroline:!1,automargin:!0},yaxis:{title:{text:t.yAxisLabel},showgrid:t.showGrid,gridcolor:n.palette.divider,color:n.palette.text.primary,zeroline:!1,automargin:!0},showlegend:t.showLegend&&(x||!u&&!x),legend:{orientation:"v",x:1.02,y:1},plot_bgcolor:n.palette.mode==="dark"?n.palette.background.default:"#fff",paper_bgcolor:n.palette.mode==="dark"?n.palette.background.paper:"#fff",font:{color:n.palette.text.primary},margin:{l:80,r:80,t:80,b:80},autosize:!0};if(!u&&!x){const c=i.data.map(m=>m[r.name]).filter(m=>typeof m=="number"&&!isNaN(m));if(c.length===0)throw new Error("No valid numerical data found.");const j=R(c),v=Y(c,t.errorBarType);A.push({x:["Overall"],y:[j],error_y:{type:"data",array:[v],visible:!0,color:o[0],thickness:t.errorBarThickness,width:t.errorBarWidth},type:"scatter",mode:t.showDataPoints?t.showConnectingLines?"markers+lines":"markers":t.showConnectingLines?"lines":"markers",marker:{color:o[0],size:t.markerSize,symbol:"circle"},name:r.name,line:t.showConnectingLines?{color:o[0]}:void 0})}else if(u&&!x)ee(u,i).forEach((j,v)=>{const m=i.data.filter(g=>String(g[a.name])===j).map(g=>g[r.name]).filter(g=>typeof g=="number"&&!isNaN(g));if(m.length>0){const g=R(m),E=Y(m,t.errorBarType);A.push({x:[j],y:[g],error_y:{type:"data",array:[E],visible:!0,color:o[v%o.length],thickness:t.errorBarThickness,width:t.errorBarWidth},type:"scatter",mode:t.showDataPoints?t.showConnectingLines?"markers+lines":"markers":t.showConnectingLines?"lines":"markers",marker:{color:o[v%o.length],size:t.markerSize,symbol:"circle"},name:j,line:t.showConnectingLines?{color:o[v%o.length]}:void 0})}});else if(u&&x){const c=ee(u,i),j=ee(x,i),v=j.length,m=.8/v,g=-(v-1)*m/2;j.forEach((E,S)=>{const $=[],oe=[],ce=[];c.forEach((De,Ee)=>{const X=i.data.filter(b=>String(b[a.name])===De&&String(b[D.name])===E).map(b=>b[r.name]).filter(b=>typeof b=="number"&&!isNaN(b));if(X.length>0){const b=g+S*m;$.push(Ee+b),oe.push(R(X)),ce.push(Y(X,t.errorBarType))}}),$.length>0&&A.push({x:$,y:oe,error_y:{type:"data",array:ce,visible:!0,color:o[S%o.length],thickness:t.errorBarThickness,width:t.errorBarWidth},type:"scatter",mode:t.showDataPoints?t.showConnectingLines?"markers+lines":"markers":t.showConnectingLines?"lines":"markers",marker:{color:o[S%o.length],size:t.markerSize,symbol:"circle"},name:E,line:t.showConnectingLines?{color:o[S%o.length]}:void 0})}),q.xaxis={...q.xaxis,tickmode:"array",tickvals:c.map((E,S)=>S),ticktext:c}}I(A),K(q)}catch(r){w(`Error generating chart: ${r instanceof Error?r.message:String(r)}`),I([])}finally{se(!1)}},we=async()=>{if(T.length>0)try{const r={format:"svg",filename:t.title.replace(/\s+/g,"_")||"error_bar_chart",width:800,height:600};await qe.downloadImage(fe,r)}catch{w("Failed to download chart. Please try again.")}else w("Chart data not available for download.")},Se=()=>{ae(be)},ke=r=>{const a=r.target.value;te(a),O(""),F(""),H(""),I([]),K({}),w("")},Be=r=>{O(r.target.value)},Le=r=>{F(r.target.value)},ze=r=>{H(r.target.value)},h=(r,a)=>{ae(D=>({...D,[r]:a}))};return d.useEffect(()=>{f&&i&&le()},[f,u,x,t,i]),d.useEffect(()=>{const r=a=>{a.altKey&&!a.ctrlKey&&!a.shiftKey&&(a.key==="1"?(a.preventDefault(),U("variables")):a.key==="2"&&(a.preventDefault(),U("settings")))};return window.addEventListener("keydown",r),()=>window.removeEventListener("keydown",r)},[]),e.jsxs(s,{p:3,children:[e.jsx(l,{variant:"h5",gutterBottom:!0,children:"Error Bar Chart Generator"}),e.jsxs(Ie,{sx:{mb:3},children:[e.jsx(Ve,{expandIcon:e.jsx(We,{}),children:e.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[e.jsx(Ae,{color:"primary"}),e.jsx(l,{variant:"h6",children:"About Error Bar Charts"})]})}),e.jsxs(Pe,{children:[e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Error bar charts display the mean values of groups with error bars indicating the variability or uncertainty in the data. Error bars can represent different measures:"}),e.jsxs(s,{component:"ul",sx:{pl:2,mb:2},children:[e.jsxs(l,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Standard Error (SE):"})," Shows the precision of the sample mean"]}),e.jsxs(l,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Standard Deviation (SD):"})," Shows the spread of individual data points"]}),e.jsxs(l,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Confidence Intervals:"})," Shows the range likely to contain the true population mean"]})]}),e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Use error bar charts to compare means between groups while showing the uncertainty in your estimates."}),e.jsxs(l,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:[e.jsx("strong",{children:"Tip:"})," Use Alt+1 for Variables panel, Alt+2 for Settings panel to quickly switch between panels."]})]})]}),e.jsxs(J,{container:!0,spacing:2,children:[e.jsxs(J,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(W,{elevation:1,sx:{mb:1,backgroundColor:n.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(_e,{value:y,onChange:(r,a)=>U(a),variant:"fullWidth",sx:{minHeight:44,"& .MuiTab-root":{minHeight:44,fontSize:"0.875rem",fontWeight:500,color:n.palette.text.secondary,textTransform:"none",transition:"all 0.2s ease-in-out","&.Mui-selected":{color:n.palette.primary.main,backgroundColor:n.palette.mode==="dark"?"rgba(255, 255, 255, 0.08)":"rgba(25, 118, 210, 0.08)"},"&:hover":{backgroundColor:n.palette.mode==="dark"?"rgba(255, 255, 255, 0.04)":"rgba(0, 0, 0, 0.04)"}},"& .MuiTabs-indicator":{height:3,borderRadius:"3px 3px 0 0"}},children:[e.jsx(P,{title:"Variable Selection Panel",placement:"top",children:e.jsx(de,{value:"variables",label:"Variables",icon:e.jsx(Ge,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(P,{title:"Chart Settings Panel",placement:"top",children:e.jsx(de,{value:"settings",label:"Settings",icon:e.jsx(Me,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(he,{in:y==="variables",timeout:300,children:e.jsx(s,{sx:{display:y==="variables"?"block":"none"},children:e.jsxs(W,{elevation:2,sx:{p:2,height:"fit-content"},children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Variable Selection"}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(B,{children:"Dataset"}),e.jsx(L,{value:z,onChange:ke,label:"Dataset",children:N.map(r=>e.jsx(C,{value:r.id,children:r.name},r.id))})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",required:!0,children:[e.jsx(B,{children:"Numerical Variable"}),e.jsx(L,{value:f,onChange:Be,label:"Numerical Variable",children:ve.map(r=>e.jsx(C,{value:r.id,children:r.name},r.id))})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(B,{children:"Grouping Variable (Optional)"}),e.jsxs(L,{value:u,onChange:Le,label:"Grouping Variable (Optional)",children:[e.jsx(C,{value:"",children:e.jsx("em",{children:"None"})}),ie.map(r=>e.jsx(C,{value:r.id,children:r.name},r.id))]})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(B,{children:"Factor Variable (Optional)"}),e.jsxs(L,{value:x,onChange:ze,label:"Factor Variable (Optional)",disabled:!u,children:[e.jsx(C,{value:"",children:e.jsx("em",{children:"None"})}),ie.filter(r=>r.id!==u).map(r=>e.jsx(C,{value:r.id,children:r.name},r.id))]})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(B,{children:"Error Bar Type"}),e.jsx(L,{value:t.errorBarType,onChange:r=>h("errorBarType",r.target.value),label:"Error Bar Type",children:Object.entries($e).map(([r,a])=>e.jsx(C,{value:r,children:a},r))})]}),t.errorBarType==="custom"&&e.jsx(_,{fullWidth:!0,margin:"normal",size:"small",label:"Custom Multiplier",type:"number",value:t.customMultiplier,onChange:r=>h("customMultiplier",parseFloat(r.target.value)||1),inputProps:{step:.1,min:.1}}),e.jsxs(s,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(Re,{variant:"contained",onClick:le,disabled:!f||V,startIcon:V?e.jsx(ue,{size:20}):e.jsx(me,{}),fullWidth:!0,children:V?"Generating...":"Generate Chart"}),e.jsxs(s,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(P,{title:"Download Chart",children:e.jsx(xe,{onClick:we,disabled:T.length===0,children:e.jsx(Ne,{})})}),e.jsx(P,{title:"Reset Settings",children:e.jsx(xe,{onClick:Se,children:e.jsx(Oe,{})})})]})]})]})})}),e.jsx(he,{in:y==="settings",timeout:300,children:e.jsx(s,{sx:{display:y==="settings"?"block":"none"},children:e.jsxs(W,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:n.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(l,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Chart Settings"}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsxs(s,{children:[e.jsx(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Labels & Title"}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:1.5},children:[e.jsx(_,{fullWidth:!0,size:"small",label:"Chart Title",value:t.title,onChange:r=>h("title",r.target.value)}),e.jsx(_,{fullWidth:!0,size:"small",label:"X-Axis Label",value:t.xAxisLabel,onChange:r=>h("xAxisLabel",r.target.value)}),e.jsx(_,{fullWidth:!0,size:"small",label:"Y-Axis Label",value:t.yAxisLabel,onChange:r=>h("yAxisLabel",r.target.value)})]})]}),e.jsxs(s,{children:[e.jsx(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(k,{fullWidth:!0,size:"small",children:[e.jsx(B,{children:"Color Scheme"}),e.jsx(L,{value:t.colorScheme,onChange:r=>h("colorScheme",r.target.value),label:"Color Scheme",children:Object.keys(re).map(r=>e.jsx(C,{value:r,children:r.charAt(0).toUpperCase()+r.slice(1)},r))})]})]}),e.jsxs(s,{children:[e.jsx(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(Fe,{children:[e.jsx(G,{control:e.jsx(M,{size:"small",checked:t.showDataPoints,onChange:r=>h("showDataPoints",r.target.checked)}),label:"Show Data Points"}),e.jsx(G,{control:e.jsx(M,{size:"small",checked:t.showConnectingLines,onChange:r=>h("showConnectingLines",r.target.checked)}),label:"Show Connecting Lines"}),e.jsx(G,{control:e.jsx(M,{size:"small",checked:t.showLegend,onChange:r=>h("showLegend",r.target.checked)}),label:"Show Legend"}),e.jsx(G,{control:e.jsx(M,{size:"small",checked:t.showGrid,onChange:r=>h("showGrid",r.target.checked)}),label:"Show Grid"})]})]}),e.jsxs(s,{children:[e.jsx(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Size & Styling"}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsxs(s,{children:[e.jsxs(l,{variant:"body2",gutterBottom:!0,children:["Marker Size: ",t.markerSize]}),e.jsx(Q,{value:t.markerSize,onChange:(r,a)=>h("markerSize",a),min:4,max:20,step:1,size:"small",valueLabelDisplay:"auto"})]}),e.jsxs(s,{children:[e.jsxs(l,{variant:"body2",gutterBottom:!0,children:["Error Bar Width: ",t.errorBarWidth]}),e.jsx(Q,{value:t.errorBarWidth,onChange:(r,a)=>h("errorBarWidth",a),min:2,max:10,step:1,size:"small",valueLabelDisplay:"auto"})]}),e.jsxs(s,{children:[e.jsxs(l,{variant:"body2",gutterBottom:!0,children:["Error Bar Thickness: ",t.errorBarThickness]}),e.jsx(Q,{value:t.errorBarThickness,onChange:(r,a)=>h("errorBarThickness",a),min:1,max:5,step:.5,size:"small",valueLabelDisplay:"auto"})]})]})]})]})]})})})]}),e.jsx(J,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(W,{elevation:2,sx:{p:2},children:[e.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(l,{variant:"h6",children:"Chart Preview"}),e.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(l,{variant:"body2",color:"text.secondary",children:["Active: ",y==="variables"?"Variables":"Settings"]}),e.jsx(s,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:y==="variables"?n.palette.primary.main:n.palette.warning.main,boxShadow:`0 0 0 2px ${y==="variables"?n.palette.primary.main+"20":n.palette.warning.main+"20"}`}})]})]}),ne&&e.jsx(He,{severity:"error",sx:{mb:2},children:ne}),e.jsx(s,{ref:ye,sx:{minHeight:500,display:"flex",justifyContent:"center",alignItems:"center",border:`1px solid ${n.palette.divider}`,borderRadius:1,backgroundColor:n.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)"},children:V?e.jsxs(s,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(ue,{}),e.jsx(l,{color:"text.secondary",children:"Generating chart..."})]}):T.length>0?e.jsx(Ye,{divId:fe,data:T,layout:{...je,height:500},config:{displayModeBar:!0,displaylogo:!1,modeBarButtonsToRemove:["pan2d","lasso2d","select2d","autoScale2d"],responsive:!0,toImageButtonOptions:{format:"svg",filename:t.title.replace(/\s+/g,"_")||"error_bar_chart",width:800,height:600,scale:1}},style:{width:"100%",height:"500px"}}):e.jsxs(s,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,children:[e.jsx(me,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(l,{color:"text.secondary",textAlign:"center",children:i?f?"Chart will appear here once generated":"Select a numerical variable to generate the error bar chart":"Select a dataset to begin"}),i&&!f&&e.jsx(l,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"})]})})]})})]})]})};export{lr as default};
