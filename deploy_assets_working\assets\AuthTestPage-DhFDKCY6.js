var he=Object.defineProperty;var me=(r,t,o)=>t in r?he(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o;var S=(r,t,o)=>me(r,typeof t!="symbol"?t+"":t,o);import{j as e,B as F,e as m,G as g,R as y,L as T,m as a,r as c,ae as q,h as O,g as R,f as h,bH as xe,d as pe,y as ve,aj as we,b2 as Se,k as z,l as L,ao as ye,ap as ke,aq as be,ar as W,as as P,at as Fe}from"./mui-libs-CfwFIaTD.js";import{r as k}from"./react-libs-Cr2nE3UY.js";import{t as p,b as je,v as Ie,w as Pe,x as Ae,y as Ce,z as B}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const b=class b{constructor(){S(this,"isProduction");S(this,"authenticationLock",!1);S(this,"originalFetch");S(this,"originalServiceWorkerRegister",null);S(this,"authenticationTimeout",null);const t=window.location.protocol==="https:",o=window.location.hostname==="datastatpro.com"||window.location.hostname.includes("datastatpro"),n=typeof import.meta<"u"&&!0;this.isProduction=t||o||n,this.originalFetch=window.fetch.bind(window)}static getInstance(){return b.instance||(b.instance=new b),b.instance}initialize(){if(!p()||!this.isProduction){console.log("🦊 Production Firefox Auth Fix: Skipping initialization - not Firefox or not production");return}console.log("🦊 Production Firefox Auth Fix: Initializing..."),this.setupPersistentServiceWorkerBlocking(),this.setupAuthenticationAwareFetch(),this.setupAuthenticationStateMonitoring(),console.log("✅ Production Firefox Auth Fix: Initialized successfully")}startAuthenticationProtection(){!p()||!this.isProduction||(console.log("🦊 Production Firefox Auth Fix: Starting authentication protection"),this.authenticationLock=!0,localStorage.setItem("firefox-production-auth-lock","true"),localStorage.setItem("firefox-production-auth-start",Date.now().toString()),this.authenticationTimeout=setTimeout(()=>{console.warn("🦊 Production Firefox Auth Fix: Authentication timeout - releasing lock"),this.endAuthenticationProtection()},3e4),this.forceUnregisterServiceWorkers(),console.log("🦊 Production Firefox Auth Fix: Authentication protection active"))}endAuthenticationProtection(){!p()||!this.isProduction||(console.log("🦊 Production Firefox Auth Fix: Ending authentication protection"),this.authenticationLock=!1,localStorage.removeItem("firefox-production-auth-lock"),localStorage.removeItem("firefox-production-auth-start"),this.authenticationTimeout&&(clearTimeout(this.authenticationTimeout),this.authenticationTimeout=null),console.log("🦊 Production Firefox Auth Fix: Authentication protection ended"))}setupPersistentServiceWorkerBlocking(){"serviceWorker"in navigator&&(this.originalServiceWorkerRegister=navigator.serviceWorker.register.bind(navigator.serviceWorker),navigator.serviceWorker.register=async(t,o)=>{if(this.authenticationLock||localStorage.getItem("firefox-production-auth-lock")==="true")return console.log("🦊 Production Firefox Auth Fix: Blocking service worker registration during authentication"),console.log("🦊 Blocked script URL:",t),this.createMockServiceWorkerRegistration();try{return console.log("🦊 Production Firefox Auth Fix: Allowing service worker registration"),await this.originalServiceWorkerRegister(t,{...o,updateViaCache:"none"})}catch(n){return console.warn("🦊 Production Firefox Auth Fix: Service worker registration failed, returning mock:",n),this.createMockServiceWorkerRegistration()}})}setupAuthenticationAwareFetch(){window.fetch=async(t,o)=>{const n=typeof t=="string"?t:t instanceof URL?t.href:t.url;if(n.includes("supabase.co")&&(n.includes("auth")||n.includes("token"))){console.log("🦊 Production Firefox Auth Fix: Intercepting Supabase auth request");const s={...o,headers:{...o==null?void 0:o.headers,"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0","X-Firefox-Production":"true","X-Requested-With":"XMLHttpRequest"},credentials:"include"};try{const u=await this.originalFetch(t,s);return console.log("🦊 Production Firefox Auth Fix: Supabase auth request completed",{status:u.status,statusText:u.statusText,ok:u.ok}),u}catch(u){throw console.error("🦊 Production Firefox Auth Fix: Supabase auth request failed:",u),u}}return this.originalFetch(t,o)}}setupAuthenticationStateMonitoring(){window.addEventListener("storage",t=>{t.key==="firefox-production-auth-success"&&t.newValue==="true"&&(console.log("🦊 Production Firefox Auth Fix: Authentication success detected"),this.endAuthenticationProtection(),localStorage.removeItem("firefox-production-auth-success"),window.dispatchEvent(new CustomEvent("firefox-production-auth-completed",{detail:{success:!0}}))),t.key==="firefox-production-auth-failure"&&t.newValue==="true"&&(console.log("🦊 Production Firefox Auth Fix: Authentication failure detected"),this.endAuthenticationProtection(),localStorage.removeItem("firefox-production-auth-failure"),window.dispatchEvent(new CustomEvent("firefox-production-auth-completed",{detail:{success:!1}})))}),setInterval(()=>{const t=localStorage.getItem("firefox-production-auth-start");t&&this.authenticationLock&&Date.now()-parseInt(t)>6e4&&(console.warn("🦊 Production Firefox Auth Fix: Forcing authentication lock release due to timeout"),this.endAuthenticationProtection())},1e4)}async forceUnregisterServiceWorkers(){if("serviceWorker"in navigator)try{const t=await navigator.serviceWorker.getRegistrations();console.log(`🦊 Production Firefox Auth Fix: Found ${t.length} service worker registrations to unregister`);for(const o of t)await o.unregister(),console.log("🦊 Production Firefox Auth Fix: Unregistered service worker:",o.scope)}catch(t){console.warn("🦊 Production Firefox Auth Fix: Error unregistering service workers:",t)}}createMockServiceWorkerRegistration(){return Promise.resolve({scope:"/",installing:null,waiting:null,active:null,onupdatefound:null,updateViaCache:"imports",navigationPreload:{enable:()=>Promise.resolve(),disable:()=>Promise.resolve(),setHeaderValue:()=>Promise.resolve(),getState:()=>Promise.resolve({enabled:!1,headerValue:""})},pushManager:{supportedContentEncodings:[],subscribe:()=>Promise.reject(new Error("Push not supported during Firefox authentication")),getSubscription:()=>Promise.resolve(null),permissionState:()=>Promise.resolve("denied")},sync:{register:()=>Promise.resolve(),getTags:()=>Promise.resolve([])},getNotifications:()=>Promise.resolve([]),showNotification:()=>Promise.resolve(),addEventListener:()=>{},removeEventListener:()=>{},dispatchEvent:()=>!1,unregister:()=>Promise.resolve(!0),update:()=>Promise.resolve()})}signalAuthenticationSuccess(){!p()||!this.isProduction||(console.log("🦊 Production Firefox Auth Fix: Signaling authentication success"),localStorage.setItem("firefox-production-auth-success","true"),setTimeout(()=>{localStorage.removeItem("firefox-production-auth-success")},2e3))}signalAuthenticationFailure(){!p()||!this.isProduction||(console.log("🦊 Production Firefox Auth Fix: Signaling authentication failure"),localStorage.setItem("firefox-production-auth-failure","true"),setTimeout(()=>{localStorage.removeItem("firefox-production-auth-failure")},2e3))}getDebugInfo(){return{isFirefox:p(),isProduction:this.isProduction,authenticationLock:this.authenticationLock,hasTimeout:this.authenticationTimeout!==null,authFlags:{"firefox-production-auth-lock":localStorage.getItem("firefox-production-auth-lock"),"firefox-production-auth-start":localStorage.getItem("firefox-production-auth-start"),"firefox-production-auth-success":localStorage.getItem("firefox-production-auth-success"),"firefox-production-auth-failure":localStorage.getItem("firefox-production-auth-failure")},timestamp:new Date().toISOString()}}cleanup(){if(!p()||!this.isProduction)return;console.log("🦊 Production Firefox Auth Fix: Cleaning up..."),this.endAuthenticationProtection(),this.originalServiceWorkerRegister&&"serviceWorker"in navigator&&(navigator.serviceWorker.register=this.originalServiceWorkerRegister),window.fetch=this.originalFetch,["firefox-production-auth-lock","firefox-production-auth-start","firefox-production-auth-success","firefox-production-auth-failure"].forEach(o=>localStorage.removeItem(o)),console.log("✅ Production Firefox Auth Fix: Cleanup completed")}};S(b,"instance");let U=b;const M=U.getInstance();function A(){return/Firefox/.test(navigator.userAgent)}function C(){const r=window.location.protocol==="https:",t=window.location.hostname==="datastatpro.com"||window.location.hostname.includes("datastatpro");return r||t||typeof import.meta<"u"&&!0}if(A()&&C()){console.log("🦊 CRITICAL: Initializing Firefox real-time blocking to prevent NS_ERROR_CONTENT_BLOCKED..."),window.WebSocket=class extends WebSocket{constructor(t,o){const n=typeof t=="string"?t:t.toString();if(n.includes("supabase.co")&&(n.includes("realtime")||n.includes("websocket"))){console.log("🦊 BLOCKED: Supabase real-time WebSocket connection:",n),super("data:,"),setTimeout(()=>{this.close(1e3,"Firefox content blocking prevention")},0);return}super(t,o)}};const r=window.fetch;window.fetch=async function(t,o){const n=typeof t=="string"?t:t instanceof URL?t.toString():t.url;return n.includes("supabase.co")&&(n.includes("realtime")||n.includes("websocket")||n.includes("subscribe"))?(console.log("🦊 BLOCKED: Supabase real-time fetch request:",n),new Response(JSON.stringify({error:"Firefox real-time blocking active",blocked:!0,message:"Real-time features disabled for Firefox compatibility"}),{status:200,statusText:"OK",headers:{"Content-Type":"application/json"}})):r.call(this,t,o)},localStorage.setItem("firefox-realtime-blocked","true"),localStorage.setItem("firefox-use-fallback-auth","true"),localStorage.setItem("firefox-content-blocked","true"),console.log("🦊 Firefox real-time blocking initialized successfully")}function Ee(){if(!A()||!C()){console.log("🦊 Early Firefox fix: Skipping initialization",{isFirefox:A(),isProduction:C(),protocol:window.location.protocol,hostname:window.location.hostname,importMetaEnv:typeof import.meta<"u"?!0:"undefined"});return}if(console.log("🦊 Early Firefox production fix initializing...",{isFirefox:A(),isProduction:C(),protocol:window.location.protocol,hostname:window.location.hostname,importMetaEnv:typeof import.meta<"u"?!0:"undefined"}),"serviceWorker"in navigator&&navigator.serviceWorker.register){const r=navigator.serviceWorker.register.bind(navigator.serviceWorker);navigator.serviceWorker.register=async(t,o)=>{console.log("🦊 Early Firefox fix: Service worker registration intercepted"),console.log("🦊 Script URL:",t),console.log("🦊 Options:",o);const n=localStorage.getItem("datastatpro-auth-in-progress")==="true";if(console.log("🦊 Authentication status check:",{isAuthInProgress:n,authFlag:localStorage.getItem("datastatpro-auth-in-progress"),timestamp:new Date().toISOString()}),n)return console.log("🦊 Blocking service worker registration during authentication"),$();try{const s={...o,scope:(o==null?void 0:o.scope)||"/",updateViaCache:"none"};console.log("🦊 Allowing service worker registration with modified options:",s);const u=await r(t,s);return console.log("🦊 Service worker registration successful:",u),u}catch(s){return console.warn("🦊 Service worker registration failed, returning mock:",s),console.warn("🦊 Error details:",{message:s instanceof Error?s.message:String(s),stack:s instanceof Error?s.stack:void 0,scriptURL:String(t),options:modifiedOptions}),$()}},console.log("✅ Early Firefox fix: Service worker registration intercepted successfully")}Te(),console.log("✅ Early Firefox production fix initialized")}function $(){return Promise.resolve({scope:"/",installing:null,waiting:null,active:null,navigationPreload:{enable:()=>Promise.resolve(),disable:()=>Promise.resolve(),setHeaderValue:()=>Promise.resolve(),getState:()=>Promise.resolve({enabled:!1,headerValue:""})},pushManager:{supportedContentEncodings:[],subscribe:()=>Promise.reject(new Error("Push not supported in Firefox production mode")),getSubscription:()=>Promise.resolve(null),permissionState:()=>Promise.resolve("denied")},sync:{register:()=>Promise.resolve(),getTags:()=>Promise.resolve([])},addEventListener:()=>{},removeEventListener:()=>{},dispatchEvent:()=>!1,unregister:()=>Promise.resolve(!0),update:()=>Promise.resolve()})}function Te(){window.addEventListener("storage",t=>{t.key==="firefox-auth-success"&&t.newValue==="true"&&(console.log("🦊 Early Firefox fix: Authentication success detected in another tab"),localStorage.removeItem("firefox-auth-success"),setTimeout(()=>{window.location.reload()},1e3))}),setInterval(()=>{localStorage.getItem("datastatpro-auth-in-progress"),localStorage.getItem("firefox-auth-success")==="true"&&(console.log("🦊 Early Firefox fix: Authentication completed successfully"),localStorage.removeItem("firefox-auth-success"),localStorage.removeItem("datastatpro-auth-in-progress"),window.dispatchEvent(new CustomEvent("firefox-auth-completed")))},1e3)}function Ne(){return{isFirefox:A(),isProduction:C(),hasServiceWorkerOverride:"serviceWorker"in navigator&&navigator.serviceWorker.register.toString().includes("Early Firefox fix"),authFlags:{"datastatpro-auth-in-progress":localStorage.getItem("datastatpro-auth-in-progress"),"firefox-auth-success":localStorage.getItem("firefox-auth-success")},timestamp:new Date().toISOString()}}Ee();function G(){return/Firefox/.test(navigator.userAgent)}function X(){const r=window.location.protocol==="https:",t=window.location.hostname==="datastatpro.com"||window.location.hostname.includes("datastatpro");return r||t||typeof import.meta<"u"&&!0}async function De(){if(!G()||!X())return{blocked:!1};console.log("🦊 Testing Supabase connectivity for Firefox content blocking...");try{const r="https://ghzibvkqmdlpyaidfbah.supabase.co",t=`${r}/rest/v1/`,o=new AbortController,n=setTimeout(()=>o.abort(),5e3);try{const s=await fetch(t,{method:"HEAD",signal:o.signal,headers:{apikey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.OCeU38SWqhgeprIekN8qoaPdCzu04RaU-ktdNOci4rs"}});return clearTimeout(n),console.log("🦊 Supabase REST API test response:",s.status),{blocked:!1}}catch(s){clearTimeout(n);const u=s.message||String(s);return console.log("🦊 Supabase connectivity test failed:",u),[/NS_ERROR_CONTENT_BLOCKED/i,/content.*blocked/i,/blocked.*request/i,/network.*error/i,/failed.*fetch/i].some(j=>j.test(u))?(console.log("🦊 Firefox content blocking detected!"),{blocked:!0,error:u,details:{errorName:s.name,errorCode:s.code,userAgent:navigator.userAgent}}):{blocked:!1,error:u}}}catch(r){return console.error("🦊 Error testing Supabase connectivity:",r),{blocked:!1,error:r.message}}}function Oe(){return G()&&X()&&(localStorage.getItem("firefox-use-fallback-auth")==="true"||localStorage.getItem("firefox-content-blocked")==="true")}function Y(r="Manual activation"){if(!G()){console.log("🦊 Not Firefox browser, ignoring fallback activation");return}console.log("🦊 Enabling Firefox fallback mode:",r),localStorage.setItem("firefox-use-fallback-auth","true"),localStorage.setItem("firefox-fallback-reason",r),window.dispatchEvent(new CustomEvent("firefox-fallback-enabled",{detail:{reason:r}}))}function Re(){console.log("🦊 Disabling Firefox fallback mode"),localStorage.removeItem("firefox-use-fallback-auth"),localStorage.removeItem("firefox-content-blocked"),localStorage.removeItem("firefox-fallback-reason"),window.dispatchEvent(new CustomEvent("firefox-fallback-disabled"))}function N(){return{enabled:Oe(),reason:localStorage.getItem("firefox-fallback-reason")||void 0,contentBlocked:localStorage.getItem("firefox-content-blocked")==="true"}}const Ke=()=>{const{user:r,userProfile:t,accountType:o,isAuthenticated:n,isGuest:s,loading:u,refreshProfile:J,signOut:D}=je(),[j,V]=k.useState(!1),[f,Z]=k.useState(!1),[H,_]=k.useState([]),[K,Q]=k.useState({}),[ze,Le]=k.useState(!1);k.useEffect(()=>{if(!f)return;const i=console.log,l=console.error,v=console.warn,E=(x,...w)=>{const d=w.map(I=>typeof I=="object"?JSON.stringify(I,null,2):String(I)).join(" ");if(d.includes("🔄")||d.includes("✅")||d.includes("❌")||d.includes("auth")||d.includes("Auth")||d.includes("logout")||d.includes("signOut")||d.includes("SIGNED_OUT")){const I=new Date().toLocaleTimeString();_(fe=>[...fe.slice(-49),`[${I}] ${x.toUpperCase()}: ${d}`])}};return console.log=(...x)=>{i(...x),E("log",...x)},console.error=(...x)=>{l(...x),E("error",...x)},console.warn=(...x)=>{v(...x),E("warn",...x)},()=>{console.log=i,console.error=l,console.warn=v}},[f]),k.useEffect(()=>{if(!f)return;const i=()=>{const v={};["datastatpro-auth-loading-stuck","datastatpro-logout-signal","datastatpro-login-signal","datastatpro-cache-corruption-detected","datastatpro-loading-failures"].forEach(w=>{const d=localStorage.getItem(w);d!==null&&(v[w]=d)}),["isGuest","showSignupSuccess"].forEach(w=>{const d=sessionStorage.getItem(w);d!==null&&(v[`session:${w}`]=d)}),Q(v)};i();const l=setInterval(i,1e3);return()=>clearInterval(l)},[f]);const ee=async()=>{V(!0);try{await J()}finally{V(!1)}},te=async()=>{try{f&&console.log("🔄 [DIAGNOSTIC] Starting logout test..."),await D()}catch(i){console.error("Logout error:",i)}},oe=()=>{_([])},re=()=>{f&&console.log("🔄 [DIAGNOSTIC] Testing cross-tab logout signal...");const i={timestamp:Date.now(),reason:"cross-tab-test"};localStorage.setItem("datastatpro-logout-signal",JSON.stringify(i)),setTimeout(()=>{localStorage.removeItem("datastatpro-logout-signal")},2e3)},ie=()=>{f&&console.log("🔄 [DIAGNOSTIC] Testing cross-tab login signal...");const i={timestamp:Date.now(),userId:(r==null?void 0:r.id)||"test-user"};localStorage.setItem("datastatpro-login-signal",JSON.stringify(i)),setTimeout(()=>{localStorage.removeItem("datastatpro-login-signal")},2e3)},ne=()=>{f&&console.log("🔄 [DIAGNOSTIC] Clearing all auth-related storage..."),Object.keys(localStorage).filter(l=>l.includes("datastatpro")||l.includes("auth")).forEach(l=>localStorage.removeItem(l)),sessionStorage.removeItem("isGuest"),sessionStorage.removeItem("showSignupSuccess")},se=async()=>{f&&console.log("🔄 [DIAGNOSTIC] Simulating network error during logout...");const i=window.fetch;window.fetch=()=>Promise.reject(new Error("Simulated network error"));try{await D()}catch(l){console.error("Expected error from simulation:",l)}finally{setTimeout(()=>{window.fetch=i},1e3)}},ae=async()=>{f&&console.log("🦊 [DIAGNOSTIC] Testing Firefox auth preparation...");try{const i={earlyFixProduction:window.location.protocol==="https:"||typeof import.meta<"u"&&!0,prodFixProduction:typeof import.meta<"u"&&!0||window.location.protocol==="https:",protocol:window.location.protocol,hostname:window.location.hostname,importMetaEnv:typeof import.meta<"u"?!0:"undefined",consistent:(window.location.protocol==="https:"||typeof import.meta<"u"&&!0)==(typeof import.meta<"u"&&!0||window.location.protocol==="https:")};console.log("🦊 Environment detection check:",i),i.consistent||console.warn("⚠️ INCONSISTENT ENVIRONMENT DETECTION - This may cause Firefox auth issues!"),await B.prepareForAuthentication(),console.log("✅ Firefox auth preparation completed")}catch(i){console.error("❌ Firefox auth preparation failed:",i)}},ce=()=>B.getDebugInfo(),le=async()=>{f&&console.log("🦊 [DIAGNOSTIC] Forcing Firefox clean reload..."),await B.forceCleanReload()},ue=async()=>{f&&console.log("🦊 [DIAGNOSTIC] Testing production Firefox auth fix...");try{console.log("🦊 Starting production Firefox authentication protection test..."),M.startAuthenticationProtection(),await new Promise(i=>setTimeout(i,2e3)),console.log("🦊 Ending production Firefox authentication protection test..."),M.endAuthenticationProtection(),console.log("✅ Production Firefox auth fix test completed")}catch(i){console.error("❌ Production Firefox auth fix test failed:",i)}},de=async()=>{if(!p()){console.log("⚠️ Not Firefox browser, skipping content blocking test");return}try{console.log("🦊 Testing Firefox content blocking detection...");const i=N();console.log("🦊 Current fallback status:",i),console.log("🦊 Testing Supabase connectivity...");const l=await De();console.log("🦊 Connectivity test result:",l),l.blocked?(console.log("🦊 Content blocking detected! Fallback mode should be enabled."),Y("Content blocking detected during test")):console.log("🦊 No content blocking detected.");const v=N();console.log("🦊 Updated fallback status:",v),console.log("✅ Firefox content blocking test completed")}catch(i){console.error("❌ Firefox content blocking test failed:",i)}},ge=()=>{N().enabled?(console.log("🦊 Disabling Firefox fallback mode..."),Re()):(console.log("🦊 Enabling Firefox fallback mode..."),Y("Manual toggle from AuthTestPage"));const l=N();console.log("🦊 Firefox fallback status:",l)};return e.jsxs(F,{p:3,children:[e.jsx(m,{variant:"h4",gutterBottom:!0,children:"Authentication Testing & Debugging"}),e.jsxs(g,{container:!0,spacing:3,children:[e.jsx(g,{item:!0,xs:12,md:6,children:e.jsxs(y,{sx:{p:2},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Browser Information"}),e.jsxs(T,{dense:!0,children:[e.jsx(a,{children:e.jsx(c,{primary:"Browser",secondary:p()?`Firefox ${Ie()}`:"Not Firefox"})}),e.jsx(a,{children:e.jsx(c,{primary:"Firefox Auth Fallback",secondary:Pe()?"Enabled":"Disabled"})}),p()&&e.jsxs(e.Fragment,{children:[e.jsx(a,{children:e.jsx(c,{primary:"Firefox Debug Info",secondary:e.jsx("pre",{style:{fontSize:"0.75rem",margin:0},children:JSON.stringify(ce(),null,2)})})}),e.jsx(a,{children:e.jsx(c,{primary:"Production Firefox Fix",secondary:e.jsx("pre",{style:{fontSize:"0.75rem",margin:0},children:JSON.stringify(Ae.getDebugInfo(),null,2)})})}),e.jsx(a,{children:e.jsx(c,{primary:"Production Firefox Auth Fix (NEW)",secondary:e.jsx("pre",{style:{fontSize:"0.75rem",margin:0},children:JSON.stringify(M.getDebugInfo(),null,2)})})}),e.jsx(a,{children:e.jsx(c,{primary:"Early Firefox Fix (Critical)",secondary:e.jsx("pre",{style:{fontSize:"0.75rem",margin:0},children:JSON.stringify(Ne(),null,2)})})}),e.jsx(a,{children:e.jsx(c,{primary:"Environment Detection Check",secondary:e.jsx("pre",{style:{fontSize:"0.75rem",margin:0},children:JSON.stringify({earlyFixProduction:window.location.protocol==="https:"||typeof import.meta<"u"&&!0,prodFixProduction:typeof import.meta<"u"&&!0||window.location.protocol==="https:",protocol:window.location.protocol,hostname:window.location.hostname,importMetaEnv:typeof import.meta<"u"?!0:"undefined",consistent:(window.location.protocol==="https:"||typeof import.meta<"u"&&!0)==(typeof import.meta<"u"&&!0||window.location.protocol==="https:")},null,2)})})}),e.jsx(a,{children:e.jsx(c,{primary:"Firefox Auth Recovery System",secondary:e.jsx("pre",{style:{fontSize:"0.75rem",margin:0},children:JSON.stringify(Ce.getDebugInfo(),null,2)})})})]})]})]})}),e.jsx(g,{item:!0,xs:12,md:6,children:e.jsxs(y,{sx:{p:2},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Authentication Status"}),e.jsxs(T,{dense:!0,children:[e.jsxs(a,{children:[e.jsx(c,{primary:"Loading",secondary:u?"Yes":"No"}),u&&e.jsx(q,{size:20})]}),e.jsxs(a,{children:[e.jsx(c,{primary:"Is Authenticated",secondary:n?"Yes":"No"}),e.jsx(O,{label:n?"Authenticated":"Not Authenticated",color:n?"success":"default",size:"small"})]}),e.jsxs(a,{children:[e.jsx(c,{primary:"Is Guest",secondary:s?"Yes":"No"}),e.jsx(O,{label:s?"Guest":"User",color:s?"warning":"primary",size:"small"})]}),e.jsxs(a,{children:[e.jsx(c,{primary:"Account Type",secondary:o||"None"}),e.jsx(O,{label:o||"None",color:o==="pro"?"primary":o==="edu"?"secondary":"default",size:"small"})]})]})]})}),e.jsx(g,{item:!0,xs:12,md:6,children:e.jsxs(y,{sx:{p:2},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"User Information"}),r?e.jsxs(T,{dense:!0,children:[e.jsx(a,{children:e.jsx(c,{primary:"User ID",secondary:r.id})}),e.jsx(a,{children:e.jsx(c,{primary:"Email",secondary:r.email})}),e.jsx(a,{children:e.jsx(c,{primary:"Created At",secondary:new Date(r.created_at||"").toLocaleString()})}),e.jsx(a,{children:e.jsx(c,{primary:"Email Confirmed",secondary:r.email_confirmed_at?"Yes":"No"})})]}):e.jsx(R,{severity:"info",children:"No user data available"})]})}),e.jsx(g,{item:!0,xs:12,children:e.jsxs(y,{sx:{p:2},children:[e.jsxs(F,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(m,{variant:"h6",children:"Profile Information"}),e.jsx(h,{variant:"outlined",size:"small",startIcon:j?e.jsx(q,{size:16}):e.jsx(xe,{}),onClick:ee,disabled:j||!r,children:j?"Refreshing...":"Refresh Profile"})]}),t?e.jsxs(g,{container:!0,spacing:2,children:[e.jsxs(g,{item:!0,xs:12,md:3,sx:{textAlign:"center"},children:[e.jsx(pe,{sx:{width:80,height:80,mx:"auto",mb:1},src:t.avatar_url||void 0,children:e.jsx(ve,{fontSize:"large"})}),e.jsx(m,{variant:"body2",color:"text.secondary",children:"Avatar"})]}),e.jsx(g,{item:!0,xs:12,md:9,children:e.jsxs(T,{dense:!0,children:[e.jsx(a,{children:e.jsx(c,{primary:"Username",secondary:t.username||"Not set"})}),e.jsx(a,{children:e.jsx(c,{primary:"Full Name",secondary:t.full_name||"Not set"})}),e.jsx(a,{children:e.jsx(c,{primary:"Institution",secondary:t.institution||"Not set"})}),e.jsx(a,{children:e.jsx(c,{primary:"Country",secondary:t.country||"Not set"})}),e.jsx(a,{children:e.jsx(c,{primary:"Account Type (from profile)",secondary:t.accounttype||"Not set"})})]})})]}):r?e.jsx(R,{severity:"warning",children:"User is authenticated but no profile data available. Try refreshing the profile."}):e.jsx(R,{severity:"info",children:"No profile data available (user not authenticated)"})]})}),e.jsx(g,{item:!0,xs:12,children:e.jsxs(y,{sx:{p:2},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Actions"}),e.jsxs(F,{display:"flex",gap:2,flexWrap:"wrap",children:[e.jsx(h,{variant:"outlined",onClick:()=>window.location.hash="auth",disabled:n,children:"Go to Login"}),e.jsx(h,{variant:"outlined",onClick:()=>window.location.hash="user-profile",disabled:!n,children:"Go to Profile"}),e.jsx(h,{variant:"outlined",color:"error",onClick:te,disabled:!n,children:"Logout"})]})]})}),e.jsx(g,{item:!0,xs:12,children:e.jsxs(y,{sx:{p:2},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Debug Information"}),e.jsx(m,{variant:"body2",component:"pre",sx:{backgroundColor:"grey.100",p:2,borderRadius:1,overflow:"auto",fontSize:"0.75rem"},children:JSON.stringify({user:r?{id:r.id,email:r.email,created_at:r.created_at,email_confirmed_at:r.email_confirmed_at}:null,userProfile:t,accountType:o,isAuthenticated:n,isGuest:s,loading:u},null,2)})]})}),e.jsx(g,{item:!0,xs:12,children:e.jsxs(y,{sx:{p:2},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Authentication Diagnostics"}),e.jsx(we,{control:e.jsx(Se,{checked:f,onChange:i=>Z(i.target.checked)}),label:"Enable Diagnostic Mode"}),f&&e.jsx(F,{sx:{mt:2},children:e.jsxs(g,{container:!0,spacing:2,children:[e.jsx(g,{item:!0,xs:12,md:6,children:e.jsx(z,{children:e.jsxs(L,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Diagnostic Controls"}),e.jsxs(F,{sx:{display:"flex",flexDirection:"column",gap:1},children:[e.jsx(h,{variant:"outlined",onClick:re,size:"small",children:"Test Cross-Tab Logout"}),e.jsx(h,{variant:"outlined",onClick:ie,size:"small",children:"Test Cross-Tab Login"}),e.jsx(h,{variant:"outlined",onClick:se,color:"warning",size:"small",children:"Simulate Network Error"}),e.jsx(h,{variant:"outlined",onClick:ne,color:"error",size:"small",children:"Clear All Auth Storage"}),e.jsx(h,{variant:"outlined",onClick:oe,size:"small",children:"Clear Logs"}),p()&&e.jsxs(e.Fragment,{children:[e.jsx(h,{variant:"outlined",onClick:ae,color:"info",size:"small",children:"Test Firefox Auth Prep"}),e.jsx(h,{variant:"outlined",onClick:le,color:"warning",size:"small",children:"Force Firefox Clean Reload"}),e.jsx(h,{variant:"outlined",onClick:ue,color:"secondary",size:"small",children:"Test Production Auth Fix"}),e.jsx(h,{variant:"outlined",onClick:de,color:"primary",size:"small",children:"Test Content Blocking"}),e.jsx(h,{variant:"outlined",onClick:ge,color:"warning",size:"small",children:"Toggle Fallback Mode"})]})]})]})})}),e.jsx(g,{item:!0,xs:12,md:6,children:e.jsx(z,{children:e.jsxs(L,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Storage Monitor"}),e.jsx(ye,{sx:{maxHeight:200},children:e.jsxs(ke,{size:"small",children:[e.jsx(be,{children:e.jsxs(W,{children:[e.jsx(P,{children:"Key"}),e.jsx(P,{children:"Value"})]})}),e.jsxs(Fe,{children:[Object.entries(K).map(([i,l])=>e.jsxs(W,{children:[e.jsx(P,{sx:{fontSize:"0.75rem"},children:i}),e.jsx(P,{sx:{fontSize:"0.75rem"},children:l})]},i)),Object.keys(K).length===0&&e.jsx(W,{children:e.jsx(P,{colSpan:2,align:"center",children:"No auth-related storage data"})})]})]})})]})})}),e.jsx(g,{item:!0,xs:12,children:e.jsx(z,{children:e.jsxs(L,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Authentication Logs (Last 50)"}),e.jsx(F,{sx:{maxHeight:300,overflow:"auto",backgroundColor:"#f5f5f5",p:1,borderRadius:1,fontFamily:"monospace",fontSize:"0.75rem"},children:H.length>0?H.map((i,l)=>e.jsx("div",{style:{marginBottom:"2px"},children:i},l)):e.jsx("div",{children:"No authentication logs captured yet..."})})]})})})]})})]})})]})]})};export{Ke as default};
