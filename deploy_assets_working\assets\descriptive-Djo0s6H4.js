import{j as c}from"./other-utils-CR9xr_gI.js";import{j as y}from"./index-Bpan7Tbe.js";const u=e=>e.filter(n=>typeof n=="number"&&!isNaN(n)&&isFinite(n)),p=e=>u(e).sort((r,s)=>r-s),M=(e,n=1.5)=>{const r=p(e);if(r.length<4)return{filtered:r,outliers:[],bounds:{lower:-1/0,upper:1/0}};const s=Math.floor((r.length-1)*.25),o=Math.floor((r.length-1)*.75),t=r[s],i=r[o],l=i-t,m=t-n*l,a=i+n*l,h=[],g=[];return r.forEach(f=>{f>=m&&f<=a?h.push(f):g.push(f)}),{filtered:h,outliers:g,bounds:{lower:m,upper:a}}},N=(e,n)=>y(e,n);function V(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return n.length===0?0:c.mean(n)}function w(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return n.length===0?0:c.median(n)}function x(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;if(n.length===0)return{mode:null,message:"No mode found (empty dataset)."};const r={};let s=0;n.forEach(t=>{r[t]=(r[t]||0)+1,r[t]>s&&(s=r[t])});const o=Object.keys(r).filter(t=>r[Number(t)]===s).map(t=>Number(t)).sort((t,i)=>t-i);return o.length===0?{mode:null,message:"No mode found."}:o.length===1?{mode:o[0],message:"Single mode found."}:{mode:o[0],message:"Multiple modes exist, smallest one is being reported."}}function E(e,n=!0){const r=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return r.length<=1?0:n?c.variance(r,!0):c.variance(r,!1)}function b(e,n=!0){const r=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return r.length<=1?0:n?c.stdev(r,!0):c.stdev(r,!1)}function j(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return n.length<=1?0:b(n,!0)/Math.sqrt(n.length)}function k(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return n.length<=2?0:c.skewness(n)}function I(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return n.length<=3?0:c.kurtosis(n)}function F(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;return n.length===0?0:Math.max(...n)-Math.min(...n)}function A(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;if(n.length===0)return[0,0,0];const r=[...n].sort((i,l)=>i-l),s=c.quantiles(r,[.25])[0],o=c.median(r),t=c.quantiles(r,[.75])[0];return[s,o,t]}function O(e){const n=Array.isArray(e)&&e.length>0&&typeof e[0]!="number"?u(e):e;if(n.length===0)return 0;const[r,,s]=A(n);return s-r}const q=e=>{const n={};return e.forEach(r=>{const s=String(r);n[s]=(n[s]||0)+1}),n},B=e=>{const n=q(e),r=e.length,s={};return Object.keys(n).forEach(o=>{s[o]=n[o]/r}),s},C=(e,n)=>{if(e.length!==n.length)throw new Error("Variables must have the same length");const r=[...new Set(e.map(String))],s=[...new Set(n.map(String))],o={};r.forEach(t=>{o[t]={},s.forEach(i=>{o[t][i]=0})});for(let t=0;t<e.length;t++){const i=String(e[t]),l=String(n[t]);o[i][l]+=1}return o};export{w as a,V as b,b as c,E as d,N as e,u as f,F as g,q as h,B as i,A as j,j as k,O as l,k as m,I as n,C as o,M as p,x as q,p as s};
