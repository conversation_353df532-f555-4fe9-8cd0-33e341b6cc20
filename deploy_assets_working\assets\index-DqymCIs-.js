const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./PlotlyBoxPlot-CsYPOD7q.js","./mui-libs-CfwFIaTD.js","./react-libs-Cr2nE3UY.js","./charts-plotly-BhN4fPIu.js","./charts-recharts-d3-BEF1Y_jn.js","./charts-plotly-CuCRB34y.css"])))=>i.map(i=>d[i]);
import{u as Vt,j as e,B as x,aQ as Rs,e as a,c_ as $i,aE as de,f as Te,bo as _s,bn as Ks,I as Ps,ax as Os,F as ys,R as H,bl as xi,G as c,ai as qe,b9 as Pe,ba as Oe,bb as B,h as Ae,bc as js,r as Qt,bW as Qs,bX as Gs,aW as ws,aD as Hs,bY as Us,aj as ae,b2 as Fe,c3 as ti,c$ as si,c6 as hi,a_ as mi,ak as vs,ah as Ti,ae as Jt,aP as Ws,bH as Bs,d0 as ui,d1 as gi,g as tt,a0 as Ni,ao as dt,ap as xt,aq as ht,ar as V,as as d,at as mt,o as os,by as Gt,a6 as Yt,a7 as je,bt as Nt,Z as Ht,d2 as ii,a2 as Mt,d3 as zs,L as Vi,m as ls,n as cs,D as ds,aF as Ct,bq as Wi,cn as zi,bD as ji,d4 as Ai,d5 as ai,W as it,d6 as Ri,ad as Es,aX as pi,bL as Ms,bM as Ne,$ as Pi,d7 as lt,k as Be,l as Ee,d8 as Oi,c9 as Bi,a as Cs,bz as Ss,V as bi,br as ps,a8 as Ds,N as ks,d9 as Ei,da as ni,bw as Di,db as Li,dc as xs,dd as _i,de as Ki,df as Qi,X as As,bR as Gi,c4 as Hi}from"./mui-libs-CfwFIaTD.js";import{r as b}from"./react-libs-Cr2nE3UY.js";import{_ as Ui}from"./supabase-lib-B3goak-P.js";import{a as $s,D as Ue,n as Ji,i as Is,p as Yi,A as Xi,B as Zi,e as ri}from"./index-Bpan7Tbe.js";import{S as $e}from"./StatsCard-op8tGQ0a.js";import{e as oi,p as ea,b as st,a as Fs,q as ta,d as sa,c as ct,g as ia,j as $t,l as aa,m as fi,n as yi,o as na,s as ra}from"./descriptive-Djo0s6H4.js";import{j as oa}from"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{c as vi}from"./normality-CwHD6Rjl.js";import{R as ze,H as la,I as ca,J as da,K as xa,M as ha,L as ut,N as Ci,C as Ye,X as Xe,Y as He,T as Re,a as St,b as Ut,e as bs,c as vt,O as Tt,Q as ma,U as ua,d as ga,V as Si,W as ki,B as qs,P as Ls,f as fs,Z as li}from"./charts-recharts-d3-BEF1Y_jn.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./math-lib-BOZ-XUok.js";const ja=b.lazy(()=>Ui(()=>import("./PlotlyBoxPlot-CsYPOD7q.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)),pa=y=>{const t=st(y),w=ct(y);return t!==0?w/Math.abs(t)*100:0},Ii=y=>ct(y)/Math.sqrt(y.length),ba=(y,t=.95)=>{const w=st(y),n=Ii(y),j=t===.95?1.96:t===.99?2.576:1.645;return[w-j*n,w+j*n]},fa=y=>{const t=y.filter(n=>n>0);if(t.length===0)return 0;const w=t.reduce((n,j)=>n*j,1);return Math.pow(w,1/t.length)},ya=y=>{const t=y.filter(n=>n>0);if(t.length===0)return 0;const w=t.reduce((n,j)=>n+1/j,0);return t.length/w},va=(y,t=.05)=>{const w=[...y].sort((U,m)=>U-m),n=Math.floor(w.length*t),j=w.slice(n,w.length-n);return st(j)},Ca=y=>{const t=Fs(y),w=y.map(n=>Math.abs(n-t));return Fs(w)},hs=({title:y,value:t,subtitle:w,icon:n,color:j="primary",trend:U})=>{const m=Vt(),v=(X=>{const A={primary:{background:m.palette.mode==="dark"?"rgba(33, 150, 243, 0.08)":"rgba(25, 118, 210, 0.04)",iconBackground:m.palette.mode==="dark"?"rgba(33, 150, 243, 0.12)":"rgba(25, 118, 210, 0.08)",iconColor:m.palette.mode==="dark"?"rgba(33, 150, 243, 0.7)":"rgba(25, 118, 210, 0.7)",border:m.palette.mode==="dark"?"rgba(33, 150, 243, 0.12)":"rgba(25, 118, 210, 0.12)"},secondary:{background:m.palette.mode==="dark"?"rgba(156, 39, 176, 0.08)":"rgba(156, 39, 176, 0.04)",iconBackground:m.palette.mode==="dark"?"rgba(156, 39, 176, 0.12)":"rgba(156, 39, 176, 0.08)",iconColor:(m.palette.mode==="dark","rgba(156, 39, 176, 0.7)"),border:(m.palette.mode==="dark","rgba(156, 39, 176, 0.12)")},success:{background:m.palette.mode==="dark"?"rgba(76, 175, 80, 0.08)":"rgba(46, 125, 50, 0.04)",iconBackground:m.palette.mode==="dark"?"rgba(76, 175, 80, 0.12)":"rgba(46, 125, 50, 0.08)",iconColor:m.palette.mode==="dark"?"rgba(76, 175, 80, 0.7)":"rgba(46, 125, 50, 0.7)",border:m.palette.mode==="dark"?"rgba(76, 175, 80, 0.12)":"rgba(46, 125, 50, 0.12)"},warning:{background:m.palette.mode==="dark"?"rgba(255, 152, 0, 0.08)":"rgba(237, 108, 2, 0.04)",iconBackground:m.palette.mode==="dark"?"rgba(255, 152, 0, 0.12)":"rgba(237, 108, 2, 0.08)",iconColor:m.palette.mode==="dark"?"rgba(255, 152, 0, 0.7)":"rgba(237, 108, 2, 0.7)",border:m.palette.mode==="dark"?"rgba(255, 152, 0, 0.12)":"rgba(237, 108, 2, 0.12)"}};return A[X]||A.primary})(j);return e.jsx(Be,{sx:{height:"100%",background:v.background,border:`1px solid ${v.border}`,borderRadius:2,transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)","&:hover":{boxShadow:m.palette.mode==="dark"?"0 8px 32px rgba(0, 0, 0, 0.3)":"0 8px 32px rgba(0, 0, 0, 0.08)",transform:"translateY(-2px)",borderColor:v.iconColor}},children:e.jsxs(Ee,{sx:{p:3,"&:last-child":{pb:3}},children:[e.jsxs(x,{display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:2,children:[e.jsxs(x,{flex:1,mr:2,children:[e.jsx(a,{color:"text.secondary",variant:"body2",fontWeight:500,sx:{textTransform:"uppercase",letterSpacing:"0.5px",fontSize:"0.75rem",mb:1},children:y}),e.jsx(a,{variant:"h4",component:"div",fontWeight:600,sx:{color:m.palette.text.primary,lineHeight:1.2,mb:.5},children:t})]}),e.jsx(x,{sx:{width:56,height:56,borderRadius:2,background:v.iconBackground,display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0},children:e.jsx(x,{sx:{color:v.iconColor,display:"flex",alignItems:"center",justifyContent:"center","& svg":{fontSize:"1.5rem"}},children:n})})]}),w&&e.jsx(a,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.875rem",lineHeight:1.4,fontWeight:400},children:w}),U&&e.jsxs(x,{display:"flex",alignItems:"center",mt:2,pt:2,borderTop:`1px solid ${v.border}`,children:[U>0?e.jsx(pi,{sx:{color:"success.main",fontSize:"1rem",mr:.5}}):U<0?e.jsx(Oi,{sx:{color:"error.main",fontSize:"1rem",mr:.5}}):e.jsx(Bi,{sx:{color:"text.disabled",fontSize:"1rem",mr:.5}}),e.jsxs(a,{variant:"body2",fontWeight:500,color:U>0?"success.main":U<0?"error.main":"text.secondary",children:[Math.abs(U).toFixed(1),"%"]})]})]})})},Sa=()=>{const{datasets:y,currentDataset:t,setCurrentDataset:w}=$s(),n=Vt(),[j,U]=b.useState((t==null?void 0:t.id)||"");b.useEffect(()=>{t!=null&&t.id&&t.id!==j&&U(t.id)},[t==null?void 0:t.id,j]);const[m,ne]=b.useState([]),[v,X]=b.useState(!0),[A,ye]=b.useState(!0),[K,O]=b.useState(!0),[N,te]=b.useState(!0),[W,ce]=b.useState(!0),[Q,xe]=b.useState(.95),[D,pe]=b.useState(!1),[g,ue]=b.useState(""),[he,De]=b.useState("histogram"),[se,ve]=b.useState(!1),[L,Ze]=b.useState(!1),[me,Le]=b.useState("iqr"),[_e,Ve]=b.useState(1.5),[Ke,Ge]=b.useState(!1),[o,f]=b.useState(null),[C,u]=b.useState(0),[F,P]=b.useState("options"),[ie,G]=b.useState("grid"),[T,We]=b.useState({});b.useEffect(()=>{const r=localStorage.getItem("descriptive_analysis_results"),i=localStorage.getItem("descriptive_analysis_config");if(r)try{We(JSON.parse(r))}catch(l){console.error("Error parsing saved descriptive analysis results:",l)}if(i)try{const l=JSON.parse(i);X(l.includeShape??!0),ye(l.includeOutliers??!0),O(l.includeNormalityTest??!0),te(l.includeBoxPlot??!0),ce(l.includeConfidenceIntervals??!0),xe(l.confidenceLevel??.95),pe(l.comparisonMode??!1),ue(l.groupingVariable??""),ve(l.showAdvancedStats??!1),Ze(l.filterOutliers??!1),Le(l.outlierMethod??"iqr"),Ve(l.outlierThreshold??1.5)}catch(l){console.error("Error parsing saved configuration:",l)}},[]),b.useEffect(()=>{const r={includeShape:v,includeOutliers:A,includeNormalityTest:K,includeBoxPlot:N,includeConfidenceIntervals:W,confidenceLevel:Q,comparisonMode:D,groupingVariable:g,showAdvancedStats:se,filterOutliers:L,outlierMethod:me,outlierThreshold:_e};localStorage.setItem("descriptive_analysis_config",JSON.stringify(r))},[v,A,K,N,W,Q,D,g,se,L,me,_e]);const Ce=(t==null?void 0:t.columns.filter(r=>r.type===Ue.NUMERIC))||[],we=(t==null?void 0:t.columns.filter(r=>r.type===Ue.CATEGORICAL||r.type===Ue.ORDINAL||r.type===Ue.BOOLEAN))||[],et=Ce,Z=r=>{const i=r.target.value;U(i),ne([]),ue(""),We({});const l=y.find(S=>S.id===i);l&&w(l),localStorage.removeItem("descriptive_analysis_results")},gt=r=>{const i=r.target.value;ne(typeof i=="string"?[i]:i),We({}),localStorage.removeItem("descriptive_analysis_results")},jt=(r,i,l)=>{if(i==="iqr"){const S=$t(r)[0],h=$t(r)[2],p=h-S,J=S-l*p,re=h+l*p,q=r.filter(Je=>Je>=J&&Je<=re);return{filtered:q,removed:r.length-q.length}}else{const S=st(r),h=ct(r),p=r.filter(J=>Math.abs((J-S)/h)<=l);return{filtered:p,removed:r.length-p.length}}},at=r=>{const i=[...r].sort((p,J)=>p-J),l=i.length,S=st(r),h=ct(r);return l===0||h===0?[]:i.map((p,J)=>{const re=(J+.5)/l,q=Math.sqrt(2)*nt(2*re-1);return{theoretical:S+q*h,sample:p}})},nt=r=>{const l=r<0?-1:1;r=Math.abs(r);const S=Math.log(1-r*r),h=2/(Math.PI*.147)+S/2,p=S/.147;return l*Math.sqrt(Math.sqrt(h*h-p)-h)},pt=r=>{if(r.length===0)return[];const i=st(r),l=ct(r);if(l===0)return[{x:i,y:1}];const S=Math.min(...r)-l,h=Math.max(...r)+l,p=100,J=(h-S)/p,re=[];for(let q=0;q<=p;q++){const Je=S+q*J,kt=1/(l*Math.sqrt(2*Math.PI))*Math.exp(-.5*Math.pow((Je-i)/l,2));re.push({x:Je,y:kt})}return re},bt=()=>{if(!t||m.length===0){f("Please select a dataset and at least one numeric column to analyze.");return}if(D&&!g){f("Please select a grouping variable for comparison mode.");return}Ge(!0),f(null),setTimeout(()=>{try{const r=t.columns.filter(l=>m.includes(l.id)),i={};r.forEach(l=>{const S=t.data.map(M=>M[l.name]);let h=oi(t.data,l);if(h.length===0)return;const p=Ji(t.data,l),J=h.length;let re=0;if(L)if(me==="iqr"){const M=ea(S,_e);h=M.filtered,re=J-M.filtered.length}else{const M=jt(h,me,_e);h=M.filtered,re=M.removed}const q=h.length,Je=p.missingCount,kt=st(h),Xt=Fs(h),Zt=ta(h),es=sa(h),ts=ct(h),rt=h.length>0?Math.min(...h):0,It=h.length>0?Math.max(...h):0,Wt=ia(h),ft=$t(h),ss=aa(h),is=pa(h),as=Ii(h),ns=W?ba(h,Q):void 0,zt={};h.length>0&&[5,10,20,30,40,60,70,80,90,95].forEach(M=>{const oe=Math.ceil(M/100*q)-1;zt[`p${M}`]=h[Math.max(0,Math.min(oe,q-1))]});let At,Rt,Pt,Ot;se&&(At=fa(h),Rt=ya(h),Pt=va(h),Ot=Ca(h));let Bt,Et,Dt;if(v&&(Bt=fi(h),Et=yi(h)),K){const M=vi(h,.05,["auto"]),oe=M.recommendedTest.toLowerCase().replace(/[^a-z]/g,"");let z=null;oe.includes("shapiro")?z=M.tests.shapiroWilk:oe.includes("kolmogorov")?z=M.tests.kolmogorovSmirnov:oe.includes("jarque")?z=M.tests.jarqueBera:oe.includes("anderson")&&(z=M.tests.andersonDarling),Dt={isNormal:M.overallAssessment.isNormal,pValue:(z==null?void 0:z.pValue)||NaN,statistic:(z==null?void 0:z.statistic)||NaN}}let Ft;if(D&&g){const M=t.columns.find(oe=>oe.id===g);if(M){Ft={};const oe={};t.data.forEach(z=>{const fe=z[l.name],ee=z[M.name],ke=Is(fe,l),yt=Is(ee,M).isMissing?"Missing":String(ee);!ke.isMissing&&typeof fe=="number"&&!isNaN(fe)&&(oe[yt]||(oe[yt]=[]),oe[yt].push(fe))}),Object.entries(oe).forEach(([z,fe])=>{let ee=[...fe].sort((ke,ot)=>ke-ot);L&&(ee=jt(ee,me,_e).filtered),Ft[z]={n:ee.length,mean:st(ee),median:Fs(ee),std:ct(ee),min:Math.min(...ee),max:Math.max(...ee),quartiles:$t(ee),values:ee}})}}const qt=Math.max(5,Math.ceil(1+3.322*Math.log10(q))),Lt=Wt/qt,_t=[];let Kt=0;for(let M=0;M<qt;M++){const oe=rt+M*Lt,z=rt+(M+1)*Lt,fe=h.filter(ke=>ke>=oe&&(M===qt-1?ke<=z:ke<z)).length;Kt+=fe;const ee=(oe+z)/2;_t.push({bin:`${oe.toFixed(2)}-${z.toFixed(2)}`,binMiddle:ee,binStart:oe,binEnd:z,frequency:fe,relativeFrequency:fe/q,cumulativeFrequency:Kt/q})}const rs=pt(h),s=at(h),I=oi(t.data,l),k=$t(I)[0],_=$t(I)[2],Se=_-k,Y=k-1.5*Se,E=_+1.5*Se,R=I.filter(M=>M<Y||M>E),ge={min:A&&I.find(M=>M>=Y)||rt,q1:ft[0],median:ft[1],q3:ft[2],max:A&&[...I].reverse().find(M=>M<=E)||It,outliers:A?R:[]};i[l.id]={column:l,statistics:{n:q,missing:Je,mean:kt,median:Xt,modeResult:Zt,variance:es,standardDeviation:ts,min:rt,max:It,range:Wt,quartiles:ft,iqr:ss,coefficientOfVariation:is,standardError:as,confidenceInterval:ns,percentiles:zt,...v?{skewness:Bt,kurtosis:Et}:{},...K?{isNormal:Dt}:{},...se?{geometricMean:At,harmonicMean:Rt,trimmedMean:Pt,mad:Ot}:{},...L?{outliersRemoved:re,originalN:J}:{}},groupStatistics:Ft,histogramData:_t,densityData:rs,qqPlotData:s,boxPlotData:ge}}),We(i),localStorage.setItem("descriptive_analysis_results",JSON.stringify(i)),Ge(!1)}catch(r){f(`Error analyzing data: ${r instanceof Error?r.message:String(r)}`),Ge(!1)}},1e3)},be=()=>{if(Object.keys(T).length===0)return;let r=`Variable,Statistic,Value
`;Object.entries(T).forEach(([,h])=>{var J;const p=h.statistics;r+=`${h.column.name},Sample Size,${p.n}
`,p.outliersRemoved!==void 0&&(r+=`${h.column.name},Original Sample Size,${p.originalN}
`,r+=`${h.column.name},Outliers Removed,${p.outliersRemoved}
`),r+=`${h.column.name},Missing Values,${p.missing}
`,r+=`${h.column.name},Mean,${p.mean.toFixed(4)}
`,r+=`${h.column.name},Median,${p.median.toFixed(4)}
`,r+=`${h.column.name},Mode,${((J=p.modeResult.mode)==null?void 0:J.toFixed(4))||"N/A"}
`,r+=`${h.column.name},Standard Deviation,${p.standardDeviation.toFixed(4)}
`,r+=`${h.column.name},Variance,${p.variance.toFixed(4)}
`,r+=`${h.column.name},Min,${p.min.toFixed(4)}
`,r+=`${h.column.name},Max,${p.max.toFixed(4)}
`,r+=`${h.column.name},Range,${p.range.toFixed(4)}
`,r+=`${h.column.name},Q1,${p.quartiles[0].toFixed(4)}
`,r+=`${h.column.name},Q3,${p.quartiles[2].toFixed(4)}
`,r+=`${h.column.name},IQR,${p.iqr.toFixed(4)}
`,p.coefficientOfVariation!==void 0&&(r+=`${h.column.name},CV %,${p.coefficientOfVariation.toFixed(2)}
`),p.geometricMean!==void 0&&(r+=`${h.column.name},Geometric Mean,${p.geometricMean.toFixed(4)}
`),p.harmonicMean!==void 0&&(r+=`${h.column.name},Harmonic Mean,${p.harmonicMean.toFixed(4)}
`),p.trimmedMean!==void 0&&(r+=`${h.column.name},Trimmed Mean (5%),${p.trimmedMean.toFixed(4)}
`),p.mad!==void 0&&(r+=`${h.column.name},MAD,${p.mad.toFixed(4)}
`),p.skewness!==void 0&&(r+=`${h.column.name},Skewness,${p.skewness.toFixed(4)}
`),p.kurtosis!==void 0&&(r+=`${h.column.name},Kurtosis,${p.kurtosis.toFixed(4)}
`),p.isNormal&&(r+=`${h.column.name},Normality p-value,${p.isNormal.pValue.toFixed(4)}
`),h.groupStatistics&&(r+=`
`,r+=`${h.column.name} - Group Statistics
`,r+=`Group,N,Mean,Median,Std Dev,Min,Max
`,Object.entries(h.groupStatistics).forEach(([re,q])=>{r+=`${re},${q.n},${q.mean.toFixed(4)},`,r+=`${q.median.toFixed(4)},${q.std.toFixed(4)},`,r+=`${q.min.toFixed(4)},${q.max.toFixed(4)}
`})),r+=`
`});const i=new Blob([r],{type:"text/csv"}),l=window.URL.createObjectURL(i),S=document.createElement("a");S.href=l,S.download="descriptive_statistics.csv",S.click(),window.URL.revokeObjectURL(l)},Qe=()=>!D||m.length<2?[]:["Mean","Std Dev","CV%","Skewness","Kurtosis"].map(l=>{const S={metric:l};return m.forEach(h=>{const p=T[h];if(p)switch(l){case"Mean":S[p.column.name]=p.statistics.mean;break;case"Std Dev":S[p.column.name]=p.statistics.standardDeviation;break;case"CV%":S[p.column.name]=p.statistics.coefficientOfVariation||0;break;case"Skewness":S[p.column.name]=Math.abs(p.statistics.skewness||0);break;case"Kurtosis":S[p.column.name]=p.statistics.kurtosis||0;break}}),S}),Me=r=>{const i=T[r];return!i||!i.groupStatistics?[]:Object.entries(i.groupStatistics).map(([l,S])=>({group:l,mean:S.mean,median:S.median,std:S.std,n:S.n}))};return e.jsxs(x,{p:3,children:[e.jsxs(x,{display:"flex",alignItems:"center",mb:3,children:[e.jsx(Rs,{sx:{fontSize:32,mr:2,color:n.palette.primary.main}}),e.jsx(a,{variant:"h4",component:"h1",children:"Descriptive Statistics Analysis"}),e.jsxs(x,{ml:"auto",children:[e.jsxs($i,{variant:"outlined",size:"small",children:[e.jsx(de,{title:"Grid View",children:e.jsx(Te,{onClick:()=>G("grid"),color:ie==="grid"?"primary":"inherit",children:e.jsx(_s,{})})}),e.jsx(de,{title:"List View",children:e.jsx(Te,{onClick:()=>G("list"),color:ie==="list"?"primary":"inherit",children:e.jsx(Ks,{})})})]}),e.jsx(de,{title:"Help",children:e.jsx(Ps,{sx:{ml:1},onClick:()=>window.location.href="/app#video-tutorials",children:e.jsx(Os,{})})})]})]}),e.jsx(ys,{in:!0,timeout:500,children:e.jsxs(H,{elevation:3,sx:{p:3,mb:3,borderRadius:2},children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(xi,{sx:{mr:1}}),"Data Selection"]}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{fullWidth:!0,margin:"normal",variant:"outlined",children:[e.jsx(Pe,{id:"dataset-select-label",children:"Dataset"}),e.jsx(Oe,{labelId:"dataset-select-label",id:"dataset-select",value:j,label:"Dataset",onChange:Z,disabled:y.length===0,children:y.length===0?e.jsx(B,{value:"",disabled:!0,children:"No datasets available"}):y.map(r=>e.jsx(B,{value:r.id,children:e.jsxs(x,{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",children:[e.jsx("span",{children:r.name}),e.jsx(Ae,{label:`${r.data.length} rows`,size:"small"})]})},r.id))})]})}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{fullWidth:!0,margin:"normal",variant:"outlined",children:[e.jsx(Pe,{id:"column-select-label",children:"Numeric Variables"}),e.jsx(Oe,{labelId:"column-select-label",id:"column-select",multiple:!0,value:m,label:"Numeric Variables",onChange:gt,disabled:Ce.length===0,renderValue:r=>e.jsx(x,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:r.map(i=>{var l;return e.jsx(Ae,{label:((l=Ce.find(S=>S.id===i))==null?void 0:l.name)||i,size:"small"},i)})}),children:Ce.length===0?e.jsx(B,{value:"",disabled:!0,children:"No numeric variables available"}):et.map(r=>e.jsxs(B,{value:r.id,children:[e.jsx(js,{checked:m.indexOf(r.id)>-1}),e.jsx(Qt,{primary:r.name})]},r.id))})]})})]}),e.jsxs(Qs,{expanded:F==="options",onChange:(r,i)=>P(i?"options":!1),sx:{mt:3},children:[e.jsx(Gs,{expandIcon:e.jsx(Hs,{}),children:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(ws,{sx:{mr:1}}),"Analysis Options"]})}),e.jsx(Us,{children:e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:v,onChange:r=>X(r.target.checked),color:"primary"}),label:"Distribution Shape"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:A,onChange:r=>ye(r.target.checked),color:"primary"}),label:"Outlier Detection"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:K,onChange:r=>O(r.target.checked),color:"primary"}),label:"Normality Test"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:N,onChange:r=>te(r.target.checked),color:"primary"}),label:"Box Plot"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:W,onChange:r=>ce(r.target.checked),color:"primary"}),label:"Confidence Intervals"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:D,onChange:r=>{pe(r.target.checked),r.target.checked||ue("")},color:"primary"}),label:e.jsxs(x,{display:"flex",alignItems:"center",children:[e.jsx(ti,{sx:{mr:.5,fontSize:20}}),"Comparison Mode"]})})}),e.jsxs(c,{item:!0,xs:12,sm:6,md:3,children:[e.jsx(ae,{control:e.jsx(Fe,{checked:se,onChange:r=>ve(r.target.checked),color:"secondary"}),label:"Advanced Statistics"}),se&&e.jsx(a,{variant:"caption",color:"text.secondary",display:"block",children:"Includes: Geometric mean, Harmonic mean, Trimmed mean, MAD"})]}),e.jsxs(c,{item:!0,xs:12,sm:6,md:3,children:[e.jsx(ae,{control:e.jsx(Fe,{checked:L,onChange:r=>Ze(r.target.checked),color:"secondary"}),label:e.jsxs(x,{display:"flex",alignItems:"center",children:[e.jsx(si,{sx:{mr:.5,fontSize:20}}),"Filter Outliers"]})}),L&&e.jsx(a,{variant:"caption",color:"text.secondary",display:"block",children:"Removes outliers before analysis"})]}),W&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(qe,{fullWidth:!0,size:"small",children:[e.jsx(Pe,{children:"Confidence Level"}),e.jsxs(Oe,{value:Q,onChange:r=>xe(r.target.value),label:"Confidence Level",children:[e.jsx(B,{value:.9,children:"90%"}),e.jsx(B,{value:.95,children:"95%"}),e.jsx(B,{value:.99,children:"99%"})]})]})}),D&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(qe,{fullWidth:!0,size:"small",children:[e.jsx(Pe,{children:"Grouping Variable"}),e.jsx(Oe,{value:g,onChange:r=>ue(r.target.value),label:"Grouping Variable",disabled:we.length===0,children:we.length===0?e.jsx(B,{value:"",disabled:!0,children:"No categorical variables available"}):we.map(r=>e.jsx(B,{value:r.id,children:r.name},r.id))})]})}),L&&e.jsxs(e.Fragment,{children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(qe,{component:"fieldset",children:[e.jsx(hi,{component:"legend",sx:{fontSize:"0.875rem"},children:"Outlier Method"}),e.jsxs(mi,{value:me,onChange:r=>Le(r.target.value),row:!0,children:[e.jsx(ae,{value:"iqr",control:e.jsx(vs,{size:"small"}),label:"IQR"}),e.jsx(ae,{value:"zscore",control:e.jsx(vs,{size:"small"}),label:"Z-Score"})]})]})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Ti,{label:me==="iqr"?"IQR Multiplier":"Z-Score Threshold",type:"number",size:"small",fullWidth:!0,value:_e,onChange:r=>Ve(parseFloat(r.target.value)||1.5),inputProps:{min:.1,max:5,step:.1},helperText:me==="iqr"?"Default: 1.5":"Default: 3"})})]})]})})]}),e.jsxs(x,{mt:3,display:"flex",gap:2,flexWrap:"wrap",children:[e.jsx(Te,{variant:"contained",color:"primary",size:"large",startIcon:e.jsx(Ws,{}),onClick:bt,disabled:Ke||m.length===0||D&&!g,sx:{minWidth:200},children:Ke?e.jsx(Jt,{size:24,color:"inherit"}):"Calculate Statistics"}),e.jsx(Te,{variant:"outlined",color:"secondary",startIcon:e.jsx(Bs,{}),onClick:()=>{We({}),localStorage.removeItem("descriptive_analysis_results")},disabled:Object.keys(T).length===0,children:"Clear Results"}),Object.keys(T).length>0&&e.jsx(Te,{variant:"outlined",startIcon:e.jsx(ui,{}),onClick:be,children:"Export CSV"})]})]})}),o&&e.jsx(gi,{in:!0,children:e.jsx(tt,{severity:"error",sx:{mb:3},onClose:()=>f(null),children:o})}),Object.keys(T).length>0&&!Ke&&e.jsx(ys,{in:!0,timeout:700,children:e.jsxs(x,{children:[D&&m.length>1&&!g&&e.jsxs(H,{elevation:3,sx:{p:3,mb:3,borderRadius:2},children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Ni,{sx:{mr:1}}),"Variable Comparison"]}),e.jsxs(c,{container:!0,spacing:3,children:[Qe().length>0&&e.jsx(c,{item:!0,xs:12,md:6,children:e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(la,{data:Qe(),children:[e.jsx(ca,{}),e.jsx(da,{dataKey:"metric"}),e.jsx(xa,{}),m.map((r,i)=>{var S;const l=(S=T[r])==null?void 0:S.column;return l?e.jsx(ha,{name:l.name,dataKey:l.name,stroke:n.palette[["primary","secondary","success","warning"][i%4]].main,fill:n.palette[["primary","secondary","success","warning"][i%4]].main,fillOpacity:.3},r):null}),e.jsx(ut,{})]})})})}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsx(dt,{children:e.jsxs(xt,{size:"small",children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{children:"Variable"}),e.jsx(d,{align:"right",children:"Mean"}),e.jsx(d,{align:"right",children:"Std Dev"}),e.jsx(d,{align:"right",children:"CV%"}),e.jsx(d,{align:"right",children:"Normal?"}),L&&e.jsx(d,{align:"right",children:"Outliers"})]})}),e.jsx(mt,{children:m.map(r=>{var l;const i=T[r];return i?e.jsxs(V,{children:[e.jsx(d,{children:i.column.name}),e.jsx(d,{align:"right",children:i.statistics.mean.toFixed(2)}),e.jsx(d,{align:"right",children:i.statistics.standardDeviation.toFixed(2)}),e.jsxs(d,{align:"right",children:[(l=i.statistics.coefficientOfVariation)==null?void 0:l.toFixed(1),"%"]}),e.jsx(d,{align:"right",children:i.statistics.isNormal?i.statistics.isNormal.isNormal?e.jsx(os,{color:"success",fontSize:"small"}):e.jsx(Gt,{color:"warning",fontSize:"small"}):"-"}),L&&e.jsx(d,{align:"right",children:i.statistics.outliersRemoved||0})]},r):null})})]})})})]})]}),e.jsxs(H,{elevation:3,sx:{borderRadius:2,overflow:"hidden"},children:[e.jsxs(Yt,{value:C,onChange:(r,i)=>u(i),indicatorColor:"primary",textColor:"primary",variant:"fullWidth",children:[e.jsx(je,{label:"Summary",icon:e.jsx(Nt,{}),iconPosition:"start"}),e.jsx(je,{label:"Detailed Statistics",icon:e.jsx(Ht,{}),iconPosition:"start"}),e.jsx(je,{label:"Visualizations",icon:e.jsx(ii,{}),iconPosition:"start"}),D&&g&&e.jsx(je,{label:"Group Comparison",icon:e.jsx(ti,{}),iconPosition:"start"})]}),e.jsxs(x,{p:3,children:[C===0&&e.jsx(c,{container:!0,spacing:3,children:Object.entries(T).map(([r,i])=>{var l,S,h,p,J,re,q;return e.jsxs(c,{item:!0,xs:12,children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Mt,{sx:{mr:1}}),i.column.name,L&&i.statistics.outliersRemoved!==void 0&&e.jsx(Ae,{label:`${i.statistics.outliersRemoved} outliers removed`,size:"small",color:"warning",sx:{ml:2}})]}),ie==="grid"?e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(hs,{title:"Mean",value:i.statistics.mean.toFixed(2),subtitle:W&&i.statistics.confidenceInterval?`CI: [${i.statistics.confidenceInterval[0].toFixed(2)}, ${i.statistics.confidenceInterval[1].toFixed(2)}]`:void 0,icon:e.jsx(zs,{}),color:"primary"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(hs,{title:"Standard Deviation",value:i.statistics.standardDeviation.toFixed(2),subtitle:`CV: ${(l=i.statistics.coefficientOfVariation)==null?void 0:l.toFixed(1)}%`,icon:e.jsx(Mt,{}),color:"secondary"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(hs,{title:"Sample Size",value:i.statistics.n,subtitle:i.statistics.missing>0?`${i.statistics.missing} missing`:"No missing",icon:e.jsx(Ht,{}),color:"success"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(hs,{title:"Distribution",value:(S=i.statistics.isNormal)!=null&&S.isNormal?"Normal":"Non-normal",subtitle:i.statistics.isNormal?`p = ${i.statistics.isNormal.pValue.toFixed(3)}`:"Not tested",icon:(h=i.statistics.isNormal)!=null&&h.isNormal?e.jsx(os,{}):e.jsx(Gt,{}),color:(p=i.statistics.isNormal)!=null&&p.isNormal?"success":"warning"})})]}):e.jsxs(Vi,{children:[e.jsxs(ls,{children:[e.jsx(cs,{children:e.jsx(zs,{color:"primary"})}),e.jsx(Qt,{primary:"Mean",secondary:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",component:"span",children:i.statistics.mean.toFixed(2)}),W&&i.statistics.confidenceInterval&&e.jsxs(a,{variant:"body2",color:"text.secondary",children:["CI: [",i.statistics.confidenceInterval[0].toFixed(2),", ",i.statistics.confidenceInterval[1].toFixed(2),"]"]})]})})]}),e.jsx(ds,{}),e.jsxs(ls,{children:[e.jsx(cs,{children:e.jsx(Mt,{color:"secondary"})}),e.jsx(Qt,{primary:"Standard Deviation",secondary:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",component:"span",children:i.statistics.standardDeviation.toFixed(2)}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["CV: ",(J=i.statistics.coefficientOfVariation)==null?void 0:J.toFixed(1),"%"]})]})})]}),e.jsx(ds,{}),e.jsxs(ls,{children:[e.jsx(cs,{children:e.jsx(Ht,{color:"success"})}),e.jsx(Qt,{primary:"Sample Size",secondary:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",component:"span",children:i.statistics.n}),e.jsx(a,{variant:"body2",color:"text.secondary",children:i.statistics.missing>0?`${i.statistics.missing} missing`:"No missing"})]})})]}),e.jsx(ds,{}),e.jsxs(ls,{children:[e.jsx(cs,{children:(re=i.statistics.isNormal)!=null&&re.isNormal?e.jsx(os,{color:"success"}):e.jsx(Gt,{color:"warning"})}),e.jsx(Qt,{primary:"Distribution",secondary:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",component:"span",children:(q=i.statistics.isNormal)!=null&&q.isNormal?"Normal":"Non-normal"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:i.statistics.isNormal?`p = ${i.statistics.isNormal.pValue.toFixed(3)}`:"Not tested"})]})})]})]}),i.boxPlotData.outliers.length>0&&!L&&e.jsx(tt,{severity:"warning",sx:{mt:2},children:e.jsxs(a,{variant:"body2",children:[e.jsxs("strong",{children:[i.boxPlotData.outliers.length," outliers detected"]})," (included in analysis)"]})}),e.jsx(ds,{sx:{my:3}})]},r)})}),C===1&&e.jsx(x,{children:Object.entries(T).map(([r,i])=>{var l,S;return e.jsxs(H,{elevation:2,sx:{p:3,mb:3,bgcolor:"background.paper"},children:[e.jsxs(x,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[e.jsxs(a,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Mt,{color:"primary"}),i.column.name," - Detailed Statistics"]}),L&&i.statistics.outliersRemoved!==void 0&&e.jsx(Ae,{label:`${i.statistics.outliersRemoved} outliers removed`,size:"small",color:"warning",variant:"outlined"})]}),e.jsxs(x,{sx:{mb:3},children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Ct,{fontSize:"small"}),"Sample Information"]}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Sample Size",value:i.statistics.n.toLocaleString(),description:"Valid observations",color:"primary",variant:"outlined",icon:e.jsx(Wi,{})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Missing Values",value:i.statistics.missing.toLocaleString(),description:`${(i.statistics.missing/(i.statistics.n+i.statistics.missing)*100).toFixed(1)}% missing`,color:i.statistics.missing>0?"warning":"success",variant:"outlined",icon:e.jsx(zi,{})})}),L&&i.statistics.originalN&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Original Sample",value:i.statistics.originalN.toLocaleString(),description:"Before outlier removal",color:"info",variant:"outlined",icon:e.jsx(si,{})})}),L&&i.statistics.outliersRemoved!==void 0&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Outliers Removed",value:i.statistics.outliersRemoved.toLocaleString(),description:`${(i.statistics.outliersRemoved/(i.statistics.originalN||i.statistics.n)*100).toFixed(1)}% of data`,color:"warning",variant:"outlined",icon:e.jsx(ji,{})})})]})]}),e.jsxs(x,{sx:{mb:3},children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Ai,{fontSize:"small"}),"Central Tendency"]}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Mean",value:i.statistics.mean.toFixed(2),description:W&&i.statistics.confidenceInterval?`CI: [${i.statistics.confidenceInterval[0].toFixed(2)}, ${i.statistics.confidenceInterval[1].toFixed(2)}]`:"Arithmetic average",color:"primary",variant:"outlined",icon:e.jsx(zs,{})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Median",value:i.statistics.median.toFixed(2),description:"Middle value (50th percentile)",color:"secondary",variant:"outlined",icon:e.jsx(ai,{})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Mode",value:i.statistics.modeResult.mode!==null?i.statistics.modeResult.mode.toFixed(2):"N/A",description:i.statistics.modeResult.message||"Most frequent value",color:"info",variant:"outlined",icon:e.jsx(it,{})})}),se&&i.statistics.trimmedMean&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Trimmed Mean",value:i.statistics.trimmedMean.toFixed(2),description:"5% trimmed mean",color:"secondary",variant:"outlined",icon:e.jsx(Ri,{})})})]})]}),se&&e.jsxs(x,{sx:{mb:3},children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Ht,{fontSize:"small"}),"Advanced Central Tendency"]}),e.jsxs(c,{container:!0,spacing:2,children:[i.statistics.geometricMean&&e.jsx(c,{item:!0,xs:12,sm:6,md:4,children:e.jsx($e,{title:"Geometric Mean",value:i.statistics.geometricMean.toFixed(2),description:"For positive values only",color:"info",variant:"outlined",icon:e.jsx(Ws,{})})}),i.statistics.harmonicMean&&e.jsx(c,{item:!0,xs:12,sm:6,md:4,children:e.jsx($e,{title:"Harmonic Mean",value:i.statistics.harmonicMean.toFixed(2),description:"For positive values only",color:"info",variant:"outlined",icon:e.jsx(Ws,{})})})]})]}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,lg:6,children:e.jsxs(H,{elevation:0,variant:"outlined",sx:{p:2,height:"fit-content"},children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Mt,{fontSize:"small"}),"Variability & Dispersion"]}),e.jsx(dt,{children:e.jsxs(xt,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{children:"Statistic"}),e.jsx(d,{align:"right",children:"Value"})]})}),e.jsxs(mt,{children:[e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Minimum"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.min.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Maximum"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.max.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Range"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.range.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Variance"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.variance.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Standard Deviation"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.standardDeviation.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Standard Error"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:((l=i.statistics.standardError)==null?void 0:l.toFixed(2))||"N/A"})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Coefficient of Variation"}),e.jsxs(d,{align:"right",sx:{fontFamily:"monospace"},children:[(S=i.statistics.coefficientOfVariation)==null?void 0:S.toFixed(2),"%"]})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"IQR"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.iqr.toFixed(2)})]}),se&&i.statistics.mad&&e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Median Absolute Deviation"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.mad.toFixed(2)})]})]})]})})]})}),e.jsx(c,{item:!0,xs:12,lg:6,children:e.jsxs(H,{elevation:0,variant:"outlined",sx:{p:2,height:"fit-content"},children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(ai,{fontSize:"small"}),"Position & Percentiles"]}),e.jsx(dt,{children:e.jsxs(xt,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{children:"Percentile"}),e.jsx(d,{align:"right",children:"Value"})]})}),e.jsxs(mt,{children:[se&&i.statistics.percentiles&&e.jsxs(e.Fragment,{children:[e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"5th Percentile"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.percentiles.p5.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"10th Percentile"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.percentiles.p10.toFixed(2)})]})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Q1 (25th Percentile)"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.quartiles[0].toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Q2 (50th Percentile)"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.quartiles[1].toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"Q3 (75th Percentile)"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.quartiles[2].toFixed(2)})]}),se&&i.statistics.percentiles&&e.jsxs(e.Fragment,{children:[e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"90th Percentile"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.percentiles.p90.toFixed(2)})]}),e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:500},children:"95th Percentile"}),e.jsx(d,{align:"right",sx:{fontFamily:"monospace"},children:i.statistics.percentiles.p95.toFixed(2)})]})]})]})]})})]})})]}),(v||K||W)&&e.jsxs(x,{sx:{mt:3},children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Es,{fontSize:"small"}),"Distribution Properties"]}),e.jsxs(c,{container:!0,spacing:2,children:[v&&i.statistics.skewness!==void 0&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Skewness",value:i.statistics.skewness.toFixed(2),description:Math.abs(i.statistics.skewness)<.5?"Symmetric":i.statistics.skewness>0?"Right-skewed":"Left-skewed",color:Math.abs(i.statistics.skewness)<.5?"success":"warning",variant:"outlined",icon:e.jsx(pi,{})})}),v&&i.statistics.kurtosis!==void 0&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Kurtosis",value:i.statistics.kurtosis.toFixed(2),description:Math.abs(i.statistics.kurtosis-3)<.5?"Mesokurtic":i.statistics.kurtosis>3?"Leptokurtic":"Platykurtic",color:Math.abs(i.statistics.kurtosis-3)<.5?"success":"info",variant:"outlined",icon:e.jsx(it,{})})}),K&&i.statistics.isNormal&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:"Normality Test",value:i.statistics.isNormal.isNormal?"Normal":"Non-normal",description:`p = ${i.statistics.isNormal.pValue.toFixed(3)}`,color:i.statistics.isNormal.isNormal?"success":"warning",variant:"outlined",icon:i.statistics.isNormal.isNormal?e.jsx(os,{}):e.jsx(Gt,{})})}),W&&i.statistics.confidenceInterval&&e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx($e,{title:`${(Q*100).toFixed(0)}% CI for Mean`,value:`[${i.statistics.confidenceInterval[0].toFixed(2)}, ${i.statistics.confidenceInterval[1].toFixed(2)}]`,description:"Confidence interval",color:"info",variant:"outlined",icon:e.jsx(Nt,{})})})]})]})]},r)})}),C===2&&e.jsxs(x,{children:[e.jsx(x,{mb:3,children:e.jsxs(Ms,{value:he,exclusive:!0,onChange:(r,i)=>i&&De(i),size:"small",children:[e.jsx(Ne,{value:"histogram",children:e.jsx(de,{title:"Histogram",children:e.jsx("span",{children:e.jsx(it,{})})})}),e.jsx(Ne,{value:"density",children:e.jsx(de,{title:"Density Plot",children:e.jsx("span",{children:e.jsx(Es,{})})})}),e.jsx(Ne,{value:"cumulative",children:e.jsx(de,{title:"Cumulative Distribution",children:e.jsx("span",{children:e.jsx(Mt,{})})})}),e.jsx(Ne,{value:"qqplot",children:e.jsx(de,{title:"Q-Q Plot",children:e.jsx("span",{children:e.jsx(Pi,{})})})}),e.jsx(Ne,{value:"boxplot",children:e.jsx(de,{title:"Box Plot",children:e.jsx("span",{children:e.jsx(ii,{})})})})]})}),Object.entries(T).map(([r,i])=>e.jsxs(H,{elevation:1,sx:{p:2,mb:3},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,children:i.column.name}),he==="histogram"&&i.histogramData&&i.histogramData.length>0&&e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(Ci,{data:i.histogramData,margin:{top:10,right:30,left:20,bottom:70},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{dataKey:"binMiddle",tickFormatter:l=>l.toFixed(2),label:{value:i.column.name,position:"insideBottom",offset:-30}}),e.jsx(He,{yAxisId:"left",label:{value:"Frequency",angle:-90,position:"insideLeft"}}),e.jsx(He,{yAxisId:"right",orientation:"right",tickFormatter:l=>`${(l*100).toFixed(0)}%`,label:{value:"Cumulative %",angle:90,position:"insideRight"}}),e.jsx(Re,{formatter:(l,S)=>[S==="frequency"?l:`${(l*100).toFixed(1)}%`,S==="frequency"?"Frequency":"Cumulative %"],labelFormatter:l=>{const S=i.histogramData.find(h=>h.binMiddle===l);return S?`Range: ${S.bin}`:""}}),e.jsx(ut,{verticalAlign:"top",align:"center"}),e.jsx(St,{yAxisId:"left",dataKey:"frequency",fill:n.palette.primary.main,name:"Frequency",barSize:Math.max(20,Math.min(60,800/i.histogramData.length)),opacity:.8,children:i.histogramData.map((l,S)=>e.jsx(Ut,{fill:l.binMiddle<i.statistics.mean?n.palette.primary.light:n.palette.primary.main},`cell-${S}`))}),e.jsx(bs,{yAxisId:"right",type:"monotone",dataKey:"cumulativeFrequency",stroke:n.palette.secondary.main,strokeWidth:2,name:"Cumulative %",dot:!1}),e.jsx(vt,{x:i.statistics.mean,stroke:"red",strokeDasharray:"3 3",yAxisId:"left",children:e.jsx(Tt,{value:"Mean",position:"top",fill:"red"})}),e.jsx(vt,{x:i.statistics.median,stroke:"green",strokeDasharray:"3 3",yAxisId:"left",children:e.jsx(Tt,{value:"Median",position:"top",fill:"green"})})]})},`histogram-${r}`)}),he==="density"&&i.densityData&&i.densityData.length>0&&e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(ma,{data:i.densityData,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{dataKey:"x",type:"number",domain:["dataMin","dataMax"],tickFormatter:l=>l.toFixed(2),label:{value:i.column.name,position:"insideBottom",offset:-10}}),e.jsx(He,{yAxisId:"left",label:{value:"Density",angle:-90,position:"insideLeft"}}),e.jsx(Re,{formatter:l=>l.toFixed(4),labelFormatter:l=>`x: ${l.toFixed(2)}`}),e.jsx(ua,{type:"monotone",dataKey:"y",yAxisId:"left",stroke:n.palette.primary.main,fill:n.palette.primary.light,fillOpacity:.6,isAnimationActive:!0,name:"Density"}),e.jsx(vt,{x:i.statistics.mean,stroke:"red",strokeDasharray:"3 3",yAxisId:"left",children:e.jsx(Tt,{value:"Mean",position:"top",fill:"red"})})]})},`density-${r}`)}),he==="cumulative"&&i.histogramData&&i.histogramData.length>0&&e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(ga,{data:i.histogramData,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{dataKey:"binMiddle",tickFormatter:l=>l.toFixed(2),label:{value:i.column.name,position:"insideBottom",offset:-10}}),e.jsx(He,{yAxisId:"left",tickFormatter:l=>`${(l*100).toFixed(0)}%`,label:{value:"Cumulative %",angle:-90,position:"insideLeft"}}),e.jsx(Re,{formatter:l=>`${(l*100).toFixed(1)}%`,labelFormatter:l=>`Value: ${l.toFixed(2)}`}),e.jsx(bs,{type:"stepAfter",dataKey:"cumulativeFrequency",yAxisId:"left",stroke:n.palette.primary.main,strokeWidth:2,dot:!0}),e.jsx(vt,{x:i.statistics.mean,stroke:"red",strokeDasharray:"3 3",yAxisId:"left",children:e.jsx(Tt,{value:"Mean",position:"top",fill:"red"})}),e.jsx(vt,{x:i.statistics.median,stroke:"green",strokeDasharray:"3 3",yAxisId:"left",children:e.jsx(Tt,{value:"Median",position:"insideTopRight",fill:"green"})}),e.jsx(vt,{y:.5,stroke:"blue",strokeDasharray:"3 3",yAxisId:"left",children:e.jsx(Tt,{value:"50%",position:"right",fill:"blue"})})]})},`cumulative-${r}`)}),he==="qqplot"&&i.qqPlotData&&i.qqPlotData.length>0&&e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(Si,{margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{type:"number",dataKey:"theoretical",name:"Theoretical Quantiles",label:{value:"Theoretical Quantiles",position:"insideBottom",offset:-10}}),e.jsx(He,{yAxisId:"left",type:"number",dataKey:"sample",name:"Sample Quantiles",label:{value:"Sample Quantiles",angle:-90,position:"insideLeft"}}),e.jsx(Re,{cursor:{strokeDasharray:"3 3"}}),e.jsx(ki,{name:"Q-Q Plot",data:i.qqPlotData,yAxisId:"left",fill:n.palette.primary.main}),i.qqPlotData.length>1&&e.jsx(bs,{yAxisId:"left",type:"monotone",data:[{theoretical:Math.min(...i.qqPlotData.map(l=>l.theoretical)),sample:Math.min(...i.qqPlotData.map(l=>l.sample))},{theoretical:Math.max(...i.qqPlotData.map(l=>l.theoretical)),sample:Math.max(...i.qqPlotData.map(l=>l.sample))}],dataKey:"sample",stroke:"red",strokeDasharray:"3 3",dot:!1,activeDot:!1,name:"Reference Line"})]})})}),he==="boxplot"&&N&&e.jsx(x,{height:200,children:e.jsx(b.Suspense,{fallback:e.jsx(x,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(Jt,{size:20})}),children:e.jsx(ja,{columnId:r,boxPlotData:i.boxPlotData,columnName:i.column.name,includeOutliers:A})})})]},r))]}),C===3&&D&&g&&e.jsx(x,{children:Object.entries(T).map(([r,i])=>{var l;return e.jsxs(H,{elevation:1,sx:{p:2,mb:3},children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,children:[i.column.name," by ",(l=we.find(S=>S.id===g))==null?void 0:l.name]}),i.groupStatistics&&e.jsxs(e.Fragment,{children:[e.jsx(x,{height:400,mb:3,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(qs,{data:Me(r),margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{dataKey:"group",angle:-45,textAnchor:"end",height:80}),e.jsx(He,{}),e.jsx(Re,{}),e.jsx(ut,{}),e.jsx(St,{dataKey:"mean",fill:n.palette.primary.main,name:"Mean"}),e.jsx(St,{dataKey:"median",fill:n.palette.secondary.main,name:"Median"})]})})}),e.jsx(dt,{children:e.jsxs(xt,{size:"small",children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{children:"Group"}),e.jsx(d,{align:"right",children:"N"}),e.jsx(d,{align:"right",children:"Mean"}),e.jsx(d,{align:"right",children:"Median"}),e.jsx(d,{align:"right",children:"Std Dev"}),e.jsx(d,{align:"right",children:"Min"}),e.jsx(d,{align:"right",children:"Max"})]})}),e.jsx(mt,{children:Object.entries(i.groupStatistics).map(([S,h])=>e.jsxs(V,{children:[e.jsx(d,{children:S}),e.jsx(d,{align:"right",children:h.n}),e.jsx(d,{align:"right",children:h.mean.toFixed(2)}),e.jsx(d,{align:"right",children:h.median.toFixed(2)}),e.jsx(d,{align:"right",children:h.std.toFixed(2)}),e.jsx(d,{align:"right",children:h.min.toFixed(2)}),e.jsx(d,{align:"right",children:h.max.toFixed(2)})]},S))})]})})]})]},r)})})]})]})]})}),Ke&&e.jsxs(x,{children:[e.jsx(lt,{variant:"rectangular",height:400,sx:{mb:2}}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(lt,{variant:"rectangular",height:120})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(lt,{variant:"rectangular",height:120})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(lt,{variant:"rectangular",height:120})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(lt,{variant:"rectangular",height:120})})]})]})]})};function ci(y){const{children:t,value:w,index:n,...j}=y;return e.jsx("div",{role:"tabpanel",hidden:w!==n,id:`simple-tabpanel-${n}`,"aria-labelledby":`simple-tab-${n}`,...j,children:w===n&&e.jsx(x,{sx:{py:3},children:t})})}const ka=()=>{const{datasets:y,currentDataset:t,setCurrentDataset:w}=$s(),n=Vt(),j=Cs(n.breakpoints.down("md")),U=Cs(n.breakpoints.down("lg")),[m,ne]=b.useState((t==null?void 0:t.id)||""),[v,X]=b.useState([]),[A,ye]=b.useState(!0),[K,O]=b.useState(!0),[N,te]=b.useState(!0),[W,ce]=b.useState("bar"),[Q,xe]=b.useState("tabs"),[D,pe]=b.useState(0),[g,ue]=b.useState(!1),[he,De]=b.useState(null),[se,ve]=b.useState({});b.useEffect(()=>{const f=localStorage.getItem("frequency_tables_results");if(f)try{ve(JSON.parse(f))}catch(C){console.error("Error parsing saved frequency tables results:",C)}},[]),b.useEffect(()=>{j?xe("tabs"):U&&xe("stacked")},[j,U]);const L=(t==null?void 0:t.columns.filter(f=>f.type===Ue.CATEGORICAL||f.type===Ue.ORDINAL||f.type===Ue.BOOLEAN))||[],Ze=f=>{const C=f.target.value;ne(C),X([]),ve({}),localStorage.removeItem("frequency_tables_results");const u=y.find(F=>F.id===C);u&&w(u)},me=f=>{const C=f.target.value;X(typeof C=="string"?[C]:C),ve({}),localStorage.removeItem("frequency_tables_results")},Le=()=>{ve({}),localStorage.removeItem("frequency_tables_results")},_e=(f,C)=>{C!==null&&(xe(C),pe(0))},Ve=()=>{if(!t||v.length===0){De("Please select a dataset and at least one column to analyze.");return}ue(!0),De(null);try{const f=t.columns.filter(u=>v.includes(u.id)),C={};f.forEach(u=>{const F=t.data.map(Z=>String(Z[u.name]??"Missing")),P=Yi(F),ie=Xi(F);let G;N?G=Object.keys(P).sort((Z,gt)=>P[gt]-P[Z]):G=Zi(u,t.data);const T={},We={};let Ce=0,we=0;G.forEach(Z=>{Ce+=P[Z],we+=ie[Z],T[Z]=Ce,We[Z]=we});const et=G.map(Z=>({category:Z,name:Z,frequency:P[Z],percentage:ie[Z]*100,value:P[Z]}));C[u.id]={column:u,frequencies:P,percentages:ie,cumulativeFreq:T,cumulativePerc:We,total:F.length,sortedKeys:G,chartData:et}}),ve(C),localStorage.setItem("frequency_tables_results",JSON.stringify(C)),ue(!1)}catch(f){De(`Error analyzing data: ${f instanceof Error?f.message:String(f)}`),ue(!1)}},Ke=f=>{const C=[n.palette.primary.main,n.palette.secondary.main,n.palette.success.main,n.palette.error.main,n.palette.warning.main,n.palette.info.main,"#8884d8","#82ca9d","#ffc658","#8dd1e1","#a4de6c","#d0ed57","#83a6ed","#ff7c7c"],u=[];for(let F=0;F<f;F++)u.push(C[F%C.length]);return u},Ge=f=>e.jsx(dt,{component:H,variant:"outlined",sx:{maxHeight:j?300:400,borderRadius:2,"& .MuiTable-root":{minWidth:j?300:450}},children:e.jsxs(xt,{size:j?"small":"medium",stickyHeader:!0,children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Category"}),e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Frequency"}),A&&e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Percentage"}),K&&e.jsxs(e.Fragment,{children:[e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Cumulative Freq"}),A&&e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Cumulative %"})]})]})}),e.jsxs(mt,{children:[f.sortedKeys.map((C,u)=>e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:e.jsx(Ae,{label:C,size:"small",variant:"outlined",sx:{maxWidth:j?100:150}})}),e.jsx(d,{align:"right",sx:{fontWeight:500},children:f.frequencies[C].toLocaleString()}),A&&e.jsxs(d,{align:"right",children:[(f.percentages[C]*100).toFixed(2),"%"]}),K&&e.jsxs(e.Fragment,{children:[e.jsx(d,{align:"right",children:f.cumulativeFreq[C].toLocaleString()}),A&&e.jsxs(d,{align:"right",children:[(f.cumulativePerc[C]*100).toFixed(2),"%"]})]})]},C)),e.jsxs(V,{sx:{backgroundColor:n.palette.action.selected},children:[e.jsx(d,{sx:{fontWeight:700},children:"Total"}),e.jsx(d,{align:"right",sx:{fontWeight:700},children:f.total.toLocaleString()}),A&&e.jsx(d,{align:"right",sx:{fontWeight:700},children:"100.00%"}),K&&e.jsxs(e.Fragment,{children:[e.jsx(d,{align:"right"}),A&&e.jsx(d,{align:"right"})]})]})]})]})}),o=f=>e.jsx(H,{variant:"outlined",sx:{p:2,height:j?300:400,borderRadius:2,backgroundColor:n.palette.grey[50]},children:e.jsx(ze,{width:"100%",height:"100%",children:W==="bar"?e.jsxs(qs,{data:f.chartData,margin:{top:20,right:30,left:20,bottom:j?60:80},children:[e.jsx(Ye,{strokeDasharray:"3 3",stroke:n.palette.divider}),e.jsx(Xe,{dataKey:"category",angle:j?-90:-45,textAnchor:"end",height:j?60:80,interval:0,tick:{fontSize:j?10:12}}),e.jsx(He,{tick:{fontSize:j?10:12}}),e.jsx(Re,{contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:8},formatter:(C,u)=>[C,u==="frequency"?"Frequency":u],labelFormatter:C=>`Category: ${C}`}),e.jsx(ut,{}),e.jsx(St,{dataKey:"frequency",fill:n.palette.primary.main,name:"Frequency",radius:[4,4,0,0]})]}):e.jsxs(Ls,{children:[e.jsx(fs,{data:f.chartData,cx:"50%",cy:"50%",outerRadius:j?80:120,fill:"#8884d8",dataKey:"frequency",nameKey:"category",label:({name:C,percent:u})=>u>.05?`${j?"":C+": "}${(u*100).toFixed(1)}%`:"",labelLine:!1,children:f.chartData.map((C,u)=>e.jsx(Ut,{fill:Ke(f.chartData.length)[u]},`cell-${u}`))}),e.jsx(Re,{contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:8},formatter:(C,u,F)=>[C,`Frequency (${(C/f.total*100).toFixed(1)}%)`]}),e.jsx(ut,{})]})})});return e.jsxs(x,{sx:{p:{xs:2,md:3},maxWidth:1400,mx:"auto"},children:[e.jsxs(x,{sx:{mb:4},children:[e.jsx(a,{variant:"h4",gutterBottom:!0,sx:{fontWeight:600,color:n.palette.primary.main},children:"Frequency Analysis"}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Analyze categorical data distributions with frequency tables and visualizations"})]}),e.jsxs(Be,{elevation:2,sx:{mb:3},children:[e.jsx(Ss,{title:"Configuration",avatar:e.jsx(ws,{color:"primary"}),sx:{pb:1}}),e.jsxs(Ee,{children:[e.jsxs(c,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{fullWidth:!0,children:[e.jsx(Pe,{children:"Dataset"}),e.jsx(Oe,{value:m,label:"Dataset",onChange:Ze,disabled:y.length===0,children:y.length===0?e.jsx(B,{value:"",disabled:!0,children:"No datasets available"}):y.map(f=>e.jsx(B,{value:f.id,children:e.jsxs(x,{children:[e.jsx(a,{variant:"body2",children:f.name}),e.jsxs(a,{variant:"caption",color:"text.secondary",children:[f.data.length," rows"]})]})},f.id))})]})}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{fullWidth:!0,children:[e.jsx(Pe,{children:"Categorical Variables"}),e.jsx(Oe,{multiple:!0,value:v,label:"Categorical Variables",onChange:me,disabled:L.length===0,renderValue:f=>e.jsx(x,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:f.map(C=>{const u=L.find(F=>F.id===C);return e.jsx(Ae,{label:(u==null?void 0:u.name)||C,size:"small",color:"primary",variant:"outlined"},C)})}),children:L.length===0?e.jsx(B,{value:"",disabled:!0,children:"No categorical variables available"}):L.map(f=>e.jsxs(B,{value:f.id,children:[e.jsx(a,{variant:"body2",children:f.name}),e.jsxs(a,{variant:"caption",color:"text.secondary",sx:{ml:1},children:["(",f.type,")"]})]},f.id))})]})})]}),Object.keys(se).length>0&&e.jsxs(x,{sx:{mb:3},children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Display Mode"}),e.jsxs(Ms,{value:Q,exclusive:!0,onChange:_e,size:"small",sx:{mb:2},children:[e.jsxs(Ne,{value:"tabs","aria-label":"tabs view",children:[e.jsx(bi,{sx:{mr:1}}),!j&&"Tabs"]}),e.jsxs(Ne,{value:"stacked","aria-label":"stacked view",children:[e.jsx(Ks,{sx:{mr:1}}),!j&&"Stacked"]}),!j&&e.jsxs(Ne,{value:"side-by-side","aria-label":"side by side view",children:[e.jsx(_s,{sx:{mr:1}}),"Side by Side"]})]}),e.jsx(a,{variant:"caption",color:"text.secondary",display:"block",children:"Choose how to display the frequency table and chart"})]}),e.jsxs(Qs,{defaultExpanded:!1,children:[e.jsx(Gs,{expandIcon:e.jsx(Hs,{}),children:e.jsx(a,{variant:"subtitle1",sx:{fontWeight:500},children:"Analysis Options"})}),e.jsx(Us,{children:e.jsxs(c,{container:!0,spacing:3,children:[e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Table Options"}),e.jsxs(ps,{spacing:1,children:[e.jsx(ae,{control:e.jsx(js,{checked:A,onChange:f=>{ye(f.target.checked),Le()}}),label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:["Include Percentages",e.jsx(de,{title:"Show percentage distribution for each category",children:e.jsx(Ct,{fontSize:"small",color:"action"})})]})}),e.jsx(ae,{control:e.jsx(js,{checked:K,onChange:f=>{O(f.target.checked),Le()}}),label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:["Cumulative Distributions",e.jsx(de,{title:"Show running totals and cumulative percentages",children:e.jsx(Ct,{fontSize:"small",color:"action"})})]})}),e.jsx(ae,{control:e.jsx(js,{checked:N,onChange:f=>{te(f.target.checked),Le()}}),label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:["Sort by Frequency",e.jsx(de,{title:"Sort categories by frequency (descending) instead of alphabetically",children:e.jsx(Ct,{fontSize:"small",color:"action"})})]})})]})]}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{component:"fieldset",children:[e.jsx(hi,{component:"legend",sx:{fontWeight:500,mb:1},children:"Visualization Type"}),e.jsxs(mi,{value:W,onChange:f=>{ce(f.target.value),Le()},children:[e.jsx(ae,{value:"bar",control:e.jsx(vs,{}),label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(it,{fontSize:"small"}),"Bar Chart"]})}),e.jsx(ae,{value:"pie",control:e.jsx(vs,{}),label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ds,{fontSize:"small"}),"Pie Chart"]})})]})]})})]})})]}),e.jsxs(x,{sx:{mt:3,display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(Te,{variant:"contained",size:"large",startIcon:e.jsx(Nt,{}),onClick:Ve,disabled:g||v.length===0,sx:{px:4,borderRadius:2,textTransform:"none",fontWeight:600},children:g?"Analyzing...":"Generate Analysis"}),Object.keys(se).length>0&&e.jsx(Te,{variant:"outlined",size:"large",onClick:Le,sx:{px:3,borderRadius:2,textTransform:"none"},children:"Clear Results"})]})]})]}),g&&e.jsx(Be,{elevation:2,children:e.jsx(Ee,{children:e.jsxs(x,{display:"flex",flexDirection:"column",alignItems:"center",py:4,children:[e.jsx(Jt,{size:60,sx:{mb:2}}),e.jsx(a,{variant:"h6",color:"text.secondary",children:"Analyzing Data..."}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Processing frequency distributions"})]})})}),he&&e.jsx(tt,{severity:"error",sx:{mb:3,borderRadius:2},action:e.jsx(Te,{color:"inherit",size:"small",onClick:()=>De(null),children:"Dismiss"}),children:he}),Object.keys(se).length>0&&!g&&e.jsx(ps,{spacing:3,children:Object.entries(se).map(([f,C])=>e.jsxs(Be,{elevation:2,children:[e.jsx(Ss,{title:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ks,{color:"primary"}),e.jsx(a,{variant:"h6",children:C.column.name})]}),subheader:e.jsxs(a,{variant:"body2",color:"text.secondary",children:[C.sortedKeys.length," unique categories • ",C.total," total observations"]})}),e.jsx(Ee,{children:Q==="tabs"?e.jsxs(x,{children:[e.jsxs(Yt,{value:D,onChange:(u,F)=>pe(F),sx:{borderBottom:1,borderColor:"divider",mb:2},children:[e.jsx(je,{label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ks,{fontSize:"small"}),!j&&"Frequency Table"]})}),e.jsx(je,{label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[W==="bar"?e.jsx(it,{fontSize:"small"}):e.jsx(Ds,{fontSize:"small"}),!j&&`${W==="bar"?"Bar":"Pie"} Chart`]})})]}),e.jsx(ci,{value:D,index:0,children:Ge(C)}),e.jsx(ci,{value:D,index:1,children:o(C)})]}):Q==="stacked"?e.jsxs(ps,{spacing:4,children:[e.jsxs(x,{children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Frequency Distribution"}),Ge(C)]}),e.jsxs(x,{children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:W==="bar"?"Bar Chart":"Pie Chart"}),o(C)]})]}):e.jsxs(c,{container:!0,spacing:4,children:[e.jsxs(c,{item:!0,xs:12,lg:6,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Frequency Distribution"}),Ge(C)]}),e.jsxs(c,{item:!0,xs:12,lg:6,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:W==="bar"?"Bar Chart":"Pie Chart"}),o(C)]})]})})]},f))}),!g&&Object.keys(se).length===0&&v.length>0&&e.jsx(Be,{elevation:1,children:e.jsx(Ee,{children:e.jsxs(x,{textAlign:"center",py:6,children:[e.jsx(Nt,{sx:{fontSize:80,color:n.palette.grey[400],mb:2}}),e.jsx(a,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"Ready to Analyze"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:'Click "Generate Analysis" to create frequency tables and visualizations'})]})})})]})},Ia=y=>{const t=Math.PI/180,{cx:w,cy:n,midAngle:j,innerRadius:U,outerRadius:m,startAngle:ne,endAngle:v,fill:X,payload:A,percent:ye,value:K}=y,O=Math.sin(-t*j),N=Math.cos(-t*j),te=w+(m+10)*N,W=n+(m+10)*O,ce=w+(m+30)*N,Q=n+(m+30)*O,xe=ce+(N>=0?1:-1)*22,D=Q,pe=N>=0?"start":"end";return e.jsxs("g",{children:[e.jsx("text",{x:w,y:n,dy:8,textAnchor:"middle",fill:X,children:A.name}),e.jsx(li,{cx:w,cy:n,innerRadius:U,outerRadius:m,startAngle:ne,endAngle:v,fill:X}),e.jsx(li,{cx:w,cy:n,startAngle:ne,endAngle:v,innerRadius:m+6,outerRadius:m+10,fill:X}),e.jsx("path",{d:`M${te},${W}L${ce},${Q}L${xe},${D}`,stroke:X,fill:"none"}),e.jsx("circle",{cx:xe,cy:D,r:2,fill:X,stroke:"none"}),e.jsx("text",{x:xe+(N>=0?1:-1)*12,y:D,textAnchor:pe,fill:"#333",children:`${K}`}),e.jsx("text",{x:xe+(N>=0?1:-1)*12,y:D,dy:18,textAnchor:pe,fill:"#999",children:`(${(ye*100).toFixed(2)}%)`})]})},Fa=y=>{const t=y.map(O=>O.reduce((N,te)=>N+te,0)),w=y[0].map((O,N)=>y.reduce((te,W)=>te+W[N],0)),n=t.reduce((O,N)=>O+N,0),j=[];let U=1/0,m=!1;for(let O=0;O<y.length;O++){j[O]=[];for(let N=0;N<y[O].length;N++){const te=t[O]*w[N]/n;j[O][N]=te,te<5&&(m=!0,U=Math.min(U,te))}}let ne=0;for(let O=0;O<y.length;O++)for(let N=0;N<y[O].length;N++){const te=y[O][N],W=j[O][N];if(m){const ce=Math.abs(te-W)-.5;ce>0&&(ne+=ce*ce/W)}else{const ce=te-W;ne+=ce*ce/W}}const v=(y.length-1)*(y[0].length-1),X=v>0?1-oa.chisquare.cdf(ne,v):NaN,A=n,ye=Math.min(y.length,y[0].length),K=Math.sqrt(ne/(A*(ye-1)));return{chiSquare:ne,df:v,pValue:X,cramersV:K,yatesCorrection:m,minExpectedFrequency:U,expected:j}},qa=()=>{var et,Z,gt,jt,at,nt,pt,bt,be,Qe,Me,r,i,l,S,h,p,J,re,q,Je,kt,Xt,Zt,es,ts,rt,It,Wt,ft,ss,is,as,ns,zt,At,Rt,Pt,Ot,Bt,Et,Dt,Ft,qt,Lt,_t,Kt,rs;const{datasets:y,currentDataset:t,setCurrentDataset:w}=$s(),n=Vt(),[j,U]=b.useState((t==null?void 0:t.id)||""),[m,ne]=b.useState(""),[v,X]=b.useState(""),[A,ye]=b.useState(!0),[K,O]=b.useState(!0),[N,te]=b.useState(!0),[W,ce]=b.useState(!0),[Q,xe]=b.useState(!1),[D,pe]=b.useState(!1),[g,ue]=b.useState(!1),[he,De]=b.useState(!1),[se,ve]=b.useState(null),[L,Ze]=b.useState("stacked"),[me,Le]=b.useState(0),[_e,Ve]=b.useState(!1),[Ke,Ge]=b.useState(0),[o,f]=b.useState(null);b.useEffect(()=>{const s=localStorage.getItem("cross_tabulation_results"),I=localStorage.getItem("cross_tabulation_config");if(s)try{f(JSON.parse(s))}catch(k){console.error("Error parsing saved cross tabulation results:",k)}if(I)try{const k=JSON.parse(I);ne(k.rowVariable||""),X(k.columnVariable||""),ye(k.includePercentages??!0),O(k.includeRowPercentages??!0),te(k.includeColumnPercentages??!0),ce(k.includeChiSquare??!0),xe(k.excludeMissing??!1),pe(k.showExpectedFrequencies??!1),ue(k.showResiduals??!1),Ze(k.visualizationType||"stacked")}catch(k){console.error("Error parsing saved configuration:",k)}},[]),b.useEffect(()=>{const s={rowVariable:m,columnVariable:v,includePercentages:A,includeRowPercentages:K,includeColumnPercentages:N,includeChiSquare:W,excludeMissing:Q,showExpectedFrequencies:D,showResiduals:g,visualizationType:L};localStorage.setItem("cross_tabulation_config",JSON.stringify(s))},[m,v,A,K,N,W,Q,D,g,L]);const C=(t==null?void 0:t.columns.filter(s=>s.type===Ue.CATEGORICAL||s.type===Ue.ORDINAL||s.type===Ue.BOOLEAN))||[],u=s=>{const I=s.target.value;U(I),ne(""),X(""),f(null),localStorage.removeItem("cross_tabulation_results");const k=y.find(_=>_.id===I);k&&w(k)},F=s=>{ne(s.target.value),f(null),localStorage.removeItem("cross_tabulation_results")},P=s=>{X(s.target.value),f(null),localStorage.removeItem("cross_tabulation_results")},ie=()=>{const s=m;ne(v),X(s),f(null),localStorage.removeItem("cross_tabulation_results")},G=(s,I,k,_)=>{const Se={},Y={},E={};return Object.keys(s).forEach(R=>{Se[R]={},Y[R]={},E[R]={},Object.keys(s[R]).forEach(ge=>{const M=I[R]*k[ge]/_;Se[R][ge]=M;const z=s[R][ge]-M;Y[R][ge]=z,M>0?E[R][ge]=z/Math.sqrt(M):E[R][ge]=0})}),{expected:Se,residuals:Y,standardizedResiduals:E}},T=()=>{if(!t||!m||!v){ve("Please select a dataset and both row and column variables.");return}De(!0),ve(null),setTimeout(()=>{try{const s=t.columns.find($=>$.id===m),I=t.columns.find($=>$.id===v);if(!s||!I)throw new Error("Selected columns not found in dataset.");const k=[],_=[];t.data.forEach($=>{const le=$[s.name],Ie=$[I.name],wt=Is(le,s),Vs=Is(Ie,I);if(!Q||!wt.isMissing&&!Vs.isMissing){const wi=wt.isMissing&&!Q?"Missing":String(le??"Missing"),Mi=Vs.isMissing&&!Q?"Missing":String(Ie??"Missing");(!Q||!wt.isMissing&&!Vs.isMissing)&&(k.push(wi),_.push(Mi))}});const E=na(k,_),R=t.columns.find($=>$.id===m),ge=t.columns.find($=>$.id===v),M=R?ri(t.data,R):[],oe=ge?ri(t.data,ge):[],z=R&&R.categoryOrder&&R.categoryOrder.length>0?R.categoryOrder.filter($=>Object.keys(E).includes($)):Object.keys(E),fe=ge&&ge.categoryOrder&&ge.categoryOrder.length>0?ge.categoryOrder.filter($=>z.length>0&&Object.keys(E[z[0]]||{}).includes($)):Object.keys(z.length>0?E[z[0]]||{}:{}),ee={},ke={};let ot=0;z.forEach($=>{ee[$]=0,fe.forEach(le=>{const Ie=E[$][le]||0;ee[$]+=Ie,ke[le]=(ke[le]||0)+Ie,ot+=Ie})});const yt={},Ts={},Ns={};z.forEach($=>{yt[$]={},Ts[$]={},Ns[$]={},fe.forEach(le=>{const Ie=E[$][le]||0;yt[$][le]=ee[$]>0?Ie/ee[$]:0,Ts[$][le]=ke[le]>0?Ie/ke[le]:0,Ns[$][le]=ot>0?Ie/ot:0})});let Js,Ys,Xs;if(D||g||W){const $=G(E,ee,ke,ot);Js=$.expected,Ys=$.residuals,Xs=$.standardizedResiduals}let Zs;if(W){const $=z.map(le=>fe.map(Ie=>E[le][Ie]||0));try{Zs=Fa($)}catch(le){console.error("Chi-square calculation failed:",le)}}const Fi=z.map($=>{const le={category:$};return fe.forEach(Ie=>{var wt;le[Ie]=((wt=E[$])==null?void 0:wt[Ie])||0}),le}),qi=z.map($=>({name:$,value:ee[$]})),ei={rowCategories:z,columnCategories:fe,frequencies:E,rowPercentages:yt,columnPercentages:Ts,totalPercentages:Ns,rowTotals:ee,columnTotals:ke,grandTotal:ot,expectedFrequencies:Js,residuals:Ys,standardizedResiduals:Xs,chiSquare:Zs,chartData:Fi,pieData:qi};f(ei),localStorage.setItem("cross_tabulation_results",JSON.stringify(ei)),De(!1)}catch(s){ve(`Error analyzing data: ${s instanceof Error?s.message:String(s)}`),De(!1)}},1e3)},We=()=>{var Se,Y,E;if(!o)return;let s=`${((Se=t==null?void 0:t.columns.find(R=>R.id===m))==null?void 0:Se.name)||"Row"} / ${((Y=t==null?void 0:t.columns.find(R=>R.id===v))==null?void 0:Y.name)||"Column"}`;o.columnCategories.forEach(R=>{s+=`,${R}`}),s+=`,Total
`,o.rowCategories.forEach(R=>{s+=R,o.columnCategories.forEach(ge=>{s+=`,${o.frequencies[R][ge]||0}`}),s+=`,${o.rowTotals[R]}
`}),s+="Total",o.columnCategories.forEach(R=>{s+=`,${o.columnTotals[R]}`}),s+=`,${o.grandTotal}
`,o.chiSquare&&(s+=`

Chi-Square Test Results
`,s+=`Chi-Square Value,${o.chiSquare.chiSquare.toFixed(3)}
`,s+=`Degrees of Freedom,${o.chiSquare.df}
`,s+=`P-Value,${o.chiSquare.pValue.toFixed(3)}
`,s+=`Cramer's V,${o.chiSquare.cramersV.toFixed(3)}
`,o.chiSquare.yatesCorrection&&(s+=`Yates Correction Applied,Yes
`,s+=`Minimum Expected Frequency,${(E=o.chiSquare.minExpectedFrequency)==null?void 0:E.toFixed(2)}
`));const I=new Blob([s],{type:"text/csv"}),k=window.URL.createObjectURL(I),_=document.createElement("a");_.href=k,_.download="cross_tabulation_results.csv",_.click(),window.URL.revokeObjectURL(k)},Ce=s=>{const I=[n.palette.mode==="dark"?"rgba(33, 150, 243, 0.8)":"rgba(25, 118, 210, 0.8)",(n.palette.mode==="dark","rgba(156, 39, 176, 0.8)"),n.palette.mode==="dark"?"rgba(76, 175, 80, 0.8)":"rgba(46, 125, 50, 0.8)",n.palette.mode==="dark"?"rgba(255, 152, 0, 0.8)":"rgba(237, 108, 2, 0.8)",n.palette.mode==="dark"?"rgba(244, 67, 54, 0.8)":"rgba(198, 40, 40, 0.8)",n.palette.mode==="dark"?"rgba(0, 188, 212, 0.8)":"rgba(0, 151, 167, 0.8)",n.palette.mode==="dark"?"rgba(121, 85, 72, 0.8)":"rgba(93, 64, 55, 0.8)",n.palette.mode==="dark"?"rgba(158, 158, 158, 0.8)":"rgba(97, 97, 97, 0.8)",n.palette.mode==="dark"?"rgba(255, 193, 7, 0.8)":"rgba(255, 143, 0, 0.8)",n.palette.mode==="dark"?"rgba(103, 58, 183, 0.8)":"rgba(81, 45, 168, 0.8)",n.palette.mode==="dark"?"rgba(63, 81, 181, 0.8)":"rgba(48, 63, 159, 0.8)",n.palette.mode==="dark"?"rgba(139, 195, 74, 0.8)":"rgba(104, 159, 56, 0.8)"],k=[];for(let _=0;_<s;_++)k.push(I[_%I.length]);return k},we=s=>s<.1?{label:"Negligible",color:"text.secondary"}:s<.3?{label:"Small",color:"info.main"}:s<.5?{label:"Medium",color:"warning.main"}:{label:"Large",color:"success.main"};return e.jsxs(x,{p:3,children:[e.jsxs(x,{display:"flex",alignItems:"center",mb:3,children:[e.jsx(Nt,{sx:{fontSize:32,mr:2,color:n.palette.primary.main}}),e.jsx(a,{variant:"h4",component:"h1",children:"Cross Tabulation Analysis"}),e.jsx(x,{ml:"auto",children:e.jsx(de,{title:"Help",children:e.jsx(Ps,{onClick:()=>window.location.href="/app#video-tutorials",children:e.jsx(Os,{})})})})]}),e.jsx(ys,{in:!0,timeout:500,children:e.jsxs(H,{elevation:3,sx:{p:3,mb:3,borderRadius:2},children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(xi,{sx:{mr:1}}),"Variable Selection"]}),e.jsxs(c,{container:!0,spacing:3,alignItems:"center",children:[e.jsx(c,{item:!0,xs:12,md:5,children:e.jsxs(qe,{fullWidth:!0,margin:"normal",variant:"outlined",children:[e.jsx(Pe,{id:"dataset-select-label",children:"Dataset"}),e.jsx(Oe,{labelId:"dataset-select-label",id:"dataset-select",value:j,label:"Dataset",onChange:u,disabled:y.length===0,children:y.length===0?e.jsx(B,{value:"",disabled:!0,children:"No datasets available"}):y.map(s=>e.jsx(B,{value:s.id,children:e.jsxs(x,{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",children:[e.jsx("span",{children:s.name}),e.jsx(Ae,{label:`${s.data.length} rows`,size:"small"})]})},s.id))})]})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsxs(qe,{fullWidth:!0,margin:"normal",variant:"outlined",children:[e.jsx(Pe,{id:"row-variable-label",children:"Row Variable"}),e.jsx(Oe,{labelId:"row-variable-label",id:"row-variable",value:m,label:"Row Variable",onChange:F,disabled:C.length===0,children:C.length===0?e.jsx(B,{value:"",disabled:!0,children:"No categorical variables"}):C.map(s=>e.jsx(B,{value:s.id,children:s.name},s.id))})]})}),e.jsx(c,{item:!0,xs:12,md:1,sx:{display:"flex",justifyContent:"center"},children:e.jsx(de,{title:"Swap Variables",children:e.jsx(Ps,{onClick:ie,disabled:!m||!v,color:"primary",size:"large",children:e.jsx(Ei,{})})})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsxs(qe,{fullWidth:!0,margin:"normal",variant:"outlined",children:[e.jsx(Pe,{id:"column-variable-label",children:"Column Variable"}),e.jsx(Oe,{labelId:"column-variable-label",id:"column-variable",value:v,label:"Column Variable",onChange:P,disabled:C.length===0,children:C.length===0?e.jsx(B,{value:"",disabled:!0,children:"No categorical variables"}):C.map(s=>e.jsx(B,{value:s.id,children:s.name},s.id))})]})})]}),e.jsxs(x,{mt:3,children:[e.jsxs(a,{variant:"subtitle1",sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(ws,{sx:{mr:1}}),"Analysis Options"]}),e.jsxs(x,{sx:{p:2,border:1,borderColor:"divider",borderRadius:1,bgcolor:"background.paper",mb:2},children:[e.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:2,fontWeight:"medium"},children:"Core Analysis Settings"}),!Q&&e.jsx(tt,{severity:"info",sx:{mb:2},children:e.jsxs(a,{variant:"body2",children:[e.jsx("strong",{children:"Missing Data Notice:"}),' User-defined missing value codes will be included as separate categories. Enable "Exclude Missing" to properly handle missing data in your analysis.']})}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:Q,onChange:s=>xe(s.target.checked),color:"warning"}),label:e.jsxs(x,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(a,{variant:"body2",fontWeight:"medium",color:"warning.main",children:"Exclude Missing"}),e.jsx(de,{title:"Exclude user-defined missing value codes from analysis",arrow:!0,children:e.jsx(Os,{sx:{ml:.5,fontSize:16,color:"text.secondary"}})})]})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:W,onChange:s=>ce(s.target.checked),color:"primary"}),label:"Chi-Square Test"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:D,onChange:s=>pe(s.target.checked),color:"secondary"}),label:"Expected Frequencies"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:g,onChange:s=>ue(s.target.checked),color:"secondary"}),label:"Residuals"})})]})]}),e.jsxs(x,{sx:{p:2,border:1,borderColor:"divider",borderRadius:1,bgcolor:"background.paper"},children:[e.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:2,fontWeight:"medium"},children:"Display Options"}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:A,onChange:s=>ye(s.target.checked),color:"primary"}),label:"Total Percentages"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:K,onChange:s=>O(s.target.checked),color:"primary"}),label:"Row Percentages"})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(ae,{control:e.jsx(Fe,{checked:N,onChange:s=>te(s.target.checked),color:"primary"}),label:"Column Percentages"})})]})]})]}),e.jsxs(x,{mt:3,display:"flex",gap:2,flexWrap:"wrap",children:[e.jsx(Te,{variant:"contained",color:"primary",size:"large",startIcon:e.jsx(ni,{}),onClick:T,disabled:he||!m||!v,sx:{minWidth:200},children:he?e.jsx(Jt,{size:24,color:"inherit"}):"Generate Analysis"}),e.jsx(Te,{variant:"outlined",color:"secondary",startIcon:e.jsx(Bs,{}),onClick:()=>{f(null),localStorage.removeItem("cross_tabulation_results")},disabled:!o,children:"Clear Results"})]})]})}),se&&e.jsx(gi,{in:!0,children:e.jsx(tt,{severity:"error",sx:{mb:3},onClose:()=>ve(null),children:se})}),o&&!he&&e.jsx(ys,{in:!0,timeout:700,children:e.jsxs(x,{children:[e.jsxs(H,{elevation:3,sx:{borderRadius:2,overflow:"hidden"},children:[e.jsxs(Yt,{value:me,onChange:(s,I)=>Le(I),indicatorColor:"primary",textColor:"primary",variant:"fullWidth",children:[e.jsx(je,{label:"Table View",icon:e.jsx(ks,{}),iconPosition:"start"}),e.jsx(je,{label:"Visualizations",icon:e.jsx(it,{}),iconPosition:"start"}),e.jsx(je,{label:"Statistical Analysis",icon:e.jsx(Rs,{}),iconPosition:"start"})]}),e.jsxs(x,{p:3,children:[me===0&&e.jsxs(x,{children:[e.jsxs(x,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(a,{variant:"h6",children:"Cross Tabulation Table"}),e.jsx(Te,{startIcon:e.jsx(Di,{}),variant:"outlined",size:"small",onClick:We,children:"Export CSV"})]}),e.jsx(dt,{component:H,variant:"outlined",children:e.jsxs(xt,{size:"small",children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:"bold",borderRight:`2px solid ${n.palette.divider}`,borderBottom:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.grey[100]},children:`${((et=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:et.name)||"Row"} / ${((Z=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Z.name)||"Column"}`}),o.columnCategories.map(s=>e.jsx(d,{align:"center",sx:{fontWeight:"bold",borderBottom:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.grey[50]},children:s},s)),e.jsx(d,{align:"center",sx:{fontWeight:"bold",borderLeft:`2px solid ${n.palette.divider}`,borderBottom:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.primary.light,color:n.palette.primary.contrastText},children:"Total"})]})}),e.jsxs(mt,{children:[o.rowCategories.map((s,I)=>e.jsxs(V,{sx:{"&:hover":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:"bold",borderRight:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.grey[50]},children:s}),o.columnCategories.map(k=>e.jsx(d,{align:"center",children:e.jsxs(x,{children:[e.jsx(a,{variant:"body2",fontWeight:"bold",children:o.frequencies[s][k]||0}),D&&o.expectedFrequencies&&e.jsxs(a,{variant:"caption",display:"block",color:"info.main",children:["Expected: ",(o.expectedFrequencies[s][k]||0).toFixed(1)]}),g&&o.standardizedResiduals&&e.jsxs(a,{variant:"caption",display:"block",color:Math.abs(o.standardizedResiduals[s][k]||0)>2?"error.main":"text.secondary",children:["Std. Res: ",(o.standardizedResiduals[s][k]||0).toFixed(2)]}),A&&e.jsxs(a,{variant:"caption",display:"block",color:"text.secondary",children:[((o.totalPercentages[s][k]||0)*100).toFixed(1),"%"]}),K&&e.jsxs(a,{variant:"caption",display:"block",color:"primary",children:["→",((o.rowPercentages[s][k]||0)*100).toFixed(1),"%"]}),N&&e.jsxs(a,{variant:"caption",display:"block",color:"secondary",children:["↓",((o.columnPercentages[s][k]||0)*100).toFixed(1),"%"]})]})},k)),e.jsxs(d,{align:"center",sx:{fontWeight:"bold",borderLeft:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.grey[50]},children:[o.rowTotals[s]||0,A&&e.jsxs(a,{variant:"caption",display:"block",color:"text.secondary",children:[((o.rowTotals[s]/o.grandTotal||0)*100).toFixed(1),"%"]})]})]},s)),e.jsxs(V,{sx:{borderTop:`2px solid ${n.palette.divider}`},children:[e.jsx(d,{sx:{fontWeight:"bold",borderRight:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.primary.light,color:n.palette.primary.contrastText},children:"Total"}),o.columnCategories.map(s=>e.jsxs(d,{align:"center",sx:{fontWeight:"bold",backgroundColor:n.palette.grey[50]},children:[o.columnTotals[s]||0,A&&e.jsxs(a,{variant:"caption",display:"block",color:"text.secondary",children:[((o.columnTotals[s]/o.grandTotal||0)*100).toFixed(1),"%"]})]},s)),e.jsxs(d,{align:"center",sx:{fontWeight:"bold",borderLeft:`2px solid ${n.palette.divider}`,backgroundColor:n.palette.primary.main,color:n.palette.primary.contrastText},children:[o.grandTotal,A&&e.jsx(a,{variant:"caption",display:"block",children:"100.0%"})]})]})]})]})})]}),me===1&&e.jsxs(x,{children:[e.jsx(x,{mb:3,children:e.jsxs(Ms,{value:L,exclusive:!0,onChange:(s,I)=>I&&Ze(I),size:"small",children:[e.jsx(Ne,{value:"stacked",children:e.jsx(de,{title:"Stacked Bar Chart",children:e.jsx(it,{})})}),e.jsx(Ne,{value:"grouped",children:e.jsx(de,{title:"Grouped Bar Chart",children:e.jsx(ni,{})})}),e.jsx(Ne,{value:"pie",children:e.jsx(de,{title:"Pie Chart",children:e.jsx(Ds,{})})})]})}),L==="stacked"&&e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(qs,{data:o.chartData,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{dataKey:"category",angle:-45,textAnchor:"end",height:70,interval:0}),e.jsx(He,{}),e.jsx(Re,{}),e.jsx(ut,{}),o.columnCategories.map((s,I)=>e.jsx(St,{dataKey:s,name:s,stackId:"a",fill:Ce(o.columnCategories.length)[I]},s))]})})}),L==="grouped"&&e.jsx(x,{height:400,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(qs,{data:o.chartData,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Xe,{dataKey:"category",angle:-45,textAnchor:"end",height:70,interval:0}),e.jsx(He,{}),e.jsx(Re,{}),e.jsx(ut,{}),o.columnCategories.map((s,I)=>e.jsx(St,{dataKey:s,name:s,fill:Ce(o.columnCategories.length)[I]},s))]})})}),L==="pie"&&o.pieData&&e.jsxs(x,{children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{textAlign:"center",mb:3},children:["Cross-Tabulation Distribution: ",(gt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:gt.name," by ",(jt=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:jt.name]}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(H,{elevation:2,sx:{p:2,borderRadius:2},children:[e.jsxs(a,{variant:"subtitle1",gutterBottom:!0,sx:{textAlign:"center"},children:[(at=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:at.name," Distribution"]}),e.jsx(x,{height:300,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(Ls,{children:[e.jsx(fs,{activeIndex:Ke,activeShape:Ia,data:o.pieData,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,fill:"#8884d8",dataKey:"value",onMouseEnter:(s,I)=>Ge(I),children:o.pieData.map((s,I)=>e.jsx(Ut,{fill:Ce(o.pieData.length)[I]},`cell-${I}`))}),e.jsx(Re,{formatter:s=>[s,"Count"],labelFormatter:s=>{var I;return`${(I=t==null?void 0:t.columns.find(k=>k.id===m))==null?void 0:I.name}: ${s}`}})]})})})]})}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(H,{elevation:2,sx:{p:2,borderRadius:2},children:[e.jsxs(a,{variant:"subtitle1",gutterBottom:!0,sx:{textAlign:"center"},children:[(nt=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:nt.name," within ",(pt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:pt.name]}),e.jsx(x,{height:300,children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(Ls,{children:[e.jsx(fs,{data:o.pieData,cx:"50%",cy:"50%",innerRadius:80,outerRadius:100,fill:"#8884d8",dataKey:"value",stroke:"white",strokeWidth:2,children:o.pieData.map((s,I)=>e.jsx(Ut,{fill:Ce(o.pieData.length)[I]},`outer-${I}`))}),e.jsx(fs,{data:(()=>{const s=[];return o.rowCategories.forEach(I=>{o.columnCategories.forEach(k=>{const _=o.frequencies[I][k]||0;_>0&&s.push({name:`${I} - ${k}`,value:_,parentCategory:I})})}),s})(),cx:"50%",cy:"50%",innerRadius:40,outerRadius:75,fill:"#82ca9d",dataKey:"value",stroke:"white",strokeWidth:1,children:(()=>{const s=[];return o.rowCategories.forEach(I=>{o.columnCategories.forEach(k=>{const _=o.frequencies[I][k]||0;_>0&&s.push({name:`${I} - ${k}`,value:_,parentCategory:I})})}),s.map((I,k)=>{const _=o.rowCategories.indexOf(I.parentCategory),Se=Ce(o.rowCategories.length)[_],Y=.6+k%o.columnCategories.length*.1;return e.jsx(Ut,{fill:Se,fillOpacity:Y},`inner-${k}`)})})()}),e.jsx(Re,{formatter:(s,I,k)=>k.payload.parentCategory?[s,`${k.payload.name.split(" - ")[1]} in ${k.payload.parentCategory}`]:[s,"Count"]})]})})})]})}),e.jsx(c,{item:!0,xs:12,children:e.jsxs(H,{elevation:1,sx:{p:2,backgroundColor:n.palette.grey[50]},children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,children:"Chart Interpretation:"}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Left chart:"})," Shows the overall distribution of ",(bt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:bt.name," categories"]}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Right chart:"})," Nested view showing how ",(be=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:be.name," is distributed within each ",(Qe=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Qe.name," category"]}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Outer ring:"})," ",(Me=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Me.name," categories"]}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Inner ring:"})," ",(r=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:r.name," breakdown within each ",(i=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:i.name]})]})})]})]})]}),me===2&&e.jsx(x,{children:o.chiSquare?e.jsxs(c,{container:!0,spacing:3,children:[o.chiSquare.yatesCorrection&&e.jsx(c,{item:!0,xs:12,children:e.jsx(tt,{severity:"warning",icon:e.jsx(Gt,{}),children:e.jsxs(a,{variant:"body2",children:[e.jsx("strong",{children:"Yates' Continuity Correction Applied:"})," At least one expected frequency was less than 5 (minimum: ",(l=o.chiSquare.minExpectedFrequency)==null?void 0:l.toFixed(2),"). The chi-square test has been adjusted for small sample sizes."]})})}),e.jsx(c,{item:!0,xs:12,children:e.jsxs(tt,{severity:o.chiSquare.pValue<.05?"success":"info",icon:e.jsx(Rs,{}),children:[e.jsx(a,{variant:"body1",fontWeight:"bold",children:o.chiSquare.pValue<.05?"Significant Association Found":"No Significant Association"}),e.jsx(a,{variant:"body2",sx:{mt:1},children:o.chiSquare.pValue<.05?`There is a statistically significant relationship between ${(S=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:S.name} and ${(h=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:h.name} (p ${o.chiSquare.pValue<.001?"< 0.001":`= ${o.chiSquare.pValue.toFixed(3)}`}). The distribution of ${(p=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:p.name} varies significantly across different ${(J=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:J.name} categories.`:`The relationship between ${(re=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:re.name} and ${(q=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:q.name} is not statistically significant (p = ${o.chiSquare.pValue.toFixed(3)}). The distribution of ${(Je=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Je.name} appears to be independent of ${(kt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:kt.name} categories.`})]})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Be,{children:e.jsxs(Ee,{children:[e.jsxs(a,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:["Chi-Square Value",o.chiSquare.yatesCorrection&&e.jsx(Ae,{label:"Yates",size:"small",color:"warning",sx:{ml:1}})]}),e.jsx(a,{variant:"h4",children:o.chiSquare.chiSquare.toFixed(3)})]})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Be,{children:e.jsxs(Ee,{children:[e.jsx(a,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Degrees of Freedom"}),e.jsx(a,{variant:"h4",children:o.chiSquare.df})]})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Be,{children:e.jsxs(Ee,{children:[e.jsx(a,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"P-Value"}),e.jsx(a,{variant:"h4",color:o.chiSquare.pValue<.05?"error.main":"text.primary",children:o.chiSquare.pValue<.001?"< 0.001":o.chiSquare.pValue.toFixed(3)})]})})}),e.jsx(c,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Be,{children:e.jsxs(Ee,{children:[e.jsx(a,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Effect Size (Cramér's V)"}),e.jsx(a,{variant:"h4",color:we(o.chiSquare.cramersV).color,children:o.chiSquare.cramersV.toFixed(3)}),e.jsxs(a,{variant:"caption",color:"text.secondary",children:[we(o.chiSquare.cramersV).label," effect"]})]})})}),e.jsx(c,{item:!0,xs:12,children:e.jsxs(H,{sx:{p:3,backgroundColor:n.palette.grey[50],borderRadius:2},children:[e.jsxs(a,{variant:"subtitle1",gutterBottom:!0,children:[e.jsx(Ct,{sx:{mr:1,verticalAlign:"middle"}}),"Analysis Interpretation for ",(Xt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Xt.name," × ",(Zt=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Zt.name]}),e.jsxs(a,{variant:"body2",paragraph:!0,children:["This chi-square test examines whether the distribution of ",e.jsx("strong",{children:(es=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:es.name})," varies significantly across different categories of ",e.jsx("strong",{children:(ts=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:ts.name}),"."]}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Statistical Significance:"}),e.jsx(a,{variant:"body2",component:"div",children:e.jsxs("ul",{style:{margin:0,paddingLeft:"1.2em"},children:[e.jsxs("li",{children:[e.jsxs("strong",{children:["P-value ",o.chiSquare.pValue<.05?"< 0.05":"≥ 0.05",":"]})," ",o.chiSquare.pValue<.05?"Significant relationship detected":"No significant relationship"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Current p-value:"})," ",o.chiSquare.pValue<.001?"< 0.001":o.chiSquare.pValue.toFixed(3)]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Interpretation:"})," ",o.chiSquare.pValue<.05?`Knowing someone's ${(rt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:rt.name} helps predict their ${(It=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:It.name}`:`${(Wt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Wt.name} and ${(ft=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:ft.name} appear to be independent`]})]})})]}),e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsxs(a,{variant:"subtitle2",gutterBottom:!0,color:"secondary",children:["Effect Size (Cramér's V = ",o.chiSquare.cramersV.toFixed(3),"):"]}),e.jsx(a,{variant:"body2",component:"div",children:e.jsxs("ul",{style:{margin:0,paddingLeft:"1.2em"},children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Strength:"})," ",we(o.chiSquare.cramersV).label," association"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Practical meaning:"})," ",o.chiSquare.cramersV<.1?`Very weak relationship - ${(ss=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:ss.name} has minimal influence on ${(is=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:is.name}`:o.chiSquare.cramersV<.3?`Weak to moderate relationship - ${(as=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:as.name} has some influence on ${(ns=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:ns.name}`:o.chiSquare.cramersV<.5?`Moderate to strong relationship - ${(zt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:zt.name} has considerable influence on ${(At=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:At.name}`:`Strong relationship - ${(Rt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Rt.name} strongly determines ${(Pt=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Pt.name}`]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Scale:"})," 0 (no association) to 1 (perfect association)"]})]})})]}),o.chiSquare.yatesCorrection&&e.jsxs(c,{item:!0,xs:12,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,color:"warning.main",children:"Yates' Continuity Correction Applied:"}),e.jsxs(a,{variant:"body2",children:["This correction was applied because some expected frequencies were below 5 (minimum: ",(Ot=o.chiSquare.minExpectedFrequency)==null?void 0:Ot.toFixed(2),"). This makes the test more conservative and reduces the chance of false positive results with small sample sizes."]})]}),e.jsxs(c,{item:!0,xs:12,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,color:"info.main",children:"Data-Specific Insights:"}),e.jsx(a,{variant:"body2",component:"div",children:(()=>{var _,Se;const s=Object.entries(o.rowTotals).reduce((Y,E)=>o.rowTotals[Y[0]]>o.rowTotals[E[0]]?Y:E),I=Object.entries(o.columnTotals).reduce((Y,E)=>o.columnTotals[Y[0]]>o.columnTotals[E[0]]?Y:E);let k={row:"",col:"",count:0};return o.rowCategories.forEach(Y=>{o.columnCategories.forEach(E=>{const R=o.frequencies[Y][E]||0;R>k.count&&(k={row:Y,col:E,count:R})})}),e.jsxs("ul",{style:{margin:0,paddingLeft:"1.2em"},children:[e.jsxs("li",{children:[e.jsxs("strong",{children:["Most common ",(_=t==null?void 0:t.columns.find(Y=>Y.id===m))==null?void 0:_.name,":"]})," ",s[0],"(",s[1]," cases, ",(s[1]/o.grandTotal*100).toFixed(1),"% of total)"]}),e.jsxs("li",{children:[e.jsxs("strong",{children:["Most common ",(Se=t==null?void 0:t.columns.find(Y=>Y.id===v))==null?void 0:Se.name,":"]})," ",I[0],"(",I[1]," cases, ",(I[1]/o.grandTotal*100).toFixed(1),"% of total)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Most frequent combination:"})," ",k.row," + ",k.col,"(",k.count," cases, ",(k.count/o.grandTotal*100).toFixed(1),"% of total)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Sample size:"})," ",o.grandTotal," total cases across ",o.rowCategories.length," × ",o.columnCategories.length," = ",o.rowCategories.length*o.columnCategories.length," possible combinations"]})]})})()})]}),e.jsxs(c,{item:!0,xs:12,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,color:"success.main",children:"Actionable Recommendations:"}),e.jsx(a,{variant:"body2",children:o.chiSquare.pValue<.05?e.jsxs(e.Fragment,{children:["• ",e.jsx("strong",{children:"Strategic focus:"})," Consider ",e.jsx("strong",{children:(Bt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Bt.name})," when making decisions about ",e.jsx("strong",{children:(Et=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Et.name}),e.jsx("br",{}),"• ",e.jsx("strong",{children:"Pattern analysis:"})," Examine the cross-tabulation table to identify which specific combinations are over/under-represented",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Practical importance:"})," The relationship strength (",we(o.chiSquare.cramersV).label.toLowerCase(),") suggests ",o.chiSquare.cramersV>.3?"this finding has strong practical implications for decision-making":"this may have limited practical importance despite being statistically significant",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Next steps:"})," Investigate why certain ",(Dt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:Dt.name," categories are associated with specific ",(Ft=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Ft.name," outcomes"]}):e.jsxs(e.Fragment,{children:["• ",e.jsx("strong",{children:"Independence confirmed:"})," ",e.jsx("strong",{children:(qt=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:qt.name})," appears to be independent of ",e.jsx("strong",{children:(Lt=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Lt.name}),e.jsx("br",{}),"• ",e.jsx("strong",{children:"Alternative analysis:"})," Consider other variables that might be related to ",e.jsx("strong",{children:(_t=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:_t.name}),e.jsx("br",{}),"• ",e.jsx("strong",{children:"Uniform distribution:"})," The distribution of ",e.jsx("strong",{children:(Kt=t==null?void 0:t.columns.find(s=>s.id===v))==null?void 0:Kt.name})," appears consistent across ",e.jsx("strong",{children:(rs=t==null?void 0:t.columns.find(s=>s.id===m))==null?void 0:rs.name})," categories",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Sample considerations:"})," Ensure adequate sample sizes in each cell before concluding independence"]})})]})]})]})})]}):e.jsx(tt,{severity:"info",children:"Chi-square test was not performed. Enable it in the analysis options to see statistical results."})})]})]}),e.jsxs(Li,{ariaLabel:"Quick actions",sx:{position:"fixed",bottom:16,right:16},icon:e.jsx(Qi,{}),open:_e,onOpen:()=>Ve(!0),onClose:()=>Ve(!1),children:[e.jsx(xs,{icon:e.jsx(ui,{}),tooltipTitle:"Export CSV",onClick:()=>{We(),Ve(!1)}}),e.jsx(xs,{icon:e.jsx(_i,{}),tooltipTitle:"Print",onClick:()=>{window.print(),Ve(!1)}}),e.jsx(xs,{icon:e.jsx(Bs,{}),tooltipTitle:"Refresh Analysis",onClick:()=>{T(),Ve(!1)}}),e.jsx(xs,{icon:e.jsx(Ki,{}),tooltipTitle:"Share",onClick:()=>{Ve(!1)}})]})]})}),he&&e.jsxs(x,{children:[e.jsx(lt,{variant:"rectangular",height:400,sx:{mb:2}}),e.jsx(lt,{variant:"text",height:60}),e.jsx(lt,{variant:"text",height:60})]})]})};function ms(y){const{children:t,value:w,index:n,...j}=y;return e.jsx("div",{role:"tabpanel",hidden:w!==n,id:`normality-tabpanel-${n}`,"aria-labelledby":`normality-tab-${n}`,...j,children:w===n&&e.jsx(x,{sx:{py:3},children:t})})}const wa=()=>{const{datasets:y,currentDataset:t,setCurrentDataset:w}=$s(),n=Vt(),j=Cs(n.breakpoints.down("md")),U=Cs(n.breakpoints.down("lg")),[m,ne]=b.useState((t==null?void 0:t.id)||""),[v,X]=b.useState(""),[A,ye]=b.useState("tabs"),[K,O]=b.useState(0),[N,te]=b.useState(["auto"]),[W,ce]=b.useState(.05),[Q,xe]=b.useState(!1),[D,pe]=b.useState(null),[g,ue]=b.useState(null);b.useEffect(()=>{const u=localStorage.getItem("normality_test_results");if(u)try{ue(JSON.parse(u))}catch(F){console.error("Error parsing saved normality test results:",F)}},[]),b.useEffect(()=>{j?ye("tabs"):U&&ye("stacked")},[j,U]);const he=(t==null?void 0:t.columns.filter(u=>u.type===Ue.NUMERIC))||[],De=u=>{const F=u.target.value;ne(F),X(""),ue(null),localStorage.removeItem("normality_test_results");const P=y.find(ie=>ie.id===F);P&&w(P)},se=u=>{X(u.target.value),ue(null),localStorage.removeItem("normality_test_results")},ve=(u,F)=>{F!==null&&(ye(F),O(0))},L=()=>{ue(null),localStorage.removeItem("normality_test_results")},[Ze,me]=b.useState(!1),[Le,_e]=b.useState(""),Ve=()=>{if(!t||!v){pe("Please select a dataset and a numeric column to analyze.");return}xe(!0),pe(null);try{const u=t.columns.find(be=>be.id===v);if(!u)throw new Error("Selected column not found in dataset.");const F=t.data.map(be=>be[u.name]),P=ra(F);if(P.length===0)throw new Error("No numeric values found in the selected column.");const ie=P.length,G=st(P),T=ct(P),We=fi(P),Ce=yi(P),we=vi(P,W,N),et=[];P.forEach((be,Qe)=>{const Me=(Qe+.5)/ie,r=2.515517,i=.802853,l=.010328,S=1.432788,h=.189269,p=.001308;let J;if(Me<=.5){const q=Math.sqrt(-2*Math.log(Me));J=-q+(r+i*q+l*q*q)/(1+S*q+h*q*q+p*q*q*q)}else{const q=Math.sqrt(-2*Math.log(1-Me));J=q-(r+i*q+l*q*q)/(1+S*q+h*q*q+p*q*q*q)}const re=G+T*J;et.push({sample:be,theoretical:re})});const Z=Math.min(...P),jt=Math.max(...P)-Z,at=Math.max(5,Math.ceil(1+3.322*Math.log10(ie))),nt=jt/at,pt=[];for(let be=0;be<at;be++){const Qe=Z+be*nt,Me=Z+(be+1)*nt,r=(Qe+Me)/2,i=P.filter(p=>p>=Qe&&(be===at-1?p<=Me:p<Me)).length,l=(r-G)/T,h=1/(T*Math.sqrt(2*Math.PI))*Math.exp(-(l*l)/2)*ie*nt;pt.push({bin:`${Qe.toFixed(2)}-${Me.toFixed(2)}`,binStart:Qe,binEnd:Me,frequency:i,normalCurve:h})}const bt={column:u,statistics:{n:ie,mean:G,standardDeviation:T,skewness:We,kurtosis:Ce},normalityTests:we,qqPlotData:et,histogramData:pt};ue(bt),localStorage.setItem("normality_test_results",JSON.stringify(bt)),xe(!1),_e("Normality test completed successfully!"),me(!0)}catch(u){pe(`Error analyzing data: ${u instanceof Error?u.message:String(u)}`),xe(!1)}},Ke=u=>{if(!u||!u.statistics||!u.normalityTests)return"";const{skewness:F,kurtosis:P}=u.statistics,{overallAssessment:ie}=u.normalityTests;let G="";G+=`• Overall Assessment: ${ie.summary}
`,G+=`• Confidence Level: ${ie.confidence.charAt(0).toUpperCase()+ie.confidence.slice(1)}

`,Math.abs(F)>1?G+=`• The distribution shows ${F>0?"strong positive":"strong negative"} skewness (${F.toFixed(2)}), indicating a significant departure from symmetry.
`:Math.abs(F)>.5?G+=`• The distribution shows ${F>0?"moderate positive":"moderate negative"} skewness (${F.toFixed(2)}).
`:G+=`• The distribution is approximately symmetric (skewness = ${F.toFixed(2)}).
`;const T=P-3;return Math.abs(T)>1?G+=`• The distribution has ${T>0?"heavy tails (leptokurtic)":"light tails (platykurtic)"} compared to a normal distribution (kurtosis = ${P.toFixed(2)}).
`:G+=`• The distribution has tail weight similar to a normal distribution (kurtosis = ${P.toFixed(2)}).
`,G+=`• The Q-Q plot shows how closely the data points follow the theoretical normal distribution. Points falling along the diagonal line indicate normality.
`,G},Ge=()=>{if(!(g!=null&&g.normalityTests))return null;const{tests:u,overallAssessment:F,recommendedTest:P}=g.normalityTests,ie=Object.entries(u).filter(([G,T])=>T&&!isNaN(T.pValue));return e.jsxs(x,{children:[e.jsxs(H,{sx:{p:3,mb:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,color:n.palette.primary.main},children:"Overall Assessment"}),e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[e.jsx(Ae,{label:F.isNormal?"Normally Distributed":"Not Normally Distributed",color:F.isNormal?"success":"error",size:"medium",sx:{fontWeight:500}}),e.jsx(Ae,{label:`${F.confidence.charAt(0).toUpperCase()+F.confidence.slice(1)} Confidence`,variant:"outlined",size:"small"})]}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:2},children:F.summary}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Recommended Test:"})," ",P," (based on sample size: ",g.normalityTests.sampleSize,")"]})]}),e.jsx(a,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,mb:2},children:"Individual Test Results"}),e.jsx(dt,{component:H,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(xt,{size:j?"small":"medium",children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Test"}),e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Statistic"}),e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"p-value"}),e.jsx(d,{align:"center",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Result"}),e.jsx(d,{sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Interpretation"})]})}),e.jsx(mt,{children:ie.map(([G,T])=>e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:e.jsxs(x,{children:[e.jsx(a,{variant:"body2",sx:{fontWeight:500},children:T.testName}),T.recommendation&&e.jsx(a,{variant:"caption",color:"text.secondary",children:T.recommendation})]})}),e.jsx(d,{align:"right",children:T.statistic.toFixed(4)}),e.jsx(d,{align:"right",children:T.pValue.toFixed(4)}),e.jsx(d,{align:"center",children:e.jsx(Ae,{label:T.isNormal?"Normal":"Non-Normal",color:T.isNormal?"success":"error",size:"small"})}),e.jsx(d,{children:e.jsx(a,{variant:"body2",color:"text.secondary",children:T.interpretation})})]},G))})]})}),e.jsxs(H,{sx:{p:3,mt:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Test Descriptions"}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsx(a,{variant:"subtitle2",sx:{fontWeight:500,mb:1},children:"Shapiro-Wilk Test"}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Most powerful normality test for small to medium samples (n ≤ 50). Tests the null hypothesis that the data comes from a normal distribution."}),e.jsx(a,{variant:"subtitle2",sx:{fontWeight:500,mb:1},children:"Kolmogorov-Smirnov Test"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Good for larger samples. Compares the empirical distribution function with the theoretical normal distribution."})]}),e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsx(a,{variant:"subtitle2",sx:{fontWeight:500,mb:1},children:"Jarque-Bera Test"}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Based on skewness and kurtosis. Particularly good at detecting departures from normality due to asymmetry or heavy/light tails."}),e.jsx(a,{variant:"subtitle2",sx:{fontWeight:500,mb:1},children:"Anderson-Darling Test"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"More sensitive to deviations in the tails of the distribution compared to the Kolmogorov-Smirnov test."})]})]})]})]})},o=()=>e.jsx(dt,{component:H,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(xt,{size:j?"small":"medium",children:[e.jsx(ht,{children:e.jsxs(V,{children:[e.jsx(d,{sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Statistic"}),e.jsx(d,{align:"right",sx:{fontWeight:600,backgroundColor:n.palette.grey[50]},children:"Value"})]})}),e.jsxs(mt,{children:[e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:"Sample Size (N)"}),e.jsx(d,{align:"right",children:g==null?void 0:g.statistics.n.toLocaleString()})]}),e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:"Mean"}),e.jsx(d,{align:"right",children:g==null?void 0:g.statistics.mean.toFixed(4)})]}),e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:"Standard Deviation"}),e.jsx(d,{align:"right",children:g==null?void 0:g.statistics.standardDeviation.toFixed(4)})]}),e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:["Skewness",e.jsx(de,{title:"Measure of asymmetry. 0 = symmetric, >0 = right-skewed, <0 = left-skewed",children:e.jsx(Ct,{fontSize:"small",color:"action"})})]})}),e.jsx(d,{align:"right",children:g==null?void 0:g.statistics.skewness.toFixed(4)})]}),e.jsxs(V,{sx:{"&:nth-of-type(odd)":{backgroundColor:n.palette.action.hover}},children:[e.jsx(d,{sx:{fontWeight:500},children:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:["Kurtosis",e.jsx(de,{title:"Measure of tail heaviness. Normal distribution = 3",children:e.jsx(Ct,{fontSize:"small",color:"action"})})]})}),e.jsx(d,{align:"right",children:g==null?void 0:g.statistics.kurtosis.toFixed(4)})]}),e.jsxs(V,{sx:{backgroundColor:n.palette.action.selected},children:[e.jsx(d,{sx:{fontWeight:700},children:"Overall Assessment"}),e.jsx(d,{align:"right",children:e.jsx(Ae,{label:g!=null&&g.normalityTests.overallAssessment.isNormal?`Normal (${g==null?void 0:g.normalityTests.overallAssessment.confidence} confidence)`:`Non-Normal (${g==null?void 0:g.normalityTests.overallAssessment.confidence} confidence)`,color:g!=null&&g.normalityTests.overallAssessment.isNormal?"success":"error",size:"small"})})]})]})]})}),f=()=>e.jsx(H,{variant:"outlined",sx:{p:2,height:j?300:400,borderRadius:2,backgroundColor:n.palette.grey[50]},children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(Ci,{data:g==null?void 0:g.histogramData,margin:{top:20,right:30,left:20,bottom:j?60:80},children:[e.jsx(Ye,{strokeDasharray:"3 3",stroke:n.palette.divider}),e.jsx(Xe,{dataKey:"bin",angle:j?-90:-45,textAnchor:"end",height:j?60:80,interval:0,tick:{fontSize:j?10:12}}),e.jsx(He,{tick:{fontSize:j?10:12}}),e.jsx(Re,{contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:8}}),e.jsx(ut,{}),e.jsx(St,{dataKey:"frequency",fill:n.palette.primary.main,name:"Frequency",radius:[4,4,0,0]}),e.jsx(bs,{type:"monotone",dataKey:"normalCurve",stroke:n.palette.error.main,strokeWidth:2,dot:!1,name:"Normal Distribution"})]})})}),C=()=>e.jsx(H,{variant:"outlined",sx:{p:2,height:j?300:400,borderRadius:2,backgroundColor:n.palette.grey[50]},children:e.jsx(ze,{width:"100%",height:"100%",children:e.jsxs(Si,{margin:{top:20,right:30,left:20,bottom:j?60:80},children:[e.jsx(Ye,{strokeDasharray:"3 3",stroke:n.palette.divider}),e.jsx(Xe,{type:"number",dataKey:"theoretical",name:"Theoretical Quantiles",tick:{fontSize:j?10:12},label:{value:"Theoretical Quantiles",position:"insideBottom",offset:j?-40:-70}}),e.jsx(He,{type:"number",dataKey:"sample",name:"Sample Quantiles",tick:{fontSize:j?10:12},label:{value:"Sample Quantiles",angle:-90,position:"insideLeft"}}),e.jsx(Re,{cursor:{strokeDasharray:"3 3"},contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:8}}),e.jsx(ki,{name:"Q-Q Plot",data:g==null?void 0:g.qqPlotData,fill:n.palette.primary.main}),e.jsx(vt,{stroke:n.palette.error.main,strokeDasharray:"3 3",segment:g!=null&&g.qqPlotData?[{x:Math.min(...g.qqPlotData.map(u=>u.theoretical)),y:Math.min(...g.qqPlotData.map(u=>u.sample))},{x:Math.max(...g.qqPlotData.map(u=>u.theoretical)),y:Math.max(...g.qqPlotData.map(u=>u.sample))}]:void 0})]})})});return e.jsxs(x,{sx:{p:{xs:2,md:3},maxWidth:1400,mx:"auto"},children:[e.jsxs(x,{sx:{mb:4},children:[e.jsx(a,{variant:"h4",gutterBottom:!0,sx:{fontWeight:600,color:n.palette.primary.main},children:"Normality Testing"}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Test whether your data follows a normal distribution using statistical tests and visual diagnostics"})]}),e.jsxs(Be,{elevation:2,sx:{mb:3},children:[e.jsx(Ss,{title:"Configuration",avatar:e.jsx(ws,{color:"primary"}),sx:{pb:1}}),e.jsxs(Ee,{children:[e.jsxs(c,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{fullWidth:!0,children:[e.jsx(Pe,{children:"Dataset"}),e.jsx(Oe,{value:m,label:"Dataset",onChange:De,disabled:y.length===0,children:y.length===0?e.jsx(B,{value:"",disabled:!0,children:"No datasets available"}):y.map(u=>e.jsx(B,{value:u.id,children:e.jsxs(x,{children:[e.jsx(a,{variant:"body2",children:u.name}),e.jsxs(a,{variant:"caption",color:"text.secondary",children:[u.data.length," rows"]})]})},u.id))})]})}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(qe,{fullWidth:!0,children:[e.jsx(Pe,{children:"Numeric Variable"}),e.jsx(Oe,{value:v,label:"Numeric Variable",onChange:se,disabled:he.length===0,children:he.length===0?e.jsx(B,{value:"",disabled:!0,children:"No numeric variables available"}):he.map(u=>e.jsxs(B,{value:u.id,children:[e.jsx(a,{variant:"body2",children:u.name}),e.jsx(a,{variant:"caption",color:"text.secondary",sx:{ml:1},children:"(Numeric)"})]},u.id))})]})})]}),e.jsxs(c,{container:!0,spacing:3,sx:{mb:3},children:[e.jsxs(c,{item:!0,xs:12,md:8,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Normality Tests to Run"}),e.jsxs(qe,{fullWidth:!0,children:[e.jsx(Pe,{children:"Select Tests"}),e.jsxs(Oe,{multiple:!0,value:N,onChange:u=>te(u.target.value),label:"Select Tests",renderValue:u=>e.jsx(x,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:u.map(F=>e.jsx(Ae,{label:F==="auto"?"Auto (Recommended)":F==="shapiroWilk"?"Shapiro-Wilk":F==="kolmogorovSmirnov"?"Kolmogorov-Smirnov":F==="jarqueBera"?"Jarque-Bera":F==="andersonDarling"?"Anderson-Darling":F,size:"small"},F))}),children:[e.jsxs(B,{value:"auto",children:[e.jsx(a,{variant:"body2",children:e.jsx("strong",{children:"Auto (Recommended)"})}),e.jsx(a,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:"Automatically selects the best tests based on sample size"})]}),e.jsxs(B,{value:"shapiroWilk",children:[e.jsx(a,{variant:"body2",children:"Shapiro-Wilk Test"}),e.jsx(a,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:"Most powerful for small to medium samples (n ≤ 50)"})]}),e.jsxs(B,{value:"kolmogorovSmirnov",children:[e.jsx(a,{variant:"body2",children:"Kolmogorov-Smirnov Test"}),e.jsx(a,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:"Good for larger samples, tests overall distribution shape"})]}),e.jsxs(B,{value:"jarqueBera",children:[e.jsx(a,{variant:"body2",children:"Jarque-Bera Test"}),e.jsx(a,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:"Based on skewness and kurtosis, good for detecting specific deviations"})]}),e.jsxs(B,{value:"andersonDarling",children:[e.jsx(a,{variant:"body2",children:"Anderson-Darling Test"}),e.jsx(a,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:"More sensitive to deviations in distribution tails"})]})]})]})]}),e.jsxs(c,{item:!0,xs:12,md:4,children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Significance Level (α)"}),e.jsxs(qe,{fullWidth:!0,children:[e.jsx(Pe,{children:"Alpha Level"}),e.jsxs(Oe,{value:W,onChange:u=>ce(u.target.value),label:"Alpha Level",children:[e.jsx(B,{value:.01,children:"0.01 (99% confidence)"}),e.jsx(B,{value:.05,children:"0.05 (95% confidence)"}),e.jsx(B,{value:.1,children:"0.10 (90% confidence)"})]})]})]})]}),g&&e.jsxs(x,{sx:{mb:3},children:[e.jsx(a,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Display Mode"}),e.jsxs(Ms,{value:A,exclusive:!0,onChange:ve,size:"small",sx:{mb:2},children:[e.jsxs(Ne,{value:"tabs","aria-label":"tabs view",children:[e.jsx(bi,{sx:{mr:1}}),!j&&"Tabs"]}),e.jsxs(Ne,{value:"stacked","aria-label":"stacked view",children:[e.jsx(Ks,{sx:{mr:1}}),!j&&"Stacked"]}),!j&&e.jsxs(Ne,{value:"side-by-side","aria-label":"side by side view",children:[e.jsx(_s,{sx:{mr:1}}),"Side by Side"]})]}),e.jsx(a,{variant:"caption",color:"text.secondary",display:"block",children:"Choose how to display the statistics and visualizations"})]}),e.jsxs(Qs,{defaultExpanded:!1,children:[e.jsx(Gs,{expandIcon:e.jsx(Hs,{}),children:e.jsx(a,{variant:"subtitle1",sx:{fontWeight:500},children:"About Normality Testing"})}),e.jsxs(Us,{children:[e.jsx(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Normality testing assesses whether your data follows a normal (Gaussian) distribution. This test includes:"}),e.jsxs(x,{component:"ul",sx:{pl:2},children:[e.jsxs(a,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Kolmogorov-Smirnov test:"})," Statistical test comparing your data to a normal distribution"]}),e.jsxs(a,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Descriptive statistics:"})," Mean, standard deviation, skewness, and kurtosis"]}),e.jsxs(a,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Q-Q Plot:"})," Visual comparison of your data quantiles vs. theoretical normal quantiles"]}),e.jsxs(a,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Histogram with normal curve:"})," Distribution shape comparison"]})]})]})]}),e.jsxs(x,{sx:{mt:3,display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(Te,{variant:"contained",size:"large",startIcon:e.jsx(As,{}),onClick:Ve,disabled:Q||!v,sx:{px:4,borderRadius:2,textTransform:"none",fontWeight:600},children:Q?"Testing...":"Test for Normality"}),g&&e.jsx(Te,{variant:"outlined",size:"large",onClick:L,sx:{px:3,borderRadius:2,textTransform:"none"},children:"Clear Results"})]})]})]}),Q&&e.jsx(Be,{elevation:2,children:e.jsx(Ee,{children:e.jsxs(x,{display:"flex",flexDirection:"column",alignItems:"center",py:4,children:[e.jsx(Jt,{size:60,sx:{mb:2}}),e.jsx(a,{variant:"h6",color:"text.secondary",children:"Testing for Normality..."}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Calculating statistics and generating plots"})]})})}),D&&e.jsx(tt,{severity:"error",sx:{mb:3,borderRadius:2},action:e.jsx(Te,{color:"inherit",size:"small",onClick:()=>pe(null),children:"Dismiss"}),children:D}),g&&!Q&&e.jsxs(Be,{elevation:2,children:[e.jsx(Ss,{title:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Nt,{color:"primary"}),e.jsxs(a,{variant:"h6",children:["Normality Test Results: ",g.column.name]})]}),subheader:e.jsx(a,{variant:"body2",color:"text.secondary",children:"Statistical assessment of normal distribution"})}),e.jsx(Ee,{children:A==="tabs"?e.jsxs(x,{children:[e.jsxs(Yt,{value:K,onChange:(u,F)=>O(F),sx:{borderBottom:1,borderColor:"divider",mb:2},children:[e.jsx(je,{label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(As,{fontSize:"small"}),!j&&"Normality Tests"]})}),e.jsx(je,{label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ht,{fontSize:"small"}),!j&&"Statistics"]})}),e.jsx(je,{label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(it,{fontSize:"small"}),!j&&"Histogram"]})}),e.jsx(je,{label:e.jsxs(x,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ji,{fontSize:"small"}),!j&&"Q-Q Plot"]})})]}),e.jsx(ms,{value:K,index:0,children:Ge()}),e.jsx(ms,{value:K,index:1,children:e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,md:6,children:o()}),e.jsx(c,{item:!0,xs:12,md:6,children:e.jsxs(H,{sx:{p:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500},children:"Interpretation"}),e.jsx(a,{variant:"body2",component:"pre",sx:{whiteSpace:"pre-line"},children:Ke(g)})]})})]})}),e.jsxs(ms,{value:K,index:2,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Histogram with Normal Curve Overlay"}),f()]}),e.jsxs(ms,{value:K,index:3,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Q-Q Plot (Normal Probability Plot)"}),C()]})]}):A==="stacked"?e.jsxs(ps,{spacing:4,children:[e.jsxs(x,{children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Statistics & Interpretation"}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,lg:6,children:o()}),e.jsx(c,{item:!0,xs:12,lg:6,children:e.jsxs(H,{sx:{p:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500},children:"Interpretation"}),e.jsx(a,{variant:"body2",component:"pre",sx:{whiteSpace:"pre-line"},children:Ke(g)})]})})]})]}),e.jsxs(x,{children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Histogram with Normal Curve Overlay"}),f()]}),e.jsxs(x,{children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Q-Q Plot (Normal Probability Plot)"}),C()]})]}):e.jsxs(c,{container:!0,spacing:3,children:[e.jsxs(c,{item:!0,xs:12,lg:4,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Statistics"}),o(),e.jsxs(H,{sx:{p:3,mt:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500},children:"Interpretation"}),e.jsx(a,{variant:"body2",component:"pre",sx:{whiteSpace:"pre-line"},children:Ke(g)})]})]}),e.jsxs(c,{item:!0,xs:12,lg:4,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Histogram with Normal Curve"}),f()]}),e.jsxs(c,{item:!0,xs:12,lg:4,children:[e.jsx(a,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Q-Q Plot"}),C()]})]})})]}),!Q&&!g&&v&&e.jsx(Be,{elevation:1,children:e.jsx(Ee,{children:e.jsxs(x,{textAlign:"center",py:6,children:[e.jsx(As,{sx:{fontSize:80,color:n.palette.grey[400],mb:2}}),e.jsx(a,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"Ready to Test"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:'Click "Test for Normality" to analyze the distribution of your selected variable'})]})})}),e.jsx(Gi,{open:Ze,autoHideDuration:6e3,onClose:()=>me(!1),message:Le,anchorOrigin:{vertical:"bottom",horizontal:"center"}})]})},us=y=>{const{children:t,value:w,index:n,...j}=y;return e.jsx("div",{role:"tabpanel",hidden:w!==n,id:`descriptive-tabpanel-${n}`,"aria-labelledby":`descriptive-tab-${n}`,...j,style:{width:"100%"},children:w===n&&e.jsx(x,{sx:{pt:2},children:t})})},gs=y=>({id:`descriptive-tab-${y}`,"aria-controls":`descriptive-tabpanel-${y}`}),di={descriptives:0,frequencies:1,crosstabs:2,normality:3},Da=({initialTab:y=""})=>{const t=Vt(),[w,n]=b.useState(0);b.useEffect(()=>{y&&di[y]!==void 0&&n(di[y])},[y]);const j=(U,m)=>{n(m)};return e.jsx(x,{sx:{width:"100%"},children:e.jsxs(H,{elevation:1,sx:{width:"100%"},children:[e.jsxs(Yt,{value:w,onChange:j,"aria-label":"Descriptive Statistics Tabs",variant:"fullWidth",sx:{borderBottom:1,borderColor:"divider",backgroundColor:t.palette.background.paper},children:[e.jsx(je,{icon:e.jsx(Es,{}),label:"Descriptives",...gs(0),sx:{py:2}}),e.jsx(je,{icon:e.jsx(it,{}),label:"Frequencies",...gs(1),sx:{py:2}}),e.jsx(je,{icon:e.jsx(Hi,{}),label:"Cross Tabulation",...gs(2),sx:{py:2}}),e.jsx(je,{icon:e.jsx(ks,{}),label:"Normality Test",...gs(3),sx:{py:2}})]}),e.jsxs(x,{sx:{p:0},children:[e.jsx(us,{value:w,index:0,children:e.jsx(Sa,{})}),e.jsx(us,{value:w,index:1,children:e.jsx(ka,{})}),e.jsx(us,{value:w,index:2,children:e.jsx(qa,{})}),e.jsx(us,{value:w,index:3,children:e.jsx(wa,{})})]})]})})};export{Da as default};
