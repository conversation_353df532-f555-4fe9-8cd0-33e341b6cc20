import{u as mt,j as e,G as u,e as l,R as re,ai as te,b9 as ae,ba as se,bb as P,B as q,aj as ke,bc as Me,f as le,a2 as xt,g as qe,ae as yt,bS as Ct,bT as St,bU as It,a_ as Et,ak as wt,bV as kt,aE as ft,I as jt,b7 as Mt,ca as Tt,bH as At,ah as pe,ao as Ge,ap as Ue,aq as ht,ar as J,as as o,at as He,h as Nt,bv as _t,a6 as Rt,a7 as ze,am as Pt,k as ot,l as ct,ad as $t,bt as Vt}from"./mui-libs-CfwFIaTD.js";import{r as g}from"./react-libs-Cr2nE3UY.js";import{a as vt,D as W,g as Ft,V as zt}from"./index-Bpan7Tbe.js";import{l as Oe,k as Lt,P as Bt}from"./charts-plotly-BhN4fPIu.js";import{j as Dt}from"./other-utils-CR9xr_gI.js";import{c as dt}from"./coxRegressionService-CqIkroID.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-recharts-d3-BEF1Y_jn.js";Lt(Bt);class We{static calculateKM(n,h,c=.95){const m=n.map((w,b)=>({time:w,event:h[b]})).sort((w,b)=>w.time-b.time),k=[...new Set(m.map(w=>w.time))].sort((w,b)=>w-b),U=[],T=this.getZScore(c);U.push({time:0,n_risk:m.length,n_events:0,n_censored:0,survival:1,std_err:0,lower_ci:1,upper_ci:1,variance:0});let z=U[0];return k.forEach(w=>{if(w===0)return;const b=m.filter(y=>y.time>=w).length,V=m.filter(y=>y.time===w&&y.event===1).length,D=m.filter(y=>y.time===w&&y.event===0).length;if(b>0){let y=z.survival,X=z.variance||0;V>0&&(y*=(b-V)/b,b-V>0&&(X+=V/(b*(b-V))));const L=y*Math.sqrt(X);let ee,Z;if(y>0&&y<1&&L>0&&!isNaN(L)&&isFinite(L)){const j=Math.log(-Math.log(y)),Y=y*Math.abs(Math.log(y));if(Y>0){const F=L/Y;ee=Math.exp(-Math.exp(j+T*F)),Z=Math.exp(-Math.exp(j-T*F))}else ee=y,Z=y}else ee=y,Z=y;const H={time:w,n_risk:b,n_events:V,n_censored:D,survival:y,std_err:isNaN(L)||!isFinite(L)?0:L,lower_ci:Math.max(0,Math.min(1,isNaN(ee)?y:ee)),upper_ci:Math.max(0,Math.min(1,isNaN(Z)?y:Z)),variance:isNaN(X)||!isFinite(X)?z.variance:X};U.push(H),z=H}}),{estimates:U}}static logRankTest(n,h,c,m){const k=[...new Set([...n.filter((V,D)=>h[D]===1),...c.filter((V,D)=>m[D]===1)])].sort((V,D)=>V-D);let U=0,T=0,z=0;k.forEach(V=>{const D=n.filter(H=>H>=V).length,y=c.filter(H=>H>=V).length,X=D+y,L=n.filter((H,j)=>H===V&&h[j]===1).length,ee=c.filter((H,j)=>H===V&&m[j]===1).length,Z=L+ee;if(X>0&&Z>0){U+=L;const H=D/X*Z;T+=H,X>1&&(z+=D*y*Z*(X-Z)/(X*X*(X-1)))}});const w=z>0?Math.pow(U-T,2)/z:0,b=1-Dt.chisquare.cdf(w,1);return{test_statistic:w,p_value:b}}static getZScore(n){return n===.95?1.96:n===.99?2.576:n===.9?1.645:1.96}static chiSquareCDF(n,h){return h===1&&n>=0?n>7.8?.995:n>3.84?.95:n>2.71?.9:n/10:this.gammaCDF(n/2,h/2)}static normalCDF(n){const h=1/(1+.2316419*Math.abs(n));let m=.3989423*Math.exp(-n*n/2)*h*(.3193815+h*(-.3565638+h*(1.781478+h*(-1.821256+h*1.330274))));return n>0&&(m=1-m),m}static gammaFunction(n){const h=[676.5203681218851,-1259.1392167224028,771.3234287776531,-176.6150291621406,12.507343278686905,-.13857109526572012,9984369578019572e-21,15056327351493116e-23];if(n<.5)return Math.PI/(Math.sin(Math.PI*n)*this.gammaFunction(1-n));n-=1;let c=.9999999999998099;for(let k=0;k<h.length;k++)c+=h[k]/(n+k+1);const m=n+h.length-.5;return Math.sqrt(2*Math.PI)*Math.pow(m,n+.5)*Math.exp(-m)*c}static incompleteGammaFunction(n,h){if(h<0||n<=0)return 0;if(h<n+1){let c=1/n,m=1/n;for(let k=1;k<100&&(m*=h/(n+k),c+=m,!(Math.abs(m)<Math.abs(c)*1e-7));k++);return Math.pow(h,n)*Math.exp(-h)*c}else{let c=0,m=1/n;c=m;for(let k=1;k<100&&(m*=h/(n+k),c+=m,!(Math.abs(m)<Math.abs(c)*1e-10));++k);return c*Math.exp(-h+n*Math.log(h))}}static gammaCDF(n,h){return n<=0?0:h===1?1-Math.exp(-n):n>10+h*2?.999:n<h/2?.001:.5}static calculateMedianSurvival(n,h=.95){const c=n.filter(b=>!isNaN(b.survival)&&b.survival!==null);if(c.length===0)return{median:null,ciLower:null,ciUpper:null};const m=c.find(b=>b.survival<=.5);if(!m){c[c.length-1];const b=c.find(D=>D.lower_ci<=.5&&!isNaN(D.lower_ci)),V=c.find(D=>D.upper_ci<=.5&&!isNaN(D.upper_ci));return{median:null,ciLower:b?b.time:null,ciUpper:V?V.time:null}}const k=m.time,U=c.find(b=>b.lower_ci<=.5&&!isNaN(b.lower_ci)),T=U?U.time:null,z=c.find(b=>b.upper_ci<=.5&&!isNaN(b.upper_ci)),w=z?z.time:null;return{median:k,ciLower:T,ciUpper:w}}}const Ot=N=>isNaN(N)||N===null?"N/A":N<.001?"< 0.001":N.toFixed(4),bt=(N,n)=>{if(!N||typeof N!="string"||!N.startsWith("#"))return`rgba(128, 128, 128, ${n})`;const h=parseInt(N.slice(1,3),16),c=parseInt(N.slice(3,5),16),m=parseInt(N.slice(5,7),16);return isNaN(h)||isNaN(c)||isNaN(m)?`rgba(128, 128, 128, ${n})`:`rgba(${h}, ${c}, ${m}, ${n})`},Wt=({timeVariable:N,eventVariable:n,groupVariable:h,confLevel:c,loading:m,setLoading:k,error:U,setError:T,categoricalColumns:z,groupColors:w,setGroupVariable:b,selectedDatasetId:V,setSelectedDatasetId:D,setTimeVariable:y,setEventVariable:X,setConfLevel:L,handleDatasetChange:ee,numericColumns:Z})=>{var et,tt;const{datasets:H,currentDataset:j}=vt(),Y=mt(),F=g.useRef(null),[A,ie]=g.useState(null),[Te,be]=g.useState(!0),[ye,K]=g.useState(!0),[$,xe]=g.useState(!0),[Ne,_e]=g.useState(!1),[Ke,fe]=g.useState(!1),[Ce,oe]=g.useState([]),[Pe,Ae]=g.useState(null),[ce,x]=g.useState(null),de={title:"Kaplan-Meier Survival Curve",xAxisLabel:"Time",yAxisLabel:"Survival Probability",yAxisMin:0,yAxisMax:1.05,yAxisTickInterval:.1},[f,Xe]=g.useState(de);g.useEffect(()=>{if(x(null),oe([]),Ae(null),fe(!1),j&&n){const i=j.columns.find(v=>v.id===n);if(i&&i.type==="categorical"){const v=[...new Set(j.data.map(M=>M[i.name]).filter(M=>M!=null&&String(M).trim()!==""))].map(String);v.length===2?(oe(v),fe(!0),T(null)):v.length>0?T(`Categorical event variable "${i.name}" must have exactly two unique non-missing values. Found ${v.length}: ${v.join(", ")}. Please choose a numeric (0/1) or binary categorical column.`):v.length===0&&j.data.length>0&&T(`Categorical event variable "${i.name}" has no valid data or only missing values.`)}else T(null)}},[n,j==null?void 0:j.id]);const $e=()=>{if(fe(!1),!ce){const i=j==null?void 0:j.columns.find(v=>v.id===n);i==null||i.type}},Re=i=>{Ae(i.target.value)},Ee=()=>{if(Pe&&Ce.length===2){const i=Pe,v=Ce.find(M=>M!==i);x({valueForOne:i,valueForZero:v}),fe(!1),T(null)}},ue=(i,v)=>{Xe(M=>({...M,[i]:v}))},Ze=()=>{if(!j||!N||!n){T("Please select dataset, time, and event variables.");return}k(!0),T(null),ie(null);try{const i=j.columns.find(S=>S.id===N),v=j.columns.find(S=>S.id===n),M=h?j.columns.find(S=>S.id===h):null;if(!i||!v)throw new Error("Selected time or event variables not found in the dataset.");if(v.type==="categorical"&&!ce){const S=[...new Set(j.data.map(_=>_[v.name]).filter(_=>_!=null&&String(_).trim()!==""))].map(String);S.length===2?(oe(S),Ae(null),fe(!0),T("Event variable mapping is required. Please complete the mapping.")):T(`Categorical event variable "${v.name}" must have exactly two unique values. Cannot proceed.`),k(!1);return}const he=[],me=M?new Set:null;if(j.data.forEach(S=>{const _=S[i.name],t=S[v.name];let a;if(v.type==="categorical"&&ce){const s=String(t);s===ce.valueForOne?a=1:s===ce.valueForZero&&(a=0)}else v.type==="numeric"&&(t===1||t===!0?a=1:(t===0||t===!1)&&(a=0));if(typeof _=="number"&&!isNaN(_)&&_>=0&&a!==void 0&&(a===0||a===1)){const s={time:_,event:a};if(M){const d=S[M.name];d!=null&&(s.group=String(d),me==null||me.add(String(d)))}he.push(s)}}),he.length===0)throw new Error("No valid survival data found after processing. Check variable selections, data types, and mappings.");const E=[],C=[];let ne;if(M&&me&&me.size>0){const S=Array.from(me).sort(),_={};if(S.forEach(t=>_[t]=he.filter(a=>a.group===t)),S.forEach(t=>{if(_[t]&&_[t].length>0){const a=_[t].map(r=>r.time),s=_[t].map(r=>r.event),d=We.calculateKM(a,s,c);d.estimates.forEach(r=>E.push({time:r.time,nRisk:r.n_risk,nEvent:r.n_events,nCensor:r.n_censored,survival:r.survival,stdError:r.std_err,ciLower:r.lower_ci,ciUpper:r.upper_ci,group:t}));const B=We.calculateMedianSurvival(d.estimates,c);C.push({...B,group:t})}}),S.length>=2){const t=_[S[0]],a=_[S[1]];if((t==null?void 0:t.length)>0&&(a==null?void 0:a.length)>0){const s=t.map(Q=>Q.time),d=t.map(Q=>Q.event),B=a.map(Q=>Q.time),r=a.map(Q=>Q.event),G=We.logRankTest(s,d,B,r);ne={chiSquare:G.test_statistic,df:1,pValue:G.p_value}}}}else{const S=he.map(t=>t.time),_=he.map(t=>t.event);if(S.length>0){const t=We.calculateKM(S,_,c);t.estimates.forEach(s=>E.push({time:s.time,nRisk:s.n_risk,nEvent:s.n_events,nCensor:s.n_censored,survival:s.survival,stdError:s.std_err,ciLower:s.lower_ci,ciUpper:s.upper_ci}));const a=We.calculateMedianSurvival(t.estimates,c);C.push(a)}}if(E.length===0)throw new Error("No survival estimates could be calculated. Ensure data is available for selected variables/groups.");ie({survivalTable:E,medianSurvival:C,logRankTest:ne})}catch(i){console.error("Kaplan-Meier calculation error:",i),T(i instanceof Error?i.message:"An error occurred during Kaplan-Meier calculation")}finally{k(!1)}};g.useEffect(()=>{if(A&&F.current&&!m){const{data:i,layout:v}=Je(),M={responsive:!0,displaylogo:!1};Oe.react(F.current,i,v,M)}else!A&&F.current&&Oe.purge(F.current);return()=>{F.current&&Oe.purge}},[A,f,ye,$,Y.palette.mode,m]);const Je=()=>{if(!A)return{data:[],layout:{}};const i=[...new Set(A.survivalTable.map(E=>E.group))],v=[],M=[],he={title:{text:f.title,x:.05,xanchor:"left"},xaxis:{title:{text:f.xAxisLabel},zeroline:!1,range:[f.xAxisMin,f.xAxisMax].filter(E=>E!==void 0),dtick:f.xAxisTickInterval,autorange:f.xAxisMin===void 0&&f.xAxisMax===void 0,showticklabels:!0,tickmode:"array",tickvals:Array.from({length:((f.xAxisMax??100)-(f.xAxisMin??0))/(f.xAxisTickInterval??10)+1},(E,C)=>(f.xAxisMin??0)+C*(f.xAxisTickInterval??10)).filter(E=>E!==0)},yaxis:{title:{text:f.yAxisLabel},range:[f.yAxisMin??0,f.yAxisMax??1.05],zeroline:!1,dtick:f.yAxisTickInterval,autorange:f.yAxisMin===void 0&&f.yAxisMax===void 0,showticklabels:!0,tickmode:"array",tickvals:Array.from({length:((f.yAxisMax??1.05)-(f.yAxisMin??0))/(f.yAxisTickInterval??.1)+1},(E,C)=>(f.yAxisMin??0)+C*(f.yAxisTickInterval??.1)).filter(E=>E!==0)},hovermode:"closest",showlegend:!0,margin:{t:50,b:50,l:60,r:20},legend:{y:.5,yanchor:"auto",traceorder:"normal"},plot_bgcolor:Y.palette.background.paper,paper_bgcolor:Y.palette.background.paper,font:{color:Y.palette.text.primary}};if(!i.some(E=>E!==void 0)||i.length===0||i.length===1&&i[0]===void 0){const E=A.survivalTable.filter(C=>C.group===void 0);if(E.length===0&&A.survivalTable.length>0,E.length>0&&(v.push({x:E.map(C=>C.time),y:E.map(C=>C.survival),mode:"lines",type:"scatter",name:h?"Overall":"Survival",line:{shape:"hv",color:Y.palette.primary.main}}),ye&&(v.push({x:E.map(C=>C.time),y:E.map(C=>C.ciUpper),mode:"lines",type:"scatter",name:"Upper CI",line:{width:0,shape:"hv"},hoverinfo:"skip",showlegend:!1}),v.push({x:E.map(C=>C.time),y:E.map(C=>C.ciLower),mode:"lines",type:"scatter",name:"Lower CI",line:{width:0,shape:"hv"},fill:"tonexty",fillcolor:bt(Y.palette.primary.main,.2),hoverinfo:"skip",showlegend:!1})),$&&E.some(C=>C.nCensor>0))){const C=E.filter(ne=>ne.nCensor>0);M.push({x:C.map(ne=>ne.time),y:C.map(ne=>ne.survival),mode:"markers",type:"scatter",name:"Censored",marker:{symbol:"line-ns-open",size:8,color:Y.palette.primary.main},hoverinfo:"skip",showlegend:!0})}}else i.filter(C=>C!==void 0).sort().forEach((C,ne)=>{const S=A.survivalTable.filter(t=>t.group===C);if(S.length===0)return;const _=w[ne%w.length]||Y.palette.augmentColor({color:{main:"#000000"}}).main;if(v.push({x:S.map(t=>t.time),y:S.map(t=>t.survival),mode:"lines",type:"scatter",name:String(C),line:{shape:"hv",color:_}}),ye&&(v.push({x:S.map(t=>t.time),y:S.map(t=>t.ciUpper),mode:"lines",type:"scatter",name:`${C} Upper CI`,line:{width:0,shape:"hv"},hoverinfo:"skip",showlegend:!1}),v.push({x:S.map(t=>t.time),y:S.map(t=>t.ciLower),mode:"lines",type:"scatter",name:`${C} Lower CI`,line:{width:0,shape:"hv"},fill:"tonexty",fillcolor:bt(_,.2),hoverinfo:"skip",showlegend:!1})),$&&S.some(t=>t.nCensor>0)){const t=S.filter(a=>a.nCensor>0);M.push({x:t.map(a=>a.time),y:t.map(a=>a.survival),mode:"markers",type:"scatter",name:`${C} Censored`,marker:{symbol:"line-ns-open",size:8,color:_},hoverinfo:"skip",showlegend:!1})}}),$&&M.length>0&&v.push({x:[null],y:[null],mode:"markers",type:"scatter",name:"Censored",marker:{symbol:"line-ns-open",size:8,color:Y.palette.text.secondary}});return{data:v.concat(M),layout:he}},it=()=>{F.current&&A&&!m?Oe.downloadImage(F.current,{format:"svg",filename:(f.title||"kaplan_meier_plot").replace(/\s+/g,"_"),width:F.current.offsetWidth||800,height:F.current.offsetHeight||600}):T("Chart data not available for download or analysis is running.")},nt=()=>{Xe(de)},at=((et=j==null?void 0:j.columns.find(i=>i.id===n))==null?void 0:et.type)==="categorical",st=m||!j||!N||!n||at&&!ce,Ye=!A||m,Qe=j?[...Z,...z]:[];return e.jsxs(u,{container:!0,spacing:3,children:[e.jsxs(u,{item:!0,xs:12,children:[e.jsx(l,{variant:"h5",gutterBottom:!0,children:"Kaplan-Meier Analysis"}),e.jsxs(re,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(l,{variant:"subtitle1",gutterBottom:!0,children:"Variable Selection"}),e.jsxs(u,{container:!0,spacing:2,children:[e.jsx(u,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"dataset-select-label",children:"Dataset"}),e.jsx(se,{labelId:"dataset-select-label",value:V,label:"Dataset",onChange:i=>{console.log("Dataset selection event:",i);const v=i.target.value;console.log("Setting selectedDatasetId to:",v),D(v),ee(i)},renderValue:i=>{if(console.log("Rendering dataset:",i),!i)return"Select a dataset";const v=H.find(M=>M.id===i);return v?`${v.name} (${v.data.length} rows)`:i},children:H.map(i=>e.jsxs(P,{value:i.id,children:[i.name," (",i.data.length," rows)"]},i.id))})]})}),e.jsx(u,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",disabled:!j,children:[e.jsx(ae,{id:"time-var-label",children:"Time Variable"}),e.jsx(se,{labelId:"time-var-label",value:N,label:"Time Variable",onChange:i=>y(i.target.value),children:Z.map(i=>e.jsx(P,{value:i.id,children:i.name},i.id))})]})}),e.jsx(u,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",disabled:!j,children:[e.jsx(ae,{id:"event-var-label",children:"Event Variable"}),e.jsx(se,{labelId:"event-var-label",value:n,label:"Event Variable",onChange:i=>{X(i.target.value),x(null),Ae(null),oe([])},children:Qe.map(i=>e.jsxs(P,{value:i.id,children:[i.name," ",i.type==="categorical"?"(Cat)":""]},i.id))}),e.jsx(l,{variant:"caption",color:"text.secondary",sx:{fontSize:"0.7rem"},children:"Numeric: 1=Event, 0=Censor. Categorical: Will prompt for 0/1 mapping."})]})}),e.jsx(u,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"conf-level-label",children:"Confidence Level"}),e.jsxs(se,{labelId:"conf-level-label",value:c,label:"Confidence Level",onChange:i=>L(Number(i.target.value)),children:[e.jsx(P,{value:.9,children:"90%"}),e.jsx(P,{value:.95,children:"95%"}),e.jsx(P,{value:.99,children:"99%"})]})]})})]})]}),e.jsx(q,{mb:2,children:e.jsxs(u,{container:!0,spacing:2,alignItems:"flex-start",children:[e.jsx(u,{item:!0,xs:12,md:4,children:e.jsxs(te,{fullWidth:!0,disabled:!j,children:[e.jsx(ae,{id:"group-var-label",children:"Group Variable (Optional)"}),e.jsxs(se,{labelId:"group-var-label",value:h,label:"Group Variable (Optional)",onChange:i=>b(i.target.value),children:[e.jsx(P,{value:"",children:"None"}),z.map(i=>e.jsx(P,{value:i.id,children:i.name},i.id))]})]})}),e.jsx(u,{item:!0,xs:12,md:8,children:e.jsxs(q,{display:"flex",gap:1,flexWrap:"wrap",sx:{pt:{xs:1,md:.5}},children:[e.jsx(ke,{control:e.jsx(Me,{size:"small",checked:Te,onChange:i=>be(i.target.checked)}),label:"Show Life Table"}),e.jsx(ke,{control:e.jsx(Me,{size:"small",checked:ye,onChange:i=>K(i.target.checked)}),label:"Show CI Bands"}),e.jsx(ke,{control:e.jsx(Me,{size:"small",checked:$,onChange:i=>xe(i.target.checked)}),label:"Show Censor Marks"})]})})]})}),e.jsx(le,{variant:"contained",color:"primary",startIcon:e.jsx(xt,{}),onClick:Ze,disabled:st,sx:{mb:2},children:"Run Kaplan-Meier Analysis"})]}),U&&e.jsx(u,{item:!0,xs:12,children:e.jsx(qe,{severity:"error",onClose:()=>T(null),sx:{mb:2},children:U})}),m&&e.jsx(u,{item:!0,xs:12,sx:{display:"flex",justifyContent:"center",my:3},children:e.jsx(yt,{})}),e.jsxs(Ct,{open:Ke,onClose:$e,children:[e.jsx(St,{children:"Map Event Variable Categories"}),e.jsxs(It,{children:[e.jsxs(l,{gutterBottom:!0,children:['The selected event variable "',(tt=j==null?void 0:j.columns.find(i=>i.id===n))==null?void 0:tt.name,'" has categories: ',e.jsx("strong",{children:Ce[0]})," and ",e.jsx("strong",{children:Ce[1]}),".",e.jsx("br",{}),"Please specify which value represents an event (mapped to 1). The other will be mapped to 0 (censored)."]}),e.jsx(te,{component:"fieldset",sx:{mt:2},children:e.jsx(Et,{"aria-label":"event-mapping",name:"event-mapping-group",value:Pe,onChange:Re,children:Ce.map(i=>e.jsx(ke,{value:i,control:e.jsx(wt,{}),label:`Map "${i}" to 1 (Event Occurred)`},i))})})]}),e.jsxs(kt,{children:[e.jsx(le,{onClick:$e,children:"Cancel"}),e.jsx(le,{onClick:Ee,color:"primary",disabled:!Pe,children:"Confirm Mapping"})]})]}),A&&!m&&e.jsxs(e.Fragment,{children:[e.jsx(u,{item:!0,xs:12,children:e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsxs(q,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(l,{variant:"h6",children:f.title||"Survival Curve"}),e.jsxs(q,{children:[e.jsx(ft,{title:"Download SVG",children:e.jsx("span",{children:e.jsx(jt,{onClick:it,disabled:Ye,size:"small",children:e.jsx(Mt,{})})})}),e.jsx(ft,{title:"Chart Settings",children:e.jsx(jt,{onClick:()=>_e(i=>!i),color:Ne?"primary":"default",size:"small",children:e.jsx(Tt,{})})})]})]}),e.jsxs(q,{sx:{height:{xs:300,sm:350,md:400},mt:1},children:[" ",e.jsx("div",{ref:F,id:"plotlyKaplanMeierPlotDiv",style:{width:"100%",height:"100%"}})]})]})}),Ne&&e.jsx(u,{item:!0,xs:12,children:e.jsxs(re,{elevation:2,sx:{p:2,mt:2},children:[e.jsxs(q,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(l,{variant:"subtitle1",children:"Chart Appearance"}),e.jsx(le,{size:"small",startIcon:e.jsx(At,{}),onClick:nt,children:"Reset Defaults"})]}),e.jsxs(u,{container:!0,spacing:2,children:[e.jsxs(u,{item:!0,xs:12,md:6,children:[e.jsx(pe,{fullWidth:!0,label:"Chart Title",value:f.title,onChange:i=>ue("title",i.target.value),margin:"dense",size:"small"}),e.jsx(pe,{fullWidth:!0,label:"X-Axis Label",value:f.xAxisLabel,onChange:i=>ue("xAxisLabel",i.target.value),margin:"dense",size:"small"}),e.jsx(pe,{fullWidth:!0,label:"Y-Axis Label",value:f.yAxisLabel,onChange:i=>ue("yAxisLabel",i.target.value),margin:"dense",size:"small"})]}),e.jsxs(u,{item:!0,xs:12,md:6,children:[e.jsx(l,{variant:"caption",display:"block",sx:{mt:{xs:1,md:0},mb:.5},children:"X-Axis Range & Ticks"}),e.jsxs(u,{container:!0,spacing:1,children:[e.jsx(u,{item:!0,xs:4,children:e.jsx(pe,{fullWidth:!0,label:"Min",type:"number",InputLabelProps:{shrink:!0},value:f.xAxisMin??"",onChange:i=>ue("xAxisMin",i.target.value?Number(i.target.value):void 0),size:"small",margin:"dense"})}),e.jsx(u,{item:!0,xs:4,children:e.jsx(pe,{fullWidth:!0,label:"Max",type:"number",InputLabelProps:{shrink:!0},value:f.xAxisMax??"",onChange:i=>ue("xAxisMax",i.target.value?Number(i.target.value):void 0),size:"small",margin:"dense"})}),e.jsx(u,{item:!0,xs:4,children:e.jsx(pe,{fullWidth:!0,label:"Tick Step",type:"number",InputLabelProps:{shrink:!0},value:f.xAxisTickInterval??"",onChange:i=>ue("xAxisTickInterval",i.target.value?Number(i.target.value):void 0),size:"small",margin:"dense"})})]}),e.jsx(l,{variant:"caption",display:"block",sx:{mt:1,mb:.5},children:"Y-Axis Range & Ticks"}),e.jsxs(u,{container:!0,spacing:1,children:[e.jsx(u,{item:!0,xs:4,children:e.jsx(pe,{fullWidth:!0,label:"Min",type:"number",InputLabelProps:{shrink:!0},value:f.yAxisMin??"",onChange:i=>ue("yAxisMin",i.target.value?Number(i.target.value):void 0),size:"small",margin:"dense",inputProps:{min:0,max:1,step:.05}})}),e.jsx(u,{item:!0,xs:4,children:e.jsx(pe,{fullWidth:!0,label:"Max",type:"number",InputLabelProps:{shrink:!0},value:f.yAxisMax??"",onChange:i=>ue("yAxisMax",i.target.value?Number(i.target.value):void 0),size:"small",margin:"dense",inputProps:{min:0,max:1.1,step:.05}})}),e.jsx(u,{item:!0,xs:4,children:e.jsx(pe,{fullWidth:!0,label:"Tick Step",type:"number",InputLabelProps:{shrink:!0},value:f.yAxisTickInterval??"",onChange:i=>ue("yAxisTickInterval",i.target.value?Number(i.target.value):void 0),size:"small",margin:"dense",inputProps:{min:.01,step:.01}})})]})]})]}),e.jsx(le,{variant:"outlined",onClick:()=>{if(A&&F.current&&!m){const{data:i,layout:v}=Je();Oe.react(F.current,i,v,{responsive:!0,displaylogo:!1})}},fullWidth:!0,sx:{mt:2},disabled:!A||m,children:"Apply Chart Customizations"})]})}),e.jsxs(u,{item:!0,xs:12,md:A.logRankTest?6:12,children:[" ",e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2,height:"100%"},children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Median Survival Times"}),e.jsx(Ge,{children:e.jsxs(Ue,{size:"small",children:[e.jsx(ht,{children:e.jsxs(J,{children:[h&&A.medianSurvival.some(i=>i.group)&&e.jsx(o,{children:"Group"}),e.jsx(o,{children:"Median"}),e.jsxs(o,{children:[c*100,"% CI Lower"]}),e.jsxs(o,{children:[c*100,"% CI Upper"]})]})}),e.jsx(He,{children:A.medianSurvival.map((i,v)=>{var M,he,me;return e.jsxs(J,{hover:!0,children:[h&&A.medianSurvival.some(E=>E.group)&&e.jsx(o,{children:i.group||"Overall"}),e.jsx(o,{children:((M=i.median)==null?void 0:M.toFixed(2))??"Not Reached"}),e.jsx(o,{children:((he=i.ciLower)==null?void 0:he.toFixed(2))??"N/A"}),e.jsx(o,{children:((me=i.ciUpper)==null?void 0:me.toFixed(2))??"N/A"})]},v)})})]})})]})]}),A.logRankTest&&e.jsx(u,{item:!0,xs:12,md:6,children:e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2,height:"100%"},children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Log-Rank Test"}),e.jsx(Ge,{children:e.jsx(Ue,{size:"small",children:e.jsxs(He,{children:[e.jsxs(J,{hover:!0,children:[e.jsx(o,{children:"Chi-square"}),e.jsx(o,{children:A.logRankTest.chiSquare.toFixed(4)})]}),e.jsxs(J,{hover:!0,children:[e.jsx(o,{children:"Degrees of freedom"}),e.jsx(o,{children:A.logRankTest.df})]}),e.jsxs(J,{hover:!0,children:[e.jsx(o,{children:"P-value"}),e.jsx(o,{children:e.jsx(Nt,{label:Ot(A.logRankTest.pValue),color:!isNaN(A.logRankTest.pValue)&&A.logRankTest.pValue<.05?"success":"default",size:"small"})})]})]})})}),e.jsx(l,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:!isNaN(A.logRankTest.pValue)&&A.logRankTest.pValue<.05?"Suggests a significant difference between groups.":"Suggests no significant difference between groups."})]})}),Te&&e.jsx(u,{item:!0,xs:12,children:e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2,mt:2},children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Life Table"}),e.jsx(Ge,{sx:{maxHeight:400},children:e.jsxs(Ue,{size:"small",stickyHeader:!0,children:[e.jsx(ht,{children:e.jsxs(J,{children:[h&&A.survivalTable.some(i=>i.group)&&e.jsx(o,{children:"Group"}),e.jsx(o,{children:"Time"}),e.jsx(o,{align:"right",children:"At Risk"}),e.jsx(o,{align:"right",children:"Events"}),e.jsx(o,{align:"right",children:"Censored"}),e.jsx(o,{align:"right",children:"Survival"}),e.jsx(o,{align:"right",children:"Std. Error"}),e.jsxs(o,{align:"right",children:[c*100,"% CI Lower"]}),e.jsxs(o,{align:"right",children:[c*100,"% CI Upper"]})]})}),e.jsx(He,{children:A.survivalTable.filter(i=>i.nEvent>0||i.nCensor>0||i.time===0).map((i,v)=>e.jsxs(J,{hover:!0,children:[h&&A.survivalTable.some(M=>M.group)&&e.jsx(o,{children:i.group||"Overall"}),e.jsx(o,{children:i.time.toFixed(2)}),e.jsx(o,{align:"right",children:i.nRisk}),e.jsx(o,{align:"right",children:i.nEvent}),e.jsx(o,{align:"right",children:i.nCensor}),e.jsx(o,{align:"right",children:i.survival.toFixed(4)}),e.jsx(o,{align:"right",children:i.stdError.toFixed(4)}),e.jsx(o,{align:"right",children:i.ciLower.toFixed(4)}),e.jsx(o,{align:"right",children:i.ciUpper.toFixed(4)})]},v))})]})})]})})]})]})},qt=()=>{var ne,S,_;const{datasets:N,currentDataset:n,setCurrentDataset:h}=vt();mt();const[c,m]=g.useState((n==null?void 0:n.id)||""),[k,U]=g.useState([]),[T,z]=g.useState(""),[w,b]=g.useState(""),[V,D]=g.useState(.95),[y,X]=g.useState({}),[L,ee]=g.useState(null),[Z,H]=g.useState(!1),[j,Y]=g.useState([]),[F,A]=g.useState(null),[ie,Te]=g.useState({}),[be,ye]=g.useState(""),[K,$]=g.useState(null),[xe,Ne]=g.useState({showConfidenceIntervals:!0,showRegressionEquation:!0}),[_e,Ke]=g.useState(!1),[fe,Ce]=g.useState(!1),[oe,Pe]=g.useState(!1),[Ae,ce]=g.useState(null),[x,de]=g.useState(null),[f,Xe]=g.useState(0),$e=g.useCallback(async()=>{if(!(oe||fe)){Ce(!0);try{await dt.initialize(),Pe(!0)}catch(t){console.error("Failed to initialize Python environment:",t),ce("Failed to initialize Python environment. Please refresh the page and try again.")}finally{Ce(!1)}}},[oe,fe]);g.useEffect(()=>{$e()},[$e]),g.useEffect(()=>{const t=localStorage.getItem("cox_regression_results");if(t)try{const a=JSON.parse(t);de(a)}catch(a){console.error("Error parsing saved Cox regression results:",a),localStorage.removeItem("cox_regression_results")}},[]);const Re=t=>{if(!n)return[];const a=n.columns.find(s=>s.id===t);return!a||a.type!==W.CATEGORICAL?[]:Ft(t,n)},Ee=(n==null?void 0:n.columns.filter(t=>t.type===W.NUMERIC||t.type===W.CATEGORICAL))||[],ue=(n==null?void 0:n.columns.filter(t=>t.type===W.NUMERIC))||[],Ze=(n==null?void 0:n.columns.filter(t=>{if(t.type===W.NUMERIC){const a=new Set;let s=0;for(const d of(n==null?void 0:n.data)||[]){const B=d[t.name];(B===0||B===1)&&(a.add(B),s++)}return s===((n==null?void 0:n.data.length)||0)&&a.size<=2}return t.type===W.CATEGORICAL?Re(t.id).length===2:!1}))||[],Je=t=>{const a=t.target.value;m(a),U([]),z(""),b(""),de(null),$(null),ee(null),localStorage.removeItem("cox_regression_results");const s=N.find(d=>d.id===a);s&&h(s)},it=t=>{const a=t.target.value,s=typeof a=="string"?a.split(","):a;U(s);const d={...y};Object.keys(y).forEach(B=>{s.includes(B)||delete d[B]}),X(d),de(null),$(null),localStorage.removeItem("cox_regression_results")},nt=t=>{z(t.target.value),de(null),$(null),localStorage.removeItem("cox_regression_results")},at=t=>{const a=t.target.value;b(a),de(null),$(null),ee(null),localStorage.removeItem("cox_regression_results");const s=n==null?void 0:n.columns.find(d=>d.id===a);if((s==null?void 0:s.type)===W.CATEGORICAL){const d=Re(a);d.length===2&&(Y(d),A(null),H(!0))}},st=t=>{D(Number(t.target.value)),de(null),$(null),localStorage.removeItem("cox_regression_results")},Ye=t=>{Ne(a=>({...a,[t]:!a[t]}))},Qe=(t,a)=>{Te(s=>({...s,[t]:a})),$(null)},et=t=>{ye(t),$(null)},tt=(t,a)=>{X(s=>({...s,[t]:a})),de(null),$(null)},i=(t,a)=>{Xe(a)},v=()=>{H(!1),L||b("")},M=t=>{A({event:t})},he=()=>{if(F){const t=j.find(a=>a!==F.event);t&&ee({event:F.event,nonEvent:t}),H(!1)}},me=async()=>{if(!(!x||!x.xColumns||!be||!oe))try{const t={};let a=!0;const s={};for(const p of x.xColumns)if(p.isDummy&&p.originalColumnId&&p.originalColumnName&&p.allCategories&&p.baseCategory&&!s[p.originalColumnId]){const R=ie[p.originalColumnId];if(R===void 0||String(R).trim()===""){$({error:`Please select a value for ${p.originalColumnName}`}),a=!1;break}s[p.originalColumnId]={name:p.originalColumnName,selectedValue:String(R),allCategories:p.allCategories,baseCategory:p.baseCategory}}if(!a)return;for(const p of x.xColumns){if(!a)break;if(p.isDummy&&p.originalColumnId&&p.dummyForCategory){const R=s[p.originalColumnId];if(!R){$({error:`Internal error: Missing info for ${p.originalColumnName}`}),a=!1;break}t[p.id]=R.selectedValue===p.dummyForCategory?1:0}else if(!p.isDummy){const R=ie[p.id];if(R===void 0||String(R).trim()===""){$({error:`Please enter a value for ${p.name}`}),a=!1;break}const O=parseFloat(String(R));if(isNaN(O)){$({error:`Invalid number entered for ${p.name}`}),a=!1;break}t[p.id]=O}}if(!a)return;const d=parseFloat(be);if(isNaN(d)||d<=0){$({error:"Please enter a valid positive time value"});return}const B={};let r=0;x.xColumns.forEach(p=>{const R=`covariate_${r}`;B[R]=t[p.id]||0,r++});const G=await dt.predictSurvival(x.coefficients,B,[d]),Q=[];Object.values(s).forEach(p=>{Q.push({name:p.name,value:p.selectedValue})}),x.xColumns.forEach(p=>{!p.isDummy&&t[p.id]!==void 0&&Q.push({name:p.name,value:t[p.id]})}),$({inputs:Q,time:d,hazardRatio:G.hazard_ratio,survivalProbability:G.survival_probabilities[d.toString()]||.5,riskCategory:G.hazard_ratio>1.5?"High Risk":G.hazard_ratio<.67?"Low Risk":"Average Risk"})}catch(t){$({error:t instanceof Error?t.message:String(t)})}},E=async()=>{if(!n||k.length===0||!T||!w){ce("Please select at least one independent variable, time variable, and event variable.");return}if(!oe){ce("Python environment is not ready. Please wait for initialization to complete.");return}Ke(!0),ce(null),de(null),$(null);try{const t=k.map(I=>n.columns.find(ve=>ve.id===I)).filter(Boolean),a=n.columns.find(I=>I.id===T),s=n.columns.find(I=>I.id===w);if(t.length===0||!a||!s)throw new Error("Selected variables not found in dataset.");if(s.type===W.CATEGORICAL&&!L)throw new Error(`Please specify the event mapping for "${s.name}". Re-select it to define the mapping.`);for(const I of t)if(I.type===W.CATEGORICAL&&!y[I.id])throw new Error(`Please select a base category for the categorical variable "${I.name}".`);const d=[],B=[],r={};t.forEach(I=>{if(I.type===W.NUMERIC)d.push(I),B.push(I.name),r[`covariate_${d.length-1}`]=[];else if(I.type===W.CATEGORICAL){const ve=y[I.id],Se=Re(I.id);Se.forEach(ge=>{if(ge!==ve){const Fe=`${I.name} (${ge} vs ${ve})`;B.push(Fe);const Be={id:`${I.id}_dummy_${ge}`,name:Fe,type:W.NUMERIC,role:zt.INDEPENDENT,description:`Dummy for ${I.name}, category ${ge}, base ${ve}`,isDummy:!0,originalColumnId:I.id,originalColumnName:I.name,dummyForCategory:ge,baseCategory:ve,allCategories:Se};d.push(Be),r[`covariate_${d.length-1}`]=[]}})}});const G=[],Q=[];if(n.data.forEach(I=>{const ve=I[a.name],Se=I[s.name];let ge=null;if(s.type===W.CATEGORICAL&&L?Se===L.event?ge=1:Se===L.nonEvent&&(ge=0):s.type===W.NUMERIC&&(Se===0||Se===1)&&(ge=Se),typeof ve!="number"||isNaN(ve)||ve<=0||ge===null)return;let Fe=!0;const Be=[];t.forEach(Ie=>{if(Fe){if(Ie.type===W.NUMERIC){const we=I[Ie.name];if(typeof we!="number"||isNaN(we)){Fe=!1;return}Be.push(we)}else if(Ie.type===W.CATEGORICAL){const we=y[Ie.id],De=I[Ie.name];Re(Ie.id).forEach(pt=>{pt!==we&&Be.push(De===pt?1:0)})}}}),Fe&&(G.push(ve),Q.push(ge),Be.forEach((Ie,we)=>{const De=`covariate_${we}`;r[De]||(r[De]=[]),r[De].push(Ie)}))}),G.length<10)throw new Error("Not enough valid data rows for Cox regression analysis. Need at least 10 valid rows.");const p={};for(const I in r)Object.prototype.hasOwnProperty.call(r,I)&&(p[I]=r[I]);const R={time:G,event:Q,covariates:p},O=await dt.runCoxRegression(R),je=Object.values(O.coefficients),rt=Object.values(O.std_errors),lt=Object.values(O.p_values),Le=Object.values(O.hazard_ratios),Ve={lower:Object.values(O.confidence_intervals).map(I=>I[0]),upper:Object.values(O.confidence_intervals).map(I=>I[1])},gt={coefficients:O.coefficients,hazard_ratios:O.hazard_ratios,confidence_intervals:O.confidence_intervals,p_values:O.p_values,std_errors:O.std_errors,concordance:O.concordance,log_likelihood:O.log_likelihood,aic:O.aic,n_observations:O.n_observations,n_events:O.n_events,xColumns:d,timeColumn:a,eventColumn:s,xNames:B,n:O.n_observations,events:O.n_events,coefficientsArray:je,stdErrorsArray:rt,pValuesArray:lt,hazardRatiosArray:Le,confidenceIntervalsArray:Ve};de(gt),localStorage.setItem("cox_regression_results",JSON.stringify(gt))}catch(t){console.error("Cox regression error:",t),ce(`Error in Cox regression analysis: ${t instanceof Error?t.message:String(t)}`)}finally{Ke(!1)}},C=()=>{if(!x)return"";const{coefficientsArray:t,pValuesArray:a,n:s,concordance:d,log_likelihood:B,xNames:r,events:G,stdErrorsArray:Q,confidenceIntervalsArray:p}=x;let R="";const O=r&&r.length>1?`${r.slice(0,-1).join(", ")} and ${r[r.length-1]}`:(r==null?void 0:r[0])||"";if(R+=`A Cox proportional hazards regression was conducted to analyze survival time based on ${O}. `,R+=`The model was trained on ${s} observations with ${G} events. `,R+=`

Model fit: The model achieved a concordance index of ${d.toFixed(3)}, `,d<.6?R+="indicating poor predictive ability. ":d<.7?R+="indicating acceptable predictive ability. ":d<.8?R+="indicating good predictive ability. ":R+="indicating excellent predictive ability. ",R+=`The log-likelihood was ${B.toFixed(2)}. `,R+=`

Hazard Ratios:
`,r&&t&&a&&Q&&p)for(let je=0;je<t.length;je++){const rt=t[je],lt=r[je]||`X${je+1}`,Le=a[je],Ve=Math.exp(rt);R+=`• ${lt}: HR = ${Ve.toFixed(3)} `,R+=`(95% CI: ${p.lower[je].toFixed(3)} - `,R+=`${p.upper[je].toFixed(3)}), `,R+=`p ${Le<.001?"< 0.001":"= "+Le.toFixed(3)}`,Le<.05?Ve>1?R+=` - indicates ${((Ve-1)*100).toFixed(1)}% increased hazard`:R+=` - indicates ${((1-Ve)*100).toFixed(1)}% decreased hazard`:R+=" - not statistically significant",R+=`
`}return R};return e.jsxs(q,{p:3,children:[e.jsx(l,{variant:"h5",gutterBottom:!0,children:"Cox Regression (Survival Analysis)"}),fe&&e.jsx(qe,{severity:"info",sx:{mb:2},children:e.jsxs(q,{children:[e.jsx(l,{variant:"body2",gutterBottom:!0,children:"Initializing Python environment for statistical analysis..."}),e.jsx(_t,{sx:{mt:1}})]})}),!oe&&!fe&&e.jsx(qe,{severity:"warning",sx:{mb:2},action:e.jsx(le,{color:"inherit",size:"small",onClick:$e,startIcon:e.jsx(At,{}),children:"Retry"}),children:"Python environment not ready. Cox regression requires Python libraries to be loaded."}),e.jsxs(re,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(l,{variant:"subtitle1",gutterBottom:!0,children:"Select Variables"}),e.jsxs(u,{container:!0,spacing:2,children:[e.jsx(u,{item:!0,xs:12,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"dataset-select-label",children:"Dataset"}),e.jsx(se,{labelId:"dataset-select-label",id:"dataset-select",value:c,label:"Dataset",onChange:Je,disabled:N.length===0,children:N.length===0?e.jsx(P,{value:"",disabled:!0,children:"No datasets available"}):N.map(t=>e.jsxs(P,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]})}),e.jsx(u,{item:!0,xs:12,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"independent-variables-label",children:"Covariates (X)"}),e.jsx(se,{labelId:"independent-variables-label",id:"independent-variables",multiple:!0,value:k,label:"Covariates (X)",onChange:it,disabled:!n,renderValue:t=>t.map(s=>{const d=Ee.find(B=>B.id===s);return d?d.name:""}).filter(Boolean).join(", "),children:Ee.length===0?e.jsx(P,{value:"",disabled:!0,children:"No suitable variables available"}):Ee.map(t=>e.jsxs(P,{value:t.id,children:[t.name," (",t.type===W.CATEGORICAL?"Categorical":"Numeric",")"]},t.id))}),e.jsx(l,{variant:"caption",color:"text.secondary",children:"Select predictor variables that may affect survival."})]})}),e.jsx(u,{item:!0,xs:12,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"time-variable-label",children:"Time Variable"}),e.jsx(se,{labelId:"time-variable-label",id:"time-variable",value:T,label:"Time Variable",onChange:nt,disabled:!n,children:ue.length===0?e.jsx(P,{value:"",disabled:!0,children:"No numeric variables available"}):ue.map(t=>e.jsx(P,{value:t.id,children:t.name},t.id))}),e.jsx(l,{variant:"caption",color:"text.secondary",children:"Select the time-to-event variable (must be positive)."})]})}),e.jsx(u,{item:!0,xs:12,md:3,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"event-variable-label",children:"Event Variable"}),e.jsx(se,{labelId:"event-variable-label",id:"event-variable",value:w,label:"Event Variable",onChange:at,disabled:!n,children:Ze.length===0?e.jsx(P,{value:"",disabled:!0,children:"No suitable binary variables"}):Ze.map(t=>e.jsxs(P,{value:t.id,children:[t.name," (",t.type===W.CATEGORICAL?"Categorical":"Numeric 0/1",")"]},t.id))}),e.jsx(l,{variant:"caption",color:"text.secondary",children:"1 = event, 0 = censored."}),L&&e.jsxs(l,{variant:"caption",color:"primary.main",sx:{mt:.5},children:['Mapping: "',L.event,'" = 1, "',L.nonEvent,'" = 0']})]})})]}),k.some(t=>{var a;return((a=Ee.find(s=>s.id===t))==null?void 0:a.type)===W.CATEGORICAL})&&e.jsxs(q,{mt:2,p:2,border:1,borderColor:"divider",borderRadius:1,children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Select Base Category for Categorical Variables"}),e.jsx(u,{container:!0,spacing:2,children:k.map(t=>{const a=Ee.find(s=>s.id===t);if(a&&a.type===W.CATEGORICAL){const s=Re(t);return e.jsx(u,{item:!0,xs:12,md:4,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsxs(ae,{id:`base-category-label-${t}`,children:["Base for ",a.name]}),e.jsx(se,{labelId:`base-category-label-${t}`,value:y[t]||"",label:`Base for ${a.name}`,onChange:d=>tt(t,d.target.value),children:s.length===0?e.jsx(P,{value:"",disabled:!0,children:"No categories found"}):s.map(d=>e.jsx(P,{value:d,children:d},d))})]})},t)}return null})})]}),e.jsx(q,{mt:1,children:e.jsx(u,{container:!0,spacing:2,children:e.jsx(u,{item:!0,xs:12,md:4,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:"conf-interval-label",children:"Confidence Level"}),e.jsxs(se,{labelId:"conf-interval-label",id:"conf-interval",value:V,label:"Confidence Level",onChange:st,children:[e.jsx(P,{value:.9,children:"90%"}),e.jsx(P,{value:.95,children:"95%"}),e.jsx(P,{value:.99,children:"99%"})]})]})})})}),e.jsxs(q,{mt:2,children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsxs(u,{container:!0,spacing:2,children:[e.jsx(u,{item:!0,xs:12,md:6,children:e.jsx(ke,{control:e.jsx(Me,{checked:xe.showConfidenceIntervals,onChange:()=>Ye("showConfidenceIntervals")}),label:"Show confidence intervals"})}),e.jsx(u,{item:!0,xs:12,md:6,children:e.jsx(ke,{control:e.jsx(Me,{checked:xe.showRegressionEquation,onChange:()=>Ye("showRegressionEquation")}),label:"Show regression equation"})})]})]}),e.jsx(q,{mt:2,children:e.jsx(le,{variant:"contained",color:"primary",startIcon:e.jsx(xt,{}),onClick:E,disabled:_e||!oe||k.length===0||!T||!w||((ne=n==null?void 0:n.columns.find(t=>t.id===w))==null?void 0:ne.type)===W.CATEGORICAL&&!L,children:_e?"Running Analysis...":"Run Cox Regression"})})]}),_e&&e.jsx(q,{display:"flex",justifyContent:"center",my:4,children:e.jsx(yt,{})}),Ae&&e.jsx(qe,{severity:"error",sx:{mb:3},children:Ae}),x&&!_e&&e.jsx(e.Fragment,{children:e.jsxs(re,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(q,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:e.jsxs(Rt,{value:f,onChange:i,"aria-label":"cox regression tabs",children:[e.jsx(ze,{label:"Model Summary"}),e.jsx(ze,{label:"Survival Prediction"}),e.jsx(ze,{label:"Interpretation"})]})}),f===0&&e.jsxs(q,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Cox Regression Results"}),e.jsxs(u,{container:!0,spacing:3,children:[e.jsxs(u,{item:!0,xs:12,md:6,children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Model Information"}),e.jsx(Ge,{children:e.jsx(Ue,{size:"small",children:e.jsxs(He,{children:[e.jsxs(J,{children:[e.jsx(o,{children:"Time Variable"}),e.jsx(o,{children:x.timeColumn.name})]}),e.jsxs(J,{children:[e.jsx(o,{children:"Event Variable"}),e.jsx(o,{children:x.eventColumn.name})]}),e.jsxs(J,{children:[e.jsx(o,{children:"Covariates"}),e.jsx(o,{children:((S=x.xNames)==null?void 0:S.join(", "))||"N/A"})]}),e.jsxs(J,{children:[e.jsx(o,{children:"Number of Observations"}),e.jsx(o,{children:x.n})]}),e.jsxs(J,{children:[e.jsx(o,{children:"Number of Events"}),e.jsx(o,{children:x.events})]}),e.jsxs(J,{children:[e.jsx(o,{children:"Concordance Index"}),e.jsx(o,{children:x.concordance.toFixed(3)})]}),e.jsxs(J,{children:[e.jsx(o,{children:"Log Likelihood"}),e.jsx(o,{children:x.log_likelihood.toFixed(2)})]}),e.jsxs(J,{children:[e.jsx(o,{children:"AIC"}),e.jsx(o,{children:x.aic.toFixed(2)})]})]})})})]}),e.jsxs(u,{item:!0,xs:12,md:6,children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Coefficients and Hazard Ratios"}),e.jsx(Ge,{children:e.jsxs(Ue,{size:"small",children:[e.jsx(ht,{children:e.jsxs(J,{children:[e.jsx(o,{children:"Variable"}),e.jsx(o,{align:"right",children:"Coefficient"}),e.jsx(o,{align:"right",children:"Std. Error"}),e.jsx(o,{align:"right",children:"HR"}),xe.showConfidenceIntervals&&e.jsx(o,{align:"right",children:"95% CI"}),e.jsx(o,{align:"right",children:"p-value"})]})}),e.jsx(He,{children:x.coefficientsArray&&x.xNames&&x.coefficientsArray.map((t,a)=>{var s;return e.jsxs(J,{children:[e.jsx(o,{children:((s=x.xNames)==null?void 0:s[a])||`X${a+1}`}),e.jsx(o,{align:"right",children:t.toFixed(4)}),e.jsx(o,{align:"right",children:x.stdErrorsArray[a].toFixed(4)}),e.jsx(o,{align:"right",children:Math.exp(t).toFixed(3)}),xe.showConfidenceIntervals&&e.jsxs(o,{align:"right",children:["(",x.confidenceIntervalsArray.lower[a].toFixed(3)," - ",x.confidenceIntervalsArray.upper[a].toFixed(3),")"]}),e.jsx(o,{align:"right",children:x.pValuesArray[a]<.001?"< 0.001":x.pValuesArray[a].toFixed(4)})]},a)})})]})}),xe.showRegressionEquation&&e.jsxs(q,{mt:2,p:2,sx:{backgroundColor:"background.default",borderRadius:1},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Cox Regression Equation"}),e.jsxs(l,{variant:"body1",sx:{fontFamily:"monospace"},children:["h(t|x) = h₀(t) × exp(",x.coefficientsArray&&x.xNames&&x.coefficientsArray.map((t,a)=>{var d;return`${a===0?"":t>=0?" + ":" - "}${a===0?t.toFixed(4):Math.abs(t).toFixed(4)} × ${((d=x.xNames)==null?void 0:d[a])||`X${a+1}`}`}).join(""),")"]}),e.jsx(l,{variant:"body2",color:"text.secondary",mt:1,children:"where h(t|x) is the hazard at time t given covariates x, and h₀(t) is the baseline hazard"})]})]})]})]}),f===1&&e.jsxs(q,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Survival Prediction Tool"}),e.jsxs(u,{container:!0,spacing:2,alignItems:"flex-start",children:[(()=>{if(!x||!x.xColumns)return null;const t=[],a=new Set,s=new Set;x.xColumns.forEach(r=>{r.isDummy&&r.originalColumnId?s.add(r.originalColumnId):r.isDummy||s.add(r.id)});const d=s.size,B=Math.max(3,Math.floor(12/Math.max(1,d)));return x.xColumns.forEach(r=>{r.isDummy&&r.originalColumnId&&r.originalColumnName&&r.allCategories&&r.baseCategory?a.has(r.originalColumnId)||(t.push(e.jsx(u,{item:!0,xs:12,sm:6,md:B,children:e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{id:`${r.originalColumnId}-predict-label`,children:r.originalColumnName}),e.jsx(se,{labelId:`${r.originalColumnId}-predict-label`,id:`${r.originalColumnId}-predict-select`,value:ie[r.originalColumnId]||"",label:r.originalColumnName,onChange:G=>Qe(r.originalColumnId,G.target.value),children:r.allCategories.map(G=>e.jsxs(P,{value:G,children:[G," ",G===r.baseCategory?"(Base)":""]},G))})]})},r.originalColumnId)),a.add(r.originalColumnId)):r.isDummy||t.push(e.jsx(u,{item:!0,xs:12,sm:6,md:B,children:e.jsx(pe,{label:`Value for ${r.name}`,value:ie[r.id]||"",onChange:G=>Qe(r.id,G.target.value),fullWidth:!0,type:"number",margin:"normal"})},r.id))}),t})(),e.jsx(u,{item:!0,xs:12,sm:6,md:3,children:e.jsx(pe,{label:"Time Point",value:be,onChange:t=>et(t.target.value),fullWidth:!0,type:"number",margin:"normal",helperText:"Enter time point for survival prediction"})}),e.jsx(u,{item:!0,xs:12,children:e.jsx(le,{variant:"contained",color:"primary",startIcon:e.jsx(Pt,{}),onClick:me,disabled:!x||!be||!oe||!(()=>{if(!(x!=null&&x.xColumns))return!0;let t=!0;const a=new Set;return x.xColumns.forEach(s=>{s.isDummy&&s.originalColumnId?a.add(s.originalColumnId):s.isDummy||(ie[s.id]===void 0||String(ie[s.id]).trim()==="")&&(t=!1)}),a.forEach(s=>{(ie[s]===void 0||String(ie[s]).trim()==="")&&(t=!1)}),t})(),sx:{mt:1},children:"Predict Survival"})}),K&&!K.error&&e.jsx(u,{item:!0,xs:12,children:e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2,mt:2},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Prediction Result"}),e.jsx("div",{children:e.jsxs(l,{variant:"body1",component:"div",children:["Input Values:",e.jsx("ul",{children:K.inputs.map(t=>e.jsx("li",{children:`${t.name} = ${t.value}`},t.name))})]})}),e.jsxs(l,{variant:"body1",fontWeight:"bold",children:["At time ",K.time,":"]}),e.jsxs(l,{variant:"body1",children:["Hazard Ratio: ",K.hazardRatio.toFixed(3)]}),e.jsxs(l,{variant:"body1",children:["Estimated Survival Probability: ",(K.survivalProbability*100).toFixed(1),"%"]}),e.jsxs(l,{variant:"body1",color:K.riskCategory==="High Risk"?"error.main":K.riskCategory==="Low Risk"?"success.main":"text.primary",children:["Risk Category: ",K.riskCategory]})]})}),K&&K.error&&e.jsx(u,{item:!0,xs:12,children:e.jsx(qe,{severity:"error",children:K.error})})]}),x&&e.jsxs(q,{mt:4,children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Interpreting Predictions"}),e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(l,{variant:"body2",children:"• Hazard Ratio (HR) > 1 indicates increased risk of the event occurring."}),e.jsx(l,{variant:"body2",children:"• Hazard Ratio (HR) < 1 indicates decreased risk of the event occurring."}),e.jsx(l,{variant:"body2",children:"• Survival probability represents the likelihood of surviving beyond the specified time."}),e.jsx(l,{variant:"body2",children:"• Risk categories: High Risk (HR > 1.5), Average Risk (0.67 ≤ HR ≤ 1.5), Low Risk (HR < 0.67)."})]}),xe.showRegressionEquation&&e.jsxs(q,{mt:2,p:2,sx:{backgroundColor:"background.default",borderRadius:1},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Cox Regression Equation"}),e.jsxs(l,{variant:"body1",sx:{fontFamily:"monospace"},children:["h(t|X) = h₀(t) × exp(",x.coefficientsArray&&x.xNames&&x.coefficientsArray.map((t,a)=>{var d;return`${a===0?"":t>=0?" + ":" - "}${a===0?t.toFixed(4):Math.abs(t).toFixed(4)} × ${((d=x.xNames)==null?void 0:d[a])||`X${a+1}`}`}).join(""),")"]}),e.jsx(l,{variant:"body2",color:"text.secondary",mt:1,children:"where h(t|X) is the hazard at time t given covariates X, and h₀(t) is the baseline hazard"})]})]})]}),f===2&&e.jsxs(q,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Interpretation"}),e.jsx(re,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:e.jsx(l,{variant:"body2",sx:{whiteSpace:"pre-line"},children:C()})}),e.jsxs(q,{mt:4,children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Key Cox Regression Concepts"}),e.jsxs(re,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(l,{variant:"body2",fontWeight:"bold",children:"Hazard Function"}),e.jsx(l,{variant:"body2",paragraph:!0,children:"The hazard function h(t) represents the instantaneous risk of the event occurring at time t, given survival up to time t. Cox regression models this as: h(t|X) = h₀(t) × exp(β₁X₁ + ... + βₙXₙ)."}),e.jsx(l,{variant:"body2",fontWeight:"bold",children:"Proportional Hazards Assumption"}),e.jsx(l,{variant:"body2",paragraph:!0,children:"Cox regression assumes that the hazard ratio between any two individuals is constant over time. This can be tested using Schoenfeld residuals."}),e.jsx(l,{variant:"body2",fontWeight:"bold",children:"Hazard Ratio"}),e.jsx(l,{variant:"body2",paragraph:!0,children:"The exponential of a coefficient (e^β) is the hazard ratio, representing the change in hazard for a one-unit increase in the covariate. HR > 1 indicates increased risk, HR < 1 indicates decreased risk."}),e.jsx(l,{variant:"body2",fontWeight:"bold",children:"Concordance Index"}),e.jsx(l,{variant:"body2",children:"The C-index measures the model's ability to correctly rank survival times. Values range from 0.5 (no discrimination) to 1.0 (perfect discrimination). A value of 0.7-0.8 is considered good."})]})]})]})]})}),e.jsxs(Ct,{open:Z,onClose:v,children:[e.jsx(St,{children:"Map Event Variable to Binary (0/1)"}),e.jsxs(It,{children:[e.jsxs(l,{gutterBottom:!0,children:['The selected event variable "',(_=n==null?void 0:n.columns.find(t=>t.id===w))==null?void 0:_.name,'" is categorical. Please specify which value represents the ',e.jsx("strong",{children:"event (1)"}),". The other value will be treated as ",e.jsx("strong",{children:"censored (0)"}),"."]}),e.jsx(te,{component:"fieldset",sx:{mt:2},children:j.map(t=>e.jsx(ke,{control:e.jsx(Me,{checked:(F==null?void 0:F.event)===t,onChange:()=>M(t)}),label:`Map "${t}" to 1 (Event Occurred)`},t))})]}),e.jsxs(kt,{children:[e.jsx(le,{onClick:v,color:"secondary",children:"Cancel"}),e.jsx(le,{onClick:he,color:"primary",disabled:!F,children:"Confirm Mapping"})]})]})]})},Gt=({kmResults:N,coxResults:n,timeVariable:h,eventVariable:c})=>{const[m,k]=g.useState("weibull"),[U,T]=g.useState(!1);return e.jsxs(u,{container:!0,spacing:3,children:[e.jsx(u,{item:!0,xs:12,children:e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Additional Survival Analysis Methods"})}),e.jsx(u,{item:!0,xs:12,md:6,children:e.jsx(ot,{children:e.jsxs(ct,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Parametric Survival Models"}),e.jsxs(te,{fullWidth:!0,margin:"normal",children:[e.jsx(ae,{children:"Distribution"}),e.jsxs(se,{value:m,label:"Distribution",onChange:z=>k(z.target.value),children:[e.jsx(P,{value:"exponential",children:"Exponential"}),e.jsx(P,{value:"weibull",children:"Weibull"}),e.jsx(P,{value:"lognormal",children:"Log-normal"}),e.jsx(P,{value:"gamma",children:"Gamma"}),e.jsx(P,{value:"gompertz",children:"Gompertz"})]})]}),e.jsx(le,{variant:"contained",color:"primary",fullWidth:!0,sx:{mt:2},disabled:!h||!c,children:"Fit Parametric Model"})]})})}),e.jsx(u,{item:!0,xs:12,md:6,children:e.jsx(ot,{children:e.jsxs(ct,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Competing Risks Analysis"}),e.jsx(ke,{control:e.jsx(Me,{checked:U,onChange:z=>T(z.target.checked)}),label:"Enable competing risks analysis"}),e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Analyze situations where multiple types of events can occur."}),e.jsx(le,{variant:"contained",color:"primary",fullWidth:!0,disabled:!U||!h||!c,children:"Run Competing Risks Analysis"})]})})}),e.jsx(u,{item:!0,xs:12,children:e.jsx(ot,{children:e.jsxs(ct,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Model Comparison"}),e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Compare different survival models using AIC, BIC, and likelihood ratio tests."}),e.jsx(le,{variant:"contained",color:"primary",disabled:!N&&!n,children:"Compare Models"})]})})})]})},ut=N=>{const{children:n,value:h,index:c,...m}=N;return e.jsx("div",{role:"tabpanel",hidden:h!==c,id:`survival-tabpanel-${c}`,"aria-labelledby":`survival-tab-${c}`,...m,children:h===c&&e.jsx(q,{sx:{p:3},children:n})})},ii=()=>{const{datasets:N,currentDataset:n,setCurrentDataset:h}=vt(),c=mt(),[m,k]=g.useState(0),[U,T]=g.useState((n==null?void 0:n.id)||""),[z,w]=g.useState(""),[b,V]=g.useState(""),[D,y]=g.useState(""),[X,L]=g.useState([]),[ee,Z]=g.useState(.95),[H,j]=g.useState(!1),[Y,F]=g.useState(null),A=(n==null?void 0:n.columns.filter(K=>K.type===W.NUMERIC))||[];n!=null&&n.columns;const ie=(n==null?void 0:n.columns.filter(K=>K.type===W.CATEGORICAL))||[],Te=K=>{const $=K.target.value;w(""),V(""),y(""),L([]);const xe=N.find(Ne=>Ne.id===$);xe&&h(xe)},be=(K,$)=>{k($)},ye=[c.palette.primary.main,c.palette.secondary.main,c.palette.error.main,c.palette.warning.main,c.palette.info.main,c.palette.success.main];return e.jsxs(q,{children:[e.jsx(l,{variant:"h5",gutterBottom:!0,children:"Survival Analysis"}),e.jsxs(re,{elevation:2,children:[e.jsxs(Rt,{value:m,onChange:be,"aria-label":"survival analysis tabs",children:[e.jsx(ze,{label:"Kaplan-Meier Analysis",icon:e.jsx(xt,{}),iconPosition:"start"}),e.jsx(ze,{label:"Cox Regression",icon:e.jsx($t,{}),iconPosition:"start"}),e.jsx(ze,{label:"Additional Analysis",icon:e.jsx(Vt,{}),iconPosition:"start"})]}),e.jsx(ut,{value:m,index:0,children:e.jsx(Wt,{timeVariable:z,eventVariable:b,groupVariable:D,confLevel:ee,loading:H,setLoading:j,error:Y,setError:F,categoricalColumns:ie,groupColors:ye,setGroupVariable:y,selectedDatasetId:U,setSelectedDatasetId:T,setTimeVariable:w,setEventVariable:V,setConfLevel:Z,handleDatasetChange:Te,numericColumns:A})}),e.jsx(ut,{value:m,index:1,children:e.jsx(qt,{})}),e.jsx(ut,{value:m,index:2,children:e.jsx(Gt,{kmResults:null,coxResults:null,timeVariable:z,eventVariable:b})})]})]})};export{ii as default};
