import{g as Ge,s as ze}from"./chunk-RZ5BOZE2-BWVq1HNy.js";import{_ as m,b as Xe,a as Je,s as Ze,g as et,p as tt,q as st,c as Ne,l as qe,y as it,B as rt,o as nt,r as at,u as lt}from"./FlowDiagram-CHRbazj7.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./mui-libs-CfwFIaTD.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./PublicationReadyGate-BGFbKbJc.js";import"./index-Bpan7Tbe.js";import"./other-utils-CR9xr_gI.js";var Ae=function(){var e=m(function(P,i,r,l){for(r=r||{},l=P.length;l--;r[P[l]]=i);return r},"o"),a=[1,3],u=[1,4],o=[1,5],f=[1,6],c=[5,6,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],d=[1,22],R=[2,7],h=[1,26],p=[1,27],I=[1,28],k=[1,29],A=[1,33],C=[1,34],V=[1,35],v=[1,36],x=[1,37],L=[1,38],D=[1,24],O=[1,31],w=[1,32],M=[1,30],g=[1,39],_=[1,40],y=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],$=[1,61],X=[89,90],Ce=[5,8,9,11,13,21,22,23,24,27,29,41,42,43,44,45,46,54,61,63,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Ee=[27,29],Ve=[1,70],ve=[1,71],xe=[1,72],Le=[1,73],De=[1,74],Oe=[1,75],we=[1,76],ee=[1,83],U=[1,80],te=[1,84],se=[1,85],ie=[1,86],re=[1,87],ne=[1,88],ae=[1,89],le=[1,90],ce=[1,91],oe=[1,92],de=[5,8,9,11,13,21,22,23,24,27,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Y=[63,64],Me=[1,101],Fe=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,76,77,89,90],N=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],B=[1,110],Q=[1,106],H=[1,107],K=[1,108],W=[1,109],j=[1,111],he=[1,116],ue=[1,117],me=[1,114],fe=[1,115],Se={trace:m(function(){},"trace"),yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,direction:17,styleStatement:18,classDefStatement:19,classStatement:20,direction_tb:21,direction_bt:22,direction_rl:23,direction_lr:24,requirementType:25,requirementName:26,STRUCT_START:27,requirementBody:28,STYLE_SEPARATOR:29,idList:30,ID:31,COLONSEP:32,id:33,TEXT:34,text:35,RISK:36,riskLevel:37,VERIFYMTHD:38,verifyType:39,STRUCT_STOP:40,REQUIREMENT:41,FUNCTIONAL_REQUIREMENT:42,INTERFACE_REQUIREMENT:43,PERFORMANCE_REQUIREMENT:44,PHYSICAL_REQUIREMENT:45,DESIGN_CONSTRAINT:46,LOW_RISK:47,MED_RISK:48,HIGH_RISK:49,VERIFY_ANALYSIS:50,VERIFY_DEMONSTRATION:51,VERIFY_INSPECTION:52,VERIFY_TEST:53,ELEMENT:54,elementName:55,elementBody:56,TYPE:57,type:58,DOCREF:59,ref:60,END_ARROW_L:61,relationship:62,LINE:63,END_ARROW_R:64,CONTAINS:65,COPIES:66,DERIVES:67,SATISFIES:68,VERIFIES:69,REFINES:70,TRACES:71,CLASSDEF:72,stylesOpt:73,CLASS:74,ALPHA:75,COMMA:76,STYLE:77,style:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,MINUS:86,LABEL:87,SEMICOLON:88,unqString:89,qString:90,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",21:"direction_tb",22:"direction_bt",23:"direction_rl",24:"direction_lr",27:"STRUCT_START",29:"STYLE_SEPARATOR",31:"ID",32:"COLONSEP",34:"TEXT",36:"RISK",38:"VERIFYMTHD",40:"STRUCT_STOP",41:"REQUIREMENT",42:"FUNCTIONAL_REQUIREMENT",43:"INTERFACE_REQUIREMENT",44:"PERFORMANCE_REQUIREMENT",45:"PHYSICAL_REQUIREMENT",46:"DESIGN_CONSTRAINT",47:"LOW_RISK",48:"MED_RISK",49:"HIGH_RISK",50:"VERIFY_ANALYSIS",51:"VERIFY_DEMONSTRATION",52:"VERIFY_INSPECTION",53:"VERIFY_TEST",54:"ELEMENT",57:"TYPE",59:"DOCREF",61:"END_ARROW_L",63:"LINE",64:"END_ARROW_R",65:"CONTAINS",66:"COPIES",67:"DERIVES",68:"SATISFIES",69:"VERIFIES",70:"REFINES",71:"TRACES",72:"CLASSDEF",74:"CLASS",75:"ALPHA",76:"COMMA",77:"STYLE",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",86:"MINUS",87:"LABEL",88:"SEMICOLON",89:"unqString",90:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[17,1],[17,1],[17,1],[17,1],[14,5],[14,7],[28,5],[28,5],[28,5],[28,5],[28,2],[28,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[37,1],[37,1],[37,1],[39,1],[39,1],[39,1],[39,1],[15,5],[15,7],[56,5],[56,5],[56,2],[56,1],[16,5],[16,5],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[19,3],[20,3],[20,3],[30,1],[30,3],[30,1],[30,3],[18,3],[73,1],[73,3],[78,1],[78,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[26,1],[26,1],[33,1],[33,1],[35,1],[35,1],[55,1],[55,1],[58,1],[58,1],[60,1],[60,1]],performAction:m(function(i,r,l,s,E,t,pe){var n=t.length-1;switch(E){case 4:this.$=t[n].trim(),s.setAccTitle(this.$);break;case 5:case 6:this.$=t[n].trim(),s.setAccDescription(this.$);break;case 7:this.$=[];break;case 17:s.setDirection("TB");break;case 18:s.setDirection("BT");break;case 19:s.setDirection("RL");break;case 20:s.setDirection("LR");break;case 21:s.addRequirement(t[n-3],t[n-4]);break;case 22:s.addRequirement(t[n-5],t[n-6]),s.setClass([t[n-5]],t[n-3]);break;case 23:s.setNewReqId(t[n-2]);break;case 24:s.setNewReqText(t[n-2]);break;case 25:s.setNewReqRisk(t[n-2]);break;case 26:s.setNewReqVerifyMethod(t[n-2]);break;case 29:this.$=s.RequirementType.REQUIREMENT;break;case 30:this.$=s.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 31:this.$=s.RequirementType.INTERFACE_REQUIREMENT;break;case 32:this.$=s.RequirementType.PERFORMANCE_REQUIREMENT;break;case 33:this.$=s.RequirementType.PHYSICAL_REQUIREMENT;break;case 34:this.$=s.RequirementType.DESIGN_CONSTRAINT;break;case 35:this.$=s.RiskLevel.LOW_RISK;break;case 36:this.$=s.RiskLevel.MED_RISK;break;case 37:this.$=s.RiskLevel.HIGH_RISK;break;case 38:this.$=s.VerifyType.VERIFY_ANALYSIS;break;case 39:this.$=s.VerifyType.VERIFY_DEMONSTRATION;break;case 40:this.$=s.VerifyType.VERIFY_INSPECTION;break;case 41:this.$=s.VerifyType.VERIFY_TEST;break;case 42:s.addElement(t[n-3]);break;case 43:s.addElement(t[n-5]),s.setClass([t[n-5]],t[n-3]);break;case 44:s.setNewElementType(t[n-2]);break;case 45:s.setNewElementDocRef(t[n-2]);break;case 48:s.addRelationship(t[n-2],t[n],t[n-4]);break;case 49:s.addRelationship(t[n-2],t[n-4],t[n]);break;case 50:this.$=s.Relationships.CONTAINS;break;case 51:this.$=s.Relationships.COPIES;break;case 52:this.$=s.Relationships.DERIVES;break;case 53:this.$=s.Relationships.SATISFIES;break;case 54:this.$=s.Relationships.VERIFIES;break;case 55:this.$=s.Relationships.REFINES;break;case 56:this.$=s.Relationships.TRACES;break;case 57:this.$=t[n-2],s.defineClass(t[n-1],t[n]);break;case 58:s.setClass(t[n-1],t[n]);break;case 59:s.setClass([t[n-2]],t[n]);break;case 60:case 62:this.$=[t[n]];break;case 61:case 63:this.$=t[n-2].concat([t[n]]);break;case 64:this.$=t[n-2],s.setCssStyle(t[n-1],t[n]);break;case 65:this.$=[t[n]];break;case 66:t[n-2].push(t[n]),this.$=t[n-2];break;case 68:this.$=t[n-1]+t[n];break}},"anonymous"),table:[{3:1,4:2,6:a,9:u,11:o,13:f},{1:[3]},{3:8,4:2,5:[1,7],6:a,9:u,11:o,13:f},{5:[1,9]},{10:[1,10]},{12:[1,11]},e(c,[2,6]),{3:12,4:2,6:a,9:u,11:o,13:f},{1:[2,2]},{4:17,5:d,7:13,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},e(c,[2,4]),e(c,[2,5]),{1:[2,1]},{8:[1,41]},{4:17,5:d,7:42,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:43,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:44,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:45,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:46,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:47,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:48,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:49,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{4:17,5:d,7:50,8:R,9:u,11:o,13:f,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:h,22:p,23:I,24:k,25:23,33:25,41:A,42:C,43:V,44:v,45:x,46:L,54:D,72:O,74:w,77:M,89:g,90:_},{26:51,89:[1,52],90:[1,53]},{55:54,89:[1,55],90:[1,56]},{29:[1,59],61:[1,57],63:[1,58]},e(y,[2,17]),e(y,[2,18]),e(y,[2,19]),e(y,[2,20]),{30:60,33:62,75:$,89:g,90:_},{30:63,33:62,75:$,89:g,90:_},{30:64,33:62,75:$,89:g,90:_},e(X,[2,29]),e(X,[2,30]),e(X,[2,31]),e(X,[2,32]),e(X,[2,33]),e(X,[2,34]),e(Ce,[2,81]),e(Ce,[2,82]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{8:[2,13]},{8:[2,14]},{8:[2,15]},{8:[2,16]},{27:[1,65],29:[1,66]},e(Ee,[2,79]),e(Ee,[2,80]),{27:[1,67],29:[1,68]},e(Ee,[2,85]),e(Ee,[2,86]),{62:69,65:Ve,66:ve,67:xe,68:Le,69:De,70:Oe,71:we},{62:77,65:Ve,66:ve,67:xe,68:Le,69:De,70:Oe,71:we},{30:78,33:62,75:$,89:g,90:_},{73:79,75:ee,76:U,78:81,79:82,80:te,81:se,82:ie,83:re,84:ne,85:ae,86:le,87:ce,88:oe},e(de,[2,60]),e(de,[2,62]),{73:93,75:ee,76:U,78:81,79:82,80:te,81:se,82:ie,83:re,84:ne,85:ae,86:le,87:ce,88:oe},{30:94,33:62,75:$,76:U,89:g,90:_},{5:[1,95]},{30:96,33:62,75:$,89:g,90:_},{5:[1,97]},{30:98,33:62,75:$,89:g,90:_},{63:[1,99]},e(Y,[2,50]),e(Y,[2,51]),e(Y,[2,52]),e(Y,[2,53]),e(Y,[2,54]),e(Y,[2,55]),e(Y,[2,56]),{64:[1,100]},e(y,[2,59],{76:U}),e(y,[2,64],{76:Me}),{33:103,75:[1,102],89:g,90:_},e(Fe,[2,65],{79:104,75:ee,80:te,81:se,82:ie,83:re,84:ne,85:ae,86:le,87:ce,88:oe}),e(N,[2,67]),e(N,[2,69]),e(N,[2,70]),e(N,[2,71]),e(N,[2,72]),e(N,[2,73]),e(N,[2,74]),e(N,[2,75]),e(N,[2,76]),e(N,[2,77]),e(N,[2,78]),e(y,[2,57],{76:Me}),e(y,[2,58],{76:U}),{5:B,28:105,31:Q,34:H,36:K,38:W,40:j},{27:[1,112],76:U},{5:he,40:ue,56:113,57:me,59:fe},{27:[1,118],76:U},{33:119,89:g,90:_},{33:120,89:g,90:_},{75:ee,78:121,79:82,80:te,81:se,82:ie,83:re,84:ne,85:ae,86:le,87:ce,88:oe},e(de,[2,61]),e(de,[2,63]),e(N,[2,68]),e(y,[2,21]),{32:[1,122]},{32:[1,123]},{32:[1,124]},{32:[1,125]},{5:B,28:126,31:Q,34:H,36:K,38:W,40:j},e(y,[2,28]),{5:[1,127]},e(y,[2,42]),{32:[1,128]},{32:[1,129]},{5:he,40:ue,56:130,57:me,59:fe},e(y,[2,47]),{5:[1,131]},e(y,[2,48]),e(y,[2,49]),e(Fe,[2,66],{79:104,75:ee,80:te,81:se,82:ie,83:re,84:ne,85:ae,86:le,87:ce,88:oe}),{33:132,89:g,90:_},{35:133,89:[1,134],90:[1,135]},{37:136,47:[1,137],48:[1,138],49:[1,139]},{39:140,50:[1,141],51:[1,142],52:[1,143],53:[1,144]},e(y,[2,27]),{5:B,28:145,31:Q,34:H,36:K,38:W,40:j},{58:146,89:[1,147],90:[1,148]},{60:149,89:[1,150],90:[1,151]},e(y,[2,46]),{5:he,40:ue,56:152,57:me,59:fe},{5:[1,153]},{5:[1,154]},{5:[2,83]},{5:[2,84]},{5:[1,155]},{5:[2,35]},{5:[2,36]},{5:[2,37]},{5:[1,156]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,41]},e(y,[2,22]),{5:[1,157]},{5:[2,87]},{5:[2,88]},{5:[1,158]},{5:[2,89]},{5:[2,90]},e(y,[2,43]),{5:B,28:159,31:Q,34:H,36:K,38:W,40:j},{5:B,28:160,31:Q,34:H,36:K,38:W,40:j},{5:B,28:161,31:Q,34:H,36:K,38:W,40:j},{5:B,28:162,31:Q,34:H,36:K,38:W,40:j},{5:he,40:ue,56:163,57:me,59:fe},{5:he,40:ue,56:164,57:me,59:fe},e(y,[2,23]),e(y,[2,24]),e(y,[2,25]),e(y,[2,26]),e(y,[2,44]),e(y,[2,45])],defaultActions:{8:[2,2],12:[2,1],41:[2,3],42:[2,8],43:[2,9],44:[2,10],45:[2,11],46:[2,12],47:[2,13],48:[2,14],49:[2,15],50:[2,16],134:[2,83],135:[2,84],137:[2,35],138:[2,36],139:[2,37],141:[2,38],142:[2,39],143:[2,40],144:[2,41],147:[2,87],148:[2,88],150:[2,89],151:[2,90]},parseError:m(function(i,r){if(r.recoverable)this.trace(i);else{var l=new Error(i);throw l.hash=r,l}},"parseError"),parse:m(function(i){var r=this,l=[0],s=[],E=[null],t=[],pe=this.table,n="",ye=0,Pe=0,He=2,$e=1,Ke=t.slice.call(arguments,1),S=Object.create(this.lexer),G={yy:{}};for(var Ie in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Ie)&&(G.yy[Ie]=this.yy[Ie]);S.setInput(i,G.yy),G.yy.lexer=S,G.yy.parser=this,typeof S.yylloc>"u"&&(S.yylloc={});var be=S.yylloc;t.push(be);var We=S.options&&S.options.ranges;typeof G.yy.parseError=="function"?this.parseError=G.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function je(T){l.length=l.length-2*T,E.length=E.length-T,t.length=t.length-T}m(je,"popStack");function Ue(){var T;return T=s.pop()||S.lex()||$e,typeof T!="number"&&(T instanceof Array&&(s=T,T=s.pop()),T=r.symbols_[T]||T),T}m(Ue,"lex");for(var b,z,q,Te,J={},ge,F,Ye,_e;;){if(z=l[l.length-1],this.defaultActions[z]?q=this.defaultActions[z]:((b===null||typeof b>"u")&&(b=Ue()),q=pe[z]&&pe[z][b]),typeof q>"u"||!q.length||!q[0]){var ke="";_e=[];for(ge in pe[z])this.terminals_[ge]&&ge>He&&_e.push("'"+this.terminals_[ge]+"'");S.showPosition?ke="Parse error on line "+(ye+1)+`:
`+S.showPosition()+`
Expecting `+_e.join(", ")+", got '"+(this.terminals_[b]||b)+"'":ke="Parse error on line "+(ye+1)+": Unexpected "+(b==$e?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(ke,{text:S.match,token:this.terminals_[b]||b,line:S.yylineno,loc:be,expected:_e})}if(q[0]instanceof Array&&q.length>1)throw new Error("Parse Error: multiple actions possible at state: "+z+", token: "+b);switch(q[0]){case 1:l.push(b),E.push(S.yytext),t.push(S.yylloc),l.push(q[1]),b=null,Pe=S.yyleng,n=S.yytext,ye=S.yylineno,be=S.yylloc;break;case 2:if(F=this.productions_[q[1]][1],J.$=E[E.length-F],J._$={first_line:t[t.length-(F||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(F||1)].first_column,last_column:t[t.length-1].last_column},We&&(J._$.range=[t[t.length-(F||1)].range[0],t[t.length-1].range[1]]),Te=this.performAction.apply(J,[n,Pe,ye,G.yy,q[1],E,t].concat(Ke)),typeof Te<"u")return Te;F&&(l=l.slice(0,-1*F*2),E=E.slice(0,-1*F),t=t.slice(0,-1*F)),l.push(this.productions_[q[1]][0]),E.push(J.$),t.push(J._$),Ye=pe[l[l.length-2]][l[l.length-1]],l.push(Ye);break;case 3:return!0}}return!0},"parse")},Qe=function(){var P={EOF:1,parseError:m(function(r,l){if(this.yy.parser)this.yy.parser.parseError(r,l);else throw new Error(r)},"parseError"),setInput:m(function(i,r){return this.yy=r||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:m(function(){var i=this._input[0];this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i;var r=i.match(/(?:\r\n?|\n).*/g);return r?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:m(function(i){var r=i.length,l=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-r),this.offset-=r;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===s.length?this.yylloc.first_column:0)+s[s.length-l.length].length-l[0].length:this.yylloc.first_column-r},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-r]),this.yyleng=this.yytext.length,this},"unput"),more:m(function(){return this._more=!0,this},"more"),reject:m(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:m(function(i){this.unput(this.match.slice(i))},"less"),pastInput:m(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:m(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:m(function(){var i=this.pastInput(),r=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+r+"^"},"showPosition"),test_match:m(function(i,r){var l,s,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),s=i[0].match(/(?:\r\n?|\n).*/g),s&&(this.yylineno+=s.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],l=this.performAction.call(this,this.yy,this,r,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),l)return l;if(this._backtrack){for(var t in E)this[t]=E[t];return!1}return!1},"test_match"),next:m(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var i,r,l,s;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),t=0;t<E.length;t++)if(l=this._input.match(this.rules[E[t]]),l&&(!r||l[0].length>r[0].length)){if(r=l,s=t,this.options.backtrack_lexer){if(i=this.test_match(l,E[t]),i!==!1)return i;if(this._backtrack){r=!1;continue}else return!1}else if(!this.options.flex)break}return r?(i=this.test_match(r,E[s]),i!==!1?i:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:m(function(){var r=this.next();return r||this.lex()},"lex"),begin:m(function(r){this.conditionStack.push(r)},"begin"),popState:m(function(){var r=this.conditionStack.length-1;return r>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:m(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:m(function(r){return r=this.conditionStack.length-1-Math.abs(r||0),r>=0?this.conditionStack[r]:"INITIAL"},"topState"),pushState:m(function(r){this.begin(r)},"pushState"),stateStackSize:m(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:m(function(r,l,s,E){switch(s){case 0:return"title";case 1:return this.begin("acc_title"),9;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),11;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:return 21;case 9:return 22;case 10:return 23;case 11:return 24;case 12:return 5;case 13:break;case 14:break;case 15:break;case 16:return 8;case 17:return 6;case 18:return 27;case 19:return 40;case 20:return 29;case 21:return 32;case 22:return 31;case 23:return 34;case 24:return 36;case 25:return 38;case 26:return 41;case 27:return 42;case 28:return 43;case 29:return 44;case 30:return 45;case 31:return 46;case 32:return 47;case 33:return 48;case 34:return 49;case 35:return 50;case 36:return 51;case 37:return 52;case 38:return 53;case 39:return 54;case 40:return 65;case 41:return 66;case 42:return 67;case 43:return 68;case 44:return 69;case 45:return 70;case 46:return 71;case 47:return 57;case 48:return 59;case 49:return this.begin("style"),77;case 50:return 75;case 51:return 81;case 52:return 88;case 53:return"PERCENT";case 54:return 86;case 55:return 84;case 56:break;case 57:this.begin("string");break;case 58:this.popState();break;case 59:return this.begin("style"),72;case 60:return this.begin("style"),74;case 61:return 61;case 62:return 64;case 63:return 63;case 64:this.begin("string");break;case 65:this.popState();break;case 66:return"qString";case 67:return l.yytext=l.yytext.trim(),89;case 68:return 75;case 69:return 80;case 70:return 76}},"anonymous"),rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::{3})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:style\b)/i,/^(?:\w+)/i,/^(?::)/i,/^(?:;)/i,/^(?:%)/i,/^(?:-)/i,/^(?:#)/i,/^(?: )/i,/^(?:["])/i,/^(?:\n)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^:,\r\n\{\<\>\-\=]*)/i,/^(?:\w+)/i,/^(?:[0-9]+)/i,/^(?:,)/i],conditions:{acc_descr_multiline:{rules:[6,7,68,69,70],inclusive:!1},acc_descr:{rules:[4,68,69,70],inclusive:!1},acc_title:{rules:[2,68,69,70],inclusive:!1},style:{rules:[50,51,52,53,54,55,56,57,58,68,69,70],inclusive:!1},unqString:{rules:[68,69,70],inclusive:!1},token:{rules:[68,69,70],inclusive:!1},string:{rules:[65,66,68,69,70],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,67,68,69,70],inclusive:!0}}};return P}();Se.lexer=Qe;function Re(){this.yy={}}return m(Re,"Parser"),Re.prototype=Se,Se.Parser=Re,new Re}();Ae.parser=Ae;var ct=Ae,Z,ot=(Z=class{constructor(){this.relations=[],this.latestRequirement=this.getInitialRequirement(),this.requirements=new Map,this.latestElement=this.getInitialElement(),this.elements=new Map,this.classes=new Map,this.direction="TB",this.RequirementType={REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"},this.RiskLevel={LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"},this.VerifyType={VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"},this.Relationships={CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"},this.setAccTitle=Xe,this.getAccTitle=Je,this.setAccDescription=Ze,this.getAccDescription=et,this.setDiagramTitle=tt,this.getDiagramTitle=st,this.getConfig=m(()=>Ne().requirement,"getConfig"),this.clear(),this.setDirection=this.setDirection.bind(this),this.addRequirement=this.addRequirement.bind(this),this.setNewReqId=this.setNewReqId.bind(this),this.setNewReqRisk=this.setNewReqRisk.bind(this),this.setNewReqText=this.setNewReqText.bind(this),this.setNewReqVerifyMethod=this.setNewReqVerifyMethod.bind(this),this.addElement=this.addElement.bind(this),this.setNewElementType=this.setNewElementType.bind(this),this.setNewElementDocRef=this.setNewElementDocRef.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setCssStyle=this.setCssStyle.bind(this),this.setClass=this.setClass.bind(this),this.defineClass=this.defineClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}getDirection(){return this.direction}setDirection(a){this.direction=a}resetLatestRequirement(){this.latestRequirement=this.getInitialRequirement()}resetLatestElement(){this.latestElement=this.getInitialElement()}getInitialRequirement(){return{requirementId:"",text:"",risk:"",verifyMethod:"",name:"",type:"",cssStyles:[],classes:["default"]}}getInitialElement(){return{name:"",type:"",docRef:"",cssStyles:[],classes:["default"]}}addRequirement(a,u){return this.requirements.has(a)||this.requirements.set(a,{name:a,type:u,requirementId:this.latestRequirement.requirementId,text:this.latestRequirement.text,risk:this.latestRequirement.risk,verifyMethod:this.latestRequirement.verifyMethod,cssStyles:[],classes:["default"]}),this.resetLatestRequirement(),this.requirements.get(a)}getRequirements(){return this.requirements}setNewReqId(a){this.latestRequirement!==void 0&&(this.latestRequirement.requirementId=a)}setNewReqText(a){this.latestRequirement!==void 0&&(this.latestRequirement.text=a)}setNewReqRisk(a){this.latestRequirement!==void 0&&(this.latestRequirement.risk=a)}setNewReqVerifyMethod(a){this.latestRequirement!==void 0&&(this.latestRequirement.verifyMethod=a)}addElement(a){return this.elements.has(a)||(this.elements.set(a,{name:a,type:this.latestElement.type,docRef:this.latestElement.docRef,cssStyles:[],classes:["default"]}),qe.info("Added new element: ",a)),this.resetLatestElement(),this.elements.get(a)}getElements(){return this.elements}setNewElementType(a){this.latestElement!==void 0&&(this.latestElement.type=a)}setNewElementDocRef(a){this.latestElement!==void 0&&(this.latestElement.docRef=a)}addRelationship(a,u,o){this.relations.push({type:a,src:u,dst:o})}getRelationships(){return this.relations}clear(){this.relations=[],this.resetLatestRequirement(),this.requirements=new Map,this.resetLatestElement(),this.elements=new Map,this.classes=new Map,it()}setCssStyle(a,u){for(const o of a){const f=this.requirements.get(o)??this.elements.get(o);if(!u||!f)return;for(const c of u)c.includes(",")?f.cssStyles.push(...c.split(",")):f.cssStyles.push(c)}}setClass(a,u){var o;for(const f of a){const c=this.requirements.get(f)??this.elements.get(f);if(c)for(const d of u){c.classes.push(d);const R=(o=this.classes.get(d))==null?void 0:o.styles;R&&c.cssStyles.push(...R)}}}defineClass(a,u){for(const o of a){let f=this.classes.get(o);f===void 0&&(f={id:o,styles:[],textStyles:[]},this.classes.set(o,f)),u&&u.forEach(function(c){if(/color/.exec(c)){const d=c.replace("fill","bgFill");f.textStyles.push(d)}f.styles.push(c)}),this.requirements.forEach(c=>{c.classes.includes(o)&&c.cssStyles.push(...u.flatMap(d=>d.split(",")))}),this.elements.forEach(c=>{c.classes.includes(o)&&c.cssStyles.push(...u.flatMap(d=>d.split(",")))})}}getClasses(){return this.classes}getData(){var f,c,d,R;const a=Ne(),u=[],o=[];for(const h of this.requirements.values()){const p=h;p.id=h.name,p.cssStyles=h.cssStyles,p.cssClasses=h.classes.join(" "),p.shape="requirementBox",p.look=a.look,u.push(p)}for(const h of this.elements.values()){const p=h;p.shape="requirementBox",p.look=a.look,p.id=h.name,p.cssStyles=h.cssStyles,p.cssClasses=h.classes.join(" "),u.push(p)}for(const h of this.relations){let p=0;const I=h.type===this.Relationships.CONTAINS,k={id:`${h.src}-${h.dst}-${p}`,start:((f=this.requirements.get(h.src))==null?void 0:f.name)??((c=this.elements.get(h.src))==null?void 0:c.name),end:((d=this.requirements.get(h.dst))==null?void 0:d.name)??((R=this.elements.get(h.dst))==null?void 0:R.name),label:`&lt;&lt;${h.type}&gt;&gt;`,classes:"relationshipLine",style:["fill:none",I?"":"stroke-dasharray: 10,7"],labelpos:"c",thickness:"normal",type:"normal",pattern:I?"normal":"dashed",arrowTypeStart:I?"requirement_contains":"",arrowTypeEnd:I?"":"requirement_arrow",look:a.look};o.push(k),p++}return{nodes:u,edges:o,other:{},config:a,direction:this.getDirection()}}},m(Z,"RequirementDB"),Z),ht=m(e=>`

  marker {
    fill: ${e.relationColor};
    stroke: ${e.relationColor};
  }

  marker.cross {
    stroke: ${e.lineColor};
  }

  svg {
    font-family: ${e.fontFamily};
    font-size: ${e.fontSize};
  }

  .reqBox {
    fill: ${e.requirementBackground};
    fill-opacity: 1.0;
    stroke: ${e.requirementBorderColor};
    stroke-width: ${e.requirementBorderSize};
  }
  
  .reqTitle, .reqLabel{
    fill:  ${e.requirementTextColor};
  }
  .reqLabelBox {
    fill: ${e.relationLabelBackground};
    fill-opacity: 1.0;
  }

  .req-title-line {
    stroke: ${e.requirementBorderColor};
    stroke-width: ${e.requirementBorderSize};
  }
  .relationshipLine {
    stroke: ${e.relationColor};
    stroke-width: 1;
  }
  .relationshipLabel {
    fill: ${e.relationLabelColor};
  }
  .divider {
    stroke: ${e.nodeBorder};
    stroke-width: 1;
  }
  .label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .label text,span {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }
  .labelBkg {
    background-color: ${e.edgeLabelBackground};
  }

`,"getStyles"),ut=ht,Be={};rt(Be,{draw:()=>mt});var mt=m(async function(e,a,u,o){qe.info("REF0:"),qe.info("Drawing requirement diagram (unified)",a);const{securityLevel:f,state:c,layout:d}=Ne(),R=o.db.getData(),h=Ge(a,f);R.type=o.type,R.layoutAlgorithm=nt(d),R.nodeSpacing=(c==null?void 0:c.nodeSpacing)??50,R.rankSpacing=(c==null?void 0:c.rankSpacing)??50,R.markers=["requirement_contains","requirement_arrow"],R.diagramId=a,await at(R,h);const p=8;lt.insertTitle(h,"requirementDiagramTitleText",(c==null?void 0:c.titleTopMargin)??25,o.db.getDiagramTitle()),ze(h,p,"requirementDiagram",(c==null?void 0:c.useMaxWidth)??!0)},"draw"),Tt={parser:ct,get db(){return new ot},renderer:Be,styles:ut};export{Tt as diagram};
