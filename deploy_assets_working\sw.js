if(!self.define){let s,e={};const a=(a,i)=>(a=new URL(a+".js",i).href,e[a]||new Promise(e=>{if("document"in self){const s=document.createElement("script");s.src=a,s.onload=e,document.head.appendChild(s)}else s=a,importScripts(a),e()}).then(()=>{let s=e[a];if(!s)throw new Error(`Module ${a} didn’t register its module`);return s}));self.define=(i,r)=>{const c=s||("document"in self?document.currentScript.src:"")||location.href;if(e[c])return;let d={};const b=s=>a(s,c),f={module:{uri:c},exports:d,require:b};e[c]=Promise.all(i.map(s=>f[s]||b(s))).then(s=>(r(...s),d))}}define(["./workbox-3741bf5c"],function(s){"use strict";self.addEventListener("message",s=>{s.data&&"SKIP_WAITING"===s.data.type&&self.skipWaiting()}),s.precacheAndRoute([{url:"advanced-analysis.png",revision:"334c3d5bfc456fa8f4351f585469fba1"},{url:"assets/_basePickBy-CvIohq0t.js",revision:"62ff66285bdda7325225568e07efab34"},{url:"assets/_baseUniq-DTckOzFM.js",revision:"c57cacc96cfbfae03eaadfe01ac71ee1"},{url:"assets/AddToResultsButton-BwSXKCt2.js",revision:"1c7d32dfd2e4821286aac69574605539"},{url:"assets/AdminDashboardPage-SPi3xD-N.js",revision:"62c2daae53234444368ce6ec9c9d043b"},{url:"assets/AdvancedAnalysisOptions-BMl4jxNG.js",revision:"157f880f7ab150df46a13029cf4600c3"},{url:"assets/AdvancedAnalysisPage-BJQTFL6B.js",revision:"9f34879c0bfa5f007d60d168d3a26dbe"},{url:"assets/AnalysisAssistantPage-CqoSPBfZ.js",revision:"48880af02503225a75e1f3fda1c313fc"},{url:"assets/AnalysisIndexPage-BvKbf9Gr.js",revision:"b440af7f9c91cfb5097ad59600b9f084"},{url:"assets/AnalysisSteps-CmfAFU5C.js",revision:"9723c07d955c0ba4730b9d81c4c16e3c"},{url:"assets/anova-DbTY6dHK.js",revision:"1ab71ed01321a6a7ab79de2fd7730134"},{url:"assets/ANOVA-VqPPZ-TR.js",revision:"58cd11ddcfeaa99e6290319fff7bde1a"},{url:"assets/ANOVAWorkflowPage-BelY7RhI.js",revision:"96036ca74221fc00222084f2dfbc0d07"},{url:"assets/architectureDiagram-IEHRJDOE-BVFoC4YC.js",revision:"5888f405a0201881afe44c233b442d80"},{url:"assets/Auth-_Z-L5t-3.js",revision:"4becbc43b888a6e346200e8a37ffa510"},{url:"assets/AuthTestPage-DhFDKCY6.js",revision:"f02cb2c77e34ec157abccd0b37c22502"},{url:"assets/BarChart-mU2G4olq.js",revision:"b7d7a6253ac34f02fd5cf432a3088e20"},{url:"assets/blockDiagram-JOT3LUYC-RLlEH1Zn.js",revision:"8f12d54cdea2688b084783d5fa2412a4"},{url:"assets/BoxPlot-ZMiXdUEt.js",revision:"********************************"},{url:"assets/browser-B0fvQgWK.js",revision:"69bf0796e8b71bb491fce66d80d601f5"},{url:"assets/c4Diagram-VJAJSXHY-BmFvRjRz.js",revision:"c6d5b97eba7f3e89e8a33fa51c8ddcb7"},{url:"assets/CaseControlCalculator-BzLsSUO6.js",revision:"834a784ca668b3a49f1abf598fcc072c"},{url:"assets/channel-BPnPIQAa.js",revision:"35ae0cd1315e80836921d1c9b09bbe83"},{url:"assets/charts-plotly-BhN4fPIu.js",revision:"892bba45608ad40d87c9b803ae9d5b21"},{url:"assets/charts-plotly-CuCRB34y.css",revision:"a38165a4e5664b196e6ab4d6e8e82da0"},{url:"assets/charts-recharts-d3-BEF1Y_jn.js",revision:"301a8866e0d75b9821ae9e99a2d904b3"},{url:"assets/chunk-4BMEZGHF-D0nfVVdS.js",revision:"36327e0d5cbb715c62c2c8a8bcd6d025"},{url:"assets/chunk-A2AXSNBT-CfubMA7k.js",revision:"31ec97cdfd1b52caa0d84c8e6583817f"},{url:"assets/chunk-AEK57VVT-BChVRIZA.js",revision:"2f6be8a04a8d265ba037717b5e18340a"},{url:"assets/chunk-D6G4REZN-J1hK3QFU.js",revision:"248b46b61ee787afb45feb1ca1896ed9"},{url:"assets/chunk-RZ5BOZE2-BWVq1HNy.js",revision:"73a47078393722c144bc2d58a982278a"},{url:"assets/chunk-XZIHB7SX-vQFpsdml.js",revision:"6feaaba1ffdb20da48da17bcc446e63f"},{url:"assets/classDiagram-GIVACNV2-OWvnKXaf.js",revision:"f61393c9cdbb442c64953cdad002a420"},{url:"assets/classDiagram-v2-COTLJTTW-OWvnKXaf.js",revision:"f61393c9cdbb442c64953cdad002a420"},{url:"assets/clone-B_Po1mvy.js",revision:"4fbf61a66a9499ad90c1f197fbabe03c"},{url:"assets/ClusterAnalysis-Dwkqf03e.js",revision:"36788836fa6a32c696dc59e3e10b5629"},{url:"assets/CohortCalculator-Ceh0DWHw.js",revision:"101ec917b71850ed93db74d4ec9ed22b"},{url:"assets/ConfirmatoryFactorAnalysis-D0HSoWGO.js",revision:"d3b7b49d9c5f2830880c26a3882014cb"},{url:"assets/ConvertToAPA-C3MGbZit.js",revision:"7e9c21050a37364af300b933b3b10447"},{url:"assets/CorrelationAnalysisOptions-X530BGye.js",revision:"50203984b9eda7f6e6807a066ef4b93d"},{url:"assets/CorrelationAnalysisPage-C4yIGAB2.js",revision:"5f63e5ca427a148f4d60fc136ba6bfba"},{url:"assets/countries-BGaMXFfG.js",revision:"e9a09738a7abbdf76a6293d3de897348"},{url:"assets/coxRegressionService-CqIkroID.js",revision:"114f2a62813ebb0e465b63426620f1b7"},{url:"assets/CrossSectionalCalculator-CiCHocwD.js",revision:"f3575b79aacc8f3a6d95947322c807d1"},{url:"assets/cytoscape.esm-Dyt8tNUa.js",revision:"59c81902d476eea2dabddec354da80f2"},{url:"assets/dagre-OKDRZEBW-demB6D4E.js",revision:"298ef5a8afd8c5941765b78f32751919"},{url:"assets/DashboardPage-48jtFBKD.js",revision:"d144ac477fc04d8f72295d8b9e5ed6b7"},{url:"assets/DataManagementOptions-BATjkCpy.js",revision:"577cfc4dd47129d5fae8d3ba29949676"},{url:"assets/DataManagementPage-BRk0DtCj.js",revision:"d28271450b8a496692e45984238546fa"},{url:"assets/DatasetSelector-G08QHuaN.js",revision:"5ff3450d19ae37f79b5543f00bed43fa"},{url:"assets/DataVisualizationOptions-B6r9UVDP.js",revision:"73412e31cecc7a363a257b9c7619c495"},{url:"assets/DataVisualizationPage-BkCc12HU.js",revision:"8d207ee8972d32d36fb42bce297cd203"},{url:"assets/descriptive-Djo0s6H4.js",revision:"552cc661582dcaa762f907ac57ae2b25"},{url:"assets/DescriptiveStatsOptions-MCsgZr0u.js",revision:"880e85261105ce01b5c02926d9b64c7c"},{url:"assets/DescriptiveStatsPage-Dax3LBrL.js",revision:"c7dce0026e439541068681bda7d58dcb"},{url:"assets/DevTrainingPage-A92mzUCf.js",revision:"3894fa1d2e895a5adef6c73ce332ecc8"},{url:"assets/diagram-SSKATNLV-CjaIi2bO.js",revision:"80fafeecca0da05dd705afde7f093818"},{url:"assets/diagram-VNBRO52H-C86j_mW_.js",revision:"8edf73b0f6a0787e7e558de0c522bc95"},{url:"assets/EducationalTierTest-BNrDUfgg.js",revision:"dd82ce2ce67922571688346b5bcbbf02"},{url:"assets/EpiCalc-BuVTzyEC.js",revision:"b2cca0ebd41cb4bc2f042d7cbae89270"},{url:"assets/EpiCalcOptions-RMQOqOWt.js",revision:"526450f0ce18c279d9f2f1fb33c9299f"},{url:"assets/EpiCalcPage-BSSz3cCG.js",revision:"d3df74b48d6a8060445fdc4f95261655"},{url:"assets/erDiagram-Q7BY3M3F-BCgjx10N.js",revision:"95e14660c27649b4dac7a27830e7e711"},{url:"assets/ErrorBarChart-SBYEVPSL.js",revision:"2ccab51ea7613638a302d13de1b75154"},{url:"assets/ExploratoryFactorAnalysis-DKexHXUh.js",revision:"be415ebf959227b3fe68f49ccb7581ee"},{url:"assets/FeatureComparisonTable-D0Q0fTUa.js",revision:"7eda1d4b220a29c5e884e0d25e51f98b"},{url:"assets/flowDiagram-4HSFHLVR-CJF4d5Iv.js",revision:"5fa403eacc97374e022b44863d6387ab"},{url:"assets/FlowDiagram-CHRbazj7.js",revision:"ce8d4f7b5e226bb6c05a7e4c6b266141"},{url:"assets/FlowDiagram-DX7sLWOj.css",revision:"f98f68db22d99b26ab733e25fe97f4fc"},{url:"assets/FlowDiagramPage-DarY2NbG.js",revision:"94f8f97a1a0ed6ef0bb60ea81fcdb579"},{url:"assets/ganttDiagram-APWFNJXF-CQe5BOum.js",revision:"63489f0ec2fe9f03df7eee67670a21e2"},{url:"assets/gitGraphDiagram-7IBYFJ6S-CugateeO.js",revision:"9e697023cb4913b19f7326e3e4dd9574"},{url:"assets/graph-CwbYeD9S.js",revision:"db5469df737f0f8a76a721b3ab225960"},{url:"assets/GuidedWorkflow-MMwzUT8W.js",revision:"6f20723c0bd435602130b0a9b44a24e1"},{url:"assets/Histogram-Cb7VnHyR.js",revision:"7fb48ab7f7d1c60a081f7bb5475976fd"},{url:"assets/HomePage-DHzfMv48.js",revision:"122652629b1b150136ccad991de90c2b"},{url:"assets/index-Bd7Staio.js",revision:"e4cdb6bef6fae5df9ca500238e505869"},{url:"assets/index-BHSDXLLS.js",revision:"78cb320f6c68f0137e30c7bb8e9fb75c"},{url:"assets/index-Bpan7Tbe.js",revision:"751090f10cc348103c3798c2eca0f70e"},{url:"assets/index-DjpOF40k.js",revision:"fe8c82b43b912665d73f9f3da263d107"},{url:"assets/index-DqymCIs-.js",revision:"719cb58c0252b4c01e477a45c1ea8ee4"},{url:"assets/index-DyRZcmzc.css",revision:"b34cb60012b8ccfcd5d2c3f9418b688e"},{url:"assets/index-v_pkX9nf.js",revision:"6d1a2800976f1b172e41e1254ae7726c"},{url:"assets/InferentialStatsOptions-Cv31a_py.js",revision:"44a972a4e6e6c7fa5976c2f4308ae013"},{url:"assets/InferentialStatsPage-CnZG1DdU.js",revision:"572ab07c9a76bc9185dd336eeedb143f"},{url:"assets/infoDiagram-PH2N3AL5-799nGRxK.js",revision:"11660fb07fcdc6fa887ee1b986178f1d"},{url:"assets/journeyDiagram-U35MCT3I-DPyCxeo8.js",revision:"046822c5e89d8abd38d53f0da6c439dc"},{url:"assets/kanban-definition-NDS4AKOZ-DIMQ9e9X.js",revision:"d8469c0157595f31d89899fc82e5e730"},{url:"assets/katex-ChWnQ-fc.js",revision:"5db81529852876ea785ddcb5a267d688"},{url:"assets/KnowledgeBasePage-1nzuPqIf.js",revision:"da764a73831ef8fd70817649eb5dae93"},{url:"assets/LandingPage-CCGGC3FQ.css",revision:"9b2cb6ab19493315a2008680aed7e772"},{url:"assets/LandingPage-MuphgYph.js",revision:"b503d0e9e4d19ddace50dfcfc3a2d55f"},{url:"assets/layout-BFwSFWu9.js",revision:"771d6f6987d77e2506f60d691ee94337"},{url:"assets/MatchedCaseControlCalculator-XX9Fsb_b.js",revision:"91cf1549197c7ba14cdbe49b3d4f23f3"},{url:"assets/math-lib-BOZ-XUok.js",revision:"5d22787297d75edad54d301e04710c18"},{url:"assets/math-setup-BTRs7Kau.js",revision:"cb828db5a2bfa099a70d8231c7878a4e"},{url:"assets/MediationModeration-BLkQPilK.js",revision:"47de426c92b68caa8502fa2d12488c9e"},{url:"assets/MetaAnalysis-C_ep_tOv.js",revision:"2216efae7633803d701f57efa3f9e3f5"},{url:"assets/mindmap-definition-ALO5MXBD-qC5qjWgw.js",revision:"db0ee107014475eec2e7bfb2966bb7fe"},{url:"assets/ml-tensorflow-D19WVUQh.js",revision:"130ea1d15925fdd75181074ae820fdcd"},{url:"assets/MoreThanTwoGroupsCalculator-DCYA-CIl.js",revision:"bfa65f5e08010b14235d2dea2e046dc4"},{url:"assets/mui-libs-CfwFIaTD.js",revision:"891b1b3e96de54c7089907a031b769aa"},{url:"assets/non-parametric-Cf6Ds91x.js",revision:"db51c3c5cebf9debee2de16f52851c00"},{url:"assets/NonParametricTests-CnQbYnts.js",revision:"ebe2a689f3ee7043c79f8ddef7752191"},{url:"assets/normality-CwHD6Rjl.js",revision:"2b28e81c3fc494b9c9ab6c8b605b5465"},{url:"assets/NotificationsPage-CJRFU61k.js",revision:"fb781a6360d27a3b3bb58e022adcf0dd"},{url:"assets/OneSampleCalculator-DdSwydDx.js",revision:"d41e89c642030eca5e812b2adebf3ab0"},{url:"assets/OneWayANOVA-ai9Q6gnX.js",revision:"a59a50e7583d16151e55e90cd5e069a8"},{url:"assets/other-utils-CR9xr_gI.js",revision:"b53bcb9b9ccc66dc37ba34a120be5fb7"},{url:"assets/PageTitle-DA3BXQ4x.js",revision:"12e94754ff7583795d7f7fe5c5e409c1"},{url:"assets/PairedSampleCalculator-B2azJaVa.js",revision:"e30050d1540105a6716fadaaaac2b289"},{url:"assets/PieChart-BIcQw2j3.js",revision:"3c35069be799693b5503b5a2c08921f1"},{url:"assets/pieDiagram-IB7DONF6-BdfZURv8.js",revision:"b94958f18fddd2096954bcf0efe8a251"},{url:"assets/PivotAnalysisViewer-ByF4D01r.css",revision:"de1849187f774ada9042c085176bb6d9"},{url:"assets/PivotAnalysisViewer-CUgNzlbS.js",revision:"3410c33f68a416124a45c277a06eb0ac"},{url:"assets/PlotlyBoxPlot-CsYPOD7q.js",revision:"********************************"},{url:"assets/PlotlyRenderers-px7c2MoL.js",revision:"42fb79d6c140eaacd2b0538b68d4cbac"},{url:"assets/PostHocTests-BRrhMOQ0.js",revision:"0a87496f5592ea1019e69aac5dfba7b8"},{url:"assets/PostHocTestsPage-DB91RNgd.js",revision:"96c37286fb6c7a8e3fc566e47e9b4a67"},{url:"assets/PricingDevPage-BfcWB05s.js",revision:"d8a926632f504ca18fdcc1fe9caf1878"},{url:"assets/PricingPage-gZ3rfXmZ.js",revision:"ac80a9cb802115dafbe534e541d136c4"},{url:"assets/PrivacyPolicyPage-or4SKdgI.js",revision:"ae2a5ae12c8298d31ba27fa1d700bda6"},{url:"assets/PublicationReadyGate-BGFbKbJc.js",revision:"848c87d8ca730e0e4d108f978baa841f"},{url:"assets/PublicationReadyOptions-BG4CsEaB.js",revision:"7603b43cb9b5e07933ff71b05b0452f0"},{url:"assets/PublicationReadyPage-DZlM7Fz2.js",revision:"9ada7b44178c0cd23220b0db9a44b927"},{url:"assets/quadrantDiagram-7GDLP6J5-DlPiuWL7.js",revision:"770e1e0ab233d225b3a76079c4c0c8be"},{url:"assets/radar-MK3ICKWK-BCMVXlQA.js",revision:"9fd3f07a28e250f44aa80c704af3217d"},{url:"assets/RainCloudPlot-DH85uixJ.js",revision:"444e9f55c19494e5b25fcc19f4187ec0"},{url:"assets/react-libs-Cr2nE3UY.js",revision:"eea40cc72b9502e27c76bf59a2816ff6"},{url:"assets/regression-BKqSTW2N.js",revision:"c9939cd94ec9a35340ff8da4d1021b8b"},{url:"assets/RegressionTable-BuRxA8Iu.js",revision:"d782fff5354a7e703b2d73ef439b4b40"},{url:"assets/RegressionTablePage-ziGV955Z.js",revision:"94c4f646f2f4d4a3b0141e829f477b19"},{url:"assets/ReliabilityAnalysis-BP7J0pqF.js",revision:"ff6201c5de896b68b8d63c6daa3f01ee"},{url:"assets/repeatedMeasuresANOVA-B8bv3NGM.js",revision:"fc48ea347669692281392294dba0eb35"},{url:"assets/RepeatedMeasuresANOVA-DwrjssMW.js",revision:"949721f085f5f57644417e2c37ef2874"},{url:"assets/RepeatedMeasuresANOVAWorkflowPage-C3fr4eBF.js",revision:"b41d22b65887149c1397c8ee401a7bb0"},{url:"assets/requirementDiagram-KVF5MWMF-Ge3Zv2Cg.js",revision:"7afd2f4a0901de3e6a2f6b4b98d019e2"},{url:"assets/ResultsManagerPage-BSs6I6Q7.js",revision:"22805b0fce4ccd1aa7de576bf60d9767"},{url:"assets/routeConfig-DN2aHLM5.js",revision:"1b095970bb65b572649709a5a563e7be"},{url:"assets/RouterTest-C8CpCslx.js",revision:"d26a778444b0d2900e38e43e6a951976"},{url:"assets/RouteTestPage-DN2MxV_Q.js",revision:"c20d922d323d5c27778a33c7a44931b9"},{url:"assets/sampleSize-nX7GCAZC.js",revision:"9498e83fa0dc71797239ffb9925b0f63"},{url:"assets/SampleSizeCalculatorsOptions-DlONI60r.js",revision:"27cdcec4925366edf5d3732dc3055f7d"},{url:"assets/SampleSizePowerCalculator-Czye5N6w.js",revision:"66e789eb5bf31842806a80748b6f19ca"},{url:"assets/SankeyDiagram-D49V6zuF.js",revision:"3410f342e58ee6944afc667c3fce6952"},{url:"assets/sankeyDiagram-QLVOVGJD-BswtmS1a.js",revision:"2fdd85a2bd009a5c63899654d71e4edb"},{url:"assets/ScatterPlot-Crke6vGe.js",revision:"b5f94011b13f0d005fa754e9f745ff0c"},{url:"assets/sequenceDiagram-X6HHIX6F-vfl950AQ.js",revision:"75fcff9805ff8002831b21e37dccf2d4"},{url:"assets/Settings-CC7UG-TA.js",revision:"c6dbc9cc29bf2c35125477085bc3b192"},{url:"assets/slick-BlzDm7g2.svg",revision:"f97e3bbf73254b0112091d0192f17aec"},{url:"assets/SMDTable-BZazxrfM.js",revision:"7870074e721b2a45608b10c8d4f0c5e9"},{url:"assets/stateDiagram-DGXRK772-JjgLOe5W.js",revision:"80c30ee88efb9dd5747f43dcdf791b9c"},{url:"assets/stateDiagram-v2-YXO3MK2T-q5rp7kkZ.js",revision:"5ffa2db55dcbff972e6c4b7ab2f64e10"},{url:"assets/StatisticalMethodsGenerator-C5cIAvRq.js",revision:"b337437b727a6e7d60cb07dc85603631"},{url:"assets/StatisticalMethodsPage-DO96Z5ro.js",revision:"16cf53e2760ff1002012e01b414530a8"},{url:"assets/StatsCard-op8tGQ0a.js",revision:"0ff10a4d4157c0dc2fb35a299527245b"},{url:"assets/supabase-lib-B3goak-P.js",revision:"d81d4ca0d7952631eb328c3189e436b6"},{url:"assets/SurvivalAnalysis-Dzi5rTjL.js",revision:"32ce54a18a8b719d38346abb804a7206"},{url:"assets/t-tests-DXw1R1jD.js",revision:"2396d7391d4ba29a61be38b5b5fe96a5"},{url:"assets/Table1-B7BcroXM.js",revision:"37f3e2935e1e1d66af09053b2d318cb4"},{url:"assets/Table1a-1M0jF3L-.js",revision:"4fb664ebc2119b60da1b50e59b719bc3"},{url:"assets/Table1b-ByG5YXGI.js",revision:"ac81764815c12b62f17fc7bf25c1f626"},{url:"assets/Table1Page-C_DgJWcu.js",revision:"49bbf5985c44205c1cc37e69be992ed1"},{url:"assets/Table2-Bf2a6rrV.js",revision:"eb688ceb59e4f34fa8d3391668abba2e"},{url:"assets/Table2Page-C30z8sqf.js",revision:"133c532dcdc0ffd2a5c8728f0745a742"},{url:"assets/TabPanel-CVuv1-VX.js",revision:"b595b60f08640577baf0caa9b207f150"},{url:"assets/TermsOfServicePage-CtfR5VKs.js",revision:"365d2abe9328411375d9dd175edd7c4e"},{url:"assets/timeline-definition-BDJGKUSR-b8pClVqI.js",revision:"402171ae5f8d56f9e20d8193a7f80578"},{url:"assets/trainingDataInitializer-DhlysJr9.js",revision:"a6666d2bc7fee8955af3332f10d543c3"},{url:"assets/TTests-CqoYonWk.js",revision:"21c49b3f60de9efbf7d34c7e7e1dbf6a"},{url:"assets/TTestWorkflowPage-CdvNLcMj.js",revision:"1225d89df3e84a1f47491c1acad46e95"},{url:"assets/TutorialPage-D7kG4YnK.js",revision:"b00f9a2b36cc9d1aed3cb464ad2ac73d"},{url:"assets/TwoSampleCalculator-cqqzlW-p.js",revision:"121e8ce5c5a31413d703028524312158"},{url:"assets/twoWayANOVA-CIsn6bwj.js",revision:"f454334eb06ff04c4adfbd8177c14cf3"},{url:"assets/TwoWayANOVA-CiVErBBN.js",revision:"f9e6d10cb0e78e9102ce4db081ba961f"},{url:"assets/UpdatePassword-CUJo7SRB.js",revision:"e366436ef604cb6b6d20f7ae00f40f5c"},{url:"assets/UserProfile-_iqLfjt2.js",revision:"8c40ea6bb23e73ecc27fe23ecae624ca"},{url:"assets/VariableSelector-CPdlCsJ2.js",revision:"337d8a26988208b68eb65a6a291cfbaf"},{url:"assets/VariableTree-BL5rEhkE.js",revision:"6c919fcd0a2cbe3c985ac92ce0cbffce"},{url:"assets/VideoTutorialsPage-CPJt_uWM.js",revision:"ccc9dcc39367e02afbcbf718a49d0833"},{url:"assets/VisualizationGuidePage-CGxTob5K.js",revision:"66b216b3eb9b5af4e6c11496415cdf3f"},{url:"assets/WhichTestPage-BVqGiyoM.js",revision:"e06c504d1d84e8ee5915223abb256e67"},{url:"assets/workbox-window.prod.es5-B9K5rw8f.js",revision:"********************************"},{url:"assets/xychartDiagram-VJFVF3MP-Ddgq57v-.js",revision:"46ed89f23829adaccb0f17617cd42d92"},{url:"beautiful-charts.png",revision:"06ea123263496bbbfa761c492deb964b"},{url:"cross-platform.png",revision:"76b3341a7b9f031095ac06344eeda273"},{url:"data-management.png",revision:"a0bfdd3a0a925215572000856adb1da5"},{url:"favicon.png",revision:"883c6b460faac350236c2794cf37f063"},{url:"firefox-auth-test.html",revision:"d3b796db362962c7cdcf128a8abda7ea"},{url:"hostinger-diagnostic.html",revision:"09b6193b7a8c0baa697067510aa413c0"},{url:"index.html",revision:"73cde2cab3c0fbe687fc190a6e960656"},{url:"logo.png",revision:"91bbf932de4ba2fdaa00d0472fb5cd74"},{url:"logout-diagnostic.html",revision:"43c6301466533eb1a9cd57e922d3c0d4"},{url:"manifest.webmanifest",revision:"aff90cce9f1107d34b9281a1649f71fb"},{url:"logo.png",revision:"91bbf932de4ba2fdaa00d0472fb5cd74"},{url:"manifest.webmanifest",revision:"aff90cce9f1107d34b9281a1649f71fb"}],{}),s.cleanupOutdatedCaches(),s.registerRoute(new s.NavigationRoute(s.createHandlerBoundToURL("index.html"),{allowlist:[/^(?!\/__).*/],denylist:[/^\/__/,/^\/api\//,/\.(?:js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot|json|xml|txt)$/]})),s.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new s.CacheFirst({cacheName:"google-fonts-cache",plugins:[new s.ExpirationPlugin({maxEntries:10,maxAgeSeconds:31536e3})]}),"GET"),s.registerRoute(/^https:\/\/.*\.supabase\.co\/.*$/i,new s.NetworkFirst({cacheName:"api-cache",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:50,maxAgeSeconds:300})]}),"GET"),s.registerRoute(/\.(?:png|jpg|jpeg|svg|gif|webp|ico)$/i,new s.CacheFirst({cacheName:"images-cache",plugins:[new s.ExpirationPlugin({maxEntries:100,maxAgeSeconds:2592e3})]}),"GET"),s.registerRoute(/^https?:\/\/[^\/]+\/(?:app|auth|pricing|knowledge-base|tutorials|video-tutorials|which-test|statistical-methods|visualization-guide|privacy-policy|terms-of-service).*$/i,new s.NetworkFirst({cacheName:"pages-cache",networkTimeoutSeconds:3,plugins:[new s.ExpirationPlugin({maxEntries:20,maxAgeSeconds:86400})]}),"GET")});
