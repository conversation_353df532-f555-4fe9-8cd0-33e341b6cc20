import{j as e,J as a,u as g,C as y,c5 as v,K as x,e as t,R as D,i as l,B as n,h as d,G as u,k as b,bz as A,d as C,l as j,f as w,bA as z}from"./mui-libs-CfwFIaTD.js";import{r as P}from"./react-libs-Cr2nE3UY.js";const c=[{id:"logistic-regression",name:"Logistic Regression Tutorial",shortDescription:"Understand and use the Logistic Regression analysis feature.",detailedDescription:"A comprehensive guide covering the basics of logistic regression, how to use the component, computational details, formula explanations, and interpretation of results.",filePath:"src/docs/tutorials/logistic-regression-tutorial.md",icon:e.jsx(a,{}),category:"Regression Analysis",color:"#FF9800"},{id:"t-tests-and-alternatives",name:"T-Tests and Alternatives",shortDescription:"Comprehensive reference guide for t-tests and non-parametric alternatives.",detailedDescription:"Detailed coverage of one-sample, independent samples, and paired t-tests, plus non-parametric alternatives including Mann-Whitney U, <PERSON>on signed-rank, and Sign tests. Includes formulas, assumptions, effect sizes, and interpretation guidelines.",filePath:"src/docs/tutorials/t-tests-and-alternatives-tutorial.md",icon:e.jsx(a,{}),category:"Inferential Statistics",color:"#2196F3"},{id:"anova-tests-and-alternatives",name:"ANOVA Tests and Alternatives",shortDescription:"Comprehensive reference guide for ANOVA tests and non-parametric alternatives.",detailedDescription:"Detailed coverage of one-way, two-way, repeated measures, and mixed-design ANOVA, plus non-parametric alternatives including Kruskal-Wallis, Friedman, and aligned rank transform tests. Includes formulas, assumptions, effect sizes, post-hoc testing, and interpretation guidelines.",filePath:"src/docs/tutorials/anova-tests-and-alternatives-tutorial.md",icon:e.jsx(a,{}),category:"Inferential Statistics",color:"#9C27B0"},{id:"numerical-descriptives",name:"Numerical Descriptives and Distributions",shortDescription:"Comprehensive reference guide for descriptive statistics of numerical data.",detailedDescription:"Detailed coverage of measures of central tendency, variability, and distribution shape. Includes confidence intervals, normality testing, data transformations, outlier detection, and interpretation guidelines for numerical data analysis.",filePath:"src/docs/tutorials/numerical-descriptives-tutorial.md",icon:e.jsx(a,{}),category:"Descriptive Statistics",color:"#4CAF50"},{id:"categorical-descriptives",name:"Categorical Descriptives and Association",shortDescription:"Comprehensive reference guide for categorical data analysis and association measures.",detailedDescription:"Detailed coverage of frequency tables, cross-tabulations, chi-square tests, measures of association, odds ratios, and specialized tests for categorical data. Includes effect sizes, confidence intervals, and interpretation guidelines.",filePath:"src/docs/tutorials/categorical-descriptives-tutorial.md",icon:e.jsx(a,{}),category:"Descriptive Statistics",color:"#FF5722"},{id:"epidemiological-calculators",name:"Epidemiological Calculators and Study Design",shortDescription:"Comprehensive reference guide for epidemiological methods and study designs.",detailedDescription:"Detailed coverage of case-control, cohort, and cross-sectional studies. Includes odds ratios, relative risk, attributable risk, diagnostic test evaluation, ROC curves, and sample size calculations for epidemiological research.",filePath:"src/docs/tutorials/epidemiological-calculators-tutorial.md",icon:e.jsx(a,{}),category:"Epidemiological Methods",color:"#795548"},{id:"sample-size-power-analysis",name:"Sample Size and Power Analysis",shortDescription:"Comprehensive reference guide for sample size calculations and power analysis.",detailedDescription:"Detailed coverage of power analysis fundamentals, sample size calculations for various study designs, effect size determination, survival analysis, cluster randomized trials, and practical guidelines for study planning.",filePath:"src/docs/tutorials/sample-size-power-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Study Design",color:"#607D8B"},{id:"correlation-linear-regression",name:"Correlation and Linear Regression Analysis",shortDescription:"Comprehensive reference guide for correlation analysis and linear regression modeling.",detailedDescription:"Detailed coverage of Pearson and Spearman correlations, simple and multiple linear regression, model assumptions, diagnostics, selection techniques, coefficient interpretation, and prediction intervals with practical examples.",filePath:"src/docs/tutorials/correlation-linear-regression-tutorial.md",icon:e.jsx(a,{}),category:"Regression Analysis",color:"#E91E63"},{id:"exploratory-factor-analysis",name:"Exploratory Factor Analysis (EFA)",shortDescription:"Comprehensive reference guide for exploratory factor analysis and latent structure discovery.",detailedDescription:"Detailed coverage of factor extraction methods, rotation techniques, factor retention criteria, interpretation guidelines, and practical applications for identifying underlying factors in multivariate data.",filePath:"src/docs/tutorials/exploratory-factor-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#FF5722"},{id:"confirmatory-factor-analysis",name:"Confirmatory Factor Analysis (CFA)",shortDescription:"Comprehensive reference guide for confirmatory factor analysis and measurement model validation.",detailedDescription:"Detailed coverage of CFA model specification, estimation methods, fit assessment, reliability and validity evaluation, measurement invariance, and structural equation modeling applications.",filePath:"src/docs/tutorials/confirmatory-factor-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#4CAF50"},{id:"survival-analysis",name:"Survival Analysis",shortDescription:"Comprehensive reference guide for time-to-event analysis and survival modeling.",detailedDescription:"Detailed coverage of Kaplan-Meier estimation, log-rank tests, Cox proportional hazards regression, parametric survival models, and advanced topics in survival analysis for medical and reliability research.",filePath:"src/docs/tutorials/survival-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#8BC34A"},{id:"reliability-analysis",name:"Reliability Analysis",shortDescription:"Comprehensive reference guide for measurement reliability and consistency assessment.",detailedDescription:"Detailed coverage of internal consistency, test-retest reliability, inter-rater reliability, generalizability theory, and advanced reliability measures for psychometric and measurement applications.",filePath:"src/docs/tutorials/reliability-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#00BCD4"},{id:"mediation-moderation",name:"Mediation and Moderation Analysis",shortDescription:"Comprehensive reference guide for mediation and moderation analysis in behavioral research.",detailedDescription:"Detailed coverage of simple and multiple mediation, moderation analysis, conditional process analysis, bootstrap methods, and advanced techniques for understanding complex variable relationships.",filePath:"src/docs/tutorials/mediation-moderation-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#673AB7"},{id:"cluster-analysis",name:"Cluster Analysis",shortDescription:"Comprehensive reference guide for cluster analysis and unsupervised learning methods.",detailedDescription:"Detailed coverage of hierarchical clustering, k-means, density-based clustering, model-based clustering, cluster validation, and practical applications for pattern recognition and data segmentation.",filePath:"src/docs/tutorials/cluster-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#9C27B0"},{id:"meta-analysis",name:"Meta-Analysis",shortDescription:"Comprehensive reference guide for meta-analysis and systematic review methods.",detailedDescription:"Detailed coverage of effect size calculation, fixed and random effects models, heterogeneity assessment, publication bias detection, moderator analysis, and advanced meta-analytic techniques.",filePath:"src/docs/tutorials/meta-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#2196F3"},{id:"variable-tree-analysis",name:"Variable Tree Analysis",shortDescription:"Comprehensive reference guide for hierarchical variable relationship visualization and analysis.",detailedDescription:"Detailed coverage of tree construction algorithms, multi-variable hierarchical analysis, interactive visualization techniques, and practical applications for exploring complex data structures.",filePath:"src/docs/tutorials/variable-tree-analysis-tutorial.md",icon:e.jsx(a,{}),category:"Advanced Analysis",color:"#795548"},{id:"data-visualization",name:"Data Visualization Methods",shortDescription:"Comprehensive reference guide for data visualization principles, techniques, and best practices.",detailedDescription:"Detailed coverage of visualization principles, chart type selection, statistical graphics, interactive visualization, color theory, accessibility considerations, and common visualization mistakes with practical solutions.",filePath:"src/docs/tutorials/data-visualization-tutorial.md",icon:e.jsx(a,{}),category:"Data Visualization",color:"#FF6F00"}],S=({onNavigate:s})=>{const r=g(),[o,m]=P.useState("All"),p=["All",...Array.from(new Set(c.map(i=>i.category)))].sort(),h=o==="All"?c:c.filter(i=>i.category===o),f=i=>{switch(i){case"Statistics":return e.jsx(a,{});default:return e.jsx(a,{})}};return e.jsxs(y,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(v,{sx:{mb:2},children:[e.jsx(x,{component:"button",onClick:()=>s==null?void 0:s("dashboard"),color:"inherit",sx:{background:"none",border:"none",cursor:"pointer",textDecoration:"underline","&:hover":{textDecoration:"none"}},children:"Home"}),e.jsx(t,{color:"text.primary",children:"Knowledge Base"})]}),e.jsxs(D,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${l(r.palette.success.main,.1)} 0%, ${l(r.palette.info.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(t,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Knowledge Base & Tutorials"}),e.jsx(t,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Learn how to use the application's features with detailed tutorials"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Browse our collection of guides and tutorials covering various statistical analyses, data management techniques, and more."})]}),e.jsxs(n,{sx:{mb:4},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),e.jsx(n,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:p.map(i=>e.jsx(d,{label:i,onClick:()=>m(i),variant:o===i?"filled":"outlined",color:o===i?"primary":"default",icon:i!=="All"?f(i):void 0,sx:{"&:hover":{backgroundColor:o===i?r.palette.primary.dark:l(r.palette.primary.main,.1)}}},i))})]}),e.jsx(u,{container:!0,spacing:3,children:h.map(i=>e.jsx(u,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(b,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:r.shadows[8],"& .launch-button":{backgroundColor:i.color,color:"white"}}},children:[e.jsx(A,{avatar:e.jsx(C,{sx:{bgcolor:i.color,width:48,height:48},children:i.icon}),title:e.jsx(t,{variant:"h6",fontWeight:"bold",children:i.name}),subheader:e.jsx(n,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:e.jsx(d,{label:i.category,size:"small",variant:"outlined",color:"primary"})})}),e.jsxs(j,{sx:{flexGrow:1,pt:0},children:[e.jsx(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:i.shortDescription}),e.jsx(t,{variant:"body2",paragraph:!0,children:i.detailedDescription})]}),e.jsx(n,{sx:{p:2,pt:0},children:e.jsx(w,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>s==null?void 0:s(`app/knowledge-base/${i.id}`),endIcon:e.jsx(z,{}),sx:{borderColor:i.color,color:i.color,fontWeight:"bold","&:hover":{borderColor:i.color}},children:"Read Tutorial"})})]})},i.id))})]})};export{S as default,c as tutorialOptions};
