var l=Object.defineProperty;var d=(t,e,i)=>e in t?l(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i;var n=(t,e,i)=>d(t,typeof e!="symbol"?e+"":e,i);class c{constructor(){n(this,"pyodide",null);n(this,"isInitialized",!1);n(this,"initializationPromise",null)}async initialize(){if(!this.isInitialized){if(this.initializationPromise)return this.initializationPromise;this.initializationPromise=this.doInitialize(),await this.initializationPromise}}async doInitialize(){try{if(typeof window<"u"&&!window.loadPyodide){const e=document.createElement("script");e.src="https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js",document.head.appendChild(e),await new Promise((i,s)=>{e.onload=i,e.onerror=s})}this.pyodide=await window.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.25.0/full/"}),await this.pyodide.loadPackage(["pandas","numpy"]),console.log("Pyodide initialized with basic packages"),this.setupBasicImplementation(),this.isInitialized=!0}catch(e){throw console.error("Failed to initialize Pyodide:",e),new Error("Failed to initialize Python environment for Cox regression analysis")}}setupBasicImplementation(){this.pyodide.runPython(`
import pandas as pd
import numpy as np
import json
import warnings
warnings.filterwarnings('ignore')

def partial_likelihood_gradient_hessian(beta, X, times, events):
    """Calculate gradient and Hessian for Cox partial likelihood"""
    n_obs, n_features = X.shape
    gradient = np.zeros(n_features)
    hessian = np.zeros((n_features, n_features))
    
    # Sort by time (ascending for proper risk set calculation)
    sort_idx = np.argsort(times)
    sorted_times = times[sort_idx]
    sorted_events = events[sort_idx]
    sorted_X = X[sort_idx]
    
    # Calculate risk scores
    risk_scores = np.exp(sorted_X @ beta)
    
    for i in range(n_obs):
        if sorted_events[i] == 1:  # Event occurred
            # Risk set: all individuals still at risk at time t_i
            at_risk_mask = sorted_times >= sorted_times[i]
            risk_set_X = sorted_X[at_risk_mask]
            risk_set_scores = risk_scores[at_risk_mask]
            
            if len(risk_set_scores) > 0 and np.sum(risk_set_scores) > 1e-10:
                # Weighted averages
                total_risk = np.sum(risk_set_scores)
                weights = risk_set_scores / total_risk
                
                # First moment (weighted mean)
                s1 = np.sum(risk_set_X.T * weights, axis=1)
                
                # Second moment for Hessian
                s2 = np.zeros((n_features, n_features))
                for j in range(len(risk_set_X)):
                    outer_prod = np.outer(risk_set_X[j], risk_set_X[j])
                    s2 += weights[j] * outer_prod
                
                # Update gradient and Hessian
                gradient += sorted_X[i] - s1
                hessian -= (s2 - np.outer(s1, s1))
    
    return gradient, hessian

def calculate_concordance(beta, X, times, events):
    """Calculate Harrell's C-index"""
    risk_scores = np.exp(X @ beta)
    n_obs = len(times)
    
    concordant = 0
    total_pairs = 0
    
    for i in range(n_obs):
        for j in range(i + 1, n_obs):
            # Only consider pairs where we can determine concordance
            if events[i] == 1 and times[i] < times[j]:
                total_pairs += 1
                if risk_scores[i] > risk_scores[j]:
                    concordant += 1
            elif events[j] == 1 and times[j] < times[i]:
                total_pairs += 1
                if risk_scores[j] > risk_scores[i]:
                    concordant += 1
    
    return concordant / max(total_pairs, 1) if total_pairs > 0 else 0.5

def run_cox_regression(data_dict):
    try:
        print("Received data keys:", list(data_dict.keys()))
        
        # Check if we have the required keys
        if 'time' not in data_dict:
            return json.dumps({'error': 'Missing time data'})
        if 'event' not in data_dict:
            return json.dumps({'error': 'Missing event data'})
            
        # Extract time and event data
        times = np.array(data_dict['time'])
        events = np.array(data_dict['event'])
        
        print(f"Time data length: {len(times)}")
        print(f"Event data length: {len(events)}")
        
        # Get covariate data
        covariate_keys = [key for key in data_dict.keys() if key not in ['time', 'event']]
        print("Covariate keys:", covariate_keys)
        
        if len(covariate_keys) == 0:
            return json.dumps({'error': 'No covariate data found'})
        
        # Build covariate matrix
        X_list = []
        for key in covariate_keys:
            covariate_data = np.array(data_dict[key])
            print(f"Covariate {key} length: {len(covariate_data)}")
            X_list.append(covariate_data)
        
        X = np.column_stack(X_list)
        print(f"Final X shape: {X.shape}")
        
        n_obs, n_features = X.shape
        
        if n_obs < 5:
            return json.dumps({'error': 'Insufficient data for Cox regression (need at least 5 observations)'})
        
        if len(times) != n_obs or len(events) != n_obs:
            return json.dumps({'error': f'Data length mismatch: times={len(times)}, events={len(events)}, covariates={n_obs}'})
        
        # Check for valid data
        if np.sum(events) == 0:
            return json.dumps({'error': 'No events found in the data'})
        
        # Initialize coefficients
        beta = np.zeros(n_features)
        
        # Newton-Raphson iterations
        max_iterations = 20
        tolerance = 1e-6
        
        for iteration in range(max_iterations):
            try:
                gradient, hessian = partial_likelihood_gradient_hessian(beta, X, times, events)
                
                # Add regularization to prevent singular matrix
                regularization = 1e-6 * np.eye(n_features)
                hessian_reg = hessian - regularization
                
                # Solve for update
                try:
                    delta = np.linalg.solve(hessian_reg, gradient)
                except np.linalg.LinAlgError:
                    # Fallback to pseudo-inverse
                    delta = np.linalg.pinv(hessian_reg) @ gradient
                
                # Line search / step size control
                step_size = 1.0
                max_step_size_iterations = 5
                
                for _ in range(max_step_size_iterations):
                    new_beta = beta - step_size * delta
                    
                    # Check if new beta gives reasonable risk scores
                    try:
                        test_scores = np.exp(X @ new_beta)
                        if np.all(np.isfinite(test_scores)) and np.max(test_scores) < 1e10:
                            beta = new_beta
                            break
                    except:
                        pass
                    
                    step_size *= 0.5
                
                # Convergence check
                if np.max(np.abs(delta)) < tolerance:
                    print(f"Converged after {iteration + 1} iterations")
                    break
                    
            except Exception as e:
                print(f"Iteration {iteration} failed: {e}")
                break
        
        # Calculate standard errors from Hessian
        try:
            _, hessian = partial_likelihood_gradient_hessian(beta, X, times, events)
            regularization = 1e-6 * np.eye(n_features)
            inv_hessian = np.linalg.inv(-hessian + regularization)
            std_errors = np.sqrt(np.maximum(np.diag(inv_hessian), 1e-8))
        except:
            std_errors = np.ones(n_features) * 0.1
        
        # Calculate p-values (Wald test)
        z_scores = beta / std_errors
        p_values = 2 * (1 - np.minimum(np.abs(z_scores) / 2, 0.999))
        p_values = np.maximum(p_values, 1e-10)
        
        # Calculate concordance index
        concordance = calculate_concordance(beta, X, times, events)
        
        # Calculate log-likelihood
        log_likelihood = 0
        risk_scores = np.exp(X @ beta)
        sort_idx = np.argsort(times)
        
        for i in sort_idx:
            if events[i] == 1:
                at_risk_mask = times >= times[i]
                risk_set_sum = np.sum(risk_scores[at_risk_mask])
                if risk_set_sum > 1e-10:
                    log_likelihood += X[i] @ beta - np.log(risk_set_sum)
        
        # Calculate AIC
        aic = 2 * n_features - 2 * log_likelihood
        
        # Prepare results using the original covariate keys
        results = {
            'coefficients': dict(zip(covariate_keys, beta.tolist())),
            'hazard_ratios': dict(zip(covariate_keys, np.exp(beta).tolist())),
            'confidence_intervals': {
                name: [float(np.exp(beta[i] - 1.96 * std_errors[i])), 
                       float(np.exp(beta[i] + 1.96 * std_errors[i]))]
                for i, name in enumerate(covariate_keys)
            },
            'p_values': dict(zip(covariate_keys, p_values.tolist())),
            'std_errors': dict(zip(covariate_keys, std_errors.tolist())),
            'concordance': float(concordance),
            'log_likelihood': float(log_likelihood),
            'aic': float(aic),
            'n_observations': int(n_obs),
            'n_events': int(np.sum(events))
        }
        
        print("Cox regression completed successfully")
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'Cox regression failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})

# Modified predict_survival function to use provided coefficients
def predict_survival(coefficients_dict, covariate_values_dict, time_points):
    try:
        print("Prediction - received coefficients:", coefficients_dict)
        print("Covariate values:", covariate_values_dict)
        print("Time points:", time_points)
        
        # Convert coefficients and covariate values to numpy arrays, ensuring order matches
        covariate_keys = list(coefficients_dict.keys())
        beta = np.array([coefficients_dict[key] for key in covariate_keys])
        covariate_array = np.array([covariate_values_dict.get(key, 0) for key in covariate_keys]) # Use .get with default 0 for safety
        
        if len(beta) != len(covariate_array):
             return json.dumps({'error': 'Coefficient and covariate value mismatch'})

        # Calculate linear predictor for new observation
        linear_pred = float(np.dot(beta, covariate_array))
        hazard_ratio = float(np.exp(linear_pred))
        
        # Note: This simplified baseline survival estimation is a placeholder.
        # A more robust approach would use the baseline hazard from the fitted model.
        # For now, we'll use a simple exponential decay as a fallback.
        
        survival_probs = {}
        
        # Using a simple exponential model for baseline survival as a placeholder
        # In a real scenario, you'd use the baseline hazard from the fitted model.
        baseline_hazard_rate = 0.05 # Example baseline hazard rate
        
        for time_point in time_points:
             # S(t|X) = S0(t)^exp(linear_predictor)
             # Using S0(t) = exp(-baseline_hazard_rate * t) as a simple example
             baseline_survival = np.exp(-baseline_hazard_rate * time_point)
             survival_prob = baseline_survival ** hazard_ratio
             survival_probs[str(time_point)] = float(max(0.001, min(0.999, survival_prob))) # Clamp between 0.001 and 0.999
        
        result = {
            'hazard_ratio': hazard_ratio,
            'survival_probabilities': survival_probs,
            'risk_score': linear_pred
        }
        
        print("Prediction completed successfully")
        return json.dumps(result)
        
    except Exception as e:
        error_msg = f'Prediction failed: {str(e)}'
        print("Prediction error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})
    `)}async runCoxRegression(e){await this.initialize();const i={time:e.time,event:e.event,...e.covariates};console.log("Sending data to Python:",{timeLength:e.time.length,eventLength:e.event.length,covariateKeys:Object.keys(e.covariates),covariateLengths:Object.fromEntries(Object.entries(e.covariates).map(([a,o])=>[a,o.length]))}),this.pyodide.globals.set("regression_data",this.pyodide.toPy(i));const s=this.pyodide.runPython("run_cox_regression(regression_data)"),r=JSON.parse(s);if(r.error)throw new Error(`Cox regression failed: ${r.error}`);return r}async predictSurvival(e,i,s){await this.initialize(),this.pyodide.globals.set("coefficients_dict",this.pyodide.toPy(e)),this.pyodide.globals.set("covariate_values_dict",this.pyodide.toPy(i)),this.pyodide.globals.set("time_points",this.pyodide.toPy(s));const r=this.pyodide.runPython("predict_survival(coefficients_dict, covariate_values_dict, time_points)"),a=JSON.parse(r);if(a.error)throw new Error(`Prediction failed: ${a.error}`);return a}isReady(){return this.isInitialized}}const p=new c;export{p as c};
