import{S as Ct,j}from"./other-utils-CR9xr_gI.js";import{m as n}from"./math-setup-BTRs7Kau.js";import{b as Ft}from"./descriptive-Djo0s6H4.js";const Bt=(A,b)=>{const _=Array.isArray(A[0])?A:A.map(e=>[e]);if(_.length!==b.length)throw new Error("Number of observations must be the same for X and y");if(_.length<3)throw new Error("Sample size must be at least 3");const r=_.length,s=_[0].length;if(s===1){const e=_.map(M=>M[0]),o=new Ct(e,b),w=o.intercept,q=o.slope,B=[q],I=o.predict(e),O=b.map((M,y)=>M-I[y]),U=O.reduce((M,y)=>M+y*y,0),xt=Ft(b),Et=b.reduce((M,y)=>M+Math.pow(y-xt,2),0),ut=1-U/Et,R=Math.sqrt(U/(r-2)),mt=e.reduce((M,y)=>M+y,0),k=e.reduce((M,y)=>M+y*y,0),N=k-mt*mt/r,G=R*Math.sqrt(k/(r*N)),$=R/Math.sqrt(N),H=[$],m=q/$,bt=w/G,Nt=[2*(1-j.studentt.cdf(Math.abs(m),r-2))],J=2*(1-j.studentt.cdf(Math.abs(bt),r-2)),K=m*m,vt=1-j.centralF.cdf(K,1,r-2),z=`y = ${w.toFixed(4)}${q>=0?" + ":" - "}${Math.abs(q).toFixed(4)}x`;return{coefficients:B,intercept:w,rSquared:ut,pValue:vt,n:r,stdErrors:H,interceptStdError:G,pValues:Nt,interceptPValue:J,residuals:O,predicted:I,equation:z,standardErrorOfRegression:R}}const L=_.map(e=>[1,...e]),u=Array(s+1).fill(0).map(()=>Array(s+1).fill(0));for(let e=0;e<s+1;e++)for(let o=0;o<s+1;o++)for(let w=0;w<r;w++)u[e][o]+=L[w][e]*L[w][o];const v=Array(s+1).fill(0);for(let e=0;e<s+1;e++)for(let o=0;o<r;o++)v[e]+=L[o][e]*b[o];const Z=n.inv(u),g=Array(s+1).fill(0);for(let e=0;e<s+1;e++)for(let o=0;o<s+1;o++)g[e]+=Z[e][o]*v[o];const W=g[0],X=g.slice(1),P=Array(r).fill(0);for(let e=0;e<r;e++){P[e]=W;for(let o=0;o<s;o++)P[e]+=X[o]*_[e][o]}const tt=b.map((e,o)=>e-P[o]),it=tt.reduce((e,o)=>e+o*o,0),yt=Ft(b),ct=b.reduce((e,o)=>e+Math.pow(o-yt,2),0),C=1-it/ct,et=Math.sqrt(it/(r-s-1)),nt=Array(s).fill(0);for(let e=0;e<s;e++)nt[e]=et*Math.sqrt(Z[e+1][e+1]);const lt=et*Math.sqrt(Z[0][0]),S=X.map((e,o)=>e/nt[o]),T=W/lt,ot=S.map(e=>2*(1-j.studentt.cdf(Math.abs(e),r-s-1))),D=2*(1-j.studentt.cdf(Math.abs(T),r-s-1)),pt=C/s/((1-C)/(r-s-1)),st=1-j.centralF.cdf(pt,s,r-s-1);let Y=`y = ${W.toFixed(4)}`;for(let e=0;e<s;e++){const o=X[e]>=0?" + ":" - ";Y+=`${o}${Math.abs(X[e]).toFixed(4)}x${e+1}`}return{coefficients:X,intercept:W,rSquared:C,pValue:st,n:r,stdErrors:nt,interceptStdError:lt,pValues:ot,interceptPValue:D,residuals:tt,predicted:P,equation:Y,standardErrorOfRegression:et}},Ut=(A,b,_=1e4,r=.05)=>{if(!b.every(t=>t===0||t===1))throw new Error("Dependent variable must contain only 0s and 1s for binary logistic regression");const s=b,L=Array.isArray(A[0])?A:A.map(t=>[t]),u=s.length,v=L[0].length;if(u!==L.length)throw new Error("X and yBinary must have the same number of observations");if(u<v+1)throw new Error(`Sample size (${u}) must be greater than the number of predictors + intercept (${v+1})`);u<10&&console.warn("Sample size is small (< 10) for logistic regression, results may be unstable.");const Z=L.map(t=>[1,...t]),g=n.matrix(Z),W=n.matrix(s),X=100,P=1e-8,tt=1e-10,it=s.map(t=>Math.log((t+.5)/(1.5-t))),yt=s.map(()=>.25),ct=n.diag(yt),C=n.transpose(g),et=n.multiply(n.multiply(C,ct),g),nt=n.multiply(n.multiply(C,ct),n.matrix(it)),lt=n.add(et,n.multiply(n.eye(v+1),1e-4));let S=n.multiply(n.inv(lt),nt),T=0,ot=!1,D=-1/0,pt=-1/0,st=0;for(;T<X&&!ot;){const t=n.multiply(g,S),a=n.map(t,p=>{const h=1/(1+Math.exp(-p));return Math.max(1e-10,Math.min(1-1e-10,h))}),l=n.map(a,p=>{const h=p*(1-p);return Math.max(tt,Math.min(h,.25))}),f=n.diag(l),d=a.valueOf(),x=l.valueOf(),c=W.valueOf(),at=t.valueOf().map((p,h)=>{const V=x[h];return V<1e-10?p:p+(c[h]-d[h])/V}),ft=n.matrix(at),i=n.transpose(g),F=n.multiply(i,f),St=n.multiply(F,g),It=n.multiply(F,ft);let dt;try{const p=1e-6*Math.pow(10,-T/X),h=n.add(St,n.multiply(n.eye(v+1),p)),V=n.inv(h);dt=n.multiply(V,It)}catch(p){console.error("Matrix inversion failed during IRLS iteration:",p),console.warn("Stopping IRLS due to numerical instability. Results may be inaccurate."),dt=S,T=X}let ht=c.reduce((p,h,V)=>{const wt=d[V],Pt=Math.log(Math.max(wt,1e-10)),rt=Math.log(Math.max(1-wt,1e-10));return p+(h*Pt+(1-h)*rt)},0),Xt=1,gt=dt;for(;ht<D&&Xt>tt;){Xt*=.5;const p=n.subtract(dt,S),h=n.multiply(p,Xt);gt=n.add(S,h);const V=n.multiply(g,gt),Pt=n.map(V,rt=>{const Mt=1/(1+Math.exp(-rt));return Math.max(1e-10,Math.min(1-1e-10,Mt))}).valueOf();ht=c.reduce((rt,Mt,$t)=>{const _t=Pt[$t],zt=Math.log(Math.max(_t,1e-10)),jt=Math.log(Math.max(1-_t,1e-10));return rt+(Mt*zt+(1-Mt)*jt)},0)}const Ot=n.subtract(gt,S),Rt=n.norm(Ot)/(n.norm(S)+P),kt=Math.abs((ht-D)/(D+P));Rt<P&&kt<P?(st++,st>=3&&(ot=!0)):st=0,S=gt,D=pt,pt=ht,T++}ot||console.warn(`IRLS did not converge within ${X} iterations.`);const Y=S.valueOf().flat(),e=Y[0],o=Y.slice(1),w=n.multiply(g,S),q=n.map(w,t=>1/(1+Math.exp(-t))),B=q.valueOf().map(t=>Math.max(0,Math.min(1,t)));let I=[];try{const a=q.valueOf().map(c=>Math.max(1e-10,c*(1-c))),l=n.diag(a),f=n.multiply(n.transpose(g),l),d=n.multiply(f,g),x=n.add(d,n.multiply(n.eye(v+1),1e-6));I=n.inv(x).valueOf()}catch(t){console.error("Failed to calculate final covariance matrix:",t),I=Array(v+1).fill(0).map(()=>Array(v+1).fill(NaN))}const O=I.map((t,a)=>Math.sqrt(Math.max(1e-10,t[a]))),U=O[0]||NaN,xt=O.slice(1),ut=Y.map((t,a)=>O[a]>0?t/O[a]:NaN).map(t=>isNaN(t)?NaN:2*(1-j.normal.cdf(Math.abs(t),0,1))),R=ut[0]||NaN,mt=ut.slice(1),k=B.map(t=>t>=.5?1:0);let N=0,G=0,$=0,H=0;const m=W.valueOf();for(let t=0;t<u;t++)k[t]===1&&m[t]===1?N++:k[t]===0&&m[t]===0?G++:k[t]===1&&m[t]===0?$++:k[t]===0&&m[t]===1&&H++;const bt=[[G,$],[H,N]],Nt=u>0?(N+G)/u:0,J=N+$===0?0:N/(N+$),K=N+H===0?0:N/(N+H),vt=J+K===0?0:2*(J*K)/(J+K),z=m.reduce((t,a,l)=>{const f=B[l],d=Math.log(Math.max(f,1e-10)),x=Math.log(Math.max(1-f,1e-10));return t+(a*d+(1-a)*x)},0),M=-2*z,y=2*(v+1)-2*z;let Q=0;try{const t=m.reduce((d,x)=>d+x,0),a=Math.max(Math.min(t/u,1-1e-10),1e-10),l=Math.log(a),f=Math.log(1-a);!isNaN(l)&&isFinite(l)&&!isNaN(f)&&isFinite(f)?Q=m.reduce((d,x)=>d+(x*l+(1-x)*f),0):Q=-u*Math.log(2)}catch(t){console.error("Error calculating null log-likelihood:",t),Q=-u*Math.log(2)}const Vt=Q!==0&&isFinite(z)&&isFinite(Q)?Math.max(0,Math.min(1,1-z/Q)):0;let E=.5;try{const t=m.filter(l=>l===1).length,a=m.filter(l=>l===0).length;if(t>0&&a>0){const l=B.map((i,F)=>({prob:i,label:m[F]})).sort((i,F)=>F.prob-i.prob);let f=0,d=0,x=-1,c=[{x:0,y:0}];for(const i of l)isNaN(i.prob)||!isFinite(i.prob)||(i.prob!==x&&(c.push({x:d/a,y:f/t}),x=i.prob),i.label===1?f++:d++);const at={x:d/a,y:f/t},ft=c[c.length-1];if((ft.x!==at.x||ft.y!==at.y)&&c.push(at),c.length>=2){E=0;for(let i=1;i<c.length;i++){const F=Math.max(0,c[i].x-c[i-1].x),St=(c[i].y+c[i-1].y)/2;E+=F*St}(isNaN(E)||!isFinite(E)||E<0||E>1)&&(E=.5)}}}catch(t){console.error("Error calculating AUC:",t),E=.5}const At=xt.map(t=>isFinite(t)&&!isNaN(t)?t:NaN),Lt=mt.map(t=>isFinite(t)&&!isNaN(t)?t:NaN),Wt=isFinite(U)&&!isNaN(U)?U:NaN,qt=isFinite(R)&&!isNaN(R)?R:NaN;return{coefficients:o,intercept:e,pValues:Lt,interceptPValue:qt,stdErrors:At,interceptStdError:Wt,logLikelihood:z,aic:y,pseudoRSquared:Vt,deviance:M,predictions:B,confusionMatrix:bt,accuracy:Nt,precision:J,recall:K,f1Score:vt,auc:E,n:u,covMatrix:I}};export{Ut as a,Bt as m};
