import{j as g}from"./other-utils-CR9xr_gI.js";const v=(t,e=.05)=>{const s=t.length;if(s<3)return{testName:"Shapiro-Wilk",statistic:NaN,pValue:NaN,isNormal:!1,alpha:e,sampleSize:s,interpretation:"Insufficient data for Shapiro-Wilk test (minimum 3 observations required)",recommendation:"Collect more data points"};if(s>5e3)return{testName:"Shapiro-Wilk",statistic:NaN,pValue:NaN,isNormal:!1,alpha:e,sampleSize:s,interpretation:"Sample size too large for Shapiro-Wilk test (maximum 5000 observations)",recommendation:"Use Kolmogorov-Smirnov or Jarque-Bera test for large samples"};const i=[...t].sort((f,a)=>f-a),{W:n,pValue:r}=k(i),o=r>=e;return{testName:"Shapiro-Wilk",statistic:n,pValue:r,isNormal:o,alpha:e,sampleSize:s,interpretation:o?`Data appears normally distributed (p = ${r.toFixed(4)} ≥ ${e})`:`Data significantly deviates from normal distribution (p = ${r.toFixed(4)} < ${e})`,recommendation:s<=50?"Shapiro-Wilk is the recommended test for this sample size":"Consider using other tests for larger samples"}},S=(t,e=.05)=>{const s=t.length;if(s<3)return{testName:"Kolmogorov-Smirnov",statistic:NaN,pValue:NaN,isNormal:!1,alpha:e,sampleSize:s,interpretation:"Insufficient data for KS test (minimum 3 observations required)"};const i=t.reduce((c,d)=>c+d,0)/s,n=t.reduce((c,d)=>c+Math.pow(d-i,2),0)/s,r=Math.sqrt(n);if(r===0)return{testName:"Kolmogorov-Smirnov",statistic:0,pValue:1,isNormal:!0,alpha:e,sampleSize:s,interpretation:"All data points are identical - cannot assess normality"};const o=[...t].sort((c,d)=>c-d);let f=0;for(let c=0;c<s;c++){const d=(o[c]-i)/r,l=g.normal.cdf(d,0,1),p=(c+1)/s,u=c/s,h=Math.abs(p-l),N=Math.abs(u-l);f=Math.max(f,h,N)}const a=q(f,s),m=a>=e;return{testName:"Kolmogorov-Smirnov",statistic:f,pValue:a,isNormal:m,alpha:e,sampleSize:s,interpretation:m?`Data appears normally distributed (p = ${a.toFixed(4)} ≥ ${e})`:`Data significantly deviates from normal distribution (p = ${a.toFixed(4)} < ${e})`,recommendation:s>=50?"KS test is suitable for this sample size":"Consider Shapiro-Wilk for smaller samples"}},M=(t,e=.05)=>{const s=t.length;if(s<4)return{testName:"Jarque-Bera",statistic:NaN,pValue:NaN,isNormal:!1,alpha:e,sampleSize:s,interpretation:"Insufficient data for Jarque-Bera test (minimum 4 observations required)"};const i=t.reduce((u,h)=>u+h,0)/s,n=t.reduce((u,h)=>u+Math.pow(h-i,2),0)/(s-1),r=Math.sqrt(n);if(r===0)return{testName:"Jarque-Bera",statistic:0,pValue:1,isNormal:!0,alpha:e,sampleSize:s,interpretation:"All data points are identical - cannot assess normality"};const o=t.map(u=>(u-i)/r),f=o.reduce((u,h)=>u+Math.pow(h,3),0)/s,a=o.reduce((u,h)=>u+Math.pow(h,4),0)/s,m=f,c=a-3,d=s/6*(Math.pow(m,2)+Math.pow(c,2)/4),l=1-g.chisquare.cdf(d,2),p=l>=e;return{testName:"Jarque-Bera",statistic:d,pValue:l,isNormal:p,alpha:e,sampleSize:s,interpretation:p?`Data appears normally distributed (p = ${l.toFixed(4)} ≥ ${e}). Skewness: ${m.toFixed(3)}, Kurtosis: ${c.toFixed(3)}`:`Data significantly deviates from normal distribution (p = ${l.toFixed(4)} < ${e}). Skewness: ${m.toFixed(3)}, Kurtosis: ${c.toFixed(3)}`,recommendation:"Jarque-Bera is particularly sensitive to skewness and kurtosis deviations"}},q=(t,e)=>{const i=Math.sqrt(e)*t;if(i<.27)return 1;if(i>3.1)return 0;let n=0,r,o=1;do r=Math.exp(-2*o*o*i*i),o%2===0?n-=r:n+=r,o++;while(r>1e-10&&o<=100);return Math.min(1,Math.max(0,2*n))},k=t=>{const e=t.length;if(e<=3)return{W:1,pValue:1};const s=t.reduce((a,m)=>a+m,0)/e,i=t.reduce((a,m)=>a+Math.pow(m-s,2),0);if(i===0)return{W:1,pValue:1};const n=D(e);let r=0;for(let a=0;a<e;a++)r+=n[a]*t[a];const o=r*r/i,f=z(o,e);return{W:Math.max(0,Math.min(1,o)),pValue:Math.max(0,Math.min(1,f))}},D=t=>{const e=new Array(t).fill(0),s=new Array(t);for(let r=0;r<t;r++){const o=(r+1-.375)/(t+.25);s[r]=g.normal.inv(o,0,1)}const i=s.reduce((r,o)=>r+o*o,0),n=1/Math.sqrt(i);for(let r=0;r<t;r++)e[r]=n*s[r];return e},z=(t,e)=>t>=1?1:t<=0?0:e<=11?V(t,e):b(t,e),V=(t,e)=>{const s=Math.log(t);let i=0,n=0,r=1;e===3?(i=0,n=-.767,r=.767):e<=5?(i=-.715,n=-1.038,r=.715):e<=7?(i=-.48,n=-.992,r=.662):e<=11&&(i=-.284,n=-.955,r=.623);const f=(s-n)/r;if(i!==0){const a=2/(i*i),m=Math.sqrt(a)*Math.asinh(i*f/Math.sqrt(2));return 1-g.normal.cdf(m,0,1)}else return 1-g.normal.cdf(f,0,1)},b=(t,e)=>{const s=Math.log(t),i=Math.log(e),n=-1.2725+1.0521*(i-Math.log(Math.PI))-.11135*i,r=Math.exp(-.4803-.082676*i+.0030302*i*i),o=-.37542-.47145/e+1.9279/(e*e)-2.1154/(e*e*e),f=(s-n)/r;if(Math.abs(o)>1e-8){const a=2/(o*o),m=Math.sqrt(a)*Math.asinh(o*f/Math.sqrt(2));return 1-g.normal.cdf(m,0,1)}else return 1-g.normal.cdf(f,0,1)},$=(t,e=.05)=>{const s=t.length;if(s<5)return{testName:"Anderson-Darling",statistic:NaN,pValue:NaN,isNormal:!1,alpha:e,sampleSize:s,interpretation:"Insufficient data for Anderson-Darling test (minimum 5 observations required)"};const i=t.reduce((l,p)=>l+p,0)/s,n=t.reduce((l,p)=>l+Math.pow(p-i,2),0)/(s-1),r=Math.sqrt(n);if(r===0)return{testName:"Anderson-Darling",statistic:0,pValue:1,isNormal:!0,alpha:e,sampleSize:s,interpretation:"All data points are identical - cannot assess normality"};const f=[...t].sort((l,p)=>l-p).map(l=>(l-i)/r);let a=0;for(let l=0;l<s;l++){const p=f[l],u=g.normal.cdf(p,0,1),h=(2*(l+1)-1)*Math.log(u),N=(2*(s-l)-1)*Math.log(1-u);u>1e-10&&u<1-1e-10&&(a+=h+N)}a=-s-a/s;const m=a*(1+.75/s+2.25/(s*s)),c=x(m),d=c>=e;return{testName:"Anderson-Darling",statistic:m,pValue:c,isNormal:d,alpha:e,sampleSize:s,interpretation:d?`Data appears normally distributed (p = ${c.toFixed(4)} ≥ ${e})`:`Data significantly deviates from normal distribution (p = ${c.toFixed(4)} < ${e})`,recommendation:"Anderson-Darling is particularly sensitive to deviations in the distribution tails"}},x=t=>t>=13.8?.001:t>=9.5?.005:t>=6?.01:t>=4?.025:t>=2.5?.05:t>=1.9?.1:t>=1.6?.15:t>=1.3?.25:t>=1?.5:t>=.6?.75:t>=.3?.9:t>=.2?.95:.99,y=(t,e=.05,s=["auto"])=>{const i=t.length;let n;s.includes("auto")?i<=50?n=["shapiroWilk","jarqueBera"]:i<=5e3?n=["kolmogorovSmirnov","jarqueBera","andersonDarling"]:n=["jarqueBera"]:n=s;let r;i<=50?r="Shapiro-Wilk":i<=5e3?r="Kolmogorov-Smirnov":r="Jarque-Bera";const o={};n.includes("shapiroWilk")&&(o.shapiroWilk=v(t,e)),n.includes("kolmogorovSmirnov")&&(o.kolmogorovSmirnov=S(t,e)),n.includes("jarqueBera")&&(o.jarqueBera=M(t,e)),n.includes("andersonDarling")&&(o.andersonDarling=$(t,e));const a=Object.values(o).filter(u=>!isNaN(u.pValue));if(a.length===0)return{sampleSize:i,recommendedTest:r,tests:o,overallAssessment:{isNormal:!1,confidence:"low",summary:"Unable to assess normality - insufficient data or all tests failed"}};const m=a.filter(u=>u.isNormal),c=m.length/a.length;let d,l,p;return c===1?(d=!0,l=a.length>=2?"high":"medium",p=`All ${a.length} test(s) indicate normal distribution`):c>=.5?(d=!0,l="medium",p=`${m.length} of ${a.length} tests indicate normal distribution`):(d=!1,l=a.length>=2?"high":"medium",p=`${a.length-m.length} of ${a.length} tests indicate non-normal distribution`),{sampleSize:i,recommendedTest:r,tests:o,overallAssessment:{isNormal:d,confidence:l,summary:p}}},w=t=>{const e=y(t,.05,["auto"]),s=e.recommendedTest.toLowerCase().replace(/[^a-z]/g,"");let i;return s.includes("shapiro")?i=e.tests.shapiroWilk:s.includes("kolmogorov")?i=e.tests.kolmogorovSmirnov:s.includes("jarque")&&(i=e.tests.jarqueBera),i?{isNormal:i.isNormal,pValue:i.pValue,statistic:i.statistic}:{isNormal:e.overallAssessment.isNormal,pValue:NaN,statistic:NaN}};export{y as c,w as i};
