import{u as Ze,j as e,B as C,e as s,R as L,ai as O,b9 as q,ba as _,bb as U,a6 as et,a7 as he,bt as tt,bC as rt,bQ as st,an as nt,G as a,k as Q,l as X,f as $,aP as Z,g as Ke,ae as at,h as J,bv as xe,L as se,m as K,r as T,ao as ne,ap as ae,aq as Te,ar as V,as as b,at as le,o as Pe,by as Ge,bs as lt}from"./mui-libs-CfwFIaTD.js";import{r as F}from"./react-libs-Cr2nE3UY.js";import{a as it,D as ze}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const me=u=>{const{children:r,value:g,index:o,...w}=u;return e.jsx("div",{role:"tabpanel",hidden:g!==o,id:`reliability-tabpanel-${o}`,"aria-labelledby":`reliability-tab-${o}`,...w,children:g===o&&e.jsx(C,{sx:{p:3},children:r})})},De=u=>{if(!u||u.length===0)return null;const r=u[0].length,g=u.length;if(r<=1||g<=1)return null;const o=u[0].map((x,n)=>u.map(c=>c[n]).reduce((c,i)=>c+i,0)/g),E=u[0].map((x,n)=>{const m=u.map(i=>i[n]),c=o[n];return m.reduce((i,v)=>i+Math.pow(v-c,2),0)/(g-1)}).reduce((x,n)=>x+n,0)/r;let I=0,S=0;for(let x=0;x<r;x++)for(let n=x+1;n<r;n++){const m=u.map(P=>P[x]),c=u.map(P=>P[n]),i=o[x],v=o[n];let M=0;for(let P=0;P<g;P++)M+=(m[P]-i)*(c[P]-v);M/=g-1,I+=M,S++}const h=S>0?I/S:0;return r*h/(E+(r-1)*h)},ot=(u,r)=>{if(u.length!==r.length)throw new Error("Raters must have the same number of ratings.");const g=u.length,o=[...new Set([...u,...r])],w=o.length,E=Array(w).fill(null).map(()=>Array(w).fill(0));for(let v=0;v<g;v++){const M=o.indexOf(u[v]),P=o.indexOf(r[v]);E[M][P]++}let I=0;for(let v=0;v<w;v++)I+=E[v][v];const S=I/g;let h=0;const p=Array(w).fill(0),x=Array(w).fill(0);for(let v=0;v<w;v++)for(let M=0;M<w;M++)p[v]+=E[v][M],x[M]+=E[v][M];for(let v=0;v<w;v++)h+=p[v]*x[v]/(g*g);const n=h,m=(S-n)/(1-n),c=Math.sqrt(S*(1-S)/(g*(1-n)*(1-n)));let i="";return m<=0?i="No agreement":m<=.2?i="Slight agreement":m<=.4?i="Fair agreement":m<=.6?i="Moderate agreement":m<=.8?i="Substantial agreement":i="Almost perfect agreement",{value:m,interpretation:i,confusionMatrix:E,categories:o,observedAgreement:S,expectedAgreement:n,standardError:c}},ct=u=>{if(!u||u.length===0)return null;const r=u.length,g=u[0].length,o={},w=new Set;u.forEach((m,c)=>{m.forEach(i=>{w.add(i),o[c]||(o[c]={}),o[c][i]=(o[c][i]||0)+1})});const E=Array.from(w),S=Object.keys(o).map(m=>{let c=0;for(const i in o[Number(m)]){const v=o[Number(m)][i];c+=v*(v-1)}return c/(g*(g-1))}).reduce((m,c)=>m+c,0)/r,h={};u.forEach(m=>{m.forEach(c=>{h[c]=(h[c]||0)+1})});let p=0;for(const m in h){const c=h[m];p+=Math.pow(c/(r*g),2)}const x=(S-p)/(1-p);let n="";return x<=0?n="Agreement no better than chance":x<=.2?n="Slight agreement":x<=.4?n="Fair agreement":x<=.6?n="Moderate agreement":x<=.8?n="Substantial agreement":n="Almost perfect agreement",{value:x,interpretation:n,observedAgreement:S,expectedAgreement:p,categories:E}},dt=(u,r)=>{if(u.length!==r.length)throw new Error("Arrays must have the same length");let g=0,o=0;const w=u.length;for(let h=0;h<w;h++)for(let p=h+1;p<w;p++){const x=u[h],n=r[h],m=u[p],c=r[p];x<m&&n<c||x>m&&n>c?g++:(x<m&&n>c||x>m&&n<c)&&o++}const E=(g-o)/(g+o);let I="";const S=Math.abs(E);return S>=.9?I="Very strong correlation":S>=.7?I="Strong correlation":S>=.5?I="Moderate correlation":S>=.3?I="Weak correlation":I="Very weak correlation",E<0&&(I="Negative "+I.toLowerCase()),{value:E,interpretation:I,details:{concordant:g,discordant:o,totalPairs:g+o}}},ut=u=>{const r=u.length,g=u[0].length,o=u.map(h=>h.reduce((p,x)=>p+x,0)),w=o.reduce((h,p)=>h+p,0)/r,E=o.reduce((h,p)=>{const x=p-w;return h+x*x},0),I=12*E/(g*g*(r*r*r-r));let S="";return I>=.9?S="Very strong agreement":I>=.7?S="Strong agreement":I>=.5?S="Moderate agreement":I>=.3?S="Fair agreement":I>=.1?S="Slight agreement":S="No agreement",{value:I,interpretation:S,details:{rowSums:o,meanRowSum:w,sumOfSquaredDeviations:E}}},ht=u=>{const r=u.length;if(r===0)return null;const g=u[0].length;if(g===0)return null;const o=u.map(c=>c.reduce((i,v)=>i+v,0)/g),E=u.reduce((c,i)=>c+i.reduce((v,M)=>v+M,0),0)/(r*g),I=o.reduce((c,i)=>c+Math.pow(i-E,2),0)*g;let S=0;for(let c=0;c<r;c++)for(let i=0;i<g;i++)S+=Math.pow(u[c][i]-o[c],2);const h=I/(r-1),p=S/(r*(g-1)),x=(h-p)/(h+(g-1)*p),n=h/p;let m="";return x>=.9?m="Excellent reliability":x>=.75?m="Good reliability":x>=.5?m="Moderate reliability":m="Poor reliability",{value:x,interpretation:m,model:"ICC(2,1)",msb:h,msw:p,f_value:n,details:{ssb:I,ssw:S,groupMeans:o,overallMean:E}}},Ct=()=>{var Fe,Me,Be,Ve,ke,Ne,We;const{datasets:u,currentDataset:r,setCurrentDataset:g}=it(),o=Ze(),[w,E]=F.useState(0),[I,S]=F.useState((r==null?void 0:r.id)||""),[h,p]=F.useState(!1),[x,n]=F.useState(null),[m,c]=F.useState([]),[i,v]=F.useState(null),[M,P]=F.useState(""),[ie,fe]=F.useState(""),[ee,ve]=F.useState([]),[B,be]=F.useState(null),[z,ye]=F.useState(null),[oe,Ce]=F.useState(""),[ce,Se]=F.useState(""),[de,we]=F.useState([]),[k,Ie]=F.useState(null),[D,Ae]=F.useState(null),[xt,Le]=F.useState([]),[te,Re]=F.useState([]),[W,Ee]=F.useState(null),Y=(r==null?void 0:r.columns.filter(t=>t.type===ze.NUMERIC))||[];r!=null&&r.columns.filter(t=>t.type===ze.CATEGORICAL);const ue=(r==null?void 0:r.columns)||[],Oe=t=>{const f=t.target.value;S(f),c([]),P(""),fe(""),ve([]),Ce(""),Se(""),we([]),Le([]),Re([]),v(null),be(null),ye(null),Ie(null),Ae(null),Ee(null);const d=u.find(j=>j.id===f);d&&g(d)},qe=(t,f)=>{E(f)},_e=()=>{if(!r||m.length<2){n("Please select at least 2 items for Cronbach's Alpha");return}p(!0),n(null);try{const t=[],f=m.map(y=>r.columns.find(A=>A.id===y)).filter(Boolean);if(r.data.forEach(y=>{const A=[];let R=!0;f.forEach(H=>{const N=y[H.name];typeof N=="number"&&!isNaN(N)?A.push(N):R=!1}),R&&A.length===f.length&&t.push(A)}),t.length<2)throw new Error("Not enough valid data rows for analysis");const d=De(t);if(d===null)throw new Error("Unable to calculate Cronbach's Alpha");const j=f.map((y,A)=>{const R=t.map(re=>re.filter((ge,pe)=>pe!==A)),H=De(R)||0,N=t.map(re=>re[A]),G=t.map(re=>re.reduce((ge,pe,$e)=>$e!==A?ge+pe:ge,0)),je=Je(N,G);return{item:y.name,correctedItemTotal:je,alphaIfDeleted:H}});let l="";d>=.9?l="Excellent internal consistency":d>=.8?l="Good internal consistency":d>=.7?l="Acceptable internal consistency":d>=.6?l="Questionable internal consistency":d>=.5?l="Poor internal consistency":l="Unacceptable internal consistency",v({value:d,interpretation:l,itemStatistics:j,confidence_interval:[d-.1,Math.min(d+.1,1)]})}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{p(!1)}},Ue=()=>{if(!r||!M||!ie){n("Please select two rater variables");return}p(!0),n(null);try{const t=r.columns.find(y=>y.id===M),f=r.columns.find(y=>y.id===ie);if(!t||!f)throw new Error("Selected variables not found");const d=[],j=[];if(r.data.forEach(y=>{const A=String(y[t.name]),R=String(y[f.name]);A&&R&&(d.push(A),j.push(R))}),d.length<10)throw new Error("Not enough valid data for Cohen's Kappa calculation");const l=ot(d,j);l&&be(l)}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{p(!1)}},Ye=()=>{if(!r||ee.length<2){n("Please select at least 2 rater variables");return}p(!0),n(null);try{const t=ee.map(j=>r.columns.find(l=>l.id===j)).filter(Boolean),f=[];if(r.data.forEach(j=>{const l=[];let y=!0;t.forEach(A=>{const R=j[A.name];R!=null&&String(R).trim()!==""?l.push(String(R)):y=!1}),y&&l.length===t.length&&f.push(l)}),f.length<10)throw new Error("Not enough valid data for Fleiss' Kappa calculation");const d=ct(f);d&&ye(d)}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{p(!1)}},He=()=>{if(!r||!oe||!ce){n("Please select two variables for Kendall's Tau");return}p(!0),n(null);try{const t=r.columns.find(y=>y.id===oe),f=r.columns.find(y=>y.id===ce);if(!t||!f)throw new Error("Selected variables not found");const d=[],j=[];if(r.data.forEach(y=>{const A=y[t.name],R=y[f.name];typeof A=="number"&&typeof R=="number"&&!isNaN(A)&&!isNaN(R)&&(d.push(A),j.push(R))}),d.length<5)throw new Error("Not enough valid data for Kendall's Tau calculation");const l=dt(d,j);l&&Ie(l)}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{p(!1)}},Qe=()=>{if(!r||de.length<2){n("Please select at least 2 ranking variables");return}p(!0),n(null);try{const t=de.map(l=>r.columns.find(y=>y.id===l)).filter(Boolean),f=[],d=r.data.length;for(let l=0;l<d;l++){const y=[];let A=!0;t.forEach(R=>{const H=r.data[l][R.name];typeof H=="number"&&!isNaN(H)?y.push(H):A=!1}),A&&y.length===t.length&&f.push(y)}if(f.length<3)throw new Error("Not enough valid data for Kendall's W calculation");const j=ut(f);j&&Ae(j)}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{p(!1)}},Xe=()=>{if(!r||te.length<2){n("Please select at least 2 measurement variables");return}p(!0),n(null);try{const t=te.map(j=>r.columns.find(l=>l.id===j)).filter(Boolean),f=[];if(r.data.forEach(j=>{const l=[];let y=!0;t.forEach(A=>{const R=j[A.name];typeof R=="number"&&!isNaN(R)?l.push(R):y=!1}),y&&l.length===t.length&&f.push(l)}),f.length<2)throw new Error("Not enough valid data for ICC calculation");const d=ht(f);d&&Ee(d)}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{p(!1)}},Je=(t,f)=>{const d=t.length,j=t.reduce((N,G)=>N+G,0),l=f.reduce((N,G)=>N+G,0),y=t.reduce((N,G,je)=>N+G*f[je],0),A=t.reduce((N,G)=>N+G*G,0),R=f.reduce((N,G)=>N+G*G,0);return(d*y-j*l)/Math.sqrt((d*A-j*j)*(d*R-l*l))};return e.jsxs(C,{sx:{overflowY:"auto",height:"100%"},children:[e.jsx(s,{variant:"h5",gutterBottom:!0,children:"Reliability Analysis"}),e.jsxs(L,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(s,{variant:"subtitle1",gutterBottom:!0,children:"Dataset Selection"}),e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Dataset"}),e.jsx(_,{value:I,label:"Dataset",onChange:Oe,children:u.map(t=>e.jsxs(U,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]})]}),e.jsxs(L,{elevation:2,children:[e.jsxs(et,{value:w,onChange:qe,"aria-label":"reliability analysis tabs",children:[e.jsx(he,{label:"Internal Consistency",icon:e.jsx(tt,{}),iconPosition:"start"}),e.jsx(he,{label:"Inter-rater Agreement",icon:e.jsx(rt,{}),iconPosition:"start"}),e.jsx(he,{label:"Rank Correlation",icon:e.jsx(st,{}),iconPosition:"start"}),e.jsx(he,{label:"Intraclass Correlation",icon:e.jsx(nt,{}),iconPosition:"start"})]}),e.jsx(me,{value:w,index:0,children:e.jsxs(a,{container:!0,spacing:3,children:[e.jsx(a,{item:!0,xs:12,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Cronbach's Alpha Analysis"}),e.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Measures internal consistency - how closely related a set of items are as a group."}),e.jsxs(O,{fullWidth:!0,sx:{mt:2},children:[e.jsx(q,{children:"Select Items (Variables)"}),e.jsx(_,{multiple:!0,value:m,label:"Select Items (Variables)",onChange:t=>c(typeof t.target.value=="string"?t.target.value.split(","):t.target.value),disabled:!r,renderValue:t=>t.map(d=>{const j=Y.find(l=>l.id===d);return j?j.name:""}).filter(Boolean).join(", "),children:Y.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]}),e.jsx($,{variant:"contained",color:"primary",startIcon:e.jsx(Z,{}),onClick:_e,disabled:h||m.length<2,sx:{mt:2},children:"Calculate Cronbach's Alpha"})]})})}),x&&e.jsx(a,{item:!0,xs:12,children:e.jsx(Ke,{severity:"error",children:x})}),h&&e.jsx(a,{item:!0,xs:12,children:e.jsx(C,{display:"flex",justifyContent:"center",children:e.jsx(at,{})})}),i&&!h&&e.jsxs(e.Fragment,{children:[e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Cronbach's Alpha Result"}),e.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h3",color:"primary",children:i.value.toFixed(3)}),e.jsx(C,{ml:2,children:e.jsx(J,{label:i.interpretation,color:i.value>=.7?"success":"warning"})})]}),e.jsx(xe,{variant:"determinate",value:i.value*100,sx:{height:10,borderRadius:5},color:i.value>=.7?"success":"warning"}),e.jsxs(C,{mt:2,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Interpretation Guidelines:"}),e.jsxs(se,{dense:!0,children:[e.jsx(K,{children:e.jsx(T,{primary:"≥ 0.9: Excellent"})}),e.jsx(K,{children:e.jsx(T,{primary:"≥ 0.8: Good"})}),e.jsx(K,{children:e.jsx(T,{primary:"≥ 0.7: Acceptable"})}),e.jsx(K,{children:e.jsx(T,{primary:"< 0.7: Questionable/Poor"})})]})]})]})}),e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Item Statistics"}),e.jsx(ne,{children:e.jsxs(ae,{size:"small",children:[e.jsx(Te,{children:e.jsxs(V,{children:[e.jsx(b,{children:"Item"}),e.jsx(b,{align:"right",children:"Item-Total Correlation"}),e.jsx(b,{align:"right",children:"Alpha if Deleted"})]})}),e.jsx(le,{children:(Fe=i.itemStatistics)==null?void 0:Fe.map(t=>e.jsxs(V,{children:[e.jsx(b,{children:t.item}),e.jsx(b,{align:"right",children:t.correctedItemTotal.toFixed(3)}),e.jsx(b,{align:"right",children:e.jsx(J,{label:t.alphaIfDeleted.toFixed(3),size:"small",color:t.alphaIfDeleted>i.value?"warning":"default"})})]},t.item))})]})}),e.jsx(s,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Items that increase alpha when deleted may be candidates for removal."})]})})]})]})}),e.jsx(me,{value:w,index:1,children:e.jsxs(a,{container:!0,spacing:3,children:[e.jsx(a,{item:!0,xs:12,lg:6,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Cohen's Kappa (Two Raters)"}),e.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Measures agreement between two raters for categorical data."}),e.jsxs(a,{container:!0,spacing:2,children:[e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Rater 1"}),e.jsx(_,{value:M,label:"Rater 1",onChange:t=>P(t.target.value),disabled:!r,children:ue.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]})}),e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Rater 2"}),e.jsx(_,{value:ie,label:"Rater 2",onChange:t=>fe(t.target.value),disabled:!r,children:ue.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]})})]}),e.jsx($,{variant:"contained",color:"primary",startIcon:e.jsx(Z,{}),onClick:Ue,disabled:h||!M||!ie,sx:{mt:2},fullWidth:!0,children:"Calculate Cohen's Kappa"})]})})}),e.jsx(a,{item:!0,xs:12,lg:6,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Fleiss' Kappa (Multiple Raters)"}),e.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Measures agreement among multiple raters for categorical data."}),e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Select Raters"}),e.jsx(_,{multiple:!0,value:ee,label:"Select Raters",onChange:t=>ve(typeof t.target.value=="string"?t.target.value.split(","):t.target.value),disabled:!r,renderValue:t=>t.map(d=>{const j=ue.find(l=>l.id===d);return j?j.name:""}).filter(Boolean).join(", "),children:ue.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]}),e.jsx($,{variant:"contained",color:"primary",startIcon:e.jsx(Z,{}),onClick:Ye,disabled:h||ee.length<2,sx:{mt:2},fullWidth:!0,children:"Calculate Fleiss' Kappa"})]})})}),(B||z)&&!h&&e.jsx(a,{item:!0,xs:12,children:e.jsxs(a,{container:!0,spacing:2,children:[B&&e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Cohen's Kappa Result"}),e.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h3",color:"primary",children:B.value.toFixed(3)}),e.jsx(C,{ml:2,children:e.jsx(J,{label:B.interpretation,color:B.value>=.6?"success":"warning"})})]}),e.jsx(ne,{children:e.jsx(ae,{size:"small",children:e.jsxs(le,{children:[e.jsxs(V,{children:[e.jsx(b,{children:"Observed Agreement"}),e.jsxs(b,{align:"right",children:[(B.observedAgreement*100).toFixed(1),"%"]})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Expected Agreement"}),e.jsxs(b,{align:"right",children:[(B.expectedAgreement*100).toFixed(1),"%"]})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Standard Error"}),e.jsx(b,{align:"right",children:(Me=B.standardError)==null?void 0:Me.toFixed(4)})]})]})})}),B.confusionMatrix&&e.jsxs(C,{mt:2,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Confusion Matrix"}),e.jsx(ne,{children:e.jsxs(ae,{size:"small",children:[e.jsx(Te,{children:e.jsxs(V,{children:[e.jsx(b,{children:"Rater 1 \\ Rater 2"}),(Be=B.categories)==null?void 0:Be.map(t=>e.jsx(b,{align:"center",children:t},t))]})}),e.jsx(le,{children:B.confusionMatrix.map((t,f)=>{var d;return e.jsxs(V,{children:[e.jsx(b,{children:(d=B.categories)==null?void 0:d[f]}),t.map((j,l)=>e.jsx(b,{align:"center",children:j},l))]},f)})})]})})]})]})}),z&&e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Fleiss' Kappa Result"}),e.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h3",color:"primary",children:z.value.toFixed(3)}),e.jsx(C,{ml:2,children:e.jsx(J,{label:z.interpretation,color:z.value>=.6?"success":"warning"})})]}),e.jsx(ne,{children:e.jsx(ae,{size:"small",children:e.jsxs(le,{children:[e.jsxs(V,{children:[e.jsx(b,{children:"Average Observed Agreement"}),e.jsxs(b,{align:"right",children:[(z.observedAgreement*100).toFixed(1),"%"]})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Expected Agreement by Chance"}),e.jsxs(b,{align:"right",children:[(z.expectedAgreement*100).toFixed(1),"%"]})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Number of Raters"}),e.jsx(b,{align:"right",children:ee.length})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Categories"}),e.jsx(b,{align:"right",children:(Ve=z.categories)==null?void 0:Ve.join(", ")})]})]})})})]})})]})})]})}),e.jsx(me,{value:w,index:2,children:e.jsxs(a,{container:!0,spacing:3,children:[e.jsx(a,{item:!0,xs:12,lg:6,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Kendall's Tau"}),e.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Measures rank correlation between two variables."}),e.jsxs(a,{container:!0,spacing:2,children:[e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Variable 1"}),e.jsx(_,{value:oe,label:"Variable 1",onChange:t=>Ce(t.target.value),disabled:!r,children:Y.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]})}),e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Variable 2"}),e.jsx(_,{value:ce,label:"Variable 2",onChange:t=>Se(t.target.value),disabled:!r,children:Y.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]})})]}),e.jsx($,{variant:"contained",color:"primary",startIcon:e.jsx(Z,{}),onClick:He,disabled:h||!oe||!ce,sx:{mt:2},fullWidth:!0,children:"Calculate Kendall's Tau"})]})})}),e.jsx(a,{item:!0,xs:12,lg:6,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Kendall's W (Coefficient of Concordance)"}),e.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Measures agreement among multiple raters for ranked data."}),e.jsxs(O,{fullWidth:!0,children:[e.jsx(q,{children:"Select Ranking Variables"}),e.jsx(_,{multiple:!0,value:de,label:"Select Ranking Variables",onChange:t=>we(typeof t.target.value=="string"?t.target.value.split(","):t.target.value),disabled:!r,renderValue:t=>t.map(d=>{const j=Y.find(l=>l.id===d);return j?j.name:""}).filter(Boolean).join(", "),children:Y.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]}),e.jsx($,{variant:"contained",color:"primary",startIcon:e.jsx(Z,{}),onClick:Qe,disabled:h||de.length<2,sx:{mt:2},fullWidth:!0,children:"Calculate Kendall's W"})]})})}),(k||D)&&!h&&e.jsx(a,{item:!0,xs:12,children:e.jsxs(a,{container:!0,spacing:2,children:[k&&e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Kendall's Tau Result"}),e.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h3",color:"primary",children:k.value.toFixed(3)}),e.jsx(C,{ml:2,children:e.jsx(J,{label:k.interpretation,color:Math.abs(k.value)>=.5?"success":"warning"})})]}),e.jsx(xe,{variant:"determinate",value:Math.abs(k.value)*100,sx:{height:10,borderRadius:5},color:Math.abs(k.value)>=.5?"success":"warning"}),k.details&&e.jsxs(C,{mt:2,children:[e.jsxs(s,{variant:"body2",children:["Concordant pairs: ",k.details.concordant]}),e.jsxs(s,{variant:"body2",children:["Discordant pairs: ",k.details.discordant]}),e.jsxs(s,{variant:"body2",children:["Total pairs: ",k.details.totalPairs]})]})]})}),D&&e.jsx(a,{item:!0,xs:12,md:6,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Kendall's W Result"}),e.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h3",color:"primary",children:D.value.toFixed(3)}),e.jsx(C,{ml:2,children:e.jsx(J,{label:D.interpretation,color:D.value>=.7?"success":"warning"})})]}),e.jsx(xe,{variant:"determinate",value:D.value*100,sx:{height:10,borderRadius:5},color:D.value>=.7?"success":"warning"}),e.jsx(C,{mt:2,children:e.jsx(s,{variant:"body2",color:"text.secondary",children:"W ranges from 0 (no agreement) to 1 (complete agreement)"})})]})})]})})]})}),e.jsx(me,{value:w,index:3,children:e.jsxs(a,{container:!0,spacing:3,children:[e.jsx(a,{item:!0,xs:12,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Intraclass Correlation Coefficient (ICC)"}),e.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Assesses reliability of ratings when multiple observers measure the same subjects."}),e.jsxs(O,{fullWidth:!0,sx:{mt:2},children:[e.jsx(q,{children:"Select Measurement Variables"}),e.jsx(_,{multiple:!0,value:te,label:"Select Measurement Variables",onChange:t=>Re(typeof t.target.value=="string"?t.target.value.split(","):t.target.value),disabled:!r,renderValue:t=>t.map(d=>{const j=Y.find(l=>l.id===d);return j?j.name:""}).filter(Boolean).join(", "),children:Y.map(t=>e.jsx(U,{value:t.id,children:t.name},t.id))})]}),e.jsx($,{variant:"contained",color:"primary",startIcon:e.jsx(Z,{}),onClick:Xe,disabled:h||te.length<2,sx:{mt:2},children:"Calculate ICC"})]})})}),W&&!h&&e.jsx(a,{item:!0,xs:12,children:e.jsxs(L,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"ICC Result"}),e.jsxs(a,{container:!0,spacing:2,children:[e.jsxs(a,{item:!0,xs:12,md:6,children:[e.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h3",color:"primary",children:W.value.toFixed(3)}),e.jsx(C,{ml:2,children:e.jsx(J,{label:W.interpretation,color:W.value>=.75?"success":"warning"})})]}),e.jsx(xe,{variant:"determinate",value:W.value*100,sx:{height:10,borderRadius:5},color:W.value>=.75?"success":"warning"}),e.jsx(C,{mt:2,children:e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,children:["Model: ",W.model]})})]}),e.jsx(a,{item:!0,xs:12,md:6,children:e.jsx(ne,{children:e.jsx(ae,{size:"small",children:e.jsxs(le,{children:[e.jsxs(V,{children:[e.jsx(b,{children:"Mean Square Between"}),e.jsx(b,{align:"right",children:(ke=W.msb)==null?void 0:ke.toFixed(3)})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Mean Square Within"}),e.jsx(b,{align:"right",children:(Ne=W.msw)==null?void 0:Ne.toFixed(3)})]}),e.jsxs(V,{children:[e.jsx(b,{children:"F-value"}),e.jsx(b,{align:"right",children:(We=W.f_value)==null?void 0:We.toFixed(3)})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Number of Subjects"}),e.jsx(b,{align:"right",children:r==null?void 0:r.data.length})]}),e.jsxs(V,{children:[e.jsx(b,{children:"Number of Measurements"}),e.jsx(b,{align:"right",children:te.length})]})]})})})}),e.jsx(a,{item:!0,xs:12,children:e.jsxs(L,{elevation:0,sx:{p:2,backgroundColor:o.palette.grey[50]},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"ICC Interpretation Guidelines:"}),e.jsxs(a,{container:!0,spacing:1,children:[e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsxs(se,{dense:!0,children:[e.jsxs(K,{children:[e.jsx(Pe,{fontSize:"small",sx:{mr:1,color:o.palette.success.main}}),e.jsx(T,{primary:"≥ 0.90: Excellent reliability"})]}),e.jsxs(K,{children:[e.jsx(Pe,{fontSize:"small",sx:{mr:1,color:o.palette.info.main}}),e.jsx(T,{primary:"0.75 - 0.90: Good reliability"})]})]})}),e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsxs(se,{dense:!0,children:[e.jsxs(K,{children:[e.jsx(Ge,{fontSize:"small",sx:{mr:1,color:o.palette.warning.main}}),e.jsx(T,{primary:"0.50 - 0.75: Moderate reliability"})]}),e.jsxs(K,{children:[e.jsx(Ge,{fontSize:"small",sx:{mr:1,color:o.palette.error.main}}),e.jsx(T,{primary:"< 0.50: Poor reliability"})]})]})})]})]})})]})]})})]})})]}),x&&e.jsx(C,{mt:2,children:e.jsx(Ke,{severity:"error",onClose:()=>n(null),children:x})}),e.jsx(C,{mt:3,children:e.jsxs(L,{elevation:0,sx:{p:2,backgroundColor:o.palette.info.light+"20"},children:[e.jsxs(C,{display:"flex",alignItems:"center",mb:1,children:[e.jsx(lt,{sx:{mr:1,color:o.palette.info.main}}),e.jsx(s,{variant:"h6",children:"Understanding Reliability Analysis"})]}),e.jsxs(a,{container:!0,spacing:2,children:[e.jsxs(a,{item:!0,xs:12,md:6,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,fontWeight:"bold",children:"When to use each method:"}),e.jsxs(se,{dense:!0,children:[e.jsx(K,{children:e.jsx(T,{primary:"Cronbach's Alpha",secondary:"For scale reliability with multiple items measuring the same construct"})}),e.jsx(K,{children:e.jsx(T,{primary:"Cohen's Kappa",secondary:"For agreement between two raters on categorical data"})}),e.jsx(K,{children:e.jsx(T,{primary:"Fleiss' Kappa",secondary:"For agreement among multiple raters on categorical data"})})]})]}),e.jsxs(a,{item:!0,xs:12,md:6,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,fontWeight:"bold",children:"Additional methods:"}),e.jsxs(se,{dense:!0,children:[e.jsx(K,{children:e.jsx(T,{primary:"Kendall's Tau",secondary:"For rank correlation between two ordinal variables"})}),e.jsx(K,{children:e.jsx(T,{primary:"Kendall's W",secondary:"For agreement among multiple rankers"})}),e.jsx(K,{children:e.jsx(T,{primary:"ICC",secondary:"For reliability of continuous measurements across raters or time"})})]})]})]}),e.jsxs(C,{mt:2,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,fontWeight:"bold",children:"General Guidelines:"}),e.jsxs(s,{variant:"body2",color:"text.secondary",children:["• Higher values indicate better reliability/agreement (usually 0-1 scale)",e.jsx("br",{}),"• Values above 0.7 are generally considered acceptable for research",e.jsx("br",{}),"• For clinical or high-stakes applications, values above 0.9 are preferred",e.jsx("br",{}),"• Always consider the context and purpose of your analysis"]})]})]})}),(i||B||z||k||D||W)&&e.jsx(C,{mt:3,children:e.jsx(Q,{children:e.jsxs(X,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Analysis Summary"}),e.jsxs(a,{container:!0,spacing:2,children:[i&&e.jsx(a,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(C,{textAlign:"center",children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",children:"Cronbach's Alpha"}),e.jsx(s,{variant:"h4",color:"primary",children:i.value.toFixed(3)}),e.jsx(s,{variant:"caption",children:i.interpretation})]})}),B&&e.jsx(a,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(C,{textAlign:"center",children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",children:"Cohen's Kappa"}),e.jsx(s,{variant:"h4",color:"primary",children:B.value.toFixed(3)}),e.jsx(s,{variant:"caption",children:B.interpretation})]})}),z&&e.jsx(a,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(C,{textAlign:"center",children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",children:"Fleiss' Kappa"}),e.jsx(s,{variant:"h4",color:"primary",children:z.value.toFixed(3)}),e.jsx(s,{variant:"caption",children:z.interpretation})]})}),k&&e.jsx(a,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(C,{textAlign:"center",children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",children:"Kendall's Tau"}),e.jsx(s,{variant:"h4",color:"primary",children:k.value.toFixed(3)}),e.jsx(s,{variant:"caption",children:k.interpretation})]})}),D&&e.jsx(a,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(C,{textAlign:"center",children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",children:"Kendall's W"}),e.jsx(s,{variant:"h4",color:"primary",children:D.value.toFixed(3)}),e.jsx(s,{variant:"caption",children:D.interpretation})]})}),W&&e.jsx(a,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(C,{textAlign:"center",children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",children:"ICC"}),e.jsx(s,{variant:"h4",color:"primary",children:W.value.toFixed(3)}),e.jsx(s,{variant:"caption",children:W.interpretation})]})})]})]})})})]})};export{Ct as default};
