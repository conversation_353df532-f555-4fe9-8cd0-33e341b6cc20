const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./charts-plotly-BhN4fPIu.js","./react-libs-Cr2nE3UY.js","./mui-libs-CfwFIaTD.js","./charts-recharts-d3-BEF1Y_jn.js","./charts-plotly-CuCRB34y.css"])))=>i.map(i=>d[i]);
import{_ as Ae}from"./supabase-lib-B3goak-P.js";import{u as Ee,j as e,B as d,e as x,G as ie,R as N,a6 as Le,aE as Y,a7 as pe,c7 as Te,aW as Oe,F as je,ai as R,b9 as q,ba as F,bb as j,c8 as Be,aj as K,b2 as Q,f as ve,ae as ye,aa as Ce,I as Se,b7 as Ne,bH as Re,ah as re,ao as qe,ap as Fe,aq as Ge,ar as we,as as c,at as _e}from"./mui-libs-CfwFIaTD.js";import{r as g,b as ne}from"./react-libs-Cr2nE3UY.js";import{a as We,D as ke,g as oe}from"./index-Bpan7Tbe.js";import{j as se,b as ce}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{l as de,P as He}from"./charts-plotly-BhN4fPIu.js";import"./ml-tensorflow-D19WVUQh.js";import"./math-lib-BOZ-XUok.js";import"./charts-recharts-d3-BEF1Y_jn.js";const ze={title:"Box Plot",xAxisLabel:"Category",yAxisLabel:"Value",showOutliers:!0,showMean:!1,horizontal:!1,colorScheme:"default",showNotches:!1},he={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],sequential:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],diverging:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"]},ue="plotlyBoxPlotDiv",la=()=>{const{datasets:D,currentDataset:p}=We(),i=Ee(),G=g.useRef(null),[M,xe]=g.useState((p==null?void 0:p.id)||""),[k,U]=g.useState(""),[v,X]=g.useState(""),[A,J]=g.useState(""),[t,ge]=g.useState(ze),[y,Z]=g.useState("variables"),[z,E]=g.useState(null),[me,L]=g.useState([]),[I,ee]=g.useState(!1),[ae,C]=g.useState(null),o=ne.useMemo(()=>M&&D.find(a=>a.id===M)||null,[D,M]),te=ne.useMemo(()=>(o==null?void 0:o.columns.filter(a=>a.type===ke.NUMERIC))||[],[o]),_=ne.useMemo(()=>(o==null?void 0:o.columns.filter(a=>a.type===ke.CATEGORICAL))||[],[o]);g.useEffect(()=>{p!=null&&p.id&&p.id!==M&&(xe(p.id),U(""),X(""),J(""),E(null),L([]),C(null))},[p]),g.useEffect(()=>{if(z&&G.current){const a={responsive:!0};de.newPlot(ue,z.data,z.layout,a)}return()=>{G.current&&typeof He<"u"&&de.purge&&de.purge(G.current)}},[z]),g.useEffect(()=>{const a=l=>{l.altKey&&!l.ctrlKey&&!l.shiftKey&&(l.key==="1"?(l.preventDefault(),Z("variables")):l.key==="2"&&(l.preventDefault(),Z("settings")))};return window.addEventListener("keydown",a),()=>window.removeEventListener("keydown",a)},[]);const Ie=a=>{const l=a.target.value;xe(l),U(""),X(""),J(""),E(null),L([]),C(null)},S=(a,l)=>{ge(m=>({...m,[a]:l}))},be=()=>{if(!o||!k){C("Please select a dataset and a value variable.");return}ee(!0),C(null),E(null),L([]);try{const a=o.columns.find(r=>r.id===k),l=v?o.columns.find(r=>r.id===v):null,m=A?o.columns.find(r=>r.id===A):null;if(!a)throw new Error("Value column not found.");const W=[],H=[],T=he[t.colorScheme]||he.default,le=o.data;if(!l&&!m){const r=le.map(n=>n[a.name]).filter(n=>typeof n=="number"&&!isNaN(n));if(r.length===0)throw new Error("No numeric data found for the selected value variable.");W.push({y:t.horizontal?void 0:r,x:t.horizontal?r:void 0,type:"box",name:a.name,marker:{color:T[0]},boxpoints:t.showOutliers?"outliers":!1,boxmean:t.showMean?"sd":!1,notched:t.showNotches,orientation:t.horizontal?"h":"v"});const w=[...r].sort((n,u)=>n-u),[b,h,s]=se(w);H.push({category:a.name,min:Math.min(...w),max:Math.max(...w),q1:b,median:h,q3:s,mean:ce(w),iqr:s-b,sampleSize:w.length})}else if(l&&!m){const r={};le.forEach(b=>{const h=String(b[l.name]),s=b[a.name];typeof s=="number"&&!isNaN(s)&&(r[h]||(r[h]=[]),r[h].push(s))}),oe(v,o).forEach((b,h)=>{const s=r[b];if(!s||s.length===0)return;W.push({y:t.horizontal?void 0:s,x:t.horizontal?s:void 0,type:"box",name:b,marker:{color:T[h%T.length]},boxpoints:t.showOutliers?"outliers":!1,boxmean:t.showMean?"sd":!1,notched:t.showNotches,orientation:t.horizontal?"h":"v"});const n=[...s].sort((f,V)=>f-V),[u,B,P]=se(n);H.push({category:b,min:Math.min(...n),max:Math.max(...n),q1:u,median:B,q3:P,mean:ce(n),iqr:P-u,sampleSize:n.length})})}else if(a&&l&&m){const r={};le.forEach(h=>{const s=h[a.name],n=String(h[l.name]),u=String(h[m.name]);typeof s=="number"&&!isNaN(s)&&(r[u]||(r[u]={}),r[u][n]||(r[u][n]=[]),r[u][n].push(s))});const w=oe(A,o),b=oe(v,o);w.forEach((h,s)=>{const n=r[h];if(!n)return;const u=[],B=[],P=[];if(b.forEach(f=>{const V=n[f];V&&V.forEach($=>{u.push($),B.push(f),P.push($)})}),u.length>0&&W.push({y:t.horizontal?B:u,x:t.horizontal?u:B,type:"box",name:h,marker:{color:T[s%T.length]},boxpoints:t.showOutliers?"outliers":!1,boxmean:t.showMean?"sd":!1,notched:t.showNotches,orientation:t.horizontal?"h":"v"}),P.length>0){const f=[...P].sort((Pe,De)=>Pe-De),[V,$,fe]=se(f);H.push({category:`${h} (overall)`,min:Math.min(...f),max:Math.max(...f),q1:V,median:$,q3:fe,mean:ce(f),iqr:fe-V,sampleSize:f.length})}})}else if(m&&!l){C("Stratification requires a grouping variable to be selected."),ee(!1);return}else throw new Error("Invalid combination of variables selected.");const O={title:{text:t.title},xaxis:{title:{text:t.horizontal?t.yAxisLabel:t.xAxisLabel},automargin:!0},yaxis:{title:{text:t.horizontal?t.xAxisLabel:t.yAxisLabel},automargin:!0},boxmode:l||m?"group":void 0,autosize:!0,paper_bgcolor:i.palette.mode==="dark"?i.palette.background.paper:"#fff",plot_bgcolor:i.palette.mode==="dark"?i.palette.background.default:"#fff",font:{color:i.palette.text.primary},legend:{bgcolor:i.palette.mode==="dark"?"rgba(0,0,0,0.5)":"rgba(255,255,255,0.5)",bordercolor:i.palette.divider,borderwidth:1}};t.horizontal?(l||m)&&(O.yaxis={...O.yaxis,type:"category"}):(l||m)&&(O.xaxis={...O.xaxis,type:"category"}),E({data:W,layout:O}),L(H)}catch(a){C(`Error generating box plot: ${a instanceof Error?a.message:String(a)}`),E(null),L([])}finally{ee(!1)}},Ve=()=>{ge(ze)},Me=async()=>{if(z)try{const a=await Ae(()=>import("./charts-plotly-BhN4fPIu.js").then(m=>m.P),__vite__mapDeps([0,1,2,3,4]),import.meta.url),l={format:"svg",filename:t.title.replace(/\s+/g,"_")||"boxplot",width:600,height:400};await a.downloadImage(ue,l)}catch{C("Failed to download chart. Please try again.")}else C("Chart data not available for download.")};return e.jsxs(d,{p:3,children:[e.jsx(x,{variant:"h5",gutterBottom:!0,children:"Box Plot Generator"}),e.jsx(d,{mb:2,p:2,sx:{backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)",borderRadius:1},children:e.jsxs(x,{variant:"body2",color:"text.secondary",children:["Generate box plots to visualize the distribution and identify outliers in numerical data. Use keyboard shortcuts: ",e.jsx("strong",{children:"Alt+1"})," for Variables panel, ",e.jsx("strong",{children:"Alt+2"})," for Settings panel."]})}),e.jsxs(ie,{container:!0,spacing:2,children:[e.jsxs(ie,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(N,{elevation:1,sx:{mb:1,backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(Le,{value:y,onChange:(a,l)=>Z(l),variant:"fullWidth",sx:{minHeight:48,"& .MuiTab-root":{minHeight:48,textTransform:"none",fontSize:"0.875rem",fontWeight:500,color:i.palette.text.secondary,"&.Mui-selected":{color:i.palette.primary.main},"&:hover":{color:i.palette.primary.main,backgroundColor:i.palette.action.hover}},"& .MuiTabs-indicator":{backgroundColor:i.palette.primary.main}},children:[e.jsx(Y,{title:"Variable Selection Panel",placement:"top",children:e.jsx(pe,{value:"variables",label:"Variables",icon:e.jsx(Te,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(Y,{title:"Chart Settings Panel",placement:"top",children:e.jsx(pe,{value:"settings",label:"Settings",icon:e.jsx(Oe,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(je,{in:y==="variables",timeout:300,children:e.jsx(d,{sx:{display:y==="variables"?"block":"none"},children:e.jsxs(N,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(x,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Data Selection"}),e.jsxs(R,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"dataset-select-label",children:"Dataset"}),e.jsx(F,{labelId:"dataset-select-label",value:M,label:"Dataset",onChange:Ie,disabled:D.length===0,children:D.length===0?e.jsx(j,{value:"",disabled:!0,children:"No datasets available"}):D.map(a=>e.jsxs(j,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]}),e.jsxs(R,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"value-variable-label",children:"Numerical Variable"}),e.jsx(F,{labelId:"value-variable-label",value:k,label:"Numerical Variable",onChange:a=>U(a.target.value),disabled:te.length===0,children:te.length===0?e.jsx(j,{value:"",disabled:!0,children:"No numeric variables"}):te.map(a=>e.jsx(j,{value:a.id,children:a.name},a.id))})]}),e.jsxs(R,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"category-variable-label",children:"Grouping Variable (Optional)"}),e.jsxs(F,{labelId:"category-variable-label",value:v,label:"Grouping Variable (Optional)",onChange:a=>X(a.target.value),disabled:_.length===0,children:[e.jsx(j,{value:"",children:e.jsx("em",{children:"None"})}),_.map(a=>e.jsx(j,{value:a.id,children:a.name},a.id))]})]}),e.jsxs(R,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"stratification-variable-label",children:"Stratification Variable (Optional)"}),e.jsxs(F,{labelId:"stratification-variable-label",value:A,label:"Stratification Variable (Optional)",onChange:a=>J(a.target.value),disabled:_.length===0||!v,children:[e.jsx(j,{value:"",children:e.jsx("em",{children:"None"})}),_.filter(a=>a.id!==v).map(a=>e.jsx(j,{value:a.id,children:a.name},a.id))]}),!v&&A&&e.jsx(x,{variant:"caption",color:"error",children:"Grouping variable must be selected to use stratification."})]}),e.jsxs(d,{sx:{mt:2},children:[e.jsx(x,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(Be,{children:[e.jsx(K,{control:e.jsx(Q,{size:"small",checked:t.showOutliers,onChange:a=>S("showOutliers",a.target.checked)}),label:"Show Outliers"}),e.jsx(K,{control:e.jsx(Q,{size:"small",checked:t.showMean,onChange:a=>S("showMean",a.target.checked)}),label:"Show Mean (+SD)"}),e.jsx(K,{control:e.jsx(Q,{size:"small",checked:t.horizontal,onChange:a=>S("horizontal",a.target.checked)}),label:"Horizontal Orientation"}),e.jsx(K,{control:e.jsx(Q,{size:"small",checked:t.showNotches,onChange:a=>S("showNotches",a.target.checked)}),label:"Show Notches"})]})]}),e.jsxs(d,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(ve,{variant:"contained",onClick:be,disabled:!k||I,startIcon:I?e.jsx(ye,{size:20}):e.jsx(Ce,{}),fullWidth:!0,children:I?"Generating...":"Generate Chart"}),e.jsxs(d,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(Y,{title:"Download Chart",children:e.jsx(Se,{onClick:Me,disabled:!z||I,children:e.jsx(Ne,{})})}),e.jsx(Y,{title:"Reset Settings",children:e.jsx(Se,{onClick:Ve,children:e.jsx(Re,{})})})]})]})]})})}),e.jsx(je,{in:y==="settings",timeout:300,children:e.jsx(d,{sx:{display:y==="settings"?"block":"none"},children:e.jsxs(N,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(x,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Chart Settings"}),e.jsxs(d,{mb:3,children:[e.jsx(x,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Labels & Title"}),e.jsx(re,{fullWidth:!0,label:"Chart Title",value:t.title,onChange:a=>S("title",a.target.value),margin:"normal",variant:"outlined",size:"small"}),e.jsx(re,{fullWidth:!0,label:"X-Axis Label",value:t.xAxisLabel,onChange:a=>S("xAxisLabel",a.target.value),margin:"normal",variant:"outlined",size:"small"}),e.jsx(re,{fullWidth:!0,label:"Y-Axis Label",value:t.yAxisLabel,onChange:a=>S("yAxisLabel",a.target.value),margin:"normal",variant:"outlined",size:"small"})]}),e.jsxs(d,{mb:3,children:[e.jsx(x,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(R,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(q,{id:"color-scheme-label",children:"Color Scheme"}),e.jsx(F,{labelId:"color-scheme-label",value:t.colorScheme,label:"Color Scheme",onChange:a=>S("colorScheme",a.target.value),children:Object.keys(he).map(a=>e.jsx(j,{value:a,children:a.charAt(0).toUpperCase()+a.slice(1)},a))})]})]}),e.jsx(ve,{variant:"outlined",color:"secondary",onClick:be,disabled:!k||I||!M,fullWidth:!0,sx:{mt:2},children:"Apply Customizations & Regenerate"})]})})})]}),e.jsx(ie,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(N,{elevation:2,sx:{p:2},children:[e.jsxs(d,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(x,{variant:"h6",children:"Chart Preview"}),e.jsxs(d,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(x,{variant:"body2",color:"text.secondary",children:["Active: ",y==="variables"?"Variables":"Settings"]}),e.jsx(d,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:y==="variables"?i.palette.primary.main:i.palette.warning.main,boxShadow:`0 0 0 2px ${y==="variables"?i.palette.primary.main+"20":i.palette.warning.main+"20"}`}})]})]}),ae&&e.jsx(d,{sx:{mb:2,p:2,backgroundColor:i.palette.error.light+"20",borderRadius:1,border:`1px solid ${i.palette.error.light}`},children:e.jsx(x,{color:"error",children:ae})}),e.jsx(d,{sx:{minHeight:500,display:"flex",justifyContent:"center",alignItems:"center",border:`1px solid ${i.palette.divider}`,borderRadius:1,backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)"},children:I?e.jsxs(d,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(ye,{}),e.jsx(x,{color:"text.secondary",children:"Generating box plot..."})]}):z?e.jsx("div",{ref:G,id:ue,style:{width:"100%",height:"500px"}}):e.jsxs(d,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,children:[e.jsx(Ce,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(x,{color:"text.secondary",textAlign:"center",children:o?k?"Chart will appear here once generated":"Select a numerical variable to generate the box plot":"Select a dataset to begin"}),o&&!k&&e.jsx(x,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"})]})}),me.length>0&&!I&&!ae&&e.jsxs(d,{mt:3,children:[e.jsx(x,{variant:"subtitle2",gutterBottom:!0,children:"Summary Statistics"}),e.jsx(qe,{component:N,variant:"outlined",children:e.jsxs(Fe,{size:"small",children:[e.jsx(Ge,{children:e.jsxs(we,{children:[e.jsx(c,{children:"Category"}),e.jsx(c,{align:"right",children:"Min"}),e.jsx(c,{align:"right",children:"Q1"}),e.jsx(c,{align:"right",children:"Median"}),e.jsx(c,{align:"right",children:"Q3"}),e.jsx(c,{align:"right",children:"Max"}),e.jsx(c,{align:"right",children:"Mean"}),e.jsx(c,{align:"right",children:"IQR"}),e.jsx(c,{align:"right",children:"N"})]})}),e.jsx(_e,{children:me.map((a,l)=>e.jsxs(we,{children:[e.jsx(c,{component:"th",scope:"row",children:a.category}),e.jsx(c,{align:"right",children:a.min.toFixed(2)}),e.jsx(c,{align:"right",children:a.q1.toFixed(2)}),e.jsx(c,{align:"right",children:a.median.toFixed(2)}),e.jsx(c,{align:"right",children:a.q3.toFixed(2)}),e.jsx(c,{align:"right",children:a.max.toFixed(2)}),e.jsx(c,{align:"right",children:a.mean.toFixed(2)}),e.jsx(c,{align:"right",children:a.iqr.toFixed(2)}),e.jsx(c,{align:"right",children:a.sampleSize})]},`${a.category}-${l}`))})]})})]})]})})]})]})};export{la as default};
