import{u as he,j as e,B as r,bL as fe,bM as H,G as n,R,e as s,bN as M,aE as w,I as T,bs as X,ah as b,f as Y,bH as Z,bO as J,bP as Q,k as U,i as ee,l as te}from"./mui-libs-CfwFIaTD.js";import{r as l}from"./react-libs-Cr2nE3UY.js";import{c as je,a as be,g as ae}from"./sampleSize-nX7GCAZC.js";import{R as se,d as re,C as ne,X as le,Y as oe,T as ie,e as ce}from"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const Ce=()=>{const o=he(),[p,ue]=l.useState("oneWayAnova"),[m,B]=l.useState(.95),[i,k]=l.useState(.8),[v,N]=l.useState(3),[C,I]=l.useState(5),[g,P]=l.useState(10),[y,F]=l.useState(4),[h,D]=l.useState(.5),[A,G]=l.useState(2),[S,W]=l.useState(6),[d,de]=l.useState(null),[z,$]=l.useState([]);l.useEffect(()=>{O(),pe()},[m,i,v,C,g,y,h,A,S,p]);const O=()=>{let t;const a=1-m,f=i;switch(p){case"oneWayAnova":t=be(v,C,g,a,f);break;case"repeatedMeasuresAnova":t=je(y,A,S,h,a,f);break;default:t=0}de(t)},pe=()=>{const t=[],xe=1-m,L=ae(1-xe/2),V=ae(i);if(p==="oneWayAnova"){const j=g,x=v;if(j===0||x<2){$([]);return}for(let c=5;c<=150;c+=5){if(c<=0)continue;let u;c>0&&j>0&&x>0?u=(L+V)*j*Math.sqrt(x/c):u=1/0,t.push({sampleSize:c,detectableDifference:u})}}else if(p==="repeatedMeasuresAnova"){const j=S,x=h,c=y;if(j===0||c<=1||x<-1||x>1){$([]);return}for(let u=5;u<=150;u+=5){if(u<=0)continue;let E;u>0&&j>0&&c>0&&1-x>0?E=Math.sqrt(Math.pow(L+V,2)*Math.pow(j,2)*(1-x)/(u*c)):E=1/0,t.push({sampleSize:u,detectableDifference:E})}}$(t)},me=(t,a)=>{a!==null&&(ue(a),a==="oneWayAnova"?(N(3),I(5),P(10)):a==="repeatedMeasuresAnova"&&(F(4),G(2),W(6),D(.5)))},K=()=>{if(d){let t="";const a=`with ${Math.round(i*100)}% power at a ${Math.round(m*100)}% confidence level.`;switch(p){case"oneWayAnova":t=`Required Sample Size: ${d} per group for a One-way ANOVA with ${v} groups, minimum detectable effect size of ${C}, and common standard deviation of ${g}, ${a}`;break;case"repeatedMeasuresAnova":t=`Required Sample Size: ${d} participants for a Repeated Measures ANOVA with ${y} measurements, effect size of ${A}, standard deviation of ${S}, and correlation between measures of ${h}, ${a}`;break;default:t="No results to copy."}navigator.clipboard.writeText(t)}},_=()=>{console.log("Export PDF functionality would go here")},q=()=>{B(.95),k(.8),N(3),I(5),P(10),F(4),G(2),W(6),D(.5),setTimeout(()=>O(),0)};return e.jsxs(r,{sx:{p:3},children:[e.jsxs(fe,{value:p,exclusive:!0,onChange:me,"aria-label":"More than 2 Groups Calculator Type",fullWidth:!0,sx:{mb:3},children:[e.jsx(H,{value:"oneWayAnova","aria-label":"One-Way ANOVA",sx:{"&.Mui-selected":{bgcolor:o.palette.primary.main,color:o.palette.primary.contrastText,"&:hover":{bgcolor:o.palette.primary.dark}}},children:"One-Way ANOVA"}),e.jsx(H,{value:"repeatedMeasuresAnova","aria-label":"Repeated Measures ANOVA",sx:{"&.Mui-selected":{bgcolor:o.palette.secondary.main,color:o.palette.secondary.contrastText,"&:hover":{bgcolor:o.palette.secondary.dark}}},children:"Repeated Measures ANOVA"})]}),p==="oneWayAnova"&&e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(R,{elevation:1,sx:{p:3},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"One-Way ANOVA Parameters"}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Confidence Level"}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(M,{value:m*100,onChange:(t,a)=>B(a/100),step:1,min:80,max:99,marks:[{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"},{value:99,label:"99%"}]})})})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsxs(s,{gutterBottom:!0,children:["Statistical Power (1-β)",e.jsx(w,{title:"The probability of correctly detecting a true difference between groups.",children:e.jsx(T,{size:"small",sx:{ml:1},children:e.jsx(X,{fontSize:"small"})})})]}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(M,{value:i*100,onChange:(t,a)=>k(a/100),step:5,min:70,max:95,marks:[{value:70,label:"70%"},{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"}]})})})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Number of Groups (k)"}),e.jsx(b,{type:"number",value:v,onChange:t=>N(Math.max(2,parseInt(t.target.value,10)||2)),fullWidth:!0,inputProps:{min:2}})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Smallest Difference Between Means (Δ)"}),e.jsx(b,{type:"number",value:C,onChange:t=>I(parseFloat(t.target.value)||0),fullWidth:!0})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Common Standard Deviation (σ)"}),e.jsx(b,{type:"number",value:g,onChange:t=>P(parseFloat(t.target.value)||0),fullWidth:!0})]}),e.jsx(r,{sx:{mt:2,display:"flex",gap:1},children:e.jsx(Y,{variant:"outlined",onClick:q,startIcon:e.jsx(Z,{}),children:"Reset"})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(R,{elevation:1,sx:{p:3},children:[e.jsxs(r,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(s,{variant:"h6",children:"Results"}),e.jsxs(r,{children:[e.jsx(w,{title:"Copy results",children:e.jsx(T,{onClick:K,children:e.jsx(J,{})})}),e.jsx(w,{title:"Export as PDF",children:e.jsx(T,{onClick:_,children:e.jsx(Q,{})})})]})]}),d!==null&&e.jsxs(e.Fragment,{children:[e.jsx(U,{sx:{mb:4,bgcolor:ee(o.palette.primary.main,.1)},children:e.jsxs(te,{sx:{textAlign:"center"},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Required Sample Size"}),e.jsx(s,{variant:"h2",color:"primary",children:d}),e.jsx(s,{variant:"body2",color:"text.secondary",children:"participants"})]})}),e.jsxs(r,{sx:{height:300,mt:2},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,children:["Smallest Detectable Difference (Δ) by Sample Size (at ",Math.round(i*100),"% Power)"]}),z.length>0?e.jsx(se,{width:"100%",height:"100%",children:e.jsxs(re,{data:z,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(ne,{strokeDasharray:"3 3"}),e.jsx(le,{dataKey:"sampleSize",label:{value:"Sample Size (per group)",position:"outerBottom",offset:15,style:{fontSize:"12px"}},height:70}),e.jsx(oe,{dataKey:"detectableDifference",type:"number",domain:["auto","auto"],name:"Smallest Detectable Difference (Δ)",label:{value:"Smallest Detectable Difference (Δ)",angle:-90,position:"outside",offset:-60,style:{fontSize:"12px"}},tickFormatter:t=>t===1/0?"∞":t.toFixed(1),padding:{top:30},width:100}),e.jsx(ie,{formatter:(t,a,f)=>f.dataKey==="detectableDifference"?[t===1/0?"∞":t.toFixed(2),"Smallest Detectable Difference (Δ)"]:[t,a],labelFormatter:t=>`Sample Size per Group: ${t}`}),e.jsx(ce,{type:"monotone",dataKey:"detectableDifference",stroke:o.palette.primary.main,activeDot:{r:8}})]})}):e.jsx(s,{sx:{textAlign:"center",color:o.palette.text.secondary,mt:2},children:"Detectable difference curve data will appear here after calculation."})]}),e.jsxs(s,{variant:"body2",sx:{mt:2,fontStyle:"italic"},children:["Interpretation: To detect a minimum difference of ",C," between ",v," group means with a common standard deviation of ",g,", using a significance level of ",((1-m)*100).toFixed(0),"% and achieving ",i*100,"% power, you need approximately ",d," participants per group."]})]})]})})]}),p==="repeatedMeasuresAnova"&&e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(R,{elevation:1,sx:{p:3},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Repeated Measures ANOVA Parameters"}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Confidence Level"}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(M,{value:m*100,onChange:(t,a)=>B(a/100),step:1,min:80,max:99,marks:[{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"},{value:99,label:"99%"}]})})})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsxs(s,{gutterBottom:!0,children:["Statistical Power (1-β)",e.jsx(w,{title:"The probability of correctly detecting a true effect across repeated measures.",children:e.jsx(T,{size:"small",sx:{ml:1},children:e.jsx(X,{fontSize:"small"})})})]}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(M,{value:i*100,onChange:(t,a)=>k(a/100),step:5,min:70,max:95,marks:[{value:70,label:"70%"},{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"}]})})})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Number of Repeated Measurements (r)"}),e.jsx(b,{type:"number",value:y,onChange:t=>F(Math.max(2,parseInt(t.target.value,10)||2)),fullWidth:!0,inputProps:{min:2}})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Effect Size Across Time/Conditions (Δ)"}),e.jsx(b,{type:"number",value:A,onChange:t=>G(parseFloat(t.target.value)||0),fullWidth:!0})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Standard Deviation (σ)"}),e.jsx(b,{type:"number",value:S,onChange:t=>W(parseFloat(t.target.value)||0),fullWidth:!0})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(s,{gutterBottom:!0,children:"Correlation Between Repeated Measures (ρ)"}),e.jsx(M,{value:h,onChange:(t,a)=>D(a),step:.01,min:0,max:.99,marks:[{value:0,label:"0.0"},{value:.25,label:"0.25"},{value:.5,label:"0.5"},{value:.75,label:"0.75"},{value:.99,label:"0.99"}],valueLabelDisplay:"auto"}),e.jsx(b,{type:"number",value:h,onChange:t=>{const a=parseFloat(t.target.value);!isNaN(a)&&a>=0&&a<=.99&&D(a)},fullWidth:!0,inputProps:{step:.01,min:0,max:.99},sx:{mt:1}})]}),e.jsx(r,{sx:{mt:2,display:"flex",gap:1},children:e.jsx(Y,{variant:"outlined",onClick:q,startIcon:e.jsx(Z,{}),children:"Reset"})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(R,{elevation:1,sx:{p:3},children:[e.jsxs(r,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(s,{variant:"h6",children:"Results"}),e.jsxs(r,{children:[e.jsx(w,{title:"Copy results",children:e.jsx(T,{onClick:K,children:e.jsx(J,{})})}),e.jsx(w,{title:"Export as PDF",children:e.jsx(T,{onClick:_,children:e.jsx(Q,{})})})]})]}),d!==null&&e.jsxs(e.Fragment,{children:[e.jsx(U,{sx:{mb:4,bgcolor:ee(o.palette.primary.main,.1)},children:e.jsxs(te,{sx:{textAlign:"center"},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Required Sample Size"}),e.jsx(s,{variant:"h2",color:"primary",children:d}),e.jsx(s,{variant:"body2",color:"text.secondary",children:"participants"})]})}),e.jsxs(r,{sx:{height:300,mt:2},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,children:["Smallest Detectable Effect Size (Δ) by Sample Size (at ",Math.round(i*100),"% Power)"]}),z.length>0?e.jsx(se,{width:"100%",height:"100%",children:e.jsxs(re,{data:z,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(ne,{strokeDasharray:"3 3"}),e.jsx(le,{dataKey:"sampleSize",label:{value:"Number of Participants",position:"outerBottom",offset:15,style:{fontSize:"12px"}},height:70}),e.jsx(oe,{dataKey:"detectableDifference",type:"number",domain:["auto","auto"],name:"Smallest Detectable Effect Size (Δ)",label:{value:"Smallest Detectable Effect Size (Δ)",angle:-90,position:"outside",offset:-60,style:{fontSize:"12px"}},tickFormatter:t=>t===1/0?"∞":t.toFixed(2),padding:{top:30},width:100}),e.jsx(ie,{formatter:(t,a,f)=>f.dataKey==="detectableDifference"?[t===1/0?"∞":t.toFixed(3),"Smallest Detectable Effect Size (Δ)"]:[t,a],labelFormatter:t=>`Number of Participants: ${t}`}),e.jsx(ce,{type:"monotone",dataKey:"detectableDifference",stroke:o.palette.primary.main,activeDot:{r:8}})]})}):e.jsx(s,{sx:{textAlign:"center",color:o.palette.text.secondary,mt:2},children:"Detectable effect size curve data will appear here after calculation."})]}),e.jsxs(s,{variant:"body2",sx:{mt:2,fontStyle:"italic"},children:["Interpretation: To detect an effect size of ",A," across ",y," repeated measurements, with a standard deviation of ",S," and a correlation of ",h," between measures, using a significance level of ",((1-m)*100).toFixed(0),"% and achieving ",i*100,"% power, you need approximately ",d," participants."]})]})]})})]})]})};export{Ce as default};
