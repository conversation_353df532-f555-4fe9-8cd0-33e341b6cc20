import{u as Oe,j as e,B as d,e as c,bW as Fe,bX as Pe,bs as Ge,aD as Ue,bY as _e,G as I,R as A,a6 as Ye,aE as K,a7 as Se,bn as qe,aW as Xe,F as ke,ai as U,b9 as J,ba as _,bb as $,f as we,ac as re,I as ae,k as Ke,l as Je,h as Qe,ae as Ce,b7 as Ze,bH as et,ah as ze,bN as ne,c8 as tt,aj as Le,b2 as Te,g as rt}from"./mui-libs-CfwFIaTD.js";import{r as z}from"./react-libs-Cr2nE3UY.js";import{a as at,D as ie}from"./index-Bpan7Tbe.js";import{g as O,z as nt,y as it,A as st,D as lt,F as ot,G as ct}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./other-utils-CR9xr_gI.js";const $e={title:"Variable Tree Analysis",nodeSize:25,spacing:100,linkLength:150,colorScheme:"default",orientation:"horizontal",showStatistics:!0,showLabels:!0,linkStyle:"curved",nodeShape:"circle",rootLabel:"Study Size"},se={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f"],muted:["#6b7280","#9ca3af","#d1d5db","#374151","#4b5563","#6b7280","#9ca3af","#d1d5db"],categorical:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5"],viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b"],pastel:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec"],bright:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf"],earth:["#8b4513","#daa520","#228b22","#4682b4","#cd853f","#d2691e","#556b2f","#8fbc8f"],ocean:["#006994","#0085c3","#00a9cc","#7dd3c0","#42b883","#409eff","#20a0ff","#13ce66"],sunset:["#ff6b6b","#ffa726","#ffcc02","#66bb6a","#42a5f5","#ab47bc","#ec407a","#ff7043"]},mt=()=>{const{datasets:le,currentDataset:Y}=at(),u=Oe(),D=z.useRef(null),[F,oe]=z.useState(""),[V,Q]=z.useState(0),[h,B]=z.useState([]),[a,ce]=z.useState($e),[q,N]=z.useState(null),[P,de]=z.useState(!1),[he,p]=z.useState(null),f=F?le.find(t=>t.id===F):Y,ue=(f==null?void 0:f.columns.filter(t=>t.type===ie.NUMERIC||t.type===ie.CATEGORICAL))||[];z.useEffect(()=>{Y&&!F&&oe(Y.id)},[Y,F]),z.useEffect(()=>{const t=r=>{r.altKey&&!r.ctrlKey&&!r.shiftKey&&(r.key==="1"?(r.preventDefault(),Q(0)):r.key==="2"&&(r.preventDefault(),Q(1)))};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)},[]),z.useEffect(()=>{if(h.length>=2&&f&&!P){const t=setTimeout(()=>{fe()},500);return()=>clearTimeout(t)}},[h,a,f]);const De=t=>{const r=t.target.value;oe(r),B([]),N(null),p(null)},k=(t,r)=>{ce(l=>({...l,[t]:r}))},Z=t=>{if(h.length>=4){p("Maximum of 4 variables allowed in hierarchy");return}if(h.some(i=>i.variable===t)){p("Variable already in hierarchy");return}const r=f==null?void 0:f.columns.find(i=>i.id===t);if(!r){p("Variable not found in dataset");return}const l=h.length,n=l===0?"root":l===h.length-1?"leaf":"branch";B(i=>[...i,{level:l,variable:t,variableName:r.name||t,role:n}]),p(null)},Ve=t=>{B(r=>{const l=r.filter(n=>n.variable!==t);return l.map((n,i)=>({...n,level:i,role:i===0?"root":i===l.length-1?"leaf":"branch"}))}),N(null),p(null)},Ie=(t,r)=>{t!==r&&(B(l=>{const n=[...l],[i]=n.splice(t,1);return n.splice(r,0,i),n.map((o,g)=>({...o,level:g,role:g===0?"root":g===n.length-1?"leaf":"branch"}))}),N(null),p(null))},Ae=()=>{B([]),N(null),p(null)},xe=(t,r,l=!1)=>{t.dataTransfer.setData("text/plain",r),t.dataTransfer.setData("application/json",JSON.stringify({variable:r,fromHierarchy:l})),t.dataTransfer.effectAllowed="move"},ge=t=>{t.preventDefault(),t.dataTransfer.dropEffect="move"},be=(t,r)=>{t.preventDefault();try{const l=JSON.parse(t.dataTransfer.getData("application/json")),{variable:n,fromHierarchy:i}=l;if(i){const o=h.findIndex(g=>g.variable===n);o!==-1&&r!==void 0&&Ie(o,r)}else if(r!==void 0){if(h.length>=4){p("Maximum of 4 variables allowed in hierarchy");return}if(h.some(g=>g.variable===n)){p("Variable already in hierarchy");return}const o=f==null?void 0:f.columns.find(g=>g.id===n);if(!o){p("Variable not found in dataset");return}B(g=>{const m=[...g];return m.splice(r,0,{level:r,variable:n,variableName:o.name||n,role:"branch"}),m.map((H,E)=>({...H,level:E,role:E===0?"root":E===m.length-1?"leaf":"branch"}))}),p(null)}else Z(n)}catch{const n=t.dataTransfer.getData("text/plain");n&&Z(n)}},fe=async()=>{if(!f||h.length<1){p("Please select at least 1 variable for the hierarchy");return}if(h.length<2){p("Please select at least 2 variables to create a meaningful tree hierarchy");return}const t=h.filter(r=>!f.columns.some(l=>l.id===r.variable));if(t.length>0){p(`Variables not found in dataset: ${t.map(r=>r.variable).join(", ")}`);return}if(f.data.length===0){p("Dataset contains no data rows");return}de(!0),p(null);try{const r=await Ne();if(!r){p("Unable to create tree structure. Please check your data and variable selection."),N(null);return}(h.length===1||!r.children||r.children.length===0)&&console.log("Tree has no children, but showing root node"),N(r),setTimeout(()=>{Be(r)},100)}catch(r){console.error("Tree generation error:",r),p(`Error generating tree: ${r instanceof Error?r.message:String(r)}`),N(null)}finally{de(!1)}},Ne=async()=>{if(!f||h.length===0)throw new Error("No dataset or hierarchy available");console.log("Building tree with hierarchy:",h),console.log("Dataset:",f.name,"Rows:",f.data.length);const t=f.data,r=f.columns,l=(n,i={})=>{console.log(`Building node at level ${n} with filters:`,i);let o;if(n===0)o={...h[0],variableName:`${h[0].variableName||h[0].variable} (Study)`};else{const y=n-1;if(y>=h.length)throw new Error(`Hierarchy index ${y} exceeds hierarchy length ${h.length}`);o=h[y]}const g=r.find(y=>y.id===o.variable);if(!g)throw console.error(`Column ${o.variable} not found in columns:`,r.map(y=>y.id)),new Error(`Column ${o.variable} not found`);let m;Object.keys(i).length===0?m=t:m=t.filter(y=>Object.entries(i).every(([j,b])=>{const S=r.find(T=>T.id===j);if(!S)return console.warn(`Filter column ${j} not found`),!1;const R=y[S.name];if(R==null||b==null)return!1;if(typeof b=="string"&&(b.includes("≤")||b.includes(" - ")||b.includes(">"))){const T=parseFloat(String(R));if(isNaN(T))return!1;if(b.startsWith("≤")){const L=parseFloat(b.substring(2));return!isNaN(L)&&T<=L}else if(b.includes(" - ")){const[L,C]=b.split(" - ").map(W=>parseFloat(W.trim()));return!isNaN(L)&&!isNaN(C)&&T>=L&&T<=C}else if(b.startsWith(">")){const L=parseFloat(b.substring(2));return!isNaN(L)&&T>L}}return String(R).toLowerCase().trim()===String(b).toLowerCase().trim()})),m.length===0&&n>0&&console.warn(`No data found for level ${n} with filters:`,i);const H=He(m,g),E=Re(n,i,h),G={id:`${o.variable}_${n}_${Object.values(i).join("_")}`,name:E,variable:o.variable,level:n,statistics:H,children:[]};if(n===0){const y=h[0],w=r.find(j=>j.id===y.variable);if(w){const j=pe(m,w);j.length>0&&(G.children=j.map(b=>{const S={[y.variable]:b};return l(n+1,S)}).filter(b=>b.statistics&&b.statistics.count&&b.statistics.count>0))}}else{const y=n;if(y<h.length){const w=h[y],j=r.find(b=>b.id===w.variable);if(j){const b=pe(m,j);b.length>0&&(G.children=b.map(S=>{const R={...i,[w.variable]:S};return l(n+1,R)}).filter(S=>S.statistics&&S.statistics.count&&S.statistics.count>0))}}}return G};return l(0)},He=(t,r)=>{const l=t.map(n=>n[r.name]).filter(n=>n!=null);if(r.type==="numerical"){const n=l.map(i=>parseFloat(i)).filter(i=>!isNaN(i));return{count:n.length,mean:n.length>0?n.reduce((i,o)=>i+o,0)/n.length:0,median:n.length>0?Ee(n):0}}else{const n={};return l.forEach(i=>{const o=String(i);n[o]=(n[o]||0)+1}),{count:l.length,categories:n}}},pe=(t,r)=>{const l=t.map(i=>i[r.name]).filter(i=>i!=null&&i!==""),n=[...new Set(l)];if(n.length>8)if(r.type==="numerical"){const i=n.map(o=>parseFloat(o)).filter(o=>!isNaN(o)).sort((o,g)=>o-g);if(i.length>8){const o=ee(i,.25),g=ee(i,.5),m=ee(i,.75);return[`≤ ${o.toFixed(2)}`,`${o.toFixed(2)} - ${g.toFixed(2)}`,`${g.toFixed(2)} - ${m.toFixed(2)}`,`> ${m.toFixed(2)}`]}}else{const i=l.reduce((o,g)=>{const m=String(g);return o[m]=(o[m]||0)+1,o},{});return Object.entries(i).sort(([,o],[,g])=>g-o).slice(0,8).map(([o])=>o)}return n.sort()},ee=(t,r)=>{const l=r*(t.length-1),n=Math.floor(l),i=Math.ceil(l),o=l%1;return i>=t.length?t[t.length-1]:t[n]*(1-o)+t[i]*o},Ee=t=>{const r=[...t].sort((n,i)=>n-i),l=Math.floor(r.length/2);return r.length%2===0?(r[l-1]+r[l])/2:r[l]},me=t=>!t.children||t.children.length===0?1:1+Math.max(...t.children.map(r=>me(r))),ye=t=>!t.children||t.children.length===0?1:t.children.reduce((r,l)=>r+ye(l),0),Re=(t,r,l,n)=>{if(t===0){if(a.rootLabel&&a.rootLabel.trim()!=="")return a.rootLabel;{const o=l[0];return o.variableName||o.variable}}const i=t-1;if(i>=0&&i<l.length){const o=l[i],g=r[o.variable];return g!==void 0?String(g):"Unknown"}return"Unknown"},Be=t=>{if(!D.current||!D.current.parentElement)return;O(D.current).selectAll("*").remove();const r=O(D.current),l=D.current.parentElement.getBoundingClientRect(),n=l.width,i=l.height;me(t),ye(t);const o=a.spacing*.6,g=a.linkLength;r.attr("width",n).attr("height",i);const m=nt().scaleExtent([.1,3]).on("zoom",s=>{H.attr("transform",s.transform)});r.call(m);const H=r.append("g").attr("class","tree-content"),E=it().nodeSize(a.orientation==="vertical"?[o,g]:[g,o]).separation((s,x)=>s.parent===x.parent?.8:1),G=st(t),y=E(G);let w=1/0,j=-1/0,b=1/0,S=-1/0;y.descendants().forEach(s=>{s.x<w&&(w=s.x),s.x>j&&(j=s.x),s.y<b&&(b=s.y),s.y>S&&(S=s.y)});const R=(n-(j-w))/2-w,T=(i-(S-b))/2-b,L=lt.translate(R,T);r.call(m.transform,L);const C=se[a.colorScheme]||se.muted,W=new Map;h.forEach((s,x)=>{W.set(s.variable,C[x%C.length])});const ve=a.linkStyle==="curved"?a.orientation==="vertical"?ot().x(s=>s.x).y(s=>s.y):ct().x(s=>s.y).y(s=>s.x):null;H.selectAll(".link").data(y.links()).enter().append("path").attr("class","link").attr("d",s=>{if(a.linkStyle==="curved"&&ve)return ve(s);if(a.linkStyle==="straight"){const x=a.orientation==="vertical"?[s.source.x,s.source.y]:[s.source.y,s.source.x],v=a.orientation==="vertical"?[s.target.x,s.target.y]:[s.target.y,s.target.x];return`M${x[0]},${x[1]}L${v[0]},${v[1]}`}else{const x=a.orientation==="vertical"?[s.source.x,s.source.y]:[s.source.y,s.source.x],v=a.orientation==="vertical"?[s.target.x,s.target.y]:[s.target.y,s.target.x],M=a.orientation==="vertical"?(x[1]+v[1])/2:(x[0]+v[0])/2;return a.orientation==="vertical"?`M${x[0]},${x[1]}V${M}H${v[0]}V${v[1]}`:`M${x[0]},${x[1]}H${M}V${v[1]}H${v[0]}`}}).style("fill","none").style("stroke",u.palette.divider).style("stroke-width",2);const X=H.selectAll(".node").data(y.descendants()).enter().append("g").attr("class","node").attr("transform",s=>{const x=a.orientation==="vertical"?s.x:s.y,v=a.orientation==="vertical"?s.y:s.x;return`translate(${x},${v})`});if(a.nodeShape==="circle"?X.append("circle").attr("r",a.nodeSize/2).style("fill",s=>W.get(s.data.variable)||C[s.depth%C.length]).style("stroke",u.palette.background.paper).style("stroke-width",3).style("cursor","pointer").on("mouseover",function(){O(this).transition().duration(200).attr("r",a.nodeSize/2+5).style("stroke-width",4)}).on("mouseout",function(){O(this).transition().duration(200).attr("r",a.nodeSize/2).style("stroke-width",3)}):X.append("rect").attr("width",a.nodeSize).attr("height",a.nodeSize).attr("x",-a.nodeSize/2).attr("y",-a.nodeSize/2).attr("rx",4).style("fill",s=>W.get(s.data.variable)||C[s.depth%C.length]).style("stroke",u.palette.background.paper).style("stroke-width",3).style("cursor","pointer").on("mouseover",function(){O(this).transition().duration(200).attr("width",a.nodeSize+10).attr("height",a.nodeSize+10).attr("x",-(a.nodeSize+10)/2).attr("y",-(a.nodeSize+10)/2).style("stroke-width",4)}).on("mouseout",function(){O(this).transition().duration(200).attr("width",a.nodeSize).attr("height",a.nodeSize).attr("x",-a.nodeSize/2).attr("y",-a.nodeSize/2).style("stroke-width",3)}),r.selectAll(".node.dragging circle").style("stroke",u.palette.primary.main).style("stroke-width",4).style("filter","drop-shadow(2px 2px 4px rgba(0,0,0,0.3))"),a.showLabels&&X.append("text").attr("dy",a.orientation==="vertical"?a.nodeSize/2+15:4).attr("dx",a.orientation==="vertical"?0:a.nodeSize/2+10).attr("text-anchor",a.orientation==="vertical"?"middle":"start").style("font-size","11px").style("font-weight","bold").style("fill",u.palette.text.primary).text(s=>s.data.name),a.showStatistics&&X.append("text").attr("dy",a.orientation==="vertical"?a.nodeSize/2+(a.showLabels?30:15):a.showLabels?16:4).attr("dx",a.orientation==="vertical"?0:a.nodeSize/2+10).attr("text-anchor",a.orientation==="vertical"?"middle":"start").style("font-size","9px").style("fill",u.palette.text.secondary).text(s=>{const x=s.data.statistics;return(x==null?void 0:x.mean)!==void 0?`μ=${x.mean.toFixed(1)}, n=${x.count}`:x!=null&&x.categories?`n=${Object.values(x.categories).reduce((M,te)=>M+te,0)}`:`n=${(x==null?void 0:x.count)||0}`}),r.append("text").attr("x",n/2).attr("y",25).attr("text-anchor","middle").style("font-size","16px").style("font-weight","bold").style("fill",u.palette.text.primary).text(a.title),h.length>0){const s=r.append("g").attr("class","legend").attr("transform","translate(20, 50)");h.forEach((x,v)=>{const M=x.variableName||x.variable,te=W.get(x.variable)||C[v%C.length],je=s.append("g").attr("transform",`translate(0, ${v*25})`);je.append("rect").attr("width",15).attr("height",15).attr("rx",2).style("fill",te).style("stroke",u.palette.background.paper).style("stroke-width",1),je.append("text").attr("x",20).attr("y",12).style("font-size","12px").style("fill",u.palette.text.primary).text(M)})}},We=()=>{if(!q||!D.current){p("No tree data available for download");return}try{const t=D.current,r=new XMLSerializer().serializeToString(t),l=new Blob([r],{type:"image/svg+xml;charset=utf-8"}),n=URL.createObjectURL(l),i=document.createElement("a");i.href=n,i.download=`${a.title.replace(/\s+/g,"_")}_tree.svg`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n)}catch(t){p(`Error downloading chart: ${t instanceof Error?t.message:String(t)}`)}},Me=()=>{ce($e)};return e.jsxs(d,{p:3,children:[e.jsx(c,{variant:"h5",gutterBottom:!0,children:"Variable Tree Analysis"}),e.jsxs(Fe,{sx:{mb:3},children:[e.jsx(Pe,{expandIcon:e.jsx(Ue,{}),children:e.jsxs(d,{display:"flex",alignItems:"center",gap:1,children:[e.jsx(Ge,{color:"primary"}),e.jsx(c,{variant:"subtitle1",children:"About Variable Tree Analysis"})]})}),e.jsxs(_e,{children:[e.jsx(c,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Variable Tree Analysis creates hierarchical visualizations showing relationships between 2-4 variables from your dataset. Build custom tree structures to explore how variables relate to each other at different levels."}),e.jsx(c,{variant:"body2",color:"text.secondary",paragraph:!0,children:e.jsx("strong",{children:"How to use:"})}),e.jsxs(c,{variant:"body2",color:"text.secondary",component:"div",sx:{ml:2},children:["• Select 2-4 variables from your dataset",e.jsx("br",{}),"• Drag variables to build hierarchy or click to add in order",e.jsx("br",{}),"• Root variable forms the base of the tree",e.jsx("br",{}),"• Each level splits data based on the variable values",e.jsx("br",{}),"• Customize appearance using the Settings panel"]}),e.jsxs(c,{variant:"body2",color:"text.secondary",paragraph:!0,sx:{mt:1},children:[e.jsx("strong",{children:"Keyboard Shortcuts:"})," Alt+1 (Variables Panel), Alt+2 (Settings Panel)"]}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Best practices:"})," Use categorical variables for clear splits, or numerical variables will be automatically binned."]})]})]}),e.jsxs(I,{container:!0,spacing:2,children:[e.jsxs(I,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(A,{elevation:1,sx:{mb:1,backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(Ye,{value:V,onChange:(t,r)=>Q(r),variant:"fullWidth",sx:{minHeight:44,"& .MuiTab-root":{minHeight:44,fontSize:"0.875rem",fontWeight:500,color:u.palette.text.secondary,textTransform:"none",transition:"all 0.2s ease-in-out","&.Mui-selected":{color:u.palette.primary.main,backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.08)":"rgba(25, 118, 210, 0.08)"},"&:hover":{backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.04)":"rgba(0, 0, 0, 0.04)"}},"& .MuiTabs-indicator":{height:3,borderRadius:"3px 3px 0 0"}},children:[e.jsx(K,{title:"Variable Selection Panel",placement:"top",children:e.jsx(Se,{label:"Variables",value:0,icon:e.jsx(qe,{}),iconPosition:"start"})}),e.jsx(K,{title:"Chart Settings Panel",placement:"top",children:e.jsx(Se,{label:"Settings",value:1,icon:e.jsx(Xe,{}),iconPosition:"start"})})]})}),e.jsx(ke,{in:V===0,timeout:300,children:e.jsx(d,{sx:{display:V===0?"block":"none"},children:e.jsxs(A,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Variable Selection"}),e.jsxs(U,{fullWidth:!0,size:"small",sx:{mb:2},children:[e.jsx(J,{children:"Dataset"}),e.jsx(_,{value:F,onChange:De,label:"Dataset",children:le.map(t=>e.jsx($,{value:t.id,children:t.name},t.id))})]}),h.length>0&&e.jsxs(d,{sx:{mb:2},children:[e.jsxs(d,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[e.jsxs(c,{variant:"subtitle2",children:["Current Hierarchy (",h.length,"/4)"]}),e.jsx(we,{size:"small",onClick:Ae,color:"error",variant:"text",children:"Clear All"})]}),e.jsx(d,{sx:{border:`2px dashed ${u.palette.divider}`,borderRadius:1,p:1,minHeight:120,backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},onDragOver:ge,onDrop:t=>be(t),children:h.length===0?e.jsxs(d,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:100,color:"text.secondary"},children:[e.jsx(re,{sx:{fontSize:32,mb:1,opacity:.5}}),e.jsx(c,{variant:"body2",textAlign:"center",children:"Drag variables here to build hierarchy"})]}):h.map((t,r)=>e.jsxs(d,{sx:{display:"flex",alignItems:"center",mb:1,p:1,backgroundColor:u.palette.background.paper,borderRadius:1,border:`1px solid ${u.palette.divider}`,cursor:"grab","&:hover":{backgroundColor:u.palette.action.hover}},draggable:!0,onDragStart:l=>xe(l,t.variable,!0),onDragOver:ge,onDrop:l=>be(l,r),children:[e.jsx(d,{sx:{width:24,height:24,borderRadius:"50%",backgroundColor:t.role==="root"?u.palette.primary.main:t.role==="leaf"?u.palette.secondary.main:u.palette.grey[500],color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"0.75rem",fontWeight:"bold",mr:1,flexShrink:0},children:r+1}),e.jsxs(d,{sx:{flexGrow:1,minWidth:0},children:[e.jsx(c,{variant:"body2",fontWeight:"medium",noWrap:!0,children:t.variableName}),e.jsxs(c,{variant:"caption",color:"text.secondary",children:[t.role," • Level ",t.level+1]})]}),e.jsx(ae,{size:"small",onClick:()=>Ve(t.variable),sx:{ml:1,flexShrink:0},children:e.jsx(c,{variant:"body2",children:"×"})})]},t.variable))})]}),e.jsx(c,{variant:"subtitle2",gutterBottom:!0,children:"Available Variables"}),e.jsx(c,{variant:"caption",color:"text.secondary",sx:{mb:1,display:"block"},children:"Click to add or drag to hierarchy"}),e.jsxs(d,{sx:{maxHeight:300,overflowY:"auto"},children:[ue.map(t=>{const r=h.some(l=>l.variable===t.id);return e.jsx(Ke,{sx:{mb:1,cursor:r?"default":"grab",opacity:r?.5:1,transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:r?"inherit":u.palette.action.hover,transform:r?"none":"translateX(4px)"}},draggable:!r,onDragStart:l=>!r&&xe(l,t.id),onClick:()=>!r&&Z(t.id),children:e.jsx(Je,{sx:{p:1.5,"&:last-child":{pb:1.5}},children:e.jsxs(d,{display:"flex",alignItems:"center",gap:1,children:[e.jsx(d,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:t.type===ie.NUMERIC?u.palette.primary.main:u.palette.secondary.main,flexShrink:0}}),e.jsxs(d,{sx:{flexGrow:1,minWidth:0},children:[e.jsx(c,{variant:"body2",fontWeight:"medium",noWrap:!0,children:t.name}),e.jsxs(c,{variant:"caption",color:"text.secondary",children:[t.type," • ",t.id]})]}),r&&e.jsx(Qe,{label:"Added",size:"small",color:"primary",variant:"outlined",sx:{fontSize:"0.6rem",height:20}})]})})},t.id)}),ue.length===0&&e.jsxs(d,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:100,color:"text.secondary"},children:[e.jsx(c,{variant:"body2",textAlign:"center",children:"No variables available"}),e.jsx(c,{variant:"caption",textAlign:"center",children:"Please select a dataset with numerical or categorical variables"})]})]}),e.jsxs(d,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(we,{variant:"contained",onClick:fe,disabled:h.length<2||P,startIcon:P?e.jsx(Ce,{size:20}):e.jsx(re,{}),fullWidth:!0,children:P?"Generating...":"Generate Tree"}),e.jsxs(d,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(K,{title:"Download Chart",children:e.jsx("span",{children:e.jsx(ae,{onClick:We,disabled:!q,children:e.jsx(Ze,{})})})}),e.jsx(K,{title:"Reset Settings",children:e.jsx(ae,{onClick:Me,children:e.jsx(et,{})})})]})]})]})})}),e.jsx(ke,{in:V===1,timeout:300,children:e.jsx(d,{sx:{display:V===1?"block":"none"},children:e.jsxs(A,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Tree Settings"}),e.jsxs(d,{display:"flex",flexDirection:"column",gap:3,children:[e.jsxs(d,{children:[e.jsx(c,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Title & Labels"}),e.jsx(ze,{fullWidth:!0,size:"small",label:"Chart Title",value:a.title,onChange:t=>k("title",t.target.value),margin:"dense"}),e.jsx(ze,{fullWidth:!0,size:"small",label:"Root Node Label",value:a.rootLabel,onChange:t=>k("rootLabel",t.target.value),margin:"dense",helperText:"Complete override of root node label (e.g., Study Size, Cohort Size, Sample Size, Total Participants)"})]}),e.jsxs(d,{children:[e.jsx(c,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Layout & Orientation"}),e.jsxs(U,{fullWidth:!0,size:"small",margin:"dense",children:[e.jsx(J,{children:"Orientation"}),e.jsxs(_,{value:a.orientation,onChange:t=>k("orientation",t.target.value),label:"Orientation",children:[e.jsx($,{value:"vertical",children:"Vertical (Top to Bottom)"}),e.jsx($,{value:"horizontal",children:"Horizontal (Left to Right)"})]})]}),e.jsxs(U,{fullWidth:!0,size:"small",margin:"dense",children:[e.jsx(J,{children:"Link Style"}),e.jsxs(_,{value:a.linkStyle,onChange:t=>k("linkStyle",t.target.value),label:"Link Style",children:[e.jsx($,{value:"curved",children:"Curved"}),e.jsx($,{value:"straight",children:"Straight"}),e.jsx($,{value:"stepped",children:"Stepped"})]})]})]}),e.jsxs(d,{children:[e.jsx(c,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(U,{fullWidth:!0,size:"small",margin:"dense",children:[e.jsx(J,{children:"Color Scheme"}),e.jsx(_,{value:a.colorScheme,onChange:t=>k("colorScheme",t.target.value),label:"Color Scheme",children:Object.keys(se).map(t=>e.jsx($,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})]}),e.jsxs(d,{children:[e.jsx(c,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Size & Spacing"}),e.jsxs(d,{sx:{mt:2},children:[e.jsxs(c,{variant:"body2",gutterBottom:!0,children:["Node Size: ",a.nodeSize,"px"]}),e.jsx(ne,{value:a.nodeSize,onChange:(t,r)=>k("nodeSize",r),step:5,marks:!0,min:20,max:80,valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(d,{sx:{mt:2},children:[e.jsxs(c,{variant:"body2",gutterBottom:!0,children:["Node Spacing: ",a.spacing,"px"]}),e.jsx(ne,{value:a.spacing,onChange:(t,r)=>k("spacing",r),step:20,marks:!0,min:40,max:300,valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(d,{sx:{mt:2},children:[e.jsxs(c,{variant:"body2",gutterBottom:!0,children:["Link Length: ",a.linkLength,"px"]}),e.jsx(ne,{value:a.linkLength,onChange:(t,r)=>k("linkLength",r),step:10,marks:!0,min:50,max:400,valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(d,{sx:{mt:2},children:[e.jsx(c,{variant:"body2",gutterBottom:!0,children:"Node Shape"}),e.jsx(U,{fullWidth:!0,size:"small",children:e.jsxs(_,{value:a.nodeShape,onChange:t=>k("nodeShape",t.target.value),children:[e.jsx($,{value:"circle",children:"Circle"}),e.jsx($,{value:"rectangle",children:"Rectangle"})]})})]})]}),e.jsxs(d,{children:[e.jsx(c,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(tt,{children:[e.jsx(Le,{control:e.jsx(Te,{size:"small",checked:a.showStatistics,onChange:t=>k("showStatistics",t.target.checked)}),label:"Show Statistics"}),e.jsx(Le,{control:e.jsx(Te,{size:"small",checked:a.showLabels,onChange:t=>k("showLabels",t.target.checked)}),label:"Show Variable Labels"})]})]}),e.jsxs(d,{children:[e.jsx(c,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Tree Controls"}),e.jsx(c,{variant:"caption",color:"text.secondary",children:"Use the Variables panel to add/remove variables from the hierarchy. Adjust settings here to customize the tree appearance."})]})]})]})})})]}),e.jsx(I,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(A,{elevation:2,sx:{p:2},children:[e.jsxs(d,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(c,{variant:"h6",children:"Tree Visualization"}),e.jsxs(d,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Active: ",V===0?"Variables":"Settings"]}),e.jsx(d,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:V===0?u.palette.primary.main:u.palette.warning.main,boxShadow:`0 0 0 2px ${V===0?u.palette.primary.main+"20":u.palette.warning.main+"20"}`}})]})]}),he&&e.jsx(rt,{severity:"error",sx:{mb:2},children:he}),e.jsx(d,{sx:{minHeight:500,display:"flex",justifyContent:"center",alignItems:"center",border:`1px solid ${u.palette.divider}`,borderRadius:1,backgroundColor:u.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)"},children:P?e.jsxs(d,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(Ce,{}),e.jsx(c,{color:"text.secondary",children:"Generating tree visualization..."})]}):q?e.jsxs(d,{sx:{width:"100%",height:"500px",overflow:"hidden",position:"relative"},children:[e.jsx("svg",{ref:D,style:{width:"100%",height:"100%",display:"block"}}),e.jsx(c,{variant:"caption",color:"text.secondary",sx:{position:"absolute",bottom:8,left:8,backgroundColor:"rgba(255, 255, 255, 0.8)",padding:"4px 8px",borderRadius:1,fontSize:"0.75rem"},children:"💡 Use mouse wheel to zoom, click and drag to pan"})]}):e.jsxs(d,{textAlign:"center",children:[e.jsx(re,{sx:{fontSize:64,color:"text.disabled",mb:2}}),e.jsx(c,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No Tree Generated"}),e.jsx(c,{variant:"body2",color:"text.secondary",children:'Select at least 2 variables and click "Generate Tree" to create your visualization'})]})}),q&&e.jsxs(d,{mt:2,children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Tree Summary"}),e.jsxs(I,{container:!0,spacing:2,children:[e.jsx(I,{item:!0,xs:6,sm:3,children:e.jsxs(A,{sx:{p:2,textAlign:"center"},children:[e.jsx(c,{variant:"h4",color:"primary",children:h.length}),e.jsx(c,{variant:"body2",color:"text.secondary",children:"Variables"})]})}),e.jsx(I,{item:!0,xs:6,sm:3,children:e.jsxs(A,{sx:{p:2,textAlign:"center"},children:[e.jsx(c,{variant:"h4",color:"primary",children:h.length-1}),e.jsx(c,{variant:"body2",color:"text.secondary",children:"Levels"})]})}),e.jsx(I,{item:!0,xs:6,sm:3,children:e.jsxs(A,{sx:{p:2,textAlign:"center"},children:[e.jsx(c,{variant:"h4",color:"primary",children:(f==null?void 0:f.data.length)||0}),e.jsx(c,{variant:"body2",color:"text.secondary",children:"Data Points"})]})}),e.jsx(I,{item:!0,xs:6,sm:3,children:e.jsxs(A,{sx:{p:2,textAlign:"center"},children:[e.jsx(c,{variant:"h4",color:"primary",children:a.orientation==="vertical"?"V":"H"}),e.jsx(c,{variant:"body2",color:"text.secondary",children:"Orientation"})]})})]})]})]})})]})]})};export{mt as default};
