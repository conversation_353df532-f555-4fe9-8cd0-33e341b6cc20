import{j as e,R as vt,B as E,aH as Bt,e as m,aE as Re,I as Ee,bs as Fa,ah as Ce,aj as Le,bc as Pe,g as Ge,f as B,ae as Va,bS as Qe,bT as Je,bU as Ye,a_ as Jt,ak as ut,ao as Mt,ap as Tt,aq as At,ar as rt,as as ce,at as Ot,ai as Te,ba as ze,bb as z,h as Me,bV as Ke,u as Nt,i as he,dx as Ua,cN as Pa,bE as sa,J as Wa,aN as Gt,ch as Pt,a2 as Ba,C as Lt,br as W,F as It,bv as Rt,cm as ra,k as ke,aF as Ga,dy as _a,G as Z,bz as Wt,d as lt,cP as Ha,l as Be,aG as Yt,a3 as qa,N as kt,aS as Xa,b9 as Fe,D as mt,d0 as ya,cy as Qa,c4 as Ja,a as Ca,cU as gt,aL as dt,dz as Ya,dA as Ka,bl as Et,b_ as Kt,am as Dt,bL as ba,bM as zt,c_ as la,Q as ot,dB as Za,bi as en,dC as tn,dk as Zt,dl as ea,bj as tt,dD as va,cc as an,cT as _t,dE as nn,cs as ta,n as Xe,dF as sn,r as qe,bH as rn,dG as ln,dH as jt,dI as yt,dJ as Ct,dK as ft,an as bt,dL as Ht,L as qt,m as Xt,c6 as Ea,dM as on,aK as wa,ay as Na,dj as Qt,dN as oa,bm as ia,dO as ca,by as cn,a6 as dn,a7 as da,bB as un,dP as ua,dn as mn,dQ as hn,o as ma,aI as wt,bR as xn,dR as pn,O as gn,cr as Ia,s as fn,bn as jn,bo as yn,aJ as Cn,be as ha,Z as xa,aX as pa,dt as bn,dS as vn,dT as En,bO as wn,b$ as Nn,av as In}from"./mui-libs-CfwFIaTD.js";import{r as u}from"./react-libs-Cr2nE3UY.js";import{a as it,b as $t,V as i,E as $e,D as t,u as Dn,F as Sn,G as Mn,L as Tn,I as An,J as On,K as Rn,M as kn,i as zn,T as je,O as Ln,Q as $n,R as Fn,S as Vn,U as Un,W as Pn,X as Wn,Y as Bn}from"./index-Bpan7Tbe.js";import{v as g}from"./other-utils-CR9xr_gI.js";import{P as Gn}from"./PageTitle-DA3BXQ4x.js";import{T as _n}from"./AnalysisSteps-CmfAFU5C.js";import"./StatsCard-op8tGQ0a.js";import{D as Hn}from"./DatasetSelector-G08QHuaN.js";import"./VariableSelector-CPdlCsJ2.js";import{T as ct}from"./TabPanel-CVuv1-VX.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";const qn=({onImportSuccess:y,disabled:s})=>{const{addDataset:a}=it(),{canImportData:h}=$t(),[n,c]=u.useState(""),[S,O]=u.useState(!1),[C,A]=u.useState(null),[w,b]=u.useState({hasHeader:!0,skipEmptyLines:!0,dynamicTyping:!0}),p=x=>{const M=x.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);return M?M[1]:null},D=x=>{const M=x.filter(f=>f!==""&&f!==null&&f!==void 0);if(M.length===0)return t.TEXT;const T=["0","1"];if(M.every(f=>T.includes(f)))return t.NUMERIC;const U=["yes","no"];if(M.every(f=>U.includes(f.toLowerCase())))return t.CATEGORICAL;const F=["true","false"];if(M.every(f=>F.includes(f.toLowerCase())))return t.BOOLEAN;if(M.every(f=>!isNaN(Number(f))&&f.trim()!==""))return t.NUMERIC;const X=/^\d{1,4}[\/\-]\d{1,2}[\/\-]\d{1,4}$|^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/;return M.some(f=>X.test(f)||!isNaN(Date.parse(f)))?t.DATE:t.CATEGORICAL},J=async()=>{if(O(!0),A(null),!h){A("Please login to import data. Guest users can only use sample datasets."),O(!1);return}try{const x=p(n);if(!x)throw new Error("Invalid Google Sheets URL. Please provide a valid URL.");const M=`https://docs.google.com/spreadsheets/d/${x}/export?format=csv`,T=await fetch(M);if(!T.ok)throw new Error('Failed to fetch data from Google Sheets. Make sure the sheet is publicly accessible ("Anyone with the link can view")');const F=(await T.text()).split(`
`).map(N=>N.split(",").map(se=>{const $=se.trim();return $.startsWith('"')&&$.endsWith('"')?$.substring(1,$.length-1):$}));if(F.length===0)throw new Error("The spreadsheet appears to be empty.");let Q=[],X=[];w.hasHeader&&F.length>0?(Q=F[0],X=F.slice(1)):(Q=F[0].map((N,se)=>`Column ${se+1}`),X=F),w.skipEmptyLines&&(X=X.filter(N=>N.some(se=>se.trim()!=="")));const oe=Q.map((N,se)=>{const $=X.map(G=>G[se]||""),ee=D($);return{id:$e(),name:N,type:ee,role:i.NONE}}),f=X.map(N=>{const se={};return Q.forEach(($,ee)=>{const G=N[ee]||"";if(w.dynamicTyping){const xe=oe[ee].type;if(xe===t.NUMERIC&&G!=="")se[$]=Number(G);else if(xe===t.DATE&&G!=="")try{se[$]=new Date(G)}catch{se[$]=G}else se[$]=G}else se[$]=G}),se}),P={id:$e(),name:`Google Sheet Import ${new Date().toLocaleDateString()}`,description:`Imported from Google Sheets: ${n}`,dateCreated:new Date,dateModified:new Date,columns:oe,data:f},L=await a(P);O(!1),typeof L=="string"&&(L==="added"||L==="selected")&&(c(""),y&&y())}catch(x){A(`Failed to import data: ${x instanceof Error?x.message:String(x)}`),O(!1)}},K=x=>{b(M=>({...M,[x]:!M[x]}))};return e.jsxs(vt,{elevation:0,sx:{p:3,mb:3},children:[e.jsxs(E,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(Bt,{sx:{mr:1,color:"primary.main"}}),e.jsx(m,{variant:"h6",children:"Import from Google Sheets"}),e.jsx(Re,{title:"The Google Sheet must be publicly accessible (set to 'Anyone with the link can view')",children:e.jsx(Ee,{size:"small",sx:{ml:1},children:e.jsx(Fa,{fontSize:"small"})})})]}),e.jsx(m,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Import data directly from a Google Sheets document. Paste the URL of your Google Sheet below."}),e.jsx(Ce,{fullWidth:!0,label:"Google Sheets URL",placeholder:"https://docs.google.com/spreadsheets/d/...",value:n,onChange:x=>c(x.target.value),variant:"outlined",sx:{mb:2},disabled:S}),e.jsxs(E,{sx:{mb:2},children:[e.jsx(m,{variant:"subtitle2",sx:{mb:1},children:"Import Options"}),e.jsx(Le,{control:e.jsx(Pe,{checked:w.hasHeader,onChange:()=>K("hasHeader"),disabled:S||s}),label:"First row contains headers"}),e.jsx(Le,{control:e.jsx(Pe,{checked:w.skipEmptyLines,onChange:()=>K("skipEmptyLines"),disabled:S||s}),label:"Skip empty rows"}),e.jsx(Le,{control:e.jsx(Pe,{checked:w.dynamicTyping,onChange:()=>K("dynamicTyping"),disabled:S||s}),label:"Automatically detect data types"})]}),C&&e.jsx(Ge,{severity:"error",sx:{mb:2},children:C}),e.jsx(B,{variant:"contained",color:"primary",onClick:J,disabled:!n.trim()||S||s,startIcon:S?e.jsx(Va,{size:20}):e.jsx(Bt,{}),children:S?"Importing...":"Import from Google Sheets"})]})};function at(y){return function(){let s=y+=**********;return s=Math.imul(s^s>>>15,s|1),s^=s+Math.imul(s^s>>>7,s|61),((s^s>>>14)>>>0)/**********}}const Xn=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Health Dataset",columns:[{id:`col-${g()}`,name:"Age",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Gender",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Income",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Education",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Satisfaction",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Height",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Weight",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"BloodPressure",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Cholesterol",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Diabetic",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Smoker",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"HeartDisease",type:t.NUMERIC,role:i.NONE}],data:[]};for(let h=0;h<100;h++){const n={},c=Math.floor(s()*50)+18,S=s()>.5?"Male":"Female";n[a.columns[0].name]=c,n[a.columns[1].name]=S,n[a.columns[2].name]=Math.floor(s()*1e5)+2e4,n[a.columns[3].name]=["High School","College","Graduate","PhD"][Math.floor(s()*4)],n[a.columns[4].name]=Math.floor(s()*10)+1;const O=S==="Male"?175:162;n[a.columns[5].name]=O+Math.floor(s()*20)-10;const A=n[a.columns[5].name]-100+c/10;n[a.columns[6].name]=A+Math.floor(s()*30)-10;const w=n[a.columns[6].name],b=110+c/10+(w-A)/2;n[a.columns[7].name]=Math.max(100,Math.min(200,Math.floor(b+s()*20-10)));const p=n[a.columns[7].name],D=150+c/5+(w-A)/2+(p-120)/2;n[a.columns[8].name]=Math.max(120,Math.min(300,Math.floor(D+s()*30-15)));const J=Math.min(.5,(c-30)/100+(w-A)/100);n[a.columns[9].name]=s()<J?1:0,n[a.columns[10].name]=s()<.3?1:0;const K=c/200+(p-120)/200+(n[a.columns[8].name]-150)/200+n[a.columns[9].name]*.2+n[a.columns[10].name]*.2;n[a.columns[11].name]=s()<K?1:0,a.data.push(n)}return{id:`dataset-${g()}`,...a}},Qn=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Credit Risk Dataset",columns:[{id:`col-${g()}`,name:"Income",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Age",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"LoanAmount",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"CreditScore",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"PreviousDefault",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"EmploymentYears",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"HasProperty",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"DefaultRisk",type:t.NUMERIC,role:i.NONE}],data:[]};for(let h=0;h<150;h++){const n={};n[a.columns[0].name]=Math.floor(s()*8e4)+2e4,n[a.columns[1].name]=Math.floor(s()*50)+20,n[a.columns[2].name]=Math.floor(s()*5e4)+5e3,n[a.columns[3].name]=Math.floor(s()*550)+300,n[a.columns[4].name]=s()<.2?1:0,n[a.columns[5].name]=Math.floor(s()*20),n[a.columns[6].name]=s()<.4?1:0;const c=n[a.columns[0].name],S=n[a.columns[1].name],O=n[a.columns[2].name],C=n[a.columns[3].name],A=n[a.columns[4].name],w=n[a.columns[5].name],b=n[a.columns[6].name],p=(C-500)/350,D=c/1e5,J=O/c,K=Math.min(1,S/60),x=Math.min(1,w/10),M=p*.4+D*.2+(1-J)*.2+K*.1+x*.1-A*.3+b*.1,T=Math.max(0,Math.min(1,1-M))+(s()*.2-.1);n[a.columns[7].name]=s()<T?1:0,a.data.push(n)}return{id:`dataset-${g()}`,...a}},Jn=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Employee Satisfaction Dataset",columns:[{id:`col-${g()}`,name:"EmployeeID",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Age",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Department",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"JobLevel",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"YearsAtCompany",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"MonthlyIncome",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"WorkLifeBalance",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"JobSatisfaction",type:t.NUMERIC,role:i.NONE}],data:[]},h=["HR","Engineering","Marketing","Sales","Finance"],n=["Entry","Junior","Mid-Level","Senior","Manager"];for(let c=0;c<120;c++){const S={},O=Math.floor(s()*40)+22,C=h[Math.floor(s()*h.length)],A=n[Math.floor(s()*n.length)],w=Math.floor(s()*(O-21)),b=3e3+n.indexOf(A)*1500+w*100+Math.floor(s()*1e3),p=Math.floor(s()*4)+1;let D=2;D+=b/1e4,D+=p/4,D+=n.indexOf(A)/4,S[a.columns[0].name]=`EMP-${1001+c}`,S[a.columns[1].name]=O,S[a.columns[2].name]=C,S[a.columns[3].name]=A,S[a.columns[4].name]=Math.max(0,w),S[a.columns[5].name]=Math.floor(b),S[a.columns[6].name]=p,S[a.columns[7].name]=Math.max(1,Math.min(5,Math.round(D+(s()*1-.5)))),a.data.push(S)}return{id:`dataset-${g()}`,...a}},Yn=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Student Performance Dataset",columns:[{id:`col-${g()}`,name:"StudentID",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Gender",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"PreviousGrade",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"StudyHoursWeekly",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"AttendancePercentage",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"HasTutor",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"FinalScore",type:t.NUMERIC,role:i.NONE}],data:[]},h=["Male","Female","Other"];for(let n=0;n<150;n++){const c={},S=h[Math.floor(s()*h.length)],O=Math.floor(s()*60)+40,C=Math.floor(s()*20)+1,A=Math.floor(s()*50)+50,w=s()<.3?1:0;let b=O*.4;b+=C*1.5,b+=A/100*20,b+=w*10,c[a.columns[0].name]=`STU-${2001+n}`,c[a.columns[1].name]=S,c[a.columns[2].name]=O,c[a.columns[3].name]=C,c[a.columns[4].name]=A,c[a.columns[5].name]=w,c[a.columns[6].name]=Math.max(0,Math.min(100,Math.floor(b+(s()*10-5)))),a.data.push(c)}return{id:`dataset-${g()}`,...a}},Kn=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Reliability Analysis Dataset",columns:[{id:`col-${g()}`,name:"ParticipantID",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Item1",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Item2",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Item3",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Item4",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Item5",type:t.NUMERIC,role:i.NONE}],data:[]};for(let h=0;h<80;h++){const n={};n[a.columns[0].name]=`PART-${3001+h}`;const c=Math.floor(s()*3)+1;n[a.columns[1].name]=Math.max(1,Math.min(5,c+Math.floor(s()*3)-1)),n[a.columns[2].name]=Math.max(1,Math.min(5,c+Math.floor(s()*3)-1)),n[a.columns[3].name]=Math.max(1,Math.min(5,c+Math.floor(s()*3)-1)),n[a.columns[4].name]=Math.max(1,Math.min(5,c+Math.floor(s()*3)-1)),n[a.columns[5].name]=Math.max(1,Math.min(5,c+Math.floor(s()*3)-1)),a.data.push(n)}return{id:`dataset-${g()}`,...a}},Zn=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Factor Analysis Dataset",columns:[{id:`col-${g()}`,name:"SubjectID",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Q1_Math",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q2_Math",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q3_Math",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q4_Math",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q5_Math",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q1_Verbal",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q2_Verbal",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q3_Verbal",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q4_Verbal",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q5_Verbal",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q1_Spatial",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q2_Spatial",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q3_Spatial",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q4_Spatial",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Q5_Spatial",type:t.NUMERIC,role:i.NONE}],data:[]};for(let h=0;h<100;h++){const n={};n[a.columns[0].name]=`SUB-${4001+h}`;const c=s()*5+1,S=s()*5+1,O=s()*5+1;for(let C=1;C<=5;C++)n[`Q${C}_Math`]=Math.max(1,Math.min(7,Math.round(c*(.6+s()*.3)+s()*2))),n[`Q${C}_Verbal`]=Math.max(1,Math.min(7,Math.round(S*(.6+s()*.3)+s()*2))),n[`Q${C}_Spatial`]=Math.max(1,Math.min(7,Math.round(O*(.6+s()*.3)+s()*2)));a.data.push(n)}return{id:`dataset-${g()}`,...a}},es=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Survival Analysis Dataset",columns:[{id:`col-${g()}`,name:"PatientID",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"Time",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Event",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Age",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"Treatment",type:t.CATEGORICAL,role:i.NONE}],data:[]},h=["A","B","C"];for(let n=0;n<100;n++){const c={},S=Math.floor(s()*40)+30,O=h[Math.floor(s()*h.length)];let C=100+(70-S)*2;O==="B"&&(C+=30),O==="C"&&(C-=20);const A=Math.max(10,Math.min(200,Math.floor(C+s()*40-20))),w=s()<(200-A)/150?1:0;c[a.columns[0].name]=`PAT-${5001+n}`,c[a.columns[1].name]=A,c[a.columns[2].name]=w,c[a.columns[3].name]=S,c[a.columns[4].name]=O,a.data.push(c)}return{id:`dataset-${g()}`,...a}},ts=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Mediation/Moderation Dataset",columns:[{id:`col-${g()}`,name:"SubjectID",type:t.CATEGORICAL,role:i.NONE},{id:`col-${g()}`,name:"IndependentVar",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"MediatorVar",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"DependentVar",type:t.NUMERIC,role:i.NONE},{id:`col-${g()}`,name:"ModeratorVar",type:t.NUMERIC,role:i.NONE}],data:[]};for(let h=0;h<100;h++){const n={},c=s()*10,S=s()*5,O=c*.6+s()*3,C=c*.3+O*.5+c*S*.2+s()*5;n[a.columns[0].name]=`SUB-${6001+h}`,n[a.columns[1].name]=parseFloat(c.toFixed(2)),n[a.columns[2].name]=parseFloat(O.toFixed(2)),n[a.columns[3].name]=parseFloat(C.toFixed(2)),n[a.columns[4].name]=parseFloat(S.toFixed(2)),a.data.push(n)}return{id:`dataset-${g()}`,...a}},as=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Meta Analysis Dataset",description:"Study-level data for meta-analysis with effect sizes, confidence intervals, and study characteristics",columns:[{id:g(),name:"Study_ID",type:t.TEXT,role:i.NONE,description:"Unique study identifier"},{id:g(),name:"Study_Name",type:t.TEXT,role:i.NONE,description:"Name of the study"},{id:g(),name:"Publication_Year",type:t.NUMERIC,role:i.INDEPENDENT,description:"Year of publication"},{id:g(),name:"Sample_Size",type:t.NUMERIC,role:i.INDEPENDENT,description:"Total sample size"},{id:g(),name:"Effect_Size",type:t.NUMERIC,role:i.DEPENDENT,description:"Standardized effect size (Cohen's d)"},{id:g(),name:"Standard_Error",type:t.NUMERIC,role:i.INDEPENDENT,description:"Standard error of effect size"},{id:g(),name:"CI_Lower",type:t.NUMERIC,role:i.INDEPENDENT,description:"95% confidence interval lower bound"},{id:g(),name:"CI_Upper",type:t.NUMERIC,role:i.INDEPENDENT,description:"95% confidence interval upper bound"},{id:g(),name:"Study_Quality",type:t.CATEGORICAL,role:i.INDEPENDENT,description:"Study quality rating"},{id:g(),name:"Country",type:t.CATEGORICAL,role:i.INDEPENDENT,description:"Country where study was conducted"},{id:g(),name:"Population_Type",type:t.CATEGORICAL,role:i.INDEPENDENT,description:"Type of population studied"},{id:g(),name:"Intervention_Duration",type:t.NUMERIC,role:i.INDEPENDENT,description:"Duration of intervention in weeks"}],data:[]},h=["High","Medium","Low"],n=["USA","UK","Canada","Germany","Australia","France","Netherlands","Sweden","Japan","Brazil"],c=["Adults","Children","Elderly","Students","Patients","Athletes"],S=["Smith et al.","Johnson & Brown","Williams Study","Davis Research","Miller Investigation","Wilson Analysis","Moore Experiment","Taylor Trial","Anderson Study","Thomas Research","Jackson Investigation","White Analysis","Harris Experiment","Martin Trial","Thompson Study","Garcia Research","Martinez Investigation","Robinson Analysis","Clark Experiment","Rodriguez Trial","Lewis Study","Lee Research","Walker Investigation","Hall Analysis","Allen Experiment","Young Trial","Hernandez Study","King Research","Wright Investigation","Lopez Analysis"];for(let O=0;O<30;O++){const C={};C[a.columns[0].name]=`STUDY_${String(O+1).padStart(3,"0")}`,C[a.columns[1].name]=S[O%S.length],C[a.columns[2].name]=Math.floor(2010+s()*14);const A=4+s()*2;C[a.columns[3].name]=Math.floor(Math.exp(A));const w=.3+(s()-.5)*.6,b=C[a.columns[3].name],p=Math.sqrt(2/b)+s()*.05,D=w+(s()-.5)*2*p;C[a.columns[4].name]=parseFloat(D.toFixed(3)),C[a.columns[5].name]=parseFloat(p.toFixed(4));const J=D-1.96*p,K=D+1.96*p;C[a.columns[6].name]=parseFloat(J.toFixed(3)),C[a.columns[7].name]=parseFloat(K.toFixed(3)),C[a.columns[8].name]=h[Math.floor(s()*h.length)],C[a.columns[9].name]=n[Math.floor(s()*n.length)],C[a.columns[10].name]=c[Math.floor(s()*c.length)],C[a.columns[11].name]=Math.floor(4+s()*20),a.data.push(C)}return{id:`dataset-${g()}`,...a}},ns=y=>{const s=y!==void 0?at(y):Math.random,a={name:"Cluster Analysis Dataset",description:"Customer segmentation data with multiple numeric variables for clustering analysis",columns:[{id:g(),name:"Customer_ID",type:t.TEXT,role:i.NONE,description:"Unique customer identifier"},{id:g(),name:"Annual_Income",type:t.NUMERIC,role:i.INDEPENDENT,description:"Annual income in thousands"},{id:g(),name:"Spending_Score",type:t.NUMERIC,role:i.INDEPENDENT,description:"Spending score (1-100)"},{id:g(),name:"Age",type:t.NUMERIC,role:i.INDEPENDENT,description:"Customer age in years"},{id:g(),name:"Purchase_Frequency",type:t.NUMERIC,role:i.INDEPENDENT,description:"Number of purchases per month"},{id:g(),name:"Online_Engagement",type:t.NUMERIC,role:i.INDEPENDENT,description:"Online engagement score (0-10)"},{id:g(),name:"Customer_Tenure",type:t.NUMERIC,role:i.INDEPENDENT,description:"Years as customer"},{id:g(),name:"Product_Diversity",type:t.NUMERIC,role:i.INDEPENDENT,description:"Number of different product categories purchased"}],data:[]},h=[{income:80,spending:85,age:45,frequency:8,engagement:8.5,tenure:5,diversity:12},{income:35,spending:25,age:35,frequency:2,engagement:4,tenure:2,diversity:4},{income:55,spending:55,age:40,frequency:5,engagement:6,tenure:3,diversity:8},{income:45,spending:70,age:28,frequency:12,engagement:9,tenure:1.5,diversity:6},{income:65,spending:40,age:60,frequency:3,engagement:3,tenure:8,diversity:5}];for(let n=0;n<400;n++){const c={},S=Math.floor(s()*h.length),O=h[S],C=()=>(s()-.5)*.4;c[a.columns[0].name]=`CUST_${String(n+1).padStart(4,"0")}`,c[a.columns[1].name]=Math.max(20,Math.min(120,Math.round(O.income*(1+C())))),c[a.columns[2].name]=Math.max(1,Math.min(100,Math.round(O.spending*(1+C())))),c[a.columns[3].name]=Math.max(18,Math.min(80,Math.round(O.age*(1+C())))),c[a.columns[4].name]=Math.max(0,Math.round(O.frequency*(1+C()))),c[a.columns[5].name]=parseFloat(Math.max(0,Math.min(10,O.engagement*(1+C()))).toFixed(1)),c[a.columns[6].name]=parseFloat(Math.max(.1,O.tenure*(1+C())).toFixed(1)),c[a.columns[7].name]=Math.max(1,Math.min(20,Math.round(O.diversity*(1+C())))),a.data.push(c)}return{id:`dataset-${g()}`,...a}};function St(y){y=y.trim();const s=y.split(/\r?\n/);if(s.length===0)return{headers:[],rows:[]};const a=s[0],h=(a.match(/\t/g)||[]).length,n=(a.match(/,/g)||[]).length,c=h>n?"	":",",S=s.map(A=>A.split(c).map(w=>w.trim())),O=S[0];return O.some(A=>isNaN(Number(A))&&A!=="")?{headers:O,rows:S.slice(1)}:{headers:O.map((w,b)=>`Column${b+1}`),rows:S}}function ss(y){if(y.length===0)return[];const s=y[0].length,a=[];for(let h=0;h<s;h++){const n=y.map(w=>w[h]).filter(w=>w!==""&&w!==null);if(n.length===0){a.push(t.TEXT);continue}const c=["0","1"];if(n.length>0&&n.every(w=>c.includes(w))){a.push(t.NUMERIC);continue}const S=["yes","no"];if(n.every(w=>S.includes(w.toLowerCase()))){a.push(t.CATEGORICAL);continue}const O=["true","false"];if(n.every(w=>O.includes(w.toLowerCase()))){a.push(t.BOOLEAN);continue}if(n.every(w=>{const b=!isNaN(Date.parse(w)),p=/[/.-]/.test(w);return b&&(p||/^\d{4}-\d{2}-\d{2}$/.test(w)||/^\d{2}\/\d{2}\/\d{4}$/.test(w))})){a.push(t.DATE);continue}if(n.every(w=>!isNaN(Number(w)))){a.push(t.NUMERIC);continue}const C=new Set(n.map(w=>w.toLowerCase()));if(C.size>0&&C.size<=20){a.push(t.CATEGORICAL);continue}a.push(t.TEXT)}return a}const Da=({open:y,onClose:s,pastedHeaders:a,pastedRows:h,existingColumns:n,onConfirm:c})=>{const[S,O]=u.useState([]),[C,A]=u.useState("append"),[w,b]=u.useState(""),[p,D]=u.useState(!0);u.useEffect(()=>{if(y&&a.length>0){const M=ss(h),T=a.map((U,F)=>{const Q=p?n.find(X=>X.name.toLowerCase()===U.toLowerCase()):null;return{pastedColumn:U,targetColumn:Q?Q.name:"new",dataType:Q?Q.type:M[F],createNew:!Q}});O(T)}},[y,a,h,n,p]);const J=(M,T,U)=>{const F=[...S];if(F[M]={...F[M],[T]:U},T==="targetColumn"&&(F[M].createNew=U==="new",U!=="new")){const Q=n.find(X=>X.name===U);Q&&(F[M].dataType=Q.type)}O(F)},K=()=>{if(C==="newDataset"&&!w.trim()){alert("Please enter a name for the new dataset");return}c(S,C,w)},x=Math.min(5,h.length);return e.jsxs(Qe,{open:y,onClose:s,maxWidth:"lg",fullWidth:!0,children:[e.jsx(Je,{children:"Import Data Preview"}),e.jsxs(Ye,{dividers:!0,children:[e.jsxs(Ge,{severity:"info",sx:{mb:2},children:["Found ",h.length," rows and ",a.length," columns"]}),e.jsxs(E,{sx:{mb:3},children:[e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"Import Mode"}),e.jsxs(Jt,{value:C,onChange:M=>A(M.target.value),children:[e.jsx(Le,{value:"append",control:e.jsx(ut,{}),label:"Append to current dataset"}),e.jsx(Le,{value:"replace",control:e.jsx(ut,{}),label:"Replace current dataset"}),e.jsx(Le,{value:"newDataset",control:e.jsx(ut,{}),label:"Create new dataset"})]}),C==="newDataset"&&e.jsx(Ce,{label:"Dataset Name",value:w,onChange:M=>b(M.target.value),fullWidth:!0,margin:"normal",required:!0})]}),e.jsx(E,{sx:{mb:2},children:e.jsx(Le,{control:e.jsx(Pe,{checked:p,onChange:M=>D(M.target.checked)}),label:"Auto-map columns by name"})}),e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"Column Mapping"}),e.jsx(Mt,{component:vt,sx:{mb:2},children:e.jsxs(Tt,{size:"small",children:[e.jsx(At,{children:e.jsxs(rt,{children:[e.jsx(ce,{children:"Source Column"}),e.jsx(ce,{children:"Target Column"}),e.jsx(ce,{children:"Data Type"}),e.jsx(ce,{children:"Sample Values"})]})}),e.jsx(Ot,{children:S.map((M,T)=>e.jsxs(rt,{children:[e.jsx(ce,{children:M.pastedColumn}),e.jsx(ce,{children:e.jsx(Te,{fullWidth:!0,size:"small",children:e.jsxs(ze,{value:M.targetColumn,onChange:U=>J(T,"targetColumn",U.target.value),children:[e.jsx(z,{value:"new",children:e.jsx("em",{children:"Create New Column"})}),n.map(U=>e.jsx(z,{value:U.name,children:U.name},U.id))]})})}),e.jsx(ce,{children:e.jsx(Te,{fullWidth:!0,size:"small",children:e.jsxs(ze,{value:M.dataType,onChange:U=>J(T,"dataType",U.target.value),disabled:M.targetColumn!=="new",children:[e.jsx(z,{value:t.TEXT,children:"Text"}),e.jsx(z,{value:t.NUMERIC,children:"Numeric"}),e.jsx(z,{value:t.BOOLEAN,children:"Boolean"}),e.jsx(z,{value:t.DATE,children:"Date"}),e.jsx(z,{value:t.CATEGORICAL,children:"Categorical"})]})})}),e.jsx(ce,{children:e.jsxs(E,{sx:{display:"flex",gap:.5,flexWrap:"wrap"},children:[h.slice(0,3).map((U,F)=>e.jsx(Me,{label:U[T]||"<empty>",size:"small",variant:"outlined"},F)),h.length>3&&e.jsx(Me,{label:"...",size:"small"})]})})]},T))})]})}),e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"Data Preview"}),e.jsx(Mt,{component:vt,children:e.jsxs(Tt,{size:"small",children:[e.jsx(At,{children:e.jsx(rt,{sx:{fontWeight:"bold",backgroundColor:"#f5f5f5"},children:a.map((M,T)=>e.jsx(ce,{children:M},T))})}),e.jsxs(Ot,{children:[h.slice(0,x).map((M,T)=>e.jsx(rt,{children:M.map((U,F)=>e.jsx(ce,{children:U},F))},T)),h.length>x&&e.jsx(rt,{children:e.jsxs(ce,{colSpan:a.length,align:"center",children:["... and ",h.length-x," more rows"]})})]})]})})]}),e.jsxs(Ke,{children:[e.jsx(B,{onClick:s,children:"Cancel"}),e.jsx(B,{onClick:K,variant:"contained",color:"primary",children:"Import Data"})]})]})},rs=({onImportSuccess:y})=>{const{addDataset:s}=it(),a=u.useRef(null),h=Nt(),n=Dn(),{user:c,isGuest:S,canAccessSampleData:O,canImportData:C,loginAsGuest:A}=$t(),[w,b]=u.useState(!1),[p,D]=u.useState(null),[J,K]=u.useState(null),[x,M]=u.useState(null),[T,U]=u.useState(!1),[F,Q]=u.useState(!1),[X,oe]=u.useState({headers:[],rows:[]}),[f,P]=u.useState({hasHeader:!0,skipEmptyLines:!0,dynamicTyping:!0}),L=[{id:"health",name:"Health Dataset",description:"Medical research data with patient outcomes and treatment effectiveness",icon:e.jsx(Ua,{}),color:"#e91e63",bgColor:he("#e91e63",.1),handler:te,tags:["Medical","Research","Outcomes"],rows:500,variables:12},{id:"credit",name:"Credit Risk Dataset",description:"Financial data for credit scoring and risk assessment models",icon:e.jsx(Pa,{}),color:"#2196f3",bgColor:he("#2196f3",.1),handler:Ie,tags:["Finance","Risk","Classification"],rows:1e3,variables:15},{id:"employee",name:"Employee Satisfaction",description:"HR survey data with performance metrics and satisfaction scores",icon:e.jsx(sa,{}),color:"#4caf50",bgColor:he("#4caf50",.1),handler:ae,tags:["HR","Survey","Performance"],rows:300,variables:10},{id:"student",name:"Student Performance",description:"Academic performance data with demographic and socioeconomic factors",icon:e.jsx(Wa,{}),color:"#ff9800",bgColor:he("#ff9800",.1),handler:be,tags:["Education","Performance","Demographics"],rows:400,variables:14},{id:"reliability",name:"Reliability Analysis",description:"Scale reliability data for internal consistency testing",icon:e.jsx(Gt,{}),color:"#9c27b0",bgColor:he("#9c27b0",.1),handler:nt,tags:["Psychology","Reliability","Scale"],rows:250,variables:20},{id:"factor",name:"Factor Analysis",description:"Multivariate data designed for factor extraction and analysis",icon:e.jsx(Pt,{}),color:"#00bcd4",bgColor:he("#00bcd4",.1),handler:De,tags:["Multivariate","Factor","Dimension"],rows:300,variables:25},{id:"survival",name:"Survival Analysis",description:"Time-to-event data with censoring for survival modeling",icon:e.jsx(Ba,{}),color:"#795548",bgColor:he("#795548",.1),handler:re,tags:["Survival","Time-to-Event","Censoring"],rows:200,variables:8},{id:"mediation",name:"Mediation/Moderation",description:"Complex relationship modeling data for advanced statistical analysis",icon:e.jsx(Pt,{}),color:"#607d8b",bgColor:he("#607d8b",.1),handler:Ve,tags:["Mediation","Moderation","Relationships"],rows:350,variables:12},{id:"meta",name:"Meta Analysis",description:"Study-level data with effect sizes and confidence intervals for meta-analysis",icon:e.jsx(Pt,{}),color:"#3f51b5",bgColor:he("#3f51b5",.1),handler:Ue,tags:["Meta-Analysis","Effect Size","Studies"],rows:30,variables:12},{id:"cluster",name:"Cluster Analysis",description:"Customer segmentation data with multiple numeric variables for clustering",icon:e.jsx(sa,{}),color:"#ff5722",bgColor:he("#ff5722",.1),handler:Ze,tags:["Clustering","Segmentation","Multivariate"],rows:400,variables:8}],N=u.useCallback(R=>{R.preventDefault(),R.stopPropagation(),R.type==="dragenter"||R.type==="dragover"?U(!0):R.type==="dragleave"&&U(!1)},[]),se=u.useCallback(R=>{if(R.preventDefault(),R.stopPropagation(),U(!1),!C){D("Please login to import data. Guest users can only use sample datasets.");return}if(R.dataTransfer.files&&R.dataTransfer.files[0]){const ue=R.dataTransfer.files[0];$({target:{files:[ue]}})}},[C]),$=async R=>{var d,Y;const ue=(d=R.target.files)==null?void 0:d[0];if(!ue)return;if(!C){D("Please login to import data. Guest users can only use sample datasets."),a.current&&(a.current.value="");return}b(!0),D(null),K(null);const le=(Y=ue.name.split(".").pop())==null?void 0:Y.toLowerCase();if(le==="csv"||le==="txt"){const _=new FileReader;_.onload=we=>{var ne;const de=(ne=we.target)==null?void 0:ne.result;if(de){const ie=St(de);ie.rows.length>0?(oe(ie),Q(!0)):D("No valid data found in the selected file")}else D("Could not read file content.");b(!1)},_.onerror=()=>{D("Error reading file."),b(!1)},_.readAsText(ue)}else le==="xls"||le==="xlsx"||le==="json"?ee(ue):(D(`Unsupported file type: .${le}. Please use CSV, TXT, XLS, XLSX, or JSON.`),b(!1))},ee=async R=>{var ue;b(!0),D(null),K(null);try{let le;const d=(ue=R.name.split(".").pop())==null?void 0:ue.toLowerCase();if(d==="xls"||d==="xlsx")le=await Sn(R);else if(d==="json")le=await Mn(R);else throw new Error(`Unsupported file type for direct import: .${d}.`);const Y=await s(le);b(!1),typeof Y=="string"&&(Y==="added"||Y==="selected")&&(K(`Successfully imported "${le.name}" with ${le.data.length} rows!`),a.current&&(a.current.value=""),y&&y())}catch(le){D(`Failed to import data: ${le instanceof Error?le.message:String(le)}`),b(!1)}},G=async(R,ue,le)=>{Q(!1),b(!0),D(null),K(null);try{const d=X.rows.map(_=>{const we={};return R.forEach((de,ne)=>{const ie=de.createNew?de.pastedColumn:de.targetColumn;if(ie&&ie!=="new"){const Ne=_[ne];we[ie]=xe(Ne,de.dataType)}}),we}),Y=R.filter(_=>_.createNew).map(_=>({id:$e(),name:_.pastedColumn,type:_.dataType,role:i.NONE}));if(ue==="newDataset"){const _={id:$e(),name:le||"Imported File Data",description:`Imported from file on ${new Date().toLocaleDateString()}`,columns:Y,data:d,dateCreated:new Date,dateModified:new Date};await s(_),K(`Successfully imported ${d.length} rows as new dataset!`)}else{console.error("Dataset update/append logic needs to be integrated with useData context."),D("Dataset update/append logic not fully implemented yet."),b(!1);return}b(!1),a.current&&(a.current.value=""),y&&y()}catch(d){D(`Failed to import data after preview: ${d instanceof Error?d.message:String(d)}`),b(!1)}},xe=(R,ue)=>{if(!R||R==="")return null;switch(ue){case t.NUMERIC:const le=Number(R);return isNaN(le)?null:le;case t.BOOLEAN:const d=R.toLowerCase();return d==="true"||d==="1"||d==="yes";case t.DATE:const Y=new Date(R);return isNaN(Y.getTime())?null:Y;default:return R}},V=12345;async function te(){await ge("health",()=>Xn(V),"Health Dataset")}async function Ie(){await ge("credit",()=>Qn(V),"Credit Risk Dataset")}async function ae(){await ge("employee",()=>Jn(V),"Employee Satisfaction Dataset")}async function be(){await ge("student",()=>Yn(V),"Student Performance Dataset")}async function nt(){await ge("reliability",()=>Kn(V),"Reliability Analysis Dataset")}async function De(){await ge("factor",()=>Zn(V),"Factor Analysis Dataset")}async function re(){await ge("survival",()=>es(V),"Survival Analysis Dataset")}async function Ve(){await ge("mediation",()=>ts(V),"Mediation/Moderation Dataset")}async function Ue(){await ge("meta",()=>as(V),"Meta Analysis Dataset")}async function Ze(){await ge("cluster",()=>ns(V),"Cluster Analysis Dataset")}const ge=async(R,ue,le)=>{if(!O){D("Please login or continue as guest to access sample datasets.");return}M(R),D(null),K(null);try{const d=ue(),Y={...d,dateCreated:new Date,dateModified:new Date},_=await s(Y);M(null),typeof _=="string"&&(_==="added"||_==="selected")&&(K(`Successfully loaded "${le}" with ${d.data.length} rows!`),y&&y())}catch(d){D(`Failed to generate ${le}: ${d instanceof Error?d.message:String(d)}`),M(null)}},_e=()=>{A(),n("/app")},st=()=>{a.current&&a.current.click()};return e.jsxs(Lt,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(E,{mb:4,textAlign:"center",children:[e.jsx(m,{variant:"h4",gutterBottom:!0,color:"primary",fontWeight:"bold",children:"Import Your Data"}),e.jsx(m,{variant:"subtitle1",color:"textSecondary",maxWidth:"600px",mx:"auto",children:"Get started by importing your own data or exploring with our sample datasets"})]}),e.jsxs(W,{spacing:2,mb:3,children:[w&&e.jsx(It,{in:w,children:e.jsx(E,{children:e.jsx(Rt,{})})}),p&&e.jsx(It,{in:!!p,children:e.jsx(Ge,{severity:"error",onClose:()=>D(null),action:e.jsx(Ee,{size:"small",color:"inherit",onClick:()=>D(null),children:e.jsx(ra,{fontSize:"small"})}),children:p})}),J&&e.jsx(It,{in:!!J,children:e.jsx(Ge,{severity:"success",onClose:()=>K(null),action:e.jsx(Ee,{size:"small",color:"inherit",onClick:()=>K(null),children:e.jsx(ra,{fontSize:"small"})}),children:J})})]}),!c&&e.jsx(ke,{sx:{mb:4,borderRadius:2,overflow:"hidden"},children:e.jsx(E,{sx:{background:`linear-gradient(135deg, ${h.palette.primary.main}, ${h.palette.primary.dark})`,color:"white",p:3},children:e.jsxs(W,{direction:"row",alignItems:"center",spacing:2,children:[e.jsx(Ga,{fontSize:"large"}),e.jsxs(E,{flex:1,children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Get Full Access to All Features"}),e.jsx(m,{variant:"body2",sx:{opacity:.9},children:S?"You're currently exploring as a guest. Login to import your own data and save your work.":"Login to analyze your own data (from files or Google Sheets), or continue as a guest to explore the app with sample datasets only."})]}),e.jsxs(W,{direction:{xs:"column",sm:"row"},spacing:1,children:[e.jsx(B,{variant:"contained",color:"inherit",component:Tn,to:"/auth/login",startIcon:e.jsx(_a,{}),sx:{bgcolor:"white",color:h.palette.primary.main,"&:hover":{bgcolor:"rgba(255,255,255,0.9)"}},children:"Login"}),!S&&e.jsx(B,{variant:"outlined",color:"inherit",onClick:_e,sx:{borderColor:"white",color:"white","&:hover":{borderColor:"white",bgcolor:"rgba(255,255,255,0.1)"}},children:"Continue as Guest"})]})]})})}),e.jsxs(Z,{container:!0,spacing:3,children:[e.jsx(Z,{item:!0,xs:12,md:5,children:e.jsxs(W,{spacing:3,children:[e.jsxs(ke,{sx:{borderRadius:2,overflow:"hidden"},children:[e.jsx(Wt,{avatar:e.jsx(lt,{sx:{bgcolor:h.palette.primary.main},children:e.jsx(Ha,{})}),title:"Import from File",subheader:"Upload CSV, Excel, or JSON files"}),e.jsxs(Be,{children:[e.jsxs(E,{onDragEnter:N,onDragLeave:N,onDragOver:N,onDrop:se,sx:{border:`2px dashed ${T?h.palette.primary.main:h.palette.divider}`,borderRadius:2,p:4,textAlign:"center",bgcolor:T?he(h.palette.primary.main,.05):"background.default",cursor:C?"pointer":"not-allowed",transition:"all 0.3s ease",opacity:C?1:.5},onClick:C?st:void 0,children:[e.jsx("input",{ref:a,type:"file",accept:".csv,.txt,.xls,.xlsx,.json",style:{display:"none"},onChange:$}),e.jsx(Yt,{sx:{fontSize:48,color:"text.secondary",mb:2}}),e.jsx(m,{variant:"h6",gutterBottom:!0,children:T?"Drop your file here":"Drag & Drop or Click to Upload"}),e.jsx(m,{variant:"body2",color:"textSecondary",children:"Supported formats: CSV, TXT, XLS, XLSX, JSON"}),!C&&e.jsx(m,{variant:"caption",color:"error",display:"block",mt:1,children:"Login required for file upload"})]}),e.jsx(qa,{in:C,children:e.jsxs(E,{mt:3,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Import Options"}),e.jsxs(W,{spacing:1,children:[e.jsx(Le,{control:e.jsx(Pe,{checked:f.hasHeader,onChange:R=>P({...f,hasHeader:R.target.checked}),size:"small"}),label:e.jsx(m,{variant:"body2",children:"First row contains headers"})}),e.jsx(Le,{control:e.jsx(Pe,{checked:f.skipEmptyLines,onChange:R=>P({...f,skipEmptyLines:R.target.checked}),size:"small"}),label:e.jsx(m,{variant:"body2",children:"Skip empty lines"})}),e.jsx(Le,{control:e.jsx(Pe,{checked:f.dynamicTyping,onChange:R=>P({...f,dynamicTyping:R.target.checked}),size:"small"}),label:e.jsx(m,{variant:"body2",children:"Auto-detect data types"})})]})]})})]})]}),e.jsxs(ke,{sx:{borderRadius:2,overflow:"hidden",opacity:C?1:.6},children:[e.jsx(Wt,{avatar:e.jsx(lt,{sx:{bgcolor:h.palette.success.main},children:e.jsx(kt,{})}),title:"Import from Google Sheets",subheader:"Connect to your Google Sheets data"}),e.jsx(Be,{children:e.jsx(qn,{onImportSuccess:y,disabled:!C})})]})]})}),e.jsx(Z,{item:!0,xs:12,md:7,children:e.jsxs(ke,{sx:{borderRadius:2,overflow:"hidden"},children:[e.jsx(Wt,{avatar:e.jsx(lt,{sx:{bgcolor:h.palette.secondary.main},children:e.jsx(Xa,{})}),title:"Sample Datasets",subheader:"Pre-configured datasets for exploration and testing"}),e.jsxs(Be,{children:[e.jsx(Z,{container:!0,spacing:2,children:L.map(R=>e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsx(ke,{variant:"outlined",sx:{height:"100%",transition:"all 0.3s ease",cursor:"pointer",bgcolor:x===R.id?R.bgColor:"background.paper",borderColor:x===R.id?R.color:"divider","&:hover":{bgcolor:R.bgColor,borderColor:R.color,transform:"translateY(-2px)",boxShadow:2}},onClick:()=>!w&&!x&&R.handler(),children:e.jsx(Be,{children:e.jsxs(W,{spacing:2,children:[e.jsxs(E,{display:"flex",alignItems:"center",gap:2,children:[e.jsx(lt,{sx:{bgcolor:R.bgColor,color:R.color,width:48,height:48},children:R.icon}),e.jsxs(E,{flex:1,children:[e.jsx(m,{variant:"subtitle1",fontWeight:"bold",children:R.name}),e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsxs(m,{variant:"caption",color:"textSecondary",children:[R.rows," rows"]}),e.jsx(m,{variant:"caption",color:"textSecondary",children:"•"}),e.jsxs(m,{variant:"caption",color:"textSecondary",children:[R.variables," variables"]})]})]})]}),e.jsx(m,{variant:"body2",color:"textSecondary",children:R.description}),e.jsx(E,{display:"flex",flexWrap:"wrap",gap:.5,children:R.tags.map(ue=>e.jsx(Me,{label:ue,size:"small",sx:{bgcolor:he(R.color,.1),color:R.color,fontSize:"0.7rem",height:20}},ue))}),x===R.id&&e.jsx(Rt,{sx:{position:"absolute",bottom:0,left:0,right:0,bgcolor:"transparent","& .MuiLinearProgress-bar":{bgcolor:R.color}}})]})})})},R.id))}),!O&&e.jsx(E,{mt:3,p:2,bgcolor:he(h.palette.warning.main,.1),borderRadius:1,children:e.jsx(m,{variant:"body2",color:"warning.main",align:"center",children:"Login or continue as guest to access sample datasets"})})]})]})})]}),e.jsx(Da,{open:F,onClose:()=>Q(!1),pastedHeaders:X.headers,pastedRows:X.rows,existingColumns:[],onConfirm:G})]})},ls=()=>{const{datasets:y,currentDataset:s}=it(),{isGuest:a}=$t(),[h,n]=u.useState((s==null?void 0:s.id)||""),[c,S]=u.useState({includeHeaders:!0,selectedColumns:[],exportAllColumns:!0}),[O,C]=u.useState(null),A=y.find(x=>x.id===h),w=x=>{n(x.target.value),S({...c,selectedColumns:[]})},b=x=>{S(M=>{const T=M.selectedColumns.includes(x)?M.selectedColumns.filter(U=>U!==x):[...M.selectedColumns,x];return{...M,selectedColumns:T}})},p=x=>{S({...c,exportAllColumns:x.target.checked,selectedColumns:x.target.checked?[]:c.selectedColumns})},D=()=>{if(!A){C("No dataset selected for export");return}try{let x=A;if(!c.exportAllColumns&&c.selectedColumns.length>0){const Q=c.selectedColumns,X=A.columns.filter(oe=>Q.includes(oe.id)).map(oe=>oe.name);x={...A,data:A.data.map(oe=>{const f={};return X.forEach(P=>{f[P]=oe[P]}),f})}}const M=An(x),T=new Blob([M],{type:"text/csv;charset=utf-8;"}),U=URL.createObjectURL(T),F=document.createElement("a");F.href=U,F.setAttribute("download",`${A.name}.csv`),document.body.appendChild(F),F.click(),document.body.removeChild(F),C(null)}catch(x){C(`Export failed: ${x instanceof Error?x.message:String(x)}`)}},J=()=>{if(!A){C("No dataset selected for export");return}try{let x=A.data;if(!c.exportAllColumns&&c.selectedColumns.length>0){const Q=c.selectedColumns,X=A.columns.filter(oe=>Q.includes(oe.id)).map(oe=>oe.name);x=A.data.map(oe=>{const f={};return X.forEach(P=>{f[P]=oe[P]}),f})}const M=JSON.stringify(x,null,2),T=new Blob([M],{type:"application/json;charset=utf-8;"}),U=URL.createObjectURL(T),F=document.createElement("a");F.href=U,F.setAttribute("download",`${A.name}.json`),document.body.appendChild(F),F.click(),document.body.removeChild(F),C(null)}catch(x){C(`Export failed: ${x instanceof Error?x.message:String(x)}`)}},K=()=>{if(!A){C("No dataset selected for export");return}try{let x=A;if(!c.exportAllColumns&&c.selectedColumns.length>0){const M=A.columns.filter(T=>c.selectedColumns.includes(T.id)).map(T=>T.name);x={...A,columns:A.columns.filter(T=>c.selectedColumns.includes(T.id)),data:A.data.map(T=>{const U={};return M.forEach(F=>{U[F]=T[F]}),U})}}On(x,`${A.name}.xlsx`),C(null)}catch(x){C(`Export failed: ${x instanceof Error?x.message:String(x)}`)}};return e.jsxs(E,{p:3,children:[e.jsx(m,{variant:"h5",gutterBottom:!0,children:"Export Data"}),e.jsx(ke,{elevation:2,sx:{mb:4},children:e.jsxs(Be,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Select Dataset to Export"}),e.jsxs(Te,{fullWidth:!0,margin:"normal",children:[e.jsx(Fe,{id:"dataset-select-label",children:"Dataset"}),e.jsx(ze,{labelId:"dataset-select-label",id:"dataset-select",value:h,label:"Dataset",onChange:w,disabled:y.length===0,children:y.length===0?e.jsx(z,{value:"",disabled:!0,children:"No datasets available"}):y.map(x=>e.jsxs(z,{value:x.id,children:[x.name," (",x.data.length," rows, ",x.columns.length," columns)"]},x.id))})]}),a&&y.length>0&&e.jsx(Ge,{severity:"warning",sx:{mt:2,mb:2},children:"You are exploring as a Guest. Exported data reflects the current session only and is not saved permanently."}),A&&e.jsxs(e.Fragment,{children:[e.jsxs(E,{mt:3,children:[e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"Export Options"}),e.jsx(Le,{control:e.jsx(Pe,{checked:c.includeHeaders,onChange:x=>S({...c,includeHeaders:x.target.checked})}),label:"Include column headers"}),e.jsx(Le,{control:e.jsx(Pe,{checked:c.exportAllColumns,onChange:p}),label:"Export all columns"})]}),!c.exportAllColumns&&e.jsxs(E,{mt:2,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Select Columns to Export"}),e.jsx(E,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:A.columns.map(x=>e.jsx(Le,{control:e.jsx(Pe,{checked:c.selectedColumns.includes(x.id),onChange:()=>b(x.id),size:"small"}),label:x.name},x.id))})]}),e.jsx(mt,{sx:{my:3}}),e.jsxs(E,{sx:{display:"flex",gap:2},children:[e.jsx(B,{variant:"contained",color:"primary",startIcon:e.jsx(ya,{}),onClick:D,disabled:!A||!c.exportAllColumns&&c.selectedColumns.length===0,children:"Export as CSV"}),e.jsx(B,{variant:"outlined",color:"primary",startIcon:e.jsx(Qa,{}),onClick:J,disabled:!A||!c.exportAllColumns&&c.selectedColumns.length===0,children:"Export as JSON"}),e.jsx(B,{variant:"outlined",color:"success",startIcon:e.jsx(Ja,{}),onClick:K,disabled:!A||!c.exportAllColumns&&c.selectedColumns.length===0,children:"Export as Excel"})]})]}),y.length===0&&e.jsx(Ge,{severity:"info",sx:{mt:2},children:"No datasets available for export. Please import or create a dataset first."}),O&&e.jsx(Ge,{severity:"error",sx:{mt:2},children:O})]})})]})},ga=y=>{const s=[{value:"eq",label:"Equals"},{value:"neq",label:"Not Equals"},{value:"contains",label:"Contains"},{value:"startsWith",label:"Starts With"},{value:"endsWith",label:"Ends With"}],a=[{value:"isEmpty",label:"Is Empty"},{value:"isNotEmpty",label:"Is Not Empty"}];switch(y){case t.NUMERIC:case t.DATE:return[{value:"eq",label:"="},{value:"neq",label:"≠"},{value:"gt",label:">"},{value:"gte",label:"≥"},{value:"lt",label:"<"},{value:"lte",label:"≤"},...a];case t.BOOLEAN:return[{value:"eq",label:"Is"},{value:"neq",label:"Is Not"},...a];case t.CATEGORICAL:case t.TEXT:default:return[...s,...a]}},fa=y=>!["isEmpty","isNotEmpty"].includes(y),os=({onGoToVariableEditor:y})=>{const s=Nt(),a=Ca(s.breakpoints.down("sm")),{datasets:h,currentDataset:n,setCurrentDataset:c,updateDataset:S,addDataset:O,addRow:C,updateRow:A,removeRow:w,addColumn:b,updateColumn:p,removeColumn:D}=it(),[J,K]=u.useState(0),[x,M]=u.useState(10),[T,U]=u.useState(null),[F,Q]=u.useState([]),[X,oe]=u.useState(null),[f,P]=u.useState(null),[L,N]=u.useState("eq"),[se,$]=u.useState(""),[ee,G]=u.useState(null),[xe,V]=u.useState(!1),[te,Ie]=u.useState(!1),[ae,be]=u.useState({}),[nt,De]=u.useState(!1),[re,Ve]=u.useState({id:"",name:"",description:"",type:t.NUMERIC,role:i.NONE}),[Ue,Ze]=u.useState(!1),[ge,_e]=u.useState(!1),[st,R]=u.useState(!1),[ue,le]=u.useState({headers:[],rows:[]}),[d,Y]=u.useState(""),[_,we]=u.useState(new Set),[de,ne]=u.useState(null),[ie,Ne]=u.useState("comfortable"),me=u.useMemo(()=>{if(!n)return[];let o=n.data.map((I,k)=>({...I,originalIndex:k}));if(console.log("DataEditor: Recalculating displayedData"),console.log("Current searchTerm:",d),console.log("Initial dataToDisplay length:",o.length),d){const I=o.length;o=o.filter(k=>Object.values(k).some(v=>String(v).toLowerCase().includes(d.toLowerCase()))),console.log(`After search (term: "${d}"): ${o.length} rows (filtered from ${I})`)}if(F.length>0){const I=o.length,k=F.filter(v=>v.column&&v.operator&&(v.operator==="isEmpty"||v.operator==="isNotEmpty"||v.value!==""));k.length>0&&(o=Rn(o,k.map(v=>({column:v.column,operator:v.operator,value:v.operator==="isEmpty"||v.operator==="isNotEmpty"?void 0:v.value})))),console.log(`After filtering (${k.length} active filters): ${o.length} rows (filtered from ${I})`)}return T&&(o=kn(o,[T]),console.log(`After sorting (column: ${T.column}, direction: ${T.direction}): ${o.length} rows`)),console.log("Final displayedData length:",o.length),o},[n,T,F,d]),et=o=>{const I=o.target.value,k=h.find(v=>v.id===I);k&&(c(k),K(0),U(null),Q([]),He(),_e(!1),Y(""),we(new Set))},He=()=>{G(null),Ie(!1),be({})},l=(o,I)=>{K(I),He()},r=o=>{M(parseInt(o.target.value,10)),K(0),He()},j=o=>{let I="asc";T&&T.column===o&&T.direction==="asc"&&(I="desc"),U({column:o,direction:I}),K(0)},q=(o,I,k)=>{G({rowIndex:o,columnName:I,value:k})},pe=o=>{ee&&G({...ee,value:o})},Ae=()=>{G(null)},Oe=(o="none")=>{var I;if(ee&&n){const{rowIndex:k,columnName:v,value:H}=ee,ve=n.columns.find(Se=>Se.name===v);let fe=H;if(ve)switch(ve.type){case t.NUMERIC:fe=H===""||H===null||isNaN(Number(H))?null:Number(H);break;case t.BOOLEAN:typeof H=="string"&&(fe=H.toLowerCase()==="true"||H==="1");break;case t.DATE:H&&typeof H=="string"&&H.trim()!==""&&!isNaN(Date.parse(H))?fe=new Date(H):H instanceof Date&&!isNaN(H.getTime())?fe=H:fe=null;break}const ye=(I=me[k])==null?void 0:I.originalIndex;ye!==void 0&&A(n.id,ye,{[v]:fe}),G(null)}},ht=()=>{if(n){const o={};n.columns.forEach(I=>{o[I.name]=null}),be(o),Ie(!0)}},Ft=(o,I)=>{be(k=>({...k,[o]:I}))},Sa=()=>{if(n){const o={};n.columns.forEach(I=>{const k=ae[I.name];let v=k;switch(I.type){case t.NUMERIC:v=k===""||k===null||isNaN(Number(k))?null:Number(k);break;case t.BOOLEAN:typeof k=="string"&&(v=k.toLowerCase()==="true"||k==="1");break;case t.DATE:k&&!isNaN(Date.parse(k))?v=new Date(k):v=null;break}o[I.name]=v}),C(n.id,o),Ie(!1),He()}},Ma=o=>{var I;if(n){const k=(I=me[o])==null?void 0:I.originalIndex;k!==void 0&&w(n.id,k)}},Ta=()=>{n&&_.size>0&&window.confirm(`Delete ${_.size} selected row(s)?`)&&(Array.from(_).sort((k,v)=>v-k).forEach(k=>{w(n.id,k)}),we(new Set))},Aa=o=>{const I=new Set(_);I.has(o)?I.delete(o):I.add(o),we(I)},Oa=()=>{_.size===me.length?we(new Set):we(new Set(me.map(o=>o.originalIndex)))},Ra=o=>{var k;const I=(k=o.target.files)==null?void 0:k[0];if(I){const v=new FileReader;v.onload=H=>{var fe;const ve=(fe=H.target)==null?void 0:fe.result;if(ve){const ye=St(ve);ye.rows.length>0?(le(ye),R(!0)):alert("No valid data found in the selected file")}},v.readAsText(I)}o.target&&(o.target.value="")},ka=(o,I,k)=>{var v;if(!ee||ee.rowIndex!==I||ee.columnName!==k)if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(o.key)){o.preventDefault();let H=I,ve=k;const fe=(n==null?void 0:n.columns.findIndex(Se=>Se.name===k))??-1;o.key==="ArrowUp"&&(H=Math.max(0,I-1)),o.key==="ArrowDown"&&(H=Math.min(me.length-1,I+1)),o.key==="ArrowLeft"&&fe>0&&(ve=(n==null?void 0:n.columns[fe-1].name)??k),o.key==="ArrowRight"&&n&&fe<n.columns.length-1&&(ve=n.columns[fe+1].name);const ye=`cell-${H}-${ve}`;(v=document.getElementById(ye))==null||v.focus()}else(o.key==="Enter"||o.key==="F2")&&(o.preventDefault(),q(I,k,me[I][k]))},aa=o=>{switch(o){case t.NUMERIC:return e.jsx(jt,{fontSize:"small"});case t.TEXT:return e.jsx(yt,{fontSize:"small"});case t.BOOLEAN:return e.jsx(Ct,{fontSize:"small"});case t.DATE:return e.jsx(ft,{fontSize:"small"});case t.CATEGORICAL:return e.jsx(bt,{fontSize:"small"});case t.ORDINAL:return e.jsx(Ht,{fontSize:"small"});default:return e.jsx(dt,{fontSize:"small"})}},za=(o,I,k)=>{const v=o[I],H=n==null?void 0:n.columns.find(We=>We.name===I),ve=(H==null?void 0:H.type)||t.TEXT,fe=ee&&ee.rowIndex===k&&ee.columnName===I,ye=H?zn(v,H):{isMissing:!1};let Se=v;if(ye.isMissing)Se="";else switch(ve){case t.NUMERIC:Se=v==null||v===""?"":typeof v=="number"?Number.isInteger(v)?v:v.toFixed(2):String(v);break;case t.BOOLEAN:Se=v==null?"":v?"✓":"✗";break;case t.DATE:Se=v instanceof Date?v.toLocaleDateString():v;break;default:Se=v==null?"":String(v)}return fe?e.jsx(Ce,{value:(ee==null?void 0:ee.value)??"",onChange:We=>pe(We.target.value),size:"small",fullWidth:!0,variant:"standard",autoFocus:!0,onBlur:()=>Oe("none"),sx:{"& .MuiInput-root":{fontSize:ie==="compact"?"0.8rem":"0.875rem"}},onKeyDown:We=>{We.key==="Enter"?(We.preventDefault(),Oe("down")):We.key==="Tab"?(We.preventDefault(),Oe("right")):We.key==="Escape"&&(We.preventDefault(),Ae())}}):e.jsx(Re,{title:ye.isMissing?`Missing value${ye.matchedCode?` (${ye.matchedCode})`:""}`:"",placement:"top",arrow:!0,children:e.jsx(E,{sx:{display:"flex",alignItems:"center",width:"100%",height:"100%",cursor:"text",padding:ie==="compact"?"4px 8px":"8px 12px",fontSize:ie==="compact"?"0.8rem":"0.875rem",color:ye.isMissing?s.palette.text.disabled:ve===t.BOOLEAN?v?s.palette.success.main:s.palette.error.main:"inherit",fontWeight:ve===t.BOOLEAN?"bold":"normal",fontStyle:ye.isMissing?"italic":"normal",bgcolor:ye.isMissing?he(s.palette.warning.main,.05):"transparent",border:ye.isMissing?`1px dashed ${he(s.palette.warning.main,.3)}`:"none",borderRadius:ye.isMissing?1:0,"&:hover":{bgcolor:ye.isMissing?he(s.palette.warning.main,.1):he(s.palette.action.hover,.04)}},onClick:()=>q(k,I,v),children:ye.isMissing?e.jsx(E,{sx:{display:"flex",alignItems:"center",gap:.5},children:e.jsx(m,{variant:"caption",color:"text.disabled",children:"(missing)"})}):Se})})},Vt=F.filter(o=>o.column&&(o.value!==""||!fa(o.operator))).length;u.useEffect(()=>{const o=I=>{var H;const k=document.activeElement;if(k&&(k.tagName==="INPUT"||k.tagName==="TEXTAREA"))return;const v=(H=I.clipboardData)==null?void 0:H.getData("text");if(v&&n){I.preventDefault();const ve=St(v);ve.rows.length>0&&(le(ve),R(!0))}};return document.addEventListener("paste",o),()=>document.removeEventListener("paste",o)},[n]);const na=(o,I)=>{if(!o||o==="")return null;switch(I){case t.NUMERIC:const k=Number(o);return isNaN(k)?null:k;case t.BOOLEAN:const v=o.toLowerCase();return v==="true"||v==="1"||v==="yes";case t.DATE:const H=new Date(o);return isNaN(H.getTime())?null:H;default:return o}};return e.jsxs(Lt,{maxWidth:xe?!1:"xl",sx:{py:xe?0:3},children:[!xe&&e.jsx(ke,{sx:{mb:1,borderRadius:2},children:e.jsx(Be,{sx:{pt:.5,pb:.5},children:e.jsxs(Z,{container:!0,spacing:1,alignItems:"center",children:[e.jsx(Z,{item:!0,xs:12,md:6,children:e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{id:"dataset-select-label",children:"Select Dataset"}),e.jsx(ze,{labelId:"dataset-select-label",value:(n==null?void 0:n.id)||"",label:"Select Dataset",onChange:et,startAdornment:n&&e.jsx(gt,{position:"start",children:e.jsx(dt,{})}),children:h.length===0?e.jsx(z,{value:"",disabled:!0,children:"No datasets available"}):h.map(o=>e.jsx(z,{value:o.id,children:e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:o.name}),e.jsxs(m,{variant:"caption",color:"text.secondary",children:[o.data.length," rows × ",o.columns.length," columns"]})]})},o.id))})]})}),n&&e.jsx(Z,{item:!0,xs:12,md:6,children:e.jsxs(W,{direction:"row",spacing:1,flexWrap:"wrap",useFlexGap:!0,children:[e.jsx(Me,{icon:e.jsx(kt,{}),label:`${me.length} rows`,size:"small",color:d||F.length>0?"primary":"default"}),e.jsx(Me,{icon:e.jsx(Ya,{}),label:`${n.columns.length} columns`,size:"small"}),T&&e.jsx(Me,{icon:e.jsx(Ka,{}),label:`Sorted by ${T.column}`,size:"small",color:"secondary",onDelete:()=>U(null)}),Vt>0&&e.jsx(Me,{icon:e.jsx(Et,{}),label:`${Vt} filter${Vt>1?"s":""}`,size:"small",color:"secondary",onDelete:()=>Q([])})]})})]})})}),n?e.jsxs(vt,{elevation:xe?0:2,sx:{borderRadius:xe?0:2,overflow:"hidden"},children:[e.jsxs(E,{sx:{px:2,py:1.5,bgcolor:"background.paper",borderBottom:1,borderColor:"divider",boxShadow:"0 1px 3px rgba(0,0,0,0.1)"},children:[e.jsxs(W,{direction:{xs:"column",md:"row"},spacing:{xs:1.5,md:2},alignItems:{md:"center"},justifyContent:"space-between",children:[e.jsxs(W,{direction:"row",spacing:1.5,alignItems:"center",sx:{minWidth:0,flex:1},children:[e.jsx(Ce,{placeholder:"Search in data...",size:"small",value:d,onChange:o=>Y(o.target.value),InputProps:{startAdornment:e.jsx(gt,{position:"start",children:e.jsx(Dt,{fontSize:"small"})}),endAdornment:d&&e.jsx(gt,{position:"end",children:e.jsx(Ee,{size:"small",onClick:()=>Y(""),children:e.jsx(Kt,{fontSize:"small"})})})},sx:{flexGrow:1,maxWidth:{xs:"100%",md:280},"& .MuiOutlinedInput-root":{backgroundColor:"background.default"}}}),e.jsxs(ba,{value:ie,exclusive:!0,onChange:(o,I)=>I&&Ne(I),size:"small",sx:{display:{xs:"none",sm:"flex"}},children:[e.jsx(zt,{value:"comfortable",sx:{px:1.5},children:"Comfortable"}),e.jsx(zt,{value:"compact",sx:{px:1.5},children:"Compact"})]})]}),e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",sx:{flexShrink:0},children:[e.jsxs(la,{variant:"outlined",size:"small",children:[e.jsx(Re,{title:"Add new row",children:e.jsx(B,{startIcon:e.jsx(ot,{}),onClick:ht,sx:{px:1.5},children:"Row"})}),e.jsx(Re,{title:"Add new column",children:e.jsx(B,{startIcon:e.jsx(ot,{}),onClick:()=>{Ve({id:"",name:"",description:"",type:t.NUMERIC,role:i.NONE}),Ze(!1),De(!0)},sx:{px:1.5},children:"Column"})})]}),e.jsxs(la,{variant:"outlined",size:"small",children:[e.jsx(Re,{title:"Paste data from clipboard",children:e.jsx(B,{startIcon:e.jsx(Za,{}),onClick:()=>{navigator.clipboard.readText().then(o=>{if(o){const I=St(o);I.rows.length>0&&(le(I),R(!0))}}).catch(()=>{alert("Unable to read clipboard. Try using Ctrl+V/Cmd+V instead.")})},sx:{px:1.5},children:"Paste"})}),e.jsx("input",{accept:".csv,.tsv,.txt",style:{display:"none"},id:"import-file-button",type:"file",onChange:Ra}),e.jsx("label",{htmlFor:"import-file-button",children:e.jsx(Re,{title:"Import data from file",children:e.jsx(B,{component:"span",startIcon:e.jsx(en,{}),sx:{px:1.5},children:"Import"})})})]}),e.jsxs(W,{direction:"row",spacing:.5,alignItems:"center",children:[y&&e.jsx(Re,{title:"Manage variables",children:e.jsx(Ee,{size:"small",onClick:()=>y(n.id),sx:{border:1,borderColor:"divider","&:hover":{borderColor:"primary.main"}},children:e.jsx(tn,{fontSize:"small"})})}),e.jsx(Re,{title:xe?"Exit fullscreen":"Fullscreen",children:e.jsx(Ee,{size:"small",onClick:()=>V(!xe),sx:{border:1,borderColor:"divider","&:hover":{borderColor:"primary.main"}},children:xe?e.jsx(Zt,{fontSize:"small"}):e.jsx(ea,{fontSize:"small"})})})]})]})]}),_.size>0&&e.jsxs(Ge,{severity:"info",sx:{mt:1.5},action:e.jsx(B,{color:"inherit",size:"small",startIcon:e.jsx(tt,{}),onClick:Ta,children:"Delete Selected"}),children:[_.size," row",_.size>1?"s":""," selected"]})]}),e.jsx(Mt,{sx:{maxHeight:xe?"calc(100vh - 200px)":600,"& .MuiTableHead-root":{"& .MuiTableRow-root":{"& .MuiTableCell-root":{position:"sticky",top:0,zIndex:100,backgroundColor:s.palette.mode==="dark"?s.palette.grey[800]:s.palette.grey[100],borderBottom:`2px solid ${s.palette.divider}`,fontWeight:600,color:s.palette.text.primary,"&::after":{content:'""',position:"absolute",left:0,right:0,bottom:0,height:"1px",backgroundColor:s.palette.divider}}}}},children:e.jsxs(Tt,{stickyHeader:!0,size:ie==="compact"?"small":"medium",children:[e.jsx(At,{children:e.jsxs(rt,{children:[e.jsx(ce,{padding:"checkbox",children:e.jsx(Pe,{indeterminate:_.size>0&&_.size<me.length,checked:me.length>0&&_.size===me.length,onChange:Oa})}),e.jsx(ce,{children:"#"}),n.columns.map(o=>e.jsx(ce,{sx:{cursor:"pointer",minWidth:120,"&:hover":{backgroundColor:he(s.palette.action.hover,.2)}},onClick:()=>j(o.name),children:e.jsxs(W,{direction:"row",alignItems:"center",spacing:.5,children:[aa(o.type),e.jsxs(E,{flex:1,children:[e.jsx(m,{variant:"subtitle2",noWrap:!0,children:o.name}),![t.NUMERIC,t.CATEGORICAL,t.ORDINAL].includes(o.type)&&e.jsx(m,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:o.type})]}),(T==null?void 0:T.column)===o.name&&(T.direction==="asc"?e.jsx(va,{fontSize:"small",color:"primary"}):e.jsx(an,{fontSize:"small",color:"primary"})),e.jsx(Ee,{size:"small",onClick:I=>{var v;I.stopPropagation(),oe(I.currentTarget),P({name:o.name,type:o.type});const k=F.find(H=>H.column===o.name);if(k)N(k.operator),$(k.value);else{const H=ga(o.type);N(((v=H[0])==null?void 0:v.value)||"eq"),$("")}},children:e.jsx(_t,{variant:"dot",color:"secondary",invisible:!F.some(I=>I.column===o.name),children:e.jsx(Et,{fontSize:"small"})})})]})},o.id)),e.jsx(ce,{sx:{bgcolor:"inherit",fontWeight:"bold"},children:"Actions"})]})}),e.jsx(Ot,{children:me.slice(J*x,J*x+x).map((o,I)=>{const k=J*x+I,v=_.has(o.originalIndex);return e.jsxs(rt,{hover:!0,selected:v,sx:{"&:hover":{bgcolor:he(s.palette.action.hover,.04)}},children:[e.jsx(ce,{padding:"checkbox",children:e.jsx(Pe,{checked:v,onChange:()=>Aa(o.originalIndex)})}),e.jsx(ce,{sx:{fontWeight:500},children:k+1}),n.columns.map(H=>e.jsx(ce,{id:`cell-${k}-${H.name}`,tabIndex:0,onKeyDown:ve=>ka(ve,k,H.name),sx:{padding:0,"&:hover":{bgcolor:he(s.palette.action.hover,.08)},"&:focus":{outline:`2px solid ${s.palette.primary.main}`,outlineOffset:"-1px"}},children:za(o,H.name,k)},`${o.originalIndex}-${H.id}`)),e.jsx(ce,{children:e.jsx(Re,{title:"Delete Row",children:e.jsx(Ee,{size:"small",onClick:()=>Ma(k),color:"error",children:e.jsx(tt,{fontSize:"small"})})})})]},o.originalIndex)})})]})}),e.jsxs(E,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:2,borderTop:1,borderColor:"divider"},children:[e.jsxs(m,{variant:"body2",color:"text.secondary",children:["Showing ",J*x+1,"-",Math.min((J+1)*x,me.length)," of ",me.length," rows"]}),e.jsx(nn,{component:"div",count:me.length,rowsPerPage:x,page:J,onPageChange:l,onRowsPerPageChange:r,rowsPerPageOptions:[10,25,50,100],labelRowsPerPage:a?"Rows:":"Rows per page:"})]})]}):e.jsxs(ke,{sx:{textAlign:"center",py:8},children:[e.jsx(kt,{sx:{fontSize:80,color:"text.disabled",mb:2}}),e.jsx(m,{variant:"h5",gutterBottom:!0,color:"text.secondary",children:"No Dataset Selected"}),e.jsx(m,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Select a dataset from the dropdown above to start editing"})]}),e.jsxs(ta,{anchorEl:de,open:!!de,onClose:()=>ne(null),children:[e.jsxs(z,{onClick:()=>{if(T||F.length>0){const o=me.map(({originalIndex:k,...v})=>v),I={...n,id:$e(),name:`${n.name} (Processed)`,data:o,dateCreated:new Date,dateModified:new Date};O(I),c(I)}ne(null)},disabled:!T&&F.length===0,children:[e.jsx(Xe,{children:e.jsx(sn,{fontSize:"small"})}),e.jsx(qe,{children:"Save as New Dataset"})]}),e.jsxs(z,{onClick:()=>{U(null),Q([]),Y(""),ne(null)},children:[e.jsx(Xe,{children:e.jsx(rn,{fontSize:"small"})}),e.jsx(qe,{children:"Reset Filters & Sort"})]}),e.jsx(mt,{}),e.jsxs(z,{onClick:()=>{ne(null)},children:[e.jsx(Xe,{children:e.jsx(ya,{fontSize:"small"})}),e.jsx(qe,{children:"Export Data"})]})]}),e.jsx(ln,{open:!!X,anchorEl:X,onClose:()=>{oe(null),P(null)},anchorOrigin:{vertical:"bottom",horizontal:"left"},children:e.jsxs(E,{sx:{p:3,minWidth:320},children:[e.jsxs(m,{variant:"h6",gutterBottom:!0,children:["Filter: ",f==null?void 0:f.name]}),e.jsxs(Te,{fullWidth:!0,sx:{mb:2},children:[e.jsx(Fe,{id:"filter-operator-label",children:"Operator"}),e.jsx(ze,{labelId:"filter-operator-label",value:L,label:"Operator",onChange:o=>N(o.target.value),children:f&&ga(f.type).map(o=>e.jsx(z,{value:o.value,children:o.label},o.value))})]}),fa(L)&&e.jsx(Ce,{label:"Value",value:se,onChange:o=>$(o.target.value),fullWidth:!0,sx:{mb:2},type:(f==null?void 0:f.type)===t.NUMERIC?"number":"text"}),e.jsxs(W,{direction:"row",spacing:1,justifyContent:"flex-end",children:[e.jsx(B,{variant:"outlined",onClick:()=>{f&&Q(o=>o.filter(I=>I.column!==f.name)),oe(null)},children:"Clear"}),e.jsx(B,{variant:"contained",onClick:()=>{if(f){const o={id:$e(),column:f.name,operator:L,value:se,columnType:f.type};Q(I=>[...I.filter(v=>v.column!==f.name),o])}oe(null)},children:"Apply"})]})]})}),e.jsxs(Qe,{open:te,onClose:()=>Ie(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(Je,{children:"Add New Row"}),e.jsx(Ye,{dividers:!0,children:e.jsx(W,{spacing:2,sx:{mt:1},children:n==null?void 0:n.columns.map(o=>e.jsx(Ce,{label:o.name,value:ae[o.name]||"",onChange:I=>Ft(o.name,I.target.value),fullWidth:!0,type:o.type===t.NUMERIC?"number":"text",InputProps:{startAdornment:e.jsx(gt,{position:"start",children:aa(o.type)})},helperText:o.description||`Type: ${o.type}`},o.id))})}),e.jsxs(Ke,{sx:{p:3},children:[e.jsx(B,{onClick:()=>Ie(!1),children:"Cancel"}),e.jsx(B,{variant:"contained",onClick:Sa,children:"Add Row"})]})]}),e.jsxs(Qe,{open:nt,onClose:()=>De(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(Je,{children:Ue?"Edit Column":"Add New Column"}),e.jsx(Ye,{dividers:!0,children:e.jsxs(W,{spacing:3,sx:{mt:1},children:[e.jsx(Ce,{label:"Column Name",value:re.name,onChange:o=>Ve({...re,name:o.target.value}),fullWidth:!0,required:!0,autoFocus:!0}),e.jsx(Ce,{label:"Description (optional)",value:re.description,onChange:o=>Ve({...re,description:o.target.value}),fullWidth:!0,multiline:!0,rows:2}),e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{children:"Data Type"}),e.jsxs(ze,{value:re.type,label:"Data Type",onChange:o=>Ve({...re,type:o.target.value}),children:[e.jsx(z,{value:t.NUMERIC,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(jt,{fontSize:"small"}),e.jsx("span",{children:"Numeric"})]})}),e.jsx(z,{value:t.TEXT,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(yt,{fontSize:"small"}),e.jsx("span",{children:"Text"})]})}),e.jsx(z,{value:t.BOOLEAN,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(Ct,{fontSize:"small"}),e.jsx("span",{children:"Boolean"})]})}),e.jsx(z,{value:t.DATE,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(ft,{fontSize:"small"}),e.jsx("span",{children:"Date"})]})}),e.jsx(z,{value:t.CATEGORICAL,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(bt,{fontSize:"small"}),e.jsx("span",{children:"Categorical"})]})})]})]}),e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{children:"Variable Role"}),e.jsxs(ze,{value:re.role,label:"Variable Role",onChange:o=>Ve({...re,role:o.target.value}),children:[e.jsx(z,{value:i.NONE,children:"None"}),e.jsx(z,{value:i.INDEPENDENT,children:"Independent Variable"}),e.jsx(z,{value:i.DEPENDENT,children:"Dependent Variable"}),e.jsx(z,{value:i.COVARIATE,children:"Covariate"})]})]})]})}),e.jsxs(Ke,{sx:{p:3},children:[e.jsx(B,{onClick:()=>De(!1),children:"Cancel"}),e.jsx(B,{variant:"contained",onClick:()=>{if(!(!n||!re.name.trim())){if(Ue)p(n.id,{id:re.id,name:re.name.trim(),description:re.description.trim()||void 0,type:re.type,role:re.role});else{if(n.columns.some(o=>o.name.toLowerCase()===re.name.trim().toLowerCase())){alert(`A column named "${re.name.trim()}" already exists.`);return}b(n.id,{name:re.name.trim(),description:re.description.trim()||void 0,type:re.type,role:re.role})}De(!1)}},disabled:!re.name.trim(),children:Ue?"Save Changes":"Add Column"})]})]}),e.jsx(Da,{open:st,onClose:()=>R(!1),pastedHeaders:ue.headers,pastedRows:ue.rows,existingColumns:(n==null?void 0:n.columns)||[],onConfirm:async(o,I,k)=>{if(!(!n&&I!=="newDataset"))try{if(I==="newDataset"){const v=o.map(fe=>({id:$e(),name:fe.pastedColumn,type:fe.dataType,role:i.NONE})),H=ue.rows.map(fe=>{const ye={};return o.forEach((Se,We)=>{const xt=fe[We];ye[Se.pastedColumn]=na(xt,Se.dataType)}),ye}),ve={id:$e(),name:k||"Imported Dataset",description:`Imported from clipboard on ${new Date().toLocaleDateString()}`,columns:v,data:H,dateCreated:new Date,dateModified:new Date};await O(ve),c(ve)}else{const v={...n},H=[];o.forEach(fe=>{fe.createNew&&H.push({id:$e(),name:fe.pastedColumn,type:fe.dataType,role:i.NONE})}),H.length>0&&(v.columns=[...v.columns,...H]);const ve=ue.rows.map(fe=>{const ye={};return v.columns.forEach(Se=>{ye[Se.name]=null}),o.forEach((Se,We)=>{const xt=Se.createNew?Se.pastedColumn:Se.targetColumn;if(xt&&xt!=="new"){const La=fe[We],Ut=v.columns.find($a=>$a.name===xt);ye[xt]=na(La,(Ut==null?void 0:Ut.type)||Se.dataType)}}),ye});I==="replace"?v.data=ve:v.data=[...v.data,...ve],v.dateModified=new Date,S(v)}R(!1),le({headers:[],rows:[]})}catch(v){console.error("Error importing data:",v),alert("Error importing data. Please check your data format and try again.")}}})]})},is=()=>{const{datasets:y,currentDataset:s,updateDataset:a}=it(),[h,n]=u.useState(null),[c,S]=u.useState(null),[O,C]=u.useState(je.STANDARDIZE),[A,w]=u.useState(null),[b,p]=u.useState("new"),[D,J]=u.useState(""),[K,x]=u.useState(!1),[M,T]=u.useState([{from:"",to:""}]),[U,F]=u.useState(!1),[Q,X]=u.useState(10),[oe,f]=u.useState(!1),[P,L]=u.useState(5),[N,se]=u.useState(!1),[$,ee]=u.useState([]),[G,xe]=u.useState("sum"),V=y.find(d=>d.id===h),te=V==null?void 0:V.columns.find(d=>d.id===c),Ie=te==null?void 0:te.type,ae=te==null?void 0:te.name;u.useEffect(()=>{s?n(s.id):(n(null),S(null))},[s]),u.useEffect(()=>{V&&V.columns.length>0?(!te||!V.columns.some(d=>d.id===te.id))&&S(V.columns[0].id):S(null)},[V,te]),u.useEffect(()=>{J(be())},[ae,O]);const be=()=>ae?`${ae}_${O.toLowerCase()}`:"",De=(d=>{if(!d)return[];const Y=[je.STANDARDIZE,je.LOG,je.SQUARE_ROOT,je.BINNING,je.COMPUTE_VARIABLE],_=[je.RECODE,je.DUMMY_CODING];switch(d){case t.NUMERIC:return[...Y,..._];case t.CATEGORICAL:case t.TEXT:case t.BOOLEAN:return _;default:return[]}})(Ie);u.useEffect(()=>{Ie&&!De.includes(O)&&(De.includes(je.RECODE)?C(je.RECODE):De.length>0?C(De[0]):C(je.STANDARDIZE))},[Ie,De,O]);const re=d=>{n(d.target.value),S(null),w(null)},Ve=d=>{S(d.target.value),J(be()),w(null)},Ue=d=>{C(d.target.value),J(be())},Ze=d=>{p(d.target.value)},ge=async(d,Y,_,we)=>{if(!(!V||!te))try{const de=te,ne=de.name;if(b==="new"&&!D)throw new Error("New variable name is required");if(b==="new"&&V.columns.some(r=>r.name.toLowerCase()===D.toLowerCase()))throw new Error("Variable name already exists");let ie=V.data,Ne="",me={},et=t.NUMERIC;switch(d){case je.STANDARDIZE:{const pe=Pn(V.data,ne);Ne="Standardized (Z-Score)",me={},b==="new"?ie=V.data.map((Ae,Oe)=>({...Ae,[D]:pe[Oe][ne]})):ie=pe;break}case je.LOG:{const pe=Un(V.data,ne,Y);Ne=`Log (base ${Y})`,me={base:Y},b==="new"?ie=V.data.map((Ae,Oe)=>({...Ae,[D]:pe[Oe][ne]})):ie=pe;break}case je.SQUARE_ROOT:{const pe=V.data.map(Ae=>({...Ae,[ne]:Math.sqrt(Ae[ne])}));Ne="Square Root",me={},b==="new"?ie=V.data.map((Ae,Oe)=>({...Ae,[D]:pe[Oe][ne]})):ie=pe;break}case je.RECODE:{const pe=we.reduce((Oe,{from:ht,to:Ft})=>(ht&&(Oe[ht]=Ft),Oe),{}),Ae=Vn(V.data,ne,pe);Ne="Recode",me={mapping:pe},b==="new"?ie=V.data.map((Oe,ht)=>({...Oe,[D]:Ae[ht][ne]})):ie=Ae;break}case je.BINNING:{const pe=Fn(V.data,ne,_);Ne=`Binned into ${_} categories`,me={numBins:_},et=t.CATEGORICAL,b==="new"?ie=V.data.map((Ae,Oe)=>({...Ae,[D]:pe[Oe][ne]})):ie=pe;break}case je.DUMMY_CODING:const{newData:r,newColumns:j}=$n(V.data,ne);ie=r,Ne="Dummy Coded",me={originalColumn:ne};const q=[...V.columns,...j.map(pe=>({id:$e(),name:pe.name,type:t.NUMERIC,role:de.role,description:`Dummy variable for ${ne}`,transformations:[{id:$e(),name:Ne,type:je.DUMMY_CODING,parameters:me,original:de}]}))];a({...V,data:ie,columns:q,dateModified:new Date}),w("Successfully created dummy variables");return;case je.COMPUTE_VARIABLE:return}const He={id:$e(),name:b==="new"?D:ne,type:et,role:de.role,description:Ne,transformations:[{id:$e(),name:Ne,type:d,parameters:me,original:de}]},l=b==="new"?[...V.columns,He]:V.columns.map(r=>r.id===de.id?{...r,...He,transformations:[...r.transformations||[],He.transformations[0]]}:r);a({...V,data:ie,columns:l,dateModified:new Date}),w("Transformation applied successfully")}catch(de){w(`Error: ${de instanceof Error?de.message:String(de)}`)}},_e=()=>{if(!V||!te){w("Error: Please select a dataset and column.");return}switch(O){case je.RECODE:x(!0);break;case je.LOG:F(!0);break;case je.BINNING:f(!0);break;case je.COMPUTE_VARIABLE:se(!0);break;default:ge(O,Q,P,M);break}},st=()=>{ge(je.RECODE,Q,P,M),x(!1)},R=()=>{ge(je.LOG,Q,P,M),F(!1)},ue=()=>{ge(je.BINNING,Q,P,M),f(!1)},le=()=>{if(!V||$.length===0){w("Error: Please select at least one column for computation.");return}if(!D){w("Error: Please provide a name for the computed variable.");return}if(V.columns.some(d=>d.name.toLowerCase()===D.toLowerCase())){w("Error: Variable name already exists.");return}try{const d=Ln(V.data,$,G,D),Y={id:$e(),name:D,type:t.NUMERIC,role:(te==null?void 0:te.role)||i.NONE,description:`Computed variable using ${G} of: ${$.join(", ")}`,transformations:[{id:$e(),name:`Compute Variable (${G})`,type:je.COMPUTE_VARIABLE,parameters:{columns:$,method:G},original:te}]},_=[...V.columns,Y];a({...V,data:d,columns:_,dateModified:new Date}),w(`Successfully created computed variable: ${D}`),se(!1),ee([])}catch(d){w(`Error: ${d instanceof Error?d.message:String(d)}`)}};return e.jsxs(E,{p:4,children:[e.jsx(m,{variant:"h5",gutterBottom:!0,children:"Data Transformations"}),e.jsx(ke,{sx:{mb:3},children:e.jsx(Be,{children:e.jsxs(Z,{container:!0,spacing:3,children:[e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsxs(Te,{fullWidth:!0,variant:"outlined",children:[e.jsx(Fe,{id:"dataset-select-label",children:"Dataset"}),e.jsxs(ze,{labelId:"dataset-select-label",value:h||"",onChange:re,disabled:!y.length,children:[y.map(d=>e.jsxs(z,{value:d.id,children:[d.name," (",d.columns.length," columns)"]},d.id)),!y.length&&e.jsx(z,{disabled:!0,children:"No datasets available"})]})]})}),e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsxs(Te,{fullWidth:!0,variant:"outlined",children:[e.jsx(Fe,{id:"column-select-label",children:"Column"}),e.jsxs(ze,{labelId:"column-select-label",value:c||"",onChange:Ve,disabled:!V,children:[V==null?void 0:V.columns.map(d=>e.jsxs(z,{value:d.id,children:[d.name," (",d.type,")"]},d.id)),!V&&e.jsx(z,{disabled:!0,children:"Select dataset first"})]})]})})]})})}),te&&e.jsx(ke,{sx:{mb:3},children:e.jsxs(Be,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Column Details"}),e.jsxs(Z,{container:!0,spacing:2,children:[e.jsxs(Z,{item:!0,xs:12,sm:6,children:[e.jsxs(m,{children:["Name: ",ae]}),e.jsxs(m,{children:["Type: ",Ie]})]}),e.jsxs(Z,{item:!0,xs:12,sm:6,children:[e.jsxs(m,{children:["Role: ",te.role]}),e.jsxs(m,{children:["Description: ",te.description]})]})]}),te.transformations&&e.jsxs(E,{mt:2,children:[e.jsx(m,{children:"Applied Transformations:"}),e.jsx(qt,{dense:!0,children:te.transformations.map((d,Y)=>e.jsx(Xt,{children:e.jsx(qe,{primary:d.name,secondary:d.type})},Y))})]})]})}),e.jsx(ke,{children:e.jsxs(Be,{children:[e.jsxs(Z,{container:!0,spacing:3,children:[e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsxs(Te,{fullWidth:!0,variant:"outlined",children:[e.jsx(Fe,{id:"trans-type-label",children:"Transformation Type"}),e.jsxs(ze,{labelId:"trans-type-label",value:O,onChange:Ue,disabled:!te||De.length===0,children:[De.map(d=>e.jsx(z,{value:d,children:d.replace(/_/g," ")},d)),De.length===0&&e.jsx(z,{disabled:!0,children:"No applicable transformations"})]})]})}),e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsxs(Te,{component:"fieldset",children:[e.jsx(Ea,{component:"legend",children:"Output Target"}),e.jsxs(Jt,{row:!0,value:b,onChange:Ze,children:[e.jsx(Le,{value:"current",control:e.jsx(ut,{}),label:"Replace Current Column"}),e.jsx(Le,{value:"new",control:e.jsx(ut,{}),label:"Create New Variable"})]})]})}),b==="new"&&e.jsx(Z,{item:!0,xs:12,children:e.jsx(Ce,{label:"New Variable Name",value:D,onChange:d=>J(d.target.value),helperText:"Suggested name: ",InputProps:{endAdornment:e.jsx(Ee,{onClick:()=>J(be()),title:"Generate suggestion",children:e.jsx(on,{fontSize:"small"})})},fullWidth:!0})})]}),e.jsx(E,{mt:3,children:e.jsx(B,{variant:"contained",startIcon:e.jsx(wa,{}),onClick:_e,disabled:!te,children:"Apply Transformation"})})]})}),A&&e.jsx(Ge,{severity:A.startsWith("Error")?"error":"success",sx:{mt:3},children:A}),e.jsxs(Qe,{open:K,onClose:()=>x(!1),fullWidth:!0,maxWidth:"sm",children:[e.jsx(Je,{children:"Recode Values"}),e.jsxs(Ye,{children:[e.jsx(qt,{dense:!0,children:M.map((d,Y)=>e.jsxs(Xt,{children:[e.jsx(Ce,{label:"From",value:d.from,onChange:_=>{const we=[...M];we[Y].from=_.target.value,T(we)},sx:{mr:2,width:150}}),e.jsx(Ce,{label:"To",value:d.to,onChange:_=>{const we=[...M];we[Y].to=_.target.value,T(we)},sx:{width:150}}),e.jsx(Ee,{onClick:()=>{if(M.length>1){const _=M.filter((we,de)=>de!==Y);T(_)}},disabled:M.length<=1,children:e.jsx(tt,{})})]},Y))}),e.jsx(B,{variant:"text",startIcon:e.jsx(ot,{}),onClick:()=>T([...M,{from:"",to:""}]),children:"Add Mapping"})]}),e.jsxs(Ke,{children:[e.jsx(B,{onClick:()=>x(!1),children:"Cancel"}),e.jsx(B,{onClick:st,variant:"contained",color:"primary",children:"Apply Recode"})]})]}),e.jsxs(Qe,{open:U,onClose:()=>F(!1),fullWidth:!0,maxWidth:"sm",children:[e.jsx(Je,{children:"Log Transformation Settings"}),e.jsxs(Ye,{children:[e.jsx(Ce,{label:"Logarithm Base",type:"number",value:Q,onChange:d=>X(Number(d.target.value)),InputProps:{inputProps:{min:1}},fullWidth:!0}),e.jsx(m,{variant:"body2",color:"textSecondary",children:"This transformation requires positive values. Non-positive values will be set to null."})]}),e.jsxs(Ke,{children:[e.jsx(B,{onClick:()=>F(!1),children:"Cancel"}),e.jsx(B,{onClick:R,variant:"contained",color:"primary",children:"Apply Log Transform"})]})]}),e.jsxs(Qe,{open:oe,onClose:()=>f(!1),fullWidth:!0,maxWidth:"sm",children:[e.jsx(Je,{children:"Binning Configuration"}),e.jsxs(Ye,{children:[e.jsx(Ce,{label:"Number of Bins",type:"number",value:P,onChange:d=>L(Number(d.target.value)),InputProps:{inputProps:{min:2,max:20}},fullWidth:!0}),e.jsx(m,{variant:"body2",color:"textSecondary",children:"This will convert numeric values into categorical bins of equal width."})]}),e.jsxs(Ke,{children:[e.jsx(B,{onClick:()=>f(!1),children:"Cancel"}),e.jsx(B,{onClick:ue,variant:"contained",color:"primary",children:"Apply Binning"})]})]}),e.jsxs(Qe,{open:N,onClose:()=>se(!1),fullWidth:!0,maxWidth:"md",children:[e.jsx(Je,{children:"Compute Variable"}),e.jsx(Ye,{children:e.jsxs(Z,{container:!0,spacing:3,children:[e.jsx(Z,{item:!0,xs:12,children:e.jsx(m,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:"Create a new variable by computing values from existing numeric variables. Perfect for creating Likert scale totals, averages, and other aggregate measures."})}),e.jsx(Z,{item:!0,xs:12,children:e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{id:"columns-select-label",children:"Select Variables"}),e.jsx(ze,{labelId:"columns-select-label",multiple:!0,value:$,onChange:d=>ee(d.target.value),renderValue:d=>e.jsx(E,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:d.map(Y=>e.jsx(Me,{label:Y,size:"small"},Y))}),children:V==null?void 0:V.columns.filter(d=>d.type===t.NUMERIC).map(d=>e.jsx(z,{value:d.name,children:d.name},d.id))})]})}),e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{id:"method-select-label",children:"Computation Method"}),e.jsxs(ze,{labelId:"method-select-label",value:G,onChange:d=>xe(d.target.value),children:[e.jsx(z,{value:"sum",children:"Sum"}),e.jsx(z,{value:"mean",children:"Mean (Average)"}),e.jsx(z,{value:"count",children:"Count (Non-null values)"}),e.jsx(z,{value:"std",children:"Standard Deviation"}),e.jsx(z,{value:"min",children:"Minimum"}),e.jsx(z,{value:"max",children:"Maximum"})]})]})}),e.jsx(Z,{item:!0,xs:12,sm:6,children:e.jsx(Ce,{label:"New Variable Name",value:D,onChange:d=>J(d.target.value),fullWidth:!0,helperText:"Enter a name for the computed variable"})}),$.length>0&&e.jsx(Z,{item:!0,xs:12,children:e.jsxs(m,{variant:"body2",color:"textSecondary",children:[e.jsx("strong",{children:"Preview:"})," This will compute the ",G," of: ",$.join(", ")]})})]})}),e.jsxs(Ke,{children:[e.jsx(B,{onClick:()=>se(!1),children:"Cancel"}),e.jsx(B,{onClick:le,variant:"contained",color:"primary",disabled:$.length===0||!D,children:"Create Variable"})]})]})]})},cs=()=>{const{currentDataset:y,activeFilters:s,filterLogic:a,clearFilters:h,applyFilters:n,isFilteredDataset:c,originalDataset:S,getFilteredRowCount:O,getTotalRowCount:C}=it(),[A,w]=u.useState(!1),[b,p]=u.useState([]),[D,J]=u.useState("AND");u.useEffect(()=>{A&&(p(s.length>0?[...s]:[K()]),J(a))},[A,s,a]);const K=()=>({column:"",operator:"eq",value:""}),x=f=>{const P=S||y;if(!P)return[];const L=P.columns.find(se=>se.name===f);if(!L)return[];const N=[{value:"isEmpty",label:"Is Empty"},{value:"isNotEmpty",label:"Is Not Empty"}];switch(L.type){case t.NUMERIC:return[{value:"eq",label:"Equals"},{value:"neq",label:"Not Equals"},{value:"gt",label:"Greater Than"},{value:"gte",label:"Greater Than or Equal"},{value:"lt",label:"Less Than"},{value:"lte",label:"Less Than or Equal"},{value:"between",label:"Between"},{value:"notBetween",label:"Not Between"},...N];case t.CATEGORICAL:case t.TEXT:return[{value:"eq",label:"Equals"},{value:"neq",label:"Not Equals"},{value:"contains",label:"Contains"},{value:"startsWith",label:"Starts With"},{value:"endsWith",label:"Ends With"},{value:"in",label:"Is In List"},{value:"notIn",label:"Is Not In List"},...N];default:return[{value:"eq",label:"Equals"},{value:"neq",label:"Not Equals"},...N]}},M=()=>{p([...b,K()])},T=f=>{b.length>1&&p(b.filter((P,L)=>L!==f))},U=(f,P,L)=>{const N=[...b];N[f]={...N[f],[P]:L},P==="column"&&(N[f].operator="eq",N[f].value="",N[f].value2=void 0,N[f].values=void 0),p(N)},F=()=>{const f=b.filter(P=>{var L;return P.column&&P.operator&&(P.operator==="isEmpty"||P.operator==="isNotEmpty"||P.value!==""||((L=P.values)==null?void 0:L.length)||(P.operator==="between"||P.operator==="notBetween")&&P.value2!=="")});n(f,D),w(!1)},Q=()=>{h(),w(!1)},X=(f,P)=>{var $;if(f.operator==="isEmpty"||f.operator==="isNotEmpty")return null;if(f.operator==="between"||f.operator==="notBetween")return e.jsxs(Z,{container:!0,spacing:1,children:[e.jsx(Z,{item:!0,xs:6,children:e.jsx(Ce,{label:"From",type:"number",value:f.value||"",onChange:ee=>U(P,"value",ee.target.value),size:"small",fullWidth:!0})}),e.jsx(Z,{item:!0,xs:6,children:e.jsx(Ce,{label:"To",type:"number",value:f.value2||"",onChange:ee=>U(P,"value2",ee.target.value),size:"small",fullWidth:!0})})]});if(f.operator==="in"||f.operator==="notIn")return e.jsx(Ce,{label:"Values (comma-separated)",value:(($=f.values)==null?void 0:$.join(", "))||"",onChange:ee=>{const G=ee.target.value.split(",").map(xe=>xe.trim()).filter(xe=>xe);U(P,"values",G)},size:"small",fullWidth:!0,helperText:"Enter values separated by commas"});const L=S||y,N=L==null?void 0:L.columns.find(ee=>ee.name===f.column),se=(N==null?void 0:N.type)===t.NUMERIC?"number":"text";return e.jsx(Ce,{label:"Value",type:se,value:f.value||"",onChange:ee=>U(P,"value",ee.target.value),size:"small",fullWidth:!0})},oe=()=>{if(s.length===0)return"No filters applied";const f=S||y;return s.map(L=>{var ee,G;const N=f==null?void 0:f.columns.find(xe=>xe.name===L.column),se=((ee=x(L.column).find(xe=>xe.value===L.operator))==null?void 0:ee.label)||L.operator;let $="";return L.operator==="between"||L.operator==="notBetween"?$=` ${L.value} and ${L.value2}`:L.operator==="in"||L.operator==="notIn"?$=` [${(G=L.values)==null?void 0:G.join(", ")}]`:L.operator!=="isEmpty"&&L.operator!=="isNotEmpty"&&($=` "${L.value}"`),`${(N==null?void 0:N.name)||L.column} ${se}${$}`}).join(` ${a} `)};return y?e.jsxs(E,{children:[e.jsx(ke,{children:e.jsxs(Be,{children:[e.jsxs(E,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(m,{variant:"h6",children:"Data Filter"}),e.jsxs(W,{direction:"row",spacing:1,children:[e.jsx(B,{variant:"outlined",startIcon:e.jsx(Et,{}),onClick:()=>w(!0),children:s.length>0?"Edit Filters":"Add Filters"}),s.length>0&&e.jsx(B,{variant:"outlined",color:"secondary",startIcon:e.jsx(Kt,{}),onClick:h,children:"Clear All"})]})]}),e.jsx(E,{mb:2,children:e.jsx(m,{variant:"body2",color:"textSecondary",children:c?e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"Active Filters:"})," ",oe()]}):"No filters applied - showing all data"})}),e.jsxs(E,{display:"flex",alignItems:"center",gap:2,children:[e.jsx(Me,{icon:c?e.jsx(Na,{}):e.jsx(Qt,{}),label:`Showing ${O()} of ${C()} rows`,color:c?"primary":"default",variant:c?"filled":"outlined"}),c&&e.jsx(Me,{label:"Filtered Dataset Active",color:"warning",variant:"outlined",size:"small"})]})]})}),e.jsxs(Qe,{open:A,onClose:()=>w(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(Je,{children:"Configure Data Filters"}),e.jsxs(Ye,{children:[e.jsx(E,{mb:3,children:e.jsxs(Te,{component:"fieldset",children:[e.jsx(Ea,{component:"legend",children:"Logic Between Conditions"}),e.jsxs(Jt,{row:!0,value:D,onChange:f=>J(f.target.value),children:[e.jsx(Le,{value:"AND",control:e.jsx(ut,{}),label:"AND (all conditions must be true)"}),e.jsx(Le,{value:"OR",control:e.jsx(ut,{}),label:"OR (any condition can be true)"})]})]})}),e.jsx(mt,{sx:{mb:2}}),e.jsx(qt,{children:b.map((f,P)=>{var L;return e.jsx(Xt,{sx:{flexDirection:"column",alignItems:"stretch",mb:2},children:e.jsxs(Z,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(Z,{item:!0,xs:12,sm:3,children:e.jsxs(Te,{fullWidth:!0,size:"small",children:[e.jsx(Fe,{children:"Column"}),e.jsx(ze,{value:f.column,onChange:N=>U(P,"column",N.target.value),children:(L=S||y)==null?void 0:L.columns.map(N=>e.jsxs(z,{value:N.name,children:[N.name," (",N.type,")"]},N.id))})]})}),e.jsx(Z,{item:!0,xs:12,sm:3,children:e.jsxs(Te,{fullWidth:!0,size:"small",children:[e.jsx(Fe,{children:"Operator"}),e.jsx(ze,{value:f.operator,onChange:N=>U(P,"operator",N.target.value),disabled:!f.column,children:x(f.column).map(N=>e.jsx(z,{value:N.value,children:N.label},N.value))})]})}),e.jsx(Z,{item:!0,xs:12,sm:5,children:X(f,P)}),e.jsx(Z,{item:!0,xs:12,sm:1,children:e.jsx(Re,{title:"Remove filter",children:e.jsx(Ee,{onClick:()=>T(P),disabled:b.length<=1,size:"small",children:e.jsx(tt,{})})})})]})},P)})}),e.jsx(B,{startIcon:e.jsx(ot,{}),onClick:M,variant:"outlined",sx:{mt:1},children:"Add Filter Condition"})]}),e.jsxs(Ke,{children:[e.jsx(B,{onClick:()=>w(!1),children:"Cancel"}),e.jsx(B,{onClick:Q,color:"secondary",children:"Clear All Filters"}),e.jsx(B,{onClick:F,variant:"contained",color:"primary",children:"Apply Filters"})]})]})]}):e.jsx(ke,{children:e.jsxs(Be,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Data Filter"}),e.jsx(Ge,{severity:"info",children:"Please select a dataset to apply filters."})]})})};function ja(y){const{children:s,value:a,index:h,...n}=y;return e.jsx(It,{in:a===h,timeout:300,children:e.jsx("div",{role:"tabpanel",hidden:a!==h,id:`dataset-tabpanel-${h}`,"aria-labelledby":`dataset-tab-${h}`,...n,style:{width:"100%"},children:a===h&&e.jsx(E,{sx:{pt:3},children:s})})})}const ds=({onNavigateToImport:y,onNavigateToEditor:s})=>{const a=Nt(),{datasets:h,currentDataset:n,setCurrentDataset:c,removeDataset:S,addDataset:O,renameDataset:C,saveCurrentDatasetToAccount:A,deleteDatasetFromAccount:w}=it(),{user:b,canImportData:p,canAccessCloudStorage:D}=$t(),[J,K]=u.useState(!1),[x,M]=u.useState(""),[T,U]=u.useState(""),[F,Q]=u.useState(!1),[X,oe]=u.useState(""),[f,P]=u.useState(""),[L,N]=u.useState(!1),[se,$]=u.useState(""),[ee,G]=u.useState("success"),[xe,V]=u.useState(!1),[te,Ie]=u.useState(null),[ae,be]=u.useState(0),[nt,De]=u.useState(null),[re,Ve]=u.useState(null),[Ue,Ze]=u.useState({used:0,total:2*1024*1024,percentage:0}),ge=u.useMemo(()=>h.filter(r=>r.userId===(b==null?void 0:b.id)),[h,b==null?void 0:b.id]),_e=u.useMemo(()=>h.filter(r=>!r.userId),[h,b==null?void 0:b.id]);u.useEffect(()=>{if(ge.length>0){let r=0;ge.forEach(j=>{const q=JSON.stringify({data:j.data,variableInfo:j.columns});r+=new TextEncoder().encode(q).length}),Ze({used:r,total:2*1024*1024,percentage:Math.min(100,r/(2*1024*1024)*100)})}else Ze({used:0,total:2*1024*1024,percentage:0})},[ge]);const st=(r,j)=>{be(j)},R=r=>{const j=h.find(q=>q.id===r);j&&(c(j),$(`"${j.name}" is now the current dataset`),G("success"),N(!0))},ue=r=>{const j=h.find(q=>q.id===r);window.confirm(`Are you sure you want to delete "${j==null?void 0:j.name}"? This action cannot be undone.`)&&(S(r),$("Dataset deleted successfully"),G("success"),N(!0))},le=(r,j)=>{De(r.currentTarget),Ve(j)},d=()=>{De(null),Ve(null)},Y=()=>{M(""),U(""),K(!0)},_=()=>{K(!1)},we=r=>{const j=h.find(q=>q.id===r);j&&(oe(r),P(j.name),Q(!0))},de=()=>{Q(!1),oe(""),P("")},ne=async()=>{if(!f.trim()){$("Please enter a dataset name"),G("error"),N(!0);return}if(!X){$("No dataset selected for renaming"),G("error"),N(!0);return}const r=h.find(j=>j.id===X);if(!r){$("Dataset not found"),G("error"),N(!0);return}if(!p&&!r.userId){$("Guest users cannot rename datasets. Please login to rename datasets."),G("error"),N(!0);return}Ie(X);try{const j=await C(X,f.trim());j.success?(de(),$(j.message),G("success")):($(j.message),G("error")),N(!0)}catch(j){$(j.message||"Failed to rename dataset"),G("error"),N(!0)}finally{Ie(null)}},ie=async()=>{if(!p){$("Please login to create datasets. Guest users can only use sample datasets."),G("error"),N(!0);return}if(!x.trim()){$("Please enter a dataset name"),G("error"),N(!0);return}const r={id:$e(),name:x.trim(),description:T.trim()||`Created on ${new Date().toLocaleDateString()}`,data:[],columns:[],dateCreated:new Date,dateModified:new Date};try{await O(r),c(r),_(),$(`Dataset "${r.name}" created successfully`),G("success"),N(!0),s()}catch(j){console.error("Error creating new dataset:",j),$("Failed to create dataset"),G("error"),N(!0)}},Ne=async r=>{if(!b){$("Please login to save datasets to cloud"),G("error"),N(!0);return}if(!D){$("Cloud storage access requires a Pro account. Please upgrade to save datasets to cloud storage."),G("error"),N(!0);return}if(r){Ie(r.id),(n==null?void 0:n.id)!==r.id&&c(r),V(!0);try{const j=await A();j.success?($(`"${r.name}" moved to cloud successfully`),be(1)):$(j.message),G(j.success?"success":"error"),N(!0)}catch(j){$(j.message||"Failed to move dataset to cloud"),G("error"),N(!0)}finally{V(!1),Ie(null)}}},me=async r=>{if(!b){$("Please login to manage cloud datasets"),G("error"),N(!0);return}const j=h.find(q=>q.id===r);if(window.confirm(`Are you sure you want to delete "${j==null?void 0:j.name}" from your cloud account? This action cannot be undone.`)){Ie(r);try{const q=await w(r);$(q.message),G(q.success?"success":"error"),N(!0)}catch(q){$(q.message||"Failed to delete dataset"),G("error"),N(!0)}finally{Ie(null)}}},et=(r,j=2)=>{if(r===0)return"0 Bytes";const q=1024,pe=j<0?0:j,Ae=["Bytes","KB","MB","GB"],Oe=Math.floor(Math.log(r)/Math.log(q));return parseFloat((r/Math.pow(q,Oe)).toFixed(pe))+" "+Ae[Oe]},He=r=>new Date(r).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),l=(r,j)=>{const q=(n==null?void 0:n.id)===r.id,pe=te===r.id;return e.jsx(pn,{in:!0,timeout:300,children:e.jsx(Z,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(ke,{elevation:q?8:2,sx:{height:"100%",display:"flex",flexDirection:"column",position:"relative",bgcolor:q?he(a.palette.primary.main,.04):"background.paper",borderColor:q?"primary.main":"divider",borderWidth:q?2:1,borderStyle:"solid",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:a.shadows[8]}},children:[pe&&e.jsx(Rt,{sx:{position:"absolute",top:0,left:0,right:0,zIndex:1}}),e.jsxs(Be,{sx:{flexGrow:1,pb:1},children:[e.jsxs(E,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsxs(E,{sx:{flex:1,mr:1},children:[e.jsx(m,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:r.name}),e.jsxs(E,{sx:{display:"flex",gap:.5,mb:1},children:[e.jsx(Me,{size:"small",label:j?"Cloud":"Local",icon:j?e.jsx(ua,{}):e.jsx(gn,{}),color:j?"primary":"default",sx:{height:24}}),q&&e.jsx(Me,{size:"small",label:"Current",icon:e.jsx(ma,{}),color:"success",sx:{height:24}})]})]}),e.jsx(Ee,{size:"small",onClick:Ae=>le(Ae,r.id),disabled:pe,children:e.jsx(Ia,{})})]}),e.jsxs(W,{spacing:1.5,children:[e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(kt,{sx:{fontSize:18,color:"text.secondary"}}),e.jsxs(m,{variant:"body2",color:"text.secondary",children:[r.data.length," rows × ",r.columns.length," columns"]})]}),e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ft,{sx:{fontSize:18,color:"text.secondary"}}),e.jsxs(m,{variant:"body2",color:"text.secondary",children:["Modified ",He(r.dateModified)]})]}),r.description&&e.jsx(m,{variant:"body2",color:"text.secondary",sx:{overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",lineHeight:1.5,minHeight:"3em"},children:r.description})]})]}),e.jsx(fn,{sx:{p:2,pt:0},children:e.jsx(B,{size:"small",variant:q?"contained":"outlined",onClick:()=>q?s():R(r.id),disabled:pe,fullWidth:!0,startIcon:q?e.jsx(wt,{}):null,children:q?"Edit":"Set as Current"})})]})})},r.id)};return e.jsxs(Lt,{maxWidth:"lg",sx:{py:3},children:[e.jsx(Gn,{title:"My Datasets"}),e.jsxs(E,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4,flexWrap:"wrap",gap:2},children:[e.jsx(m,{variant:"body2",color:"text.secondary",children:"Manage your local and cloud datasets"}),e.jsxs(W,{direction:"row",spacing:2,children:[e.jsx(B,{variant:"outlined",color:"primary",startIcon:e.jsx(oa,{}),onClick:p?Y:void 0,disabled:!p,sx:{opacity:p?1:.5,cursor:p?"pointer":"not-allowed"},children:"Create New"}),e.jsx(B,{variant:"contained",startIcon:e.jsx(ia,{}),onClick:y,children:"Import Dataset"})]})]}),b&&e.jsx(ke,{sx:{mb:3,borderRadius:2,overflow:"hidden"},children:e.jsx(E,{sx:{background:`linear-gradient(135deg, ${a.palette.primary.main}, ${a.palette.primary.dark})`,color:"white",p:3},children:e.jsxs(Z,{container:!0,spacing:3,alignItems:"center",children:[e.jsx(Z,{item:!0,xs:12,md:6,children:e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(lt,{sx:{bgcolor:"rgba(255,255,255,0.2)",width:56,height:56},children:e.jsx(ca,{fontSize:"large"})}),e.jsxs(E,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Cloud Storage"}),e.jsxs(m,{variant:"body2",sx:{opacity:.9},children:[ge.length," of 2 datasets saved"]})]})]})}),e.jsx(Z,{item:!0,xs:12,md:6,children:e.jsxs(E,{children:[e.jsxs(E,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(m,{variant:"body2",children:"Storage Used"}),e.jsxs(m,{variant:"body2",fontWeight:"bold",children:[et(Ue.used)," / ",et(Ue.total)]})]}),e.jsx(Rt,{variant:"determinate",value:Ue.percentage,sx:{height:8,borderRadius:1,bgcolor:"rgba(255,255,255,0.2)","& .MuiLinearProgress-bar":{bgcolor:Ue.percentage>80?a.palette.error.main:"white"}}}),Ue.percentage>80&&e.jsxs(m,{variant:"caption",sx:{display:"flex",alignItems:"center",mt:.5},children:[e.jsx(cn,{sx:{fontSize:14,mr:.5}}),"Storage almost full"]})]})})]})})}),e.jsx(E,{sx:{borderBottom:1,borderColor:"divider",mb:3},children:e.jsxs(dn,{value:ae,onChange:st,"aria-label":"dataset tabs",sx:{"& .MuiTab-root":{textTransform:"none",fontSize:"1rem",fontWeight:500}},children:[e.jsx(da,{label:e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(un,{}),e.jsx("span",{children:"Local Datasets"}),e.jsx(_t,{badgeContent:_e.length,color:"default"})]})}),e.jsx(da,{label:e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ua,{}),e.jsx("span",{children:"Cloud Datasets"}),e.jsx(_t,{badgeContent:ge.length,color:"primary"})]}),disabled:!b})]})}),e.jsx(ja,{value:ae,index:0,children:_e.length===0?e.jsxs(E,{sx:{textAlign:"center",py:8},children:[e.jsx(mn,{sx:{fontSize:80,color:"text.disabled",mb:2}}),e.jsx(m,{variant:"h5",gutterBottom:!0,color:"text.secondary",children:"No Local Datasets"}),e.jsx(m,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Create a new dataset or import existing data to get started"}),e.jsxs(W,{direction:"row",spacing:2,justifyContent:"center",children:[e.jsx(B,{variant:"outlined",startIcon:e.jsx(oa,{}),onClick:p?Y:void 0,disabled:!p,sx:{opacity:p?1:.5,cursor:p?"pointer":"not-allowed"},children:"Create Dataset"}),e.jsx(B,{variant:"contained",startIcon:e.jsx(ia,{}),onClick:y,children:"Import Dataset"})]})]}):e.jsx(Z,{container:!0,spacing:3,children:_e.map(r=>l(r,!1))})}),e.jsx(ja,{value:ae,index:1,children:b?ge.length===0?e.jsxs(E,{sx:{textAlign:"center",py:8},children:[e.jsx(ca,{sx:{fontSize:80,color:"text.disabled",mb:2}}),e.jsx(m,{variant:"h5",gutterBottom:!0,color:"text.secondary",children:"No Cloud Datasets"}),e.jsx(m,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Save your local datasets to cloud for access across devices"}),_e.length>0&&e.jsx(B,{variant:"outlined",startIcon:e.jsx(va,{}),onClick:()=>be(0),children:"View Local Datasets"})]}):e.jsx(Z,{container:!0,spacing:3,children:ge.map(r=>l(r,!0))}):e.jsxs(E,{sx:{textAlign:"center",py:8},children:[e.jsx(hn,{sx:{fontSize:80,color:"text.disabled",mb:2}}),e.jsx(m,{variant:"h5",gutterBottom:!0,color:"text.secondary",children:"Login Required"}),e.jsx(m,{variant:"body1",color:"text.secondary",children:"Please login to access cloud storage features"})]})}),e.jsx(ta,{anchorEl:nt,open:!!nt,onClose:d,children:re&&(()=>{const r=h.find(q=>q.id===re),j=(r==null?void 0:r.userId)===(b==null?void 0:b.id);return e.jsxs(e.Fragment,{children:[e.jsxs(z,{onClick:()=>{R(re),d()},children:[e.jsx(Xe,{children:e.jsx(ma,{fontSize:"small"})}),e.jsx(qe,{children:"Set as Current"})]}),e.jsxs(z,{onClick:()=>{s(),d()},children:[e.jsx(Xe,{children:e.jsx(Na,{fontSize:"small"})}),e.jsx(qe,{children:"View/Edit"})]}),(p&&!(r!=null&&r.userId)||(r==null?void 0:r.userId)===(b==null?void 0:b.id))&&e.jsxs(z,{onClick:()=>{we(re),d()},children:[e.jsx(Xe,{children:e.jsx(wt,{fontSize:"small"})}),e.jsx(qe,{children:"Rename"})]}),!j&&b&&D&&ge.length<2&&e.jsxs(z,{onClick:()=>{r&&Ne(r),d()},children:[e.jsx(Xe,{children:e.jsx(Yt,{fontSize:"small"})}),e.jsx(qe,{children:"Move to Cloud"})]}),e.jsx(mt,{}),e.jsxs(z,{onClick:()=>{j?me(re):ue(re),d()},children:[e.jsx(Xe,{children:e.jsx(tt,{fontSize:"small",color:"error"})}),e.jsx(qe,{children:"Delete"})]})]})})()}),e.jsxs(Qe,{open:J,onClose:_,maxWidth:"sm",fullWidth:!0,children:[e.jsx(Je,{children:"Create New Dataset"}),e.jsx(Ye,{children:e.jsxs(W,{spacing:3,sx:{mt:1},children:[e.jsx(Ce,{autoFocus:!0,label:"Dataset Name",fullWidth:!0,variant:"outlined",value:x,onChange:r=>M(r.target.value),placeholder:"e.g., Survey Results 2024"}),e.jsx(Ce,{label:"Description (optional)",fullWidth:!0,variant:"outlined",multiline:!0,rows:2,value:T,onChange:r=>U(r.target.value),placeholder:"Brief description of your dataset..."})]})}),e.jsxs(Ke,{sx:{p:3,pt:0},children:[e.jsx(B,{onClick:_,children:"Cancel"}),e.jsx(B,{onClick:ie,variant:"contained",disabled:!x.trim(),children:"Create Dataset"})]})]}),e.jsxs(Qe,{open:F,onClose:de,maxWidth:"sm",fullWidth:!0,children:[e.jsx(Je,{children:"Rename Dataset"}),e.jsx(Ye,{children:e.jsx(W,{spacing:3,sx:{mt:1},children:e.jsx(Ce,{autoFocus:!0,label:"Dataset Name",fullWidth:!0,variant:"outlined",value:f,onChange:r=>P(r.target.value),placeholder:"Enter new dataset name",disabled:te===X})})}),e.jsxs(Ke,{sx:{p:3,pt:0},children:[e.jsx(B,{onClick:de,disabled:te===X,children:"Cancel"}),e.jsx(B,{onClick:ne,variant:"contained",disabled:!f.trim()||te===X,children:te===X?"Renaming...":"Rename Dataset"})]})]}),e.jsx(xn,{open:L,autoHideDuration:4e3,onClose:()=>N(!1),anchorOrigin:{vertical:"bottom",horizontal:"center"},children:e.jsx(Ge,{onClose:()=>N(!1),severity:ee,variant:"filled",elevation:6,children:se})})]})},us=({onGoToDataEditor:y})=>{const s=Nt();Ca(s.breakpoints.down("sm"));const{datasets:a,currentDataset:h,setCurrentDataset:n,addColumn:c,updateColumn:S,removeColumn:O}=it(),[C,A]=u.useState((h==null?void 0:h.id)||null),[w,b]=u.useState(!1),[p,D]=u.useState({id:"",name:"",description:"",type:t.NUMERIC,role:i.NONE,categoryOrder:[],missingValueCodes:[]}),[J,K]=u.useState(!1),[x,M]=u.useState(!1),[T,U]=u.useState(""),[F,Q]=u.useState("all"),[X,oe]=u.useState("all"),[f,P]=u.useState("table"),[L,N]=u.useState(new Set),[se,$]=u.useState(null),[ee,G]=u.useState(null),[xe,V]=u.useState(!1),[te,Ie]=u.useState(new Set),ae=a.find(l=>l.id===C),be=u.useMemo(()=>ae?ae.columns.filter(l=>!(T&&!l.name.toLowerCase().includes(T.toLowerCase())&&!(l.description&&l.description.toLowerCase().includes(T.toLowerCase()))||F!=="all"&&l.type!==F||X!=="all"&&l.role!==X||!xe&&te.has(l.id))):[],[ae,T,F,X,xe,te]),nt=()=>{M(!x)},De=l=>{A(l);const r=a.find(j=>j.id===l);n(r||null),D({id:"",name:"",description:"",type:t.NUMERIC,role:i.NONE,categoryOrder:[],missingValueCodes:[]}),K(!1),b(!1),U(""),Q("all"),oe("all"),N(new Set)},re=()=>{D({id:"",name:"",description:"",type:t.NUMERIC,role:i.NONE,categoryOrder:[],missingValueCodes:[]}),K(!1),b(!0)},Ve=l=>{if(ae){const r=ae.columns.find(j=>j.id===l);r&&(D({id:r.id,name:r.name,description:r.description||"",type:r.type,role:r.role,categoryOrder:r.categoryOrder||[],missingValueCodes:r.missingValueCodes||[]}),K(!0),b(!0))}},Ue=l=>{if(!ae)return[];const r=ae.data.map(j=>j[l]).filter(j=>j!=null&&j!=="").map(j=>String(j));return[...new Set(r)].sort()},Ze=()=>{const l=`Category ${p.categoryOrder.length+1}`;D(r=>({...r,categoryOrder:[...r.categoryOrder,l]}))},ge=l=>{D(r=>({...r,categoryOrder:r.categoryOrder.filter((j,q)=>q!==l)}))},_e=l=>{l!==0&&D(r=>{const j=[...r.categoryOrder];return[j[l-1],j[l]]=[j[l],j[l-1]],{...r,categoryOrder:j}})},st=l=>{l!==p.categoryOrder.length-1&&D(r=>{const j=[...r.categoryOrder];return[j[l],j[l+1]]=[j[l+1],j[l]],{...r,categoryOrder:j}})},R=(l,r)=>{D(j=>{const q=[...j.categoryOrder];return q[l]=r,{...j,categoryOrder:q}})},ue=()=>{if(!ae||!p.name)return;const l=Ue(p.name);D(r=>({...r,categoryOrder:l}))},le=()=>{if(!ae||!p.name.trim()){alert("Variable name cannot be empty.");return}if(J)S(ae.id,{id:p.id,name:p.name.trim(),description:p.description.trim()||void 0,type:p.type,role:p.role,categoryOrder:p.type===t.CATEGORICAL||p.type===t.ORDINAL?p.categoryOrder:void 0,missingValueCodes:p.missingValueCodes.length>0?p.missingValueCodes.filter(l=>l.trim()!==""):void 0});else{if(ae.columns.some(l=>l.name.toLowerCase()===p.name.trim().toLowerCase())){alert(`A variable named "${p.name.trim()}" already exists in this dataset.`);return}c(ae.id,{name:p.name.trim(),description:p.description.trim()||void 0,type:p.type,role:p.role,categoryOrder:p.type===t.CATEGORICAL||p.type===t.ORDINAL?p.categoryOrder:void 0,missingValueCodes:p.missingValueCodes.length>0?p.missingValueCodes.filter(l=>l.trim()!==""):void 0})}b(!1)},d=l=>{if(ae){const r=ae.columns.find(j=>j.id===l);window.confirm(`Are you sure you want to delete the variable "${r==null?void 0:r.name}"? This will remove all data in this column.`)&&O(ae.id,l)}},Y=()=>{ae&&L.size>0&&window.confirm(`Delete ${L.size} selected variable(s)? This will remove all associated data.`)&&(L.forEach(l=>{O(ae.id,l)}),N(new Set))},_=l=>{const r=new Set(L);r.has(l)?r.delete(l):r.add(l),N(r)},we=()=>{L.size===be.length?N(new Set):N(new Set(be.map(l=>l.id)))},de=l=>{switch(l){case t.NUMERIC:return e.jsx(jt,{fontSize:"small"});case t.TEXT:return e.jsx(yt,{fontSize:"small"});case t.BOOLEAN:return e.jsx(Ct,{fontSize:"small"});case t.DATE:return e.jsx(ft,{fontSize:"small"});case t.CATEGORICAL:return e.jsx(bt,{fontSize:"small"});case t.ORDINAL:return e.jsx(Ht,{fontSize:"small"});default:return e.jsx(dt,{fontSize:"small"})}},ne=l=>{switch(l){case t.NUMERIC:return s.palette.info.main;case t.TEXT:return s.palette.warning.main;case t.BOOLEAN:return s.palette.success.main;case t.DATE:return s.palette.error.main;case t.CATEGORICAL:case t.ORDINAL:return s.palette.secondary.main;default:return s.palette.text.secondary}},ie=l=>{switch(l){case i.INDEPENDENT:return e.jsx(xa,{fontSize:"small"});case i.DEPENDENT:return e.jsx(pa,{fontSize:"small"});case i.COVARIATE:return e.jsx(Gt,{fontSize:"small"});default:return null}},Ne=l=>{switch(l){case i.INDEPENDENT:return s.palette.primary.main;case i.DEPENDENT:return s.palette.secondary.main;case i.COVARIATE:return s.palette.warning.main;default:return s.palette.text.secondary}},me=l=>{switch(l){case t.NUMERIC:return"Numeric";case t.CATEGORICAL:return"Categorical";case t.ORDINAL:return"Ordinal";case t.DATE:return"Date";case t.TEXT:return"Text";case t.BOOLEAN:return"Boolean";default:return"Unknown"}},et=l=>{switch(l){case i.INDEPENDENT:return"Independent";case i.DEPENDENT:return"Dependent";case i.COVARIATE:return"Covariate";case i.NONE:return"None";default:return"Unknown"}},He=l=>{const r=L.has(l.id),j=te.has(l.id);return e.jsx(Z,{item:!0,xs:12,sm:6,md:4,children:e.jsx(ke,{variant:"outlined",sx:{position:"relative",borderColor:r?"primary.main":"divider",borderWidth:r?2:1,opacity:j?.6:1,transition:"all 0.2s ease","&:hover":{boxShadow:s.shadows[4],transform:"translateY(-2px)"}},children:e.jsxs(Be,{children:[e.jsxs(E,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Pe,{checked:r,onChange:()=>_(l.id),size:"small"}),e.jsx(lt,{sx:{bgcolor:he(ne(l.type),.1),color:ne(l.type),width:32,height:32},children:de(l.type)})]}),e.jsx(Ee,{size:"small",onClick:q=>{$(q.currentTarget),N(new Set([l.id]))},children:e.jsx(Ia,{})})]}),e.jsx(m,{variant:"h6",gutterBottom:!0,noWrap:!0,children:l.name}),e.jsxs(W,{direction:"row",spacing:1,mb:1,children:[e.jsx(Me,{label:me(l.type),size:"small",sx:{bgcolor:he(ne(l.type),.1),color:ne(l.type),fontWeight:500}}),l.role!==i.NONE&&e.jsx(Me,{icon:ie(l.role)||void 0,label:et(l.role),size:"small",sx:{bgcolor:he(Ne(l.role),.1),color:Ne(l.role),fontWeight:500}}),j&&e.jsx(Me,{icon:e.jsx(Qt,{}),label:"Hidden",size:"small",color:"default"})]}),l.description&&e.jsx(m,{variant:"body2",color:"text.secondary",sx:{overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:l.description})]})})},l.id)};return e.jsxs(Lt,{maxWidth:x?!1:"xl",sx:{py:x?0:2},children:[!x&&e.jsx(e.Fragment,{children:e.jsx(ke,{sx:{mb:2,borderRadius:2},children:e.jsx(Be,{sx:{py:1.5,"&:last-child":{pb:1.5}},children:e.jsxs(W,{direction:"row",spacing:1.5,alignItems:"center",children:[e.jsx(lt,{sx:{bgcolor:s.palette.primary.main,width:32,height:32},children:e.jsx(dt,{fontSize:"small"})}),e.jsxs(E,{sx:{flex:1,minWidth:0},children:[e.jsx(m,{variant:"subtitle2",fontWeight:"medium",sx:{mb:.5},children:"Dataset Variables"}),e.jsx(Hn,{value:C||"",onChange:De,label:"Select Dataset to Edit Variables"})]})]})})})}),ae?e.jsxs(ke,{elevation:x?0:2,sx:{borderRadius:x?0:2,overflow:"hidden"},children:[e.jsx(E,{sx:{px:2,py:1.5,bgcolor:"background.paper",borderBottom:1,borderColor:"divider",boxShadow:"0 1px 3px rgba(0,0,0,0.1)"},children:e.jsxs(W,{direction:{xs:"column",lg:"row"},spacing:{xs:1.5,lg:2},alignItems:{lg:"center"},justifyContent:"space-between",children:[e.jsx(W,{direction:{xs:"column",md:"row"},spacing:1.5,alignItems:{md:"center"},sx:{minWidth:0,flex:1},children:e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(m,{variant:"h6",noWrap:!0,children:ae.name}),e.jsxs(W,{direction:"row",spacing:.5,children:[e.jsx(Me,{icon:e.jsx(dt,{}),label:`${ae.columns.length} total`,size:"small",variant:"outlined"}),T||F!=="all"||X!=="all"?e.jsx(Me,{icon:e.jsx(Et,{}),label:`${be.length} shown`,size:"small",color:"primary",variant:"outlined"}):null,L.size>0&&e.jsx(Me,{label:`${L.size} selected`,size:"small",color:"secondary",onDelete:()=>N(new Set)})]})]})}),e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",sx:{flexShrink:0},children:[e.jsx(Ce,{placeholder:"Search variables...",size:"small",value:T,onChange:l=>U(l.target.value),InputProps:{startAdornment:e.jsx(gt,{position:"start",children:e.jsx(Dt,{fontSize:"small"})}),endAdornment:T&&e.jsx(gt,{position:"end",children:e.jsx(Ee,{size:"small",onClick:()=>U(""),children:e.jsx(Kt,{fontSize:"small"})})})},sx:{width:{xs:"100%",md:200},"& .MuiOutlinedInput-root":{backgroundColor:"background.default"}}}),e.jsxs(Te,{size:"small",sx:{minWidth:100},children:[e.jsx(Fe,{children:"Type"}),e.jsxs(ze,{value:F,label:"Type",onChange:l=>Q(l.target.value),children:[e.jsx(z,{value:"all",children:"All"}),e.jsx(mt,{}),e.jsx(z,{value:t.NUMERIC,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(jt,{fontSize:"small"}),e.jsx("span",{children:"Numeric"})]})}),e.jsx(z,{value:t.TEXT,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(yt,{fontSize:"small"}),e.jsx("span",{children:"Text"})]})}),e.jsx(z,{value:t.CATEGORICAL,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(bt,{fontSize:"small"}),e.jsx("span",{children:"Categorical"})]})}),e.jsx(z,{value:t.BOOLEAN,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(Ct,{fontSize:"small"}),e.jsx("span",{children:"Boolean"})]})}),e.jsx(z,{value:t.DATE,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(ft,{fontSize:"small"}),e.jsx("span",{children:"Date"})]})})]})]}),e.jsxs(Te,{size:"small",sx:{minWidth:100},children:[e.jsx(Fe,{children:"Role"}),e.jsxs(ze,{value:X,label:"Role",onChange:l=>oe(l.target.value),children:[e.jsx(z,{value:"all",children:"All"}),e.jsx(mt,{}),e.jsx(z,{value:i.NONE,children:"None"}),e.jsx(z,{value:i.INDEPENDENT,children:"Independent"}),e.jsx(z,{value:i.DEPENDENT,children:"Dependent"}),e.jsx(z,{value:i.COVARIATE,children:"Covariate"})]})]}),e.jsxs(W,{direction:"row",spacing:.5,alignItems:"center",children:[e.jsx(B,{variant:"contained",size:"small",startIcon:e.jsx(ot,{}),onClick:re,sx:{px:1.5},children:"Add"}),L.size>0&&e.jsx(Re,{title:`Delete ${L.size} selected variable${L.size>1?"s":""}`,children:e.jsx(Ee,{size:"small",color:"error",onClick:Y,sx:{border:1,borderColor:"error.main","&:hover":{borderColor:"error.dark"}},children:e.jsx(tt,{fontSize:"small"})})}),e.jsxs(ba,{value:f,exclusive:!0,onChange:(l,r)=>r&&P(r),size:"small",sx:{display:{xs:"none",sm:"flex"}},children:[e.jsx(zt,{value:"table",sx:{px:1},children:e.jsx(Re,{title:"Table view",children:e.jsx(jn,{fontSize:"small"})})}),e.jsx(zt,{value:"cards",sx:{px:1},children:e.jsx(Re,{title:"Card view",children:e.jsx(yn,{fontSize:"small"})})})]}),y&&e.jsx(Re,{title:"View data",children:e.jsx(Ee,{size:"small",onClick:()=>y(ae.id),sx:{border:1,borderColor:"divider","&:hover":{borderColor:"primary.main"}},children:e.jsx(Cn,{fontSize:"small"})})}),e.jsx(Re,{title:x?"Exit fullscreen":"Fullscreen",children:e.jsx(Ee,{size:"small",onClick:nt,sx:{border:1,borderColor:"divider","&:hover":{borderColor:"primary.main"}},children:x?e.jsx(Zt,{fontSize:"small"}):e.jsx(ea,{fontSize:"small"})})})]})]})]})}),e.jsx(E,{sx:{p:1.5},children:be.length===0?e.jsxs(E,{sx:{textAlign:"center",py:8},children:[e.jsx(dt,{sx:{fontSize:64,color:"text.disabled",mb:2}}),e.jsx(m,{variant:"h6",gutterBottom:!0,color:"text.secondary",children:"No Variables Found"}),e.jsx(m,{variant:"body2",color:"text.secondary",paragraph:!0,children:T||F!=="all"||X!=="all"?"Try adjusting your search or filters":"Add variables to define your dataset structure"}),ae.columns.length===0&&e.jsx(B,{variant:"contained",startIcon:e.jsx(ot,{}),onClick:re,sx:{mt:2},children:"Add First Variable"})]}):f==="table"?e.jsx(Mt,{sx:{maxHeight:x?"calc(100vh - 200px)":600,"& .MuiTableHead-root":{"& .MuiTableRow-root":{"& .MuiTableCell-root":{position:"sticky",top:0,zIndex:100,backgroundColor:s.palette.mode==="dark"?s.palette.grey[800]:s.palette.grey[100],borderBottom:`2px solid ${s.palette.divider}`,fontWeight:600,color:s.palette.text.primary,py:1}}}},children:e.jsxs(Tt,{stickyHeader:!0,size:"small",children:[e.jsx(At,{children:e.jsxs(rt,{children:[e.jsx(ce,{padding:"checkbox",sx:{width:48},children:e.jsx(Pe,{size:"small",indeterminate:L.size>0&&L.size<be.length,checked:be.length>0&&L.size===be.length,onChange:we})}),e.jsx(ce,{sx:{minWidth:120},children:"Name"}),e.jsx(ce,{sx:{width:100},children:"Type"}),e.jsx(ce,{sx:{width:120},children:"Role"}),e.jsx(ce,{children:"Description"}),e.jsx(ce,{align:"right",sx:{width:80},children:"Actions"})]})}),e.jsx(Ot,{children:be.map(l=>{const r=L.has(l.id),j=te.has(l.id);return e.jsxs(rt,{hover:!0,selected:r,sx:{opacity:j?.6:1},children:[e.jsx(ce,{padding:"checkbox",children:e.jsx(Pe,{size:"small",checked:r,onChange:()=>_(l.id)})}),e.jsx(ce,{sx:{py:1},children:e.jsxs(E,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(lt,{sx:{bgcolor:he(ne(l.type),.1),color:ne(l.type),width:24,height:24},children:de(l.type)}),e.jsx(m,{variant:"body2",fontWeight:500,noWrap:!0,children:l.name}),j&&e.jsx(Re,{title:"Hidden variable",children:e.jsx(Qt,{fontSize:"small",color:"disabled"})})]})}),e.jsx(ce,{sx:{py:1},children:e.jsx(Me,{label:me(l.type),size:"small",variant:"outlined",sx:{bgcolor:he(ne(l.type),.05),color:ne(l.type),borderColor:he(ne(l.type),.3),fontWeight:500,height:24}})}),e.jsx(ce,{sx:{py:1},children:l.role!==i.NONE?e.jsx(Me,{icon:ie(l.role)||void 0,label:et(l.role),size:"small",variant:"outlined",sx:{bgcolor:he(Ne(l.role),.05),color:Ne(l.role),borderColor:he(Ne(l.role),.3),fontWeight:500,height:24}}):e.jsx(m,{variant:"body2",color:"text.secondary",children:"-"})}),e.jsx(ce,{sx:{py:1},children:e.jsx(m,{variant:"body2",color:"text.secondary",noWrap:!0,children:l.description||"-"})}),e.jsx(ce,{align:"right",children:e.jsxs(W,{direction:"row",spacing:.5,justifyContent:"flex-end",children:[e.jsx(Re,{title:"Edit variable",children:e.jsx(Ee,{size:"small",onClick:()=>Ve(l.id),children:e.jsx(wt,{fontSize:"small"})})}),e.jsx(Re,{title:"Delete variable",children:e.jsx(Ee,{size:"small",onClick:()=>d(l.id),color:"error",children:e.jsx(tt,{fontSize:"small"})})})]})})]},l.id)})})]})}):e.jsx(Z,{container:!0,spacing:2,children:be.map(l=>He(l))})})]}):e.jsxs(ke,{sx:{textAlign:"center",py:8},children:[e.jsx(dt,{sx:{fontSize:80,color:"text.disabled",mb:2}}),e.jsx(m,{variant:"h5",gutterBottom:!0,color:"text.secondary",children:"No Dataset Selected"}),e.jsx(m,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Select a dataset above to view and manage its variables"})]}),e.jsxs(Qe,{open:w,onClose:()=>b(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(Je,{children:J?"Edit Variable":"Add New Variable"}),e.jsx(Ye,{dividers:!0,children:e.jsxs(W,{spacing:3,sx:{mt:1},children:[e.jsx(Ce,{label:"Variable Name",value:p.name,onChange:l=>D({...p,name:l.target.value}),fullWidth:!0,required:!0,autoFocus:!0,helperText:"Choose a descriptive name for your variable"}),e.jsx(Ce,{label:"Description",value:p.description,onChange:l=>D({...p,description:l.target.value}),fullWidth:!0,multiline:!0,rows:3,helperText:"Optional: Describe what this variable represents"}),e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{children:"Data Type"}),e.jsxs(ze,{value:p.type,label:"Data Type",onChange:l=>D({...p,type:l.target.value}),children:[e.jsx(z,{value:t.NUMERIC,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(jt,{fontSize:"small"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Numeric"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Numbers (integers or decimals)"})]})]})}),e.jsx(z,{value:t.TEXT,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(yt,{fontSize:"small"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Text"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Free-form text values"})]})]})}),e.jsx(z,{value:t.CATEGORICAL,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(bt,{fontSize:"small"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Categorical"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Distinct categories without order"})]})]})}),e.jsx(z,{value:t.ORDINAL,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(Ht,{fontSize:"small"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Ordinal"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Ordered categories (e.g., Low, Medium, High)"})]})]})}),e.jsx(z,{value:t.BOOLEAN,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(Ct,{fontSize:"small"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Boolean"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"True/False values"})]})]})}),e.jsx(z,{value:t.DATE,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(ft,{fontSize:"small"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Date"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Date and time values"})]})]})})]}),e.jsx(ha,{children:"Select the type of data this variable will contain"})]}),e.jsxs(Te,{fullWidth:!0,children:[e.jsx(Fe,{children:"Variable Role"}),e.jsxs(ze,{value:p.role,label:"Variable Role",onChange:l=>D({...p,role:l.target.value}),children:[e.jsx(z,{value:i.NONE,children:e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"None"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"No specific role in analysis"})]})}),e.jsx(z,{value:i.INDEPENDENT,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(xa,{fontSize:"small",color:"primary"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Independent Variable"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Predictor or input variable"})]})]})}),e.jsx(z,{value:i.DEPENDENT,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(pa,{fontSize:"small",color:"secondary"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Dependent Variable"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Outcome or target variable"})]})]})}),e.jsx(z,{value:i.COVARIATE,children:e.jsxs(W,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(Gt,{fontSize:"small",color:"warning"}),e.jsxs(E,{children:[e.jsx(m,{variant:"body1",children:"Covariate"}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Control variable in analysis"})]})]})})]}),e.jsx(ha,{children:"Define the role of this variable in your analysis"})]}),(p.type===t.CATEGORICAL||p.type===t.ORDINAL)&&e.jsxs(E,{sx:{mt:2},children:[e.jsxs(E,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(m,{variant:"subtitle1",fontWeight:"medium",children:"Category Order"}),e.jsxs(W,{direction:"row",spacing:1,children:[J&&e.jsx(B,{size:"small",variant:"outlined",onClick:ue,startIcon:e.jsx(Dt,{}),children:"Detect from Data"}),e.jsx(B,{size:"small",variant:"outlined",onClick:Ze,startIcon:e.jsx(ot,{}),children:"Add Category"})]})]}),e.jsx(m,{variant:"body2",color:"text.secondary",sx:{mb:2},children:p.type===t.ORDINAL?"Define the logical order of categories (e.g., Low → Medium → High)":"Optionally define a custom display order for categories"}),p.categoryOrder.length===0?e.jsx(E,{sx:{p:3,border:1,borderColor:"divider",borderRadius:1,textAlign:"center",bgcolor:"background.default"},children:e.jsx(m,{variant:"body2",color:"text.secondary",children:"No categories defined. Add categories manually or detect them from existing data."})}):e.jsx(E,{sx:{border:1,borderColor:"divider",borderRadius:1,bgcolor:"background.default",maxHeight:300,overflow:"auto"},children:p.categoryOrder.map((l,r)=>e.jsxs(E,{sx:{display:"flex",alignItems:"center",p:1.5,borderBottom:r<p.categoryOrder.length-1?1:0,borderColor:"divider","&:hover":{bgcolor:"action.hover"}},children:[e.jsx(bn,{sx:{color:"text.disabled",mr:1}}),e.jsxs(m,{variant:"body2",sx:{minWidth:24,textAlign:"center",color:"text.secondary",mr:1},children:[r+1,"."]}),e.jsx(Ce,{size:"small",value:l,onChange:j=>R(r,j.target.value),sx:{flex:1,mr:1},variant:"outlined"}),e.jsxs(W,{direction:"row",spacing:.5,children:[e.jsx(Ee,{size:"small",onClick:()=>_e(r),disabled:r===0,children:e.jsx(vn,{fontSize:"small"})}),e.jsx(Ee,{size:"small",onClick:()=>st(r),disabled:r===p.categoryOrder.length-1,children:e.jsx(En,{fontSize:"small"})}),e.jsx(Ee,{size:"small",onClick:()=>ge(r),color:"error",children:e.jsx(tt,{fontSize:"small"})})]})]},r))})]}),e.jsxs(E,{sx:{mt:2},children:[e.jsxs(E,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(m,{variant:"subtitle1",fontWeight:"medium",children:"Missing Value Codes"}),e.jsxs(W,{direction:"row",spacing:1,children:[J&&ae&&e.jsx(B,{size:"small",variant:"outlined",onClick:()=>{const l=Wn(ae.data,p.name);l.length>0&&D(r=>({...r,missingValueCodes:[...new Set([...r.missingValueCodes,...l])]}))},startIcon:e.jsx(Dt,{}),children:"Suggest from Data"}),e.jsx(B,{size:"small",variant:"outlined",onClick:()=>{D(l=>({...l,missingValueCodes:[...l.missingValueCodes,""]}))},startIcon:e.jsx(ot,{}),children:"Add Code"})]})]}),e.jsx(m,{variant:"body2",color:"text.secondary",sx:{mb:2},children:'Define values that should be treated as missing data (e.g., "na", "N.A.", "-", "999"). These values will be automatically excluded from statistical analyses.'}),p.missingValueCodes.length===0?e.jsx(E,{sx:{p:3,border:1,borderColor:"divider",borderRadius:1,textAlign:"center",bgcolor:"background.default"},children:e.jsx(m,{variant:"body2",color:"text.secondary",children:"No missing value codes defined. Values like null, undefined, and empty strings are always treated as missing."})}):e.jsx(E,{sx:{border:1,borderColor:"divider",borderRadius:1,bgcolor:"background.default",maxHeight:200,overflow:"auto"},children:p.missingValueCodes.map((l,r)=>e.jsxs(E,{sx:{p:2,borderBottom:r<p.missingValueCodes.length-1?1:0,borderColor:"divider",display:"flex",alignItems:"center",gap:2},children:[e.jsx(Ce,{value:l,onChange:j=>{const q=[...p.missingValueCodes];q[r]=j.target.value,D(pe=>({...pe,missingValueCodes:q}))},placeholder:"Enter missing value code",size:"small",fullWidth:!0,variant:"outlined"}),e.jsx(W,{direction:"row",spacing:.5,children:e.jsx(Ee,{size:"small",onClick:()=>{const j=p.missingValueCodes.filter((q,pe)=>pe!==r);D(q=>({...q,missingValueCodes:j}))},color:"error",children:e.jsx(tt,{fontSize:"small"})})})]},r))}),p.missingValueCodes.length>0&&(()=>{const l=Bn(p.missingValueCodes,p.type);return e.jsxs(e.Fragment,{children:[l.errors.length>0&&e.jsx(Ge,{severity:"error",sx:{mt:1},children:l.errors.join(", ")}),l.warnings.length>0&&e.jsx(Ge,{severity:"warning",sx:{mt:1},children:l.warnings.join(", ")})]})})()]})]})}),e.jsxs(Ke,{sx:{p:3},children:[e.jsx(B,{onClick:()=>b(!1),children:"Cancel"}),e.jsx(B,{onClick:le,variant:"contained",disabled:!p.name.trim(),children:J?"Save Changes":"Add Variable"})]})]}),e.jsxs(ta,{anchorEl:se,open:!!se,onClose:()=>$(null),children:[e.jsxs(z,{onClick:()=>{L.size===1&&Ve(Array.from(L)[0]),$(null)},children:[e.jsx(Xe,{children:e.jsx(wt,{fontSize:"small"})}),e.jsx(qe,{children:"Edit"})]}),e.jsxs(z,{onClick:()=>{$(null)},children:[e.jsx(Xe,{children:e.jsx(wn,{fontSize:"small"})}),e.jsx(qe,{children:"Duplicate"})]}),e.jsx(mt,{}),e.jsxs(z,{onClick:()=>{L.forEach(l=>{d(l)}),$(null)},children:[e.jsx(Xe,{children:e.jsx(tt,{fontSize:"small",color:"error"})}),e.jsx(qe,{children:"Delete"})]})]})]})},pt={import:0,datasets:1,editor:2,variables:3,filter:4,transform:5,export:6},Is=({initialTab:y="",onNavigate:s})=>{Nt();const[a,h]=u.useState(0),[n,c]=u.useState(!1);u.useEffect(()=>{y&&pt[y]!==void 0&&h(pt[y])},[y]);const S=()=>{c(!n)},O=(w,b)=>{h(b)},C=()=>{h(pt.import),s("/data-management/import")},A=[{label:"Import Data",icon:e.jsx(Yt,{})},{label:"Datasets",icon:e.jsx(Nn,{})},{label:"Data Editor",icon:e.jsx(wt,{})},{label:"Variable Editor",icon:e.jsx(In,{})},{label:"Data Filter",icon:e.jsx(Et,{})},{label:"Transform",icon:e.jsx(wa,{})},{label:"Export Data",icon:e.jsx(Bt,{})}];return e.jsx(E,{sx:{width:n?"100vw":"100%",height:n?"100vh":"100%",position:n?"fixed":"relative",top:n?0:"auto",left:n?0:"auto",zIndex:n?1300:"auto",bgcolor:"background.default",display:"flex",flexDirection:"column",p:n?2:0},children:e.jsxs(vt,{elevation:1,sx:{width:"100%",flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},children:[e.jsxs(E,{sx:{borderBottom:1,borderColor:"divider",bgcolor:"background.paper",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(_n,{items:A,value:a,onChange:O,customVariant:"card",scrollable:!0}),e.jsx(Re,{title:n?"Exit Full View":"Enter Full View",children:e.jsx(Ee,{onClick:S,size:"large",sx:{mr:1},children:n?e.jsx(Zt,{}):e.jsx(ea,{})})})]}),e.jsxs(E,{sx:{flex:1,overflow:"hidden",display:"flex",flexDirection:"column"},children:[e.jsx(ct,{value:a,index:0,children:e.jsx(rs,{onImportSuccess:()=>h(pt.datasets)})}),e.jsx(ct,{value:a,index:1,children:e.jsx(ds,{onNavigateToImport:C,onNavigateToEditor:()=>s("data-management/editor")})}),e.jsx(ct,{value:a,index:2,children:e.jsx(os,{onGoToVariableEditor:()=>h(pt.variables)})}),e.jsx(ct,{value:a,index:3,children:e.jsx(us,{onGoToDataEditor:()=>h(pt.editor)})}),e.jsx(ct,{value:a,index:4,children:e.jsx(cs,{})}),e.jsx(ct,{value:a,index:5,children:e.jsx(is,{})}),e.jsx(ct,{value:a,index:6,children:e.jsx(ls,{})})]})]})})};export{Is as default};
