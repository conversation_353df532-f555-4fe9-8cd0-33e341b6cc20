import{u as ht,j as e,B as b,e as s,R as K,G as x,ai as ge,b9 as fe,ba as be,bb as q,c8 as Lt,aj as ye,bc as Ne,f as Ke,c4 as Ft,ae as Ct,g as ut,h as ke,bq as qe,aF as He,i as le,aE as Nt,dg as zt,ao as nt,ap as st,aq as ot,ar as ue,as as p,at as lt,D as Wt,a2 as xt,aX as Et,bt as rt,aP as yt,ad as ct,Z as Ue,$ as pt,ah as kt,am as Mt,bS as Bt,bT as Ot,bU as _t,a_ as qt,ak as $t,bV as Xt,a6 as Pt,a7 as _e,ch as mt,d3 as Gt}from"./mui-libs-CfwFIaTD.js";import{r as L,b as Ut}from"./react-libs-Cr2nE3UY.js";import{a as St,D as P,g as Vt,V as At}from"./index-Bpan7Tbe.js";import{j as It}from"./other-utils-CR9xr_gI.js";import{f as Kt}from"./descriptive-Djo0s6H4.js";import"./math-setup-BTRs7Kau.js";import{S as H}from"./StatsCard-op8tGQ0a.js";import{m as Ht,a as Yt}from"./regression-BKqSTW2N.js";import{R as Ye,V as ft,C as Qe,X as Je,Y as Xe,T as Ze,L as dt,W as gt,e as at,c as Tt,b as Qt,N as bt,a as Jt}from"./charts-recharts-d3-BEF1Y_jn.js";import{c as Zt,a as ei}from"./math-lib-BOZ-XUok.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";const Rt=(z,g)=>{if(z.length!==g.length)throw new Error("Arrays must have the same length");if(z.length<3)throw new Error("Sample size must be at least 3");const te=z.length,a=It.corrcoeff(z,g),Ce=a*Math.sqrt((te-2)/(1-a*a)),Me=2*(1-It.studentt.cdf(Math.abs(Ce),te-2)),G=a*a;return{r:a,pValue:Me,n:te,rSquared:G}},ti=({value:z,pValue:g,significant:te,colorScale:a,sampleSize:Ce,var1Name:Me,var2Name:G})=>{const Ie=ht(),J=z.toFixed(3),Ve=g<.001?"< 0.001":g.toFixed(3),Ee=a(z),We=Math.abs(z)>.6?"white":Ie.palette.text.primary,re=ce=>{const we=Math.abs(ce);return we<.1?"Negligible":we<.3?"Weak":we<.5?"Moderate":we<.7?"Strong":"Very Strong"},Re=e.jsxs(b,{children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,children:[Me," × ",G]}),e.jsx(Wt,{sx:{my:1}}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Correlation:"})," ",J]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Strength:"})," ",re(z)," ",z<0?"Negative":"Positive"]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"p-value:"})," ",Ve]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Sample Size:"})," n = ",Ce]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Significance:"})," ",te?"Significant":"Not Significant"]})]});return e.jsx(Nt,{title:Re,arrow:!0,placement:"top",children:e.jsxs(b,{sx:{backgroundColor:Ee,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",p:1,height:"100%",width:"100%",color:We,position:"relative",cursor:"pointer",transition:"all 0.2s ease-in-out",border:`1px solid ${le(Ie.palette.divider,.1)}`,borderRadius:1,"&:hover":{transform:"scale(1.05)",boxShadow:Ie.shadows[4],zIndex:10}},children:[e.jsx(s,{variant:"body2",fontWeight:te?"bold":"normal",sx:{fontSize:"0.875rem"},children:J}),te&&e.jsx(s,{variant:"caption",sx:{fontSize:"0.6rem",color:We,opacity:.9},children:g<.001?"***":g<.01?"**":g<.05?"*":""})]})})},ii=()=>{const{datasets:z,currentDataset:g,setCurrentDataset:te}=St(),a=ht(),[Ce,Me]=L.useState((g==null?void 0:g.id)||""),[G,Ie]=L.useState([]),[J,Ve]=L.useState("pearson"),[Ee,We]=L.useState({showPValues:!1,showSignificanceStars:!0,highlightSignificant:!0,showHistograms:!1}),[re,Re]=L.useState(!1),[ce,we]=L.useState(null),[A,_]=L.useState(null);L.useEffect(()=>{const c=localStorage.getItem("correlation_matrix_results");if(c)try{_(JSON.parse(c))}catch($){console.error("Error parsing saved correlation matrix results:",$)}},[]);const ie=(g==null?void 0:g.columns.filter(c=>c.type===P.NUMERIC))||[],et=c=>{const $=c.target.value;Me($),Ie([]),_(null),localStorage.removeItem("correlation_matrix_results");const m=z.find(W=>W.id===$);m&&te(m)},Ae=c=>{const $=c.target.value;Ie(typeof $=="string"?[$]:$),_(null),localStorage.removeItem("correlation_matrix_results")},Pe=c=>{Ve(c.target.value),_(null),localStorage.removeItem("correlation_matrix_results")},Te=c=>{We($=>({...$,[c]:!$[c]}))},X=c=>{const $=a.palette.mode==="dark";return c===1?$?"#1565c0":"#0d47a1":c>.7?$?"#1976d2":"#1565c0":c>.5?$?"#42a5f5":"#1976d2":c>.3?$?"#64b5f6":"#42a5f5":c>.1?$?"#90caf9":"#64b5f6":c>-.1?$?"#424242":"#f5f5f5":c>-.3?$?"#ff8a65":"#ffab91":c>-.5?$?"#ff7043":"#ff8a65":c>-.7?$?"#ff5722":"#ff7043":c>-1?$?"#e64a19":"#ff5722":$?"#d84315":"#e64a19"},t=()=>{if(!g||G.length<2){we("Please select at least 2 variables for correlation analysis.");return}Re(!0),we(null);try{const c=G.map(M=>{const w=g.columns.find(N=>N.id===M);if(!w)throw new Error(`Column with ID ${M} not found.`);return w}),$={};c.forEach(M=>{const w=g.data.map(E=>E[M.name]),N=Kt(w);$[M.id]=N});const m={};c.forEach(M=>{m[M.id]={},c.forEach(w=>{if(M.id===w.id){m[M.id][w.id]={correlation:1,pValue:1,n:$[M.id].length};return}const N=[];if(g.data.forEach(E=>{const ae=E[M.name],me=E[w.name];typeof ae=="number"&&!isNaN(ae)&&typeof me=="number"&&!isNaN(me)&&N.push({x:ae,y:me})}),N.length<3){m[M.id][w.id]={correlation:null,pValue:null,n:N.length,error:"Not enough valid paired data"};return}if(J==="pearson")try{const E=Rt(N.map(ae=>ae.x),N.map(ae=>ae.y));m[M.id][w.id]={correlation:E.r,pValue:E.pValue,n:E.n,rSquared:E.rSquared}}catch(E){m[M.id][w.id]={correlation:null,pValue:null,n:N.length,error:E instanceof Error?E.message:String(E)}}else if(J==="spearman")try{const E=N.length,ae=[...N].sort((i,l)=>i.x-l.x),me={};ae.forEach((i,l)=>{let h=l;for(;h<E-1&&ae[h].x===ae[h+1].x;)h++;const k=(l+h)/2+1;for(let F=l;F<=h;F++)me[ae[F].x]=k;l=h});const xe=[...N].sort((i,l)=>i.y-l.y),De={};xe.forEach((i,l)=>{let h=l;for(;h<E-1&&xe[h].y===xe[h+1].y;)h++;const k=(l+h)/2+1;for(let F=l;F<=h;F++)De[xe[F].y]=k;l=h});const Le=N.map(i=>me[i.x]),Fe=N.map(i=>De[i.y]),Se=Rt(Le,Fe);m[M.id][w.id]={correlation:Se.r,pValue:Se.pValue,n:Se.n,rSquared:Se.rSquared}}catch(E){m[M.id][w.id]={correlation:null,pValue:null,n:N.length,error:E instanceof Error?E.message:String(E)}}})});const W={};c.forEach(M=>{W[M.id]={},c.forEach(w=>{if(M.id===w.id){const N=$[M.id],E=Math.min(...N),me=Math.max(...N)-E,xe=Math.min(20,Math.max(5,Math.ceil(Math.sqrt(N.length)))),De=me/xe,Le=[];for(let Fe=0;Fe<xe;Fe++){const Se=E+Fe*De,i=E+(Fe+1)*De,l=N.filter(h=>h>=Se&&(Fe===xe-1?h<=i:h<i)).length;Le.push({bin:`${Se.toFixed(1)}-${i.toFixed(1)}`,binMiddle:(Se+i)/2,count:l,frequency:l/N.length})}W[M.id][w.id]=Le}else{const N=g.data.map(E=>({x:E[M.name],y:E[w.name]})).filter(E=>typeof E.x=="number"&&!isNaN(E.x)&&typeof E.y=="number"&&!isNaN(E.y));W[M.id][w.id]=N}})});const B={columns:c,matrix:m,scatterPlots:W};_(B),localStorage.setItem("correlation_matrix_results",JSON.stringify(B)),Re(!1)}catch(c){we(`Error in correlation analysis: ${c instanceof Error?c.message:String(c)}`),Re(!1)}},de=()=>{if(!A)return"";const c=[],$=[];A.columns.forEach((B,M)=>{A.columns.slice(M+1).forEach(w=>{const N=A.matrix[B.id][w.id];if(N&&N.correlation!==null){const E={var1:B.name,var2:w.name,r:N.correlation,pValue:N.pValue,n:N.n};$.push(E),N.pValue<.05&&c.push(E)}})});let m="";if(m+=`## Correlation Analysis Summary

`,m+=`This analysis examined ${$.length} pairwise correlations between ${A.columns.length} variables using ${J==="pearson"?"Pearson":"Spearman"} correlation coefficients.

`,c.length===0)m+=`### Statistical Significance

`,m+=`No statistically significant correlations were found at the α = 0.05 level. This could indicate:
`,m+=`• True absence of linear relationships between variables
`,m+=`• Insufficient sample size to detect existing relationships
`,m+=`• Non-linear relationships that correlation analysis cannot capture
`,m+=`• High measurement error reducing correlation strength

`;else{m+=`### Statistical Significance

`,m+=`${c.length} of ${$.length} correlations (${(c.length/$.length*100).toFixed(1)}%) were statistically significant at p < 0.05.

`;const B=c.sort((M,w)=>Math.abs(w.r)-Math.abs(M.r)).slice(0,5);m+=`**Strongest Significant Correlations:**

`,B.forEach((M,w)=>{const N=Math.abs(M.r),E=N<.1?"negligible":N<.3?"weak":N<.5?"moderate":N<.7?"strong":"very strong",ae=M.r<0?"negative":"positive",me=N<.1?"no practical significance":N<.3?"small effect":N<.5?"medium effect":N<.7?"large effect":"very large effect";m+=`${w+1}. **${M.var1} ↔ ${M.var2}**
`,m+=`   • Correlation: r = ${M.r.toFixed(3)} (${ae} ${E})
`,m+=`   • Significance: p = ${M.pValue<.001?"< 0.001":M.pValue.toFixed(3)}
`,m+=`   • Effect Size: ${me}
`,m+=`   • Sample Size: n = ${M.n}
`,m+=`   • Shared Variance: ${(M.r*M.r*100).toFixed(1)}% (r²)

`}),c.length>5&&(m+=`*${c.length-5} additional significant correlations found. See matrix for complete results.*

`)}const W={negligible:$.filter(B=>Math.abs(B.r)<.1).length,weak:$.filter(B=>Math.abs(B.r)>=.1&&Math.abs(B.r)<.3).length,moderate:$.filter(B=>Math.abs(B.r)>=.3&&Math.abs(B.r)<.5).length,strong:$.filter(B=>Math.abs(B.r)>=.5&&Math.abs(B.r)<.7).length,veryStrong:$.filter(B=>Math.abs(B.r)>=.7).length};return m+=`### Correlation Strength Distribution

`,m+=`• **Negligible** (|r| < 0.1): ${W.negligible} correlations
`,m+=`• **Weak** (|r| = 0.1-0.3): ${W.weak} correlations
`,m+=`• **Moderate** (|r| = 0.3-0.5): ${W.moderate} correlations
`,m+=`• **Strong** (|r| = 0.5-0.7): ${W.strong} correlations
`,m+=`• **Very Strong** (|r| ≥ 0.7): ${W.veryStrong} correlations

`,m+=`### Methodological Notes

`,J==="pearson"?(m+=`**Pearson Correlation** was used, which:
`,m+=`• Measures linear relationships between continuous variables
`,m+=`• Assumes variables are normally distributed
`,m+=`• Is sensitive to outliers and extreme values
`,m+=`• Ranges from -1 (perfect negative) to +1 (perfect positive)

`):(m+=`**Spearman Correlation** was used, which:
`,m+=`• Measures monotonic relationships (not necessarily linear)
`,m+=`• Does not assume normal distribution
`,m+=`• Is robust to outliers and extreme values
`,m+=`• Based on rank ordering of values

`),m+=`### Interpretation Guidelines

`,m+=`**Statistical vs. Practical Significance:**
`,m+=`• Statistical significance (p < 0.05) indicates the correlation is unlikely due to chance
`,m+=`• Practical significance depends on effect size (correlation strength)
`,m+=`• Large samples can produce statistically significant but practically meaningless correlations

`,m+=`**Effect Size Interpretation (Cohen's Guidelines):**
`,m+=`• Small effect: |r| ≈ 0.1 (1% shared variance)
`,m+=`• Medium effect: |r| ≈ 0.3 (9% shared variance)
`,m+=`• Large effect: |r| ≈ 0.5 (25% shared variance)

`,m+=`**Important Considerations:**
`,m+=`• Correlation does not imply causation
`,m+=`• Missing data may affect correlation strength
`,m+=`• Non-linear relationships may not be detected
`,m+=`• Multiple comparisons increase Type I error risk
`,m};return e.jsxs(b,{p:3,children:[e.jsx(s,{variant:"h5",gutterBottom:!0,children:"Correlation Analysis"}),e.jsxs(K,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(s,{variant:"subtitle1",gutterBottom:!0,children:"Select Variables"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"dataset-select-label",children:"Dataset"}),e.jsx(be,{labelId:"dataset-select-label",id:"dataset-select",value:Ce,label:"Dataset",onChange:et,disabled:z.length===0,children:z.length===0?e.jsx(q,{value:"",disabled:!0,children:"No datasets available"}):z.map(c=>e.jsxs(q,{value:c.id,children:[c.name," (",c.data.length," rows)"]},c.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"correlation-type-label",children:"Correlation Type"}),e.jsxs(be,{labelId:"correlation-type-label",id:"correlation-type",value:J,label:"Correlation Type",onChange:Pe,children:[e.jsxs(q,{value:"pearson",children:["Pearson Correlation",e.jsx(s,{variant:"caption",display:"block",color:"text.secondary",children:"Measures linear relationship between variables"})]}),e.jsxs(q,{value:"spearman",children:["Spearman Rank Correlation",e.jsx(s,{variant:"caption",display:"block",color:"text.secondary",children:"Non-parametric alternative, robust to outliers"})]})]})]})}),e.jsxs(x,{item:!0,xs:12,children:[e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"variables-label",children:"Variables to Correlate"}),e.jsx(be,{labelId:"variables-label",id:"variables",multiple:!0,value:G,label:"Variables to Correlate",onChange:Ae,disabled:!g,renderValue:c=>c.map(m=>{var W;return((W=ie.find(B=>B.id===m))==null?void 0:W.name)||m}).join(", "),children:ie.length===0?e.jsx(q,{value:"",disabled:!0,children:"No numeric variables available"}):ie.map(c=>e.jsx(q,{value:c.id,children:c.name},c.id))})]}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Select at least 2 numeric variables to calculate correlations."})]})]}),e.jsxs(b,{mt:2,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsxs(Lt,{row:!0,children:[e.jsx(ye,{control:e.jsx(Ne,{checked:Ee.showPValues,onChange:()=>Te("showPValues")}),label:"Show p-values"}),e.jsx(ye,{control:e.jsx(Ne,{checked:Ee.showSignificanceStars,onChange:()=>Te("showSignificanceStars")}),label:"Show significance stars"}),e.jsx(ye,{control:e.jsx(Ne,{checked:Ee.highlightSignificant,onChange:()=>Te("highlightSignificant")}),label:"Highlight significant correlations"})]})]}),e.jsx(b,{mt:2,children:e.jsx(Ke,{variant:"contained",color:"primary",startIcon:e.jsx(Ft,{}),onClick:t,disabled:re||G.length<2,children:"Generate Correlation Matrix"})})]}),re&&e.jsx(b,{display:"flex",justifyContent:"center",my:4,children:e.jsx(Ct,{})}),ce&&e.jsx(ut,{severity:"error",sx:{mb:3},children:ce}),A&&!re&&e.jsxs(K,{elevation:2,sx:{p:2,mb:3},children:[e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsxs(s,{variant:"h6",children:[J==="pearson"?"Pearson":"Spearman"," Correlation Matrix"]}),e.jsx(ke,{icon:e.jsx(qe,{}),label:`Total Variables: ${A.columns.length}`,color:"primary",variant:"outlined"})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(He,{fontSize:"small"}),"Sample Size Information"]}),e.jsx(x,{container:!0,spacing:2,children:(()=>{const c=[];A.columns.forEach(M=>{A.columns.forEach(w=>{const N=A.matrix[M.id][w.id];N&&N.n&&M.id!==w.id&&c.push(N.n)})});const $=Math.max(...c),m=Math.min(...c),W=Math.round(c.reduce((M,w)=>M+w,0)/c.length),B=m!==$;return e.jsxs(e.Fragment,{children:[e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Maximum N",value:$.toLocaleString(),description:"Largest pairwise sample size",color:"primary",variant:"outlined",icon:e.jsx(qe,{})})}),B&&e.jsxs(e.Fragment,{children:[e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Minimum N",value:m.toLocaleString(),description:"Smallest pairwise sample size",color:"warning",variant:"outlined",icon:e.jsx(qe,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Average N",value:W.toLocaleString(),description:"Mean pairwise sample size",color:"info",variant:"outlined",icon:e.jsx(qe,{})})})]}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Missing Data",value:B?"Variable":"None",description:B?"Sample sizes vary by pair":"Consistent across all pairs",color:B?"warning":"success",variant:"outlined",icon:e.jsx(He,{})})})]})})()})]}),e.jsxs(b,{sx:{overflowX:"auto",mb:3},children:[e.jsx(s,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Hover over cells for detailed information. Darker colors indicate stronger correlations."}),e.jsxs(b,{sx:{display:"grid",gridTemplateColumns:`minmax(140px, auto) repeat(${A.columns.length}, minmax(110px, 1fr))`,gap:1,my:2,p:1,backgroundColor:le(a.palette.background.default,.3),borderRadius:2,border:`1px solid ${a.palette.divider}`},role:"table","aria-label":"Correlation matrix",children:[e.jsx(b,{sx:{fontWeight:"bold",p:1,display:"flex",alignItems:"center",justifyContent:"center"},role:"columnheader",children:e.jsx(s,{variant:"body2",fontWeight:"bold",color:"text.secondary",children:"Variables"})}),A.columns.map(c=>e.jsx(b,{sx:{fontWeight:"bold",p:1.5,textAlign:"center",backgroundColor:le(a.palette.primary.main,.1),borderRadius:1,border:`1px solid ${le(a.palette.primary.main,.2)}`,minHeight:48,display:"flex",alignItems:"center",justifyContent:"center"},role:"columnheader","aria-label":`Column for ${c.name}`,children:e.jsx(s,{variant:"body2",fontWeight:"bold",noWrap:!0,children:c.name})},c.id)),A.columns.map(c=>e.jsxs(Ut.Fragment,{children:[e.jsx(b,{sx:{fontWeight:"bold",p:1.5,backgroundColor:le(a.palette.primary.main,.1),borderRadius:1,border:`1px solid ${le(a.palette.primary.main,.2)}`,minHeight:48,display:"flex",alignItems:"center",justifyContent:"flex-start"},role:"rowheader","aria-label":`Row for ${c.name}`,children:e.jsx(s,{variant:"body2",fontWeight:"bold",noWrap:!0,children:c.name})}),A.columns.map($=>{const m=A.matrix[c.id][$.id];if(m.error)return e.jsx(b,{sx:{p:1.5,backgroundColor:le(a.palette.error.main,.1),textAlign:"center",color:"error.main",fontSize:"0.8rem",borderRadius:1,border:`1px solid ${le(a.palette.error.main,.2)}`,minHeight:48,display:"flex",alignItems:"center",justifyContent:"center"},role:"cell","aria-label":`No data available for ${c.name} and ${$.name}`,children:e.jsx(Nt,{title:m.error||"Insufficient data for correlation calculation",arrow:!0,children:e.jsx(s,{variant:"body2",fontWeight:"medium",children:"N/A"})})},`cell-${c.id}-${$.id}`);const W=m.pValue<.05;return e.jsx(b,{sx:{aspectRatio:"1/1",display:"flex",alignItems:"center",justifyContent:"center",minHeight:48},role:"cell","aria-label":`Correlation between ${c.name} and ${$.name}: ${m.correlation.toFixed(3)}, p-value: ${m.pValue<.001?"less than 0.001":m.pValue.toFixed(3)}`,children:e.jsx(ti,{value:m.correlation,pValue:m.pValue,significant:W,colorScale:X,sampleSize:m.n,var1Name:c.name,var2Name:$.name})},`cell-${c.id}-${$.id}`)})]},`row-${c.id}`))]})]}),e.jsxs(b,{mt:3,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(zt,{fontSize:"small"}),"Correlation Strength & Color Scale"]}),e.jsxs(b,{sx:{mb:2},children:[e.jsx(b,{sx:{height:40,borderRadius:2,background:`linear-gradient(to right,
                    ${X(-1)},
                    ${X(-.8)},
                    ${X(-.6)},
                    ${X(-.4)},
                    ${X(-.2)},
                    ${X(0)},
                    ${X(.2)},
                    ${X(.4)},
                    ${X(.6)},
                    ${X(.8)},
                    ${X(1)})`,border:`1px solid ${a.palette.divider}`,position:"relative"}}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mt:1,px:.5},children:[e.jsx(s,{variant:"caption",fontWeight:"medium",children:"-1.0"}),e.jsx(s,{variant:"caption",fontWeight:"medium",children:"-0.5"}),e.jsx(s,{variant:"caption",fontWeight:"medium",children:"0"}),e.jsx(s,{variant:"caption",fontWeight:"medium",children:"+0.5"}),e.jsx(s,{variant:"caption",fontWeight:"medium",children:"+1.0"})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mt:.5,px:.5},children:[e.jsx(s,{variant:"caption",color:"text.secondary",children:"Perfect Negative"}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Moderate Negative"}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"No Correlation"}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Moderate Positive"}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Perfect Positive"})]})]}),e.jsx(x,{container:!0,spacing:1,sx:{mb:2},children:[{range:"0.0 - 0.1",strength:"Negligible",color:X(.05)},{range:"0.1 - 0.3",strength:"Weak",color:X(.2)},{range:"0.3 - 0.5",strength:"Moderate",color:X(.4)},{range:"0.5 - 0.7",strength:"Strong",color:X(.6)},{range:"0.7 - 1.0",strength:"Very Strong",color:X(.85)}].map((c,$)=>e.jsx(x,{item:!0,xs:12,sm:6,md:2.4,children:e.jsxs(b,{sx:{p:1,borderRadius:1,backgroundColor:le(c.color,.1),border:`1px solid ${le(c.color,.3)}`,textAlign:"center"},children:[e.jsx(s,{variant:"caption",fontWeight:"medium",display:"block",children:c.strength}),e.jsxs(s,{variant:"caption",color:"text.secondary",children:["|r| = ",c.range]})]})},$))})]}),e.jsxs(b,{mt:2,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Statistical Significance"}),e.jsxs(b,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(ke,{label:"* p < 0.05",size:"small",variant:"outlined"}),e.jsx(ke,{label:"** p < 0.01",size:"small",variant:"outlined"}),e.jsx(ke,{label:"*** p < 0.001",size:"small",variant:"outlined"}),e.jsx(ke,{label:"Bold = Significant",size:"small",color:"primary",variant:"outlined"})]})]}),e.jsxs(b,{mt:4,children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Correlation Table"}),e.jsx(nt,{children:e.jsxs(st,{size:"small",children:[e.jsx(ot,{children:e.jsxs(ue,{children:[e.jsx(p,{children:"Variable 1"}),e.jsx(p,{children:"Variable 2"}),e.jsx(p,{align:"right",children:"Correlation"}),e.jsx(p,{align:"right",children:"p-value"}),e.jsx(p,{align:"right",children:"N"}),J==="pearson"&&e.jsx(p,{align:"right",children:"R²"})]})}),e.jsx(lt,{children:A.columns.map((c,$)=>A.columns.slice($+1).map(m=>{const W=A.matrix[c.id][m.id];if(W.error)return null;const B=W.pValue<.05;return e.jsxs(ue,{sx:{backgroundColor:Ee.highlightSignificant&&B?`${a.palette.success.main}20`:"inherit"},children:[e.jsx(p,{children:c.name}),e.jsx(p,{children:m.name}),e.jsxs(p,{align:"right",sx:{fontWeight:B?"bold":"normal"},children:[W.correlation.toFixed(3),Ee.showSignificanceStars&&B&&e.jsx(b,{component:"span",ml:.5,children:W.pValue<.001?"***":W.pValue<.01?"**":"*"})]}),e.jsx(p,{align:"right",children:W.pValue<.001?"< 0.001":W.pValue.toFixed(3)}),e.jsx(p,{align:"right",children:W.n}),J==="pearson"&&e.jsx(p,{align:"right",children:W.rSquared.toFixed(3)})]},`${c.id}-${m.id}`)}))})]})})]}),e.jsxs(b,{mt:4,children:[e.jsxs(s,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(He,{}),"Statistical Interpretation & Guidance"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:3,bgcolor:le(a.palette.background.paper,.8),borderRadius:2},children:e.jsx(b,{sx:{"& h2":{fontSize:"1.1rem",fontWeight:600,color:a.palette.primary.main,mt:2,mb:1,"&:first-of-type":{mt:0}},"& h3":{fontSize:"1rem",fontWeight:600,color:a.palette.text.primary,mt:2,mb:1},"& p":{mb:1,lineHeight:1.6},"& ul":{pl:2,mb:1},"& li":{mb:.5}},children:e.jsx(s,{variant:"body2",sx:{whiteSpace:"pre-line","& strong":{fontWeight:600}},dangerouslySetInnerHTML:{__html:de().replace(/## (.*)/g,"<h2>$1</h2>").replace(/### (.*)/g,"<h3>$1</h3>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/• (.*)/g,"• $1").replace(/\n\n/g,"</p><p>").replace(/^\s*/,"<p>").replace(/\s*$/,"</p>")}})})})]})]})]})},ri=()=>{var Fe,Se;const{datasets:z,currentDataset:g,setCurrentDataset:te}=St(),a=ht(),[Ce,Me]=L.useState((g==null?void 0:g.id)||""),[G,Ie]=L.useState([]),[J,Ve]=L.useState(""),[Ee,We]=L.useState(.95),[re,Re]=L.useState({}),[ce,we]=L.useState({}),[A,_]=L.useState(null),[ie,et]=L.useState({showConfidenceIntervals:!0,showPredictionIntervals:!1,showRegressionEquation:!0,showRegressionLine:!0,showResidualPlot:!0}),[Ae,Pe]=L.useState(!1),[Te,X]=L.useState(null),[t,de]=L.useState(null);L.useEffect(()=>{const i=localStorage.getItem("linear_regression_results");if(i)try{const l=JSON.parse(i);de(l)}catch(l){console.error("Error parsing saved linear regression results:",l),localStorage.removeItem("linear_regression_results")}},[]);const c=(g==null?void 0:g.columns.filter(i=>i.type===P.NUMERIC||i.type===P.CATEGORICAL))||[],$=(g==null?void 0:g.columns.filter(i=>i.type===P.NUMERIC))||[],m=i=>{const l=i.target.value;Me(l),Ie([]),Ve(""),Re({}),de(null),_(null),localStorage.removeItem("linear_regression_results");const h=z.find(k=>k.id===l);h&&te(h)},W=i=>{const l=i.target.value,h=typeof l=="string"?l.split(","):l;Ie(h);const k={...re};Object.keys(re).forEach(F=>{h.includes(F)||delete k[F]}),Re(k),de(null),_(null),localStorage.removeItem("linear_regression_results")},B=i=>{Ve(i.target.value),de(null),_(null),localStorage.removeItem("linear_regression_results")},M=(i,l)=>{Re(h=>({...h,[i]:l})),de(null),_(null),localStorage.removeItem("linear_regression_results")},w=i=>{if(!g)return[];const l=g.columns.find(h=>h.id===i);return!l||l.type!==P.CATEGORICAL?[]:Vt(i,g)},N=i=>{We(Number(i.target.value)),de(null),_(null),localStorage.removeItem("linear_regression_results")},E=i=>{et(l=>({...l,[i]:!l[i]}))},ae=(i,l)=>{we(h=>({...h,[i]:l})),_(null)},me=()=>{if(!(!t||!t.originalXColumns||!t.xColumns||!t.coefficients))try{const i={},l=[];let h=!0;if(t.originalXColumns.forEach(O=>{if(!h)return;const ne=ce[O.id];if(ne===void 0||String(ne).trim()===""){_({error:`Please enter a value for ${O.name}`}),h=!1;return}if(O.type===P.NUMERIC){const R=parseFloat(String(ne));if(isNaN(R)){_({error:`Invalid number entered for ${O.name}`}),h=!1;return}i[O.id]=R,l.push({name:O.name,value:R})}else if(O.type===P.CATEGORICAL){const R=String(ne);t.xColumns.filter(d=>d.originalColumnId===O.id).forEach(d=>{d.dummyForCategory&&(i[d.id]=R===d.dummyForCategory?1:0)}),l.push({name:O.name,value:R})}}),!h)return;let k=t.intercept;t.coefficients.forEach((O,ne)=>{const R=t.xColumns[ne];i[R.id]!==void 0?k+=O*i[R.id]:console.error(`Missing processed value for ${R.name} (${R.id}) in prediction calculation.`)});const F=[k*.9,k*1.1];_({inputs:l,predicted:k,predictionInterval:F,confidenceInterval:F,intervalNote:"Note: Prediction and confidence intervals shown are placeholders for multiple regression."})}catch(i){_({error:i instanceof Error?i.message:String(i)})}},xe=()=>{if(!g||G.length===0||!J){X("Please select at least one independent variable and one dependent variable.");return}Pe(!0),X(null),de(null),_(null);try{const i=G.map(f=>g.columns.find(o=>o.id===f)).filter(Boolean),l=g.columns.find(f=>f.id===J);if(i.length===0||!l)throw new Error("Selected variables not found in dataset.");for(const f of i)if(f.type===P.CATEGORICAL&&!re[f.id])throw new Error(`Please select a base category for the categorical variable "${f.name}".`);const h=[],k=[];i.forEach(f=>{if(f.type===P.NUMERIC)h.push(f),k.push(f.name);else if(f.type===P.CATEGORICAL){const o=re[f.id];w(f.id).forEach(C=>{if(C!==o){const ee=`${f.name} (${C} vs ${o})`;k.push(ee),h.push({id:`${f.id}_dummy_${C}`,name:ee,type:P.NUMERIC,role:At.INDEPENDENT,description:`Dummy for ${f.name}, cat ${C}, base ${o}`})}})}});const F=[],O=[],ne=[];if(g.data.forEach((f,o)=>{const y=f[l.name];if(typeof y!="number"||isNaN(y))return;const C=[];let ee=!0;i.forEach(D=>{if(ee){if(D.type===P.NUMERIC){const Q=f[D.name];if(typeof Q!="number"||isNaN(Q)){ee=!1;return}C.push(Q)}else if(D.type===P.CATEGORICAL){const Q=re[D.id],se=f[D.name];w(D.id).forEach(pe=>{pe!==Q&&C.push(se===pe?1:0)})}}}),ee&&(F.push(C),O.push(y),ne.push(o))}),F.length<k.length+2||F.length<3)throw new Error(`Not enough valid data rows for regression analysis. Need at least ${Math.max(3,k.length+2)} valid rows after processing categorical variables.`);const R=Ht(F,O);R.xNames=k;let U=null;const d=i[0];let $e=null;d&&d.type===P.NUMERIC&&($e=d,U=[],F.forEach((f,o)=>{const y=i.findIndex(C=>C.id===d.id);y!==-1&&(U==null||U.push({x:f[y],y:O[o],residual:R.residuals[o],predicted:R.predicted[o],index:ne[o]}))}),U&&U.length>0?U.sort((o,y)=>o.x-y.x):U=null);const je=R.residuals.reduce((f,o)=>f+o,0)/R.residuals.length,r=Math.sqrt(R.residuals.reduce((f,o)=>f+Math.pow(o-je,2),0)/(R.residuals.length-1))||1,n=R.predicted.map((f,o)=>({x:f,residual:R.residuals[o],standardized:r!==0?R.residuals[o]/r:0,index:ne[o]})).sort((f,o)=>f.x-o.x),u=R.residuals.reduce((f,o)=>f+Math.pow((o-je)/r,3),0)/R.residuals.length,I=R.residuals.reduce((f,o)=>f+Math.pow((o-je)/r,4),0)/R.residuals.length-3,j=Math.abs(u)<1&&Math.abs(I)<1,v=[...R.residuals].sort((f,o)=>f-o),V=v.map((f,o)=>{const y=(o+.5)/v.length,C=2.515517,ee=.802853,D=.010328,Q=1.432788,se=.189269,he=.001308;let pe;if(y<=.5){const T=Math.sqrt(-2*Math.log(y));pe=-T+(C+ee*T+D*T*T)/(1+Q*T+se*T*T+he*T*T*T)}else{const T=Math.sqrt(-2*Math.log(1-y));pe=T-(C+ee*T+D*T*T)/(1+Q*T+se*T*T+he*T*T*T)}return{theoretical:pe*r+je,observed:f}}),S={...R,xColumns:h,originalXColumns:i,yColumn:l,xColumn:h[0],scatterData:U,firstSelectedXColForScatter:$e,residualPlotData:n,qqPlotData:V,residualStats:{mean:je,std:r,skewness:u,kurtosis:I,normal:j}};de(S);try{localStorage.setItem("linear_regression_results",JSON.stringify(S))}catch(f){console.error("Error saving linear regression results to localStorage:",f)}Pe(!1)}catch(i){X(`Error in regression analysis: ${i instanceof Error?i.message:String(i)}`),Pe(!1)}},De=()=>{if(!t)return"";const{coefficients:i,intercept:l,rSquared:h,pValue:k,n:F,pValues:O,interceptPValue:ne,xNames:R,yColumn:U}=t;let d="";const $e=(R==null?void 0:R.length)||0,je=$e,r=F-$e-1,n=r>0&&$e>0?1-(1-h)*(F-1)/r:null,u=r>0&&$e>0&&h<1?h/je/((1-h)/r):null,I=R&&R.length>1?`${R.slice(0,-1).join(", ")} and ${R[R.length-1]}`:(R==null?void 0:R[0])||"the selected predictor(s)";if(d+=`A linear regression analysis was conducted to predict ${U.name} based on ${I}. `,d+=`The model was fitted using ${F.toLocaleString()} observations. `,u!==null?k<.05?(d+=`The overall regression model was statistically significant (F(${je}, ${r}) = ${u.toFixed(2)}, p ${k<.001?"< 0.001":"= "+k.toFixed(3)}), `,d+=`explaining ${(h*100).toFixed(1)}% of the variance in ${U.name} (R² = ${h.toFixed(3)}`,n!==null&&(d+=`, adjusted R² = ${n.toFixed(3)}`),d+="). "):(d+=`The overall regression model was not statistically significant (F(${je}, ${r}) = ${u.toFixed(2)}, p = ${k.toFixed(3)}), `,d+=`explaining only ${(h*100).toFixed(1)}% of the variance in ${U.name} (R² = ${h.toFixed(3)}). `):d+=`The model explained ${(h*100).toFixed(1)}% of the variance in ${U.name} (R² = ${h.toFixed(3)}). `,R&&i&&O){const j=i.filter((V,S)=>O[S]<.05),v=R.filter((V,S)=>O[S]<.05);if(j.length>0)if(j.length===1){const V=O.findIndex(o=>o<.05),S=i[V],f=R[V];d+=`${f} was a statistically significant predictor (β = ${S.toFixed(3)}, p ${O[V]<.001?"< 0.001":"= "+O[V].toFixed(3)}), `,d+=`indicating that for each one-unit increase in ${f}, ${U.name} ${S>0?"increases":"decreases"} by ${Math.abs(S).toFixed(3)} units on average, holding other variables constant. `}else d+=`${j.length} of the ${$e} predictor variables were statistically significant: ${v.join(", ")}. `;else d+="None of the predictor variables were statistically significant at the α = 0.05 level. "}if(h<.01?d+="The effect size was negligible (R² < 0.01), suggesting minimal practical significance. ":h<.09?d+="The effect size was small (R² = 0.01-0.09), indicating a weak but detectable relationship. ":h<.25?d+="The effect size was medium (R² = 0.09-0.25), indicating a moderate relationship with practical significance. ":d+="The effect size was large (R² ≥ 0.25), indicating a strong relationship with substantial practical significance. ",d+=`

`,d+=`## Detailed Analysis Summary

`,d+=`This analysis examined the relationship between ${U.name} (dependent variable) and ${$e} predictor variable${$e>1?"s":""} using multiple linear regression.

`,d+=`### Model Performance

`,d+=`**Sample Size:** n = ${F.toLocaleString()} observations

`,u!==null&&(k<.05?d+=`**Overall Model Significance:** The regression model was statistically significant (F(${je}, ${r}) = ${u.toFixed(2)}, p ${k<.001?"< 0.001":"= "+k.toFixed(3)}), indicating that the predictors collectively explain a significant portion of the variance in ${U.name}.

`:d+=`**Overall Model Significance:** The regression model was not statistically significant (F(${je}, ${r}) = ${u.toFixed(2)}, p = ${k.toFixed(3)}), suggesting that the predictors do not collectively explain a significant portion of the variance in ${U.name}.

`),d+=`**Variance Explained:**
`,d+=`• R² = ${h.toFixed(4)} (${(h*100).toFixed(1)}% of variance explained)
`,n!==null&&(d+=`• Adjusted R² = ${n.toFixed(4)} (adjusted for model complexity)
`),d+=`• Standard Error of Regression = ${t.standardErrorOfRegression?t.standardErrorOfRegression.toFixed(4):"N/A"}

`,h<.01?d+=`**Effect Size:** Negligible effect (R² < 0.01)

`:h<.09?d+=`**Effect Size:** Small effect (R² = 0.01-0.09)

`:h<.25?d+=`**Effect Size:** Medium effect (R² = 0.09-0.25)

`:d+=`**Effect Size:** Large effect (R² ≥ 0.25)

`,d+=`### Regression Equation

`,d+=`**${U.name} = ${l.toFixed(4)}`,R&&i&&i.forEach((j,v)=>{const V=R[v]||`Predictor ${v+1}`;d+=` ${j>=0?"+":"-"} ${Math.abs(j).toFixed(4)} × (${V})`}),d+=`**

`,d+=`### Coefficient Analysis

`,ne<.05?d+=`**Intercept:** ${l.toFixed(4)} (p ${ne<.001?"< 0.001":"= "+ne.toFixed(3)}) - Statistically significant. This represents the expected value of ${U.name} when all predictors equal zero.

`:d+=`**Intercept:** ${l.toFixed(4)} (p = ${ne.toFixed(3)}) - Not statistically significant.

`,R&&i&&O&&(d+=`**Predictor Variables:**

`,i.forEach((j,v)=>{const V=R[v]||`Predictor ${v+1}`,S=O[v],f=t.stdErrors?j/t.stdErrors[v]:null;d+=`• **${V}:**
`,d+=`  - Coefficient: ${j.toFixed(4)}
`,d+=`  - Standard Error: ${t.stdErrors?t.stdErrors[v].toFixed(4):"N/A"}
`,f&&(d+=`  - t-value: ${f.toFixed(4)}
`),d+=`  - p-value: ${S<.001?"< 0.001":S.toFixed(4)}
`,d+=`  - Significance: ${S<.05?"**Significant**":"Not significant"}
`,S<.05?V.includes("vs")||V.includes("dummy")?d+=`  - Interpretation: This category differs significantly from the reference category by ${j.toFixed(4)} units in ${U.name}, holding other variables constant.
`:d+=`  - Interpretation: For each one-unit increase in ${V}, ${U.name} ${j>0?"increases":"decreases"} by ${Math.abs(j).toFixed(4)} units, holding other variables constant.
`:d+=`  - Interpretation: No significant relationship detected with ${U.name}.
`,d+=`
`})),d+=`### Model Assumptions and Diagnostics

`,d+=`**Residual Analysis:**
`,t.residualStats){const{skewness:j,kurtosis:v,normal:V}=t.residualStats;V?d+=`• **Normality:** Residuals appear normally distributed (skewness = ${j.toFixed(3)}, kurtosis = ${v.toFixed(3)}). This supports the normality assumption.
`:d+=`• **Normality:** Residuals may violate normality assumption (skewness = ${j.toFixed(3)}, kurtosis = ${v.toFixed(3)}). Consider data transformation or robust regression methods.
`,d+=`• **Homoscedasticity:** Examine the residual plot for constant variance. Points should be randomly scattered around zero with consistent spread.
`,d+=`• **Independence:** Ensure observations are independent. Check for autocorrelation if data has temporal or spatial structure.
`,d+=`• **Linearity:** The relationship between predictors and outcome should be linear. Consider polynomial terms or transformations if needed.

`}return d+=`### Practical Significance and Limitations

`,d+=`**Practical Considerations:**
`,d+=`• **Clinical/Practical Significance:** Consider whether the magnitude of effects is meaningful in the real-world context, not just statistical significance.
`,d+=`• **Confidence Intervals:** Examine confidence intervals for coefficients to understand the range of plausible effect sizes.
`,d+=`• **Multicollinearity:** Check for high correlations between predictors that might affect coefficient stability.
`,d+=`• **Outliers and Influential Points:** Examine residual plots and leverage statistics for observations that might unduly influence results.

`,d+=`### Methodological Notes

`,d+=`**Linear Regression Assumptions:**
`,d+=`• **Linearity:** The relationship between predictors and outcome is linear
`,d+=`• **Independence:** Observations are independent of each other
`,d+=`• **Homoscedasticity:** Constant variance of residuals across all levels of predictors
`,d+=`• **Normality:** Residuals are normally distributed
`,d+=`• **No perfect multicollinearity:** Predictors are not perfectly correlated

`,d+=`**Interpretation Guidelines:**
`,d+=`• **R² Interpretation:** Proportion of variance in the dependent variable explained by the model
`,d+=`• **Adjusted R²:** R² adjusted for the number of predictors (penalizes model complexity)
`,d+=`• **F-test:** Tests whether the overall model is statistically significant
`,d+=`• **t-tests:** Test whether individual coefficients are significantly different from zero
`,d+=`• **Standard Error:** Measure of precision for coefficient estimates

`,d+=`**Important Considerations:**
`,d+=`• Correlation does not imply causation
`,d+=`• Results are specific to the sample and may not generalize to other populations
`,d+=`• Missing data patterns may affect results
`,d+=`• Model selection and variable inclusion decisions affect interpretation
`,d+=`• Consider alternative models (non-linear, robust regression) if assumptions are violated
`,d},Le=i=>Math.abs(i)>3?a.palette.error.main:Math.abs(i)>2?a.palette.warning.main:a.palette.primary.main;return e.jsxs(b,{p:3,children:[e.jsx(s,{variant:"h5",gutterBottom:!0,children:"Linear Regression"}),e.jsxs(K,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(s,{variant:"subtitle1",gutterBottom:!0,children:"Select Variables"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"dataset-select-label",children:"Dataset"}),e.jsx(be,{labelId:"dataset-select-label",id:"dataset-select",value:Ce,label:"Dataset",onChange:m,disabled:z.length===0,children:z.length===0?e.jsx(q,{value:"",disabled:!0,children:"No datasets available"}):z.map(i=>e.jsxs(q,{value:i.id,children:[i.name," (",i.data.length," rows)"]},i.id))})]})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"independent-variables-label",children:"Independent Variables (X)"}),e.jsx(be,{labelId:"independent-variables-label",id:"independent-variables",multiple:!0,value:G,label:"Independent Variables (X)",onChange:W,disabled:!g,renderValue:i=>i.map(h=>{const k=c.find(F=>F.id===h);return k?k.name:""}).filter(Boolean).join(", "),children:c.length===0?e.jsx(q,{value:"",disabled:!0,children:"No suitable variables available"}):c.map(i=>e.jsxs(q,{value:i.id,children:[i.name," (",i.type===P.CATEGORICAL?"Categorical":"Numeric",")"]},i.id))}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Select one or more predictor variables (Numeric or Categorical)."})]})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"dependent-variable-label",children:"Dependent Variable (Y)"}),e.jsx(be,{labelId:"dependent-variable-label",id:"dependent-variable",value:J,label:"Dependent Variable (Y)",onChange:B,disabled:!g,children:$.length===0?e.jsx(q,{value:"",disabled:!0,children:"No numeric variables available"}):$.map(i=>e.jsx(q,{value:i.id,children:i.name},i.id))})]})})]}),G.some(i=>{var l;return((l=c.find(h=>h.id===i))==null?void 0:l.type)===P.CATEGORICAL})&&e.jsxs(b,{mt:2,p:2,border:1,borderColor:"divider",borderRadius:1,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Select Base Category for Categorical Variables"}),e.jsx(x,{container:!0,spacing:2,children:G.map(i=>{const l=c.find(h=>h.id===i);if(l&&l.type===P.CATEGORICAL){const h=w(i);return e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsxs(fe,{id:`base-category-label-${i}`,children:["Base for ",l.name]}),e.jsx(be,{labelId:`base-category-label-${i}`,value:re[i]||"",label:`Base for ${l.name}`,onChange:k=>M(i,k.target.value),children:h.length===0?e.jsx(q,{value:"",disabled:!0,children:"No categories found"}):h.map(k=>e.jsx(q,{value:k,children:k},k))})]})},i)}return null})})]}),e.jsx(b,{mt:1,children:e.jsx(x,{container:!0,spacing:2,children:e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"conf-interval-label",children:"Confidence Level"}),e.jsxs(be,{labelId:"conf-interval-label",id:"conf-interval",value:Ee,label:"Confidence Level",onChange:N,children:[e.jsx(q,{value:.9,children:"90%"}),e.jsx(q,{value:.95,children:"95%"}),e.jsx(q,{value:.99,children:"99%"})]})]})})})}),e.jsxs(b,{mt:2,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showRegressionLine,onChange:()=>E("showRegressionLine")}),label:"Show regression line"}),e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showConfidenceIntervals,onChange:()=>E("showConfidenceIntervals")}),label:"Show confidence intervals"}),e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showPredictionIntervals,onChange:()=>E("showPredictionIntervals")}),label:"Show prediction intervals"})]}),e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showRegressionEquation,onChange:()=>E("showRegressionEquation")}),label:"Show regression equation"}),e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showResidualPlot,onChange:()=>E("showResidualPlot")}),label:"Show residual plot"})]})]})]}),e.jsx(b,{mt:2,children:e.jsx(Ke,{variant:"contained",color:"primary",startIcon:e.jsx(xt,{}),onClick:xe,disabled:Ae||G.length===0||!J||G.includes(J),children:"Run Regression Analysis"})})]}),Ae&&e.jsx(b,{display:"flex",justifyContent:"center",my:4,children:e.jsx(Ct,{})}),Te&&e.jsx(ut,{severity:"error",sx:{mb:3},children:Te}),t&&!Ae&&e.jsx(e.Fragment,{children:e.jsxs(K,{elevation:2,sx:{p:3,mb:3},children:[e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsx(s,{variant:"h6",children:"Linear Regression Results"}),e.jsx(ke,{icon:e.jsx(qe,{}),label:`n = ${t.n.toLocaleString()}`,color:"primary",variant:"outlined"})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(He,{fontSize:"small"}),"Model Overview"]}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,children:e.jsx(H,{title:"Dependent Variable",value:t.yColumn.name,description:"Outcome variable being predicted",color:"primary",variant:"outlined",icon:e.jsx(Et,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,children:e.jsx(H,{title:"Predictors",value:((Fe=t.xNames)==null?void 0:Fe.length)||0,description:t.xNames&&t.xNames.length>0?t.xNames.slice(0,2).join(", ")+(t.xNames.length>2?"...":""):"No predictors",color:"info",variant:"outlined",icon:e.jsx(rt,{})})})]})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(yt,{fontSize:"small"}),"Model Performance"]}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"R-squared",value:t.rSquared.toFixed(4),description:`${(t.rSquared*100).toFixed(1)}% variance explained`,color:"primary",variant:"gradient",icon:e.jsx(ct,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Adjusted R²",value:(()=>{var h;const i=((h=t.xNames)==null?void 0:h.length)||0,l=t.n;return l-i-1>0&&i>0?(1-(1-t.rSquared)*(l-1)/(l-i-1)).toFixed(4):"N/A"})(),description:"Adjusted for model complexity",color:"secondary",variant:"outlined",icon:e.jsx(Ue,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"F-statistic",value:(()=>{var k;const i=((k=t.xNames)==null?void 0:k.length)||0,l=t.n,h=t.rSquared;return l-i-1>0&&i>0&&h<1?(h/i/((1-h)/(l-i-1))).toFixed(2):"N/A"})(),description:"Overall model significance",color:"warning",variant:"outlined",icon:e.jsx(xt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"p-value",value:t.pValue<.001?"< 0.001":t.pValue.toFixed(4),description:t.pValue<.05?"Statistically significant":"Not significant",color:t.pValue<.05?"success":"error",variant:"outlined",icon:e.jsx(pt,{})})})]})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Additional Statistics"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(H,{title:"Sample Size",value:t.n.toLocaleString(),description:"Valid observations used",color:"info",variant:"outlined",icon:e.jsx(qe,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(H,{title:"Standard Error",value:t.standardErrorOfRegression?t.standardErrorOfRegression.toFixed(4):"N/A",description:"Residual standard error",color:"warning",variant:"outlined",icon:e.jsx(xt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(H,{title:"Degrees of Freedom",value:(()=>{var h;const i=((h=t.xNames)==null?void 0:h.length)||0,l=t.n;return`${i}, ${l-i-1}`})(),description:"Model, Error",color:"info",variant:"outlined",icon:e.jsx(yt,{})})})]})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Regression Coefficients"}),e.jsx(nt,{component:K,elevation:0,variant:"outlined",sx:{borderRadius:2,"& .MuiTableCell-head":{backgroundColor:le(a.palette.primary.main,.1),fontWeight:"bold"}},children:e.jsxs(st,{size:"small",children:[e.jsx(ot,{children:e.jsxs(ue,{children:[e.jsx(p,{children:"Parameter"}),e.jsx(p,{align:"right",children:"Estimate"}),e.jsx(p,{align:"right",children:"Std. Error"}),e.jsx(p,{align:"right",children:"t value"}),e.jsx(p,{align:"right",children:"p-value"}),e.jsx(p,{align:"center",children:"Significance"})]})}),e.jsxs(lt,{children:[e.jsxs(ue,{children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"Intercept"}),e.jsx(p,{align:"right",children:t.intercept.toFixed(4)}),e.jsx(p,{align:"right",children:t.interceptStdError.toFixed(4)}),e.jsx(p,{align:"right",children:(t.intercept/t.interceptStdError).toFixed(4)}),e.jsx(p,{align:"right",children:t.interceptPValue<.001?"< 0.001":t.interceptPValue.toFixed(4)}),e.jsx(p,{align:"center",children:e.jsx(ke,{label:t.interceptPValue<.05?"Significant":"Not Significant",color:t.interceptPValue<.05?"success":"default",size:"small",variant:"outlined"})})]}),t.xNames&&t.coefficients&&t.coefficients.map((i,l)=>e.jsxs(ue,{children:[e.jsx(p,{sx:{fontWeight:"medium"},children:t.xNames[l]||`Predictor ${l+1}`}),e.jsx(p,{align:"right",children:i.toFixed(4)}),e.jsx(p,{align:"right",children:t.stdErrors[l].toFixed(4)}),e.jsx(p,{align:"right",children:(i/t.stdErrors[l]).toFixed(4)}),e.jsx(p,{align:"right",children:t.pValues[l]<.001?"< 0.001":t.pValues[l].toFixed(4)}),e.jsx(p,{align:"center",children:e.jsx(ke,{label:t.pValues[l]<.05?"Significant":"Not Significant",color:t.pValues[l]<.05?"success":"default",size:"small",variant:"outlined"})})]},t.xNames[l]||`predictor-${l}`))]})]})})]}),ie.showRegressionEquation&&e.jsxs(b,{sx:{mb:3},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Regression Equation"}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:3,backgroundColor:le(a.palette.background.default,.5),borderRadius:2},children:e.jsxs(s,{variant:"h6",sx:{fontFamily:"monospace",wordBreak:"break-all",color:a.palette.primary.main,textAlign:"center"},children:[t.yColumn.name," = ",t.intercept.toFixed(4),t.xNames&&t.coefficients&&t.coefficients.map((i,l)=>`${i>=0?" + ":" - "}${Math.abs(i).toFixed(4)} × (${t.xNames[l]||`Predictor ${l+1}`})`)]})})]}),e.jsxs(x,{container:!0,spacing:3,children:[t.scatterData&&t.firstSelectedXColForScatter&&e.jsxs(x,{item:!0,xs:12,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,children:["Regression Plot (vs ",t.firstSelectedXColForScatter.name,")"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:2,borderRadius:2},children:e.jsx(b,{height:400,children:e.jsx(Ye,{width:"100%",height:"100%",children:e.jsxs(ft,{margin:{top:20,right:20,bottom:20,left:20},children:[e.jsx(Qe,{strokeDasharray:"3 3",stroke:le(a.palette.divider,.5)}),e.jsx(Je,{type:"number",dataKey:"x",name:t.firstSelectedXColForScatter.name,label:{value:t.firstSelectedXColForScatter.name,position:"insideBottom",offset:-10}}),e.jsx(Xe,{type:"number",dataKey:"y",name:t.yColumn.name,label:{value:t.yColumn.name,angle:-90,position:"insideLeft"}}),e.jsx(Ze,{cursor:{strokeDasharray:"3 3"},formatter:(i,l)=>[typeof i=="number"?i.toFixed(4):i,l],contentStyle:{backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,borderRadius:8}}),e.jsx(dt,{}),e.jsx(gt,{name:"Data Points",data:t.scatterData,fill:le(a.palette.primary.main,.7)}),ie.showRegressionLine&&e.jsx(at,{type:"monotone",dataKey:"predicted",data:t.scatterData,stroke:a.palette.secondary.main,strokeWidth:3,dot:!1,name:"Regression Line"})]})})})})]}),ie.showResidualPlot&&t.residualPlotData&&e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Residual Plot"}),e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:2,borderRadius:2},children:[e.jsx(b,{height:400,children:e.jsx(Ye,{width:"100%",height:"100%",children:e.jsxs(ft,{data:t.residualPlotData,margin:{top:20,right:30,bottom:60,left:60},children:[e.jsx(Qe,{strokeDasharray:"3 3",stroke:le(a.palette.divider,.5)}),e.jsx(Je,{type:"number",dataKey:"x",name:"Fitted Values",domain:["dataMin - 0.1","dataMax + 0.1"],label:{value:"Fitted Values",position:"insideBottom",offset:-5,style:{textAnchor:"middle",fontSize:"14px",fontWeight:"bold"}},tick:{fontSize:12},tickFormatter:i=>typeof i=="number"?i.toFixed(2):i}),e.jsx(Xe,{type:"number",dataKey:"residual",name:"Residuals",domain:(()=>{const i=t.residualPlotData.map(O=>O.residual),l=Math.min(...i),h=Math.max(...i),F=(h-l)*.1;return[l-F,h+F]})(),label:{value:"Residuals",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fontSize:"14px",fontWeight:"bold"}},tick:{fontSize:12},tickFormatter:i=>typeof i=="number"?i.toFixed(2):i}),e.jsx(Ze,{cursor:{strokeDasharray:"3 3"},formatter:(i,l)=>l==="Fitted Values"?[i.toFixed(3),"Fitted Value"]:l==="Residuals"?[i.toFixed(3),"Residual"]:[i.toFixed(3),l],labelFormatter:i=>`Point ${i}`,contentStyle:{backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,borderRadius:8,fontSize:"12px"}}),e.jsx(dt,{wrapperStyle:{fontSize:"12px"},iconType:"circle"}),e.jsx(Tt,{y:0,stroke:a.palette.mode==="dark"?"#666":"#333",strokeWidth:2,strokeDasharray:"5 5",label:{value:"y = 0",position:"insideTopRight",style:{fontSize:"11px",fill:a.palette.text.secondary}}}),e.jsx(gt,{name:"Residuals",data:t.residualPlotData,fill:a.palette.primary.main,children:t.residualPlotData.map((i,l)=>e.jsx(Qt,{fill:Le(i.standardized),stroke:le(Le(i.standardized),.8),strokeWidth:1},`cell-${l}`))})]})})}),e.jsxs(b,{sx:{mt:2},children:[e.jsxs(s,{variant:"caption",color:"text.secondary",sx:{display:"block",mb:1},children:[e.jsx("strong",{children:"Residual Plot Interpretation:"})," Points should be randomly scattered around the zero line with no clear pattern. Patterns may indicate violations of linearity, homoscedasticity, or independence assumptions."]}),e.jsxs(s,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:[e.jsx("strong",{children:"Color Coding:"}),e.jsx("span",{style:{color:a.palette.primary.main,marginLeft:4},children:"● Normal"}),e.jsxs("span",{style:{color:a.palette.warning.main,marginLeft:8},children:["● Potential outlier (|z| ",">"," 2)"]}),e.jsxs("span",{style:{color:a.palette.error.main,marginLeft:8},children:["● Severe outlier (|z| ",">"," 3)"]})]})]})]})]}),ie.showResidualPlot&&e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Q-Q Plot of Residuals"}),e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:2,borderRadius:2},children:[e.jsx(b,{height:350,children:e.jsx(Ye,{width:"100%",height:"100%",children:e.jsxs(ft,{margin:{top:20,right:20,bottom:20,left:20},children:[e.jsx(Qe,{strokeDasharray:"3 3",stroke:le(a.palette.divider,.5)}),e.jsx(Je,{type:"number",dataKey:"theoretical",name:"Theoretical Quantiles",label:{value:"Theoretical Quantiles",position:"insideBottom",offset:-10}}),e.jsx(Xe,{type:"number",dataKey:"observed",name:"Sample Quantiles",label:{value:"Sample Quantiles",angle:-90,position:"insideLeft"}}),e.jsx(Ze,{cursor:{strokeDasharray:"3 3"},formatter:i=>typeof i=="number"?i.toFixed(4):i,contentStyle:{backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,borderRadius:8}}),e.jsx(gt,{name:"Q-Q Plot",data:t.qqPlotData,fill:le(a.palette.secondary.main,.7)})]})})}),e.jsx(s,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Q-Q plot assesses normality of residuals. Points should follow the diagonal line if residuals are normally distributed."})]})]})]}),e.jsxs(b,{mt:4,children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Prediction Tool"}),e.jsxs(x,{container:!0,spacing:2,alignItems:"flex-start",children:[(()=>{if(!t||!t.originalXColumns)return null;const i=[],l=new Set,h=t.originalXColumns.length,k=Math.max(3,Math.floor(12/Math.max(1,h)));return t.originalXColumns.forEach(F=>{if(F.type===P.NUMERIC)i.push(e.jsx(x,{item:!0,xs:12,sm:6,md:k,children:e.jsx(kt,{label:`Value for ${F.name}`,value:ce[F.id]||"",onChange:O=>ae(F.id,O.target.value),fullWidth:!0,type:"number",margin:"normal"})},F.id));else if(F.type===P.CATEGORICAL&&!l.has(F.id)){const O=w(F.id),ne=re[F.id];i.push(e.jsx(x,{item:!0,xs:12,sm:6,md:k,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:`${F.id}-predict-label`,children:F.name}),e.jsx(be,{labelId:`${F.id}-predict-label`,id:`${F.id}-predict-select`,value:ce[F.id]||"",label:F.name,onChange:R=>ae(F.id,R.target.value),children:O.map(R=>e.jsxs(q,{value:R,children:[R," ",R===ne?"(Base)":""]},R))})]})},F.id)),l.add(F.id)}}),i})(),e.jsx(x,{item:!0,xs:12,sx:{mt:(((Se=t==null?void 0:t.originalXColumns)==null?void 0:Se.length)||0)>0?0:2},children:e.jsxs(Ke,{variant:"contained",color:"primary",startIcon:e.jsx(Mt,{}),onClick:me,disabled:!t||!t.originalXColumns||!t.originalXColumns.every(i=>ce[i.id]!==void 0&&String(ce[i.id]).trim()!==""),sx:{mt:1},children:["Predict ",t.yColumn.name]})}),A&&!A.error&&e.jsx(x,{item:!0,xs:12,children:e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:2,mt:2},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Prediction Result"}),e.jsx(s,{variant:"body1",children:"Input Values:"}),e.jsx("ul",{children:A.inputs.map(i=>e.jsx("li",{children:`${i.name} = ${i.value}`},i.name))}),e.jsx(s,{variant:"body1",fontWeight:"bold",children:`Predicted ${t.yColumn.name} = ${A.predicted.toFixed(4)}`}),A.intervalNote&&e.jsx(s,{variant:"caption",color:"text.secondary",display:"block",mt:1,children:A.intervalNote})]})}),A&&A.error&&e.jsx(x,{item:!0,xs:12,children:e.jsx(ut,{severity:"error",children:A.error})})]})]}),e.jsxs(b,{mt:4,children:[e.jsxs(s,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(He,{}),"Statistical Interpretation & Guidance"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:3,bgcolor:le(a.palette.background.paper,.8),borderRadius:2},children:e.jsx(b,{sx:{"& h2":{fontSize:"1.1rem",fontWeight:600,color:a.palette.primary.main,mt:2,mb:1,"&:first-of-type":{mt:0}},"& h3":{fontSize:"1rem",fontWeight:600,color:a.palette.text.primary,mt:2,mb:1},"& p":{mb:1,lineHeight:1.6},"& ul":{pl:2,mb:1},"& li":{mb:.5}},children:e.jsx(s,{variant:"body2",sx:{whiteSpace:"pre-line","& strong":{fontWeight:600}},dangerouslySetInnerHTML:{__html:De().replace(/## (.*)/g,"<h2>$1</h2>").replace(/### (.*)/g,"<h3>$1</h3>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/• (.*)/g,"• $1").replace(/\n\n/g,"</p><p>").replace(/^\s*/,"<p>").replace(/\s*$/,"</p>")}})})})]})]})})]})},it=Zt(ei),ai=()=>{var $e,je;const{datasets:z,currentDataset:g,setCurrentDataset:te}=St(),a=ht(),[Ce,Me]=L.useState((g==null?void 0:g.id)||""),[G,Ie]=L.useState([]),[J,Ve]=L.useState(""),[Ee,We]=L.useState(.95),[re,Re]=L.useState({}),[ce,we]=L.useState({}),[A,_]=L.useState(null),[ie,et]=L.useState({showConfidenceIntervals:!0,showRegressionEquation:!0,showRegressionCurve:!0,showROCCurve:!0}),[Ae,Pe]=L.useState(!1),[Te,X]=L.useState(null),[t,de]=L.useState(null),[c,$]=L.useState(0);L.useEffect(()=>{const r=localStorage.getItem("logistic_regression_results");if(r)try{const n=JSON.parse(r);de(n)}catch(n){console.error("Error parsing saved logistic regression results:",n),localStorage.removeItem("logistic_regression_results")}},[]);const[m,W]=L.useState(!1),[B,M]=L.useState([]),[w,N]=L.useState({value1:"",value2:"",oneValue:""}),E=(g==null?void 0:g.columns.filter(r=>r.type===P.NUMERIC||r.type===P.CATEGORICAL))||[];g!=null&&g.columns.filter(r=>r.type===P.NUMERIC);const ae=(g==null?void 0:g.columns.filter(r=>{if(r.type!==P.NUMERIC&&r.type!==P.CATEGORICAL)return!1;const n=new Set;let u=0;for(const I of g.data){const j=I[r.name];(j===0||j===1||j==="0"||j==="1"||j===!1||j===!0||j==="false"||j==="true"||j==="False"||j==="True"||j==="FALSE"||j==="TRUE"||j==="no"||j==="yes"||j==="No"||j==="Yes"||j==="NO"||j==="YES")&&(n.add(j),u++)}return u===g.data.length&&n.size<=2}))||[],me=(g==null?void 0:g.columns.filter(r=>{if(r.type!==P.CATEGORICAL&&r.type!==P.BOOLEAN)return!1;const n=new Set;for(const u of g.data){const I=u[r.name];if(I!=null&&n.add(String(I)),n.size>2)return!1}return n.size===2}))||[],xe=[...ae];me.forEach(r=>{xe.some(n=>n.id===r.id)||xe.push(r)});const De=r=>{const n=r.target.value;Me(n),Ie([]),Ve(""),de(null),_(null),localStorage.removeItem("logistic_regression_results");const u=z.find(I=>I.id===n);u&&te(u)},Le=r=>{const n=r.target.value,u=typeof n=="string"?n.split(","):n;Ie(u);const I={...re};Object.keys(re).forEach(j=>{u.includes(j)||delete I[j]}),Re(I),de(null),_(null),localStorage.removeItem("logistic_regression_results")},Fe=r=>{const n=r.target.value;Ve(n),de(null),_(null),localStorage.removeItem("logistic_regression_results");const u=g==null?void 0:g.columns.find(I=>I.id===n);if(u&&(u.type===P.CATEGORICAL||u.type===P.BOOLEAN)&&me.some(I=>I.id===n)){const I=new Set;g!=null&&g.data&&g.data.forEach(v=>{const V=v[u.name];V!=null&&I.add(String(V))});const j=Array.from(I);j.length===2&&(M(j),N({value1:j[0],value2:j[1],oneValue:j[0]}),W(!0))}},Se=r=>{We(Number(r.target.value)),de(null),_(null),localStorage.removeItem("logistic_regression_results")},i=r=>{et(n=>({...n,[r]:!n[r]}))},l=(r,n)=>{we(u=>({...u,[r]:n})),_(null)},h=(r,n)=>{Re(u=>({...u,[r]:n})),de(null),_(null)},k=r=>{if(!g)return[];const n=g.columns.find(u=>u.id===r);return!n||n.type!==P.CATEGORICAL?[]:Vt(r,g)},F=(r,n)=>{$(n)},O=()=>{if(!(!t||!t.xColumns||!t.coefficients))try{const r={};let n=!0;const u={};for(const o of t.xColumns)if(o.isDummy&&o.originalColumnId&&o.originalColumnName&&o.allCategories&&o.baseCategory&&!u[o.originalColumnId]){const y=ce[o.originalColumnId];if(y===void 0||String(y).trim()===""){_({error:`Please select a value for ${o.originalColumnName}`}),n=!1;break}u[o.originalColumnId]={name:o.originalColumnName,selectedValue:String(y),allCategories:o.allCategories,baseCategory:o.baseCategory}}if(!n)return;for(const o of t.xColumns){if(!n)break;if(o.isDummy&&o.originalColumnId&&o.dummyForCategory){const y=u[o.originalColumnId];if(!y){_({error:`Internal error: Missing info for ${o.originalColumnName}`}),n=!1;break}r[o.id]=y.selectedValue===o.dummyForCategory?1:0}else if(!o.isDummy){const y=ce[o.id];if(y===void 0||String(y).trim()===""){_({error:`Please enter a value for ${o.name}`}),n=!1;break}const C=parseFloat(String(y));if(isNaN(C)){_({error:`Invalid number entered for ${o.name}`}),n=!1;break}r[o.id]=C}}if(!n)return;let I=t.intercept;t.coefficients.forEach((o,y)=>{const C=t.xColumns[y];r[C.id]!==void 0?I+=o*r[C.id]:console.error(`Missing processed value for ${C.name} (${C.id}) in logit calculation.`)});const j=1/(1+Math.exp(-I));let v=NaN,V=NaN,S="";if(t.xColumns.length===1&&t.covMatrix&&t.covMatrix.length===2&&t.covMatrix[0].length===2)try{const o=t.xColumns[0].id,y=r[o];if(y===void 0)throw new Error("Processed value for single predictor not found for CI calculation.");const C=[1,y],ee=it.matrix(t.covMatrix),D=it.matrix(C),Q=it.multiply(it.multiply(it.transpose(D),ee),D),se=Math.max(0,Q.get([0,0])),he=Math.sqrt(se),pe=1.96,T=I-pe*he,Y=I+pe*he;v=1/(1+Math.exp(-T)),V=1/(1+Math.exp(-Y))}catch(o){console.error("Error calculating prediction CI for single predictor:",o),S="Error calculating confidence interval."}else t.xColumns.length>1&&(S="Confidence interval calculation is not supported for multiple predictors in this tool.");const f=[];Object.values(u).forEach(o=>{f.push({name:o.name,value:o.selectedValue})}),t.xColumns.forEach(o=>{o.isDummy||(r[o.id]!==void 0?f.push({name:o.name,value:r[o.id]}):console.error(`Missing processed value for numeric display input ${o.name} (${o.id})`))}),_({inputs:f,probability:j,confidenceInterval:[isNaN(v)?0:v,isNaN(V)?1:V],predictedClass:j>=.5?1:0,intervalNote:S||(isNaN(v)?"Confidence interval could not be calculated.":"")})}catch(r){_({error:r instanceof Error?r.message:String(r)})}},ne=()=>{W(!1)},R=r=>{N(n=>({...n,oneValue:r.target.value}))},U=()=>{if(!g||G.length===0||!J){X("Please select at least one independent variable and one dependent variable.");return}Pe(!0),X(null),de(null),_(null);try{const r=G.map(S=>g.columns.find(f=>f.id===S)).filter(Boolean),n=g.columns.find(S=>S.id===J);if(r.length===0||!n)throw new Error("Selected variables not found in dataset.");for(const S of r)if(S.type===P.CATEGORICAL&&!re[S.id])throw new Error(`Please select a base category for the categorical variable "${S.name}".`);const u=[],I=[];r.forEach(S=>{if(S.type===P.NUMERIC)u.push(S),I.push(S.name);else if(S.type===P.CATEGORICAL){const f=re[S.id],o=k(S.id);o.forEach(y=>{if(y!==f){const C=`${S.name} (${y} vs ${f})`;I.push(C),u.push({id:`${S.id}_dummy_${y}`,name:C,type:P.NUMERIC,role:At.INDEPENDENT,description:`Dummy for ${S.name}, category ${y}, base ${f}`,isDummy:!0,originalColumnId:S.id,originalColumnName:S.name,dummyForCategory:y,baseCategory:f,allCategories:o})}})}});const j=[],v=[],V=[];if(g.data.forEach((S,f)=>{let o=S[n.name];if(n.type===P.CATEGORICAL||n.type===P.BOOLEAN){if(w.value1&&w.value2&&w.oneValue)if(String(o)===w.oneValue)o=1;else if(String(o)===(w.oneValue===w.value1?w.value2:w.value1))o=0;else return;else if(typeof o=="string")if(["true","True","TRUE","yes","Yes","YES","1"].includes(o))o=1;else if(["false","False","FALSE","no","No","NO","0"].includes(o))o=0;else return;else if(typeof o=="boolean")o=o?1:0;else if(o!==0&&o!==1)return}else if(o!==0&&o!==1)return;const y=[];let C=!0;r.forEach(ee=>{if(C){if(ee.type===P.NUMERIC){const D=S[ee.name];if(typeof D!="number"||isNaN(D)){C=!1;return}y.push(D)}else if(ee.type===P.CATEGORICAL){const D=re[ee.id],Q=S[ee.name];k(ee.id).forEach(he=>{he!==D&&y.push(Q===he?1:0)})}}}),C&&(j.push(y),v.push(o),V.push(f))}),j.length<10||j.length<I.length+2)throw new Error(`Not enough valid data rows for regression. Need at least ${Math.max(10,I.length+2)} rows after processing categorical variables.`);if(j.length<10)throw new Error("Not enough valid data rows for logistic regression analysis. Need at least 10 valid rows.");try{let S=!1;if(r.length===1&&r[0].type===P.NUMERIC){const T=u.findIndex(Y=>Y.id===r[0].id);if(T!==-1){const Y=j.map((oe,ve)=>({x:oe[T],y:v[ve]}));Y.sort((oe,ve)=>oe.x-ve.x);for(let oe=0;oe<Y.length-1;oe++)if(Y[oe].y!==Y[oe+1].y){const ve=Y[oe].y,ze=Y[oe+1].y,Be=Y.slice(0,oe+1).every(Oe=>Oe.y===ve),Ge=Y.slice(oe+1).every(Oe=>Oe.y===ze);if(Be&&Ge){S=!0;break}}}}if(S)throw new Error("Perfect separation detected. The predictor perfectly separates classes, causing instability.");const f=Yt(j,v),o=r.filter(T=>T.type===P.NUMERIC).map(T=>{const Y=u.findIndex(Z=>Z.id===T.id);if(Y===-1)return null;const oe=j.map(Z=>Z[Y]),ve=Math.min(...oe),ze=Math.max(...oe),Be=ze-ve,Ge=Be*.1,Oe=[];if(r.length===1&&r[0].id===T.id)for(let Z=ve-Ge;Z<=ze+Ge;Z+=Be/100){const tt=f.intercept+f.coefficients[0]*Z,Dt=1/(1+Math.exp(-tt));Oe.push({x:Z,predicted:Dt})}return{variable:T.name,xMin:ve,xMax:ze,curvePoints:Oe,scatterData:j.map((Z,tt)=>({x:Z[Y],y:v[tt],predicted:f.predictions[tt],index:V[tt],variable:T.name}))}}).filter(Boolean),y=o.length>0?o[0]:null,C=y?y.scatterData:[],ee=y?y.curvePoints:[],D=y?y.xMin:0,Q=y?y.xMax:0,he=Array.from({length:101},(T,Y)=>Y/100).map(T=>{const Y=f.predictions.map(Z=>Z>=T?1:0);let oe=0,ve=0,ze=0,Be=0;for(let Z=0;Z<v.length;Z++)v[Z]===1&&Y[Z]===1?oe++:v[Z]===0&&Y[Z]===1?ve++:v[Z]===0&&Y[Z]===0?ze++:v[Z]===1&&Y[Z]===0&&Be++;const Ge=oe+Be>0?oe/(oe+Be):0,Oe=ve+ze>0?ve/(ve+ze):0;return{threshold:T,tpr:Ge,fpr:Oe}});he.sort((T,Y)=>T.fpr-Y.fpr);const pe={...f,xColumns:u,yColumn:n,xColumn:y&&r.find(T=>T.name===y.variable&&T.type===P.NUMERIC)||u[0],scatterData:C,curvePoints:ee,rocPoints:he,xMin:D,xMax:Q,xNames:I};de(pe),localStorage.setItem("logistic_regression_results",JSON.stringify(pe)),Pe(!1)}catch(S){console.error("Logistic regression error:",S),String(S).includes("determinant is zero")||String(S).includes("calculate inverse")?u.length===1?X("Error in regression analysis: Numerical instability detected. This may be due to complete or quasi-complete separation in your data, or multicollinearity if using multiple predictors. Try adding more data points with mixed outcomes, or check for highly correlated predictors."):X("Error in regression analysis: Cannot calculate regression. This could be due to multicollinearity or other numerical issues. Please check your variable selection and data."):String(S).includes("Perfect separation")?X(`Error in regression analysis: ${S instanceof Error?S.message:String(S)}. Try adding more data points with mixed outcomes.`):X(`Error in regression analysis: ${S instanceof Error?S.message:String(S)}`),Pe(!1)}}catch(r){X(`Error in regression analysis: ${r instanceof Error?r.message:String(r)}`),Pe(!1)}},d=()=>{if(!t)return"";const{intercept:r,coefficients:n,interceptPValue:u,pValues:I,n:j,accuracy:v,auc:V,pseudoRSquared:S,yColumn:f,xColumns:o,xNames:y}=t;let C="";const ee=y&&y.length>1?`${y.slice(0,-1).join(", ")} and ${y[y.length-1]}`:(y==null?void 0:y[0])||"";if(C+=`A binary logistic regression was conducted to predict ${f.name} (0/1) based on ${ee}. `,C+=`The model was trained on ${j} observations. `,C+=`

Model fit: The model achieved an accuracy of ${(v*100).toFixed(1)}% and an AUC of ${V.toFixed(3)}. `,C+=`McFadden's pseudo R² was ${S.toFixed(3)}, `,S<.2?C+="indicating a relatively weak relationship between the predictor and outcome. ":S<.4?C+="indicating a moderate relationship between the predictor and outcome. ":C+="indicating a strong relationship between the predictor and outcome. ",C+=`

The regression equation is: logit(p) = ${r.toFixed(4)}`,y&&n)for(let D=0;D<n.length;D++){const Q=n[D],se=y[D]||`X${D+1}`;C+=` ${Q>=0?"+":""} ${Q.toFixed(4)} × ${se}`}if(C+=". ",y&&n&&I)for(let D=0;D<n.length;D++){const Q=n[D],se=y[D]||`X${D+1}`,he=I[D];if(he<.05){C+=`The coefficient for ${se} (${Q.toFixed(4)}) was statistically significant (p ${he<.001?"< 0.001":"= "+he.toFixed(3)}), `;const pe=Math.exp(Q);Q>0?(C+=`indicating that for each one-unit increase in ${se}, the odds of ${f.name} = 1 increase by a factor of ${pe.toFixed(3)} (odds ratio). `,C+=`This means the probability of ${f.name} = 1 increases as ${se} increases. `):(C+=`indicating that for each one-unit increase in ${se}, the odds of ${f.name} = 1 decrease by a factor of ${pe.toFixed(3)} (odds ratio). `,C+=`This means the probability of ${f.name} = 1 decreases as ${se} increases. `)}else C+=`The coefficient for ${se} (${Q.toFixed(4)}) was not statistically significant (p = ${he.toFixed(3)}), `,C+=`suggesting that there is not enough evidence to conclude that ${se} is associated with ${f.name}. `}if(u<.05){C+=`The intercept (${r.toFixed(4)}) was statistically significant (p ${u<.001?"< 0.001":"= "+u.toFixed(3)}). `;const D=1/(1+Math.exp(-r));C+=`It represents the log odds when all predictors are 0, which corresponds to a probability of ${D.toFixed(3)} for ${f.name} = 1. `}else C+=`The intercept (${r.toFixed(4)}) was not statistically significant (p = ${u.toFixed(3)}). `;if(C+=`

Classification performance: `,C+=`At the standard threshold (0.5), the model correctly classified ${(v*100).toFixed(1)}% of cases. `,C+=`The area under the ROC curve (AUC) of ${V.toFixed(3)} `,V<.7?C+="suggests poor to fair discriminative ability. ":V<.8?C+="suggests acceptable discriminative ability. ":V<.9?C+="suggests good discriminative ability. ":C+="suggests excellent discriminative ability. ",C+=`

Practical interpretation: `,y&&y.length===1&&n&&n.length===1){const D=y[0],Q=1/(1+Math.exp(-(r+n[0]*t.xMin))),se=1/(1+Math.exp(-(r+n[0]*t.xMax)));C+=`At the minimum observed value of ${D} (${t.xMin.toFixed(2)}), the predicted probability of ${f.name} = 1 is ${Q.toFixed(3)}. `,C+=`At the maximum observed value of ${D} (${t.xMax.toFixed(2)}), the predicted probability of ${f.name} = 1 is ${se.toFixed(3)}. `}else C+="For multiple predictors, the probability depends on the combination of all predictor values. ";return C};return e.jsxs(b,{p:3,children:[e.jsx(s,{variant:"h5",gutterBottom:!0,children:"Logistic Regression"}),e.jsxs(Bt,{open:m,onClose:ne,children:[e.jsx(Ot,{children:"Map Categorical Values to Binary (0/1)"}),e.jsxs(_t,{children:[e.jsx(s,{variant:"body1",gutterBottom:!0,children:"The selected variable has two categorical values. Please specify which value should be mapped to 1 (the other will be mapped to 0)."}),e.jsx(b,{sx:{mt:2},children:e.jsxs(qt,{value:w.oneValue,onChange:R,children:[e.jsx(ye,{value:w.value1,control:e.jsx($t,{}),label:`Map "${w.value1}" to 1 (and "${w.value2}" to 0)`}),e.jsx(ye,{value:w.value2,control:e.jsx($t,{}),label:`Map "${w.value2}" to 1 (and "${w.value1}" to 0)`})]})})]}),e.jsx(Xt,{children:e.jsx(Ke,{onClick:ne,color:"primary",children:"Confirm"})})]}),e.jsxs(K,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(s,{variant:"subtitle1",gutterBottom:!0,children:"Select Variables"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"dataset-select-label",children:"Dataset"}),e.jsx(be,{labelId:"dataset-select-label",id:"dataset-select",value:Ce,label:"Dataset",onChange:De,disabled:z.length===0,children:z.length===0?e.jsx(q,{value:"",disabled:!0,children:"No datasets available"}):z.map(r=>e.jsxs(q,{value:r.id,children:[r.name," (",r.data.length," rows)"]},r.id))})]})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"independent-variables-label",children:"Independent Variables (X)"}),e.jsx(be,{labelId:"independent-variables-label",id:"independent-variables",multiple:!0,value:G,label:"Independent Variables (X)",onChange:Le,disabled:!g,renderValue:r=>r.map(u=>{const I=E.find(j=>j.id===u);return I?I.name:""}).filter(Boolean).join(", "),children:E.length===0?e.jsx(q,{value:"",disabled:!0,children:"No suitable variables available"}):E.map(r=>e.jsxs(q,{value:r.id,children:[r.name," (",r.type===P.CATEGORICAL?"Categorical":"Numeric",")"]},r.id))}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Select one or more predictor variables (Numeric or Categorical)."})]})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"dependent-variable-label",children:"Dependent Variable (Y - Binary)"}),e.jsx(be,{labelId:"dependent-variable-label",id:"dependent-variable",value:J,label:"Dependent Variable (Y - Binary)",onChange:Fe,disabled:!g,children:xe.length===0?e.jsx(q,{value:"",disabled:!0,children:"No binary variables available"}):xe.map(r=>e.jsxs(q,{value:r.id,children:[r.name," ",r.type===P.CATEGORICAL?"(categorical)":""]},r.id))}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Select a binary (0/1) outcome variable or a categorical variable with exactly two values."})]})})]}),G.some(r=>{var n;return((n=E.find(u=>u.id===r))==null?void 0:n.type)===P.CATEGORICAL})&&e.jsxs(b,{mt:2,p:2,border:1,borderColor:"divider",borderRadius:1,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Select Base Category for Categorical Independent Variables"}),e.jsx(x,{container:!0,spacing:2,children:G.map(r=>{const n=E.find(u=>u.id===r);if(n&&n.type===P.CATEGORICAL){const u=k(r);return e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsxs(fe,{id:`base-category-label-${r}`,children:["Base for ",n.name]}),e.jsx(be,{labelId:`base-category-label-${r}`,value:re[r]||"",label:"Base for {column.name}",onChange:I=>h(r,I.target.value),MenuProps:{disableScrollLock:!0},children:u.length===0?e.jsx(q,{value:"",disabled:!0,children:"No categories found"}):u.map(I=>e.jsx(q,{value:I,children:I},I))})]})},r)}return null})})]}),e.jsx(b,{mt:1,children:e.jsx(x,{container:!0,spacing:2,children:e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:"conf-interval-label",children:"Confidence Level"}),e.jsxs(be,{labelId:"conf-interval-label",id:"conf-interval",value:Ee,label:"Confidence Level",onChange:Se,MenuProps:{disableScrollLock:!0},children:[e.jsx(q,{value:.9,children:"90%"}),e.jsx(q,{value:.95,children:"95%"}),e.jsx(q,{value:.99,children:"99%"})]})]})})})}),e.jsxs(b,{mt:2,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showRegressionCurve,onChange:()=>i("showRegressionCurve")}),label:"Show regression curve"}),e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showConfidenceIntervals,onChange:()=>i("showConfidenceIntervals")}),label:"Show confidence intervals"})]}),e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showRegressionEquation,onChange:()=>i("showRegressionEquation")}),label:"Show regression equation"}),e.jsx(ye,{control:e.jsx(Ne,{checked:ie.showROCCurve,onChange:()=>i("showROCCurve")}),label:"Show ROC curve"})]})]})]}),e.jsx(b,{mt:2,children:e.jsx(Ke,{variant:"contained",color:"primary",startIcon:e.jsx(rt,{}),onClick:U,disabled:Ae||G.length===0||!J||G.includes(J),children:"Run Logistic Regression"})})]}),Ae&&e.jsx(b,{display:"flex",justifyContent:"center",my:4,children:e.jsx(Ct,{})}),Te&&e.jsx(ut,{severity:"error",sx:{mb:3},children:Te}),t&&!Ae&&e.jsx(e.Fragment,{children:e.jsxs(K,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(b,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:e.jsxs(Pt,{value:c,onChange:F,"aria-label":"logistic regression tabs",children:[e.jsx(_e,{label:"Model Summary"}),e.jsx(_e,{label:"Visualizations"}),e.jsx(_e,{label:"Classification Results"}),e.jsx(_e,{label:"Prediction"}),e.jsx(_e,{label:"Interpretation"})]})}),c===0&&e.jsxs(b,{children:[e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsx(s,{variant:"h6",children:"Logistic Regression Results"}),e.jsx(ke,{icon:e.jsx(qe,{}),label:`n = ${t.n.toLocaleString()}`,color:"primary",variant:"outlined"})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(He,{fontSize:"small"}),"Model Overview"]}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,children:e.jsx(H,{title:"Dependent Variable",value:`${t.yColumn.name} (Binary)`,description:"Binary outcome variable being predicted",color:"primary",variant:"outlined",icon:e.jsx(Et,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,children:e.jsx(H,{title:"Predictors",value:(($e=t.xNames)==null?void 0:$e.length)||0,description:t.xNames&&t.xNames.length>0?t.xNames.slice(0,2).join(", ")+(t.xNames.length>2?"...":""):"No predictors",color:"secondary",variant:"outlined",icon:e.jsx(mt,{})})})]})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(yt,{fontSize:"small"}),"Model Performance"]}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Pseudo R²",value:t.pseudoRSquared.toFixed(4),description:`${(t.pseudoRSquared*100).toFixed(1)}% explained deviance`,color:"primary",variant:"gradient",icon:e.jsx(ct,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Log Likelihood",value:t.logLikelihood.toFixed(2),description:"Model fit statistic",color:"info",variant:"outlined",icon:e.jsx(xt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"AIC",value:t.aic.toFixed(2),description:"Akaike Information Criterion",color:"warning",variant:"outlined",icon:e.jsx(Gt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Accuracy",value:`${(t.accuracy*100).toFixed(1)}%`,description:"Classification accuracy",color:t.accuracy>.7?"success":t.accuracy>.6?"warning":"error",variant:"outlined",icon:e.jsx(rt,{})})})]})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Additional Statistics"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(H,{title:"Sample Size",value:t.n.toLocaleString(),description:"Valid observations used",color:"info",variant:"outlined",icon:e.jsx(qe,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(H,{title:"AUC",value:t.auc.toFixed(3),description:"Area Under ROC Curve",color:t.auc>.8?"success":t.auc>.7?"warning":"error",variant:"outlined",icon:e.jsx(pt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(H,{title:"Deviance",value:(-2*t.logLikelihood).toFixed(2),description:"Model deviance (-2 × Log Likelihood)",color:"secondary",variant:"outlined",icon:e.jsx(Ue,{})})})]})]}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ue,{fontSize:"small"}),"Model Coefficients"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsx(nt,{children:e.jsxs(st,{children:[e.jsx(ot,{children:e.jsxs(ue,{sx:{backgroundColor:a.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.04)"},children:[e.jsx(p,{sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Parameter"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Estimate"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Std. Error"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"z-value"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"p-value"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Odds Ratio"}),e.jsx(p,{align:"center",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Significance"})]})}),e.jsxs(lt,{children:[e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"Intercept"}),e.jsx(p,{align:"right",children:t.intercept.toFixed(4)}),e.jsx(p,{align:"right",children:t.interceptStdError.toFixed(4)}),e.jsx(p,{align:"right",children:(t.intercept/t.interceptStdError).toFixed(4)}),e.jsx(p,{align:"right",children:t.interceptPValue<.001?"< 0.001":t.interceptPValue.toFixed(4)}),e.jsx(p,{align:"right",children:Math.exp(t.intercept).toFixed(4)}),e.jsx(p,{align:"center",children:e.jsx(ke,{label:t.interceptPValue<.001?"***":t.interceptPValue<.01?"**":t.interceptPValue<.05?"*":"ns",size:"small",color:t.interceptPValue<.05?"success":"default",variant:t.interceptPValue<.05?"filled":"outlined"})})]}),t.coefficients&&t.xNames&&t.coefficients.map((r,n)=>{var u;return e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:((u=t.xNames)==null?void 0:u[n])||`X${n+1}`}),e.jsx(p,{align:"right",children:r.toFixed(4)}),e.jsx(p,{align:"right",children:t.stdErrors[n].toFixed(4)}),e.jsx(p,{align:"right",children:(r/t.stdErrors[n]).toFixed(4)}),e.jsx(p,{align:"right",children:t.pValues[n]<.001?"< 0.001":t.pValues[n].toFixed(4)}),e.jsx(p,{align:"right",children:Math.exp(r).toFixed(4)}),e.jsx(p,{align:"center",children:e.jsx(ke,{label:t.pValues[n]<.001?"***":t.pValues[n]<.01?"**":t.pValues[n]<.05?"*":"ns",size:"small",color:t.pValues[n]<.05?"success":"default",variant:t.pValues[n]<.05?"filled":"outlined"})})]},n)})]})]})})}),e.jsxs(s,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:[e.jsx("strong",{children:"Significance codes:"})," *** p ","<"," 0.001, ** p ","<"," 0.01, * p ","<"," 0.05, ns = not significant"]})]}),ie.showRegressionEquation&&e.jsx(b,{sx:{mb:3},children:e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:3,borderRadius:2,backgroundColor:a.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ue,{fontSize:"small"}),"Logistic Regression Equation"]}),e.jsxs(s,{variant:"h6",sx:{fontFamily:"monospace",textAlign:"center",my:2,color:a.palette.primary.main},children:["logit(p) = ",t.intercept.toFixed(4),t.coefficients&&t.xNames&&t.coefficients.map((r,n)=>{var I;return`${r>=0?" + ":" - "}${Math.abs(r).toFixed(4)} × ${((I=t.xNames)==null?void 0:I[n])||`X${n+1}`}`})]}),e.jsxs(s,{variant:"body2",color:"text.secondary",sx:{textAlign:"center"},children:["where logit(p) = ln(p / (1 - p)) and p is the probability of ",t.yColumn.name," = 1"]})]})})]}),c===1&&e.jsxs(b,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Logistic Regression Visualizations"}),e.jsxs(x,{container:!0,spacing:3,children:[e.jsxs(x,{item:!0,xs:12,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ct,{fontSize:"small"}),"Probability Curve"]}),t.xColumns.length>1&&e.jsxs(s,{variant:"caption",display:"block",gutterBottom:!0,color:"text.secondary",children:["Displaying relationship for the first numeric predictor: ",t.xColumn.name]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:2,borderRadius:2},children:e.jsx(b,{height:400,children:e.jsx(Ye,{width:"100%",height:"100%",children:e.jsxs(bt,{margin:{top:20,right:30,bottom:60,left:70},children:[e.jsx(Qe,{strokeDasharray:"3 3"}),e.jsx(Je,{type:"number",dataKey:"x",name:t.xColumn.name,domain:["dataMin","dataMax"],tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:t.xColumn.name,position:"insideBottom",offset:-25,style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px"}}}),e.jsx(Xe,{type:"number",domain:[0,1],tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:`Probability of ${t.yColumn.name} = 1`,angle:-90,position:"insideLeft",style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px",textAnchor:"middle"},dx:-20}}),e.jsx(Ze,{cursor:{strokeDasharray:"3 3"},formatter:(r,n)=>n==="y"?[r>.5?"Yes/True/1":"No/False/0",`Actual ${t.yColumn.name}`]:n==="predicted"?[r.toFixed(4),"Predicted Probability"]:[r.toFixed(4),n],contentStyle:{backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,borderRadius:8,fontSize:"12px"}}),e.jsx(dt,{verticalAlign:"top",wrapperStyle:{paddingBottom:"10px"}}),e.jsx(gt,{name:"Observations",dataKey:"y",data:t.scatterData.map(r=>({...r,y:r.y===1?1-Math.random()*.05:0+Math.random()*.05})),fill:a.palette.primary.main}),ie.showRegressionCurve&&e.jsx(at,{type:"monotone",dataKey:"predicted",data:t.curvePoints,stroke:a.palette.secondary.main,strokeWidth:2,dot:!1,name:"Logistic Curve",activeDot:!1}),e.jsx(Tt,{y:.5,stroke:a.palette.warning.main,strokeDasharray:"3 3",label:{value:"Classification Threshold (0.5)",position:"right",fill:a.palette.warning.main}})]})})})})]}),ie.showROCCurve&&e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(pt,{fontSize:"small"}),"ROC Curve"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:2,borderRadius:2},children:e.jsx(b,{height:350,children:e.jsx(Ye,{width:"100%",height:"100%",children:e.jsxs(bt,{data:t.rocPoints,margin:{top:20,right:30,bottom:60,left:70},children:[e.jsx(Qe,{strokeDasharray:"3 3"}),e.jsx(Je,{type:"number",dataKey:"fpr",name:"False Positive Rate",domain:[0,1],tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:"False Positive Rate (1 - Specificity)",position:"insideBottom",offset:-25,style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px"}}}),e.jsx(Xe,{type:"number",dataKey:"tpr",name:"True Positive Rate",domain:[0,1],tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:"True Positive Rate (Sensitivity)",angle:-90,position:"insideLeft",style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px",textAnchor:"middle"},dx:-20}}),e.jsx(Ze,{cursor:{strokeDasharray:"3 3"},formatter:(r,n,u)=>{if(u.payload.threshold!==void 0){const I=n.startsWith("ROC")?"Sensitivity":n;return[`${r.toFixed(3)} (${I})`,`Threshold: ${u.payload.threshold.toFixed(2)}`]}return[r.toFixed(3),n]},contentStyle:{backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,borderRadius:8,fontSize:"12px"}}),e.jsx(dt,{verticalAlign:"top",wrapperStyle:{paddingBottom:"10px"}}),e.jsx(at,{type:"linear",dataKey:"fpr",stroke:a.palette.text.disabled,strokeWidth:1,strokeDasharray:"5 5",dot:!1,name:"Random Chance",legendType:"none"}),e.jsx(at,{type:"monotone",dataKey:"tpr",stroke:a.palette.success.main,strokeWidth:2,dot:{r:2},name:`ROC Curve (AUC = ${t.auc.toFixed(3)})`,activeDot:{r:6}})]})})})})]}),e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(mt,{fontSize:"small"}),"Predicted Probabilities"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:2,borderRadius:2},children:e.jsx(b,{height:350,children:e.jsx(Ye,{width:"100%",height:"100%",children:e.jsxs(bt,{margin:{top:20,right:70,bottom:60,left:70},children:[e.jsx(Qe,{strokeDasharray:"3 3"}),e.jsx(Je,{type:"number",dataKey:"x",domain:[0,1],name:"Predicted Probability",tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:"Predicted Probability",position:"insideBottom",offset:-25,style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px"}}}),e.jsx(Xe,{yAxisId:"left",tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:"Frequency",angle:-90,position:"insideLeft",style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px",textAnchor:"middle"},dx:-20}}),e.jsx(Xe,{yAxisId:"right",orientation:"right",domain:[0,1],tick:{fill:a.palette.text.secondary,fontSize:12},label:{value:"Actual Class Proportion",angle:90,position:"insideRight",style:{fill:a.palette.text.primary,fontWeight:"bold",fontSize:"14px",textAnchor:"middle"},dx:25}}),e.jsx(Ze,{contentStyle:{backgroundColor:a.palette.background.paper,border:`1px solid ${a.palette.divider}`,borderRadius:8,fontSize:"12px"}}),e.jsx(dt,{}),(()=>{const n=Array(10).fill(0).map((u,I)=>({bin:I/10,x:(I+.5)/10,count:0,actualProportion:0,trueCount:0}));return t.predictions.forEach((u,I)=>{var v;const j=Math.min(Math.floor(u*10),9);n[j].count++,t.scatterData&&((v=t.scatterData[I])==null?void 0:v.y)===1&&n[j].trueCount++}),n.forEach(u=>{u.actualProportion=u.count>0?u.trueCount/u.count:0}),e.jsxs(e.Fragment,{children:[e.jsx(Jt,{yAxisId:"left",dataKey:"count",data:n,fill:a.palette.primary.light,name:"Count",barSize:20}),e.jsx(at,{yAxisId:"right",type:"monotone",dataKey:"actualProportion",data:n,stroke:a.palette.success.main,strokeWidth:2,name:"Actual Proportion"})]})})()]})})})})]})]})]}),c===2&&e.jsxs(b,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Classification Performance"}),e.jsxs(b,{sx:{mb:3},children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(rt,{fontSize:"small"}),"Performance Metrics"]}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Accuracy",value:`${(t.accuracy*100).toFixed(1)}%`,description:"Overall correct classification rate",color:t.accuracy>.8?"success":t.accuracy>.7?"warning":"error",variant:"gradient",icon:e.jsx(rt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Precision",value:`${(t.precision*100).toFixed(1)}%`,description:"Positive predictions that are correct",color:t.precision>.8?"success":t.precision>.7?"warning":"error",variant:"outlined",icon:e.jsx(pt,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"Recall",value:`${(t.recall*100).toFixed(1)}%`,description:"Actual positives correctly identified",color:t.recall>.8?"success":t.recall>.7?"warning":"error",variant:"outlined",icon:e.jsx(ct,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(H,{title:"F1 Score",value:`${(t.f1Score*100).toFixed(1)}%`,description:"Harmonic mean of precision and recall",color:t.f1Score>.8?"success":t.f1Score>.7?"warning":"error",variant:"outlined",icon:e.jsx(mt,{})})})]})]}),e.jsxs(x,{container:!0,spacing:3,children:[e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ue,{fontSize:"small"}),"Confusion Matrix"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsx(nt,{children:e.jsxs(st,{children:[e.jsxs(ot,{children:[e.jsxs(ue,{sx:{backgroundColor:a.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.04)"},children:[e.jsx(p,{sx:{fontWeight:"bold"}}),e.jsx(p,{colSpan:2,align:"center",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Predicted"})]}),e.jsxs(ue,{sx:{backgroundColor:a.palette.mode==="dark"?"rgba(255, 255, 255, 0.03)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(p,{sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Actual"}),e.jsx(p,{align:"center",sx:{fontWeight:"bold"},children:"0"}),e.jsx(p,{align:"center",sx:{fontWeight:"bold"},children:"1"})]})]}),e.jsxs(lt,{children:[e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"0"}),e.jsxs(p,{align:"center",sx:{bgcolor:a.palette.mode==="dark"?"rgba(76, 175, 80, 0.2)":"rgba(76, 175, 80, 0.1)",fontWeight:"bold",border:`2px solid ${a.palette.success.main}`,borderRadius:1},children:[t.confusionMatrix[0][0]," (TN)"]}),e.jsxs(p,{align:"center",sx:{bgcolor:a.palette.mode==="dark"?"rgba(244, 67, 54, 0.2)":"rgba(244, 67, 54, 0.1)",border:`1px solid ${a.palette.error.main}`,borderRadius:1},children:[t.confusionMatrix[0][1]," (FP)"]})]}),e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"1"}),e.jsxs(p,{align:"center",sx:{bgcolor:a.palette.mode==="dark"?"rgba(244, 67, 54, 0.2)":"rgba(244, 67, 54, 0.1)",border:`1px solid ${a.palette.error.main}`,borderRadius:1},children:[t.confusionMatrix[1][0]," (FN)"]}),e.jsxs(p,{align:"center",sx:{bgcolor:a.palette.mode==="dark"?"rgba(76, 175, 80, 0.2)":"rgba(76, 175, 80, 0.1)",fontWeight:"bold",border:`2px solid ${a.palette.success.main}`,borderRadius:1},children:[t.confusionMatrix[1][1]," (TP)"]})]})]})]})})}),e.jsxs(s,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:[e.jsx("strong",{children:"Legend:"})," TN = True Negative, FP = False Positive, FN = False Negative, TP = True Positive"]})]}),e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsxs(s,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(mt,{fontSize:"small"}),"Detailed Metrics"]}),e.jsx(K,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsx(nt,{children:e.jsxs(st,{children:[e.jsx(ot,{children:e.jsxs(ue,{sx:{backgroundColor:a.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.04)"},children:[e.jsx(p,{sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Metric"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Value"}),e.jsx(p,{sx:{fontWeight:"bold",color:a.palette.primary.main},children:"Description"})]})}),e.jsxs(lt,{children:[e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"Accuracy"}),e.jsxs(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.primary.main},children:[(t.accuracy*100).toFixed(2),"%"]}),e.jsx(p,{children:"Overall correct classification rate"})]}),e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"Precision"}),e.jsxs(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.secondary.main},children:[(t.precision*100).toFixed(2),"%"]}),e.jsx(p,{children:"Proportion of positive predictions that are correct"})]}),e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"Recall (Sensitivity)"}),e.jsxs(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.info.main},children:[(t.recall*100).toFixed(2),"%"]}),e.jsx(p,{children:"Proportion of actual positives correctly identified"})]}),e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"Specificity"}),e.jsxs(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.warning.main},children:[(t.confusionMatrix[0][0]/(t.confusionMatrix[0][0]+t.confusionMatrix[0][1])*100).toFixed(2),"%"]}),e.jsx(p,{children:"Proportion of actual negatives correctly identified"})]}),e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"F1 Score"}),e.jsxs(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.success.main},children:[(t.f1Score*100).toFixed(2),"%"]}),e.jsx(p,{children:"Harmonic mean of precision and recall"})]}),e.jsxs(ue,{sx:{"&:hover":{backgroundColor:a.palette.action.hover}},children:[e.jsx(p,{sx:{fontWeight:"medium"},children:"AUC"}),e.jsx(p,{align:"right",sx:{fontWeight:"bold",color:a.palette.error.main},children:t.auc.toFixed(3)}),e.jsx(p,{children:"Area under the ROC curve, measures discrimination"})]})]})]})})})]})]})]}),c===3&&e.jsxs(b,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Prediction Tool"}),e.jsxs(x,{container:!0,spacing:2,alignItems:"flex-start",children:[(()=>{if(!t||!t.xColumns)return null;const r=[],n=new Set,u=new Set;t.xColumns.forEach(v=>{v.isDummy&&v.originalColumnId?u.add(v.originalColumnId):v.isDummy||u.add(v.id)});const I=u.size,j=Math.max(3,Math.floor(12/Math.max(1,I)));return t.xColumns.forEach(v=>{v.isDummy&&v.originalColumnId&&v.originalColumnName&&v.allCategories&&v.baseCategory?n.has(v.originalColumnId)||(r.push(e.jsx(x,{item:!0,xs:12,sm:6,md:j,children:e.jsxs(ge,{fullWidth:!0,margin:"normal",children:[e.jsx(fe,{id:`${v.originalColumnId}-predict-label`,children:v.originalColumnName}),e.jsx(be,{labelId:`${v.originalColumnId}-predict-label`,id:`${v.originalColumnId}-predict-select`,value:ce[v.originalColumnId]||"",label:v.originalColumnName,onChange:V=>l(v.originalColumnId,V.target.value),children:v.allCategories.map(V=>e.jsxs(q,{value:V,children:[V," ",V===v.baseCategory?"(Base)":""]},V))})]})},v.originalColumnId)),n.add(v.originalColumnId)):v.isDummy||r.push(e.jsx(x,{item:!0,xs:12,sm:6,md:j,children:e.jsx(kt,{label:`Value for ${v.name}`,value:ce[v.id]||"",onChange:V=>l(v.id,V.target.value),fullWidth:!0,type:"number",margin:"normal"})},v.id))}),r})(),e.jsx(x,{item:!0,xs:12,sx:{mt:(((je=t==null?void 0:t.xColumns)==null?void 0:je.length)||0)>0?0:2},children:e.jsx(Ke,{variant:"contained",color:"primary",startIcon:e.jsx(Mt,{}),onClick:O,disabled:!t||!(()=>{if(!(t!=null&&t.xColumns))return!0;let r=!0;const n=new Set;return t.xColumns.forEach(u=>{u.isDummy&&u.originalColumnId?n.add(u.originalColumnId):u.isDummy||(ce[u.id]===void 0||String(ce[u.id]).trim()==="")&&(r=!1)}),n.forEach(u=>{(ce[u]===void 0||String(ce[u]).trim()==="")&&(r=!1)}),r})(),sx:{mt:1},children:"Predict Probability"})}),A&&!A.error&&e.jsx(x,{item:!0,xs:12,children:e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:2,mt:2},children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Prediction Result"}),e.jsx("div",{children:e.jsxs(s,{variant:"body1",component:"div",children:["Input Values:",e.jsx("ul",{children:A.inputs.map(r=>e.jsx("li",{children:`${r.name} = ${r.value}`},r.name))})]})}),e.jsx(s,{variant:"body1",fontWeight:"bold",color:A.predictedClass===1?"success.main":"inherit",children:`Predicted Probability = ${A.probability.toFixed(4)}`}),e.jsx(s,{variant:"body1",children:`Predicted Class: ${A.predictedClass===1?"1 (Yes)":"0 (No)"}`}),e.jsxs(s,{variant:"body2",color:"text.secondary",children:["95% Confidence Interval: [",A.confidenceInterval[0].toFixed(4),", ",A.confidenceInterval[1].toFixed(4),"]"]}),A.intervalNote&&e.jsx(s,{variant:"caption",color:"text.secondary",display:"block",mt:1,children:A.intervalNote})]})}),A&&A.error&&e.jsx(x,{item:!0,xs:12,md:4,children:e.jsx(ut,{severity:"error",children:A.error})})]}),t&&e.jsxs(b,{mt:4,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Interpreting Predictions"}),e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(s,{variant:"body2",children:'• A predicted probability of 0.5 or higher means the model predicts class "1" (Yes/True).'}),e.jsx(s,{variant:"body2",children:'• A predicted probability below 0.5 means the model predicts class "0" (No/False).'}),e.jsx(s,{variant:"body2",children:"• The confidence interval represents uncertainty around the predicted probability."}),e.jsx(s,{variant:"body2",children:"• Predictions far from the range of observed values may be less reliable."})]})]})]}),c===4&&e.jsxs(b,{children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Interpretation"}),e.jsx(K,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:e.jsx(s,{variant:"body2",sx:{whiteSpace:"pre-line"},children:d()})}),e.jsxs(b,{mt:4,children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Key Logistic Regression Concepts"}),e.jsxs(K,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(s,{variant:"body2",fontWeight:"bold",children:"Logit Transformation"}),e.jsx(s,{variant:"body2",paragraph:!0,children:"Logistic regression models the log odds (logit) of the outcome as a linear function of the predictors: logit(p) = ln(p/(1-p)) = β₀ + β₁X₁ + ... + βₙXₙ"}),e.jsx(s,{variant:"body2",fontWeight:"bold",children:"Odds Ratio"}),e.jsx(s,{variant:"body2",paragraph:!0,children:"The exponential of a coefficient (e^β) is the odds ratio, which represents how the odds change for a one-unit increase in the predictor. An odds ratio more than 1 indicates increased odds of the outcome, while less than 1 indicates decreased odds."}),e.jsx(s,{variant:"body2",fontWeight:"bold",children:"Prediction"}),e.jsx(s,{variant:"body2",paragraph:!0,children:"To convert from log odds to probability: p = 1 / (1 + e^(-logit))"}),e.jsx(s,{variant:"body2",fontWeight:"bold",children:"AUC (Area Under Curve)"}),e.jsx(s,{variant:"body2",children:"AUC measures the model's ability to discriminate between positive and negative classes. Values range from 0.5 (no discrimination) to 1.0 (perfect discrimination)."})]})]})]})]})})]})},jt=z=>{const{children:g,value:te,index:a,...Ce}=z;return e.jsx("div",{role:"tabpanel",hidden:te!==a,id:`correlation-tabpanel-${a}`,"aria-labelledby":`correlation-tab-${a}`,...Ce,style:{width:"100%"},children:te===a&&e.jsx(b,{sx:{pt:2},children:g})})},vt=z=>({id:`correlation-tab-${z}`,"aria-controls":`correlation-tabpanel-${z}`}),wt={pearson:0,linear:1,regression:1,logistic:2},bi=({initialTab:z=""})=>{const g=ht(),[te,a]=L.useState(0);L.useEffect(()=>{z&&wt[z]!==void 0&&a(wt[z])},[z]);const Ce=(Me,G)=>{a(G)};return e.jsx(b,{sx:{width:"100%"},children:e.jsxs(K,{elevation:1,sx:{width:"100%"},children:[e.jsxs(Pt,{value:te,onChange:Ce,"aria-label":"Correlation Analysis Tabs",variant:"fullWidth",sx:{borderBottom:1,borderColor:"divider",backgroundColor:g.palette.background.paper},children:[e.jsx(_e,{icon:e.jsx(Ft,{}),label:"Correlation Matrix",...vt(0),sx:{py:2}}),e.jsx(_e,{icon:e.jsx(ct,{}),label:"Linear Regression",...vt(1),sx:{py:2}}),e.jsx(_e,{icon:e.jsx(Ue,{}),label:"Logistic Regression",...vt(2),sx:{py:2}})]}),e.jsxs(b,{sx:{p:0},children:[e.jsx(jt,{value:te,index:0,children:e.jsx(ii,{})}),e.jsx(jt,{value:te,index:1,children:e.jsx(ri,{})}),e.jsx(jt,{value:te,index:2,children:e.jsx(ai,{})})]})]})})};export{bi as default};
