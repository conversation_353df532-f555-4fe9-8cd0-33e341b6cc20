import{u as he,j as e,B as i,aN as ae,e as t,g as X,f as y,bS as se,bT as te,bU as ne,ah as T,ai as M,b9 as U,ba as H,bb as u,bV as ie,aj as _e,bc as Ye,Q as ge,G as C,k as G,l as K,h as B,aI as ye,bv as Xe,J as Je,bw as es,cX as ss,bW as ts,bX as ns,o as Ae,aD as is,bY as as,L as de,m as ue,r as xe,R as k,b_ as rs,I as je,bj as ls,D as os,cY as ve,bu as cs,by as fe,cy as ce,cZ as be}from"./mui-libs-CfwFIaTD.js";import{r as d}from"./react-libs-Cr2nE3UY.js";import{t as o,i as Se}from"./trainingDataInitializer-DhlysJr9.js";const ds=({onClose:w})=>{const f=he(),[a,b]=d.useState("questions"),[j,m]=d.useState([]),[I,V]=d.useState([]),[P,L]=d.useState([]),[O,R]=d.useState(o.getStats()),[g,F]=d.useState({question:"",keywords:"",patterns:"",category:"other",difficulty:"basic",context:""}),[v,c]=d.useState({questionId:"",analysisId:"",priority:"medium",reason:"",prerequisites:"",alternatives:""}),[A,N]=d.useState({name:"",description:"",selectedQuestions:[]}),[_,we]=d.useState(null),[Te,J]=d.useState(!1),[Ie,ee]=d.useState(!1),[De,re]=d.useState(!1),[ms,qe]=d.useState(null),[Ee,le]=d.useState(!1),[p,Qe]=d.useState(null),[oe,S]=d.useState(null),Y=[{id:"DESC1",name:"Descriptive Analysis"},{id:"DESC2",name:"Frequency Tables"},{id:"DESC3",name:"Cross-Tabulation"},{id:"DESC4",name:"Normality Test"},{id:"CORR1",name:"Correlation Matrix"},{id:"REG1",name:"Linear Regression"},{id:"REG2",name:"Logistic Regression"},{id:"TTEST1",name:"One-Sample T-Test"},{id:"TTEST2",name:"Independent T-Test"},{id:"TTEST3",name:"Paired T-Test"},{id:"ANOVA1",name:"One-Way ANOVA"},{id:"ANOVA2",name:"Two-Way ANOVA"},{id:"ANOVA3",name:"Repeated Measures ANOVA"},{id:"CAT1",name:"Chi-Square Test"},{id:"NONPAR1",name:"Mann-Whitney U Test"},{id:"NONPAR2",name:"Wilcoxon Signed-Rank Test"},{id:"NONPAR3",name:"Kruskal-Wallis Test"},{id:"NONPAR4",name:"Friedman Test"},{id:"ADV1",name:"Exploratory Factor Analysis"},{id:"ADV2",name:"Confirmatory Factor Analysis"},{id:"ADV3",name:"Reliability Analysis"},{id:"ADV4",name:"Survival Analysis"},{id:"ADV5",name:"Cluster Analysis"},{id:"ADV6",name:"Meta Analysis"},{id:"EPI1",name:"Case-Control Calculator"},{id:"EPI2",name:"Cohort Calculator"},{id:"SS1",name:"One Sample Calculator"},{id:"SS2",name:"Two Sample Calculator"},{id:"PUB1",name:"Table 1 Generator"},{id:"PUB2",name:"Table 1a Generator"},{id:"PUB3",name:"SMD Table"},{id:"PUB4",name:"Regression Table"},{id:"VIZ1",name:"Bar Chart"},{id:"VIZ2",name:"Histogram"},{id:"VIZ3",name:"Box Plot"},{id:"VIZ4",name:"Scatter Plot"},{id:"VIZ5",name:"Correlation Heatmap"}];d.useEffect(()=>{W()},[]);const W=()=>{m(o.getAllQuestions()),V(o.getAllSessions()),L(o.getCuratedSuggestions()),R(o.getStats())},Re=()=>{if(!g.question.trim()){S({message:"Question text is required",type:"error"});return}const s=g.keywords.split(",").map(h=>h.trim()).filter(h=>h),l=g.patterns.split(`
`).map(h=>h.trim()).filter(h=>h),r={question:g.question,keywords:s,patterns:l,category:g.category,difficulty:g.difficulty,context:g.context||void 0};_?(o.updateQuestion(_,r),we(null),S({message:"Question updated successfully",type:"success"})):(o.addQuestion(r),S({message:"Question added successfully",type:"success"})),F({question:"",keywords:"",patterns:"",category:"other",difficulty:"basic",context:""}),J(!1),W()},ke=()=>{if(!v.questionId||!v.analysisId||!v.reason.trim()){S({message:"Question, analysis, and reason are required",type:"error"});return}const s=v.prerequisites.split(",").map(r=>r.trim()).filter(r=>r),l=v.alternatives.split(",").map(r=>r.trim()).filter(r=>r);o.addAnswer({questionId:v.questionId,analysisId:v.analysisId,priority:v.priority,reason:v.reason,prerequisites:s.length>0?s:void 0,alternatives:l.length>0?l:void 0,validated:!0}),c({questionId:"",analysisId:"",priority:"medium",reason:"",prerequisites:"",alternatives:""}),re(!1),qe(null),S({message:"Answer added successfully",type:"success"}),W()},Pe=()=>{if(!A.name.trim()){S({message:"Session name is required",type:"error"});return}const s=o.createSession(A.name,A.description);A.selectedQuestions.forEach(l=>{o.addQuestionToSession(s,l)}),N({name:"",description:"",selectedQuestions:[]}),ee(!1),S({message:"Training session created successfully",type:"success"}),W()},Ne=()=>{o.getAllQuestions().forEach(l=>{var q;const h=o.getAnswersForQuestion(l.id).filter(E=>E.validated);if(h.length>0){const E=h.map(z=>({analysisId:z.analysisId,priority:z.priority==="high"?8:z.priority==="medium"?5:3,reason:z.reason,confidence:.9})),x=((q=l.patterns)==null?void 0:q.map(z=>{try{return new RegExp(z,"i")}catch{return new RegExp(z.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"i")}}))||[],$={questionPattern:l.question,keywords:l.keywords,regexPatterns:x,suggestions:E,category:l.category,validated:!0};o.addCuratedSuggestion($)}}),W(),S({message:"Curated suggestions generated successfully",type:"success"})},Oe=()=>{const s=o.exportTrainingData(),l=new Blob([s],{type:"application/json"}),r=URL.createObjectURL(l),h=document.createElement("a");h.href=r,h.download=`analysis-assistant-training-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(r),S({message:"Training data exported successfully",type:"success"})},We=s=>{var h;const l=(h=s.target.files)==null?void 0:h[0];if(!l)return;const r=new FileReader;r.onload=q=>{var x;const E=(x=q.target)==null?void 0:x.result;o.importTrainingData(E)?(W(),S({message:"Training data imported successfully",type:"success"})):S({message:"Failed to import training data",type:"error"})},r.readAsText(l)},$e=()=>{window.confirm("Are you sure you want to clear all training data? This action cannot be undone.")&&(o.clearAllData(),W(),S({message:"All training data cleared",type:"success"}))},Be=()=>{var pe;const[s,l]=d.useState({question:(p==null?void 0:p.question)||"",keywords:(p==null?void 0:p.keywords.join(", "))||"",patterns:((pe=p==null?void 0:p.patterns)==null?void 0:pe.join(`
`))||"",category:(p==null?void 0:p.category)||"other",difficulty:(p==null?void 0:p.difficulty)||"basic",context:(p==null?void 0:p.context)||""}),[r,h]=d.useState([]),[q,E]=d.useState(null),[x,$]=d.useState({analysisId:"",priority:"medium",reason:"",prerequisites:"",alternatives:""});d.useEffect(()=>{if(p){const n=o.getAnswersForQuestion(p.id);h(n)}},[p]);const z=()=>{if(!s.question.trim()){S({message:"Question text is required",type:"error"});return}const n=s.keywords.split(",").map(D=>D.trim()).filter(D=>D),Q=s.patterns.split(`
`).map(D=>D.trim()).filter(D=>D);p&&(o.updateQuestion(p.id,{question:s.question,keywords:n,patterns:Q,category:s.category,difficulty:s.difficulty,context:s.context||void 0}),S({message:"Question updated successfully",type:"success"})),W()},Me=n=>{if(window.confirm("Are you sure you want to delete this answer?")){o.deleteAnswer(n);const Q=r.filter(D=>D.id!==n);h(Q),S({message:"Answer deleted successfully",type:"success"}),W()}},Ue=n=>{var Q,D;E(n.id),$({analysisId:n.analysisId,priority:n.priority,reason:n.reason,prerequisites:((Q=n.prerequisites)==null?void 0:Q.join(", "))||"",alternatives:((D=n.alternatives)==null?void 0:D.join(", "))||""})},He=()=>{if(!x.analysisId||!x.reason.trim()){S({message:"Analysis and reason are required",type:"error"});return}const n=x.prerequisites.split(",").map(Z=>Z.trim()).filter(Z=>Z),Q=x.alternatives.split(",").map(Z=>Z.trim()).filter(Z=>Z);q?(o.updateAnswer(q,{analysisId:x.analysisId,priority:x.priority,reason:x.reason,prerequisites:n.length>0?n:void 0,alternatives:Q.length>0?Q:void 0}),S({message:"Answer updated successfully",type:"success"})):(o.addAnswer({questionId:p.id,analysisId:x.analysisId,priority:x.priority,reason:x.reason,prerequisites:n.length>0?n:void 0,alternatives:Q.length>0?Q:void 0,validated:!0}),S({message:"Answer added successfully",type:"success"})),E(null),$({analysisId:"",priority:"medium",reason:"",prerequisites:"",alternatives:""});const D=o.getAnswersForQuestion(p.id);h(D),W()},Ze=()=>{E(null),$({analysisId:"",priority:"medium",reason:"",prerequisites:"",alternatives:""})},Ge=n=>{const Q=Y.find(D=>D.id===n);return Q?Q.name:n},Ke=n=>{switch(n){case"high":return"error";case"medium":return"warning";case"low":return"info";default:return"default"}};return e.jsxs(se,{open:Ee,onClose:()=>le(!1),maxWidth:"lg",fullWidth:!0,PaperProps:{sx:{height:"90vh"}},children:[e.jsx(te,{children:e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(ae,{color:"primary"}),e.jsx(t,{variant:"h6",children:p?"Edit Question & Answers":"Question Editor"})]})}),e.jsx(ne,{sx:{p:0},children:e.jsxs(i,{sx:{display:"flex",height:"100%"},children:[e.jsxs(i,{sx:{width:"40%",p:3,borderRight:1,borderColor:"divider"},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Question Details"}),e.jsx(T,{label:"Question",multiline:!0,rows:3,value:s.question,onChange:n=>l({...s,question:n.target.value}),fullWidth:!0,sx:{mb:2}}),e.jsx(T,{label:"Keywords (comma-separated)",value:s.keywords,onChange:n=>l({...s,keywords:n.target.value}),fullWidth:!0,sx:{mb:2},helperText:"Keywords for matching user queries"}),e.jsx(T,{label:"Patterns (one per line)",multiline:!0,rows:3,value:s.patterns,onChange:n=>l({...s,patterns:n.target.value}),fullWidth:!0,sx:{mb:2},helperText:"Optional regex patterns for matching"}),e.jsxs(i,{sx:{display:"flex",gap:2,mb:2},children:[e.jsxs(M,{sx:{minWidth:150},children:[e.jsx(U,{children:"Category"}),e.jsxs(H,{value:s.category,onChange:n=>l({...s,category:n.target.value}),children:[e.jsx(u,{value:"correlation",children:"Correlation"}),e.jsx(u,{value:"comparison",children:"Comparison"}),e.jsx(u,{value:"categorical",children:"Categorical"}),e.jsx(u,{value:"descriptive",children:"Descriptive"}),e.jsx(u,{value:"prediction",children:"Prediction"}),e.jsx(u,{value:"test",children:"Test"}),e.jsx(u,{value:"visualization",children:"Visualization"}),e.jsx(u,{value:"other",children:"Other"})]})]}),e.jsxs(M,{sx:{minWidth:150},children:[e.jsx(U,{children:"Difficulty"}),e.jsxs(H,{value:s.difficulty,onChange:n=>l({...s,difficulty:n.target.value}),children:[e.jsx(u,{value:"basic",children:"Basic"}),e.jsx(u,{value:"intermediate",children:"Intermediate"}),e.jsx(u,{value:"advanced",children:"Advanced"})]})]})]}),e.jsx(T,{label:"Context (optional)",multiline:!0,rows:2,value:s.context,onChange:n=>l({...s,context:n.target.value}),fullWidth:!0,sx:{mb:2}}),e.jsx(y,{variant:"contained",onClick:z,fullWidth:!0,sx:{mt:2},children:"Save Question"})]}),e.jsxs(i,{sx:{width:"60%",p:3},children:[e.jsxs(t,{variant:"h6",gutterBottom:!0,children:["Answers (",r.length,")"]}),e.jsx(i,{sx:{mb:3,maxHeight:"300px",overflow:"auto"},children:r.map(n=>e.jsx(G,{variant:"outlined",sx:{mb:2},children:e.jsxs(K,{sx:{pb:1},children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:1},children:[e.jsxs(i,{children:[e.jsxs(t,{variant:"subtitle2",color:"primary",children:[Ge(n.analysisId)," (",n.analysisId,")"]}),e.jsx(B,{label:n.priority,size:"small",color:Ke(n.priority),sx:{mt:.5}})]}),e.jsxs(i,{children:[e.jsx(je,{size:"small",onClick:()=>Ue(n),children:e.jsx(ye,{fontSize:"small"})}),e.jsx(je,{size:"small",onClick:()=>Me(n.id),color:"error",children:e.jsx(ls,{fontSize:"small"})})]})]}),e.jsx(t,{variant:"body2",sx:{mb:1},children:n.reason}),n.prerequisites&&n.prerequisites.length>0&&e.jsxs(t,{variant:"caption",color:"text.secondary",children:["Prerequisites: ",n.prerequisites.join(", ")]}),n.alternatives&&n.alternatives.length>0&&e.jsxs(t,{variant:"caption",color:"text.secondary",display:"block",children:["Alternatives: ",n.alternatives.join(", ")]})]})},n.id))}),e.jsx(os,{sx:{my:2}}),e.jsx(t,{variant:"subtitle1",gutterBottom:!0,children:q?"Edit Answer":"Add New Answer"}),e.jsxs(M,{fullWidth:!0,sx:{mb:2},children:[e.jsx(U,{children:"Analysis"}),e.jsx(H,{value:x.analysisId,onChange:n=>$({...x,analysisId:n.target.value}),children:Y.map(n=>e.jsxs(u,{value:n.id,children:[n.name," (",n.id,")"]},n.id))})]}),e.jsxs(M,{fullWidth:!0,sx:{mb:2},children:[e.jsx(U,{children:"Priority"}),e.jsxs(H,{value:x.priority,onChange:n=>$({...x,priority:n.target.value}),children:[e.jsx(u,{value:"high",children:"High"}),e.jsx(u,{value:"medium",children:"Medium"}),e.jsx(u,{value:"low",children:"Low"})]})]}),e.jsx(T,{label:"Reason",multiline:!0,rows:2,value:x.reason,onChange:n=>$({...x,reason:n.target.value}),fullWidth:!0,sx:{mb:2},required:!0}),e.jsx(T,{label:"Prerequisites (comma-separated)",value:x.prerequisites,onChange:n=>$({...x,prerequisites:n.target.value}),fullWidth:!0,sx:{mb:2},helperText:"Optional: conditions that must be met"}),e.jsx(T,{label:"Alternatives (comma-separated analysis IDs)",value:x.alternatives,onChange:n=>$({...x,alternatives:n.target.value}),fullWidth:!0,sx:{mb:2},helperText:"Optional: alternative analysis suggestions"}),e.jsxs(i,{sx:{display:"flex",gap:1},children:[e.jsx(y,{variant:"contained",onClick:He,disabled:!x.analysisId||!x.reason.trim(),children:q?"Update Answer":"Add Answer"}),q&&e.jsx(y,{onClick:Ze,children:"Cancel Edit"})]})]})]})}),e.jsx(ie,{children:e.jsx(y,{onClick:()=>le(!1),children:"Close"})})]})},Ve=()=>e.jsxs(i,{children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",mb:3},children:[e.jsxs(t,{variant:"h6",children:["Training Questions (",j.length,")"]}),e.jsx(y,{variant:"contained",startIcon:e.jsx(ge,{}),onClick:()=>J(!0),children:"Add Question"})]}),e.jsx(C,{container:!0,spacing:2,children:j.map(s=>e.jsx(C,{item:!0,xs:12,md:6,children:e.jsx(G,{variant:"outlined",children:e.jsxs(K,{children:[e.jsx(t,{variant:"subtitle1",gutterBottom:!0,children:s.question}),e.jsxs(i,{sx:{mb:2},children:[e.jsx(B,{label:s.category,size:"small",sx:{mr:1}}),e.jsx(B,{label:s.difficulty,size:"small",variant:"outlined"})]}),e.jsxs(t,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Keywords: ",s.keywords.join(", ")]}),s.context&&e.jsxs(t,{variant:"body2",color:"text.secondary",children:["Context: ",s.context]}),(()=>{const l=o.getAnswersForQuestion(s.id);return l.length>0&&e.jsxs(i,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1},children:[e.jsxs(t,{variant:"caption",color:"text.secondary",sx:{fontWeight:500},children:["Answers (",l.length,"):"]}),e.jsxs(i,{sx:{mt:1,display:"flex",flexWrap:"wrap",gap:.5},children:[l.slice(0,3).map((r,h)=>{var E;const q=((E=Y.find(x=>x.id===r.analysisId))==null?void 0:E.name)||r.analysisId;return e.jsx(B,{label:`${q} (${r.priority})`,size:"small",variant:"outlined",color:r.priority==="high"?"error":r.priority==="medium"?"warning":"info"},h)}),l.length>3&&e.jsx(B,{label:`+${l.length-3} more`,size:"small",variant:"outlined",color:"default"})]})]})})(),e.jsxs(i,{sx:{mt:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(y,{variant:"contained",size:"small",startIcon:e.jsx(ye,{}),onClick:()=>{Qe(s),le(!0)},sx:{minWidth:120},children:"Edit Question"}),e.jsxs(t,{variant:"caption",color:"text.secondary",children:[s.difficulty," • ",s.category]})]})]})})},s.id))})]}),Fe=()=>e.jsxs(i,{children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",mb:3},children:[e.jsxs(t,{variant:"h6",children:["Training Sessions (",I.length,")"]}),e.jsx(y,{variant:"contained",startIcon:e.jsx(ge,{}),onClick:()=>ee(!0),children:"Create Session"})]}),e.jsx(C,{container:!0,spacing:2,children:I.map(s=>e.jsx(C,{item:!0,xs:12,md:6,children:e.jsx(G,{variant:"outlined",children:e.jsxs(K,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:s.name}),e.jsx(t,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:s.description}),e.jsx(i,{sx:{mb:2},children:e.jsx(B,{label:s.status,size:"small",color:s.status==="completed"?"success":s.status==="active"?"primary":"default"})}),e.jsx(Xe,{variant:"determinate",value:s.completedQuestions.length/s.questions.length*100,sx:{mb:1}}),e.jsxs(t,{variant:"caption",color:"text.secondary",children:["Progress: ",s.completedQuestions.length,"/",s.questions.length," questions"]}),e.jsxs(t,{variant:"caption",display:"block",color:"text.secondary",children:["Created: ",new Date(s.createdAt).toLocaleDateString()]})]})})},s.id))})]}),ze=()=>e.jsxs(i,{children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",mb:3},children:[e.jsxs(t,{variant:"h6",children:["Curated Suggestions (",P.length,")"]}),e.jsxs(i,{sx:{display:"flex",gap:1},children:[e.jsx(y,{variant:"outlined",startIcon:e.jsx(Je,{}),onClick:Ne,children:"Generate from Training"}),e.jsx(y,{variant:"contained",startIcon:e.jsx(es,{}),onClick:Oe,children:"Export Data"}),e.jsx("input",{type:"file",accept:".json",style:{display:"none"},id:"import-file",onChange:We}),e.jsx("label",{htmlFor:"import-file",children:e.jsx(y,{variant:"outlined",startIcon:e.jsx(ss,{}),component:"span",children:"Import Data"})})]})]}),e.jsx(C,{container:!0,spacing:2,children:P.map((s,l)=>e.jsx(C,{item:!0,xs:12,children:e.jsxs(ts,{children:[e.jsx(ns,{expandIcon:e.jsx(is,{}),children:e.jsxs(i,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(t,{variant:"subtitle1",sx:{flexGrow:1},children:s.questionPattern}),e.jsxs(i,{sx:{display:"flex",gap:1,mr:2},children:[e.jsx(B,{label:s.category,size:"small"}),s.validated&&e.jsx(Ae,{color:"success"})]})]})}),e.jsx(as,{children:e.jsxs(i,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Keywords:"})," ",s.keywords.join(", ")]}),e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Usage:"})," ",s.usage_count," times, Success Rate: ",(s.success_rate*100).toFixed(1),"%"]}),e.jsx(t,{variant:"body2",children:e.jsx("strong",{children:"Suggestions:"})}),e.jsx(de,{dense:!0,children:s.suggestions.map((r,h)=>{var q;return e.jsx(ue,{children:e.jsx(xe,{primary:((q=Y.find(E=>E.id===r.analysisId))==null?void 0:q.name)||r.analysisId,secondary:`Priority: ${r.priority}/10, Confidence: ${(r.confidence*100).toFixed(0)}% - ${r.reason}`})},h)})})]})})]})},l))})]}),Le=()=>e.jsxs(i,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Training Statistics"}),e.jsxs(C,{container:!0,spacing:3,children:[e.jsx(C,{item:!0,xs:12,md:6,children:e.jsxs(k,{sx:{p:3},children:[e.jsx(t,{variant:"h4",color:"primary",gutterBottom:!0,children:O.totalQuestions}),e.jsx(t,{variant:"body1",children:"Total Questions"})]})}),e.jsx(C,{item:!0,xs:12,md:6,children:e.jsxs(k,{sx:{p:3},children:[e.jsx(t,{variant:"h4",color:"primary",gutterBottom:!0,children:O.totalAnswers}),e.jsx(t,{variant:"body1",children:"Total Answers"})]})}),e.jsx(C,{item:!0,xs:12,md:6,children:e.jsxs(k,{sx:{p:3},children:[e.jsx(t,{variant:"h4",color:"success.main",gutterBottom:!0,children:O.validatedAnswers}),e.jsx(t,{variant:"body1",children:"Validated Answers"})]})}),e.jsx(C,{item:!0,xs:12,md:6,children:e.jsxs(k,{sx:{p:3},children:[e.jsxs(t,{variant:"h4",color:"info.main",gutterBottom:!0,children:[O.accuracyScore.toFixed(1),"%"]}),e.jsx(t,{variant:"body1",children:"Accuracy Score"})]})}),e.jsx(C,{item:!0,xs:12,md:6,children:e.jsxs(k,{sx:{p:3},children:[e.jsx(t,{variant:"h4",color:"secondary.main",gutterBottom:!0,children:O.sessionsCompleted}),e.jsx(t,{variant:"body1",children:"Sessions Completed"})]})}),e.jsx(C,{item:!0,xs:12,md:6,children:e.jsxs(k,{sx:{p:3},children:[e.jsx(t,{variant:"body1",gutterBottom:!0,children:"Last Training"}),e.jsx(t,{variant:"h6",children:new Date(O.lastTrainingDate).toLocaleDateString()})]})})]}),e.jsxs(i,{sx:{mt:4},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Data Management"}),e.jsx(i,{sx:{display:"flex",gap:2},children:e.jsx(y,{variant:"outlined",color:"error",startIcon:e.jsx(rs,{}),onClick:$e,children:"Clear All Data"})})]})]});return e.jsxs(i,{sx:{p:3},children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(ae,{sx:{mr:2,color:f.palette.primary.main}}),e.jsx(t,{variant:"h4",children:"Analysis Assistant Training System"})]}),oe&&e.jsx(X,{severity:oe.type,sx:{mb:3},onClose:()=>S(null),children:oe.message}),e.jsx(i,{sx:{borderBottom:1,borderColor:"divider",mb:3},children:e.jsx(i,{sx:{display:"flex",gap:2},children:[{value:"questions",label:"Questions"},{value:"sessions",label:"Sessions"},{value:"curated",label:"Curated"},{value:"stats",label:"Statistics"}].map(s=>e.jsx(y,{variant:a===s.value?"contained":"text",onClick:()=>b(s.value),children:s.label},s.value))})}),a==="questions"&&Ve(),a==="sessions"&&Fe(),a==="curated"&&ze(),a==="stats"&&Le(),e.jsxs(se,{open:Te,onClose:()=>J(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(te,{children:_?"Edit Question":"Add New Question"}),e.jsx(ne,{children:e.jsxs(i,{sx:{display:"flex",flexDirection:"column",gap:2,mt:1},children:[e.jsx(T,{label:"Question",multiline:!0,rows:3,value:g.question,onChange:s=>F({...g,question:s.target.value}),fullWidth:!0}),e.jsx(T,{label:"Keywords (comma-separated)",value:g.keywords,onChange:s=>F({...g,keywords:s.target.value}),fullWidth:!0,helperText:"e.g., correlation, relationship, association"}),e.jsx(T,{label:"Regex Patterns (one per line)",multiline:!0,rows:3,value:g.patterns,onChange:s=>F({...g,patterns:s.target.value}),fullWidth:!0,helperText:"Optional regex patterns for matching"}),e.jsxs(i,{sx:{display:"flex",gap:2},children:[e.jsxs(M,{sx:{minWidth:150},children:[e.jsx(U,{children:"Category"}),e.jsxs(H,{value:g.category,onChange:s=>F({...g,category:s.target.value}),children:[e.jsx(u,{value:"correlation",children:"Correlation"}),e.jsx(u,{value:"comparison",children:"Comparison"}),e.jsx(u,{value:"categorical",children:"Categorical"}),e.jsx(u,{value:"descriptive",children:"Descriptive"}),e.jsx(u,{value:"prediction",children:"Prediction"}),e.jsx(u,{value:"test",children:"Test"}),e.jsx(u,{value:"visualization",children:"Visualization"}),e.jsx(u,{value:"other",children:"Other"})]})]}),e.jsxs(M,{sx:{minWidth:150},children:[e.jsx(U,{children:"Difficulty"}),e.jsxs(H,{value:g.difficulty,onChange:s=>F({...g,difficulty:s.target.value}),children:[e.jsx(u,{value:"basic",children:"Basic"}),e.jsx(u,{value:"intermediate",children:"Intermediate"}),e.jsx(u,{value:"advanced",children:"Advanced"})]})]})]}),e.jsx(T,{label:"Context (optional)",multiline:!0,rows:2,value:g.context,onChange:s=>F({...g,context:s.target.value}),fullWidth:!0,helperText:"Additional context about when this question applies"})]})}),e.jsxs(ie,{children:[e.jsx(y,{onClick:()=>J(!1),children:"Cancel"}),e.jsxs(y,{onClick:Re,variant:"contained",children:[_?"Update":"Add"," Question"]})]})]}),e.jsxs(se,{open:De,onClose:()=>re(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(te,{children:"Add Answer for Question"}),e.jsx(ne,{children:e.jsxs(i,{sx:{display:"flex",flexDirection:"column",gap:2,mt:1},children:[e.jsxs(M,{fullWidth:!0,children:[e.jsx(U,{children:"Analysis"}),e.jsx(H,{value:v.analysisId,onChange:s=>c({...v,analysisId:s.target.value}),children:Y.map(s=>e.jsx(u,{value:s.id,children:s.name},s.id))})]}),e.jsxs(M,{children:[e.jsx(U,{children:"Priority"}),e.jsxs(H,{value:v.priority,onChange:s=>c({...v,priority:s.target.value}),children:[e.jsx(u,{value:"high",children:"High"}),e.jsx(u,{value:"medium",children:"Medium"}),e.jsx(u,{value:"low",children:"Low"})]})]}),e.jsx(T,{label:"Reason",multiline:!0,rows:3,value:v.reason,onChange:s=>c({...v,reason:s.target.value}),fullWidth:!0,required:!0,helperText:"Explain why this analysis is recommended"}),e.jsx(T,{label:"Prerequisites (comma-separated)",value:v.prerequisites,onChange:s=>c({...v,prerequisites:s.target.value}),fullWidth:!0,helperText:"Optional: conditions that must be met"}),e.jsx(T,{label:"Alternatives (comma-separated analysis IDs)",value:v.alternatives,onChange:s=>c({...v,alternatives:s.target.value}),fullWidth:!0,helperText:"Optional: alternative analysis suggestions"})]})}),e.jsxs(ie,{children:[e.jsx(y,{onClick:()=>re(!1),children:"Cancel"}),e.jsx(y,{onClick:ke,variant:"contained",children:"Add Answer"})]})]}),e.jsxs(se,{open:Ie,onClose:()=>ee(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(te,{children:"Create Training Session"}),e.jsx(ne,{children:e.jsxs(i,{sx:{display:"flex",flexDirection:"column",gap:2,mt:1},children:[e.jsx(T,{label:"Session Name",value:A.name,onChange:s=>N({...A,name:s.target.value}),fullWidth:!0,required:!0}),e.jsx(T,{label:"Description",multiline:!0,rows:3,value:A.description,onChange:s=>N({...A,description:s.target.value}),fullWidth:!0}),e.jsx(t,{variant:"subtitle2",children:"Select Questions for Session:"}),e.jsx(i,{sx:{maxHeight:300,overflow:"auto"},children:j.map(s=>e.jsx(_e,{control:e.jsx(Ye,{checked:A.selectedQuestions.includes(s.id),onChange:l=>{l.target.checked?N({...A,selectedQuestions:[...A.selectedQuestions,s.id]}):N({...A,selectedQuestions:A.selectedQuestions.filter(r=>r!==s.id)})}}),label:e.jsxs(i,{children:[e.jsx(t,{variant:"body2",children:s.question}),e.jsxs(t,{variant:"caption",color:"text.secondary",children:[s.category," • ",s.difficulty]})]})},s.id))})]})}),e.jsxs(ie,{children:[e.jsx(y,{onClick:()=>ee(!1),children:"Cancel"}),e.jsx(y,{onClick:Pe,variant:"contained",children:"Create Session"})]})]}),e.jsx(Be,{})]})},me=()=>{Se();const w=[{query:"How do I test if age is correlated with test scores?",expectedAnalyses:["CORR1","VIZ4","REG1"],description:"Should suggest correlation analysis, not normality tests"},{query:"What test for categorical data?",expectedAnalyses:["CAT1","DESC2","DESC3"],description:"Should suggest categorical data analyses"},{query:"Test correlation between variables",expectedAnalyses:["CORR1","VIZ4"],description:"Should suggest correlation analysis tools"},{query:"Compare means between two groups",expectedAnalyses:["TTEST2","NONPAR1"],description:"Should suggest group comparison tests"},{query:"Is my data normally distributed?",expectedAnalyses:["NORM1","VIZ2","DESC1"],description:"Should suggest normality assessment tools"},{query:"Check data quality and missing values",expectedAnalyses:["DESC1"],description:"Should suggest comprehensive data quality assessment"},{query:"Detect outliers in my data",expectedAnalyses:["DESC1","VIZ3","VIZ4"],description:"Should suggest outlier detection methods"},{query:"Assess overall data quality",expectedAnalyses:["DESC1"],description:"Should suggest comprehensive quality assessment"},{query:"I have variables with similar names like item1, item2, item3",expectedAnalyses:["REL1","FACTOR1","DESC1"],description:"Should recognize scale items and suggest reliability analysis"},{query:"I have pre/post or before/after variables",expectedAnalyses:["TTEST3","NONPAR2"],description:"Should recognize paired data structure"},{query:"I have survey data with Likert scales",expectedAnalyses:["DESC1","REL1","NONPAR1"],description:"Should recognize survey context and suggest appropriate analyses"},{query:"I have experimental data with treatment groups",expectedAnalyses:["TTEST2","ANOVA1","DESC1"],description:"Should recognize experimental design"},{query:"Analyze longitudinal data with repeated measures",expectedAnalyses:["ANOVA3","TTEST3"],description:"Should suggest repeated measures analysis"},{query:"Compare multiple treatments with control group",expectedAnalyses:["ANOVA1"],description:"Should suggest ANOVA with post-hoc comparisons"},{query:"Analyze survival data",expectedAnalyses:["ADV4"],description:"Should suggest survival analysis for time-to-event data"},{query:"Perform factor analysis on my questionnaire data",expectedAnalyses:["ADV1","ADV3"],description:"Should suggest factor analysis and reliability assessment"},{query:"Test reliability of my scale",expectedAnalyses:["ADV3"],description:"Should suggest reliability analysis for scale assessment"},{query:"Calculate odds ratio for case-control study",expectedAnalyses:["EPI1"],description:"Should suggest epidemiological case-control analysis"},{query:"Calculate sample size for my study",expectedAnalyses:["SS1","SS2"],description:"Should suggest sample size calculation tools"},{query:"Create Table 1 for my manuscript",expectedAnalyses:["PUB1"],description:"Should suggest publication-ready table generation"},{query:"How do I compare means between two groups? My data is non normal",expectedAnalyses:["NONPAR1"],description:"Should prioritize non-parametric test over t-test when data is non-normal"},{query:"Compare paired data but my data is not normally distributed",expectedAnalyses:["NONPAR2"],description:"Should suggest non-parametric alternative for paired non-normal data"},{query:"Compare multiple groups but data is not normal",expectedAnalyses:["NONPAR3"],description:"Should suggest non-parametric alternative for multiple group comparison"}],f=[];return w.forEach(a=>{console.log(`
Testing query: "${a.query}"`);const b=o.findMatchingSuggestions(a.query),j=[];b.forEach(P=>{P.suggestions.forEach(L=>{j.includes(L.analysisId)||j.push(L.analysisId)})});const m=a.expectedAnalyses.filter(P=>j.includes(P)),I=m.length/a.expectedAnalyses.length,V=I>=.5;console.log(`Expected: ${a.expectedAnalyses.join(", ")}`),console.log(`Found: ${j.join(", ")}`),console.log(`Score: ${(I*100).toFixed(1)}% (${m.length}/${a.expectedAnalyses.length})`),console.log(`Status: ${V?"PASS":"FAIL"}`),f.push({query:a.query,expectedAnalyses:a.expectedAnalyses,actualSuggestions:b,passed:V,score:I})}),f},Ce=w=>{const f=w.length,a=w.filter(m=>m.passed).length,b=w.reduce((m,I)=>m+I.score,0)/f;let j=`
# Training System Test Report

## Summary
- Total Tests: ${f}
- Passed: ${a}
- Failed: ${f-a}
- Success Rate: ${(a/f*100).toFixed(1)}%
- Average Score: ${(b*100).toFixed(1)}%

## Detailed Results

`;return w.forEach((m,I)=>{j+=`### Test ${I+1}: ${m.passed?"✅ PASS":"❌ FAIL"}
**Query:** "${m.query}"
**Expected:** ${m.expectedAnalyses.join(", ")}
**Found:** ${m.actualSuggestions.length} suggestions
**Score:** ${(m.score*100).toFixed(1)}%

`}),j},us=()=>{console.log(`🧪 Starting Training System Test...
`);const w=me(),f=Ce(w);console.log(f),console.log(`
🔧 Testing Database Functionality...`);const a=o.getStats();console.log(`Database Stats:
- Questions: ${a.totalQuestions}
- Answers: ${a.totalAnswers}
- Validated Answers: ${a.validatedAnswers}
- Accuracy Score: ${a.accuracyScore.toFixed(1)}%
`);const b=o.getCuratedSuggestions();console.log(`Curated Suggestions: ${b.length}`),console.log(`
✅ Training System Test Complete!`)},xs=w=>{console.log(`
🔍 Testing Query: "${w}"`);const f=o.findMatchingSuggestions(w);console.log(`Found ${f.length} curated suggestions:`),f.forEach((a,b)=>{console.log(`
${b+1}. Pattern: "${a.questionPattern}"`),console.log(`   Keywords: ${a.keywords.join(", ")}`),console.log(`   Category: ${a.category}`),console.log(`   Validated: ${a.validated?"Yes":"No"}`),console.log(`   Usage: ${a.usage_count} times, Success: ${(a.success_rate*100).toFixed(1)}%`),a.suggestions.forEach((j,m)=>{console.log(`   ${m+1}. ${j.analysisId} (Priority: ${j.priority}, Confidence: ${(j.confidence*100).toFixed(0)}%)`),console.log(`      Reason: ${j.reason}`)})})};typeof window<"u"&&(window.testTrainingSystem={runComprehensiveTest:us,testSpecificQuery:xs,testTrainingSystem:me,generateTestReport:Ce});const hs=()=>{const w=he(),[f,a]=d.useState(""),[b,j]=d.useState([]),[m,I]=d.useState([]),[V,P]=d.useState(!1),L={CORR1:"Correlation Matrix",VIZ4:"Scatter Plot",REG1:"Linear Regression",CAT1:"Chi-Square Test",DESC2:"Frequency Tables",DESC3:"Cross-Tabulation",TTEST2:"Independent T-Test",NONPAR1:"Mann-Whitney U Test",DESC1:"Descriptive Analysis",TTEST3:"Paired T-Test",NONPAR2:"Wilcoxon Signed-Rank Test",ANOVA1:"One-Way ANOVA",NONPAR3:"Kruskal-Wallis Test",NORM1:"Normality Tests",VIZ2:"Histograms and Q-Q plots"},O=["How do I test if age is correlated with test scores?","What test for categorical data?","Test correlation between variables","Compare means between two groups","Is my data normally distributed?"];d.useEffect(()=>{Se(),P(!0)},[]);const R=()=>{if(!f.trim())return;const c=o.findMatchingSuggestions(f);j(c)},g=()=>{const c=me();I(c)},F=c=>{a(c);const A=o.findMatchingSuggestions(c);j(A)},v=()=>o.getStats();return e.jsxs(i,{sx:{p:3},children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(ae,{sx:{mr:2,color:w.palette.primary.main}}),e.jsx(t,{variant:"h4",children:"Training System Demo"})]}),V?e.jsxs(e.Fragment,{children:[e.jsx(G,{sx:{mb:3},children:e.jsxs(K,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Training Database Stats"}),e.jsxs(C,{container:!0,spacing:2,children:[e.jsx(C,{item:!0,xs:3,children:e.jsxs(k,{sx:{p:2,textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"primary",children:v().totalQuestions}),e.jsx(t,{variant:"body2",children:"Questions"})]})}),e.jsx(C,{item:!0,xs:3,children:e.jsxs(k,{sx:{p:2,textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"primary",children:v().totalAnswers}),e.jsx(t,{variant:"body2",children:"Answers"})]})}),e.jsx(C,{item:!0,xs:3,children:e.jsxs(k,{sx:{p:2,textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"success.main",children:v().validatedAnswers}),e.jsx(t,{variant:"body2",children:"Validated"})]})}),e.jsx(C,{item:!0,xs:3,children:e.jsxs(k,{sx:{p:2,textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"info.main",children:o.getCuratedSuggestions().length}),e.jsx(t,{variant:"body2",children:"Curated"})]})})]})]})}),e.jsx(G,{sx:{mb:3},children:e.jsxs(K,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Test Individual Query"}),e.jsxs(i,{sx:{display:"flex",gap:2,mb:2},children:[e.jsx(T,{label:"Enter your statistical question",value:f,onChange:c=>a(c.target.value),fullWidth:!0,placeholder:"e.g., How do I test if age is correlated with test scores?"}),e.jsx(y,{variant:"contained",onClick:R,disabled:!f.trim(),startIcon:e.jsx(ve,{}),children:"Test"})]}),e.jsx(t,{variant:"subtitle2",gutterBottom:!0,children:"Quick Tests (Problematic Queries):"}),e.jsx(i,{sx:{display:"flex",flexWrap:"wrap",gap:1,mb:2},children:O.map((c,A)=>e.jsx(B,{label:c,onClick:()=>F(c),clickable:!0,variant:"outlined"},A))}),b.length>0&&e.jsxs(i,{children:[e.jsxs(t,{variant:"subtitle2",gutterBottom:!0,children:["Found ",b.length," curated suggestions:"]}),e.jsx(de,{children:b.map((c,A)=>e.jsx(ue,{divider:!0,children:e.jsx(xe,{primary:e.jsxs(i,{children:[e.jsxs(t,{variant:"subtitle1",children:['Pattern: "',c.questionPattern,'"']}),e.jsxs(i,{sx:{mt:1},children:[e.jsx(B,{label:c.category,size:"small",sx:{mr:1}}),c.validated&&e.jsx(B,{label:"Validated",size:"small",color:"success"})]})]}),secondary:e.jsxs(i,{sx:{mt:1},children:[e.jsxs(t,{variant:"body2",color:"text.secondary",children:["Keywords: ",c.keywords.join(", ")]}),e.jsx(t,{variant:"body2",sx:{mt:1},children:e.jsx("strong",{children:"Suggested Analyses:"})}),c.suggestions.map((N,_)=>e.jsxs(i,{sx:{ml:2,mt:.5},children:[e.jsxs(t,{variant:"body2",children:["• ",e.jsx("strong",{children:L[N.analysisId]||N.analysisId}),"(Priority: ",N.priority,", Confidence: ",(N.confidence*100).toFixed(0),"%)"]}),e.jsx(t,{variant:"caption",color:"text.secondary",children:N.reason})]},_))]})})},A))})]})]})}),e.jsx(G,{sx:{mb:3},children:e.jsxs(K,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Comprehensive Test Suite"}),e.jsx(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Test all problematic queries identified in the original issue to validate the training system's accuracy."}),e.jsx(y,{variant:"contained",onClick:g,startIcon:e.jsx(ve,{}),sx:{mb:2},children:"Run All Tests"}),m.length>0&&e.jsxs(i,{children:[e.jsxs(t,{variant:"subtitle1",gutterBottom:!0,children:["Test Results: ",m.filter(c=>c.passed).length,"/",m.length," passed"]}),e.jsx(de,{children:m.map((c,A)=>e.jsx(ue,{divider:!0,children:e.jsx(xe,{primary:e.jsxs(i,{sx:{display:"flex",alignItems:"center"},children:[c.passed?e.jsx(Ae,{color:"success",sx:{mr:1}}):e.jsx(cs,{color:"error",sx:{mr:1}}),e.jsx(t,{variant:"subtitle2",children:c.query})]}),secondary:e.jsxs(i,{children:[e.jsxs(t,{variant:"body2",children:["Expected: ",c.expectedAnalyses.join(", ")]}),e.jsxs(t,{variant:"body2",children:["Score: ",(c.score*100).toFixed(1),"% (",c.actualSuggestions.length," suggestions found)"]})]})})},A))})]})]})}),e.jsxs(X,{severity:"info",children:[e.jsx(t,{variant:"subtitle2",gutterBottom:!0,children:"How to Use:"}),e.jsxs(t,{variant:"body2",children:["1. ",e.jsx("strong",{children:"Test Individual Queries:"})," Enter any statistical question to see what the trained system suggests.",e.jsx("br",{}),"2. ",e.jsx("strong",{children:"Quick Tests:"})," Click on the problematic query chips to test the specific issues mentioned.",e.jsx("br",{}),"3. ",e.jsx("strong",{children:"Comprehensive Test:"})," Run all tests to see overall system accuracy.",e.jsx("br",{}),"4. The system now prioritizes curated suggestions from the training database over hardcoded scenarios."]})]})]}):e.jsx(X,{severity:"info",children:"Initializing training system..."})]})},js=()=>{const w=he(),[f,a]=d.useState(!1),[b,j]=d.useState("interface"),[m,I]=d.useState({}),[V,P]=d.useState("");d.useEffect(()=>{const R=window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"||window.location.port==="5173"||window.location.port==="5174"||window.location.port==="3000";if(a(R),R){const g=o.getDuplicateCuratedSuggestions();I(g)}},[]);const L=()=>{const R=o.removeDuplicateCuratedSuggestions();P(`Removed ${R} duplicate curated suggestions`),I({}),setTimeout(()=>P(""),5e3)},O=()=>{const R=o.getDuplicateCuratedSuggestions();I(R)};return f?e.jsxs(i,{sx:{p:3},children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(ce,{sx:{mr:2,color:w.palette.primary.main,fontSize:32}}),e.jsxs(i,{children:[e.jsx(t,{variant:"h4",children:"Development Training System"}),e.jsx(t,{variant:"subtitle1",color:"text.secondary",children:"Analysis Assistant Training Data Management"})]})]}),e.jsxs(X,{severity:"warning",sx:{mb:3},icon:e.jsx(fe,{}),children:[e.jsx(t,{variant:"subtitle2",gutterBottom:!0,children:"Development Environment Only"}),e.jsx(t,{variant:"body2",children:"This page is only accessible in development mode and provides tools to manage the Analysis Assistant training data. Changes made here will affect the AI suggestions for all users."})]}),e.jsx(k,{elevation:1,sx:{mb:3},children:e.jsxs(i,{sx:{display:"flex",borderBottom:1,borderColor:"divider"},children:[e.jsx(y,{variant:b==="interface"?"contained":"text",onClick:()=>j("interface"),sx:{borderRadius:0,px:3,py:1.5,textTransform:"none"},startIcon:e.jsx(ae,{}),children:"Training Interface"}),e.jsx(y,{variant:b==="demo"?"contained":"text",onClick:()=>j("demo"),sx:{borderRadius:0,px:3,py:1.5,textTransform:"none"},startIcon:e.jsx(ce,{}),children:"Testing & Demo"}),e.jsx(y,{variant:b==="cleanup"?"contained":"text",onClick:()=>j("cleanup"),sx:{borderRadius:0,px:3,py:1.5,textTransform:"none"},startIcon:e.jsx(be,{}),children:"Data Cleanup"})]})}),e.jsxs(k,{elevation:1,sx:{minHeight:"70vh"},children:[b==="interface"&&e.jsx(i,{sx:{p:0},children:e.jsx(ds,{})}),b==="demo"&&e.jsx(i,{sx:{p:3},children:e.jsx(hs,{})}),b==="cleanup"&&e.jsxs(i,{sx:{p:3},children:[e.jsx(t,{variant:"h5",gutterBottom:!0,children:"Data Cleanup & Maintenance"}),V&&e.jsx(X,{severity:"success",sx:{mb:3},children:V}),e.jsx(G,{sx:{mb:3},children:e.jsxs(K,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Duplicate Curated Suggestions"}),e.jsx(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:"The training system may create duplicate curated suggestions if initialization is called multiple times. Use the tools below to identify and clean up duplicates."}),e.jsxs(i,{sx:{display:"flex",gap:2,mb:2},children:[e.jsx(y,{variant:"outlined",onClick:O,startIcon:e.jsx(ce,{}),children:"Check for Duplicates"}),e.jsx(y,{variant:"contained",color:"warning",onClick:L,startIcon:e.jsx(be,{}),disabled:Object.keys(m).length===0,children:"Remove Duplicates"})]}),Object.keys(m).length>0?e.jsxs(i,{children:[e.jsxs(t,{variant:"subtitle2",color:"error",gutterBottom:!0,children:["Found ",Object.keys(m).length," duplicate patterns:"]}),Object.entries(m).map(([R,g])=>e.jsxs(t,{variant:"body2",sx:{ml:2},children:['• "',R,'" - ',g," copies"]},R))]}):e.jsx(t,{variant:"body2",color:"success.main",children:"✅ No duplicates found"})]})})]})]}),e.jsx(i,{sx:{mt:3,textAlign:"center"},children:e.jsxs(t,{variant:"caption",color:"text.secondary",children:["Environment: ","production"," | Host: ",window.location.hostname,":",window.location.port]})})]}):e.jsx(i,{sx:{p:4,maxWidth:800,mx:"auto"},children:e.jsxs(k,{elevation:2,sx:{p:4,textAlign:"center"},children:[e.jsx(fe,{sx:{fontSize:64,color:w.palette.error.main,mb:2}}),e.jsx(t,{variant:"h4",gutterBottom:!0,children:"Access Denied"}),e.jsx(t,{variant:"body1",color:"text.secondary",paragraph:!0,children:"This page is only accessible in development environment."}),e.jsxs(t,{variant:"body2",color:"text.secondary",children:["Current environment: ","production"]})]})})};export{js as default};
