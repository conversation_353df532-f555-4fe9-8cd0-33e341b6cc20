import{u as Te,j as e,B as Q,e as c,R as we,G as I,ai as Z,b9 as ee,ba as te,bb as k,aE as Ae,aF as _t,a_ as Vt,aj as De,I as rt,bs as at,ak as st,D as Be,bc as Pt,f as kt,c2 as Tt,ae as At,g as fe,a6 as Dt,a7 as ge,k as ye,l as be,h as nt,o as Bt,by as Rt,ao as it,ap as lt,aq as ot,ar as Ce,as as _,at as dt}from"./mui-libs-CfwFIaTD.js";import{r as $,b as ct}from"./react-libs-Cr2nE3UY.js";import{g as je,m as Wt,h as Re,l as We,i as Oe,S as Ot,j as qt,k as Ht,n as ut,o as Lt,r as ft,q as Qt,p as Gt}from"./charts-recharts-d3-BEF1Y_jn.js";import{a as Xt,D as qe}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./other-utils-CR9xr_gI.js";const W={MD:{name:"Mean Difference",logTransform:!1,nullValue:0},SMD:{name:"Standardized Mean Difference",logTransform:!1,nullValue:0},OR:{name:"Odds Ratio",logTransform:!0,nullValue:1},RR:{name:"Risk Ratio",logTransform:!0,nullValue:1},HR:{name:"Hazard Ratio",logTransform:!0,nullValue:1}},Ut=z=>{const J=1-(1-z)/2;if(z===.9)return 1.645;if(z===.95)return 1.96;if(z===.99)return 2.576;try{return Qt(ft(1e3).map(Gt()),J)||1.96}catch(ie){return console.warn("d3.randomNormal or d3.quantile not available for Z-score calculation, falling back to 1.96.",ie),1.96}},Fe=z=>{const f=.31938153,J=-.356563782,ie=1.781477937,O=-1.821255978,Se=1.330274429,re=.2316419,pe=.39894228;if(z>=0){const P=1/(1+re*z);return 1-pe*Math.exp(-z*z/2)*P*(P*(P*(P*(P*Se+O)+ie)+J)+f)}else{const P=1/(1-re*z);return pe*Math.exp(-z*z/2)*P*(P*(P*(P*(P*Se+O)+ie)+J)+f)}},Yt=(z,f)=>{if(f<=0||z<0)return 0;if(f===1&&z>0)return 2*Fe(Math.sqrt(z))-1;const J=(Math.pow(z/f,1/3)-(1-2/(9*f)))/Math.sqrt(2/(9*f));return Fe(J)},nr=()=>{const{datasets:z,currentDataset:f,setCurrentDataset:J}=Xt(),ie=Te(),[O,Se]=$.useState((f==null?void 0:f.id)||""),[re,pe]=$.useState(""),[P,He]=$.useState(""),[ve,Le]=$.useState(""),[ze,Qe]=$.useState(""),[le,Ge]=$.useState(""),[Xe,Ue]=$.useState("random"),[ae,pt]=$.useState(.95),[$e,Ye]=$.useState("MD"),[oe,ht]=$.useState(0),[q,mt]=$.useState({showForestPlot:!0,showFunnelPlot:!0,showWeights:!0,showHeterogeneity:!0,showSubgroupAnalysis:!0,showPublicationBias:!0}),[Ee,Ze]=$.useState(!1),[Je,Ie]=$.useState(null),[Ke,Me]=$.useState(null),[n,Ne]=$.useState(null);$.useEffect(()=>{const t=localStorage.getItem(`meta_analysis_results_${O}`);if(t)try{const a=JSON.parse(t);a.pooledEffect!==void 0&&a.forestPlotData?(Ne(a),Ue(a.model),Ye(a.effectMeasure)):localStorage.removeItem(`meta_analysis_results_${O}`)}catch(a){console.error("Error parsing saved meta-analysis results:",a),localStorage.removeItem(`meta_analysis_results_${O}`)}},[O]);const de=$.useCallback(()=>{Ne(null),O&&localStorage.removeItem(`meta_analysis_results_${O}`)},[O]),_e=(f==null?void 0:f.columns.filter(t=>t.type===qe.TEXT||t.type===qe.CATEGORICAL))||[],he=(f==null?void 0:f.columns.filter(t=>t.type===qe.NUMERIC))||[],xt=t=>{const a=t.target.value;Se(a),pe(""),He(""),Le(""),Qe(""),Ge(""),de();const p=z.find(d=>d.id===a);p&&J(p)},me=t=>a=>{t(a.target.value),de()},gt=me(pe),yt=me(He),bt=me(Le),jt=me(Qe),St=me(Ge),vt=t=>{Ue(t.target.value),de()},Et=t=>{pt(Number(t.target.value)),de()},wt=t=>{Ye(t.target.value),de()},Ct=t=>{mt(a=>({...a,[t]:!a[t]}))},Ft=(t,a,p,d)=>{let s=[...t],g=0;const{logTransform:b}=W[d];if(b?s=s.map(r=>r.originalEffectSize<=0?(g++,{...r,problematic:!0}):r.originalStandardError<=0?(g++,{...r,problematic:!0}):{...r,effectSize:Math.log(r.originalEffectSize),standardError:r.originalStandardError/r.originalEffectSize}).filter(r=>!r.problematic):s=s.map(r=>r.originalStandardError<=0?(g++,{...r,problematic:!0}):r).filter(r=>!r.problematic),s.length<2)throw new Error(`At least 2 valid studies are required after filtering. ${g>0?`${g} excluded.`:""}`);const l=s.length,h=Ut(p);let j={};s.forEach(r=>{j[r.id]=1/r.standardError**2});const y=Object.values(j).reduce((r,u)=>r+u,0),E=s.reduce((r,u)=>r+u.effectSize*j[u.id],0)/y,i=s.reduce((r,u)=>r+j[u.id]*Math.pow(u.effectSize-E,2),0),S=Math.max(1,l-1),w=1-Yt(i,S),T=i>0&&S<i?(i-S)/i*100:0;let C=0,B={};if(a==="random"&&i>S){const r=y-s.reduce((u,v)=>u+j[v.id]**2,0)/y;r>0&&(C=Math.max(0,(i-S)/r)),s.forEach(u=>{B[u.id]=1/(u.standardError**2+C)})}else B=j;const F=Object.values(B).reduce((r,u)=>r+u,0),A=s.reduce((r,u)=>r+u.effectSize*B[u.id],0)/F,R=1/F,H=Math.sqrt(R);let U,V;b?(U=Math.exp(A),V=[Math.exp(A-h*H),Math.exp(A+h*H)]):(U=A,V=[A-h*H,A+h*H]);const K=A/H,m=2*(1-Fe(Math.abs(K))),X=s.map(r=>{const u=B[r.id]/F*100;let v,D;return b?(v=Math.exp(r.effectSize-h*r.standardError),D=Math.exp(r.effectSize+h*r.standardError)):(v=r.effectSize-h*r.standardError,D=r.effectSize+h*r.standardError),{name:r.name,effectSize:r.originalEffectSize,ci_lower:v,ci_upper:D,weight:u,standardError:r.originalStandardError,subgroup:r.subgroup}});X.push({name:`Pooled Effect (${a==="fixed"?"Fixed":"Random"})`,effectSize:U,ci_lower:V[0],ci_upper:V[1],weight:100,isSummary:!0,subgroup:"Overall",standardError:void 0});const L=s.map(r=>({name:r.name,effectSize:r.originalEffectSize,standardError:r.originalStandardError,precision:r.originalStandardError>0?1/r.originalStandardError:void 0}));let M;le&&s.some(r=>r.subgroup)&&(M={},[...new Set(s.map(u=>u.subgroup).filter(Boolean))].forEach(u=>{const v=s.filter(D=>D.subgroup===u);if(v.length>0){let D={};a==="random"&&C>0?v.forEach(N=>D[N.id]=1/(N.standardError**2+C)):v.forEach(N=>D[N.id]=1/N.standardError**2);const Y=Object.values(D).reduce((N,G)=>N+G,0);if(Y>0){const N=v.reduce((x,ue)=>x+ue.effectSize*D[ue.id],0)/Y,G=Math.sqrt(1/Y);let ce,ne;b?(ce=Math.exp(N),ne=[Math.exp(N-h*G),Math.exp(N+h*G)]):(ce=N,ne=[N-h*G,N+h*G]),M[u]={pooledEffect:ce,pooledSE:G,pooledCI:ne,studies:v.map(x=>x.id),numStudies:v.length}}}}));const o={};if(l>=10){const r=s.map(x=>x.standardError),u=s.map(x=>x.effectSize),v=s.map(x=>1/x.standardError**2);let D=0,Y=0,N=0,G=0,ce=0;for(let x=0;x<l;x++)D+=v[x],Y+=v[x]*r[x],N+=v[x]*u[x],G+=v[x]*r[x]*r[x],ce+=v[x]*r[x]*u[x];const ne=D*G-Y*Y;if(Math.abs(ne)>1e-9){const x=(D*ce-Y*N)/ne,ue=(N-x*Y)/D,It=r.map(xe=>ue+x*xe),Mt=s.reduce((xe,Pe,ke)=>xe+v[ke]*Math.pow(u[ke]-It[ke],2),0),Nt=l-2>0?Mt/(l-2):0,Ve=Math.sqrt(Nt*(G/ne));if(!isNaN(Ve)&&Ve>0){const xe=ue/Ve,Pe=2*(1-Fe(Math.abs(xe)));o.eggerTest={intercept:ue,pValue:Pe,significant:Pe<.1,sufficientStudies:!0}}else o.eggerTest={intercept:NaN,pValue:NaN,significant:!1,sufficientStudies:!0}}else o.eggerTest={intercept:NaN,pValue:NaN,significant:!1,sufficientStudies:!0,note:"Denominator too small for Egger's test."}}else o.eggerTest={intercept:NaN,pValue:NaN,significant:!1,sufficientStudies:!1};return{model:a,effectMeasure:d,pooledEffect:U,pooledSE:H,pooledCI:V,pooledPValue:m,heterogeneity:{Q:i,df:S,pValue:w,I2:T,tau2:C},studyWeights:B,forestPlotData:X,funnelPlotData:L,subgroupAnalysis:M,publicationBias:o,excludedStudiesCount:g}},zt=()=>{if(!f||!re||!P||!ve){Ie("Please select: Study Name, Effect Size, and Standard Error."),Me(null);return}Ze(!0),Ie(null),Me(null),de();try{const t=f.columns.find(l=>l.id===re),a=f.columns.find(l=>l.id===P),p=f.columns.find(l=>l.id===ve),d=ze?f.columns.find(l=>l.id===ze):null,s=le?f.columns.find(l=>l.id===le):null;if(!t||!a||!p)throw new Error("Selected variables not found.");const g=[];if(f.data.forEach((l,h)=>{const j=l[t.name],y=l[a.name],E=l[p.name],i=d?l[d.name]:null,S=s?l[s.name]:null;j!=null&&j!==""&&typeof y=="number"&&!isNaN(y)&&typeof E=="number"&&!isNaN(E)&&g.push({id:`s_${h}_${String(j).slice(0,10).replace(/\W/g,"")}`,name:String(j),originalEffectSize:y,originalStandardError:E,effectSize:y,standardError:E,sampleSize:typeof i=="number"&&!isNaN(i)?i:0,subgroup:S?String(S):void 0})}),g.length<2)throw new Error("At least 2 studies with valid numeric ES and SE required.");const b=Ft(g,Xe,ae,$e);Ne(b),b.excludedStudiesCount>0&&Me(`${b.excludedStudiesCount} studies excluded due to invalid data for chosen effect measure.`),localStorage.setItem(`meta_analysis_results_${O}`,JSON.stringify(b))}catch(t){Ie(`Analysis Error: ${t instanceof Error?t.message:String(t)}`),console.error(t)}finally{Ze(!1)}},$t=()=>{if(!n)return"";const{pooledEffect:t,pooledCI:a,pooledPValue:p,heterogeneity:d,model:s,effectMeasure:g,publicationBias:b,subgroupAnalysis:l}=n,{name:h,logTransform:j,nullValue:y}=W[g],E=`${ae*100}%`;let i=`A ${s==="fixed"?"fixed-effect":"random-effects"} meta-analysis was conducted on ${n.forestPlotData.length-1-n.excludedStudiesCount} studies to estimate the pooled ${h.toLowerCase()}.`;n.excludedStudiesCount>0&&(i+=` (${n.excludedStudiesCount} studies excluded).`),i+=`

The pooled ${h.toLowerCase()} was ${t.toFixed(3)} (${E} CI: ${a[0].toFixed(3)} to ${a[1].toFixed(3)}). `;let S="";if(j?a[0]>y&&a[1]>y?S=`Statistically significant increase (p ${p<.001?"<0.001":`= ${p.toFixed(3)}`}). ${h} is ${t.toFixed(2)}x reference.`:a[0]<y&&a[1]<y?S=`Statistically significant decrease (p ${p<.001?"<0.001":`= ${p.toFixed(3)}`}). ${h} is ${t.toFixed(2)}x reference.`:S=`Not statistically significant (p = ${p.toFixed(3)}), CI includes null value ${y}.`:p<.05?S=`Statistically significant ${t>y?"increase":"decrease"} (p ${p<.001?"<0.001":`= ${p.toFixed(3)}`}).`:S=`Not statistically significant (p = ${p.toFixed(3)}), CI includes null value ${y}.`,i+=S,i+=`

--- Heterogeneity ---
Cochran's Q = ${d.Q.toFixed(2)} (df=${d.df}, p ${d.pValue<.001?"<0.001":"="+d.pValue.toFixed(3)}). I² = ${d.I2.toFixed(1)}%. `,d.I2===0&&d.Q<=d.df?i+="No observable heterogeneity.":d.I2<25?i+="Low heterogeneity.":d.I2<50?i+="Moderate heterogeneity.":d.I2<75?i+="Substantial heterogeneity.":i+="Considerable heterogeneity.",s==="random"&&d.tau2>0?i+=` Est. between-study variance (τ²) = ${d.tau2.toFixed(4)}.`:s==="random"&&d.tau2===0&&(i+=" τ² estimated as 0, results similar to fixed-effect."),b.eggerTest){const w=b.eggerTest;i+=`

--- Publication Bias (Egger's Test) ---
`,w.sufficientStudies?isNaN(w.intercept)||isNaN(w.pValue)?i+="Egger's test could not be computed.":(i+=`Egger's intercept = ${w.intercept.toFixed(3)}, p ${w.pValue<.001?"<0.001":`= ${w.pValue.toFixed(3)}`}. `,w.significant?i+="Suggests potential funnel plot asymmetry (p < 0.10).":i+="No strong evidence of funnel plot asymmetry (p >= 0.10).",i+=" Note: Low power with few studies."):i+="Egger's test not performed (requires ≥10 studies)."}return l&&Object.keys(l).length>0&&(i+=`

--- Subgroup Analysis ---
`,Object.entries(l).forEach(([w,T])=>{i+=`"${w}" (${T.numStudies} studies): Pooled ${h.toLowerCase()} = ${T.pooledEffect.toFixed(3)} (${E} CI: ${T.pooledCI[0].toFixed(3)} to ${T.pooledCI[1].toFixed(3)}).
`}),i+="Interpret subgroup differences cautiously."),i+=`

--- General Considerations ---
Results depend on study quality. Interpret in context.`,i},et=ct.memo(({data:t,effectMeasureType:a,confLevelPercent:p})=>{const d=$.useRef(null),s=Te(),{nullValue:g,name:b}=W[a],l=n;return $.useEffect(()=>{if(!t||t.length===0||!d.current||!d.current.parentElement)return;je(d.current).selectAll("*").remove();const h=25,j=40,y=50,E=t.length*h+j+y,i={top:j,right:20,left:20},S=d.current.parentElement.clientWidth||900,w=Math.max(S,700),T={study:Math.floor(w*.3),effectCI:Math.floor(w*.2),weight:Math.floor(w*.1)},C=i.left+T.study+T.effectCI+T.weight,B=w-C-i.right,F=je(d.current).attr("width",w).attr("height",E).style("font-family",Array.isArray(s.typography.fontFamily)?s.typography.fontFamily[0]:s.typography.fontFamily||"sans-serif").style("font-size","12px");F.append("text").text("Study").attr("x",i.left).attr("y",i.top-15).style("font-weight","bold"),F.append("text").text(`${b} [${p} CI]`).attr("x",i.left+T.study).attr("y",i.top-15).style("font-weight","bold"),F.append("text").text("Weight (%)").attr("x",C-5).attr("y",i.top-15).style("font-weight","bold").attr("text-anchor","end"),F.append("text").text(b).attr("x",C+B/2).attr("y",i.top-15).style("font-weight","bold").attr("text-anchor","middle");const A=t.flatMap(m=>[m.effectSize,m.ci_lower,m.ci_upper,g].filter(X=>typeof X=="number")),R=Wt(A)??0,H=Re(A)??1,U=Math.abs(H-R)*.1||.1,V=We().domain([R-U,H+U]).range([0,B]),K=F.append("g").attr("transform",`translate(${C}, ${i.top})`);if(K.append("g").attr("transform",`translate(0, ${t.length*h+5})`).call(Oe(V).ticks(5).tickSizeOuter(0)).selectAll("text").style("font-size","10px"),V(g)>=0&&V(g)<=B&&K.append("line").attr("x1",V(g)).attr("x2",V(g)).attr("y1",-5).attr("y2",t.length*h).attr("stroke",s.palette.text.secondary).attr("stroke-dasharray","3,3"),t.forEach((m,X)=>{const L=X*h+h/2,M=m.isSummary;F.append("text").text(m.name).attr("x",i.left).attr("y",i.top+L).attr("dominant-baseline","middle").style("font-weight",M?"bold":"normal").style("font-size",M?"13px":"12px"),F.append("text").text(`${m.effectSize.toFixed(3)} [${m.ci_lower.toFixed(3)}; ${m.ci_upper.toFixed(3)}]`).attr("x",i.left+T.study).attr("y",i.top+L).attr("dominant-baseline","middle").style("font-weight",M?"bold":"normal").style("font-size",M?"13px":"12px"),F.append("text").text(M?"-":m.weight.toFixed(1)).attr("x",C-5).attr("y",i.top+L).attr("dominant-baseline","middle").attr("text-anchor","end").style("font-weight",M?"bold":"normal").style("font-size",M?"13px":"12px");const o=K.append("g").attr("transform",`translate(0, ${L})`);if(m.ci_lower!=null&&m.ci_upper!=null&&o.append("line").attr("x1",V(m.ci_lower)).attr("x2",V(m.ci_upper)).attr("stroke",s.palette.text.primary).attr("stroke-width",M?2:1),M)o.append("path").attr("d",Ot(qt,8*8*(a==="MD"||a==="SMD"?1.5:2.5))).attr("transform",`translate(${V(m.effectSize)}, 0)`).attr("fill",s.palette.secondary.main).attr("stroke",s.palette.text.primary).attr("stroke-width",1);else{const se=Re(t.filter(v=>!v.isSummary),v=>v.weight)||1,r=m.weight/se,u=Math.max(3,Math.min(10,5*Math.sqrt(r)+2));o.append("rect").attr("x",V(m.effectSize)-u/2).attr("y",-u/2).attr("width",u).attr("height",u).attr("fill",s.palette.primary.main)}}),l!=null&&l.heterogeneity){const m=l.heterogeneity,X=`Heterogeneity: Q=${m.Q.toFixed(2)} (df=${m.df}, p=${m.pValue<.001?"<0.001":m.pValue.toFixed(3)}); I²=${m.I2.toFixed(1)}%`,L=l.model==="random"?`; τ²=${m.tau2.toFixed(4)}`:"";F.append("text").attr("x",i.left).attr("y",E-y/2+10).attr("text-anchor","start").style("font-size","11px").text(X+L)}},[t,s,a,p,l,g,b]),e.jsx(Q,{sx:{overflowX:"auto",overflowY:"auto",maxHeight:"600px",border:`1px solid ${s.palette.divider}`,p:1},children:e.jsx("svg",{ref:d})})});et.displayName="ForestPlot";const tt=ct.memo(({data:t,pooledEffect:a,effectMeasureType:p})=>{const d=$.useRef(null),s=Te(),{name:g,nullValue:b}=W[p];return $.useEffect(()=>{if(!t||t.length===0||!d.current||!d.current.parentElement)return;je(d.current).selectAll("*").remove();const l={top:50,right:50,bottom:60,left:70},h=d.current.parentElement.clientWidth||800,j=Math.max(h,600)-l.left-l.right,y=450-l.top-l.bottom,E=je(d.current).attr("width",j+l.left+l.right).attr("height",y+l.top+l.bottom).append("g").attr("transform",`translate(${l.left},${l.top})`).style("font-family",Array.isArray(s.typography.fontFamily)?s.typography.fontFamily[0]:s.typography.fontFamily||"sans-serif"),i=t.map(o=>o.effectSize).concat(a,b).filter(o=>typeof o=="number"),S=Ht(i),w=S[0]!==void 0&&S[1]!==void 0?[S[0],S[1]]:[0,1],T=Math.abs(w[1]-w[0])*.15||.1,C=We().domain([w[0]-T,w[1]+T]).range([0,j]),B=t.map(o=>o.standardError).filter(o=>typeof o=="number"&&o>0),F=B.length>0&&Re(B)||1,A=0,R=We().domain([F*1.05,A]).range([y,0]);E.append("g").attr("class","grid").attr("transform",`translate(0,${y})`).call(Oe(C).ticks(8).tickSize(-340).tickFormat(()=>"")).selectAll("line").attr("stroke",s.palette.divider),E.append("g").attr("class","grid").call(ut(R).ticks(8).tickSize(-j).tickFormat(()=>"")).selectAll("line").attr("stroke",s.palette.divider),E.append("g").attr("transform",`translate(0,${y})`).call(Oe(C).ticks(8)).append("text").attr("fill",s.palette.text.primary).attr("x",j/2).attr("y",l.bottom-15).attr("text-anchor","middle").style("font-size","14px").text(g),E.append("g").call(ut(R).ticks(8)).append("text").attr("fill",s.palette.text.primary).attr("transform","rotate(-90)").attr("x",-340/2).attr("y",-50).attr("text-anchor","middle").style("font-size","14px").text("Standard Error"),E.append("line").attr("x1",C(a)).attr("x2",C(a)).attr("y1",R(F*1.05)).attr("y2",R(A)).attr("stroke",s.palette.secondary.main).attr("stroke-width",2).attr("stroke-dasharray","4,4"),C(b)>=0&&C(b)<=j&&E.append("line").attr("x1",C(b)).attr("x2",C(b)).attr("y1",R(F*1.05)).attr("y2",R(A)).attr("stroke",s.palette.text.secondary).attr("stroke-width",1).attr("stroke-dasharray","2,2");const H=1.96,U=Lt().x(o=>C(o.x)).y(o=>R(o.se)),V=F>1e-5?F/50:.01,K=F>1e-5?ft(A,F*1.05,V):[0,F*1.05],m=K.map(o=>({se:o,x:a+H*o})),X=K.map(o=>({se:o,x:a-H*o})).reverse(),L=[...m,...X];L.length>1&&L.every(o=>!isNaN(o.x)&&!isNaN(o.se))&&E.append("path").datum(L).attr("fill","none").attr("stroke",s.palette.info.light).attr("stroke-width",1.5).attr("stroke-dasharray","3,3").attr("d",U);const M=je(document.createElement("div")).attr("class","funnel-tooltip-meta").style("position","absolute").style("visibility","hidden").style("background-color",s.palette.background.paper).style("border",`1px solid ${s.palette.divider}`).style("border-radius","4px").style("padding","8px").style("font-size","12px").style("pointer-events","none").style("z-index",String(s.zIndex.tooltip));return document.body.appendChild(M.node()),E.selectAll(".study-point").data(t.filter(o=>typeof o.standardError=="number"&&o.standardError>0)).enter().append("circle").attr("class","study-point").attr("cx",o=>C(o.effectSize)).attr("cy",o=>R(o.standardError)).attr("r",5).attr("fill",s.palette.primary.main).attr("stroke",s.palette.background.paper).attr("stroke-width",.5).style("opacity",.8).on("mouseover",(o,se)=>{M.html(`<b>${se.name}</b><br/>${g}: ${se.effectSize.toFixed(3)}<br/>SE: ${se.standardError.toFixed(3)}`).style("visibility","visible")}).on("mousemove",o=>{M.style("top",o.pageY-10+"px").style("left",o.pageX+10+"px")}).on("mouseout",()=>{M.style("visibility","hidden")}),E.append("text").attr("x",j/2).attr("y",0-l.top/2).attr("text-anchor","middle").style("font-size","16px").style("font-weight","bold").text(`Funnel Plot of ${g} vs. Standard Error`),()=>{M.remove()}},[t,a,s,p,g,b]),e.jsx(Q,{sx:{overflowX:"auto",overflowY:"auto",maxHeight:"600px",border:`1px solid ${s.palette.divider}`,p:1},children:e.jsx("svg",{ref:d})})});return tt.displayName="FunnelPlot",e.jsxs(Q,{p:3,children:[e.jsx(c,{variant:"h4",gutterBottom:!0,children:"Meta-Analysis"}),e.jsxs(we,{elevation:3,sx:{p:3,mb:3},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{borderBottom:`1px solid ${ie.palette.divider}`,pb:1,mb:2},children:"Configuration"}),e.jsxs(I,{container:!0,spacing:3,children:[e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",children:[e.jsx(ee,{id:"dataset-select-label",children:"Dataset"}),e.jsx(te,{labelId:"dataset-select-label",value:O,label:"Dataset",onChange:xt,children:z.length===0?e.jsx(k,{value:"",disabled:!0,children:"No datasets available"}):z.map(t=>e.jsxs(k,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",disabled:!f,children:[e.jsx(ee,{id:"study-name-label",children:"Study Name/Identifier"}),e.jsx(te,{labelId:"study-name-label",value:re,label:"Study Name/Identifier",onChange:gt,children:_e.length===0?e.jsx(k,{value:"",disabled:!0,children:"No text/categorical columns"}):_e.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",children:[e.jsx(ee,{id:"effect-measure-label",children:"Effect Measure Type"}),e.jsx(te,{labelId:"effect-measure-label",value:$e,label:"Effect Measure Type",onChange:wt,renderValue:t=>W[t].name,children:Object.entries(W).map(([t,a])=>e.jsx(k,{value:t,children:e.jsxs(Q,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsxs(c,{sx:{flexGrow:1},children:[a.name," (",t,")"]}),e.jsx(Ae,{title:a.logTransform?"Analyzed on log scale, then back-transformed. Null = 1.":"Analyzed on original scale. Null = 0.",placement:"right",children:e.jsx(_t,{fontSize:"small",color:"action"})})]})},t))})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",disabled:!f,children:[e.jsx(ee,{id:"effect-size-label",children:"Effect Size (Value)"}),e.jsx(te,{labelId:"effect-size-label",value:P,label:"Effect Size (Value)",onChange:yt,children:he.length===0?e.jsx(k,{value:"",disabled:!0,children:"No numeric columns"}):he.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",disabled:!f,children:[e.jsx(ee,{id:"se-label",children:"Standard Error (of Effect Size)"}),e.jsx(te,{labelId:"se-label",value:ve,label:"Standard Error (of Effect Size)",onChange:bt,children:he.length===0?e.jsx(k,{value:"",disabled:!0,children:"No numeric columns"}):he.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))}),e.jsx(c,{variant:"caption",color:"textSecondary",sx:{mt:.5},children:W[$e].logTransform?"Provide SE of original ES (e.g. SE of OR, not log(OR)).":"Provide SE of the ES."})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",disabled:!f,children:[e.jsx(ee,{id:"sample-size-label",children:"Sample Size (Optional)"}),e.jsxs(te,{labelId:"sample-size-label",value:ze,label:"Sample Size (Optional)",onChange:jt,children:[e.jsx(k,{value:"",children:e.jsx("em",{children:"None"})}),he.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))]})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",disabled:!f,children:[e.jsx(ee,{id:"subgroup-label",children:"Subgroup Variable (Optional)"}),e.jsxs(te,{labelId:"subgroup-label",value:le,label:"Subgroup Variable (Optional)",onChange:St,children:[e.jsx(k,{value:"",children:e.jsx("em",{children:"None"})}),_e.map(t=>e.jsx(k,{value:t.id,children:t.name},t.id))]})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{fullWidth:!0,margin:"normal",children:[e.jsx(ee,{id:"conf-level-label",children:"Confidence Level"}),e.jsxs(te,{labelId:"conf-level-label",value:ae,label:"Confidence Level",onChange:Et,children:[e.jsx(k,{value:.9,children:"90%"})," ",e.jsx(k,{value:.95,children:"95%"})," ",e.jsx(k,{value:.99,children:"99%"})]})]})}),e.jsx(I,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(Z,{component:"fieldset",margin:"normal",fullWidth:!0,children:[e.jsx(c,{variant:"subtitle2",gutterBottom:!0,component:"legend",children:"Analysis Model"}),e.jsxs(Vt,{row:!0,value:Xe,onChange:vt,children:[e.jsx(De,{value:"fixed",control:e.jsx(st,{}),label:e.jsxs(Q,{display:"flex",alignItems:"center",children:["Fixed-Effect",e.jsx(Ae,{title:"Assumes one true effect size. Weights by precision.",children:e.jsx(rt,{size:"small",children:e.jsx(at,{fontSize:"inherit"})})})]})}),e.jsx(De,{value:"random",control:e.jsx(st,{}),label:e.jsxs(Q,{display:"flex",alignItems:"center",children:["Random-Effects",e.jsx(Ae,{title:"Assumes true effects vary. Incorporates between-study variance (τ²).",children:e.jsx(rt,{size:"small",children:e.jsx(at,{fontSize:"inherit"})})})]})})]})]})})]}),e.jsx(Be,{sx:{my:3}}),e.jsx(c,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsx(I,{container:!0,spacing:1,children:Object.keys(q).map(t=>{const a=t;return e.jsx(I,{item:!0,xs:12,sm:6,md:4,children:e.jsx(De,{control:e.jsx(Pt,{checked:q[a],onChange:()=>Ct(a)}),label:a.replace(/([A-Z])/g," $1").replace(/^./,p=>p.toUpperCase())})},a)})}),e.jsx(Q,{mt:3,display:"flex",justifyContent:"flex-start",children:e.jsx(kt,{variant:"contained",color:"primary",size:"large",startIcon:e.jsx(Tt,{}),onClick:zt,disabled:Ee||!re||!P||!ve||!f,children:"Run Meta-Analysis"})})]}),Ee&&e.jsx(Q,{display:"flex",justifyContent:"center",my:5,children:e.jsx(At,{size:50})}),Je&&e.jsx(fe,{severity:"error",sx:{mb:3},children:Je}),Ke&&!Ee&&e.jsx(fe,{severity:"warning",sx:{mb:3},children:Ke}),n&&!Ee&&e.jsxs(we,{elevation:3,sx:{p:0,mt:3},children:[e.jsxs(Dt,{value:oe,onChange:(t,a)=>ht(a),sx:{borderBottom:1,borderColor:"divider",px:2},variant:"scrollable",scrollButtons:"auto",children:[e.jsx(ge,{label:"Summary Statistics"}),q.showForestPlot&&e.jsx(ge,{label:"Forest Plot"}),q.showFunnelPlot&&e.jsx(ge,{label:"Funnel Plot"}),e.jsx(ge,{label:"Diagnostics & Subgroups"}),e.jsx(ge,{label:"Full Interpretation"})]}),e.jsxs(Q,{p:3,children:[oe===0&&e.jsxs(I,{container:!0,spacing:3,children:[e.jsx(I,{item:!0,xs:12,md:6,children:e.jsx(ye,{variant:"outlined",children:e.jsxs(be,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Pooled Effect Estimate"}),e.jsxs(c,{variant:"h3",component:"div",sx:{fontWeight:"bold"},children:[n.pooledEffect.toFixed(3),e.jsxs(c,{variant:"caption",color:"text.secondary",sx:{ml:1},children:["(",W[n.effectMeasure].name,")"]})]}),e.jsx(Be,{sx:{my:1.5}}),e.jsxs(c,{variant:"body1",children:[e.jsxs("strong",{children:[ae*100,"% CI:"]})," [",n.pooledCI[0].toFixed(3),", ",n.pooledCI[1].toFixed(3),"]"]}),e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"p-value:"})," ",n.pooledPValue<.001?"< 0.001":n.pooledPValue.toFixed(3)]}),e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"Model:"})," ",n.model==="fixed"?"Fixed-Effect":"Random-Effects"]}),e.jsxs(c,{variant:"body1",children:[e.jsxs("strong",{children:["Std. Error (of ",W[n.effectMeasure].logTransform?`log(${n.effectMeasure})`:"Effect","):"]})," ",n.pooledSE.toFixed(3)]}),n.pooledPValue<1-ae&&e.jsx(nt,{icon:e.jsx(Bt,{}),label:"Statistically Significant",color:"success",size:"small",sx:{mt:1.5}})]})})}),q.showHeterogeneity&&e.jsx(I,{item:!0,xs:12,md:6,children:e.jsx(ye,{variant:"outlined",children:e.jsxs(be,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Heterogeneity"}),e.jsxs(c,{variant:"h4",component:"div",children:["I² = ",n.heterogeneity.I2.toFixed(1),"%"]}),e.jsxs(c,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:[n.heterogeneity.I2<25?"Low":n.heterogeneity.I2<50?"Moderate":n.heterogeneity.I2<75?"Substantial":"Considerable"," heterogeneity"]}),e.jsx(Be,{sx:{my:1.5}}),e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"Cochran's Q:"})," ",n.heterogeneity.Q.toFixed(2)," (df=",n.heterogeneity.df,")"]}),e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"p-value (for Q):"})," ",n.heterogeneity.pValue<.001?"< 0.001":n.heterogeneity.pValue.toFixed(3)]}),n.model==="random"&&e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"τ²:"})," ",n.heterogeneity.tau2.toFixed(4)]}),n.heterogeneity.pValue<.05&&n.heterogeneity.I2>0&&e.jsx(nt,{icon:e.jsx(Rt,{}),label:"Significant Heterogeneity",color:"warning",size:"small",sx:{mt:1.5}})]})})}),q.showWeights&&e.jsxs(I,{item:!0,xs:12,children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",sx:{mt:2},children:"Study Details & Weights"}),e.jsx(it,{component:we,variant:"outlined",children:e.jsxs(lt,{size:"small",children:[e.jsx(ot,{children:e.jsxs(Ce,{children:[e.jsx(_,{children:"Study"}),e.jsx(_,{align:"right",children:W[n.effectMeasure].name}),e.jsx(_,{align:"right",children:"Std. Error"}),e.jsx(_,{align:"right",children:"Weight (%)"}),le&&e.jsx(_,{children:"Subgroup"})]})}),e.jsx(dt,{children:n.forestPlotData.filter(t=>!t.isSummary).map((t,a)=>{var p;return e.jsxs(Ce,{hover:!0,children:[e.jsx(_,{component:"th",scope:"row",children:t.name}),e.jsx(_,{align:"right",children:t.effectSize.toFixed(3)}),e.jsx(_,{align:"right",children:((p=t.standardError)==null?void 0:p.toFixed(3))??"-"}),e.jsxs(_,{align:"right",children:[t.weight.toFixed(1),"%"]}),le&&e.jsx(_,{children:t.subgroup||"N/A"})]},t.name+a)})})]})})]})]}),oe===1&&q.showForestPlot&&e.jsxs(Q,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Forest Plot"}),e.jsx(et,{data:n.forestPlotData,effectMeasureType:n.effectMeasure,confLevelPercent:`${ae*100}%`}),e.jsxs(c,{variant:"caption",color:"text.secondary",display:"block",mt:1,children:["Individual study effects (squares by weight) & CIs. Diamond is pooled effect. Dashed line: no effect (",W[n.effectMeasure].nullValue,")."]})]}),oe===2&&q.showFunnelPlot&&e.jsxs(Q,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Funnel Plot"}),e.jsx(tt,{data:n.funnelPlotData,pooledEffect:n.pooledEffect,effectMeasureType:n.effectMeasure}),e.jsx(c,{variant:"caption",color:"text.secondary",display:"block",mt:1,children:"ES vs SE. Asymmetry may indicate bias. Solid line: Pooled effect. Dashed: No effect. Dotted: Pseudo 95% CI."})]}),oe===3&&e.jsxs(I,{container:!0,spacing:3,children:[q.showPublicationBias&&n.publicationBias.eggerTest&&e.jsx(I,{item:!0,xs:12,md:6,children:e.jsx(ye,{variant:"outlined",children:e.jsxs(be,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Publication Bias"}),e.jsx(c,{variant:"subtitle1",gutterBottom:!0,children:"Egger's Test"}),n.publicationBias.eggerTest.sufficientStudies?isNaN(n.publicationBias.eggerTest.intercept)||isNaN(n.publicationBias.eggerTest.pValue)?e.jsx(fe,{severity:"warning",children:"Egger's test N/A (data variance issues?)."}):e.jsxs(e.Fragment,{children:[e.jsxs(c,{variant:"body1",children:[e.jsxs("strong",{children:["Intercept (",W[n.effectMeasure].logTransform?`log(${n.effectMeasure})`:"Effect","):"]})," ",n.publicationBias.eggerTest.intercept.toFixed(3)]}),e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"p-value:"})," ",n.publicationBias.eggerTest.pValue.toFixed(3)]}),n.publicationBias.eggerTest.significant?e.jsx(fe,{severity:"warning",sx:{mt:1.5},children:"Potential asymmetry (p < 0.10). May indicate bias."}):e.jsx(fe,{severity:"success",sx:{mt:1.5},children:"No significant asymmetry by Egger's (p >= 0.10)."})]}):e.jsxs(fe,{severity:"info",children:["Egger's test requires ≥10 studies. Current: ",n.funnelPlotData.length,"."]}),e.jsx(c,{variant:"caption",display:"block",sx:{mt:1},children:"Egger's test assesses funnel plot asymmetry. Interpret with caution, especially with few studies."})]})})}),q.showSubgroupAnalysis&&n.subgroupAnalysis&&Object.keys(n.subgroupAnalysis).length>0&&e.jsx(I,{item:!0,xs:12,md:q.showPublicationBias&&n.publicationBias.eggerTest?6:12,children:e.jsx(ye,{variant:"outlined",children:e.jsxs(be,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Subgroup Analysis"}),e.jsx(it,{children:e.jsxs(lt,{size:"small",children:[e.jsx(ot,{children:e.jsxs(Ce,{children:[e.jsx(_,{children:"Subgroup"}),e.jsx(_,{align:"right",children:"N Studies"}),e.jsxs(_,{align:"right",children:["Pooled ",W[n.effectMeasure].name]}),e.jsxs(_,{align:"right",children:[ae*100,"% CI"]})]})}),e.jsx(dt,{children:Object.entries(n.subgroupAnalysis).map(([t,a])=>e.jsxs(Ce,{hover:!0,children:[e.jsx(_,{component:"th",scope:"row",children:t}),e.jsx(_,{align:"right",children:a.numStudies}),e.jsx(_,{align:"right",children:a.pooledEffect.toFixed(3)}),e.jsxs(_,{align:"right",children:["[",a.pooledCI[0].toFixed(3),", ",a.pooledCI[1].toFixed(3),"]"]})]},t))})]})}),e.jsx(c,{variant:"caption",display:"block",sx:{mt:1},children:"Subgroup effects use overall τ² if applicable. Formal test for subgroup differences not performed."})]})})}),q.showSubgroupAnalysis&&(!n.subgroupAnalysis||Object.keys(n.subgroupAnalysis).length===0)&&e.jsx(I,{item:!0,xs:12,md:6,children:e.jsx(ye,{variant:"outlined",children:e.jsxs(be,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Subgroup Analysis"}),e.jsx(c,{children:"No subgroup variable selected or no subgroups found."})]})})})]}),oe===4&&e.jsxs(Q,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,color:"primary",children:"Comprehensive Interpretation"}),e.jsx(we,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.default",whiteSpace:"pre-line",lineHeight:1.6,fontSize:"0.95rem"},children:$t()})]})]})]})]})};export{nr as default};
