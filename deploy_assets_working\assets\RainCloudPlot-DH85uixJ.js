import{u as Le,j as e,B as s,e as o,bW as Re,bX as Te,bs as Ee,aD as Be,bY as Me,G as J,R as D,a6 as Ge,aE as F,a7 as pe,c7 as Oe,aW as Fe,F as fe,ai as L,b9 as R,ba as T,bb as v,c8 as We,aj as W,b2 as q,f as qe,ae as je,ad as He,I as ye,b7 as _e,bH as $e,ah as N,bN as Ke,g as Qe,ab as Ye,ao as Ue,ap as Xe,aq as Je,ar as ve,as as c,at as Ne}from"./mui-libs-CfwFIaTD.js";import{r as h,b as Z}from"./react-libs-Cr2nE3UY.js";import{a as Ze,D as ee}from"./index-Bpan7Tbe.js";import{j as et,b as tt}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{l as H,P as at}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./math-lib-BOZ-XUok.js";import"./charts-recharts-d3-BEF1Y_jn.js";const Ce={title:"Violin Plot",xAxisLabel:"Category",yAxisLabel:"Value",showOutliers:!0,showMean:!0,horizontal:!1,colorScheme:"default",jitterAmount:0,showBox:!0},te={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],sequential:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],diverging:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"]},ae="plotlyRainCloudPlotDiv",gt=()=>{const{datasets:E,currentDataset:x}=Ze(),d=Le(),f=h.useRef(null),[A,le]=h.useState((x==null?void 0:x.id)||""),[b,_]=h.useState(""),[B,$]=h.useState(""),[M,K]=h.useState(""),[a,ie]=h.useState(Ce),[p,Q]=h.useState("variables"),[j,k]=h.useState(null),[ne,z]=h.useState([]),[I,re]=h.useState(!1),[Y,C]=h.useState(null),n=Z.useMemo(()=>A&&E.find(t=>t.id===A)||null,[E,A]),se=Z.useMemo(()=>(n==null?void 0:n.columns.filter(t=>t.type===ee.NUMERIC))||[],[n]),G=Z.useMemo(()=>(n==null?void 0:n.columns.filter(t=>t.type===ee.CATEGORICAL))||[],[n]);h.useEffect(()=>{x!=null&&x.id&&x.id!==A&&(le(x.id),_(""),$(""),K(""),k(null),z([]),C(null))},[x]),h.useEffect(()=>{if(j&&f.current){const t={responsive:!0};H.newPlot(ae,j.data,j.layout,t)}return()=>{f.current&&typeof at<"u"&&H.purge&&H.purge(f.current)}},[j]),h.useEffect(()=>{b&&n&&oe()},[b,B,M,a,n]),h.useEffect(()=>{const t=i=>{i.altKey&&!i.ctrlKey&&!i.shiftKey&&(i.key==="1"?(i.preventDefault(),Q("variables")):i.key==="2"&&(i.preventDefault(),Q("settings")))};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)},[]);const Se=t=>{const i=t.target.value;le(i),_(""),$(""),K(""),k(null),z([]),C(null)},g=(t,i)=>{ie(r=>({...r,[t]:i}))},oe=()=>{if(!n||!b){C("Please select a dataset and a numerical variable.");return}re(!0),C(null),k(null),z([]);try{const t=n.columns.find(l=>l.id===b),i=B?n.columns.find(l=>l.id===B):null,r=M?n.columns.find(l=>l.id===M):null;if(!t)throw new Error("Value column not found.");if(r&&r.type!==ee.CATEGORICAL)throw new Error("Grouping variable must be categorical.");if(r&&[...new Set(n.data.map(u=>u[r.name]))].length>2)throw new Error("Grouping variable must be binary (have only two unique values).");const ce=[],de=[],U=te[a.colorScheme]||te.default,ke=n.data,S={};ke.forEach(l=>{const u=i?String(l[i.name]):"All Data",m=r?String(l[r.name]):"All",y=l[t.name];typeof y=="number"&&!isNaN(y)&&(S[u]||(S[u]={}),S[u][m]||(S[u][m]=[]),S[u][m].push(y))});const ze=i?[...new Set(n.data.map(l=>l[i.name]))].map(String).sort():["All Data"],he=r?[...new Set(n.data.map(l=>l[r.name]))].map(String).sort():["All"];ze.forEach((l,u)=>{he.forEach((m,y)=>{var me;const P=((me=S[l])==null?void 0:me[m])||[];if(P.length===0)return;let ue="both",xe=0;r&&he.length===2&&(ue=y===0?"positive":"negative",xe=y===0?.45:-.45);const Pe=r?m:l,Ve=r?m:l,De=r?m:l,ge=U[r?y%U.length:u%U.length],V={type:"violin",name:Pe,text:`sample length: ${P.length}`,hoveron:"points",meanline:{visible:a.showMean},legendgroup:Ve,scalegroup:De,points:a.showOutliers?"all":!1,pointpos:xe,box:{visible:a.showBox},jitter:a.jitterAmount,scalemode:"count",marker:{line:{width:2,color:ge},symbol:"dots"},showlegend:r?u===0:!0,side:ue,span:[0],line:{color:ge},orientation:a.horizontal?"h":"v"};a.horizontal?(V.y0=l,V.x=P):(V.x0=l,V.y=P),ce.push(V);const w=[...P].sort((O,X)=>O-X);if(w.length>0){const[O,X,be]=et(w);de.push({category:r?`${l} - ${m}`:l,min:Math.min(...w),max:Math.max(...w),q1:O,median:X,q3:be,mean:tt(w),iqr:be-O,sampleSize:w.length})}})});const Ie={title:{text:a.title+(r?"<br><i>scaled by number of observations per group":"")},xaxis:{title:{text:a.horizontal?a.yAxisLabel:a.xAxisLabel},showgrid:!a.horizontal},yaxis:{title:{text:a.horizontal?a.xAxisLabel:a.yAxisLabel},showgrid:a.horizontal},hovermode:"closest",width:void 0,height:600,paper_bgcolor:d.palette.mode==="dark"?d.palette.background.paper:"#fff",plot_bgcolor:d.palette.mode==="dark"?d.palette.background.default:"#fff",font:{color:d.palette.text.primary},legend:{tracegroupgap:0},violingap:0,violingroupgap:0,violinmode:r?"overlay":"group"};k({data:ce,layout:Ie}),z(de)}catch(t){C(`Error generating violin plot: ${t instanceof Error?t.message:String(t)}`),k(null),z([])}finally{re(!1)}},we=()=>{ie(Ce)},Ae=()=>{if(f.current&&j){const t={format:"svg",filename:a.title.replace(/\s+/g,"_")||"violin_plot",width:f.current.offsetWidth||600,height:f.current.offsetHeight||400};H.downloadImage(ae,t)}else C("Chart data not available for download.")};return e.jsxs(s,{p:3,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,children:"Rain Cloud Plot Generator"}),e.jsxs(Re,{sx:{mb:3},children:[e.jsx(Te,{expandIcon:e.jsx(Be,{}),children:e.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[e.jsx(Ee,{color:"primary"}),e.jsx(o,{variant:"subtitle1",children:"About Rain Cloud Plots"})]})}),e.jsxs(Me,{children:[e.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Rain cloud plots combine violin plots, box plots, and individual data points to provide a comprehensive view of data distribution. They are particularly useful for visualizing the shape, central tendency, and variability of your data."}),e.jsxs(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:[e.jsx("strong",{children:"Keyboard Shortcuts:"})," Alt+1 (Variables Panel), Alt+2 (Settings Panel)"]}),e.jsx(o,{variant:"body2",color:"text.secondary",children:"Select a numerical variable to get started. Optionally add categorical variables for grouping and comparison."})]})]}),e.jsxs(J,{container:!0,spacing:2,children:[e.jsxs(J,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(D,{elevation:1,sx:{mb:1,backgroundColor:d.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(Ge,{value:p,onChange:(t,i)=>Q(i),variant:"fullWidth",sx:{minHeight:44,"& .MuiTab-root":{minHeight:44,fontSize:"0.875rem",fontWeight:500,color:d.palette.text.secondary,textTransform:"none",transition:"all 0.2s ease-in-out","&.Mui-selected":{color:d.palette.primary.main,backgroundColor:d.palette.mode==="dark"?"rgba(255, 255, 255, 0.08)":"rgba(25, 118, 210, 0.08)"},"&:hover":{backgroundColor:d.palette.mode==="dark"?"rgba(255, 255, 255, 0.04)":"rgba(0, 0, 0, 0.04)"}},"& .MuiTabs-indicator":{height:3,borderRadius:"3px 3px 0 0"}},children:[e.jsx(F,{title:"Variable Selection Panel",placement:"top",children:e.jsx(pe,{value:"variables",label:"Variables",icon:e.jsx(Oe,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(F,{title:"Chart Settings Panel",placement:"top",children:e.jsx(pe,{value:"settings",label:"Settings",icon:e.jsx(Fe,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(fe,{in:p==="variables",timeout:300,children:e.jsx(s,{sx:{display:p==="variables"?"block":"none"},children:e.jsxs(D,{elevation:2,sx:{p:2,height:"fit-content"},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Variable Selection"}),e.jsxs(L,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(R,{children:"Dataset"}),e.jsx(T,{value:A,onChange:Se,label:"Dataset",disabled:E.length===0,children:E.map(t=>e.jsxs(v,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]}),e.jsxs(L,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(R,{children:"Numerical Variable (Required)"}),e.jsx(T,{value:b,onChange:t=>_(t.target.value),label:"Numerical Variable (Required)",disabled:se.length===0,children:se.map(t=>e.jsx(v,{value:t.id,children:t.name},t.id))})]}),e.jsxs(L,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(R,{children:"Factor Variable (Optional)"}),e.jsxs(T,{value:B,onChange:t=>$(t.target.value),label:"Factor Variable (Optional)",disabled:G.length===0,children:[e.jsx(v,{value:"",children:e.jsx("em",{children:"None"})}),G.map(t=>e.jsx(v,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(L,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(R,{children:"Grouping Variable (Optional, Binary)"}),e.jsxs(T,{value:M,onChange:t=>K(t.target.value),label:"Grouping Variable (Optional, Binary)",disabled:G.length===0,children:[e.jsx(v,{value:"",children:e.jsx("em",{children:"None"})}),G.map(t=>e.jsx(v,{value:t.id,children:t.name},t.id))]})]}),e.jsxs(s,{mt:2,children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(We,{children:[e.jsx(W,{control:e.jsx(q,{size:"small",checked:a.showOutliers,onChange:t=>g("showOutliers",t.target.checked)}),label:"Show All Points"}),e.jsx(W,{control:e.jsx(q,{size:"small",checked:a.showMean,onChange:t=>g("showMean",t.target.checked)}),label:"Show Mean Line"}),e.jsx(W,{control:e.jsx(q,{size:"small",checked:a.showBox,onChange:t=>g("showBox",t.target.checked)}),label:"Show Box Plot"}),e.jsx(W,{control:e.jsx(q,{size:"small",checked:a.horizontal,onChange:t=>g("horizontal",t.target.checked)}),label:"Horizontal Orientation"})]})]}),e.jsxs(s,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(qe,{variant:"contained",onClick:oe,disabled:!b||I,startIcon:I?e.jsx(je,{size:20}):e.jsx(He,{}),fullWidth:!0,children:I?"Generating...":"Generate Rain Cloud Plot"}),e.jsxs(s,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(F,{title:"Download Chart",children:e.jsx(ye,{onClick:Ae,disabled:!j,children:e.jsx(_e,{})})}),e.jsx(F,{title:"Reset Settings",children:e.jsx(ye,{onClick:we,children:e.jsx($e,{})})})]})]})]})})}),e.jsx(fe,{in:p==="settings",timeout:300,children:e.jsx(s,{sx:{display:p==="settings"?"block":"none"},children:e.jsxs(D,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:d.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:1},children:"Chart Settings"}),e.jsxs(s,{display:"flex",flexDirection:"column",gap:3,children:[e.jsxs(s,{children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Labels & Title"}),e.jsx(N,{fullWidth:!0,size:"small",label:"Chart Title",value:a.title,onChange:t=>g("title",t.target.value),margin:"dense"}),e.jsx(N,{fullWidth:!0,size:"small",label:"X-Axis Label",value:a.xAxisLabel,onChange:t=>g("xAxisLabel",t.target.value),margin:"dense"}),e.jsx(N,{fullWidth:!0,size:"small",label:"Y-Axis Label",value:a.yAxisLabel,onChange:t=>g("yAxisLabel",t.target.value),margin:"dense"})]}),e.jsxs(s,{children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(L,{fullWidth:!0,size:"small",margin:"dense",children:[e.jsx(R,{children:"Color Scheme"}),e.jsx(T,{value:a.colorScheme,onChange:t=>g("colorScheme",t.target.value),label:"Color Scheme",children:Object.keys(te).map(t=>e.jsx(v,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})]}),e.jsxs(s,{children:[e.jsx(o,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Size & Styling"}),e.jsxs(s,{sx:{mt:2},children:[e.jsxs(o,{variant:"body2",gutterBottom:!0,children:["Jitter Amount: ",a.jitterAmount]}),e.jsx(Ke,{value:a.jitterAmount,onChange:(t,i)=>g("jitterAmount",i),step:.05,marks:!0,min:0,max:.5,valueLabelDisplay:"auto",size:"small"})]})]})]})]})})})]}),e.jsx(J,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(D,{elevation:2,sx:{p:2},children:[e.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(o,{variant:"h6",children:"Chart Preview"}),e.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(o,{variant:"body2",color:"text.secondary",children:["Active: ",p==="variables"?"Variables":"Settings"]}),e.jsx(s,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:p==="variables"?d.palette.primary.main:d.palette.warning.main,boxShadow:`0 0 0 2px ${p==="variables"?d.palette.primary.main+"20":d.palette.warning.main+"20"}`}})]})]}),Y&&e.jsx(Qe,{severity:"error",sx:{mb:2},children:Y}),e.jsx(s,{ref:f,sx:{minHeight:500,display:"flex",justifyContent:"center",alignItems:"center",border:`1px solid ${d.palette.divider}`,borderRadius:1,backgroundColor:d.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)"},children:I?e.jsxs(s,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(je,{}),e.jsx(o,{color:"text.secondary",children:"Generating rain cloud plot..."})]}):j?e.jsx("div",{id:ae,style:{width:"100%",height:"500px"}}):e.jsxs(s,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,children:[e.jsx(Ye,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(o,{color:"text.secondary",textAlign:"center",children:n?b?"Chart will appear here once generated":"Select a numerical variable to generate the rain cloud plot":"Select a dataset to begin"}),n&&!b&&e.jsx(o,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"})]})}),ne.length>0&&!I&&!Y&&e.jsxs(s,{mt:3,children:[e.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Summary Statistics"}),e.jsx(Ue,{component:D,variant:"outlined",children:e.jsxs(Xe,{size:"small",children:[e.jsx(Je,{children:e.jsxs(ve,{children:[e.jsx(c,{children:"Category"}),e.jsx(c,{align:"right",children:"Min"}),e.jsx(c,{align:"right",children:"Q1"}),e.jsx(c,{align:"right",children:"Median"}),e.jsx(c,{align:"right",children:"Q3"}),e.jsx(c,{align:"right",children:"Max"}),e.jsx(c,{align:"right",children:"Mean"}),e.jsx(c,{align:"right",children:"IQR"}),e.jsx(c,{align:"right",children:"N"})]})}),e.jsx(Ne,{children:ne.map((t,i)=>e.jsxs(ve,{children:[e.jsx(c,{component:"th",scope:"row",children:t.category}),e.jsx(c,{align:"right",children:t.min.toFixed(2)}),e.jsx(c,{align:"right",children:t.q1.toFixed(2)}),e.jsx(c,{align:"right",children:t.median.toFixed(2)}),e.jsx(c,{align:"right",children:t.q3.toFixed(2)}),e.jsx(c,{align:"right",children:t.max.toFixed(2)}),e.jsx(c,{align:"right",children:t.mean.toFixed(2)}),e.jsx(c,{align:"right",children:t.iqr.toFixed(2)}),e.jsx(c,{align:"right",children:t.sampleSize})]},`${t.category}-${i}`))})]})})]})]})})]})]})};export{gt as default};
