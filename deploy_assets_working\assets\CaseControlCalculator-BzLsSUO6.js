import{u as ue,j as e,B as E,e as m,R as P,G as b,ao as ne,ap as re,aq as ie,ar as R,as as d,at as le,ah as q,f as A,bG as he,bH as xe,Q as me,ai as pe,ba as je,bb as ge,bj as ve,i as V,D as be}from"./mui-libs-CfwFIaTD.js";import{r as D}from"./react-libs-Cr2nE3UY.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{S as z}from"./StatsCard-op8tGQ0a.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import{j as fe}from"./other-utils-CR9xr_gI.js";import{c as Ce}from"./non-parametric-Cf6Ds91x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";class I{static oddsRatio(s,o,t,c){const a=s*c/(o*t),h=Math.log(a),n=Math.sqrt(1/s+1/o+1/t+1/c);return{or:a,ci:[Math.exp(h-1.96*n),Math.exp(h******n)]}}static directlyAdjustedOddsRatio(s){let o=0,t=0;const c=1.96;if(s.forEach(({a:l,b:u,c:x,d:g})=>{if(u*x===0)return;const f=l*g/(u*x),S=(1/l||0)+(1/u||0)+(1/x||0)+(1/g||0);if(S===0)return;const y=1/S;o+=y*Math.log(f),t+=y}),t===0)return{orDirect:NaN,ci:[NaN,NaN]};const a=Math.exp(o/t),h=1/Math.sqrt(t),n=Math.exp(Math.log(a)-c*h),p=Math.exp(Math.log(a)+c*h);return{orDirect:a,ci:[n,p]}}static mantelHaenszelOR(s){let o=0,t=0;if(s.forEach(({a,b:h,c:n,d:p})=>{const l=a+h+n+p;l!==0&&(o+=a*p/l,t+=h*n/l)}),t===0)return{mhOR:0,ci:[0,0]};const c=o/t;return{mhOR:c,ci:this.calculateMantelHaenszelCI(s,c)}}static calculateMantelHaenszelCI(s,o){let t=0,c=0,a=0,h=0,n=0;if(s.forEach(({a:g,b:f,c:S,d:y})=>{const j=g+f+S+y;if(j===0)return;const M=g*y/j,_=f*S/j,H=(g+y)/j,O=(f+S)/j;t+=M,c+=_,a+=H*M,h+=O*_,n+=H*_+O*M}),t===0||c===0)return[0,0];const p=a/(2*Math.pow(t,2))+n/(2*t*c)+h/(2*Math.pow(c,2)),l=1.96,u=Math.exp(Math.log(o)-l*Math.sqrt(p)),x=Math.exp(Math.log(o)+l*Math.sqrt(p));return[u,x]}static uncorrectedChiSquareTest(s,o,t,c){return Ce([[s,o],[t,c]])}static chiSquareTestYates(s,o,t,c){const a=s+o+t+c,h=(s+o)*(s+t)/a,n=(s+o)*(o+c)/a,p=(t+c)*(s+t)/a,l=(t+c)*(o+c)/a,u=Math.pow(Math.abs(s-h)-.5,2)/h+Math.pow(Math.abs(o-n)-.5,2)/n+Math.pow(Math.abs(t-p)-.5,2)/p+Math.pow(Math.abs(c-l)-.5,2)/l;return{chi2:u,p:this.chiSquarePValue(u,1)}}static fisherExact(s,o,t,c){const a=l=>{if(l===0||l===1)return 1;let u=1;for(let x=2;x<=l;x++)u*=x;return u},h=(l,u,x,g)=>a(l+u)*a(x+g)*a(l+x)*a(u+g)/(a(l)*a(u)*a(x)*a(g)*a(l+u+x+g)),n=h(s,o,t,c);let p=0;for(let l=0;l<=Math.min(s+o,s+t);l++){const u=s+o-l,x=s+t-l,g=o+c-u;if(u>=0&&x>=0&&g>=0){const f=h(l,u,x,g);f<=n&&(p+=f)}}return p}static chiSquarePValue(s,o){return o<=0?null:1-fe.chisquare.cdf(s,o)}static mantelHaenszelChiSquare(s){let o=0,t=0;if(s.forEach(({a,b:h,c:n,d:p})=>{const l=a+h+n+p;if(l===0||l===1)return;const u=(a+h)*(a+n)/l,x=(a+h)*(n+p)*(a+n)*(h+p)/(Math.pow(l,2)*(l-1));o+=a-u,t+=x}),t===0)return{chi2:0,p:null};const c=Math.pow(o,2)/t;return{chi2:c,p:this.chiSquarePValue(c,1)}}}const Ne=()=>{var _,H,O,k,F,G,Q,W,J,K,L,X,Z,ee,te,ae;const N=ue(),[s,o]=D.useState({a:0,b:0,c:0,d:0}),[t,c]=D.useState([]),[a,h]=D.useState(0),[n,p]=D.useState({}),l=(r,i)=>{const v=i===""?0:parseInt(i,10);o(C=>({...C,[r]:isNaN(v)?0:v}))},u=(r,i,v)=>{const C=v===""?0:parseInt(v,10);c(w=>w.map(T=>T.id===r?{...T,[i]:isNaN(C)?0:C}:T))},x=()=>{const r={id:`stratum_${Date.now()}`,a:0,b:0,c:0,d:0};c(i=>[...i,r]),h(t.length)},g=()=>{if(t.length<=1)return;const r=t.filter((i,v)=>v!==a);c(r),a>=r.length&&h(Math.max(0,r.length-1))},f=()=>{let r={...s};return t.forEach(i=>{r.a+=i.a,r.b+=i.b,r.c+=i.c,r.d+=i.d}),r},S=()=>{try{const r=f(),{a:i,b:v,c:C,d:w}=r,T=i+v+C+w;if(T===0)return;const oe=I.oddsRatio(i,v,C,w),ce=I.chiSquareTestYates(i,v,C,w),de=I.uncorrectedChiSquareTest(i,v,C,w);let se=null;T<100&&(se=I.fisherExact(i,v,C,w));let $=[s,...t],U=null,Y=null,B=null;t.length>0&&(U=I.mantelHaenszelOR($),Y=I.mantelHaenszelChiSquare($),B=I.directlyAdjustedOddsRatio($)),p({oddsRatio:oe,chiSquareYates:ce,chiSquareUncorrected:de,fisherExact:se,...U&&{mantelHaenszel:U},...Y&&{mantelHaenszelChiSquare:Y},...B&&{directlyAdjustedOR:B}})}catch(r){console.error("Error calculating results:",r)}},y=()=>{o({a:0,b:0,c:0,d:0}),c([]),p({})},j=(r,i=3)=>r.toFixed(i),M=(r,i=3)=>`${j(r[0],i)} - ${j(r[1],i)}`;return e.jsxs(E,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Case-Control Study Calculator"}),e.jsx(m,{variant:"body1",paragraph:!0,children:"Calculate odds ratios and other measures of association for case-control studies."}),e.jsxs(P,{elevation:1,sx:{p:3,mb:3},children:[e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"2×2 Contingency Table"}),e.jsx(m,{variant:"body2",paragraph:!0,children:"Enter the values for your 2×2 table to calculate epidemiological measures."}),e.jsxs(b,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(b,{item:!0,xs:12,md:8,children:e.jsx(ne,{component:P,variant:"outlined",children:e.jsxs(re,{"aria-label":"2x2 contingency table",children:[e.jsx(ie,{children:e.jsxs(R,{children:[e.jsx(d,{}),e.jsx(d,{align:"center",children:"Cases"}),e.jsx(d,{align:"center",children:"Controls"}),e.jsx(d,{align:"center",children:"Total"})]})}),e.jsxs(le,{children:[e.jsxs(R,{children:[e.jsx(d,{component:"th",scope:"row",children:"Exposed"}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:s.a||"",onChange:r=>l("a",r.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:s.b||"",onChange:r=>l("b",r.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:s.a+s.b})})]}),e.jsxs(R,{children:[e.jsx(d,{component:"th",scope:"row",children:"Unexposed"}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:s.c||"",onChange:r=>l("c",r.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:s.d||"",onChange:r=>l("d",r.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:s.c+s.d})})]}),e.jsxs(R,{children:[e.jsx(d,{component:"th",scope:"row",children:"Total"}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:s.a+s.c})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:s.b+s.d})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:s.a+s.b+s.c+s.d})})]})]})]})})}),e.jsxs(b,{item:!0,xs:12,md:4,sx:{display:"flex",flexDirection:"column",justifyContent:"center"},children:[e.jsxs(E,{sx:{display:"flex",gap:2,mb:2},children:[e.jsx(A,{variant:"contained",color:"primary",startIcon:e.jsx(he,{}),onClick:S,disabled:Object.values(s).every(r=>r===0)&&t.length===0,children:"Calculate"}),e.jsx(A,{variant:"outlined",startIcon:e.jsx(xe,{}),onClick:y,children:"Reset"})]}),e.jsx(m,{variant:"body2",color:"text.secondary",children:"Enter the values in the 2×2 table and click Calculate to compute epidemiological measures."})]})]}),e.jsxs(E,{sx:{mb:3,mt:3},children:[e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"Stratified Analysis (Controlling for potential confounders)"}),e.jsx(m,{variant:"body2",paragraph:!0,children:"Add strata to calculate the Directly adjusted and Mantel-Haenszel adjusted odds ratios. The crude OR will be calculated by combining all tables."}),e.jsxs(E,{sx:{display:"flex",alignItems:"center",mb:2,gap:2},children:[e.jsx(A,{variant:"outlined",startIcon:e.jsx(me,{}),onClick:x,children:"Add Stratum"}),t.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(pe,{sx:{minWidth:120},children:e.jsx(je,{value:a.toString(),onChange:r=>h(Number(r.target.value)),size:"small",children:t.map((r,i)=>e.jsxs(ge,{value:i.toString(),children:["Stratum ",i+1]},i))})}),e.jsx(A,{variant:"outlined",color:"error",startIcon:e.jsx(ve,{}),onClick:g,disabled:t.length<=0,children:"Delete Stratum"})]})]}),t.length>0&&e.jsxs(E,{sx:{mb:2,p:2,border:`1px solid ${V(N.palette.primary.main,.2)}`,borderRadius:1},children:[e.jsxs(m,{variant:"subtitle2",sx:{mb:1},children:["Stratum ",a+1]}),e.jsx(ne,{component:P,variant:"outlined",children:e.jsxs(re,{size:"small",children:[e.jsx(ie,{children:e.jsxs(R,{children:[e.jsx(d,{}),e.jsx(d,{align:"center",children:"Cases"}),e.jsx(d,{align:"center",children:"Controls"}),e.jsx(d,{align:"center",children:"Total"})]})}),e.jsxs(le,{children:[e.jsxs(R,{children:[e.jsx(d,{children:"Exposed"}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:((_=t[a])==null?void 0:_.a)||"",onChange:r=>{var i;return u((i=t[a])==null?void 0:i.id,"a",r.target.value)},inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:((H=t[a])==null?void 0:H.b)||"",onChange:r=>{var i;return u((i=t[a])==null?void 0:i.id,"b",r.target.value)},inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:(((O=t[a])==null?void 0:O.a)||0)+(((k=t[a])==null?void 0:k.b)||0)})})]}),e.jsxs(R,{children:[e.jsx(d,{children:"Unexposed"}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:((F=t[a])==null?void 0:F.c)||"",onChange:r=>{var i;return u((i=t[a])==null?void 0:i.id,"c",r.target.value)},inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(d,{align:"center",children:e.jsx(q,{type:"number",value:((G=t[a])==null?void 0:G.d)||"",onChange:r=>{var i;return u((i=t[a])==null?void 0:i.id,"d",r.target.value)},inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:(((Q=t[a])==null?void 0:Q.c)||0)+(((W=t[a])==null?void 0:W.d)||0)})})]}),e.jsxs(R,{children:[e.jsx(d,{component:"th",scope:"row",children:"Total"}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:(((J=t[a])==null?void 0:J.a)||0)+(((K=t[a])==null?void 0:K.c)||0)})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:(((L=t[a])==null?void 0:L.b)||0)+(((X=t[a])==null?void 0:X.d)||0)})}),e.jsx(d,{align:"center",children:e.jsx(m,{variant:"body2",children:(((Z=t[a])==null?void 0:Z.a)||0)+(((ee=t[a])==null?void 0:ee.b)||0)+(((te=t[a])==null?void 0:te.c)||0)+(((ae=t[a])==null?void 0:ae.d)||0)})})]})]})]})})]})]}),Object.keys(n).length>0&&e.jsxs(E,{sx:{mt:4},children:[e.jsx(be,{sx:{mb:3}}),e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Results"}),e.jsxs(b,{container:!0,spacing:3,children:[n.oddsRatio&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Crude Odds Ratio",value:j(n.oddsRatio.or),description:`95% CI: ${M(n.oddsRatio.ci)}`,color:"primary",variant:"outlined",tooltip:"Crude odds ratio of exposure in cases vs. controls (combined across all tables)"})}),n.directlyAdjustedOR&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Directly Adjusted OR",value:j(n.directlyAdjustedOR.orDirect),description:`95% CI: ${M(n.directlyAdjustedOR.ci)}`,color:"primary",variant:"outlined",tooltip:"Directly adjusted odds ratio for stratified data"})}),n.mantelHaenszel&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Mantel-Haenszel OR",value:j(n.mantelHaenszel.mhOR),description:`95% CI: ${M(n.mantelHaenszel.ci)}`,color:"secondary",variant:"outlined",tooltip:"Adjusted odds ratio across strata"})}),n.chiSquareUncorrected&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Chi-Square Test (Uncorrected)",value:j(n.chiSquareUncorrected.chiSquare),description:`p-value: ${j(n.chiSquareUncorrected.pValue)} (df=${n.chiSquareUncorrected.df})`,color:"warning",variant:"outlined",tooltip:"Uncorrected Chi-square test for consolidated 2×2 table"})}),n.chiSquareYates&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Chi-Square Test (Yates)",value:j(n.chiSquareYates.chi2),description:n.chiSquareYates.p!==null?`p-value: ${j(n.chiSquareYates.p)}`:"p-value: Not calculated",color:"warning",variant:"outlined",tooltip:"Chi-square test with Yates' correction for consolidated 2×2 table"})}),n.mantelHaenszelChiSquare&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Mantel-Haenszel Chi-Square",value:j(n.mantelHaenszelChiSquare.chi2),description:n.mantelHaenszelChiSquare.p!==null?`p-value: ${j(n.mantelHaenszelChiSquare.p)}`:"p-value: Not calculated",color:"info",variant:"outlined",tooltip:"Mantel-Haenszel Chi-Square test for stratified data (df=1)"})}),n.fisherExact!==null&&n.fisherExact!==void 0&&e.jsx(b,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{title:"Fisher's Exact Test",value:`p = ${j(n.fisherExact)}`,description:`Significance at α = 0.05: ${n.fisherExact<.05?"Yes":"No"}`,color:"success",variant:"outlined",tooltip:"Fisher's exact test for small sample sizes (consolidated table)"})})]}),e.jsxs(P,{variant:"outlined",sx:{mt:3,p:2,backgroundColor:V(N.palette.info.main,.05),borderColor:V(N.palette.info.main,.2)},children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Interpretation Guidelines:"}),e.jsxs(m,{variant:"body2",children:["• ",e.jsx("strong",{children:"Odds Ratio = 1:"})," No association between exposure and disease",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Odds Ratio > 1:"})," Positive association (exposure may increase disease odds)",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Odds Ratio < 1:"})," Negative association (exposure may decrease disease odds)",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Statistical significance:"})," If the 95% confidence interval does not include 1, or p-value < 0.05"]})]})]})]})]})};export{Ne as default};
