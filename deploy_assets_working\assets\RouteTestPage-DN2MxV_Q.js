import{j as e,B as h,e as s,G as u,R as x,h as P,D as C,g as p,f as B,L as G,m as g,r as j}from"./mui-libs-CfwFIaTD.js";import{r as c}from"./react-libs-Cr2nE3UY.js";import{r as f}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const D=()=>{const[R,y]=c.useState(window.location.hash),[a,v]=c.useState([]),[n,b]=c.useState(null);c.useEffect(()=>{const t=()=>{var m;y(window.location.hash),v(f.getAllRoutes());const i=window.location.hash.slice(1),o=i.split("/").filter(Boolean);let l="home",d="";o.length>0&&(o[0]==="app"&&o.length>1?(l=o[1],d=o[2]||""):(l=o[0],d=o[1]||""));const r=f.findMatchingRoute(l,d);b({hash:i,pathSegments:o,page:l,subPage:d,foundRoute:r?{path:r.path,componentName:(m=r.component)==null?void 0:m.name,requiresAuth:r.requiresAuth,allowGuest:r.allowGuest}:null})};return t(),window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]);const w=["#home","#dashboard","#inference/ttest","#inference/anova","#stats/descriptives","#data-management/import","#publication-ready/table1","#advanced-analysis/cfa"],A=t=>{window.location.hash=t};return e.jsxs(h,{p:3,children:[e.jsx(s,{variant:"h4",gutterBottom:!0,children:"Route Testing & Debugging"}),e.jsxs(u,{container:!0,spacing:3,children:[e.jsx(u,{item:!0,xs:12,md:6,children:e.jsxs(x,{sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Current Route Status"}),e.jsxs(h,{mb:2,children:[e.jsx(s,{variant:"body2",color:"text.secondary",children:"Current Hash:"}),e.jsx(P,{label:R||"(empty)",variant:"outlined"})]}),n&&e.jsxs(e.Fragment,{children:[e.jsxs(s,{variant:"body2",color:"text.secondary",children:["Parsed Page: ",e.jsx("strong",{children:n.page})]}),e.jsxs(s,{variant:"body2",color:"text.secondary",children:["Parsed SubPage: ",e.jsx("strong",{children:n.subPage||"(none)"})]}),e.jsx(C,{sx:{my:2}}),n.foundRoute?e.jsxs(p,{severity:"success",children:[e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Route Found:"})," ",n.foundRoute.path]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Component:"})," ",n.foundRoute.componentName]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Requires Auth:"})," ",n.foundRoute.requiresAuth?"Yes":"No"]}),e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"Allow Guest:"})," ",n.foundRoute.allowGuest?"Yes":"No"]})]}):e.jsx(p,{severity:"error",children:e.jsxs(s,{variant:"body2",children:[e.jsx("strong",{children:"No Route Found"}),' for page "',n.page,'"',n.subPage&&` and subPage "${n.subPage}"`]})})]})]})}),e.jsx(u,{item:!0,xs:12,md:6,children:e.jsxs(x,{sx:{p:2},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Test Routes"}),e.jsx(s,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Click to navigate to different routes:"}),e.jsx(h,{display:"flex",flexDirection:"column",gap:1,children:w.map(t=>e.jsx(B,{variant:"outlined",size:"small",onClick:()=>A(t),sx:{justifyContent:"flex-start"},children:t},t))})]})}),e.jsx(u,{item:!0,xs:12,children:e.jsxs(x,{sx:{p:2},children:[e.jsxs(s,{variant:"h6",gutterBottom:!0,children:["All Registered Routes (",a.length,")"]}),e.jsxs(G,{dense:!0,children:[a.slice(0,20).map((t,i)=>{var o;return e.jsx(g,{divider:!0,children:e.jsx(j,{primary:t.path,secondary:`Component: ${((o=t.component)==null?void 0:o.name)||"Unknown"} | Auth: ${t.requiresAuth?"Required":"Not Required"} | Guest: ${t.allowGuest?"Allowed":"Not Allowed"}`})},i)}),a.length>20&&e.jsx(g,{children:e.jsx(j,{primary:`... and ${a.length-20} more routes`,secondary:"Check browser console for full list"})})]})]})})]})]})};export{D as default};
