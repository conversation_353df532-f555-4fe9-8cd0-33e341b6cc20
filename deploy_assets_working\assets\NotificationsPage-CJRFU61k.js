import{u as Se,j as i,B as v,ae as Pe,e as P,R as X,i as F,G as W,ah as Ce,am as Oe,ai as G,b9 as U,ba as V,bb as y,br as z,aE as re,I as ae,e2 as Te,b_ as We,cs as Ne,D as Ye,f as B,e3 as $,bj as Fe,bc as se,e4 as Ee,k as _e,l as Ie,h as Re,s as qe,di as ze,e5 as Be}from"./mui-libs-CfwFIaTD.js";import{r as E}from"./react-libs-Cr2nE3UY.js";import{_ as Ae,N as Le}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const he=6048e5,He=864e5,fe=6e4,me=36e5,ie=Symbol.for("constructDateFrom");function k(t,e){return typeof t=="function"?t(e):t&&typeof t=="object"&&ie in t?t[ie](e):t instanceof Date?new t.constructor(e):new Date(e)}function b(t,e){return k(e||t,t)}function Qe(t,e,n){const r=b(t,n==null?void 0:n.in);return isNaN(e)?k(t,NaN):(r.setDate(r.getDate()+e),r)}let Xe={};function Q(){return Xe}function q(t,e){var l,h,u,g;const n=Q(),r=(e==null?void 0:e.weekStartsOn)??((h=(l=e==null?void 0:e.locale)==null?void 0:l.options)==null?void 0:h.weekStartsOn)??n.weekStartsOn??((g=(u=n.locale)==null?void 0:u.options)==null?void 0:g.weekStartsOn)??0,a=b(t,e==null?void 0:e.in),s=a.getDay(),c=(s<r?7:0)+s-r;return a.setDate(a.getDate()-c),a.setHours(0,0,0,0),a}function L(t,e){return q(t,{...e,weekStartsOn:1})}function ge(t,e){const n=b(t,e==null?void 0:e.in),r=n.getFullYear(),a=k(n,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);const s=L(a),c=k(n,0);c.setFullYear(r,0,4),c.setHours(0,0,0,0);const l=L(c);return n.getTime()>=s.getTime()?r+1:n.getTime()>=l.getTime()?r:r-1}function oe(t){const e=b(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function ye(t,...e){const n=k.bind(null,e.find(r=>typeof r=="object"));return e.map(n)}function H(t,e){const n=b(t,e==null?void 0:e.in);return n.setHours(0,0,0,0),n}function Ge(t,e,n){const[r,a]=ye(n==null?void 0:n.in,t,e),s=H(r),c=H(a),l=+s-oe(s),h=+c-oe(c);return Math.round((l-h)/He)}function Ue(t,e){const n=ge(t,e),r=k(t,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),L(r)}function we(t){return k(t,Date.now())}function xe(t,e,n){const[r,a]=ye(n==null?void 0:n.in,t,e);return+H(r)==+H(a)}function Ve(t){return t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function $e(t){return!(!Ve(t)&&typeof t!="number"||isNaN(+b(t)))}function Je(t,e){const n=b(t,e==null?void 0:e.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}const Ze={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ke=(t,e,n)=>{let r;const a=Ze[t];return typeof a=="string"?r=a:e===1?r=a.one:r=a.other.replace("{{count}}",e.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function J(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const et={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},tt={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},nt={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},rt={date:J({formats:et,defaultWidth:"full"}),time:J({formats:tt,defaultWidth:"full"}),dateTime:J({formats:nt,defaultWidth:"full"})},at={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},st=(t,e,n,r)=>at[t];function _(t){return(e,n)=>{const r=n!=null&&n.context?String(n.context):"standalone";let a;if(r==="formatting"&&t.formattingValues){const c=t.defaultFormattingWidth||t.defaultWidth,l=n!=null&&n.width?String(n.width):c;a=t.formattingValues[l]||t.formattingValues[c]}else{const c=t.defaultWidth,l=n!=null&&n.width?String(n.width):t.defaultWidth;a=t.values[l]||t.values[c]}const s=t.argumentCallback?t.argumentCallback(e):e;return a[s]}}const it={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},ot={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},ct={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},ut={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},lt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},dt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ht=(t,e)=>{const n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},ft={ordinalNumber:ht,era:_({values:it,defaultWidth:"wide"}),quarter:_({values:ot,defaultWidth:"wide",argumentCallback:t=>t-1}),month:_({values:ct,defaultWidth:"wide"}),day:_({values:ut,defaultWidth:"wide"}),dayPeriod:_({values:lt,defaultWidth:"wide",formattingValues:dt,defaultFormattingWidth:"wide"})};function I(t){return(e,n={})=>{const r=n.width,a=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(a);if(!s)return null;const c=s[0],l=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],h=Array.isArray(l)?gt(l,p=>p.test(c)):mt(l,p=>p.test(c));let u;u=t.valueCallback?t.valueCallback(h):h,u=n.valueCallback?n.valueCallback(u):u;const g=e.slice(c.length);return{value:u,rest:g}}}function mt(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}function gt(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}function yt(t){return(e,n={})=>{const r=e.match(t.matchPattern);if(!r)return null;const a=r[0],s=e.match(t.parsePattern);if(!s)return null;let c=t.valueCallback?t.valueCallback(s[0]):s[0];c=n.valueCallback?n.valueCallback(c):c;const l=e.slice(a.length);return{value:c,rest:l}}}const wt=/^(\d+)(th|st|nd|rd)?/i,xt=/\d+/i,bt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Dt={any:[/^b/i,/^(a|c)/i]},kt={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},pt={any:[/1/i,/2/i,/3/i,/4/i]},Mt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},vt={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},jt={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},St={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Pt={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Ct={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Ot={ordinalNumber:yt({matchPattern:wt,parsePattern:xt,valueCallback:t=>parseInt(t,10)}),era:I({matchPatterns:bt,defaultMatchWidth:"wide",parsePatterns:Dt,defaultParseWidth:"any"}),quarter:I({matchPatterns:kt,defaultMatchWidth:"wide",parsePatterns:pt,defaultParseWidth:"any",valueCallback:t=>t+1}),month:I({matchPatterns:Mt,defaultMatchWidth:"wide",parsePatterns:vt,defaultParseWidth:"any"}),day:I({matchPatterns:jt,defaultMatchWidth:"wide",parsePatterns:St,defaultParseWidth:"any"}),dayPeriod:I({matchPatterns:Pt,defaultMatchWidth:"any",parsePatterns:Ct,defaultParseWidth:"any"})},Tt={code:"en-US",formatDistance:Ke,formatLong:rt,formatRelative:st,localize:ft,match:Ot,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Wt(t,e){const n=b(t,e==null?void 0:e.in);return Ge(n,Je(n))+1}function Nt(t,e){const n=b(t,e==null?void 0:e.in),r=+L(n)-+Ue(n);return Math.round(r/he)+1}function be(t,e){var g,p,D,j;const n=b(t,e==null?void 0:e.in),r=n.getFullYear(),a=Q(),s=(e==null?void 0:e.firstWeekContainsDate)??((p=(g=e==null?void 0:e.locale)==null?void 0:g.options)==null?void 0:p.firstWeekContainsDate)??a.firstWeekContainsDate??((j=(D=a.locale)==null?void 0:D.options)==null?void 0:j.firstWeekContainsDate)??1,c=k((e==null?void 0:e.in)||t,0);c.setFullYear(r+1,0,s),c.setHours(0,0,0,0);const l=q(c,e),h=k((e==null?void 0:e.in)||t,0);h.setFullYear(r,0,s),h.setHours(0,0,0,0);const u=q(h,e);return+n>=+l?r+1:+n>=+u?r:r-1}function Yt(t,e){var l,h,u,g;const n=Q(),r=(e==null?void 0:e.firstWeekContainsDate)??((h=(l=e==null?void 0:e.locale)==null?void 0:l.options)==null?void 0:h.firstWeekContainsDate)??n.firstWeekContainsDate??((g=(u=n.locale)==null?void 0:u.options)==null?void 0:g.firstWeekContainsDate)??1,a=be(t,e),s=k((e==null?void 0:e.in)||t,0);return s.setFullYear(a,0,r),s.setHours(0,0,0,0),q(s,e)}function Ft(t,e){const n=b(t,e==null?void 0:e.in),r=+q(n,e)-+Yt(n,e);return Math.round(r/he)+1}function f(t,e){const n=t<0?"-":"",r=Math.abs(t).toString().padStart(e,"0");return n+r}const O={y(t,e){const n=t.getFullYear(),r=n>0?n:1-n;return f(e==="yy"?r%100:r,e.length)},M(t,e){const n=t.getMonth();return e==="M"?String(n+1):f(n+1,2)},d(t,e){return f(t.getDate(),e.length)},a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(t,e){return f(t.getHours()%12||12,e.length)},H(t,e){return f(t.getHours(),e.length)},m(t,e){return f(t.getMinutes(),e.length)},s(t,e){return f(t.getSeconds(),e.length)},S(t,e){const n=e.length,r=t.getMilliseconds(),a=Math.trunc(r*Math.pow(10,n-3));return f(a,e.length)}},N={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},ce={G:function(t,e,n){const r=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if(e==="yo"){const r=t.getFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return O.y(t,e)},Y:function(t,e,n,r){const a=be(t,r),s=a>0?a:1-a;if(e==="YY"){const c=s%100;return f(c,2)}return e==="Yo"?n.ordinalNumber(s,{unit:"year"}):f(s,e.length)},R:function(t,e){const n=ge(t);return f(n,e.length)},u:function(t,e){const n=t.getFullYear();return f(n,e.length)},Q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return f(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return f(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){const r=t.getMonth();switch(e){case"M":case"MM":return O.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){const r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return f(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){const a=Ft(t,r);return e==="wo"?n.ordinalNumber(a,{unit:"week"}):f(a,e.length)},I:function(t,e,n){const r=Nt(t);return e==="Io"?n.ordinalNumber(r,{unit:"week"}):f(r,e.length)},d:function(t,e,n){return e==="do"?n.ordinalNumber(t.getDate(),{unit:"date"}):O.d(t,e)},D:function(t,e,n){const r=Wt(t);return e==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):f(r,e.length)},E:function(t,e,n){const r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){const a=t.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(s);case"ee":return f(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){const a=t.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(s);case"cc":return f(s,e.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){const r=t.getDay(),a=r===0?7:r;switch(e){case"i":return String(a);case"ii":return f(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){const a=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,e,n){const r=t.getHours();let a;switch(r===12?a=N.noon:r===0?a=N.midnight:a=r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,n){const r=t.getHours();let a;switch(r>=17?a=N.evening:r>=12?a=N.afternoon:r>=4?a=N.morning:a=N.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,n){if(e==="ho"){let r=t.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return O.h(t,e)},H:function(t,e,n){return e==="Ho"?n.ordinalNumber(t.getHours(),{unit:"hour"}):O.H(t,e)},K:function(t,e,n){const r=t.getHours()%12;return e==="Ko"?n.ordinalNumber(r,{unit:"hour"}):f(r,e.length)},k:function(t,e,n){let r=t.getHours();return r===0&&(r=24),e==="ko"?n.ordinalNumber(r,{unit:"hour"}):f(r,e.length)},m:function(t,e,n){return e==="mo"?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):O.m(t,e)},s:function(t,e,n){return e==="so"?n.ordinalNumber(t.getSeconds(),{unit:"second"}):O.s(t,e)},S:function(t,e){return O.S(t,e)},X:function(t,e,n){const r=t.getTimezoneOffset();if(r===0)return"Z";switch(e){case"X":return le(r);case"XXXX":case"XX":return T(r);case"XXXXX":case"XXX":default:return T(r,":")}},x:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"x":return le(r);case"xxxx":case"xx":return T(r);case"xxxxx":case"xxx":default:return T(r,":")}},O:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+ue(r,":");case"OOOO":default:return"GMT"+T(r,":")}},z:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+ue(r,":");case"zzzz":default:return"GMT"+T(r,":")}},t:function(t,e,n){const r=Math.trunc(+t/1e3);return f(r,e.length)},T:function(t,e,n){return f(+t,e.length)}};function ue(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),a=Math.trunc(r/60),s=r%60;return s===0?n+String(a):n+String(a)+e+f(s,2)}function le(t,e){return t%60===0?(t>0?"-":"+")+f(Math.abs(t)/60,2):T(t,e)}function T(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),a=f(Math.trunc(r/60),2),s=f(r%60,2);return n+a+e+s}const de=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},De=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},Et=(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return de(t,e);let s;switch(r){case"P":s=e.dateTime({width:"short"});break;case"PP":s=e.dateTime({width:"medium"});break;case"PPP":s=e.dateTime({width:"long"});break;case"PPPP":default:s=e.dateTime({width:"full"});break}return s.replace("{{date}}",de(r,e)).replace("{{time}}",De(a,e))},_t={p:De,P:Et},It=/^D+$/,Rt=/^Y+$/,qt=["D","DD","YY","YYYY"];function zt(t){return It.test(t)}function Bt(t){return Rt.test(t)}function At(t,e,n){const r=Lt(t,e,n);if(console.warn(r),qt.includes(t))throw new RangeError(r)}function Lt(t,e,n){const r=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Ht=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Qt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Xt=/^'([^]*?)'?$/,Gt=/''/g,Ut=/[a-zA-Z]/;function Z(t,e,n){var g,p,D,j;const r=Q(),a=r.locale??Tt,s=r.firstWeekContainsDate??((p=(g=r.locale)==null?void 0:g.options)==null?void 0:p.firstWeekContainsDate)??1,c=r.weekStartsOn??((j=(D=r.locale)==null?void 0:D.options)==null?void 0:j.weekStartsOn)??0,l=b(t,n==null?void 0:n.in);if(!$e(l))throw new RangeError("Invalid time value");let h=e.match(Qt).map(w=>{const m=w[0];if(m==="p"||m==="P"){const Y=_t[m];return Y(w,a.formatLong)}return w}).join("").match(Ht).map(w=>{if(w==="''")return{isToken:!1,value:"'"};const m=w[0];if(m==="'")return{isToken:!1,value:Vt(w)};if(ce[m])return{isToken:!0,value:w};if(m.match(Ut))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return{isToken:!1,value:w}});a.localize.preprocessor&&(h=a.localize.preprocessor(l,h));const u={firstWeekContainsDate:s,weekStartsOn:c,locale:a};return h.map(w=>{if(!w.isToken)return w.value;const m=w.value;(Bt(m)||zt(m))&&At(m,e,String(t));const Y=ce[m[0]];return Y(l,m,a.localize,u)}).join("")}function Vt(t){const e=t.match(Xt);return e?e[1].replace(Gt,"'"):t}function $t(t,e){return xe(k(t,t),we(t))}function Jt(t,e,n){return Qe(t,-1,n)}function Zt(t,e){return xe(k(t,t),Jt(we(t)))}function Kt(t,e){const n=()=>k(e==null?void 0:e.in,NaN),a=rn(t);let s;if(a.date){const u=an(a.date,2);s=sn(u.restDateString,u.year)}if(!s||isNaN(+s))return n();const c=+s;let l=0,h;if(a.time&&(l=on(a.time),isNaN(l)))return n();if(a.timezone){if(h=cn(a.timezone),isNaN(h))return n()}else{const u=new Date(c+l),g=b(0,e==null?void 0:e.in);return g.setFullYear(u.getUTCFullYear(),u.getUTCMonth(),u.getUTCDate()),g.setHours(u.getUTCHours(),u.getUTCMinutes(),u.getUTCSeconds(),u.getUTCMilliseconds()),g}return b(c+l+h,e==null?void 0:e.in)}const A={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},en=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,tn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,nn=/^([+-])(\d{2})(?::?(\d{2}))?$/;function rn(t){const e={},n=t.split(A.dateTimeDelimiter);let r;if(n.length>2)return e;if(/:/.test(n[0])?r=n[0]:(e.date=n[0],r=n[1],A.timeZoneDelimiter.test(e.date)&&(e.date=t.split(A.timeZoneDelimiter)[0],r=t.substr(e.date.length,t.length))),r){const a=A.timezone.exec(r);a?(e.time=r.replace(a[1],""),e.timezone=a[1]):e.time=r}return e}function an(t,e){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),r=t.match(n);if(!r)return{year:NaN,restDateString:""};const a=r[1]?parseInt(r[1]):null,s=r[2]?parseInt(r[2]):null;return{year:s===null?a:s*100,restDateString:t.slice((r[1]||r[2]).length)}}function sn(t,e){if(e===null)return new Date(NaN);const n=t.match(en);if(!n)return new Date(NaN);const r=!!n[4],a=R(n[1]),s=R(n[2])-1,c=R(n[3]),l=R(n[4]),h=R(n[5])-1;if(r)return fn(e,l,h)?un(e,l,h):new Date(NaN);{const u=new Date(0);return!dn(e,s,c)||!hn(e,a)?new Date(NaN):(u.setUTCFullYear(e,s,Math.max(a,c)),u)}}function R(t){return t?parseInt(t):1}function on(t){const e=t.match(tn);if(!e)return NaN;const n=K(e[1]),r=K(e[2]),a=K(e[3]);return mn(n,r,a)?n*me+r*fe+a*1e3:NaN}function K(t){return t&&parseFloat(t.replace(",","."))||0}function cn(t){if(t==="Z")return 0;const e=t.match(nn);if(!e)return 0;const n=e[1]==="+"?-1:1,r=parseInt(e[2]),a=e[3]&&parseInt(e[3])||0;return gn(r,a)?n*(r*me+a*fe):NaN}function un(t,e,n){const r=new Date(0);r.setUTCFullYear(t,0,4);const a=r.getUTCDay()||7,s=(e-1)*7+n+1-a;return r.setUTCDate(r.getUTCDate()+s),r}const ln=[31,null,31,30,31,30,31,31,30,31,30,31];function ke(t){return t%400===0||t%4===0&&t%100!==0}function dn(t,e,n){return e>=0&&e<=11&&n>=1&&n<=(ln[e]||(ke(t)?29:28))}function hn(t,e){return e>=1&&e<=(ke(t)?366:365)}function fn(t,e,n){return e>=1&&e<=53&&n>=0&&n<=6}function mn(t,e,n){return t===24?e===0&&n===0:n>=0&&n<60&&e>=0&&e<60&&t>=0&&t<25}function gn(t,e){return e>=0&&e<=59}const ee=10,vn=()=>{const{notifications:t,loading:e,markAsRead:n,markAllAsRead:r}=Ae(),a=Se(),[s,c]=E.useState({type:"all",status:"all",dateRange:"all",search:"",sortBy:"created_at",sortOrder:"desc"}),[l,h]=E.useState(1),[u,g]=E.useState(new Set),[p,D]=E.useState(null),j=E.useMemo(()=>{let o=[...t];if(s.search){const d=s.search.toLowerCase();o=o.filter(x=>x.title.toLowerCase().includes(d)||x.message.toLowerCase().includes(d))}if(s.type!=="all"&&(o=o.filter(d=>d.type===s.type)),s.status==="read"?o=o.filter(d=>d.is_read):s.status==="unread"&&(o=o.filter(d=>!d.is_read)),s.dateRange!=="all"){const d=new Date,x=new Date;switch(s.dateRange){case"today":x.setHours(0,0,0,0);break;case"week":x.setDate(d.getDate()-7);break;case"month":x.setMonth(d.getMonth()-1);break}s.dateRange!=="all"&&(o=o.filter(M=>new Date(M.created_at)>=x))}return o.sort((d,x)=>{let M,C;switch(s.sortBy){case"title":M=d.title.toLowerCase(),C=x.title.toLowerCase();break;case"type":M=d.type,C=x.type;break;case"status":M=d.is_read?1:0,C=x.is_read?1:0;break;default:M=new Date(d.created_at),C=new Date(x.created_at)}return s.sortOrder==="asc"?M<C?-1:M>C?1:0:M>C?-1:M<C?1:0}),o},[t,s]),w=Math.ceil(j.length/ee),m=j.slice((l-1)*ee,l*ee),Y=o=>{const d=Kt(o);return $t(d)?`Today at ${Z(d,"h:mm a")}`:Zt(d)?`Yesterday at ${Z(d,"h:mm a")}`:Z(d,"MMM d, yyyy 'at' h:mm a")},te=o=>{switch(o){case"success":return a.palette.success.main;case"warning":return a.palette.warning.main;case"error":return a.palette.error.main;default:return a.palette.info.main}},pe=o=>o.charAt(0).toUpperCase()+o.slice(1),S=(o,d)=>{c(x=>({...x,[o]:d})),h(1)},Me=o=>{const d=new Set(u);d.has(o)?d.delete(o):d.add(o),g(d)},ve=()=>{u.size===m.length?g(new Set):g(new Set(m.map(o=>o.id)))},je=()=>{u.forEach(o=>{const d=t.find(x=>x.id===o);d&&!d.is_read&&n(o)}),g(new Set)},ne=()=>{c({type:"all",status:"all",dateRange:"all",search:"",sortBy:"created_at",sortOrder:"desc"}),h(1)};return e?i.jsx(v,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"50vh"},children:i.jsx(Pe,{})}):i.jsxs(v,{sx:{p:3,maxWidth:1200,mx:"auto"},children:[i.jsxs(v,{sx:{mb:4},children:[i.jsx(P,{variant:"h4",component:"h1",gutterBottom:!0,sx:{fontWeight:600},children:"Notification Center"}),i.jsx(P,{variant:"body1",color:"text.secondary",children:"Stay updated with all your notifications and system messages"})]}),i.jsx(X,{sx:{p:3,mb:3,backgroundColor:F(a.palette.primary.main,.02)},children:i.jsxs(W,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(W,{item:!0,xs:12,md:4,children:i.jsx(Ce,{fullWidth:!0,size:"small",placeholder:"Search notifications...",value:s.search,onChange:o=>S("search",o.target.value),InputProps:{startAdornment:i.jsx(Oe,{sx:{mr:1,color:"text.secondary"}})}})}),i.jsx(W,{item:!0,xs:6,md:2,children:i.jsxs(G,{fullWidth:!0,size:"small",children:[i.jsx(U,{children:"Type"}),i.jsxs(V,{value:s.type,label:"Type",onChange:o=>S("type",o.target.value),children:[i.jsx(y,{value:"all",children:"All Types"}),i.jsx(y,{value:"info",children:"Info"}),i.jsx(y,{value:"success",children:"Success"}),i.jsx(y,{value:"warning",children:"Warning"}),i.jsx(y,{value:"error",children:"Error"})]})]})}),i.jsx(W,{item:!0,xs:6,md:2,children:i.jsxs(G,{fullWidth:!0,size:"small",children:[i.jsx(U,{children:"Status"}),i.jsxs(V,{value:s.status,label:"Status",onChange:o=>S("status",o.target.value),children:[i.jsx(y,{value:"all",children:"All"}),i.jsx(y,{value:"unread",children:"Unread"}),i.jsx(y,{value:"read",children:"Read"})]})]})}),i.jsx(W,{item:!0,xs:6,md:2,children:i.jsxs(G,{fullWidth:!0,size:"small",children:[i.jsx(U,{children:"Date"}),i.jsxs(V,{value:s.dateRange,label:"Date",onChange:o=>S("dateRange",o.target.value),children:[i.jsx(y,{value:"all",children:"All Time"}),i.jsx(y,{value:"today",children:"Today"}),i.jsx(y,{value:"week",children:"This Week"}),i.jsx(y,{value:"month",children:"This Month"})]})]})}),i.jsx(W,{item:!0,xs:6,md:2,children:i.jsxs(z,{direction:"row",spacing:1,children:[i.jsx(re,{title:"Sort options",children:i.jsx(ae,{size:"small",onClick:o=>D(o.currentTarget),children:i.jsx(Te,{})})}),i.jsx(re,{title:"Clear filters",children:i.jsx(ae,{size:"small",onClick:ne,children:i.jsx(We,{})})})]})})]})}),i.jsxs(Ne,{anchorEl:p,open:!!p,onClose:()=>D(null),children:[i.jsx(y,{onClick:()=>{S("sortBy","created_at"),D(null)},children:"Sort by Date"}),i.jsx(y,{onClick:()=>{S("sortBy","title"),D(null)},children:"Sort by Title"}),i.jsx(y,{onClick:()=>{S("sortBy","type"),D(null)},children:"Sort by Type"}),i.jsx(y,{onClick:()=>{S("sortBy","status"),D(null)},children:"Sort by Status"}),i.jsx(Ye,{}),i.jsx(y,{onClick:()=>{S("sortOrder",s.sortOrder==="asc"?"desc":"asc"),D(null)},children:s.sortOrder==="asc"?"Sort Descending":"Sort Ascending"})]}),u.size>0&&i.jsx(X,{sx:{p:2,mb:3,backgroundColor:F(a.palette.primary.main,.08)},children:i.jsxs(z,{direction:"row",alignItems:"center",spacing:2,children:[i.jsxs(P,{variant:"body2",children:[u.size," notification",u.size!==1?"s":""," selected"]}),i.jsx(B,{size:"small",startIcon:i.jsx($,{}),onClick:je,children:"Mark as Read"}),i.jsx(B,{size:"small",color:"error",startIcon:i.jsx(Fe,{}),onClick:()=>g(new Set),children:"Clear Selection"})]})}),i.jsxs(v,{sx:{mb:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[i.jsxs(P,{variant:"body2",color:"text.secondary",children:["Showing ",m.length," of ",j.length," notifications"]}),m.length>0&&i.jsxs(z,{direction:"row",alignItems:"center",spacing:1,children:[i.jsx(se,{size:"small",checked:u.size===m.length&&m.length>0,indeterminate:u.size>0&&u.size<m.length,onChange:ve}),i.jsx(P,{variant:"body2",color:"text.secondary",children:"Select All"})]})]}),m.length===0?i.jsxs(X,{sx:{p:6,textAlign:"center"},children:[i.jsx(Ee,{sx:{fontSize:64,color:"text.secondary",mb:2}}),i.jsx(P,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No notifications found"}),i.jsx(P,{variant:"body2",color:"text.secondary",sx:{mb:3},children:s.search||s.type!=="all"||s.status!=="all"||s.dateRange!=="all"?"Try adjusting your filters to see more notifications.":"You're all caught up! New notifications will appear here."}),(s.search||s.type!=="all"||s.status!=="all"||s.dateRange!=="all")&&i.jsx(B,{variant:"outlined",onClick:ne,children:"Clear Filters"})]}):i.jsx(z,{spacing:2,children:m.map(o=>i.jsxs(_e,{sx:{position:"relative",borderLeft:o.is_read?"none":`4px solid ${a.palette.primary.main}`,backgroundColor:o.is_read?"transparent":F(a.palette.primary.main,.02),"&:hover":{backgroundColor:F(a.palette.action.hover,.04),transform:"translateY(-1px)",boxShadow:a.shadows[4]},transition:"all 0.2s ease-in-out"},children:[i.jsx(Ie,{sx:{pb:1},children:i.jsxs(v,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[i.jsx(se,{size:"small",checked:u.has(o.id),onChange:()=>Me(o.id),sx:{mt:.5}}),i.jsxs(v,{sx:{flex:1,minWidth:0},children:[i.jsxs(v,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[i.jsx(P,{variant:"h6",sx:{fontWeight:o.is_read?400:600,flex:1,fontSize:"1.1rem"},children:o.title}),i.jsx(Re,{label:pe(o.type),size:"small",sx:{backgroundColor:F(te(o.type),.1),color:te(o.type),fontWeight:500}}),!o.is_read&&i.jsx(v,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:a.palette.primary.main}})]}),i.jsx(Le,{text:o.message,variant:"body1",color:"text.primary",sx:{mb:2,lineHeight:1.6},showYouTubePreview:!0}),i.jsx(P,{variant:"caption",color:"text.secondary",children:Y(o.created_at)})]})]})}),i.jsx(qe,{sx:{pt:0,px:2,pb:2},children:i.jsxs(v,{sx:{ml:5},children:[" ",!o.is_read&&i.jsx(B,{size:"small",startIcon:i.jsx($,{}),onClick:()=>n(o.id),children:"Mark as Read"})]})})]},o.id))}),w>1&&i.jsx(v,{sx:{display:"flex",justifyContent:"center",mt:4},children:i.jsx(ze,{count:w,page:l,onChange:(o,d)=>h(d),color:"primary",size:"large",showFirstButton:!0,showLastButton:!0})}),t.some(o=>!o.is_read)&&i.jsx(Be,{color:"primary",sx:{position:"fixed",bottom:24,right:24,zIndex:a.zIndex.fab},onClick:r,children:i.jsx($,{})})]})};export{vn as default};
