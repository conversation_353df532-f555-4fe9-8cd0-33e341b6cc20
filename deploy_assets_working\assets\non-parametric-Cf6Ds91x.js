import{j as E}from"./other-utils-CR9xr_gI.js";const S=(i,f)=>{if(i.length<1||f.length<1)throw new Error("Each group must have at least 1 observation");const r=i.length,l=f.length,s=r+l,h=[...i.map(t=>({value:t,group:1})),...f.map(t=>({value:t,group:2}))].sort((t,p)=>t.value-p.value);let c=0;for(;c<s;){let t=c;for(;t<s-1&&h[t].value===h[t+1].value;)t++;const p=(c+1+t+1)/2;for(let b=c;b<=t;b++)h[b].rank=p;c=t+1}const m=h.filter(t=>t.group===1).reduce((t,p)=>t+(p.rank||0),0)-r*(r+1)/2,w=r*l-m,g=Math.min(m,w),k=r*l/2,v={};h.forEach(t=>{const p=String(t.value);v[p]=(v[p]||0)+1});let n=0;Object.values(v).forEach(t=>{t>1&&(n+=Math.pow(t,3)-t)});const a=r*l/(s*(s-1))*((Math.pow(s,3)-s)/12-n/12),o=Math.sqrt(a),u=o>0?(g-k)/o:0,e=2*(1-E.normal.cdf(Math.abs(u),0,1));return{U:g,z:u,pValue:e,n1:r,n2:l}},q=(i,f)=>{if(i.length!==f.length)throw new Error("Paired samples must have the same length");const l=i.map((e,t)=>e-f[t]).filter(e=>e!==0),s=l.length;if(s===0)return{W:NaN,z:NaN,pValue:NaN,n:0};s<6&&console.warn("Sample size (non-zero differences) < 6 for Wilcoxon Signed-Rank test, normal approximation may be inaccurate.");const h=l.map(e=>({diff:e,abs:Math.abs(e)})).sort((e,t)=>e.abs-t.abs);let c=0;for(;c<s;){let e=c;for(;e<s-1&&h[e].abs===h[e+1].abs;)e++;const t=(c+1+e+1)/2;for(let p=c;p<=e;p++)h[p].rank=t;c=e+1}let d=0,m=0;h.forEach(e=>{e.diff>0?d+=e.rank||0:m+=e.rank||0});const w=Math.min(d,m),g=s*(s+1)/4,k={};h.forEach(e=>{const t=String(e.abs);k[t]=(k[t]||0)+1});let v=0;Object.values(k).forEach(e=>{e>1&&(v+=Math.pow(e,3)-e)});const n=s*(s+1)*(2*s+1)/24-v/48,a=Math.sqrt(Math.max(0,n)),o=a>0?(w-g)/a:0,u=2*(1-E.normal.cdf(Math.abs(o),0,1));return{W:w,z:o,pValue:u,n:s}},N=i=>{const f=i.length;if(f<2)throw new Error("At least 2 groups required for Kruskal-Wallis test");if(i.some(n=>n.length===0))throw new Error("All groups must contain at least one observation");let r=[];i.forEach((n,a)=>{r=[...r,...n.map(o=>({value:o,group:a}))]});const l=r.length;r.sort((n,a)=>n.value-a.value);let s=0;for(;s<l;){let n=s;for(;n<l-1&&r[n].value===r[n+1].value;)n++;const a=(s+1+n+1)/2;for(let o=s;o<=n;o++)r[o].rank=a;s=n+1}const h=i.map((n,a)=>{const o=r.filter(e=>e.group===a),u=o.reduce((e,t)=>e+(t.rank||0),0);return{n:o.length,rankSum:u}});let c=0;h.forEach(n=>{n.n>0&&(c+=Math.pow(n.rankSum,2)/n.n)}),c=12/(l*(l+1))*c-3*(l+1);const d={};r.forEach(n=>{const a=String(n.value);d[a]=(d[a]||0)+1});let m=0;Object.values(d).forEach(n=>{n>1&&(m+=Math.pow(n,3)-n)});const w=m>0?1-m/(Math.pow(l,3)-l):1,g=w>0?c/w:c,k=f-1,v=1-E.chisquare.cdf(g,k);return{H:g,df:k,pValue:v,n:l}},x=i=>{var n;const f=i.length;if(f===0)throw new Error("Contingency table cannot be empty");const r=((n=i[0])==null?void 0:n.length)||0;if(r===0)throw new Error("Contingency table must have at least one column");if(i.some(a=>a.length!==r))throw new Error("All rows in the contingency table must have the same number of columns");if(i.flat().some(a=>a<0))throw new Error("Observed frequencies in the contingency table cannot be negative");const l=i.map(a=>a.reduce((o,u)=>o+u,0)),s=Array(r).fill(0).map((a,o)=>i.reduce((u,e)=>u+e[o],0)),h=l.reduce((a,o)=>a+o,0);if(h===0)return{chiSquare:0,df:(f-1)*(r-1),pValue:1,cramersV:NaN};let c=0,d=!1;const m=[];for(let a=0;a<f;a++)for(let o=0;o<r;o++){const u=l[a]*s[o]/h;if(u<5&&(d=!0,m.push({row:a,col:o,expected:u}),console.warn(`Expected frequency in cell (${a}, ${o}) is ${u.toFixed(2)}, which is less than 5. Chi-Square approximation may be inaccurate.`)),u>0){const e=i[a][o];c+=Math.pow(e-u,2)/u}}const w=(f-1)*(r-1),g=w>0?1-E.chisquare.cdf(c,w):NaN,k=Math.min(f,r),v=w>0?Math.sqrt(c/(h*(k-1))):NaN;return{chiSquare:c,df:w,pValue:g,cramersV:v,hasLowExpectedFrequencies:d,lowFrequencyCells:m.length>0?m:void 0}};export{x as c,N as k,S as m,q as w};
