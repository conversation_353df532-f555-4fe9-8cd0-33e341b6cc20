import{u as Z,j as e,B as z,e as m,R as J,G as _,ao as Q,ap as K,aq as X,ar as D,as as s,at as Y,ah as A,f as q,bG as ee,bH as te,Q as ie,bj as re,i as G,D as ne}from"./mui-libs-CfwFIaTD.js";import{r as I}from"./react-libs-Cr2nE3UY.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{S as M}from"./StatsCard-op8tGQ0a.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import"./other-utils-CR9xr_gI.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";class k{static incidenceRate(l,g){let u=l/g,j=1.96,r=Math.sqrt(u*(1-u)/g),d=u-j*r,i=u+j*r;return d=Math.max(0,d),i=Math.min(1,i),{risk:u,lower:d,upper:i}}static riskRatio(l,g,u,j,r=.05){let d=l/g,i=u/j,p=d/i,n=(1-d)/l+(1-i)/u,b=1.96,o=Math.sqrt(n),a=p*Math.exp(-1.96*o),R=p*Math.exp(b*o);return{RR:p,lower:a,upper:R}}static riskDifference(l,g,u,j,r=.05){let d=l/g,i=u/j,p=d-i,n=d*(1-d)/g+i*(1-i)/j,b=1.96,o=Math.sqrt(n),a=p-b*o,R=p+b*o;return{RD:p,lower:a,upper:R}}static directlyAdjustedRR(l,g=.05){let u=[],j=[];l.forEach(a=>{let R=a.ai/a.n1i/(a.ci/a.n0i);j.push(Math.log(R));let T=1/(a.bi/(a.ai*a.n1i)+a.di/(a.ci*a.n0i));u.push(T)});let r=u.reduce((a,R)=>a+R,0),d=u.map((a,R)=>a*j[R]).reduce((a,R)=>a+R,0)/r,i=Math.exp(d),p=1/Math.sqrt(r),n=1.96,b=Math.exp(d-n*p),o=Math.exp(d+n*p);return{RR_direct:i,lower:b,upper:o}}static directlyAdjustedRD(l,g=.05){let u=[],j=[];l.forEach(o=>{let a=o.ai/o.n1i-o.ci/o.n0i;j.push(a);let R=1/(o.ai*o.bi/Math.pow(o.n1i,3)+o.ci*o.di/Math.pow(o.n0i,3));u.push(R)});let r=u.reduce((o,a)=>o+a,0),d=u.map((o,a)=>o*j[a]).reduce((o,a)=>o+a,0)/r,i=1/Math.sqrt(r),p=1.96,n=d-p*i,b=d+p*i;return{RD_direct:d,lower:n,upper:b}}static mantelHaenszelRR(l,g=.05){let u=0,j=0;l.forEach(x=>{let y=x.n1i+x.n0i;u+=x.ai*x.n0i/y,j+=x.ci*x.n1i/y});let r=u/j,d=0;l.forEach(x=>{let y=x.n1i+x.n0i,C=x.ai+x.ci;d+=(C*x.n1i*x.n0i-x.ai*x.ci*y)/(y*y)});let i=0,p=0;l.forEach(x=>{let y=x.n1i+x.n0i;i+=x.ai*x.n0i/y,p+=x.ci*x.n1i/y});let n=i*p,b=d/n,o=1.96,a=Math.sqrt(b),R=r*Math.exp(-1.96*a),T=r*Math.exp(o*a);return{RR_MH:r,lower:R,upper:T}}}const ge=()=>{const P=Z(),[l,g]=I.useState({a:0,b:0,c:0,d:0}),u=l.a+l.b,j=l.c+l.d,[r,d]=I.useState([]),[i,p]=I.useState(-1),[n,b]=I.useState({});I.useEffect(()=>{const t=localStorage.getItem("cohort_calculator_cell_values"),c=localStorage.getItem("cohort_calculator_strata"),h=localStorage.getItem("cohort_calculator_results"),f=localStorage.getItem("cohort_calculator_current_stratum_index");t&&g(JSON.parse(t)),c&&d(JSON.parse(c)),h&&b(JSON.parse(h)),f&&p(JSON.parse(f))},[]),I.useEffect(()=>{localStorage.setItem("cohort_calculator_cell_values",JSON.stringify(l))},[l]),I.useEffect(()=>{localStorage.setItem("cohort_calculator_strata",JSON.stringify(r))},[r]),I.useEffect(()=>{localStorage.setItem("cohort_calculator_results",JSON.stringify(n))},[n]),I.useEffect(()=>{localStorage.setItem("cohort_calculator_current_stratum_index",JSON.stringify(i))},[i]);const o=(t,c)=>{const h=c===""?0:parseInt(c,10);g(f=>({...f,[t]:isNaN(h)?0:h}))},a=(t,c,h)=>{const f=h===""?0:parseInt(h,10);d(E=>E.map(S=>{if(S.id===t){const w={...S,[c]:isNaN(f)?0:f};return(c==="ai"||c==="bi")&&(w.n1i=w.ai+w.bi),(c==="ci"||c==="di")&&(w.n0i=w.ci+w.di),w}return S}))},R=()=>{const t={id:`stratum_${Date.now()}`,ai:0,bi:0,ci:0,di:0,n1i:0,n0i:0},c=[...r,t];d(c),p(c.length-1)},T=t=>{const c=r.findIndex(f=>f.id===t),h=r.filter(f=>f.id!==t);d(h),h.length===0?p(-1):c<=i&&p(Math.min(i-1,h.length-1))},x=()=>{try{const{a:t,b:c,c:h,d:f}=l,E=t+c,S=h+f;if(E+S===0)return;const v={};if(r.length>0){const U=[{ai:t,bi:c,ci:h,di:f,n1i:E,n0i:S},...r];let $=0,W=0,O=0,L=0;U.forEach(B=>{$+=B.ai,W+=B.bi,O+=B.ci,L+=B.di});const F=$+W,V=O+L;v.incidenceExposed=k.incidenceRate($,F),v.incidenceUnexposed=k.incidenceRate(O,V),v.riskRatio=k.riskRatio($,F,O,V),v.riskDifference=k.riskDifference($,F,O,V),v.directlyAdjustedRR=k.directlyAdjustedRR(U),v.directlyAdjustedRD=k.directlyAdjustedRD(U),v.mantelHaenszelRR=k.mantelHaenszelRR(U)}else v.incidenceExposed=k.incidenceRate(t,E),v.incidenceUnexposed=k.incidenceRate(h,S),v.riskRatio=k.riskRatio(t,E,h,S),v.riskDifference=k.riskDifference(t,E,h,S);b(v)}catch(t){console.error("Error calculating results:",t)}},y=()=>{g({a:0,b:0,c:0,d:0}),d([]),b({}),p(-1),localStorage.removeItem("cohort_calculator_cell_values"),localStorage.removeItem("cohort_calculator_strata"),localStorage.removeItem("cohort_calculator_results"),localStorage.removeItem("cohort_calculator_current_stratum_index")},C=(t,c=3)=>t.toFixed(c),H=(t,c,h=3)=>`${C(t,h)} - ${C(c,h)}`,N=(t,c=1)=>`${(t*100).toFixed(c)}%`;return e.jsxs(z,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Cohort Study Calculator"}),e.jsx(m,{variant:"body1",paragraph:!0,children:"Calculate incidence rates, risk ratios, risk differences, and other measures for cohort studies."}),e.jsxs(J,{elevation:1,sx:{p:3,mb:3},children:[e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"2×2 Contingency Table"}),e.jsx(m,{variant:"body2",paragraph:!0,children:"Enter the values for your 2×2 table to calculate epidemiological measures."}),e.jsxs(_,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(_,{item:!0,xs:12,md:8,children:e.jsx(Q,{component:J,variant:"outlined",children:e.jsxs(K,{"aria-label":"2x2 contingency table",children:[e.jsx(X,{children:e.jsxs(D,{children:[e.jsx(s,{}),e.jsx(s,{align:"center",children:"Cases"}),e.jsx(s,{align:"center",children:"Non-cases"}),e.jsx(s,{align:"center",children:"Total"})]})}),e.jsxs(Y,{children:[e.jsxs(D,{children:[e.jsx(s,{component:"th",scope:"row",children:"Exposed"}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:l.a||"",onChange:t=>o("a",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:l.b||"",onChange:t=>o("b",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:u})})]}),e.jsxs(D,{children:[e.jsx(s,{component:"th",scope:"row",children:"Unexposed"}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:l.c||"",onChange:t=>o("c",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:l.d||"",onChange:t=>o("d",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:j})})]}),e.jsxs(D,{children:[e.jsx(s,{component:"th",scope:"row",children:"Total"}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:l.a+l.c})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:l.b+l.d})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:u+j})})]})]})]})})}),e.jsxs(_,{item:!0,xs:12,md:4,sx:{display:"flex",flexDirection:"column",justifyContent:"center"},children:[e.jsxs(z,{sx:{display:"flex",gap:2,mb:2},children:[e.jsx(q,{variant:"contained",color:"primary",startIcon:e.jsx(ee,{}),onClick:x,disabled:Object.values(l).every(t=>t===0)&&r.length===0,children:"Calculate"}),e.jsx(q,{variant:"outlined",startIcon:e.jsx(te,{}),onClick:y,children:"Reset"})]}),e.jsx(m,{variant:"body2",color:"text.secondary",children:"Enter the values in the 2×2 table and click Calculate to compute epidemiological measures."})]})]}),e.jsxs(z,{sx:{mb:3,border:"1px solid rgba(25, 118, 210, 0.5)",borderRadius:"4px",p:2},children:[e.jsx(m,{variant:"subtitle1",sx:{mb:2},children:e.jsx("strong",{children:"Stratified Analysis"})}),e.jsx(m,{variant:"body2",paragraph:!0,children:"Add strata to calculate adjusted risk ratios and risk differences. The crude measures will be calculated by combining all tables."}),e.jsxs(z,{sx:{display:"flex",gap:2,mb:2,alignItems:"center"},children:[e.jsx(q,{variant:"outlined",startIcon:e.jsx(ie,{}),onClick:R,children:"Add Stratum"}),r.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(z,{sx:{minWidth:120},children:e.jsx("select",{value:i,onChange:t=>p(Number(t.target.value)),style:{padding:"8px 12px",borderRadius:"4px",border:"1px solid rgba(0, 0, 0, 0.23)",backgroundColor:"transparent",minWidth:"120px"},children:r.map((t,c)=>e.jsxs("option",{value:c,children:["Stratum ",c+1]},c))})}),e.jsx(q,{variant:"outlined",color:"error",startIcon:e.jsx(re,{}),onClick:()=>T(r[i].id),disabled:i===-1,children:"Delete Stratum"})]})]}),i!==-1&&r[i]&&e.jsxs(z,{sx:{mb:2,p:2,border:`1px solid ${G(P.palette.primary.main,.2)}`,borderRadius:1},children:[e.jsxs(m,{variant:"subtitle2",sx:{mb:1},children:["Stratum ",i+1]}),e.jsx(Q,{component:J,variant:"outlined",children:e.jsxs(K,{size:"small",children:[e.jsx(X,{children:e.jsxs(D,{children:[e.jsx(s,{}),e.jsx(s,{align:"center",children:"Cases"}),e.jsx(s,{align:"center",children:"Non-cases"}),e.jsx(s,{align:"center",children:"Total"})]})}),e.jsxs(Y,{children:[e.jsxs(D,{children:[e.jsx(s,{children:"Exposed"}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:r[i].ai||"",onChange:t=>a(r[i].id,"ai",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:r[i].bi||"",onChange:t=>a(r[i].id,"bi",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:r[i].n1i})})]}),e.jsxs(D,{children:[e.jsx(s,{children:"Unexposed"}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:r[i].ci||"",onChange:t=>a(r[i].id,"ci",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(s,{align:"center",children:e.jsx(A,{type:"number",value:r[i].di||"",onChange:t=>a(r[i].id,"di",t.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"70px"}})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:r[i].n0i})})]}),e.jsxs(D,{children:[e.jsx(s,{component:"th",scope:"row",children:"Total"}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:r[i].ai+r[i].ci})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:r[i].bi+r[i].di})}),e.jsx(s,{align:"center",children:e.jsx(m,{variant:"body2",children:r[i].n1i+r[i].n0i})})]})]})]})})]})]}),Object.keys(n).length>0&&e.jsxs(z,{sx:{mt:4},children:[e.jsx(ne,{sx:{mb:3}}),e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Results"}),e.jsxs(_,{container:!0,spacing:3,children:[n.incidenceExposed&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Incidence Rate (Exposed)",value:N(n.incidenceExposed.risk),description:`95% CI: ${N(n.incidenceExposed.lower)} - ${N(n.incidenceExposed.upper)}`,color:"primary",variant:"outlined",tooltip:"Incidence proportion (risk) in the exposed group"})}),n.incidenceUnexposed&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Incidence Rate (Unexposed)",value:N(n.incidenceUnexposed.risk),description:`95% CI: ${N(n.incidenceUnexposed.lower)} - ${N(n.incidenceUnexposed.upper)}`,color:"primary",variant:"outlined",tooltip:"Incidence proportion (risk) in the unexposed group"})}),n.riskRatio&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Risk Ratio (Relative Risk)",value:C(n.riskRatio.RR),description:`95% CI: ${H(n.riskRatio.lower,n.riskRatio.upper)}`,color:"secondary",variant:"outlined",tooltip:"Ratio of risk in exposed vs. unexposed groups"})}),n.riskDifference&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Risk Difference",value:C(n.riskDifference.RD),description:`95% CI: ${H(n.riskDifference.lower,n.riskDifference.upper)}`,color:"info",variant:"outlined",tooltip:"Absolute difference in risk between exposed and unexposed groups"})}),n.directlyAdjustedRR&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Directly Adjusted Risk Ratio",value:C(n.directlyAdjustedRR.RR_direct),description:`95% CI: ${H(n.directlyAdjustedRR.lower,n.directlyAdjustedRR.upper)}`,color:"warning",variant:"outlined",tooltip:"Directly adjusted risk ratio for stratified data"})}),n.directlyAdjustedRD&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Directly Adjusted Risk Difference",value:C(n.directlyAdjustedRD.RD_direct),description:`95% CI: ${H(n.directlyAdjustedRD.lower,n.directlyAdjustedRD.upper)}`,color:"warning",variant:"outlined",tooltip:"Directly adjusted risk difference for stratified data"})}),n.mantelHaenszelRR&&e.jsx(_,{item:!0,xs:12,sm:6,md:4,children:e.jsx(M,{title:"Mantel-Haenszel Risk Ratio",value:C(n.mantelHaenszelRR.RR_MH),description:`95% CI: ${H(n.mantelHaenszelRR.lower,n.mantelHaenszelRR.upper)}`,color:"success",variant:"outlined",tooltip:"Mantel-Haenszel adjusted risk ratio across strata"})})]}),e.jsxs(J,{variant:"outlined",sx:{mt:3,p:2,backgroundColor:G(P.palette.info.main,.05),borderColor:G(P.palette.info.main,.2)},children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Interpretation Guidelines:"}),e.jsxs(m,{variant:"body2",children:["• ",e.jsx("strong",{children:"Risk Ratio = 1:"})," No association between exposure and outcome",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Risk Ratio > 1:"})," Positive association (exposure may increase risk of outcome)",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Risk Ratio < 1:"})," Negative association (exposure may decrease risk of outcome)",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Risk Difference = 0:"})," No absolute difference in risk between groups",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Statistical significance:"})," If the 95% confidence interval does not include the null value (1 for RR, 0 for RD)"]})]})]})]})]})};export{ge as default};
