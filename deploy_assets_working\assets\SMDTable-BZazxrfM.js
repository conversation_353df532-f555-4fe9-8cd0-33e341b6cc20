import{j as e,B as y,e as L,R as E,g as J,G as w,ai as G,b9 as B,ba as z,bb as x,f as de,ae as ce,ao as ue,ap as be,aq as he,ar as K,as as b,at as ge,bR as pe}from"./mui-libs-CfwFIaTD.js";import{r as h}from"./react-libs-Cr2nE3UY.js";import{a as me,D as U}from"./index-Bpan7Tbe.js";import{A as xe}from"./AddToResultsButton-BwSXKCt2.js";import{P as fe}from"./PublicationReadyGate-BGFbKbJc.js";import{b as Q,c as X}from"./descriptive-Djo0s6H4.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const je=(r,o,g,v,f,l)=>{const p=Math.sqrt(((g-1)*Math.pow(o,2)+(l-1)*Math.pow(f,2))/(g+l-2));return(r-v)/p},ve=(r,o,g,v=.95)=>{const f=o+g,l=f/(o*g)+Math.pow(r,2)/(2*(f-2)),p=Math.sqrt(l),c=1.96,S=r-c*p,C=r+c*p;return{lower:S,upper:C}},W=r=>{if(r===void 0||isNaN(r))return"N/A";const o=Math.abs(r);return o>=.8?"Large":o>=.5?"Medium":o>=.2?"Small":"Trivial"},ke=()=>{var $;const{datasets:r,currentDataset:o,setCurrentDataset:g}=me(),[v,f]=h.useState((o==null?void 0:o.id)||""),[l,p]=h.useState(""),[c,S]=h.useState([]),[C,T]=h.useState(!1),[O,j]=h.useState(null),[R,N]=h.useState(null),[Y,V]=h.useState(!1),[Z,P]=h.useState(""),s=r.find(a=>a.id===v),m=(s==null?void 0:s.columns)||[],D=m.filter(a=>a.type===U.CATEGORICAL&&(s==null?void 0:s.data.map(t=>String(t[a.name])).filter((t,d,u)=>u.indexOf(t)===d).length)===2),k=m.filter(a=>a.type===U.NUMERIC),_=a=>{const t=a.target.value;f(t),p(""),S([]),N(null),j(null);const d=r.find(u=>u.id===t);d&&g(d)},ee=a=>{const t=a.target.value;p(t),S([]),N(null),j(null)},ae=a=>{const t=a.target.value;S(typeof t=="string"?t.split(","):t),N(null),j(null)},te=()=>{if(!s||!l||c.length===0){j("Please select a dataset, a binary grouping variable, and at least one continuous variable to analyze."),N(null);return}T(!0),j(null);const a=[],t=m.find(i=>i.id===l),d=m.filter(i=>c.includes(i.id));if(!t||t.type!==U.CATEGORICAL||D.findIndex(i=>i.id===l)===-1){j("Selected grouping variable is not a binary categorical variable."),T(!1);return}const u=Array.from(new Set(s.data.map(i=>String(i[t.name])))),se=u[0],re=u[1];d.forEach(i=>{s.data.map(n=>n[i.name]);const M=s.data.filter(n=>String(n[t.name])===se).map(n=>n[i.name]).filter(n=>typeof n=="number"&&!isNaN(n)),I=s.data.filter(n=>String(n[t.name])===re).map(n=>n[i.name]).filter(n=>typeof n=="number"&&!isNaN(n));if(M.length>1&&I.length>1)try{const n=Q(M),ie=X(M),F=M.length,le=Q(I),oe=X(I),q=I.length,A=je(n,ie,F,le,oe,q),H=ve(A,F,q);a.push({variableName:i.name,smd:A,ciLower:H.lower,ciUpper:H.upper,remarks:W(A)})}catch(n){console.error(`Error calculating SMD for ${i.name}:`,n),a.push({variableName:i.name,smd:NaN,ciLower:NaN,ciUpper:NaN,remarks:W(NaN)})}else a.push({variableName:i.name,smd:NaN,ciLower:NaN,ciUpper:NaN,remarks:W(NaN)})}),N(a),T(!1)},ne=()=>{V(!1)};return e.jsx(fe,{children:e.jsxs(y,{p:3,children:[e.jsx(L,{variant:"h5",gutterBottom:!0,children:"Publication Ready: Standardized Mean Differences (SMD) Table"}),e.jsxs(E,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(L,{variant:"subtitle1",gutterBottom:!0,children:"Select Data and Variables"}),e.jsx(J,{severity:"info",sx:{mb:2},children:"This table calculates Standardized Mean Differences (e.g., Cohen's d) for continuous variables, comparing two groups defined by a binary categorical variable."}),e.jsxs(w,{container:!0,spacing:2,children:[e.jsx(w,{item:!0,xs:12,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(B,{id:"dataset-select-label",children:"Dataset"}),e.jsx(z,{labelId:"dataset-select-label",id:"dataset-select",value:v,label:"Dataset",onChange:_,disabled:r.length===0,children:r.length===0?e.jsx(x,{value:"",disabled:!0,children:"No datasets available"}):r.map(a=>e.jsxs(x,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]})}),e.jsx(w,{item:!0,xs:12,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(B,{id:"grouping-variable-select-label",children:"Grouping Variable (Binary Categorical)"}),e.jsxs(z,{labelId:"grouping-variable-select-label",id:"grouping-variable-select",value:l,onChange:ee,label:"Grouping Variable (Binary Categorical)",disabled:D.length===0||!s,children:[e.jsx(x,{value:"",children:e.jsx("em",{children:"None"})}),D.length===0?e.jsx(x,{value:"",disabled:!0,children:"No binary categorical variables available"}):D.map(a=>e.jsx(x,{value:a.id,children:a.name},a.id))]})]})}),e.jsx(w,{item:!0,xs:12,children:e.jsxs(G,{fullWidth:!0,margin:"normal",children:[e.jsx(B,{id:"continuous-variables-select-label",children:"Continuous Variables"}),e.jsx(z,{labelId:"continuous-variables-select-label",id:"continuous-variables-select",multiple:!0,value:c,onChange:ae,label:"Continuous Variables",disabled:k.length===0||!l,renderValue:a=>a.map(t=>{var d;return((d=m.find(u=>u.id===t))==null?void 0:d.name)||""}).join(", "),children:k.length===0?e.jsx(x,{value:"",disabled:!0,children:"No continuous variables available"}):k.map(a=>e.jsx(x,{value:a.id,children:a.name},a.id))})]})})]}),e.jsx(y,{mt:2,children:e.jsx(de,{variant:"contained",color:"primary",onClick:te,disabled:C||!s||!l||c.length===0,children:"Generate SMD Table"})})]}),C&&e.jsx(y,{display:"flex",justifyContent:"center",my:4,children:e.jsx(ce,{})}),O&&e.jsx(J,{severity:"error",sx:{mb:3},children:O}),R&&!C&&s&&l&&c.length>0&&e.jsxs(y,{children:[e.jsxs(E,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(L,{variant:"h6",gutterBottom:!0,children:"Standardized Mean Differences (SMD)"}),e.jsx(ue,{component:E,variant:"outlined",children:e.jsxs(be,{size:"small",children:[e.jsx(he,{children:e.jsxs(K,{children:[e.jsx(b,{sx:{fontWeight:"bold"},children:"Variable"}),e.jsx(b,{sx:{fontWeight:"bold"},align:"center",children:"SMD (Cohen's d)"}),e.jsx(b,{sx:{fontWeight:"bold"},align:"center",children:"95% CI"}),e.jsx(b,{sx:{fontWeight:"bold"},align:"center",children:"Remarks/Interpretation"})," "]})}),e.jsx(ge,{children:R.map((a,t)=>e.jsxs(K,{children:[e.jsx(b,{sx:{fontWeight:"bold"},children:a.variableName}),e.jsx(b,{align:"center",children:a.smd!==void 0&&!isNaN(a.smd)?a.smd.toFixed(3):"N/A"}),e.jsx(b,{align:"center",children:a.ciLower!==void 0&&!isNaN(a.ciLower)&&a.ciUpper!==void 0&&!isNaN(a.ciUpper)?`[${a.ciLower.toFixed(3)}, ${a.ciUpper.toFixed(3)}]`:"N/A"}),e.jsx(b,{align:"center",children:a.remarks||"N/A"})," "]},t))})]})})]}),e.jsx(y,{sx:{display:"flex",justifyContent:"center",mt:2},children:e.jsx(xe,{resultData:{title:`SMD Table - Standardized Mean Differences (${s.name})`,type:"other",component:"SMDTable",data:{dataset:s.name,groupingVariable:(($=m.find(a=>a.id===l))==null?void 0:$.name)||"Unknown",variables:c.map(a=>{var t;return((t=m.find(d=>d.id===a))==null?void 0:t.name)||a}),results:R,timestamp:new Date().toISOString(),totalSampleSize:s.data.length}},onSuccess:()=>{P("Results successfully added to Results Manager!"),V(!0)},onError:a=>{P(`Error adding results to Results Manager: ${a}`),V(!0)}})})]}),e.jsx(pe,{open:Y,autoHideDuration:4e3,onClose:ne,message:Z})]})})};export{ke as default};
