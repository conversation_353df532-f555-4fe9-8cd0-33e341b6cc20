import{c as J,P as ut}from"./mui-libs-CfwFIaTD.js";import{e as Da,g as ft,r as W,b as A}from"./react-libs-Cr2nE3UY.js";var _x=Array.isArray,Xt=_x,wx=typeof Da=="object"&&Da&&Da.Object===Object&&Da,Ty=wx,Ox=Ty,Ax=typeof self=="object"&&self&&self.Object===Object&&self,Px=Ox||Ax||Function("return this")(),Se=Px,Sx=Se,$x=Sx.Symbol,_a=$x,lh=_a,jy=Object.prototype,Ex=jy.hasOwnProperty,Tx=jy.toString,fi=lh?lh.toStringTag:void 0;function jx(t){var e=Ex.call(t,fi),r=t[fi];try{t[fi]=void 0;var n=!0}catch{}var i=Tx.call(t);return n&&(e?t[fi]=r:delete t[fi]),i}var Mx=jx,Cx=Object.prototype,kx=Cx.toString;function Ix(t){return kx.call(t)}var Nx=Ix,fh=_a,Dx=Mx,Rx=Nx,Lx="[object Null]",Bx="[object Undefined]",hh=fh?fh.toStringTag:void 0;function zx(t){return t==null?t===void 0?Bx:Lx:hh&&hh in Object(t)?Dx(t):Rx(t)}var Xe=zx;function Fx(t){return t!=null&&typeof t=="object"}var Ye=Fx,Wx=Xe,Ux=Ye,Kx="[object Symbol]";function qx(t){return typeof t=="symbol"||Ux(t)&&Wx(t)==Kx}var Yn=qx,Hx=Xt,Gx=Yn,Vx=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Xx=/^\w*$/;function Yx(t,e){if(Hx(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Gx(t)?!0:Xx.test(t)||!Vx.test(t)||e!=null&&t in Object(e)}var Ul=Yx;function Zx(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}var fr=Zx;const Zn=ft(fr);var Jx=Xe,Qx=fr,t_="[object AsyncFunction]",e_="[object Function]",r_="[object GeneratorFunction]",n_="[object Proxy]";function i_(t){if(!Qx(t))return!1;var e=Jx(t);return e==e_||e==r_||e==t_||e==n_}var Kl=i_;const V=ft(Kl);var a_=Se,o_=a_["__core-js_shared__"],u_=o_,ms=u_,ph=function(){var t=/[^.]+$/.exec(ms&&ms.keys&&ms.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function s_(t){return!!ph&&ph in t}var c_=s_,l_=Function.prototype,f_=l_.toString;function h_(t){if(t!=null){try{return f_.call(t)}catch{}try{return t+""}catch{}}return""}var My=h_,p_=Kl,d_=c_,v_=fr,y_=My,m_=/[\\^$.*+?()[\]{}|]/g,g_=/^\[object .+?Constructor\]$/,b_=Function.prototype,x_=Object.prototype,__=b_.toString,w_=x_.hasOwnProperty,O_=RegExp("^"+__.call(w_).replace(m_,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function A_(t){if(!v_(t)||d_(t))return!1;var e=p_(t)?O_:g_;return e.test(y_(t))}var P_=A_;function S_(t,e){return t==null?void 0:t[e]}var $_=S_,E_=P_,T_=$_;function j_(t,e){var r=T_(t,e);return E_(r)?r:void 0}var Hr=j_,M_=Hr,C_=M_(Object,"create"),mu=C_,dh=mu;function k_(){this.__data__=dh?dh(null):{},this.size=0}var I_=k_;function N_(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var D_=N_,R_=mu,L_="__lodash_hash_undefined__",B_=Object.prototype,z_=B_.hasOwnProperty;function F_(t){var e=this.__data__;if(R_){var r=e[t];return r===L_?void 0:r}return z_.call(e,t)?e[t]:void 0}var W_=F_,U_=mu,K_=Object.prototype,q_=K_.hasOwnProperty;function H_(t){var e=this.__data__;return U_?e[t]!==void 0:q_.call(e,t)}var G_=H_,V_=mu,X_="__lodash_hash_undefined__";function Y_(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=V_&&e===void 0?X_:e,this}var Z_=Y_,J_=I_,Q_=D_,tw=W_,ew=G_,rw=Z_;function Jn(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Jn.prototype.clear=J_;Jn.prototype.delete=Q_;Jn.prototype.get=tw;Jn.prototype.has=ew;Jn.prototype.set=rw;var nw=Jn;function iw(){this.__data__=[],this.size=0}var aw=iw;function ow(t,e){return t===e||t!==t&&e!==e}var ql=ow,uw=ql;function sw(t,e){for(var r=t.length;r--;)if(uw(t[r][0],e))return r;return-1}var gu=sw,cw=gu,lw=Array.prototype,fw=lw.splice;function hw(t){var e=this.__data__,r=cw(e,t);if(r<0)return!1;var n=e.length-1;return r==n?e.pop():fw.call(e,r,1),--this.size,!0}var pw=hw,dw=gu;function vw(t){var e=this.__data__,r=dw(e,t);return r<0?void 0:e[r][1]}var yw=vw,mw=gu;function gw(t){return mw(this.__data__,t)>-1}var bw=gw,xw=gu;function _w(t,e){var r=this.__data__,n=xw(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this}var ww=_w,Ow=aw,Aw=pw,Pw=yw,Sw=bw,$w=ww;function Qn(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Qn.prototype.clear=Ow;Qn.prototype.delete=Aw;Qn.prototype.get=Pw;Qn.prototype.has=Sw;Qn.prototype.set=$w;var bu=Qn,Ew=Hr,Tw=Se,jw=Ew(Tw,"Map"),Hl=jw,vh=nw,Mw=bu,Cw=Hl;function kw(){this.size=0,this.__data__={hash:new vh,map:new(Cw||Mw),string:new vh}}var Iw=kw;function Nw(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}var Dw=Nw,Rw=Dw;function Lw(t,e){var r=t.__data__;return Rw(e)?r[typeof e=="string"?"string":"hash"]:r.map}var xu=Lw,Bw=xu;function zw(t){var e=Bw(this,t).delete(t);return this.size-=e?1:0,e}var Fw=zw,Ww=xu;function Uw(t){return Ww(this,t).get(t)}var Kw=Uw,qw=xu;function Hw(t){return qw(this,t).has(t)}var Gw=Hw,Vw=xu;function Xw(t,e){var r=Vw(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this}var Yw=Xw,Zw=Iw,Jw=Fw,Qw=Kw,t1=Gw,e1=Yw;function ti(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}ti.prototype.clear=Zw;ti.prototype.delete=Jw;ti.prototype.get=Qw;ti.prototype.has=t1;ti.prototype.set=e1;var Gl=ti,Cy=Gl,r1="Expected a function";function Vl(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(r1);var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=t.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(Vl.Cache||Cy),r}Vl.Cache=Cy;var ky=Vl;const n1=ft(ky);var i1=ky,a1=500;function o1(t){var e=i1(t,function(n){return r.size===a1&&r.clear(),n}),r=e.cache;return e}var u1=o1,s1=u1,c1=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,l1=/\\(\\)?/g,f1=s1(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(c1,function(r,n,i,a){e.push(i?a.replace(l1,"$1"):n||r)}),e}),h1=f1;function p1(t,e){for(var r=-1,n=t==null?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}var Xl=p1,yh=_a,d1=Xl,v1=Xt,y1=Yn,mh=yh?yh.prototype:void 0,gh=mh?mh.toString:void 0;function Iy(t){if(typeof t=="string")return t;if(v1(t))return d1(t,Iy)+"";if(y1(t))return gh?gh.call(t):"";var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}var m1=Iy,g1=m1;function b1(t){return t==null?"":g1(t)}var Ny=b1,x1=Xt,_1=Ul,w1=h1,O1=Ny;function A1(t,e){return x1(t)?t:_1(t,e)?[t]:w1(O1(t))}var Dy=A1,P1=Yn;function S1(t){if(typeof t=="string"||P1(t))return t;var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}var _u=S1,$1=Dy,E1=_u;function T1(t,e){e=$1(e,t);for(var r=0,n=e.length;t!=null&&r<n;)t=t[E1(e[r++])];return r&&r==n?t:void 0}var Yl=T1,j1=Yl;function M1(t,e,r){var n=t==null?void 0:j1(t,e);return n===void 0?r:n}var Ry=M1;const te=ft(Ry);function C1(t){return t==null}var k1=C1;const X=ft(k1);var I1=Xe,N1=Xt,D1=Ye,R1="[object String]";function L1(t){return typeof t=="string"||!N1(t)&&D1(t)&&I1(t)==R1}var B1=L1;const Lr=ft(B1);var Ly={exports:{}},st={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zl=Symbol.for("react.element"),Jl=Symbol.for("react.portal"),wu=Symbol.for("react.fragment"),Ou=Symbol.for("react.strict_mode"),Au=Symbol.for("react.profiler"),Pu=Symbol.for("react.provider"),Su=Symbol.for("react.context"),z1=Symbol.for("react.server_context"),$u=Symbol.for("react.forward_ref"),Eu=Symbol.for("react.suspense"),Tu=Symbol.for("react.suspense_list"),ju=Symbol.for("react.memo"),Mu=Symbol.for("react.lazy"),F1=Symbol.for("react.offscreen"),By;By=Symbol.for("react.module.reference");function fe(t){if(typeof t=="object"&&t!==null){var e=t.$$typeof;switch(e){case Zl:switch(t=t.type,t){case wu:case Au:case Ou:case Eu:case Tu:return t;default:switch(t=t&&t.$$typeof,t){case z1:case Su:case $u:case Mu:case ju:case Pu:return t;default:return e}}case Jl:return e}}}st.ContextConsumer=Su;st.ContextProvider=Pu;st.Element=Zl;st.ForwardRef=$u;st.Fragment=wu;st.Lazy=Mu;st.Memo=ju;st.Portal=Jl;st.Profiler=Au;st.StrictMode=Ou;st.Suspense=Eu;st.SuspenseList=Tu;st.isAsyncMode=function(){return!1};st.isConcurrentMode=function(){return!1};st.isContextConsumer=function(t){return fe(t)===Su};st.isContextProvider=function(t){return fe(t)===Pu};st.isElement=function(t){return typeof t=="object"&&t!==null&&t.$$typeof===Zl};st.isForwardRef=function(t){return fe(t)===$u};st.isFragment=function(t){return fe(t)===wu};st.isLazy=function(t){return fe(t)===Mu};st.isMemo=function(t){return fe(t)===ju};st.isPortal=function(t){return fe(t)===Jl};st.isProfiler=function(t){return fe(t)===Au};st.isStrictMode=function(t){return fe(t)===Ou};st.isSuspense=function(t){return fe(t)===Eu};st.isSuspenseList=function(t){return fe(t)===Tu};st.isValidElementType=function(t){return typeof t=="string"||typeof t=="function"||t===wu||t===Au||t===Ou||t===Eu||t===Tu||t===F1||typeof t=="object"&&t!==null&&(t.$$typeof===Mu||t.$$typeof===ju||t.$$typeof===Pu||t.$$typeof===Su||t.$$typeof===$u||t.$$typeof===By||t.getModuleId!==void 0)};st.typeOf=fe;Ly.exports=st;var W1=Ly.exports,U1=Xe,K1=Ye,q1="[object Number]";function H1(t){return typeof t=="number"||K1(t)&&U1(t)==q1}var zy=H1;const G1=ft(zy);var V1=zy;function X1(t){return V1(t)&&t!=+t}var Y1=X1;const ei=ft(Y1);var Ut=function(e){return e===0?0:e>0?1:-1},jr=function(e){return Lr(e)&&e.indexOf("%")===e.length-1},F=function(e){return G1(e)&&!ei(e)},Et=function(e){return F(e)||Lr(e)},Z1=0,hr=function(e){var r=++Z1;return"".concat(e||"").concat(r)},Kt=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!F(e)&&!Lr(e))return n;var a;if(jr(e)){var o=e.indexOf("%");a=r*parseFloat(e.slice(0,o))/100}else a=+e;return ei(a)&&(a=n),i&&a>r&&(a=r),a},er=function(e){if(!e)return null;var r=Object.keys(e);return r&&r.length?e[r[0]]:null},J1=function(e){if(!Array.isArray(e))return!1;for(var r=e.length,n={},i=0;i<r;i++)if(!n[e[i]])n[e[i]]=!0;else return!0;return!1},pt=function(e,r){return F(e)&&F(r)?function(n){return e+n*(r-e)}:function(){return r}};function uo(t,e,r){return!t||!t.length?null:t.find(function(n){return n&&(typeof e=="function"?e(n):te(n,e))===r})}var Q1=function(e){if(!e||!e.length)return null;for(var r=e.length,n=0,i=0,a=0,o=0,u=1/0,s=-1/0,c=0,l=0,f=0;f<r;f++)c=e[f].cx||0,l=e[f].cy||0,n+=c,i+=l,a+=c*l,o+=c*c,u=Math.min(u,c),s=Math.max(s,c);var h=r*o!==n*n?(r*a-n*i)/(r*o-n*n):0;return{xmin:u,xmax:s,a:h,b:(i-h*n)/r}},tO=function(e,r){return F(e)&&F(r)?e-r:Lr(e)&&Lr(r)?e.localeCompare(r):e instanceof Date&&r instanceof Date?e.getTime()-r.getTime():String(e).localeCompare(String(r))};function hn(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function tc(t){"@babel/helpers - typeof";return tc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},tc(t)}var eO=["viewBox","children"],rO=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],bh=["points","pathLength"],gs={svg:eO,polygon:bh,polyline:bh},Ql=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],so=function(e,r){if(!e||typeof e=="function"||typeof e=="boolean")return null;var n=e;if(W.isValidElement(e)&&(n=e.props),!Zn(n))return null;var i={};return Object.keys(n).forEach(function(a){Ql.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},nO=function(e,r,n){return function(i){return e(r,n,i),null}},sr=function(e,r,n){if(!Zn(e)||tc(e)!=="object")return null;var i=null;return Object.keys(e).forEach(function(a){var o=e[a];Ql.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=nO(o,r,n))}),i},iO=["children"],aO=["children"];function xh(t,e){if(t==null)return{};var r=oO(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function oO(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function ec(t){"@babel/helpers - typeof";return ec=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ec(t)}var _h={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Fe=function(e){return typeof e=="string"?e:e?e.displayName||e.name||"Component":""},wh=null,bs=null,tf=function t(e){if(e===wh&&Array.isArray(bs))return bs;var r=[];return W.Children.forEach(e,function(n){X(n)||(W1.isFragment(n)?r=r.concat(t(n.props.children)):r.push(n))}),bs=r,wh=e,r};function Ht(t,e){var r=[],n=[];return Array.isArray(e)?n=e.map(function(i){return Fe(i)}):n=[Fe(e)],tf(t).forEach(function(i){var a=te(i,"type.displayName")||te(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Jt(t,e){var r=Ht(t,e);return r&&r[0]}var Oh=function(e){if(!e||!e.props)return!1;var r=e.props,n=r.width,i=r.height;return!(!F(n)||n<=0||!F(i)||i<=0)},uO=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],sO=function(e){return e&&e.type&&Lr(e.type)&&uO.indexOf(e.type)>=0},Fy=function(e){return e&&ec(e)==="object"&&"clipDot"in e},cO=function(e,r,n,i){var a,o=(a=gs==null?void 0:gs[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!V(e)&&(i&&o.includes(r)||rO.includes(r))||n&&Ql.includes(r)},q=function(e,r,n){if(!e||typeof e=="function"||typeof e=="boolean")return null;var i=e;if(W.isValidElement(e)&&(i=e.props),!Zn(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;cO((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},rc=function t(e,r){if(e===r)return!0;var n=W.Children.count(e);if(n!==W.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Ah(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!t(a,o))return!1}else if(!Ah(a,o))return!1}return!0},Ah=function(e,r){if(X(e)&&X(r))return!0;if(!X(e)&&!X(r)){var n=e.props||{},i=n.children,a=xh(n,iO),o=r.props||{},u=o.children,s=xh(o,aO);return i&&u?hn(a,s)&&rc(i,u):!i&&!u?hn(a,s):!1}return!1},Ph=function(e,r){var n=[],i={};return tf(e).forEach(function(a,o){if(sO(a))n.push(a);else if(a){var u=Fe(a.type),s=r[u]||{},c=s.handler,l=s.once;if(c&&(!l||!i[u])){var f=c(a,u,o);n.push(f),i[u]=!0}}}),n},lO=function(e){var r=e&&e.type;return r&&_h[r]?_h[r]:null},fO=function(e,r){return tf(r).indexOf(e)},hO=["children","width","height","viewBox","className","style","title","desc"];function nc(){return nc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},nc.apply(this,arguments)}function pO(t,e){if(t==null)return{};var r=dO(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function dO(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function ic(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,a=t.className,o=t.style,u=t.title,s=t.desc,c=pO(t,hO),l=i||{width:r,height:n,x:0,y:0},f=J("recharts-surface",a);return A.createElement("svg",nc({},q(c,!0,"svg"),{className:f,width:r,height:n,style:o,viewBox:"".concat(l.x," ").concat(l.y," ").concat(l.width," ").concat(l.height)}),A.createElement("title",null,u),A.createElement("desc",null,s),e)}var vO=["children","className"];function ac(){return ac=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ac.apply(this,arguments)}function yO(t,e){if(t==null)return{};var r=mO(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function mO(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var Q=A.forwardRef(function(t,e){var r=t.children,n=t.className,i=yO(t,vO),a=J("recharts-layer",n);return A.createElement("g",ac({className:a},q(i,!0),{ref:e}),r)}),ge=function(e,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function gO(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),r=r>i?i:r,r<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var a=Array(i);++n<i;)a[n]=t[n+e];return a}var bO=gO,xO=bO;function _O(t,e,r){var n=t.length;return r=r===void 0?n:r,!e&&r>=n?t:xO(t,e,r)}var wO=_O,OO="\\ud800-\\udfff",AO="\\u0300-\\u036f",PO="\\ufe20-\\ufe2f",SO="\\u20d0-\\u20ff",$O=AO+PO+SO,EO="\\ufe0e\\ufe0f",TO="\\u200d",jO=RegExp("["+TO+OO+$O+EO+"]");function MO(t){return jO.test(t)}var Wy=MO;function CO(t){return t.split("")}var kO=CO,Uy="\\ud800-\\udfff",IO="\\u0300-\\u036f",NO="\\ufe20-\\ufe2f",DO="\\u20d0-\\u20ff",RO=IO+NO+DO,LO="\\ufe0e\\ufe0f",BO="["+Uy+"]",oc="["+RO+"]",uc="\\ud83c[\\udffb-\\udfff]",zO="(?:"+oc+"|"+uc+")",Ky="[^"+Uy+"]",qy="(?:\\ud83c[\\udde6-\\uddff]){2}",Hy="[\\ud800-\\udbff][\\udc00-\\udfff]",FO="\\u200d",Gy=zO+"?",Vy="["+LO+"]?",WO="(?:"+FO+"(?:"+[Ky,qy,Hy].join("|")+")"+Vy+Gy+")*",UO=Vy+Gy+WO,KO="(?:"+[Ky+oc+"?",oc,qy,Hy,BO].join("|")+")",qO=RegExp(uc+"(?="+uc+")|"+KO+UO,"g");function HO(t){return t.match(qO)||[]}var GO=HO,VO=kO,XO=Wy,YO=GO;function ZO(t){return XO(t)?YO(t):VO(t)}var JO=ZO,QO=wO,tA=Wy,eA=JO,rA=Ny;function nA(t){return function(e){e=rA(e);var r=tA(e)?eA(e):void 0,n=r?r[0]:e.charAt(0),i=r?QO(r,1).join(""):e.slice(1);return n[t]()+i}}var iA=nA,aA=iA,oA=aA("toUpperCase"),uA=oA;const Cu=ft(uA);function et(t){return function(){return t}}const Sh=Math.abs,Rt=Math.atan2,Ce=Math.cos,sA=Math.max,xs=Math.min,Vt=Math.sin,Dt=Math.sqrt,Lt=1e-12,cr=Math.PI,co=cr/2,ar=2*cr;function cA(t){return t>1?0:t<-1?cr:Math.acos(t)}function $h(t){return t>=1?co:t<=-1?-co:Math.asin(t)}const sc=Math.PI,cc=2*sc,Pr=1e-6,lA=cc-Pr;function Xy(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}function fA(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return Xy;const r=10**e;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class hA{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=e==null?Xy:fA(e)}moveTo(e,r){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,r){this._append`L${this._x1=+e},${this._y1=+r}`}quadraticCurveTo(e,r,n,i){this._append`Q${+e},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(e,r,n,i,a,o){this._append`C${+e},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(e,r,n,i,a){if(e=+e,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,s=n-e,c=i-r,l=o-e,f=u-r,h=l*l+f*f;if(this._x1===null)this._append`M${this._x1=e},${this._y1=r}`;else if(h>Pr)if(!(Math.abs(f*s-c*l)>Pr)||!a)this._append`L${this._x1=e},${this._y1=r}`;else{let p=n-o,d=i-u,v=s*s+c*c,y=p*p+d*d,g=Math.sqrt(v),b=Math.sqrt(h),_=a*Math.tan((sc-Math.acos((v+h-y)/(2*g*b)))/2),w=_/b,m=_/g;Math.abs(w-1)>Pr&&this._append`L${e+w*l},${r+w*f}`,this._append`A${a},${a},0,0,${+(f*p>l*d)},${this._x1=e+m*s},${this._y1=r+m*c}`}}arc(e,r,n,i,a,o){if(e=+e,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),s=n*Math.sin(i),c=e+u,l=r+s,f=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${c},${l}`:(Math.abs(this._x1-c)>Pr||Math.abs(this._y1-l)>Pr)&&this._append`L${c},${l}`,n&&(h<0&&(h=h%cc+cc),h>lA?this._append`A${n},${n},0,1,${f},${e-u},${r-s}A${n},${n},0,1,${f},${this._x1=c},${this._y1=l}`:h>Pr&&this._append`A${n},${n},0,${+(h>=sc)},${f},${this._x1=e+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(e,r,n,i){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function wa(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(r==null)e=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);e=n}return t},()=>new hA(e)}function pA(t){return t.innerRadius}function dA(t){return t.outerRadius}function vA(t){return t.startAngle}function yA(t){return t.endAngle}function mA(t){return t&&t.padAngle}function gA(t,e,r,n,i,a,o,u){var s=r-t,c=n-e,l=o-i,f=u-a,h=f*s-l*c;if(!(h*h<Lt))return h=(l*(e-a)-f*(t-i))/h,[t+h*s,e+h*c]}function Ra(t,e,r,n,i,a,o){var u=t-r,s=e-n,c=(o?a:-a)/Dt(u*u+s*s),l=c*s,f=-c*u,h=t+l,p=e+f,d=r+l,v=n+f,y=(h+d)/2,g=(p+v)/2,b=d-h,_=v-p,w=b*b+_*_,m=i-a,x=h*v-d*p,O=(_<0?-1:1)*Dt(sA(0,m*m*w-x*x)),S=(x*_-b*O)/w,$=(-x*b-_*O)/w,k=(x*_+b*O)/w,T=(-x*b+_*O)/w,M=S-y,C=$-g,P=k-y,j=T-g;return M*M+C*C>P*P+j*j&&(S=k,$=T),{cx:S,cy:$,x01:-l,y01:-f,x11:S*(i/m-1),y11:$*(i/m-1)}}function Y4(){var t=pA,e=dA,r=et(0),n=null,i=vA,a=yA,o=mA,u=null,s=wa(c);function c(){var l,f,h=+t.apply(this,arguments),p=+e.apply(this,arguments),d=i.apply(this,arguments)-co,v=a.apply(this,arguments)-co,y=Sh(v-d),g=v>d;if(u||(u=l=s()),p<h&&(f=p,p=h,h=f),!(p>Lt))u.moveTo(0,0);else if(y>ar-Lt)u.moveTo(p*Ce(d),p*Vt(d)),u.arc(0,0,p,d,v,!g),h>Lt&&(u.moveTo(h*Ce(v),h*Vt(v)),u.arc(0,0,h,v,d,g));else{var b=d,_=v,w=d,m=v,x=y,O=y,S=o.apply(this,arguments)/2,$=S>Lt&&(n?+n.apply(this,arguments):Dt(h*h+p*p)),k=xs(Sh(p-h)/2,+r.apply(this,arguments)),T=k,M=k,C,P;if($>Lt){var j=$h($/h*Vt(S)),E=$h($/p*Vt(S));(x-=j*2)>Lt?(j*=g?1:-1,w+=j,m-=j):(x=0,w=m=(d+v)/2),(O-=E*2)>Lt?(E*=g?1:-1,b+=E,_-=E):(O=0,b=_=(d+v)/2)}var I=p*Ce(b),N=p*Vt(b),R=h*Ce(m),B=h*Vt(m);if(k>Lt){var U=p*Ce(_),z=p*Vt(_),G=h*Ce(w),Z=h*Vt(w),rt;if(y<cr)if(rt=gA(I,N,G,Z,U,z,R,B)){var ht=I-rt[0],xt=N-rt[1],ct=U-rt[0],K=z-rt[1],tt=1/Vt(cA((ht*ct+xt*K)/(Dt(ht*ht+xt*xt)*Dt(ct*ct+K*K)))/2),nt=Dt(rt[0]*rt[0]+rt[1]*rt[1]);T=xs(k,(h-nt)/(tt-1)),M=xs(k,(p-nt)/(tt+1))}else T=M=0}O>Lt?M>Lt?(C=Ra(G,Z,I,N,p,M,g),P=Ra(U,z,R,B,p,M,g),u.moveTo(C.cx+C.x01,C.cy+C.y01),M<k?u.arc(C.cx,C.cy,M,Rt(C.y01,C.x01),Rt(P.y01,P.x01),!g):(u.arc(C.cx,C.cy,M,Rt(C.y01,C.x01),Rt(C.y11,C.x11),!g),u.arc(0,0,p,Rt(C.cy+C.y11,C.cx+C.x11),Rt(P.cy+P.y11,P.cx+P.x11),!g),u.arc(P.cx,P.cy,M,Rt(P.y11,P.x11),Rt(P.y01,P.x01),!g))):(u.moveTo(I,N),u.arc(0,0,p,b,_,!g)):u.moveTo(I,N),!(h>Lt)||!(x>Lt)?u.lineTo(R,B):T>Lt?(C=Ra(R,B,U,z,h,-T,g),P=Ra(I,N,G,Z,h,-T,g),u.lineTo(C.cx+C.x01,C.cy+C.y01),T<k?u.arc(C.cx,C.cy,T,Rt(C.y01,C.x01),Rt(P.y01,P.x01),!g):(u.arc(C.cx,C.cy,T,Rt(C.y01,C.x01),Rt(C.y11,C.x11),!g),u.arc(0,0,h,Rt(C.cy+C.y11,C.cx+C.x11),Rt(P.cy+P.y11,P.cx+P.x11),g),u.arc(P.cx,P.cy,T,Rt(P.y11,P.x11),Rt(P.y01,P.x01),!g))):u.arc(0,0,h,m,w,g)}if(u.closePath(),l)return u=null,l+""||null}return c.centroid=function(){var l=(+t.apply(this,arguments)+ +e.apply(this,arguments))/2,f=(+i.apply(this,arguments)+ +a.apply(this,arguments))/2-cr/2;return[Ce(f)*l,Vt(f)*l]},c.innerRadius=function(l){return arguments.length?(t=typeof l=="function"?l:et(+l),c):t},c.outerRadius=function(l){return arguments.length?(e=typeof l=="function"?l:et(+l),c):e},c.cornerRadius=function(l){return arguments.length?(r=typeof l=="function"?l:et(+l),c):r},c.padRadius=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:et(+l),c):n},c.startAngle=function(l){return arguments.length?(i=typeof l=="function"?l:et(+l),c):i},c.endAngle=function(l){return arguments.length?(a=typeof l=="function"?l:et(+l),c):a},c.padAngle=function(l){return arguments.length?(o=typeof l=="function"?l:et(+l),c):o},c.context=function(l){return arguments.length?(u=l??null,c):u},c}var bA=Array.prototype.slice;function ku(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function Yy(t){this._context=t}Yy.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e);break}}};function Iu(t){return new Yy(t)}function ef(t){return t[0]}function rf(t){return t[1]}function Zy(t,e){var r=et(!0),n=null,i=Iu,a=null,o=wa(u);t=typeof t=="function"?t:t===void 0?ef:et(t),e=typeof e=="function"?e:e===void 0?rf:et(e);function u(s){var c,l=(s=ku(s)).length,f,h=!1,p;for(n==null&&(a=i(p=o())),c=0;c<=l;++c)!(c<l&&r(f=s[c],c,s))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+t(f,c,s),+e(f,c,s));if(p)return a=null,p+""||null}return u.x=function(s){return arguments.length?(t=typeof s=="function"?s:et(+s),u):t},u.y=function(s){return arguments.length?(e=typeof s=="function"?s:et(+s),u):e},u.defined=function(s){return arguments.length?(r=typeof s=="function"?s:et(!!s),u):r},u.curve=function(s){return arguments.length?(i=s,n!=null&&(a=i(n)),u):i},u.context=function(s){return arguments.length?(s==null?n=a=null:a=i(n=s),u):n},u}function La(t,e,r){var n=null,i=et(!0),a=null,o=Iu,u=null,s=wa(c);t=typeof t=="function"?t:t===void 0?ef:et(+t),e=typeof e=="function"?e:et(e===void 0?0:+e),r=typeof r=="function"?r:r===void 0?rf:et(+r);function c(f){var h,p,d,v=(f=ku(f)).length,y,g=!1,b,_=new Array(v),w=new Array(v);for(a==null&&(u=o(b=s())),h=0;h<=v;++h){if(!(h<v&&i(y=f[h],h,f))===g)if(g=!g)p=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),d=h-1;d>=p;--d)u.point(_[d],w[d]);u.lineEnd(),u.areaEnd()}g&&(_[h]=+t(y,h,f),w[h]=+e(y,h,f),u.point(n?+n(y,h,f):_[h],r?+r(y,h,f):w[h]))}if(b)return u=null,b+""||null}function l(){return Zy().defined(i).curve(o).context(a)}return c.x=function(f){return arguments.length?(t=typeof f=="function"?f:et(+f),n=null,c):t},c.x0=function(f){return arguments.length?(t=typeof f=="function"?f:et(+f),c):t},c.x1=function(f){return arguments.length?(n=f==null?null:typeof f=="function"?f:et(+f),c):n},c.y=function(f){return arguments.length?(e=typeof f=="function"?f:et(+f),r=null,c):e},c.y0=function(f){return arguments.length?(e=typeof f=="function"?f:et(+f),c):e},c.y1=function(f){return arguments.length?(r=f==null?null:typeof f=="function"?f:et(+f),c):r},c.lineX0=c.lineY0=function(){return l().x(t).y(e)},c.lineY1=function(){return l().x(t).y(r)},c.lineX1=function(){return l().x(n).y(e)},c.defined=function(f){return arguments.length?(i=typeof f=="function"?f:et(!!f),c):i},c.curve=function(f){return arguments.length?(o=f,a!=null&&(u=o(a)),c):o},c.context=function(f){return arguments.length?(f==null?a=u=null:u=o(a=f),c):a},c}function xA(t,e){return e<t?-1:e>t?1:e>=t?0:NaN}function _A(t){return t}function Z4(){var t=_A,e=xA,r=null,n=et(0),i=et(ar),a=et(0);function o(u){var s,c=(u=ku(u)).length,l,f,h=0,p=new Array(c),d=new Array(c),v=+n.apply(this,arguments),y=Math.min(ar,Math.max(-ar,i.apply(this,arguments)-v)),g,b=Math.min(Math.abs(y)/c,a.apply(this,arguments)),_=b*(y<0?-1:1),w;for(s=0;s<c;++s)(w=d[p[s]=s]=+t(u[s],s,u))>0&&(h+=w);for(e!=null?p.sort(function(m,x){return e(d[m],d[x])}):r!=null&&p.sort(function(m,x){return r(u[m],u[x])}),s=0,f=h?(y-c*_)/h:0;s<c;++s,v=g)l=p[s],w=d[l],g=v+(w>0?w*f:0)+_,d[l]={data:u[l],index:s,value:w,startAngle:v,endAngle:g,padAngle:b};return d}return o.value=function(u){return arguments.length?(t=typeof u=="function"?u:et(+u),o):t},o.sortValues=function(u){return arguments.length?(e=u,r=null,o):e},o.sort=function(u){return arguments.length?(r=u,e=null,o):r},o.startAngle=function(u){return arguments.length?(n=typeof u=="function"?u:et(+u),o):n},o.endAngle=function(u){return arguments.length?(i=typeof u=="function"?u:et(+u),o):i},o.padAngle=function(u){return arguments.length?(a=typeof u=="function"?u:et(+u),o):a},o}class Jy{constructor(e,r){this._context=e,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(e,r){switch(e=+e,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,r,e,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,e,this._y0,e,r);break}}this._x0=e,this._y0=r}}function Qy(t){return new Jy(t,!0)}function tm(t){return new Jy(t,!1)}function wA(t){return t.source}function OA(t){return t.target}function em(t){let e=wA,r=OA,n=ef,i=rf,a=null,o=null,u=wa(s);function s(){let c;const l=bA.call(arguments),f=e.apply(this,l),h=r.apply(this,l);if(a==null&&(o=t(c=u())),o.lineStart(),l[0]=f,o.point(+n.apply(this,l),+i.apply(this,l)),l[0]=h,o.point(+n.apply(this,l),+i.apply(this,l)),o.lineEnd(),c)return o=null,c+""||null}return s.source=function(c){return arguments.length?(e=c,s):e},s.target=function(c){return arguments.length?(r=c,s):r},s.x=function(c){return arguments.length?(n=typeof c=="function"?c:et(+c),s):n},s.y=function(c){return arguments.length?(i=typeof c=="function"?c:et(+c),s):i},s.context=function(c){return arguments.length?(c==null?a=o=null:o=t(a=c),s):a},s}function J4(){return em(Qy)}function Q4(){return em(tm)}const nf={draw(t,e){const r=Dt(e/cr);t.moveTo(r,0),t.arc(0,0,r,0,ar)}},AA={draw(t,e){const r=Dt(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},rm=Dt(1/3),PA=rm*2,SA={draw(t,e){const r=Dt(e/PA),n=r*rm;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},$A={draw(t,e){const r=Dt(e),n=-r/2;t.rect(n,n,r,r)}},EA=.8908130915292852,nm=Vt(cr/10)/Vt(7*cr/10),TA=Vt(ar/10)*nm,jA=-Ce(ar/10)*nm,MA={draw(t,e){const r=Dt(e*EA),n=TA*r,i=jA*r;t.moveTo(0,-r),t.lineTo(n,i);for(let a=1;a<5;++a){const o=ar*a/5,u=Ce(o),s=Vt(o);t.lineTo(s*r,-u*r),t.lineTo(u*n-s*i,s*n+u*i)}t.closePath()}},_s=Dt(3),CA={draw(t,e){const r=-Dt(e/(_s*3));t.moveTo(0,r*2),t.lineTo(-_s*r,-r),t.lineTo(_s*r,-r),t.closePath()}},ne=-.5,ie=Dt(3)/2,lc=1/Dt(12),kA=(lc/2+1)*3,IA={draw(t,e){const r=Dt(e/kA),n=r/2,i=r*lc,a=n,o=r*lc+r,u=-a,s=o;t.moveTo(n,i),t.lineTo(a,o),t.lineTo(u,s),t.lineTo(ne*n-ie*i,ie*n+ne*i),t.lineTo(ne*a-ie*o,ie*a+ne*o),t.lineTo(ne*u-ie*s,ie*u+ne*s),t.lineTo(ne*n+ie*i,ne*i-ie*n),t.lineTo(ne*a+ie*o,ne*o-ie*a),t.lineTo(ne*u+ie*s,ne*s-ie*u),t.closePath()}};function NA(t,e){let r=null,n=wa(i);t=typeof t=="function"?t:et(t||nf),e=typeof e=="function"?e:et(e===void 0?64:+e);function i(){let a;if(r||(r=a=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(t=typeof a=="function"?a:et(a),i):t},i.size=function(a){return arguments.length?(e=typeof a=="function"?a:et(+a),i):e},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function lr(){}function lo(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function Nu(t){this._context=t}Nu.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:lo(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:lo(this,t,e);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};function DA(t){return new Nu(t)}function im(t){this._context=t}im.prototype={areaStart:lr,areaEnd:lr,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:lo(this,t,e);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};function RA(t){return new im(t)}function am(t){this._context=t}am.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:lo(this,t,e);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};function LA(t){return new am(t)}function om(t,e){this._basis=new Nu(t),this._beta=e}om.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,e=this._y,r=t.length-1;if(r>0)for(var n=t[0],i=e[0],a=t[r]-n,o=e[r]-i,u=-1,s;++u<=r;)s=u/r,this._basis.point(this._beta*t[u]+(1-this._beta)*(n+s*a),this._beta*e[u]+(1-this._beta)*(i+s*o));this._x=this._y=null,this._basis.lineEnd()},point:function(t,e){this._x.push(+t),this._y.push(+e)}};const tG=function t(e){function r(n){return e===1?new Nu(n):new om(n,e)}return r.beta=function(n){return t(+n)},r}(.85);function fo(t,e,r){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-e),t._y2+t._k*(t._y1-r),t._x2,t._y2)}function af(t,e){this._context=t,this._k=(1-e)/6}af.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:fo(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2,this._x1=t,this._y1=e;break;case 2:this._point=3;default:fo(this,t,e);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};const eG=function t(e){function r(n){return new af(n,e)}return r.tension=function(n){return t(+n)},r}(0);function of(t,e){this._context=t,this._k=(1-e)/6}of.prototype={areaStart:lr,areaEnd:lr,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x3=t,this._y3=e;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=e);break;case 2:this._point=3,this._x5=t,this._y5=e;break;default:fo(this,t,e);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};const rG=function t(e){function r(n){return new of(n,e)}return r.tension=function(n){return t(+n)},r}(0);function uf(t,e){this._context=t,this._k=(1-e)/6}uf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:fo(this,t,e);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};const nG=function t(e){function r(n){return new uf(n,e)}return r.tension=function(n){return t(+n)},r}(0);function sf(t,e,r){var n=t._x1,i=t._y1,a=t._x2,o=t._y2;if(t._l01_a>Lt){var u=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,s=3*t._l01_a*(t._l01_a+t._l12_a);n=(n*u-t._x0*t._l12_2a+t._x2*t._l01_2a)/s,i=(i*u-t._y0*t._l12_2a+t._y2*t._l01_2a)/s}if(t._l23_a>Lt){var c=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,l=3*t._l23_a*(t._l23_a+t._l12_a);a=(a*c+t._x1*t._l23_2a-e*t._l12_2a)/l,o=(o*c+t._y1*t._l23_2a-r*t._l12_2a)/l}t._context.bezierCurveTo(n,i,a,o,t._x2,t._y2)}function um(t,e){this._context=t,this._alpha=e}um.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){if(t=+t,e=+e,this._point){var r=this._x2-t,n=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+n*n,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3;default:sf(this,t,e);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};const iG=function t(e){function r(n){return e?new um(n,e):new af(n,0)}return r.alpha=function(n){return t(+n)},r}(.5);function sm(t,e){this._context=t,this._alpha=e}sm.prototype={areaStart:lr,areaEnd:lr,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(t,e){if(t=+t,e=+e,this._point){var r=this._x2-t,n=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+n*n,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=e;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=e);break;case 2:this._point=3,this._x5=t,this._y5=e;break;default:sf(this,t,e);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};const aG=function t(e){function r(n){return e?new sm(n,e):new of(n,0)}return r.alpha=function(n){return t(+n)},r}(.5);function cm(t,e){this._context=t,this._alpha=e}cm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){if(t=+t,e=+e,this._point){var r=this._x2-t,n=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+n*n,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:sf(this,t,e);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};const oG=function t(e){function r(n){return e?new cm(n,e):new uf(n,0)}return r.alpha=function(n){return t(+n)},r}(.5);function lm(t){this._context=t}lm.prototype={areaStart:lr,areaEnd:lr,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function BA(t){return new lm(t)}function Eh(t){return t<0?-1:1}function Th(t,e,r){var n=t._x1-t._x0,i=e-t._x1,a=(t._y1-t._y0)/(n||i<0&&-0),o=(r-t._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Eh(a)+Eh(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function jh(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function ws(t,e,r){var n=t._x0,i=t._y0,a=t._x1,o=t._y1,u=(a-n)/3;t._context.bezierCurveTo(n+u,i+u*e,a-u,o-u*r,a,o)}function ho(t){this._context=t}ho.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ws(this,this._t0,jh(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(t=+t,e=+e,!(t===this._x1&&e===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,ws(this,jh(this,r=Th(this,t,e)),r);break;default:ws(this,this._t0,r=Th(this,t,e));break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}};function fm(t){this._context=new hm(t)}(fm.prototype=Object.create(ho.prototype)).point=function(t,e){ho.prototype.point.call(this,e,t)};function hm(t){this._context=t}hm.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,a){this._context.bezierCurveTo(e,t,n,r,a,i)}};function zA(t){return new ho(t)}function FA(t){return new fm(t)}function pm(t){this._context=t}pm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),r===2)this._context.lineTo(t[1],e[1]);else for(var n=Mh(t),i=Mh(e),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],t[o],e[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}};function Mh(t){var e,r=t.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=t[0]+2*t[1],e=1;e<r-1;++e)i[e]=1,a[e]=4,o[e]=4*t[e]+2*t[e+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*t[r-1]+t[r],e=1;e<r;++e)n=i[e]/a[e-1],a[e]-=n,o[e]-=n*o[e-1];for(i[r-1]=o[r-1]/a[r-1],e=r-2;e>=0;--e)i[e]=(o[e]-i[e+1])/a[e];for(a[r-1]=(t[r]+i[r-1])/2,e=0;e<r-1;++e)a[e]=2*t[e+1]-i[e+1];return[i,a]}function WA(t){return new pm(t)}function Du(t,e){this._context=t,this._t=e}Du.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}break}}this._x=t,this._y=e}};function UA(t){return new Du(t,.5)}function KA(t){return new Du(t,0)}function qA(t){return new Du(t,1)}function mn(t,e){if((o=t.length)>1)for(var r=1,n,i,a=t[e[0]],o,u=a.length;r<o;++r)for(i=a,a=t[e[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function fc(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function HA(t,e){return t[e]}function GA(t){const e=[];return e.key=t,e}function VA(){var t=et([]),e=fc,r=mn,n=HA;function i(a){var o=Array.from(t.apply(this,arguments),GA),u,s=o.length,c=-1,l;for(const f of a)for(u=0,++c;u<s;++u)(o[u][c]=[0,+n(f,o[u].key,c,a)]).data=f;for(u=0,l=ku(e(o));u<s;++u)o[l[u]].index=u;return r(o,l),o}return i.keys=function(a){return arguments.length?(t=typeof a=="function"?a:et(Array.from(a)),i):t},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:et(+a),i):n},i.order=function(a){return arguments.length?(e=a==null?fc:typeof a=="function"?a:et(Array.from(a)),i):e},i.offset=function(a){return arguments.length?(r=a??mn,i):r},i}function XA(t,e){if((n=t.length)>0){for(var r,n,i=0,a=t[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}mn(t,e)}}function YA(t,e){if((i=t.length)>0){for(var r=0,n=t[e[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=t[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}mn(t,e)}}function ZA(t,e){if(!(!((o=t.length)>0)||!((a=(i=t[e[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,s=0,c=0;u<o;++u){for(var l=t[e[u]],f=l[n][1]||0,h=l[n-1][1]||0,p=(f-h)/2,d=0;d<u;++d){var v=t[e[d]],y=v[n][1]||0,g=v[n-1][1]||0;p+=y-g}s+=f,c+=p*f}i[n-1][1]+=i[n-1][0]=r,s&&(r-=c/s)}i[n-1][1]+=i[n-1][0]=r,mn(t,e)}}function Di(t){"@babel/helpers - typeof";return Di=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Di(t)}var JA=["type","size","sizeType"];function hc(){return hc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},hc.apply(this,arguments)}function Ch(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function kh(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ch(Object(r),!0).forEach(function(n){QA(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ch(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function QA(t,e,r){return e=tP(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tP(t){var e=eP(t,"string");return Di(e)=="symbol"?e:e+""}function eP(t,e){if(Di(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Di(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function rP(t,e){if(t==null)return{};var r=nP(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function nP(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var dm={symbolCircle:nf,symbolCross:AA,symbolDiamond:SA,symbolSquare:$A,symbolStar:MA,symbolTriangle:CA,symbolWye:IA},iP=Math.PI/180,aP=function(e){var r="symbol".concat(Cu(e));return dm[r]||nf},oP=function(e,r,n){if(r==="area")return e;switch(n){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":{var i=18*iP;return 1.25*e*e*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},uP=function(e,r){dm["symbol".concat(Cu(e))]=r},Ru=function(e){var r=e.type,n=r===void 0?"circle":r,i=e.size,a=i===void 0?64:i,o=e.sizeType,u=o===void 0?"area":o,s=rP(e,JA),c=kh(kh({},s),{},{type:n,size:a,sizeType:u}),l=function(){var y=aP(n),g=NA().type(y).size(oP(a,u,n));return g()},f=c.className,h=c.cx,p=c.cy,d=q(c,!0);return h===+h&&p===+p&&a===+a?A.createElement("path",hc({},d,{className:J("recharts-symbols",f),transform:"translate(".concat(h,", ").concat(p,")"),d:l()})):null};Ru.registerSymbol=uP;function gn(t){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},gn(t)}function pc(){return pc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pc.apply(this,arguments)}function Ih(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function sP(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ih(Object(r),!0).forEach(function(n){Ri(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ih(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function cP(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function lP(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ym(n.key),n)}}function fP(t,e,r){return e&&lP(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function hP(t,e,r){return e=po(e),pP(t,vm()?Reflect.construct(e,r||[],po(t).constructor):e.apply(t,r))}function pP(t,e){if(e&&(gn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dP(t)}function dP(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function vm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(vm=function(){return!!t})()}function po(t){return po=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},po(t)}function vP(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dc(t,e)}function dc(t,e){return dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},dc(t,e)}function Ri(t,e,r){return e=ym(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ym(t){var e=yP(t,"string");return gn(e)=="symbol"?e:e+""}function yP(t,e){if(gn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var pe=32,cf=function(t){function e(){return cP(this,e),hP(this,e,arguments)}return vP(e,t),fP(e,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=pe/2,o=pe/6,u=pe/3,s=n.inactive?i:n.color;if(n.type==="plainline")return A.createElement("line",{strokeWidth:4,fill:"none",stroke:s,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:pe,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return A.createElement("path",{strokeWidth:4,fill:"none",stroke:s,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(pe,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return A.createElement("path",{stroke:"none",fill:s,d:"M0,".concat(pe/8,"h").concat(pe,"v").concat(pe*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(A.isValidElement(n.legendIcon)){var c=sP({},n);return delete c.legendIcon,A.cloneElement(n.legendIcon,c)}return A.createElement(Ru,{fill:s,cx:a,cy:a,size:pe,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,s=i.formatter,c=i.inactiveColor,l={x:0,y:0,width:pe,height:pe},f={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(p,d){var v=p.formatter||s,y=J(Ri(Ri({"recharts-legend-item":!0},"legend-item-".concat(d),!0),"inactive",p.inactive));if(p.type==="none")return null;var g=V(p.value)?null:p.value;ge(!V(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var b=p.inactive?c:p.color;return A.createElement("li",pc({className:y,style:f,key:"legend-item-".concat(d)},sr(n.props,p,d)),A.createElement(ic,{width:o,height:o,viewBox:l,style:h},n.renderIcon(p)),A.createElement("span",{className:"recharts-legend-item-text",style:{color:b}},v?v(g,p,d):g))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return A.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(W.PureComponent);Ri(cf,"displayName","Legend");Ri(cf,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var mP=bu;function gP(){this.__data__=new mP,this.size=0}var bP=gP;function xP(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}var _P=xP;function wP(t){return this.__data__.get(t)}var OP=wP;function AP(t){return this.__data__.has(t)}var PP=AP,SP=bu,$P=Hl,EP=Gl,TP=200;function jP(t,e){var r=this.__data__;if(r instanceof SP){var n=r.__data__;if(!$P||n.length<TP-1)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new EP(n)}return r.set(t,e),this.size=r.size,this}var MP=jP,CP=bu,kP=bP,IP=_P,NP=OP,DP=PP,RP=MP;function ri(t){var e=this.__data__=new CP(t);this.size=e.size}ri.prototype.clear=kP;ri.prototype.delete=IP;ri.prototype.get=NP;ri.prototype.has=DP;ri.prototype.set=RP;var mm=ri,LP="__lodash_hash_undefined__";function BP(t){return this.__data__.set(t,LP),this}var zP=BP;function FP(t){return this.__data__.has(t)}var WP=FP,UP=Gl,KP=zP,qP=WP;function vo(t){var e=-1,r=t==null?0:t.length;for(this.__data__=new UP;++e<r;)this.add(t[e])}vo.prototype.add=vo.prototype.push=KP;vo.prototype.has=qP;var gm=vo;function HP(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}var bm=HP;function GP(t,e){return t.has(e)}var xm=GP,VP=gm,XP=bm,YP=xm,ZP=1,JP=2;function QP(t,e,r,n,i,a){var o=r&ZP,u=t.length,s=e.length;if(u!=s&&!(o&&s>u))return!1;var c=a.get(t),l=a.get(e);if(c&&l)return c==e&&l==t;var f=-1,h=!0,p=r&JP?new VP:void 0;for(a.set(t,e),a.set(e,t);++f<u;){var d=t[f],v=e[f];if(n)var y=o?n(v,d,f,e,t,a):n(d,v,f,t,e,a);if(y!==void 0){if(y)continue;h=!1;break}if(p){if(!XP(e,function(g,b){if(!YP(p,b)&&(d===g||i(d,g,r,n,a)))return p.push(b)})){h=!1;break}}else if(!(d===v||i(d,v,r,n,a))){h=!1;break}}return a.delete(t),a.delete(e),h}var _m=QP,tS=Se,eS=tS.Uint8Array,rS=eS;function nS(t){var e=-1,r=Array(t.size);return t.forEach(function(n,i){r[++e]=[i,n]}),r}var iS=nS;function aS(t){var e=-1,r=Array(t.size);return t.forEach(function(n){r[++e]=n}),r}var lf=aS,Nh=_a,Dh=rS,oS=ql,uS=_m,sS=iS,cS=lf,lS=1,fS=2,hS="[object Boolean]",pS="[object Date]",dS="[object Error]",vS="[object Map]",yS="[object Number]",mS="[object RegExp]",gS="[object Set]",bS="[object String]",xS="[object Symbol]",_S="[object ArrayBuffer]",wS="[object DataView]",Rh=Nh?Nh.prototype:void 0,Os=Rh?Rh.valueOf:void 0;function OS(t,e,r,n,i,a,o){switch(r){case wS:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case _S:return!(t.byteLength!=e.byteLength||!a(new Dh(t),new Dh(e)));case hS:case pS:case yS:return oS(+t,+e);case dS:return t.name==e.name&&t.message==e.message;case mS:case bS:return t==e+"";case vS:var u=sS;case gS:var s=n&lS;if(u||(u=cS),t.size!=e.size&&!s)return!1;var c=o.get(t);if(c)return c==e;n|=fS,o.set(t,e);var l=uS(u(t),u(e),n,i,a,o);return o.delete(t),l;case xS:if(Os)return Os.call(t)==Os.call(e)}return!1}var AS=OS;function PS(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}var wm=PS,SS=wm,$S=Xt;function ES(t,e,r){var n=e(t);return $S(t)?n:SS(n,r(t))}var TS=ES;function jS(t,e){for(var r=-1,n=t==null?0:t.length,i=0,a=[];++r<n;){var o=t[r];e(o,r,t)&&(a[i++]=o)}return a}var MS=jS;function CS(){return[]}var kS=CS,IS=MS,NS=kS,DS=Object.prototype,RS=DS.propertyIsEnumerable,Lh=Object.getOwnPropertySymbols,LS=Lh?function(t){return t==null?[]:(t=Object(t),IS(Lh(t),function(e){return RS.call(t,e)}))}:NS,BS=LS;function zS(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}var FS=zS,WS=Xe,US=Ye,KS="[object Arguments]";function qS(t){return US(t)&&WS(t)==KS}var HS=qS,Bh=HS,GS=Ye,Om=Object.prototype,VS=Om.hasOwnProperty,XS=Om.propertyIsEnumerable,YS=Bh(function(){return arguments}())?Bh:function(t){return GS(t)&&VS.call(t,"callee")&&!XS.call(t,"callee")},ff=YS,yo={exports:{}};function ZS(){return!1}var JS=ZS;yo.exports;(function(t,e){var r=Se,n=JS,i=e&&!e.nodeType&&e,a=i&&!0&&t&&!t.nodeType&&t,o=a&&a.exports===i,u=o?r.Buffer:void 0,s=u?u.isBuffer:void 0,c=s||n;t.exports=c})(yo,yo.exports);var Am=yo.exports,QS=9007199254740991,t$=/^(?:0|[1-9]\d*)$/;function e$(t,e){var r=typeof t;return e=e??QS,!!e&&(r=="number"||r!="symbol"&&t$.test(t))&&t>-1&&t%1==0&&t<e}var hf=e$,r$=9007199254740991;function n$(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=r$}var pf=n$,i$=Xe,a$=pf,o$=Ye,u$="[object Arguments]",s$="[object Array]",c$="[object Boolean]",l$="[object Date]",f$="[object Error]",h$="[object Function]",p$="[object Map]",d$="[object Number]",v$="[object Object]",y$="[object RegExp]",m$="[object Set]",g$="[object String]",b$="[object WeakMap]",x$="[object ArrayBuffer]",_$="[object DataView]",w$="[object Float32Array]",O$="[object Float64Array]",A$="[object Int8Array]",P$="[object Int16Array]",S$="[object Int32Array]",$$="[object Uint8Array]",E$="[object Uint8ClampedArray]",T$="[object Uint16Array]",j$="[object Uint32Array]",mt={};mt[w$]=mt[O$]=mt[A$]=mt[P$]=mt[S$]=mt[$$]=mt[E$]=mt[T$]=mt[j$]=!0;mt[u$]=mt[s$]=mt[x$]=mt[c$]=mt[_$]=mt[l$]=mt[f$]=mt[h$]=mt[p$]=mt[d$]=mt[v$]=mt[y$]=mt[m$]=mt[g$]=mt[b$]=!1;function M$(t){return o$(t)&&a$(t.length)&&!!mt[i$(t)]}var C$=M$;function k$(t){return function(e){return t(e)}}var Pm=k$,mo={exports:{}};mo.exports;(function(t,e){var r=Ty,n=e&&!e.nodeType&&e,i=n&&!0&&t&&!t.nodeType&&t,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var s=i&&i.require&&i.require("util").types;return s||o&&o.binding&&o.binding("util")}catch{}}();t.exports=u})(mo,mo.exports);var I$=mo.exports,N$=C$,D$=Pm,zh=I$,Fh=zh&&zh.isTypedArray,R$=Fh?D$(Fh):N$,Sm=R$,L$=FS,B$=ff,z$=Xt,F$=Am,W$=hf,U$=Sm,K$=Object.prototype,q$=K$.hasOwnProperty;function H$(t,e){var r=z$(t),n=!r&&B$(t),i=!r&&!n&&F$(t),a=!r&&!n&&!i&&U$(t),o=r||n||i||a,u=o?L$(t.length,String):[],s=u.length;for(var c in t)(e||q$.call(t,c))&&!(o&&(c=="length"||i&&(c=="offset"||c=="parent")||a&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||W$(c,s)))&&u.push(c);return u}var G$=H$,V$=Object.prototype;function X$(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||V$;return t===r}var Y$=X$;function Z$(t,e){return function(r){return t(e(r))}}var $m=Z$,J$=$m,Q$=J$(Object.keys,Object),tE=Q$,eE=Y$,rE=tE,nE=Object.prototype,iE=nE.hasOwnProperty;function aE(t){if(!eE(t))return rE(t);var e=[];for(var r in Object(t))iE.call(t,r)&&r!="constructor"&&e.push(r);return e}var oE=aE,uE=Kl,sE=pf;function cE(t){return t!=null&&sE(t.length)&&!uE(t)}var Oa=cE,lE=G$,fE=oE,hE=Oa;function pE(t){return hE(t)?lE(t):fE(t)}var Lu=pE,dE=TS,vE=BS,yE=Lu;function mE(t){return dE(t,yE,vE)}var gE=mE,Wh=gE,bE=1,xE=Object.prototype,_E=xE.hasOwnProperty;function wE(t,e,r,n,i,a){var o=r&bE,u=Wh(t),s=u.length,c=Wh(e),l=c.length;if(s!=l&&!o)return!1;for(var f=s;f--;){var h=u[f];if(!(o?h in e:_E.call(e,h)))return!1}var p=a.get(t),d=a.get(e);if(p&&d)return p==e&&d==t;var v=!0;a.set(t,e),a.set(e,t);for(var y=o;++f<s;){h=u[f];var g=t[h],b=e[h];if(n)var _=o?n(b,g,h,e,t,a):n(g,b,h,t,e,a);if(!(_===void 0?g===b||i(g,b,r,n,a):_)){v=!1;break}y||(y=h=="constructor")}if(v&&!y){var w=t.constructor,m=e.constructor;w!=m&&"constructor"in t&&"constructor"in e&&!(typeof w=="function"&&w instanceof w&&typeof m=="function"&&m instanceof m)&&(v=!1)}return a.delete(t),a.delete(e),v}var OE=wE,AE=Hr,PE=Se,SE=AE(PE,"DataView"),$E=SE,EE=Hr,TE=Se,jE=EE(TE,"Promise"),ME=jE,CE=Hr,kE=Se,IE=CE(kE,"Set"),Em=IE,NE=Hr,DE=Se,RE=NE(DE,"WeakMap"),LE=RE,vc=$E,yc=Hl,mc=ME,gc=Em,bc=LE,Tm=Xe,ni=My,Uh="[object Map]",BE="[object Object]",Kh="[object Promise]",qh="[object Set]",Hh="[object WeakMap]",Gh="[object DataView]",zE=ni(vc),FE=ni(yc),WE=ni(mc),UE=ni(gc),KE=ni(bc),Sr=Tm;(vc&&Sr(new vc(new ArrayBuffer(1)))!=Gh||yc&&Sr(new yc)!=Uh||mc&&Sr(mc.resolve())!=Kh||gc&&Sr(new gc)!=qh||bc&&Sr(new bc)!=Hh)&&(Sr=function(t){var e=Tm(t),r=e==BE?t.constructor:void 0,n=r?ni(r):"";if(n)switch(n){case zE:return Gh;case FE:return Uh;case WE:return Kh;case UE:return qh;case KE:return Hh}return e});var qE=Sr,As=mm,HE=_m,GE=AS,VE=OE,Vh=qE,Xh=Xt,Yh=Am,XE=Sm,YE=1,Zh="[object Arguments]",Jh="[object Array]",Ba="[object Object]",ZE=Object.prototype,Qh=ZE.hasOwnProperty;function JE(t,e,r,n,i,a){var o=Xh(t),u=Xh(e),s=o?Jh:Vh(t),c=u?Jh:Vh(e);s=s==Zh?Ba:s,c=c==Zh?Ba:c;var l=s==Ba,f=c==Ba,h=s==c;if(h&&Yh(t)){if(!Yh(e))return!1;o=!0,l=!1}if(h&&!l)return a||(a=new As),o||XE(t)?HE(t,e,r,n,i,a):GE(t,e,s,r,n,i,a);if(!(r&YE)){var p=l&&Qh.call(t,"__wrapped__"),d=f&&Qh.call(e,"__wrapped__");if(p||d){var v=p?t.value():t,y=d?e.value():e;return a||(a=new As),i(v,y,r,n,a)}}return h?(a||(a=new As),VE(t,e,r,n,i,a)):!1}var QE=JE,tT=QE,tp=Ye;function jm(t,e,r,n,i){return t===e?!0:t==null||e==null||!tp(t)&&!tp(e)?t!==t&&e!==e:tT(t,e,r,n,jm,i)}var df=jm,eT=mm,rT=df,nT=1,iT=2;function aT(t,e,r,n){var i=r.length,a=i,o=!n;if(t==null)return!a;for(t=Object(t);i--;){var u=r[i];if(o&&u[2]?u[1]!==t[u[0]]:!(u[0]in t))return!1}for(;++i<a;){u=r[i];var s=u[0],c=t[s],l=u[1];if(o&&u[2]){if(c===void 0&&!(s in t))return!1}else{var f=new eT;if(n)var h=n(c,l,s,t,e,f);if(!(h===void 0?rT(l,c,nT|iT,n,f):h))return!1}}return!0}var oT=aT,uT=fr;function sT(t){return t===t&&!uT(t)}var Mm=sT,cT=Mm,lT=Lu;function fT(t){for(var e=lT(t),r=e.length;r--;){var n=e[r],i=t[n];e[r]=[n,i,cT(i)]}return e}var hT=fT;function pT(t,e){return function(r){return r==null?!1:r[t]===e&&(e!==void 0||t in Object(r))}}var Cm=pT,dT=oT,vT=hT,yT=Cm;function mT(t){var e=vT(t);return e.length==1&&e[0][2]?yT(e[0][0],e[0][1]):function(r){return r===t||dT(r,t,e)}}var gT=mT;function bT(t,e){return t!=null&&e in Object(t)}var xT=bT,_T=Dy,wT=ff,OT=Xt,AT=hf,PT=pf,ST=_u;function $T(t,e,r){e=_T(e,t);for(var n=-1,i=e.length,a=!1;++n<i;){var o=ST(e[n]);if(!(a=t!=null&&r(t,o)))break;t=t[o]}return a||++n!=i?a:(i=t==null?0:t.length,!!i&&PT(i)&&AT(o,i)&&(OT(t)||wT(t)))}var ET=$T,TT=xT,jT=ET;function MT(t,e){return t!=null&&jT(t,e,TT)}var CT=MT,kT=df,IT=Ry,NT=CT,DT=Ul,RT=Mm,LT=Cm,BT=_u,zT=1,FT=2;function WT(t,e){return DT(t)&&RT(e)?LT(BT(t),e):function(r){var n=IT(r,t);return n===void 0&&n===e?NT(r,t):kT(e,n,zT|FT)}}var UT=WT;function KT(t){return t}var ii=KT;function qT(t){return function(e){return e==null?void 0:e[t]}}var HT=qT,GT=Yl;function VT(t){return function(e){return GT(e,t)}}var XT=VT,YT=HT,ZT=XT,JT=Ul,QT=_u;function tj(t){return JT(t)?YT(QT(t)):ZT(t)}var ej=tj,rj=gT,nj=UT,ij=ii,aj=Xt,oj=ej;function uj(t){return typeof t=="function"?t:t==null?ij:typeof t=="object"?aj(t)?nj(t[0],t[1]):rj(t):oj(t)}var $e=uj;function sj(t,e,r,n){for(var i=t.length,a=r+(n?1:-1);n?a--:++a<i;)if(e(t[a],a,t))return a;return-1}var km=sj;function cj(t){return t!==t}var lj=cj;function fj(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return-1}var hj=fj,pj=km,dj=lj,vj=hj;function yj(t,e,r){return e===e?vj(t,e,r):pj(t,dj,r)}var mj=yj,gj=mj;function bj(t,e){var r=t==null?0:t.length;return!!r&&gj(t,e,0)>-1}var xj=bj;function _j(t,e,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}var wj=_j;function Oj(){}var Aj=Oj,Ps=Em,Pj=Aj,Sj=lf,$j=1/0,Ej=Ps&&1/Sj(new Ps([,-0]))[1]==$j?function(t){return new Ps(t)}:Pj,Tj=Ej,jj=gm,Mj=xj,Cj=wj,kj=xm,Ij=Tj,Nj=lf,Dj=200;function Rj(t,e,r){var n=-1,i=Mj,a=t.length,o=!0,u=[],s=u;if(r)o=!1,i=Cj;else if(a>=Dj){var c=e?null:Ij(t);if(c)return Nj(c);o=!1,i=kj,s=new jj}else s=e?[]:u;t:for(;++n<a;){var l=t[n],f=e?e(l):l;if(l=r||l!==0?l:0,o&&f===f){for(var h=s.length;h--;)if(s[h]===f)continue t;e&&s.push(f),u.push(l)}else i(s,f,r)||(s!==u&&s.push(f),u.push(l))}return u}var Lj=Rj,Bj=$e,zj=Lj;function Fj(t,e){return t&&t.length?zj(t,Bj(e)):[]}var Wj=Fj;const ep=ft(Wj);function Im(t,e,r){return e===!0?ep(t,r):V(e)?ep(t,e):t}function bn(t){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bn(t)}var Uj=["ref"];function rp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function je(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?rp(Object(r),!0).forEach(function(n){Bu(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rp(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Kj(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function np(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Dm(n.key),n)}}function qj(t,e,r){return e&&np(t.prototype,e),r&&np(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Hj(t,e,r){return e=go(e),Gj(t,Nm()?Reflect.construct(e,r||[],go(t).constructor):e.apply(t,r))}function Gj(t,e){if(e&&(bn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Vj(t)}function Vj(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Nm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Nm=function(){return!!t})()}function go(t){return go=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},go(t)}function Xj(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&xc(t,e)}function xc(t,e){return xc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},xc(t,e)}function Bu(t,e,r){return e=Dm(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Dm(t){var e=Yj(t,"string");return bn(e)=="symbol"?e:e+""}function Yj(t,e){if(bn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function Zj(t,e){if(t==null)return{};var r=Jj(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Jj(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Qj(t){return t.value}function t2(t,e){if(A.isValidElement(t))return A.cloneElement(t,e);if(typeof t=="function")return A.createElement(t,e);e.ref;var r=Zj(e,Uj);return A.createElement(cf,r)}var ip=1,pn=function(t){function e(){var r;Kj(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Hj(this,e,[].concat(i)),Bu(r,"lastBoundingBox",{width:-1,height:-1}),r}return Xj(e,t),qj(e,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>ip||Math.abs(i.height-this.lastBoundingBox.height)>ip)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?je({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,s=i.margin,c=i.chartWidth,l=i.chartHeight,f,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var p=this.getBBoxSnapshot();f={left:((c||0)-p.width)/2}}else f=o==="right"?{right:s&&s.right||0}:{left:s&&s.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var d=this.getBBoxSnapshot();h={top:((l||0)-d.height)/2}}else h=u==="bottom"?{bottom:s&&s.bottom||0}:{top:s&&s.top||0};return je(je({},f),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,s=i.wrapperStyle,c=i.payloadUniqBy,l=i.payload,f=je(je({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(s)),s);return A.createElement("div",{className:"recharts-legend-wrapper",style:f,ref:function(p){n.wrapperNode=p}},t2(a,je(je({},this.props),{},{payload:Im(l,c,Qj)})))}}],[{key:"getWithHeight",value:function(n,i){var a=je(je({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&F(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(W.PureComponent);Bu(pn,"displayName","Legend");Bu(pn,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ap=_a,e2=ff,r2=Xt,op=ap?ap.isConcatSpreadable:void 0;function n2(t){return r2(t)||e2(t)||!!(op&&t&&t[op])}var i2=n2,a2=wm,o2=i2;function Rm(t,e,r,n,i){var a=-1,o=t.length;for(r||(r=o2),i||(i=[]);++a<o;){var u=t[a];e>0&&r(u)?e>1?Rm(u,e-1,r,n,i):a2(i,u):n||(i[i.length]=u)}return i}var Lm=Rm;function u2(t){return function(e,r,n){for(var i=-1,a=Object(e),o=n(e),u=o.length;u--;){var s=o[t?u:++i];if(r(a[s],s,a)===!1)break}return e}}var s2=u2,c2=s2,l2=c2(),f2=l2,h2=f2,p2=Lu;function d2(t,e){return t&&h2(t,e,p2)}var Bm=d2,v2=Oa;function y2(t,e){return function(r,n){if(r==null)return r;if(!v2(r))return t(r,n);for(var i=r.length,a=e?i:-1,o=Object(r);(e?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var m2=y2,g2=Bm,b2=m2,x2=b2(g2),vf=x2,_2=vf,w2=Oa;function O2(t,e){var r=-1,n=w2(t)?Array(t.length):[];return _2(t,function(i,a,o){n[++r]=e(i,a,o)}),n}var zm=O2;function A2(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}var P2=A2,up=Yn;function S2(t,e){if(t!==e){var r=t!==void 0,n=t===null,i=t===t,a=up(t),o=e!==void 0,u=e===null,s=e===e,c=up(e);if(!u&&!c&&!a&&t>e||a&&o&&s&&!u&&!c||n&&o&&s||!r&&s||!i)return 1;if(!n&&!a&&!c&&t<e||c&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!s)return-1}return 0}var $2=S2,E2=$2;function T2(t,e,r){for(var n=-1,i=t.criteria,a=e.criteria,o=i.length,u=r.length;++n<o;){var s=E2(i[n],a[n]);if(s){if(n>=u)return s;var c=r[n];return s*(c=="desc"?-1:1)}}return t.index-e.index}var j2=T2,Ss=Xl,M2=Yl,C2=$e,k2=zm,I2=P2,N2=Pm,D2=j2,R2=ii,L2=Xt;function B2(t,e,r){e.length?e=Ss(e,function(a){return L2(a)?function(o){return M2(o,a.length===1?a[0]:a)}:a}):e=[R2];var n=-1;e=Ss(e,N2(C2));var i=k2(t,function(a,o,u){var s=Ss(e,function(c){return c(a)});return{criteria:s,index:++n,value:a}});return I2(i,function(a,o){return D2(a,o,r)})}var z2=B2;function F2(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}var W2=F2,U2=W2,sp=Math.max;function K2(t,e,r){return e=sp(e===void 0?t.length-1:e,0),function(){for(var n=arguments,i=-1,a=sp(n.length-e,0),o=Array(a);++i<a;)o[i]=n[e+i];i=-1;for(var u=Array(e+1);++i<e;)u[i]=n[i];return u[e]=r(o),U2(t,this,u)}}var q2=K2;function H2(t){return function(){return t}}var G2=H2,V2=Hr,X2=function(){try{var t=V2(Object,"defineProperty");return t({},"",{}),t}catch{}}(),Fm=X2,Y2=G2,cp=Fm,Z2=ii,J2=cp?function(t,e){return cp(t,"toString",{configurable:!0,enumerable:!1,value:Y2(e),writable:!0})}:Z2,Q2=J2,tM=800,eM=16,rM=Date.now;function nM(t){var e=0,r=0;return function(){var n=rM(),i=eM-(n-r);if(r=n,i>0){if(++e>=tM)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}var iM=nM,aM=Q2,oM=iM,uM=oM(aM),sM=uM,cM=ii,lM=q2,fM=sM;function hM(t,e){return fM(lM(t,e,cM),t+"")}var pM=hM,dM=ql,vM=Oa,yM=hf,mM=fr;function gM(t,e,r){if(!mM(r))return!1;var n=typeof e;return(n=="number"?vM(r)&&yM(e,r.length):n=="string"&&e in r)?dM(r[e],t):!1}var zu=gM,bM=Lm,xM=z2,_M=pM,lp=zu,wM=_M(function(t,e){if(t==null)return[];var r=e.length;return r>1&&lp(t,e[0],e[1])?e=[]:r>2&&lp(e[0],e[1],e[2])&&(e=[e[0]]),xM(t,bM(e,1),[])}),OM=wM;const yf=ft(OM);function Li(t){"@babel/helpers - typeof";return Li=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Li(t)}function _c(){return _c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_c.apply(this,arguments)}function AM(t,e){return EM(t)||$M(t,e)||SM(t,e)||PM()}function PM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SM(t,e){if(t){if(typeof t=="string")return fp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fp(t,e)}}function fp(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function $M(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function EM(t){if(Array.isArray(t))return t}function hp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function $s(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?hp(Object(r),!0).forEach(function(n){TM(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hp(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function TM(t,e,r){return e=jM(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jM(t){var e=MM(t,"string");return Li(e)=="symbol"?e:e+""}function MM(t,e){if(Li(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Li(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function CM(t){return Array.isArray(t)&&Et(t[0])&&Et(t[1])?t.join(" ~ "):t}var kM=function(e){var r=e.separator,n=r===void 0?" : ":r,i=e.contentStyle,a=i===void 0?{}:i,o=e.itemStyle,u=o===void 0?{}:o,s=e.labelStyle,c=s===void 0?{}:s,l=e.payload,f=e.formatter,h=e.itemSorter,p=e.wrapperClassName,d=e.labelClassName,v=e.label,y=e.labelFormatter,g=e.accessibilityLayer,b=g===void 0?!1:g,_=function(){if(l&&l.length){var M={padding:0,margin:0},C=(h?yf(l,h):l).map(function(P,j){if(P.type==="none")return null;var E=$s({display:"block",paddingTop:4,paddingBottom:4,color:P.color||"#000"},u),I=P.formatter||f||CM,N=P.value,R=P.name,B=N,U=R;if(I&&B!=null&&U!=null){var z=I(N,R,P,j,l);if(Array.isArray(z)){var G=AM(z,2);B=G[0],U=G[1]}else B=z}return A.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(j),style:E},Et(U)?A.createElement("span",{className:"recharts-tooltip-item-name"},U):null,Et(U)?A.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,A.createElement("span",{className:"recharts-tooltip-item-value"},B),A.createElement("span",{className:"recharts-tooltip-item-unit"},P.unit||""))});return A.createElement("ul",{className:"recharts-tooltip-item-list",style:M},C)}return null},w=$s({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=$s({margin:0},c),x=!X(v),O=x?v:"",S=J("recharts-default-tooltip",p),$=J("recharts-tooltip-label",d);x&&y&&l!==void 0&&l!==null&&(O=y(v,l));var k=b?{role:"status","aria-live":"assertive"}:{};return A.createElement("div",_c({className:S,style:w},k),A.createElement("p",{className:$,style:m},A.isValidElement(O)?O:"".concat(O)),_())};function Bi(t){"@babel/helpers - typeof";return Bi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bi(t)}function za(t,e,r){return e=IM(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function IM(t){var e=NM(t,"string");return Bi(e)=="symbol"?e:e+""}function NM(t,e){if(Bi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Bi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var hi="recharts-tooltip-wrapper",DM={visibility:"hidden"};function RM(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return J(hi,za(za(za(za({},"".concat(hi,"-right"),F(r)&&e&&F(e.x)&&r>=e.x),"".concat(hi,"-left"),F(r)&&e&&F(e.x)&&r<e.x),"".concat(hi,"-bottom"),F(n)&&e&&F(e.y)&&n>=e.y),"".concat(hi,"-top"),F(n)&&e&&F(e.y)&&n<e.y))}function pp(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,a=t.position,o=t.reverseDirection,u=t.tooltipDimension,s=t.viewBox,c=t.viewBoxDimension;if(a&&F(a[n]))return a[n];var l=r[n]-u-i,f=r[n]+i;if(e[n])return o[n]?l:f;if(o[n]){var h=l,p=s[n];return h<p?Math.max(f,s[n]):Math.max(l,s[n])}var d=f+u,v=s[n]+c;return d>v?Math.max(l,s[n]):Math.max(f,s[n])}function LM(t){var e=t.translateX,r=t.translateY,n=t.useTranslate3d;return{transform:n?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}function BM(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.offsetTopLeft,i=t.position,a=t.reverseDirection,o=t.tooltipBox,u=t.useTranslate3d,s=t.viewBox,c,l,f;return o.height>0&&o.width>0&&r?(l=pp({allowEscapeViewBox:e,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:s,viewBoxDimension:s.width}),f=pp({allowEscapeViewBox:e,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:s,viewBoxDimension:s.height}),c=LM({translateX:l,translateY:f,useTranslate3d:u})):c=DM,{cssProperties:c,cssClasses:RM({translateX:l,translateY:f,coordinate:r})}}function xn(t){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xn(t)}function dp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function vp(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?dp(Object(r),!0).forEach(function(n){Oc(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dp(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function zM(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function FM(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Um(n.key),n)}}function WM(t,e,r){return e&&FM(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function UM(t,e,r){return e=bo(e),KM(t,Wm()?Reflect.construct(e,r||[],bo(t).constructor):e.apply(t,r))}function KM(t,e){if(e&&(xn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return qM(t)}function qM(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Wm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Wm=function(){return!!t})()}function bo(t){return bo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},bo(t)}function HM(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wc(t,e)}function wc(t,e){return wc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},wc(t,e)}function Oc(t,e,r){return e=Um(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Um(t){var e=GM(t,"string");return xn(e)=="symbol"?e:e+""}function GM(t,e){if(xn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var yp=1,VM=function(t){function e(){var r;zM(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=UM(this,e,[].concat(i)),Oc(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Oc(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,s,c,l;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(s=r.props.coordinate)===null||s===void 0?void 0:s.x)!==null&&u!==void 0?u:0,y:(c=(l=r.props.coordinate)===null||l===void 0?void 0:l.y)!==null&&c!==void 0?c:0}})}}),r}return HM(e,t),WM(e,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>yp||Math.abs(n.height-this.state.lastBoundingBox.height)>yp)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,s=i.animationEasing,c=i.children,l=i.coordinate,f=i.hasPayload,h=i.isAnimationActive,p=i.offset,d=i.position,v=i.reverseDirection,y=i.useTranslate3d,g=i.viewBox,b=i.wrapperStyle,_=BM({allowEscapeViewBox:o,coordinate:l,offsetTopLeft:p,position:d,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:g}),w=_.cssClasses,m=_.cssProperties,x=vp(vp({transition:h&&a?"transform ".concat(u,"ms ").concat(s):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&f?"visible":"hidden",position:"absolute",top:0,left:0},b);return A.createElement("div",{tabIndex:-1,className:w,style:x,ref:function(S){n.wrapperNode=S}},c)}}])}(W.PureComponent),XM=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Ee={isSsr:XM()};function _n(t){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_n(t)}function mp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function gp(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?mp(Object(r),!0).forEach(function(n){mf(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mp(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function YM(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ZM(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,qm(n.key),n)}}function JM(t,e,r){return e&&ZM(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function QM(t,e,r){return e=xo(e),tC(t,Km()?Reflect.construct(e,r||[],xo(t).constructor):e.apply(t,r))}function tC(t,e){if(e&&(_n(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return eC(t)}function eC(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Km(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Km=function(){return!!t})()}function xo(t){return xo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},xo(t)}function rC(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ac(t,e)}function Ac(t,e){return Ac=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ac(t,e)}function mf(t,e,r){return e=qm(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function qm(t){var e=nC(t,"string");return _n(e)=="symbol"?e:e+""}function nC(t,e){if(_n(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(_n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function iC(t){return t.dataKey}function aC(t,e){return A.isValidElement(t)?A.cloneElement(t,e):typeof t=="function"?A.createElement(t,e):A.createElement(kM,e)}var ke=function(t){function e(){return YM(this,e),QM(this,e,arguments)}return rC(e,t),JM(e,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,s=i.animationEasing,c=i.content,l=i.coordinate,f=i.filterNull,h=i.isAnimationActive,p=i.offset,d=i.payload,v=i.payloadUniqBy,y=i.position,g=i.reverseDirection,b=i.useTranslate3d,_=i.viewBox,w=i.wrapperStyle,m=d??[];f&&m.length&&(m=Im(d.filter(function(O){return O.value!=null&&(O.hide!==!0||n.props.includeHidden)}),v,iC));var x=m.length>0;return A.createElement(VM,{allowEscapeViewBox:o,animationDuration:u,animationEasing:s,isAnimationActive:h,active:a,coordinate:l,hasPayload:x,offset:p,position:y,reverseDirection:g,useTranslate3d:b,viewBox:_,wrapperStyle:w},aC(c,gp(gp({},this.props),{},{payload:m})))}}])}(W.PureComponent);mf(ke,"displayName","Tooltip");mf(ke,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Ee.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var oC=Se,uC=function(){return oC.Date.now()},sC=uC,cC=/\s/;function lC(t){for(var e=t.length;e--&&cC.test(t.charAt(e)););return e}var fC=lC,hC=fC,pC=/^\s+/;function dC(t){return t&&t.slice(0,hC(t)+1).replace(pC,"")}var vC=dC,yC=vC,bp=fr,mC=Yn,xp=NaN,gC=/^[-+]0x[0-9a-f]+$/i,bC=/^0b[01]+$/i,xC=/^0o[0-7]+$/i,_C=parseInt;function wC(t){if(typeof t=="number")return t;if(mC(t))return xp;if(bp(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=bp(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=yC(t);var r=bC.test(t);return r||xC.test(t)?_C(t.slice(2),r?2:8):gC.test(t)?xp:+t}var Hm=wC,OC=fr,Es=sC,_p=Hm,AC="Expected a function",PC=Math.max,SC=Math.min;function $C(t,e,r){var n,i,a,o,u,s,c=0,l=!1,f=!1,h=!0;if(typeof t!="function")throw new TypeError(AC);e=_p(e)||0,OC(r)&&(l=!!r.leading,f="maxWait"in r,a=f?PC(_p(r.maxWait)||0,e):a,h="trailing"in r?!!r.trailing:h);function p(x){var O=n,S=i;return n=i=void 0,c=x,o=t.apply(S,O),o}function d(x){return c=x,u=setTimeout(g,e),l?p(x):o}function v(x){var O=x-s,S=x-c,$=e-O;return f?SC($,a-S):$}function y(x){var O=x-s,S=x-c;return s===void 0||O>=e||O<0||f&&S>=a}function g(){var x=Es();if(y(x))return b(x);u=setTimeout(g,v(x))}function b(x){return u=void 0,h&&n?p(x):(n=i=void 0,o)}function _(){u!==void 0&&clearTimeout(u),c=0,n=s=i=u=void 0}function w(){return u===void 0?o:b(Es())}function m(){var x=Es(),O=y(x);if(n=arguments,i=this,s=x,O){if(u===void 0)return d(s);if(f)return clearTimeout(u),u=setTimeout(g,e),p(s)}return u===void 0&&(u=setTimeout(g,e)),o}return m.cancel=_,m.flush=w,m}var EC=$C,TC=EC,jC=fr,MC="Expected a function";function CC(t,e,r){var n=!0,i=!0;if(typeof t!="function")throw new TypeError(MC);return jC(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),TC(t,e,{leading:n,maxWait:e,trailing:i})}var kC=CC;const Gm=ft(kC);function zi(t){"@babel/helpers - typeof";return zi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zi(t)}function wp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Fa(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?wp(Object(r),!0).forEach(function(n){IC(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wp(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function IC(t,e,r){return e=NC(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function NC(t){var e=DC(t,"string");return zi(e)=="symbol"?e:e+""}function DC(t,e){if(zi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(zi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function RC(t,e){return FC(t)||zC(t,e)||BC(t,e)||LC()}function LC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function BC(t,e){if(t){if(typeof t=="string")return Op(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Op(t,e)}}function Op(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function zC(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function FC(t){if(Array.isArray(t))return t}var uG=W.forwardRef(function(t,e){var r=t.aspect,n=t.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=t.width,o=a===void 0?"100%":a,u=t.height,s=u===void 0?"100%":u,c=t.minWidth,l=c===void 0?0:c,f=t.minHeight,h=t.maxHeight,p=t.children,d=t.debounce,v=d===void 0?0:d,y=t.id,g=t.className,b=t.onResize,_=t.style,w=_===void 0?{}:_,m=W.useRef(null),x=W.useRef();x.current=b,W.useImperativeHandle(e,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var O=W.useState({containerWidth:i.width,containerHeight:i.height}),S=RC(O,2),$=S[0],k=S[1],T=W.useCallback(function(C,P){k(function(j){var E=Math.round(C),I=Math.round(P);return j.containerWidth===E&&j.containerHeight===I?j:{containerWidth:E,containerHeight:I}})},[]);W.useEffect(function(){var C=function(R){var B,U=R[0].contentRect,z=U.width,G=U.height;T(z,G),(B=x.current)===null||B===void 0||B.call(x,z,G)};v>0&&(C=Gm(C,v,{trailing:!0,leading:!1}));var P=new ResizeObserver(C),j=m.current.getBoundingClientRect(),E=j.width,I=j.height;return T(E,I),P.observe(m.current),function(){P.disconnect()}},[T,v]);var M=W.useMemo(function(){var C=$.containerWidth,P=$.containerHeight;if(C<0||P<0)return null;ge(jr(o)||jr(s),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,s),ge(!r||r>0,"The aspect(%s) must be greater than zero.",r);var j=jr(o)?C:o,E=jr(s)?P:s;r&&r>0&&(j?E=j/r:E&&(j=E*r),h&&E>h&&(E=h)),ge(j>0||E>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,j,E,o,s,l,f,r);var I=!Array.isArray(p)&&Fe(p.type).endsWith("Chart");return A.Children.map(p,function(N){return A.isValidElement(N)?W.cloneElement(N,Fa({width:j,height:E},I?{style:Fa({height:"100%",width:"100%",maxHeight:E,maxWidth:j},N.props.style)}:{})):N})},[r,p,s,h,f,l,$,o]);return A.createElement("div",{id:y?"".concat(y):void 0,className:J("recharts-responsive-container",g),style:Fa(Fa({},w),{},{width:o,height:s,minWidth:l,minHeight:f,maxHeight:h}),ref:m},M)}),Fu=function(e){return null};Fu.displayName="Cell";function Fi(t){"@babel/helpers - typeof";return Fi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fi(t)}function Ap(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Pc(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ap(Object(r),!0).forEach(function(n){WC(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ap(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function WC(t,e,r){return e=UC(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function UC(t){var e=KC(t,"string");return Fi(e)=="symbol"?e:e+""}function KC(t,e){if(Fi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Fi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var rn={widthCache:{},cacheCount:0},qC=2e3,HC={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Pp="recharts_measurement_span";function GC(t){var e=Pc({},t);return Object.keys(e).forEach(function(r){e[r]||delete e[r]}),e}var $i=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(e==null||Ee.isSsr)return{width:0,height:0};var n=GC(r),i=JSON.stringify({text:e,copyStyle:n});if(rn.widthCache[i])return rn.widthCache[i];try{var a=document.getElementById(Pp);a||(a=document.createElement("span"),a.setAttribute("id",Pp),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Pc(Pc({},HC),n);Object.assign(a.style,o),a.textContent="".concat(e);var u=a.getBoundingClientRect(),s={width:u.width,height:u.height};return rn.widthCache[i]=s,++rn.cacheCount>qC&&(rn.cacheCount=0,rn.widthCache={}),s}catch{return{width:0,height:0}}},VC=function(e){return{top:e.top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft}};function Wi(t){"@babel/helpers - typeof";return Wi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wi(t)}function _o(t,e){return JC(t)||ZC(t,e)||YC(t,e)||XC()}function XC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function YC(t,e){if(t){if(typeof t=="string")return Sp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sp(t,e)}}function Sp(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ZC(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function JC(t){if(Array.isArray(t))return t}function QC(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function $p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ek(n.key),n)}}function tk(t,e,r){return e&&$p(t.prototype,e),r&&$p(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function ek(t){var e=rk(t,"string");return Wi(e)=="symbol"?e:e+""}function rk(t,e){if(Wi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Wi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ep=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Tp=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nk=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,ik=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Vm={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},ak=Object.keys(Vm),un="NaN";function ok(t,e){return t*Vm[e]}var Wa=function(){function t(e,r){QC(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),r!==""&&!nk.test(r)&&(this.num=NaN,this.unit=""),ak.includes(r)&&(this.num=ok(e,r),this.unit="px")}return tk(t,[{key:"add",value:function(r){return this.unit!==r.unit?new t(NaN,""):new t(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new t(NaN,""):new t(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new t(NaN,""):new t(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new t(NaN,""):new t(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=ik.exec(r))!==null&&n!==void 0?n:[],a=_o(i,3),o=a[1],u=a[2];return new t(parseFloat(o),u??"")}}])}();function Xm(t){if(t.includes(un))return un;for(var e=t;e.includes("*")||e.includes("/");){var r,n=(r=Ep.exec(e))!==null&&r!==void 0?r:[],i=_o(n,4),a=i[1],o=i[2],u=i[3],s=Wa.parse(a??""),c=Wa.parse(u??""),l=o==="*"?s.multiply(c):s.divide(c);if(l.isNaN())return un;e=e.replace(Ep,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var f,h=(f=Tp.exec(e))!==null&&f!==void 0?f:[],p=_o(h,4),d=p[1],v=p[2],y=p[3],g=Wa.parse(d??""),b=Wa.parse(y??""),_=v==="+"?g.add(b):g.subtract(b);if(_.isNaN())return un;e=e.replace(Tp,_.toString())}return e}var jp=/\(([^()]*)\)/;function uk(t){for(var e=t;e.includes("(");){var r=jp.exec(e),n=_o(r,2),i=n[1];e=e.replace(jp,Xm(i))}return e}function sk(t){var e=t.replace(/\s+/g,"");return e=uk(e),e=Xm(e),e}function ck(t){try{return sk(t)}catch{return un}}function Ts(t){var e=ck(t.slice(5,-1));return e===un?"":e}var lk=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],fk=["dx","dy","angle","className","breakAll"];function Sc(){return Sc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Sc.apply(this,arguments)}function Mp(t,e){if(t==null)return{};var r=hk(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function hk(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Cp(t,e){return yk(t)||vk(t,e)||dk(t,e)||pk()}function pk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dk(t,e){if(t){if(typeof t=="string")return kp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kp(t,e)}}function kp(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function vk(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function yk(t){if(Array.isArray(t))return t}var Ym=/[ \f\n\r\t\v\u2028\u2029]+/,Zm=function(e){var r=e.children,n=e.breakAll,i=e.style;try{var a=[];X(r)||(n?a=r.toString().split(""):a=r.toString().split(Ym));var o=a.map(function(s){return{word:s,width:$i(s,i).width}}),u=n?0:$i(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},mk=function(e,r,n,i,a){var o=e.maxLines,u=e.children,s=e.style,c=e.breakAll,l=F(o),f=u,h=function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return j.reduce(function(E,I){var N=I.word,R=I.width,B=E[E.length-1];if(B&&(i==null||a||B.width+R+n<Number(i)))B.words.push(N),B.width+=R+n;else{var U={words:[N],width:R};E.push(U)}return E},[])},p=h(r),d=function(j){return j.reduce(function(E,I){return E.width>I.width?E:I})};if(!l)return p;for(var v="…",y=function(j){var E=f.slice(0,j),I=Zm({breakAll:c,style:s,children:E+v}).wordsWithComputedWidth,N=h(I),R=N.length>o||d(N).width>Number(i);return[R,N]},g=0,b=f.length-1,_=0,w;g<=b&&_<=f.length-1;){var m=Math.floor((g+b)/2),x=m-1,O=y(x),S=Cp(O,2),$=S[0],k=S[1],T=y(m),M=Cp(T,1),C=M[0];if(!$&&!C&&(g=m+1),$&&C&&(b=m-1),!$&&C){w=k;break}_++}return w||p},Ip=function(e){var r=X(e)?[]:e.toString().split(Ym);return[{words:r}]},gk=function(e){var r=e.width,n=e.scaleToFit,i=e.children,a=e.style,o=e.breakAll,u=e.maxLines;if((r||n)&&!Ee.isSsr){var s,c,l=Zm({breakAll:o,children:i,style:a});if(l){var f=l.wordsWithComputedWidth,h=l.spaceWidth;s=f,c=h}else return Ip(i);return mk({breakAll:o,children:i,maxLines:u,style:a},s,c,r,n)}return Ip(i)},Np="#808080",Br=function(e){var r=e.x,n=r===void 0?0:r,i=e.y,a=i===void 0?0:i,o=e.lineHeight,u=o===void 0?"1em":o,s=e.capHeight,c=s===void 0?"0.71em":s,l=e.scaleToFit,f=l===void 0?!1:l,h=e.textAnchor,p=h===void 0?"start":h,d=e.verticalAnchor,v=d===void 0?"end":d,y=e.fill,g=y===void 0?Np:y,b=Mp(e,lk),_=W.useMemo(function(){return gk({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:f,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,f,b.style,b.width]),w=b.dx,m=b.dy,x=b.angle,O=b.className,S=b.breakAll,$=Mp(b,fk);if(!Et(n)||!Et(a))return null;var k=n+(F(w)?w:0),T=a+(F(m)?m:0),M;switch(v){case"start":M=Ts("calc(".concat(c,")"));break;case"middle":M=Ts("calc(".concat((_.length-1)/2," * -").concat(u," + (").concat(c," / 2))"));break;default:M=Ts("calc(".concat(_.length-1," * -").concat(u,")"));break}var C=[];if(f){var P=_[0].width,j=b.width;C.push("scale(".concat((F(j)?j/P:1)/P,")"))}return x&&C.push("rotate(".concat(x,", ").concat(k,", ").concat(T,")")),C.length&&($.transform=C.join(" ")),A.createElement("text",Sc({},q($,!0),{x:k,y:T,className:J("recharts-text",O),textAnchor:p,fill:g.includes("url")?Np:g}),_.map(function(E,I){var N=E.words.join(S?"":" ");return A.createElement("tspan",{x:k,dy:I===0?M:u,key:"".concat(N,"-").concat(I)},N)}))};function or(t,e){return t==null||e==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function bk(t,e){return t==null||e==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function gf(t){let e,r,n;t.length!==2?(e=or,r=(u,s)=>or(t(u),s),n=(u,s)=>t(u)-s):(e=t===or||t===bk?t:xk,r=t,n=t);function i(u,s,c=0,l=u.length){if(c<l){if(e(s,s)!==0)return l;do{const f=c+l>>>1;r(u[f],s)<0?c=f+1:l=f}while(c<l)}return c}function a(u,s,c=0,l=u.length){if(c<l){if(e(s,s)!==0)return l;do{const f=c+l>>>1;r(u[f],s)<=0?c=f+1:l=f}while(c<l)}return c}function o(u,s,c=0,l=u.length){const f=i(u,s,c,l-1);return f>c&&n(u[f-1],s)>-n(u[f],s)?f-1:f}return{left:i,center:o,right:a}}function xk(){return 0}function Jm(t){return t===null?NaN:+t}function*_k(t,e){for(let r of t)r!=null&&(r=+r)>=r&&(yield r)}const wk=gf(or),Aa=wk.right;gf(Jm).center;function sG(t,e){let r,n;for(const i of t)i!=null&&(r===void 0?i>=i&&(r=n=i):(r>i&&(r=i),n<i&&(n=i)));return[r,n]}class Dp extends Map{constructor(e,r=Pk){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),e!=null)for(const[n,i]of e)this.set(n,i)}get(e){return super.get(Rp(this,e))}has(e){return super.has(Rp(this,e))}set(e,r){return super.set(Ok(this,e),r)}delete(e){return super.delete(Ak(this,e))}}function Rp({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):r}function Ok({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}function Ak({_intern:t,_key:e},r){const n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}function Pk(t){return t!==null&&typeof t=="object"?t.valueOf():t}function Sk(t=or){if(t===or)return Qm;if(typeof t!="function")throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||n===0?n:(t(r,r)===0)-(t(e,e)===0)}}function Qm(t,e){return(t==null||!(t>=t))-(e==null||!(e>=e))||(t<e?-1:t>e?1:0)}const $k=Math.sqrt(50),Ek=Math.sqrt(10),Tk=Math.sqrt(2);function wo(t,e,r){const n=(e-t)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=$k?10:a>=Ek?5:a>=Tk?2:1;let u,s,c;return i<0?(c=Math.pow(10,-i)/o,u=Math.round(t*c),s=Math.round(e*c),u/c<t&&++u,s/c>e&&--s,c=-c):(c=Math.pow(10,i)*o,u=Math.round(t/c),s=Math.round(e/c),u*c<t&&++u,s*c>e&&--s),s<u&&.5<=r&&r<2?wo(t,e,r*2):[u,s,c]}function $c(t,e,r){if(e=+e,t=+t,r=+r,!(r>0))return[];if(t===e)return[t];const n=e<t,[i,a,o]=n?wo(e,t,r):wo(t,e,r);if(!(a>=i))return[];const u=a-i+1,s=new Array(u);if(n)if(o<0)for(let c=0;c<u;++c)s[c]=(a-c)/-o;else for(let c=0;c<u;++c)s[c]=(a-c)*o;else if(o<0)for(let c=0;c<u;++c)s[c]=(i+c)/-o;else for(let c=0;c<u;++c)s[c]=(i+c)*o;return s}function Ec(t,e,r){return e=+e,t=+t,r=+r,wo(t,e,r)[2]}function Tc(t,e,r){e=+e,t=+t,r=+r;const n=e<t,i=n?Ec(e,t,r):Ec(t,e,r);return(n?-1:1)*(i<0?1/-i:i)}function Lp(t,e){let r;if(e===void 0)for(const n of t)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of t)(i=e(i,++n,t))!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}return r}function Bp(t,e){let r;if(e===void 0)for(const n of t)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of t)(i=e(i,++n,t))!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}return r}function tg(t,e,r=0,n=1/0,i){if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(i=i===void 0?Qm:Sk(i);n>r;){if(n-r>600){const s=n-r+1,c=e-r+1,l=Math.log(s),f=.5*Math.exp(2*l/3),h=.5*Math.sqrt(l*f*(s-f)/s)*(c-s/2<0?-1:1),p=Math.max(r,Math.floor(e-c*f/s+h)),d=Math.min(n,Math.floor(e+(s-c)*f/s+h));tg(t,e,p,d,i)}const a=t[e];let o=r,u=n;for(pi(t,r,e),i(t[n],a)>0&&pi(t,r,n);o<u;){for(pi(t,o,u),++o,--u;i(t[o],a)<0;)++o;for(;i(t[u],a)>0;)--u}i(t[r],a)===0?pi(t,r,u):(++u,pi(t,u,n)),u<=e&&(r=u+1),e<=u&&(n=u-1)}return t}function pi(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function jk(t,e,r){if(t=Float64Array.from(_k(t)),!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return Bp(t);if(e>=1)return Lp(t);var n,i=(n-1)*e,a=Math.floor(i),o=Lp(tg(t,a).subarray(0,a+1)),u=Bp(t.subarray(a+1));return o+(u-o)*(i-a)}}function Mk(t,e,r=Jm){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,a=Math.floor(i),o=+r(t[a],a,t),u=+r(t[a+1],a+1,t);return o+(u-o)*(i-a)}}function Ck(t,e,r){t=+t,e=+e,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((e-t)/r))|0,a=new Array(i);++n<i;)a[n]=t+n*r;return a}function he(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t);break}return this}function Ze(t,e){switch(arguments.length){case 0:break;case 1:{typeof t=="function"?this.interpolator(t):this.range(t);break}default:{this.domain(t),typeof e=="function"?this.interpolator(e):this.range(e);break}}return this}const jc=Symbol("implicit");function bf(){var t=new Dp,e=[],r=[],n=jc;function i(a){let o=t.get(a);if(o===void 0){if(n!==jc)return n;t.set(a,o=e.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return e.slice();e=[],t=new Dp;for(const o of a)t.has(o)||t.set(o,e.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return bf(e,r).unknown(n)},he.apply(i,arguments),i}function Ui(){var t=bf().unknown(void 0),e=t.domain,r=t.range,n=0,i=1,a,o,u=!1,s=0,c=0,l=.5;delete t.unknown;function f(){var h=e().length,p=i<n,d=p?i:n,v=p?n:i;a=(v-d)/Math.max(1,h-s+c*2),u&&(a=Math.floor(a)),d+=(v-d-a*(h-s))*l,o=a*(1-s),u&&(d=Math.round(d),o=Math.round(o));var y=Ck(h).map(function(g){return d+a*g});return r(p?y.reverse():y)}return t.domain=function(h){return arguments.length?(e(h),f()):e()},t.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,f()):[n,i]},t.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,f()},t.bandwidth=function(){return o},t.step=function(){return a},t.round=function(h){return arguments.length?(u=!!h,f()):u},t.padding=function(h){return arguments.length?(s=Math.min(1,c=+h),f()):s},t.paddingInner=function(h){return arguments.length?(s=Math.min(1,h),f()):s},t.paddingOuter=function(h){return arguments.length?(c=+h,f()):c},t.align=function(h){return arguments.length?(l=Math.max(0,Math.min(1,h)),f()):l},t.copy=function(){return Ui(e(),[n,i]).round(u).paddingInner(s).paddingOuter(c).align(l)},he.apply(f(),arguments)}function eg(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return eg(e())},t}function Ei(){return eg(Ui.apply(null,arguments).paddingInner(1))}function ai(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function Pa(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function pr(){}var zr=.7,wn=1/zr,dn="\\s*([+-]?\\d+)\\s*",Ki="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",we="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",kk=/^#([0-9a-f]{3,8})$/,Ik=new RegExp(`^rgb\\(${dn},${dn},${dn}\\)$`),Nk=new RegExp(`^rgb\\(${we},${we},${we}\\)$`),Dk=new RegExp(`^rgba\\(${dn},${dn},${dn},${Ki}\\)$`),Rk=new RegExp(`^rgba\\(${we},${we},${we},${Ki}\\)$`),Lk=new RegExp(`^hsl\\(${Ki},${we},${we}\\)$`),Bk=new RegExp(`^hsla\\(${Ki},${we},${we},${Ki}\\)$`),zp={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};ai(pr,Fr,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Fp,formatHex:Fp,formatHex8:zk,formatHsl:Fk,formatRgb:Wp,toString:Wp});function Fp(){return this.rgb().formatHex()}function zk(){return this.rgb().formatHex8()}function Fk(){return rg(this).formatHsl()}function Wp(){return this.rgb().formatRgb()}function Fr(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=kk.exec(t))?(r=e[1].length,e=parseInt(e[1],16),r===6?Up(e):r===3?new Ct(e>>8&15|e>>4&240,e>>4&15|e&240,(e&15)<<4|e&15,1):r===8?Ua(e>>24&255,e>>16&255,e>>8&255,(e&255)/255):r===4?Ua(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|e&240,((e&15)<<4|e&15)/255):null):(e=Ik.exec(t))?new Ct(e[1],e[2],e[3],1):(e=Nk.exec(t))?new Ct(e[1]*255/100,e[2]*255/100,e[3]*255/100,1):(e=Dk.exec(t))?Ua(e[1],e[2],e[3],e[4]):(e=Rk.exec(t))?Ua(e[1]*255/100,e[2]*255/100,e[3]*255/100,e[4]):(e=Lk.exec(t))?Hp(e[1],e[2]/100,e[3]/100,1):(e=Bk.exec(t))?Hp(e[1],e[2]/100,e[3]/100,e[4]):zp.hasOwnProperty(t)?Up(zp[t]):t==="transparent"?new Ct(NaN,NaN,NaN,0):null}function Up(t){return new Ct(t>>16&255,t>>8&255,t&255,1)}function Ua(t,e,r,n){return n<=0&&(t=e=r=NaN),new Ct(t,e,r,n)}function xf(t){return t instanceof pr||(t=Fr(t)),t?(t=t.rgb(),new Ct(t.r,t.g,t.b,t.opacity)):new Ct}function Oo(t,e,r,n){return arguments.length===1?xf(t):new Ct(t,e,r,n??1)}function Ct(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}ai(Ct,Oo,Pa(pr,{brighter(t){return t=t==null?wn:Math.pow(wn,t),new Ct(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=t==null?zr:Math.pow(zr,t),new Ct(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Ct(Nr(this.r),Nr(this.g),Nr(this.b),Ao(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Kp,formatHex:Kp,formatHex8:Wk,formatRgb:qp,toString:qp}));function Kp(){return`#${Mr(this.r)}${Mr(this.g)}${Mr(this.b)}`}function Wk(){return`#${Mr(this.r)}${Mr(this.g)}${Mr(this.b)}${Mr((isNaN(this.opacity)?1:this.opacity)*255)}`}function qp(){const t=Ao(this.opacity);return`${t===1?"rgb(":"rgba("}${Nr(this.r)}, ${Nr(this.g)}, ${Nr(this.b)}${t===1?")":`, ${t})`}`}function Ao(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Nr(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Mr(t){return t=Nr(t),(t<16?"0":"")+t.toString(16)}function Hp(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new me(t,e,r,n)}function rg(t){if(t instanceof me)return new me(t.h,t.s,t.l,t.opacity);if(t instanceof pr||(t=Fr(t)),!t)return new me;if(t instanceof me)return t;t=t.rgb();var e=t.r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),a=Math.max(e,r,n),o=NaN,u=a-i,s=(a+i)/2;return u?(e===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-e)/u+2:o=(e-r)/u+4,u/=s<.5?a+i:2-a-i,o*=60):u=s>0&&s<1?0:o,new me(o,u,s,t.opacity)}function Mc(t,e,r,n){return arguments.length===1?rg(t):new me(t,e,r,n??1)}function me(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}ai(me,Mc,Pa(pr,{brighter(t){return t=t==null?wn:Math.pow(wn,t),new me(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?zr:Math.pow(zr,t),new me(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new Ct(js(t>=240?t-240:t+120,i,n),js(t,i,n),js(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new me(Gp(this.h),Ka(this.s),Ka(this.l),Ao(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Ao(this.opacity);return`${t===1?"hsl(":"hsla("}${Gp(this.h)}, ${Ka(this.s)*100}%, ${Ka(this.l)*100}%${t===1?")":`, ${t})`}`}}));function Gp(t){return t=(t||0)%360,t<0?t+360:t}function Ka(t){return Math.max(0,Math.min(1,t||0))}function js(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}const ng=Math.PI/180,ig=180/Math.PI,Po=18,ag=.96422,og=1,ug=.82521,sg=4/29,vn=6/29,cg=3*vn*vn,Uk=vn*vn*vn;function lg(t){if(t instanceof Oe)return new Oe(t.l,t.a,t.b,t.opacity);if(t instanceof De)return fg(t);t instanceof Ct||(t=xf(t));var e=Is(t.r),r=Is(t.g),n=Is(t.b),i=Ms((.2225045*e+.7168786*r+.0606169*n)/og),a,o;return e===r&&r===n?a=o=i:(a=Ms((.4360747*e+.3850649*r+.1430804*n)/ag),o=Ms((.0139322*e+.0971045*r+.7141733*n)/ug)),new Oe(116*i-16,500*(a-i),200*(i-o),t.opacity)}function Cc(t,e,r,n){return arguments.length===1?lg(t):new Oe(t,e,r,n??1)}function Oe(t,e,r,n){this.l=+t,this.a=+e,this.b=+r,this.opacity=+n}ai(Oe,Cc,Pa(pr,{brighter(t){return new Oe(this.l+Po*(t??1),this.a,this.b,this.opacity)},darker(t){return new Oe(this.l-Po*(t??1),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,r=isNaN(this.b)?t:t-this.b/200;return e=ag*Cs(e),t=og*Cs(t),r=ug*Cs(r),new Ct(ks(3.1338561*e-1.6168667*t-.4906146*r),ks(-.9787684*e+1.9161415*t+.033454*r),ks(.0719453*e-.2289914*t+1.4052427*r),this.opacity)}}));function Ms(t){return t>Uk?Math.pow(t,1/3):t/cg+sg}function Cs(t){return t>vn?t*t*t:cg*(t-sg)}function ks(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function Is(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Kk(t){if(t instanceof De)return new De(t.h,t.c,t.l,t.opacity);if(t instanceof Oe||(t=lg(t)),t.a===0&&t.b===0)return new De(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*ig;return new De(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function kc(t,e,r,n){return arguments.length===1?Kk(t):new De(t,e,r,n??1)}function De(t,e,r,n){this.h=+t,this.c=+e,this.l=+r,this.opacity=+n}function fg(t){if(isNaN(t.h))return new Oe(t.l,0,0,t.opacity);var e=t.h*ng;return new Oe(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}ai(De,kc,Pa(pr,{brighter(t){return new De(this.h,this.c,this.l+Po*(t??1),this.opacity)},darker(t){return new De(this.h,this.c,this.l-Po*(t??1),this.opacity)},rgb(){return fg(this).rgb()}}));var hg=-.14861,_f=1.78277,wf=-.29227,Wu=-.90649,qi=1.97294,Vp=qi*Wu,Xp=qi*_f,Yp=_f*wf-Wu*hg;function qk(t){if(t instanceof Dr)return new Dr(t.h,t.s,t.l,t.opacity);t instanceof Ct||(t=xf(t));var e=t.r/255,r=t.g/255,n=t.b/255,i=(Yp*n+Vp*e-Xp*r)/(Yp+Vp-Xp),a=n-i,o=(qi*(r-i)-wf*a)/Wu,u=Math.sqrt(o*o+a*a)/(qi*i*(1-i)),s=u?Math.atan2(o,a)*ig-120:NaN;return new Dr(s<0?s+360:s,u,i,t.opacity)}function Ic(t,e,r,n){return arguments.length===1?qk(t):new Dr(t,e,r,n??1)}function Dr(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}ai(Dr,Ic,Pa(pr,{brighter(t){return t=t==null?wn:Math.pow(wn,t),new Dr(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?zr:Math.pow(zr,t),new Dr(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*ng,e=+this.l,r=isNaN(this.s)?0:this.s*e*(1-e),n=Math.cos(t),i=Math.sin(t);return new Ct(255*(e+r*(hg*n+_f*i)),255*(e+r*(wf*n+Wu*i)),255*(e+r*(qi*n)),this.opacity)}}));function pg(t,e,r,n,i){var a=t*t,o=a*t;return((1-3*t+3*a-o)*e+(4-6*a+3*o)*r+(1+3*t+3*a-3*o)*n+o*i)/6}function dg(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],a=t[n+1],o=n>0?t[n-1]:2*i-a,u=n<e-1?t[n+2]:2*a-i;return pg((r-n/e)*e,o,i,a,u)}}function vg(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],a=t[n%e],o=t[(n+1)%e],u=t[(n+2)%e];return pg((r-n/e)*e,i,a,o,u)}}const Uu=t=>()=>t;function yg(t,e){return function(r){return t+r*e}}function Hk(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}function Ku(t,e){var r=e-t;return r?yg(t,r>180||r<-180?r-360*Math.round(r/360):r):Uu(isNaN(t)?e:t)}function Gk(t){return(t=+t)==1?kt:function(e,r){return r-e?Hk(e,r,t):Uu(isNaN(e)?r:e)}}function kt(t,e){var r=e-t;return r?yg(t,r):Uu(isNaN(t)?e:t)}const Hi=function t(e){var r=Gk(e);function n(i,a){var o=r((i=Oo(i)).r,(a=Oo(a)).r),u=r(i.g,a.g),s=r(i.b,a.b),c=kt(i.opacity,a.opacity);return function(l){return i.r=o(l),i.g=u(l),i.b=s(l),i.opacity=c(l),i+""}}return n.gamma=t,n}(1);function mg(t){return function(e){var r=e.length,n=new Array(r),i=new Array(r),a=new Array(r),o,u;for(o=0;o<r;++o)u=Oo(e[o]),n[o]=u.r||0,i[o]=u.g||0,a[o]=u.b||0;return n=t(n),i=t(i),a=t(a),u.opacity=1,function(s){return u.r=n(s),u.g=i(s),u.b=a(s),u+""}}}var Vk=mg(dg),Xk=mg(vg);function Of(t,e){e||(e=[]);var r=t?Math.min(e.length,t.length):0,n=e.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=t[i]*(1-a)+e[i]*a;return n}}function gg(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function Yk(t,e){return(gg(e)?Of:bg)(t,e)}function bg(t,e){var r=e?e.length:0,n=t?Math.min(r,t.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Gr(t[o],e[o]);for(;o<r;++o)a[o]=e[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function xg(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function oe(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function _g(t,e){var r={},n={},i;(t===null||typeof t!="object")&&(t={}),(e===null||typeof e!="object")&&(e={});for(i in e)i in t?r[i]=Gr(t[i],e[i]):n[i]=e[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Nc=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ns=new RegExp(Nc.source,"g");function Zk(t){return function(){return t}}function Jk(t){return function(e){return t(e)+""}}function Af(t,e){var r=Nc.lastIndex=Ns.lastIndex=0,n,i,a,o=-1,u=[],s=[];for(t=t+"",e=e+"";(n=Nc.exec(t))&&(i=Ns.exec(e));)(a=i.index)>r&&(a=e.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,s.push({i:o,x:oe(n,i)})),r=Ns.lastIndex;return r<e.length&&(a=e.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?s[0]?Jk(s[0].x):Zk(e):(e=s.length,function(c){for(var l=0,f;l<e;++l)u[(f=s[l]).i]=f.x(c);return u.join("")})}function Gr(t,e){var r=typeof e,n;return e==null||r==="boolean"?Uu(e):(r==="number"?oe:r==="string"?(n=Fr(e))?(e=n,Hi):Af:e instanceof Fr?Hi:e instanceof Date?xg:gg(e)?Of:Array.isArray(e)?bg:typeof e.valueOf!="function"&&typeof e.toString!="function"||isNaN(e)?_g:oe)(t,e)}function Qk(t){var e=t.length;return function(r){return t[Math.max(0,Math.min(e-1,Math.floor(r*e)))]}}function tI(t,e){var r=Ku(+t,+e);return function(n){var i=r(n);return i-360*Math.floor(i/360)}}function qu(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}var Zp=180/Math.PI,Dc={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function wg(t,e,r,n,i,a){var o,u,s;return(o=Math.sqrt(t*t+e*e))&&(t/=o,e/=o),(s=t*r+e*n)&&(r-=t*s,n-=e*s),(u=Math.sqrt(r*r+n*n))&&(r/=u,n/=u,s/=u),t*n<e*r&&(t=-t,e=-e,s=-s,o=-o),{translateX:i,translateY:a,rotate:Math.atan2(e,t)*Zp,skewX:Math.atan(s)*Zp,scaleX:o,scaleY:u}}var qa;function eI(t){const e=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?Dc:wg(e.a,e.b,e.c,e.d,e.e,e.f)}function rI(t){return t==null||(qa||(qa=document.createElementNS("http://www.w3.org/2000/svg","g")),qa.setAttribute("transform",t),!(t=qa.transform.baseVal.consolidate()))?Dc:(t=t.matrix,wg(t.a,t.b,t.c,t.d,t.e,t.f))}function Og(t,e,r,n){function i(c){return c.length?c.pop()+" ":""}function a(c,l,f,h,p,d){if(c!==f||l!==h){var v=p.push("translate(",null,e,null,r);d.push({i:v-4,x:oe(c,f)},{i:v-2,x:oe(l,h)})}else(f||h)&&p.push("translate("+f+e+h+r)}function o(c,l,f,h){c!==l?(c-l>180?l+=360:l-c>180&&(c+=360),h.push({i:f.push(i(f)+"rotate(",null,n)-2,x:oe(c,l)})):l&&f.push(i(f)+"rotate("+l+n)}function u(c,l,f,h){c!==l?h.push({i:f.push(i(f)+"skewX(",null,n)-2,x:oe(c,l)}):l&&f.push(i(f)+"skewX("+l+n)}function s(c,l,f,h,p,d){if(c!==f||l!==h){var v=p.push(i(p)+"scale(",null,",",null,")");d.push({i:v-4,x:oe(c,f)},{i:v-2,x:oe(l,h)})}else(f!==1||h!==1)&&p.push(i(p)+"scale("+f+","+h+")")}return function(c,l){var f=[],h=[];return c=t(c),l=t(l),a(c.translateX,c.translateY,l.translateX,l.translateY,f,h),o(c.rotate,l.rotate,f,h),u(c.skewX,l.skewX,f,h),s(c.scaleX,c.scaleY,l.scaleX,l.scaleY,f,h),c=l=null,function(p){for(var d=-1,v=h.length,y;++d<v;)f[(y=h[d]).i]=y.x(p);return f.join("")}}}var Ag=Og(eI,"px, ","px)","deg)"),Pg=Og(rI,", ",")",")"),nI=1e-12;function Jp(t){return((t=Math.exp(t))+1/t)/2}function iI(t){return((t=Math.exp(t))-1/t)/2}function aI(t){return((t=Math.exp(2*t))-1)/(t+1)}const Sg=function t(e,r,n){function i(a,o){var u=a[0],s=a[1],c=a[2],l=o[0],f=o[1],h=o[2],p=l-u,d=f-s,v=p*p+d*d,y,g;if(v<nI)g=Math.log(h/c)/e,y=function(O){return[u+O*p,s+O*d,c*Math.exp(e*O*g)]};else{var b=Math.sqrt(v),_=(h*h-c*c+n*v)/(2*c*r*b),w=(h*h-c*c-n*v)/(2*h*r*b),m=Math.log(Math.sqrt(_*_+1)-_),x=Math.log(Math.sqrt(w*w+1)-w);g=(x-m)/e,y=function(O){var S=O*g,$=Jp(m),k=c/(r*b)*($*aI(e*S+m)-iI(m));return[u+k*p,s+k*d,c*$/Jp(e*S+m)]}}return y.duration=g*1e3*e/Math.SQRT2,y}return i.rho=function(a){var o=Math.max(.001,+a),u=o*o,s=u*u;return t(o,u,s)},i}(Math.SQRT2,2,4);function $g(t){return function(e,r){var n=t((e=Mc(e)).h,(r=Mc(r)).h),i=kt(e.s,r.s),a=kt(e.l,r.l),o=kt(e.opacity,r.opacity);return function(u){return e.h=n(u),e.s=i(u),e.l=a(u),e.opacity=o(u),e+""}}}const oI=$g(Ku);var uI=$g(kt);function sI(t,e){var r=kt((t=Cc(t)).l,(e=Cc(e)).l),n=kt(t.a,e.a),i=kt(t.b,e.b),a=kt(t.opacity,e.opacity);return function(o){return t.l=r(o),t.a=n(o),t.b=i(o),t.opacity=a(o),t+""}}function Eg(t){return function(e,r){var n=t((e=kc(e)).h,(r=kc(r)).h),i=kt(e.c,r.c),a=kt(e.l,r.l),o=kt(e.opacity,r.opacity);return function(u){return e.h=n(u),e.c=i(u),e.l=a(u),e.opacity=o(u),e+""}}}const cI=Eg(Ku);var lI=Eg(kt);function Tg(t){return function e(r){r=+r;function n(i,a){var o=t((i=Ic(i)).h,(a=Ic(a)).h),u=kt(i.s,a.s),s=kt(i.l,a.l),c=kt(i.opacity,a.opacity);return function(l){return i.h=o(l),i.s=u(l),i.l=s(Math.pow(l,r)),i.opacity=c(l),i+""}}return n.gamma=e,n}(1)}const fI=Tg(Ku);var hI=Tg(kt);function jg(t,e){e===void 0&&(e=t,t=Gr);for(var r=0,n=e.length-1,i=e[0],a=new Array(n<0?0:n);r<n;)a[r]=t(i,i=e[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function pI(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t(n/(e-1));return r}const cG=Object.freeze(Object.defineProperty({__proto__:null,interpolate:Gr,interpolateArray:Yk,interpolateBasis:dg,interpolateBasisClosed:vg,interpolateCubehelix:fI,interpolateCubehelixLong:hI,interpolateDate:xg,interpolateDiscrete:Qk,interpolateHcl:cI,interpolateHclLong:lI,interpolateHsl:oI,interpolateHslLong:uI,interpolateHue:tI,interpolateLab:sI,interpolateNumber:oe,interpolateNumberArray:Of,interpolateObject:_g,interpolateRgb:Hi,interpolateRgbBasis:Vk,interpolateRgbBasisClosed:Xk,interpolateRound:qu,interpolateString:Af,interpolateTransformCss:Ag,interpolateTransformSvg:Pg,interpolateZoom:Sg,piecewise:jg,quantize:pI},Symbol.toStringTag,{value:"Module"}));function dI(t){return function(){return t}}function So(t){return+t}var Qp=[0,1];function qt(t){return t}function Rc(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:dI(isNaN(e)?NaN:.5)}function vI(t,e){var r;return t>e&&(r=t,t=e,e=r),function(n){return Math.max(t,Math.min(e,n))}}function yI(t,e,r){var n=t[0],i=t[1],a=e[0],o=e[1];return i<n?(n=Rc(i,n),a=r(o,a)):(n=Rc(n,i),a=r(a,o)),function(u){return a(n(u))}}function mI(t,e,r){var n=Math.min(t.length,e.length)-1,i=new Array(n),a=new Array(n),o=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++o<n;)i[o]=Rc(t[o],t[o+1]),a[o]=r(e[o],e[o+1]);return function(u){var s=Aa(t,u,1,n)-1;return a[s](i[s](u))}}function Sa(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Hu(){var t=Qp,e=Qp,r=Gr,n,i,a,o=qt,u,s,c;function l(){var h=Math.min(t.length,e.length);return o!==qt&&(o=vI(t[0],t[h-1])),u=h>2?mI:yI,s=c=null,f}function f(h){return h==null||isNaN(h=+h)?a:(s||(s=u(t.map(n),e,r)))(n(o(h)))}return f.invert=function(h){return o(i((c||(c=u(e,t.map(n),oe)))(h)))},f.domain=function(h){return arguments.length?(t=Array.from(h,So),l()):t.slice()},f.range=function(h){return arguments.length?(e=Array.from(h),l()):e.slice()},f.rangeRound=function(h){return e=Array.from(h),r=qu,l()},f.clamp=function(h){return arguments.length?(o=h?!0:qt,l()):o!==qt},f.interpolate=function(h){return arguments.length?(r=h,l()):r},f.unknown=function(h){return arguments.length?(a=h,f):a},function(h,p){return n=h,i=p,l()}}function Pf(){return Hu()(qt,qt)}function gI(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function $o(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function On(t){return t=$o(Math.abs(t)),t?t[1]:NaN}function bI(t,e){return function(r,n){for(var i=r.length,a=[],o=0,u=t[0],s=0;i>0&&u>0&&(s+u+1>n&&(u=Math.max(1,n-s)),a.push(r.substring(i-=u,i+u)),!((s+=u+1)>n));)u=t[o=(o+1)%t.length];return a.reverse().join(e)}}function xI(t){return function(e){return e.replace(/[0-9]/g,function(r){return t[+r]})}}var _I=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Gi(t){if(!(e=_I.exec(t)))throw new Error("invalid format: "+t);var e;return new Sf({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}Gi.prototype=Sf.prototype;function Sf(t){this.fill=t.fill===void 0?" ":t.fill+"",this.align=t.align===void 0?">":t.align+"",this.sign=t.sign===void 0?"-":t.sign+"",this.symbol=t.symbol===void 0?"":t.symbol+"",this.zero=!!t.zero,this.width=t.width===void 0?void 0:+t.width,this.comma=!!t.comma,this.precision=t.precision===void 0?void 0:+t.precision,this.trim=!!t.trim,this.type=t.type===void 0?"":t.type+""}Sf.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function wI(t){t:for(var e=t.length,r=1,n=-1,i;r<e;++r)switch(t[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+t[r])break t;n>0&&(n=0);break}return n>0?t.slice(0,n)+t.slice(i+1):t}var Mg;function OI(t,e){var r=$o(t,e);if(!r)return t+"";var n=r[0],i=r[1],a=i-(Mg=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+$o(t,Math.max(0,e+a-1))[0]}function td(t,e){var r=$o(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const ed={"%":(t,e)=>(t*100).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:gI,e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>td(t*100,e),r:td,s:OI,X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function rd(t){return t}var nd=Array.prototype.map,id=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function AI(t){var e=t.grouping===void 0||t.thousands===void 0?rd:bI(nd.call(t.grouping,Number),t.thousands+""),r=t.currency===void 0?"":t.currency[0]+"",n=t.currency===void 0?"":t.currency[1]+"",i=t.decimal===void 0?".":t.decimal+"",a=t.numerals===void 0?rd:xI(nd.call(t.numerals,String)),o=t.percent===void 0?"%":t.percent+"",u=t.minus===void 0?"−":t.minus+"",s=t.nan===void 0?"NaN":t.nan+"";function c(f){f=Gi(f);var h=f.fill,p=f.align,d=f.sign,v=f.symbol,y=f.zero,g=f.width,b=f.comma,_=f.precision,w=f.trim,m=f.type;m==="n"?(b=!0,m="g"):ed[m]||(_===void 0&&(_=12),w=!0,m="g"),(y||h==="0"&&p==="=")&&(y=!0,h="0",p="=");var x=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",O=v==="$"?n:/[%p]/.test(m)?o:"",S=ed[m],$=/[defgprs%]/.test(m);_=_===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,_)):Math.max(0,Math.min(20,_));function k(T){var M=x,C=O,P,j,E;if(m==="c")C=S(T)+C,T="";else{T=+T;var I=T<0||1/T<0;if(T=isNaN(T)?s:S(Math.abs(T),_),w&&(T=wI(T)),I&&+T==0&&d!=="+"&&(I=!1),M=(I?d==="("?d:u:d==="-"||d==="("?"":d)+M,C=(m==="s"?id[8+Mg/3]:"")+C+(I&&d==="("?")":""),$){for(P=-1,j=T.length;++P<j;)if(E=T.charCodeAt(P),48>E||E>57){C=(E===46?i+T.slice(P+1):T.slice(P))+C,T=T.slice(0,P);break}}}b&&!y&&(T=e(T,1/0));var N=M.length+T.length+C.length,R=N<g?new Array(g-N+1).join(h):"";switch(b&&y&&(T=e(R+T,R.length?g-C.length:1/0),R=""),p){case"<":T=M+T+C+R;break;case"=":T=M+R+T+C;break;case"^":T=R.slice(0,N=R.length>>1)+M+T+C+R.slice(N);break;default:T=R+M+T+C;break}return a(T)}return k.toString=function(){return f+""},k}function l(f,h){var p=c((f=Gi(f),f.type="f",f)),d=Math.max(-8,Math.min(8,Math.floor(On(h)/3)))*3,v=Math.pow(10,-d),y=id[8+d/3];return function(g){return p(v*g)+y}}return{format:c,formatPrefix:l}}var Ha,$f,Cg;PI({thousands:",",grouping:[3],currency:["$",""]});function PI(t){return Ha=AI(t),$f=Ha.format,Cg=Ha.formatPrefix,Ha}function SI(t){return Math.max(0,-On(Math.abs(t)))}function $I(t,e){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(On(e)/3)))*3-On(Math.abs(t)))}function EI(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,On(e)-On(t))+1}function kg(t,e,r,n){var i=Tc(t,e,r),a;switch(n=Gi(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(t),Math.abs(e));return n.precision==null&&!isNaN(a=$I(i,o))&&(n.precision=a),Cg(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=EI(i,Math.max(Math.abs(t),Math.abs(e))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=SI(i))&&(n.precision=a-(n.type==="%")*2);break}}return $f(n)}function dr(t){var e=t.domain;return t.ticks=function(r){var n=e();return $c(n[0],n[n.length-1],r??10)},t.tickFormat=function(r,n){var i=e();return kg(i[0],i[i.length-1],r??10,n)},t.nice=function(r){r==null&&(r=10);var n=e(),i=0,a=n.length-1,o=n[i],u=n[a],s,c,l=10;for(u<o&&(c=o,o=u,u=c,c=i,i=a,a=c);l-- >0;){if(c=Ec(o,u,r),c===s)return n[i]=o,n[a]=u,e(n);if(c>0)o=Math.floor(o/c)*c,u=Math.ceil(u/c)*c;else if(c<0)o=Math.ceil(o*c)/c,u=Math.floor(u*c)/c;else break;s=c}return t},t}function Eo(){var t=Pf();return t.copy=function(){return Sa(t,Eo())},he.apply(t,arguments),dr(t)}function Ig(t){var e;function r(n){return n==null||isNaN(n=+n)?e:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(t=Array.from(n,So),r):t.slice()},r.unknown=function(n){return arguments.length?(e=n,r):e},r.copy=function(){return Ig(t).unknown(e)},t=arguments.length?Array.from(t,So):[0,1],dr(r)}function Ng(t,e){t=t.slice();var r=0,n=t.length-1,i=t[r],a=t[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),t[r]=e.floor(i),t[n]=e.ceil(a),t}function ad(t){return Math.log(t)}function od(t){return Math.exp(t)}function TI(t){return-Math.log(-t)}function jI(t){return-Math.exp(-t)}function MI(t){return isFinite(t)?+("1e"+t):t<0?0:t}function CI(t){return t===10?MI:t===Math.E?Math.exp:e=>Math.pow(t,e)}function kI(t){return t===Math.E?Math.log:t===10&&Math.log10||t===2&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}function ud(t){return(e,r)=>-t(-e,r)}function Ef(t){const e=t(ad,od),r=e.domain;let n=10,i,a;function o(){return i=kI(n),a=CI(n),r()[0]<0?(i=ud(i),a=ud(a),t(TI,jI)):t(ad,od),e}return e.base=function(u){return arguments.length?(n=+u,o()):n},e.domain=function(u){return arguments.length?(r(u),o()):r()},e.ticks=u=>{const s=r();let c=s[0],l=s[s.length-1];const f=l<c;f&&([c,l]=[l,c]);let h=i(c),p=i(l),d,v;const y=u==null?10:+u;let g=[];if(!(n%1)&&p-h<y){if(h=Math.floor(h),p=Math.ceil(p),c>0){for(;h<=p;++h)for(d=1;d<n;++d)if(v=h<0?d/a(-h):d*a(h),!(v<c)){if(v>l)break;g.push(v)}}else for(;h<=p;++h)for(d=n-1;d>=1;--d)if(v=h>0?d/a(-h):d*a(h),!(v<c)){if(v>l)break;g.push(v)}g.length*2<y&&(g=$c(c,l,y))}else g=$c(h,p,Math.min(p-h,y)).map(a);return f?g.reverse():g},e.tickFormat=(u,s)=>{if(u==null&&(u=10),s==null&&(s=n===10?"s":","),typeof s!="function"&&(!(n%1)&&(s=Gi(s)).precision==null&&(s.trim=!0),s=$f(s)),u===1/0)return s;const c=Math.max(1,n*u/e.ticks().length);return l=>{let f=l/a(Math.round(i(l)));return f*n<n-.5&&(f*=n),f<=c?s(l):""}},e.nice=()=>r(Ng(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),e}function Dg(){const t=Ef(Hu()).domain([1,10]);return t.copy=()=>Sa(t,Dg()).base(t.base()),he.apply(t,arguments),t}function sd(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function cd(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function Tf(t){var e=1,r=t(sd(e),cd(e));return r.constant=function(n){return arguments.length?t(sd(e=+n),cd(e)):e},dr(r)}function Rg(){var t=Tf(Hu());return t.copy=function(){return Sa(t,Rg()).constant(t.constant())},he.apply(t,arguments)}function ld(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function II(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function NI(t){return t<0?-t*t:t*t}function jf(t){var e=t(qt,qt),r=1;function n(){return r===1?t(qt,qt):r===.5?t(II,NI):t(ld(r),ld(1/r))}return e.exponent=function(i){return arguments.length?(r=+i,n()):r},dr(e)}function Mf(){var t=jf(Hu());return t.copy=function(){return Sa(t,Mf()).exponent(t.exponent())},he.apply(t,arguments),t}function DI(){return Mf.apply(null,arguments).exponent(.5)}function fd(t){return Math.sign(t)*t*t}function RI(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}function Lg(){var t=Pf(),e=[0,1],r=!1,n;function i(a){var o=RI(t(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return t.invert(fd(a))},i.domain=function(a){return arguments.length?(t.domain(a),i):t.domain()},i.range=function(a){return arguments.length?(t.range((e=Array.from(a,So)).map(fd)),i):e.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(t.clamp(a),i):t.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Lg(t.domain(),e).round(r).clamp(t.clamp()).unknown(n)},he.apply(i,arguments),dr(i)}function Bg(){var t=[],e=[],r=[],n;function i(){var o=0,u=Math.max(1,e.length);for(r=new Array(u-1);++o<u;)r[o-1]=Mk(t,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:e[Aa(r,o)]}return a.invertExtent=function(o){var u=e.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:t[0],u<r.length?r[u]:t[t.length-1]]},a.domain=function(o){if(!arguments.length)return t.slice();t=[];for(let u of o)u!=null&&!isNaN(u=+u)&&t.push(u);return t.sort(or),i()},a.range=function(o){return arguments.length?(e=Array.from(o),i()):e.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return Bg().domain(t).range(e).unknown(n)},he.apply(a,arguments)}function zg(){var t=0,e=1,r=1,n=[.5],i=[0,1],a;function o(s){return s!=null&&s<=s?i[Aa(n,s,0,r)]:a}function u(){var s=-1;for(n=new Array(r);++s<r;)n[s]=((s+1)*e-(s-r)*t)/(r+1);return o}return o.domain=function(s){return arguments.length?([t,e]=s,t=+t,e=+e,u()):[t,e]},o.range=function(s){return arguments.length?(r=(i=Array.from(s)).length-1,u()):i.slice()},o.invertExtent=function(s){var c=i.indexOf(s);return c<0?[NaN,NaN]:c<1?[t,n[0]]:c>=r?[n[r-1],e]:[n[c-1],n[c]]},o.unknown=function(s){return arguments.length&&(a=s),o},o.thresholds=function(){return n.slice()},o.copy=function(){return zg().domain([t,e]).range(i).unknown(a)},he.apply(dr(o),arguments)}function Fg(){var t=[.5],e=[0,1],r,n=1;function i(a){return a!=null&&a<=a?e[Aa(t,a,0,n)]:r}return i.domain=function(a){return arguments.length?(t=Array.from(a),n=Math.min(t.length,e.length-1),i):t.slice()},i.range=function(a){return arguments.length?(e=Array.from(a),n=Math.min(t.length,e.length-1),i):e.slice()},i.invertExtent=function(a){var o=e.indexOf(a);return[t[o-1],t[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Fg().domain(t).range(e).unknown(r)},he.apply(i,arguments)}const Ds=new Date,Rs=new Date;function Tt(t,e,r,n){function i(a){return t(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(t(a=new Date(+a)),a),i.ceil=a=>(t(a=new Date(a-1)),e(a,1),t(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(e(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const s=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return s;let c;do s.push(c=new Date(+a)),e(a,u),t(a);while(c<a&&a<o);return s},i.filter=a=>Tt(o=>{if(o>=o)for(;t(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;e(o,-1),!a(o););else for(;--u>=0;)for(;e(o,1),!a(o););}),r&&(i.count=(a,o)=>(Ds.setTime(+a),Rs.setTime(+o),t(Ds),t(Rs),Math.floor(r(Ds,Rs))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const To=Tt(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);To.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?Tt(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):To);To.range;const Re=1e3,se=Re*60,Le=se*60,Ue=Le*24,Cf=Ue*7,hd=Ue*30,Ls=Ue*365,Cr=Tt(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*Re)},(t,e)=>(e-t)/Re,t=>t.getUTCSeconds());Cr.range;const kf=Tt(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Re)},(t,e)=>{t.setTime(+t+e*se)},(t,e)=>(e-t)/se,t=>t.getMinutes());kf.range;const If=Tt(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*se)},(t,e)=>(e-t)/se,t=>t.getUTCMinutes());If.range;const Nf=Tt(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Re-t.getMinutes()*se)},(t,e)=>{t.setTime(+t+e*Le)},(t,e)=>(e-t)/Le,t=>t.getHours());Nf.range;const Df=Tt(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*Le)},(t,e)=>(e-t)/Le,t=>t.getUTCHours());Df.range;const $a=Tt(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*se)/Ue,t=>t.getDate()-1);$a.range;const Gu=Tt(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/Ue,t=>t.getUTCDate()-1);Gu.range;const Wg=Tt(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/Ue,t=>Math.floor(t/Ue));Wg.range;function Vr(t){return Tt(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,r)=>{e.setDate(e.getDate()+r*7)},(e,r)=>(r-e-(r.getTimezoneOffset()-e.getTimezoneOffset())*se)/Cf)}const Vu=Vr(0),jo=Vr(1),LI=Vr(2),BI=Vr(3),An=Vr(4),zI=Vr(5),FI=Vr(6);Vu.range;jo.range;LI.range;BI.range;An.range;zI.range;FI.range;function Xr(t){return Tt(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCDate(e.getUTCDate()+r*7)},(e,r)=>(r-e)/Cf)}const Xu=Xr(0),Mo=Xr(1),WI=Xr(2),UI=Xr(3),Pn=Xr(4),KI=Xr(5),qI=Xr(6);Xu.range;Mo.range;WI.range;UI.range;Pn.range;KI.range;qI.range;const Rf=Tt(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());Rf.range;const Lf=Tt(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());Lf.range;const Ke=Tt(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());Ke.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:Tt(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)});Ke.range;const qe=Tt(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());qe.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:Tt(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)});qe.range;function Ug(t,e,r,n,i,a){const o=[[Cr,1,Re],[Cr,5,5*Re],[Cr,15,15*Re],[Cr,30,30*Re],[a,1,se],[a,5,5*se],[a,15,15*se],[a,30,30*se],[i,1,Le],[i,3,3*Le],[i,6,6*Le],[i,12,12*Le],[n,1,Ue],[n,2,2*Ue],[r,1,Cf],[e,1,hd],[e,3,3*hd],[t,1,Ls]];function u(c,l,f){const h=l<c;h&&([c,l]=[l,c]);const p=f&&typeof f.range=="function"?f:s(c,l,f),d=p?p.range(c,+l+1):[];return h?d.reverse():d}function s(c,l,f){const h=Math.abs(l-c)/f,p=gf(([,,y])=>y).right(o,h);if(p===o.length)return t.every(Tc(c/Ls,l/Ls,f));if(p===0)return To.every(Math.max(Tc(c,l,f),1));const[d,v]=o[h/o[p-1][2]<o[p][2]/h?p-1:p];return d.every(v)}return[u,s]}const[HI,GI]=Ug(qe,Lf,Xu,Wg,Df,If),[VI,XI]=Ug(Ke,Rf,Vu,$a,Nf,kf);function Bs(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function zs(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function di(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}function YI(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,a=t.days,o=t.shortDays,u=t.months,s=t.shortMonths,c=vi(i),l=yi(i),f=vi(a),h=yi(a),p=vi(o),d=yi(o),v=vi(u),y=yi(u),g=vi(s),b=yi(s),_={a:I,A:N,b:R,B,c:null,d:gd,e:gd,f:bN,g:TN,G:MN,H:yN,I:mN,j:gN,L:Kg,m:xN,M:_N,p:U,q:z,Q:_d,s:wd,S:wN,u:ON,U:AN,V:PN,w:SN,W:$N,x:null,X:null,y:EN,Y:jN,Z:CN,"%":xd},w={a:G,A:Z,b:rt,B:ht,c:null,d:bd,e:bd,f:DN,g:HN,G:VN,H:kN,I:IN,j:NN,L:Hg,m:RN,M:LN,p:xt,q:ct,Q:_d,s:wd,S:BN,u:zN,U:FN,V:WN,w:UN,W:KN,x:null,X:null,y:qN,Y:GN,Z:XN,"%":xd},m={a:k,A:T,b:M,B:C,c:P,d:yd,e:yd,f:hN,g:vd,G:dd,H:md,I:md,j:sN,L:fN,m:uN,M:cN,p:$,q:oN,Q:dN,s:vN,S:lN,u:eN,U:rN,V:nN,w:tN,W:iN,x:j,X:E,y:vd,Y:dd,Z:aN,"%":pN};_.x=x(r,_),_.X=x(n,_),_.c=x(e,_),w.x=x(r,w),w.X=x(n,w),w.c=x(e,w);function x(K,tt){return function(nt){var L=[],_t=-1,it=0,Pt=K.length,St,Gt,Je;for(nt instanceof Date||(nt=new Date(+nt));++_t<Pt;)K.charCodeAt(_t)===37&&(L.push(K.slice(it,_t)),(Gt=pd[St=K.charAt(++_t)])!=null?St=K.charAt(++_t):Gt=St==="e"?" ":"0",(Je=tt[St])&&(St=Je(nt,Gt)),L.push(St),it=_t+1);return L.push(K.slice(it,_t)),L.join("")}}function O(K,tt){return function(nt){var L=di(1900,void 0,1),_t=S(L,K,nt+="",0),it,Pt;if(_t!=nt.length)return null;if("Q"in L)return new Date(L.Q);if("s"in L)return new Date(L.s*1e3+("L"in L?L.L:0));if(tt&&!("Z"in L)&&(L.Z=0),"p"in L&&(L.H=L.H%12+L.p*12),L.m===void 0&&(L.m="q"in L?L.q:0),"V"in L){if(L.V<1||L.V>53)return null;"w"in L||(L.w=1),"Z"in L?(it=zs(di(L.y,0,1)),Pt=it.getUTCDay(),it=Pt>4||Pt===0?Mo.ceil(it):Mo(it),it=Gu.offset(it,(L.V-1)*7),L.y=it.getUTCFullYear(),L.m=it.getUTCMonth(),L.d=it.getUTCDate()+(L.w+6)%7):(it=Bs(di(L.y,0,1)),Pt=it.getDay(),it=Pt>4||Pt===0?jo.ceil(it):jo(it),it=$a.offset(it,(L.V-1)*7),L.y=it.getFullYear(),L.m=it.getMonth(),L.d=it.getDate()+(L.w+6)%7)}else("W"in L||"U"in L)&&("w"in L||(L.w="u"in L?L.u%7:"W"in L?1:0),Pt="Z"in L?zs(di(L.y,0,1)).getUTCDay():Bs(di(L.y,0,1)).getDay(),L.m=0,L.d="W"in L?(L.w+6)%7+L.W*7-(Pt+5)%7:L.w+L.U*7-(Pt+6)%7);return"Z"in L?(L.H+=L.Z/100|0,L.M+=L.Z%100,zs(L)):Bs(L)}}function S(K,tt,nt,L){for(var _t=0,it=tt.length,Pt=nt.length,St,Gt;_t<it;){if(L>=Pt)return-1;if(St=tt.charCodeAt(_t++),St===37){if(St=tt.charAt(_t++),Gt=m[St in pd?tt.charAt(_t++):St],!Gt||(L=Gt(K,nt,L))<0)return-1}else if(St!=nt.charCodeAt(L++))return-1}return L}function $(K,tt,nt){var L=c.exec(tt.slice(nt));return L?(K.p=l.get(L[0].toLowerCase()),nt+L[0].length):-1}function k(K,tt,nt){var L=p.exec(tt.slice(nt));return L?(K.w=d.get(L[0].toLowerCase()),nt+L[0].length):-1}function T(K,tt,nt){var L=f.exec(tt.slice(nt));return L?(K.w=h.get(L[0].toLowerCase()),nt+L[0].length):-1}function M(K,tt,nt){var L=g.exec(tt.slice(nt));return L?(K.m=b.get(L[0].toLowerCase()),nt+L[0].length):-1}function C(K,tt,nt){var L=v.exec(tt.slice(nt));return L?(K.m=y.get(L[0].toLowerCase()),nt+L[0].length):-1}function P(K,tt,nt){return S(K,e,tt,nt)}function j(K,tt,nt){return S(K,r,tt,nt)}function E(K,tt,nt){return S(K,n,tt,nt)}function I(K){return o[K.getDay()]}function N(K){return a[K.getDay()]}function R(K){return s[K.getMonth()]}function B(K){return u[K.getMonth()]}function U(K){return i[+(K.getHours()>=12)]}function z(K){return 1+~~(K.getMonth()/3)}function G(K){return o[K.getUTCDay()]}function Z(K){return a[K.getUTCDay()]}function rt(K){return s[K.getUTCMonth()]}function ht(K){return u[K.getUTCMonth()]}function xt(K){return i[+(K.getUTCHours()>=12)]}function ct(K){return 1+~~(K.getUTCMonth()/3)}return{format:function(K){var tt=x(K+="",_);return tt.toString=function(){return K},tt},parse:function(K){var tt=O(K+="",!1);return tt.toString=function(){return K},tt},utcFormat:function(K){var tt=x(K+="",w);return tt.toString=function(){return K},tt},utcParse:function(K){var tt=O(K+="",!0);return tt.toString=function(){return K},tt}}}var pd={"-":"",_:" ",0:"0"},It=/^\s*\d+/,ZI=/^%/,JI=/[\\^$*+?|[\]().{}]/g;function at(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(e)+i:i)}function QI(t){return t.replace(JI,"\\$&")}function vi(t){return new RegExp("^(?:"+t.map(QI).join("|")+")","i")}function yi(t){return new Map(t.map((e,r)=>[e.toLowerCase(),r]))}function tN(t,e,r){var n=It.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eN(t,e,r){var n=It.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function rN(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function nN(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function iN(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function dd(t,e,r){var n=It.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function vd(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aN(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function oN(t,e,r){var n=It.exec(e.slice(r,r+1));return n?(t.q=n[0]*3-3,r+n[0].length):-1}function uN(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function yd(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function sN(t,e,r){var n=It.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function md(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function cN(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function lN(t,e,r){var n=It.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function fN(t,e,r){var n=It.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function hN(t,e,r){var n=It.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function pN(t,e,r){var n=ZI.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function dN(t,e,r){var n=It.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function vN(t,e,r){var n=It.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function gd(t,e){return at(t.getDate(),e,2)}function yN(t,e){return at(t.getHours(),e,2)}function mN(t,e){return at(t.getHours()%12||12,e,2)}function gN(t,e){return at(1+$a.count(Ke(t),t),e,3)}function Kg(t,e){return at(t.getMilliseconds(),e,3)}function bN(t,e){return Kg(t,e)+"000"}function xN(t,e){return at(t.getMonth()+1,e,2)}function _N(t,e){return at(t.getMinutes(),e,2)}function wN(t,e){return at(t.getSeconds(),e,2)}function ON(t){var e=t.getDay();return e===0?7:e}function AN(t,e){return at(Vu.count(Ke(t)-1,t),e,2)}function qg(t){var e=t.getDay();return e>=4||e===0?An(t):An.ceil(t)}function PN(t,e){return t=qg(t),at(An.count(Ke(t),t)+(Ke(t).getDay()===4),e,2)}function SN(t){return t.getDay()}function $N(t,e){return at(jo.count(Ke(t)-1,t),e,2)}function EN(t,e){return at(t.getFullYear()%100,e,2)}function TN(t,e){return t=qg(t),at(t.getFullYear()%100,e,2)}function jN(t,e){return at(t.getFullYear()%1e4,e,4)}function MN(t,e){var r=t.getDay();return t=r>=4||r===0?An(t):An.ceil(t),at(t.getFullYear()%1e4,e,4)}function CN(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+at(e/60|0,"0",2)+at(e%60,"0",2)}function bd(t,e){return at(t.getUTCDate(),e,2)}function kN(t,e){return at(t.getUTCHours(),e,2)}function IN(t,e){return at(t.getUTCHours()%12||12,e,2)}function NN(t,e){return at(1+Gu.count(qe(t),t),e,3)}function Hg(t,e){return at(t.getUTCMilliseconds(),e,3)}function DN(t,e){return Hg(t,e)+"000"}function RN(t,e){return at(t.getUTCMonth()+1,e,2)}function LN(t,e){return at(t.getUTCMinutes(),e,2)}function BN(t,e){return at(t.getUTCSeconds(),e,2)}function zN(t){var e=t.getUTCDay();return e===0?7:e}function FN(t,e){return at(Xu.count(qe(t)-1,t),e,2)}function Gg(t){var e=t.getUTCDay();return e>=4||e===0?Pn(t):Pn.ceil(t)}function WN(t,e){return t=Gg(t),at(Pn.count(qe(t),t)+(qe(t).getUTCDay()===4),e,2)}function UN(t){return t.getUTCDay()}function KN(t,e){return at(Mo.count(qe(t)-1,t),e,2)}function qN(t,e){return at(t.getUTCFullYear()%100,e,2)}function HN(t,e){return t=Gg(t),at(t.getUTCFullYear()%100,e,2)}function GN(t,e){return at(t.getUTCFullYear()%1e4,e,4)}function VN(t,e){var r=t.getUTCDay();return t=r>=4||r===0?Pn(t):Pn.ceil(t),at(t.getUTCFullYear()%1e4,e,4)}function XN(){return"+0000"}function xd(){return"%"}function _d(t){return+t}function wd(t){return Math.floor(+t/1e3)}var nn,Vg,Xg;YN({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function YN(t){return nn=YI(t),Vg=nn.format,nn.parse,Xg=nn.utcFormat,nn.utcParse,nn}function ZN(t){return new Date(t)}function JN(t){return t instanceof Date?+t:+new Date(+t)}function Bf(t,e,r,n,i,a,o,u,s,c){var l=Pf(),f=l.invert,h=l.domain,p=c(".%L"),d=c(":%S"),v=c("%I:%M"),y=c("%I %p"),g=c("%a %d"),b=c("%b %d"),_=c("%B"),w=c("%Y");function m(x){return(s(x)<x?p:u(x)<x?d:o(x)<x?v:a(x)<x?y:n(x)<x?i(x)<x?g:b:r(x)<x?_:w)(x)}return l.invert=function(x){return new Date(f(x))},l.domain=function(x){return arguments.length?h(Array.from(x,JN)):h().map(ZN)},l.ticks=function(x){var O=h();return t(O[0],O[O.length-1],x??10)},l.tickFormat=function(x,O){return O==null?m:c(O)},l.nice=function(x){var O=h();return(!x||typeof x.range!="function")&&(x=e(O[0],O[O.length-1],x??10)),x?h(Ng(O,x)):l},l.copy=function(){return Sa(l,Bf(t,e,r,n,i,a,o,u,s,c))},l}function QN(){return he.apply(Bf(VI,XI,Ke,Rf,Vu,$a,Nf,kf,Cr,Vg).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function tD(){return he.apply(Bf(HI,GI,qe,Lf,Xu,Gu,Df,If,Cr,Xg).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Yu(){var t=0,e=1,r,n,i,a,o=qt,u=!1,s;function c(f){return f==null||isNaN(f=+f)?s:o(i===0?.5:(f=(a(f)-r)*i,u?Math.max(0,Math.min(1,f)):f))}c.domain=function(f){return arguments.length?([t,e]=f,r=a(t=+t),n=a(e=+e),i=r===n?0:1/(n-r),c):[t,e]},c.clamp=function(f){return arguments.length?(u=!!f,c):u},c.interpolator=function(f){return arguments.length?(o=f,c):o};function l(f){return function(h){var p,d;return arguments.length?([p,d]=h,o=f(p,d),c):[o(0),o(1)]}}return c.range=l(Gr),c.rangeRound=l(qu),c.unknown=function(f){return arguments.length?(s=f,c):s},function(f){return a=f,r=f(t),n=f(e),i=r===n?0:1/(n-r),c}}function vr(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Yg(){var t=dr(Yu()(qt));return t.copy=function(){return vr(t,Yg())},Ze.apply(t,arguments)}function Zg(){var t=Ef(Yu()).domain([1,10]);return t.copy=function(){return vr(t,Zg()).base(t.base())},Ze.apply(t,arguments)}function Jg(){var t=Tf(Yu());return t.copy=function(){return vr(t,Jg()).constant(t.constant())},Ze.apply(t,arguments)}function zf(){var t=jf(Yu());return t.copy=function(){return vr(t,zf()).exponent(t.exponent())},Ze.apply(t,arguments)}function eD(){return zf.apply(null,arguments).exponent(.5)}function Qg(){var t=[],e=qt;function r(n){if(n!=null&&!isNaN(n=+n))return e((Aa(t,n,1)-1)/(t.length-1))}return r.domain=function(n){if(!arguments.length)return t.slice();t=[];for(let i of n)i!=null&&!isNaN(i=+i)&&t.push(i);return t.sort(or),r},r.interpolator=function(n){return arguments.length?(e=n,r):e},r.range=function(){return t.map((n,i)=>e(i/(t.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>jk(t,a/n))},r.copy=function(){return Qg(e).domain(t)},Ze.apply(r,arguments)}function Zu(){var t=0,e=.5,r=1,n=1,i,a,o,u,s,c=qt,l,f=!1,h;function p(v){return isNaN(v=+v)?h:(v=.5+((v=+l(v))-a)*(n*v<n*a?u:s),c(f?Math.max(0,Math.min(1,v)):v))}p.domain=function(v){return arguments.length?([t,e,r]=v,i=l(t=+t),a=l(e=+e),o=l(r=+r),u=i===a?0:.5/(a-i),s=a===o?0:.5/(o-a),n=a<i?-1:1,p):[t,e,r]},p.clamp=function(v){return arguments.length?(f=!!v,p):f},p.interpolator=function(v){return arguments.length?(c=v,p):c};function d(v){return function(y){var g,b,_;return arguments.length?([g,b,_]=y,c=jg(v,[g,b,_]),p):[c(0),c(.5),c(1)]}}return p.range=d(Gr),p.rangeRound=d(qu),p.unknown=function(v){return arguments.length?(h=v,p):h},function(v){return l=v,i=v(t),a=v(e),o=v(r),u=i===a?0:.5/(a-i),s=a===o?0:.5/(o-a),n=a<i?-1:1,p}}function tb(){var t=dr(Zu()(qt));return t.copy=function(){return vr(t,tb())},Ze.apply(t,arguments)}function eb(){var t=Ef(Zu()).domain([.1,1,10]);return t.copy=function(){return vr(t,eb()).base(t.base())},Ze.apply(t,arguments)}function rb(){var t=Tf(Zu());return t.copy=function(){return vr(t,rb()).constant(t.constant())},Ze.apply(t,arguments)}function Ff(){var t=jf(Zu());return t.copy=function(){return vr(t,Ff()).exponent(t.exponent())},Ze.apply(t,arguments)}function rD(){return Ff.apply(null,arguments).exponent(.5)}const Od=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Ui,scaleDiverging:tb,scaleDivergingLog:eb,scaleDivergingPow:Ff,scaleDivergingSqrt:rD,scaleDivergingSymlog:rb,scaleIdentity:Ig,scaleImplicit:jc,scaleLinear:Eo,scaleLog:Dg,scaleOrdinal:bf,scalePoint:Ei,scalePow:Mf,scaleQuantile:Bg,scaleQuantize:zg,scaleRadial:Lg,scaleSequential:Yg,scaleSequentialLog:Zg,scaleSequentialPow:zf,scaleSequentialQuantile:Qg,scaleSequentialSqrt:eD,scaleSequentialSymlog:Jg,scaleSqrt:DI,scaleSymlog:Rg,scaleThreshold:Fg,scaleTime:QN,scaleUtc:tD,tickFormat:kg},Symbol.toStringTag,{value:"Module"}));var nD=Yn;function iD(t,e,r){for(var n=-1,i=t.length;++n<i;){var a=t[n],o=e(a);if(o!=null&&(u===void 0?o===o&&!nD(o):r(o,u)))var u=o,s=a}return s}var Ju=iD;function aD(t,e){return t>e}var nb=aD,oD=Ju,uD=nb,sD=ii;function cD(t){return t&&t.length?oD(t,sD,uD):void 0}var lD=cD;const rr=ft(lD);function fD(t,e){return t<e}var ib=fD,hD=Ju,pD=ib,dD=ii;function vD(t){return t&&t.length?hD(t,dD,pD):void 0}var yD=vD;const Qu=ft(yD);var mD=Xl,gD=$e,bD=zm,xD=Xt;function _D(t,e){var r=xD(t)?mD:bD;return r(t,gD(e))}var wD=_D,OD=Lm,AD=wD;function PD(t,e){return OD(AD(t,e),1)}var SD=PD;const $D=ft(SD);var ED=df;function TD(t,e){return ED(t,e)}var jD=TD;const He=ft(jD);var oi=1e9,MD={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Uf,bt=!0,le="[DecimalError] ",Rr=le+"Invalid argument: ",Wf=le+"Exponent out of range: ",ui=Math.floor,$r=Math.pow,CD=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Qt,jt=1e7,gt=7,ab=9007199254740991,Co=ui(ab/gt),H={};H.absoluteValue=H.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t};H.comparedTo=H.cmp=function(t){var e,r,n,i,a=this;if(t=new a.constructor(t),a.s!==t.s)return a.s||-t.s;if(a.e!==t.e)return a.e>t.e^a.s<0?1:-1;for(n=a.d.length,i=t.d.length,e=0,r=n<i?n:i;e<r;++e)if(a.d[e]!==t.d[e])return a.d[e]>t.d[e]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};H.decimalPlaces=H.dp=function(){var t=this,e=t.d.length-1,r=(e-t.e)*gt;if(e=t.d[e],e)for(;e%10==0;e/=10)r--;return r<0?0:r};H.dividedBy=H.div=function(t){return We(this,new this.constructor(t))};H.dividedToIntegerBy=H.idiv=function(t){var e=this,r=e.constructor;return vt(We(e,new r(t),0,1),r.precision)};H.equals=H.eq=function(t){return!this.cmp(t)};H.exponent=function(){return At(this)};H.greaterThan=H.gt=function(t){return this.cmp(t)>0};H.greaterThanOrEqualTo=H.gte=function(t){return this.cmp(t)>=0};H.isInteger=H.isint=function(){return this.e>this.d.length-2};H.isNegative=H.isneg=function(){return this.s<0};H.isPositive=H.ispos=function(){return this.s>0};H.isZero=function(){return this.s===0};H.lessThan=H.lt=function(t){return this.cmp(t)<0};H.lessThanOrEqualTo=H.lte=function(t){return this.cmp(t)<1};H.logarithm=H.log=function(t){var e,r=this,n=r.constructor,i=n.precision,a=i+5;if(t===void 0)t=new n(10);else if(t=new n(t),t.s<1||t.eq(Qt))throw Error(le+"NaN");if(r.s<1)throw Error(le+(r.s?"NaN":"-Infinity"));return r.eq(Qt)?new n(0):(bt=!1,e=We(Vi(r,a),Vi(t,a),a),bt=!0,vt(e,i))};H.minus=H.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?sb(e,t):ob(e,(t.s=-t.s,t))};H.modulo=H.mod=function(t){var e,r=this,n=r.constructor,i=n.precision;if(t=new n(t),!t.s)throw Error(le+"NaN");return r.s?(bt=!1,e=We(r,t,0,1).times(t),bt=!0,r.minus(e)):vt(new n(r),i)};H.naturalExponential=H.exp=function(){return ub(this)};H.naturalLogarithm=H.ln=function(){return Vi(this)};H.negated=H.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t};H.plus=H.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?ob(e,t):sb(e,(t.s=-t.s,t))};H.precision=H.sd=function(t){var e,r,n,i=this;if(t!==void 0&&t!==!!t&&t!==1&&t!==0)throw Error(Rr+t);if(e=At(i)+1,n=i.d.length-1,r=n*gt+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return t&&e>r?e:r};H.squareRoot=H.sqrt=function(){var t,e,r,n,i,a,o,u=this,s=u.constructor;if(u.s<1){if(!u.s)return new s(0);throw Error(le+"NaN")}for(t=At(u),bt=!1,i=Math.sqrt(+u),i==0||i==1/0?(e=xe(u.d),(e.length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=ui((t+1)/2)-(t<0||t%2),i==1/0?e="5e"+t:(e=i.toExponential(),e=e.slice(0,e.indexOf("e")+1)+t),n=new s(e)):n=new s(i.toString()),r=s.precision,i=o=r+3;;)if(a=n,n=a.plus(We(u,a,o+2)).times(.5),xe(a.d).slice(0,o)===(e=xe(n.d)).slice(0,o)){if(e=e.slice(o-3,o+1),i==o&&e=="4999"){if(vt(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(e!="9999")break;o+=4}return bt=!0,vt(n,r)};H.times=H.mul=function(t){var e,r,n,i,a,o,u,s,c,l=this,f=l.constructor,h=l.d,p=(t=new f(t)).d;if(!l.s||!t.s)return new f(0);for(t.s*=l.s,r=l.e+t.e,s=h.length,c=p.length,s<c&&(a=h,h=p,p=a,o=s,s=c,c=o),a=[],o=s+c,n=o;n--;)a.push(0);for(n=c;--n>=0;){for(e=0,i=s+n;i>n;)u=a[i]+p[n]*h[i-n-1]+e,a[i--]=u%jt|0,e=u/jt|0;a[i]=(a[i]+e)%jt|0}for(;!a[--o];)a.pop();return e?++r:a.shift(),t.d=a,t.e=r,bt?vt(t,f.precision):t};H.toDecimalPlaces=H.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),t===void 0?r:(Pe(t,0,oi),e===void 0?e=n.rounding:Pe(e,0,8),vt(r,t+At(r)+1,e))};H.toExponential=function(t,e){var r,n=this,i=n.constructor;return t===void 0?r=Wr(n,!0):(Pe(t,0,oi),e===void 0?e=i.rounding:Pe(e,0,8),n=vt(new i(n),t+1,e),r=Wr(n,!0,t+1)),r};H.toFixed=function(t,e){var r,n,i=this,a=i.constructor;return t===void 0?Wr(i):(Pe(t,0,oi),e===void 0?e=a.rounding:Pe(e,0,8),n=vt(new a(i),t+At(i)+1,e),r=Wr(n.abs(),!1,t+At(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};H.toInteger=H.toint=function(){var t=this,e=t.constructor;return vt(new e(t),At(t)+1,e.rounding)};H.toNumber=function(){return+this};H.toPower=H.pow=function(t){var e,r,n,i,a,o,u=this,s=u.constructor,c=12,l=+(t=new s(t));if(!t.s)return new s(Qt);if(u=new s(u),!u.s){if(t.s<1)throw Error(le+"Infinity");return u}if(u.eq(Qt))return u;if(n=s.precision,t.eq(Qt))return vt(u,n);if(e=t.e,r=t.d.length-1,o=e>=r,a=u.s,o){if((r=l<0?-l:l)<=ab){for(i=new s(Qt),e=Math.ceil(n/gt+4),bt=!1;r%2&&(i=i.times(u),Pd(i.d,e)),r=ui(r/2),r!==0;)u=u.times(u),Pd(u.d,e);return bt=!0,t.s<0?new s(Qt).div(i):vt(i,n)}}else if(a<0)throw Error(le+"NaN");return a=a<0&&t.d[Math.max(e,r)]&1?-1:1,u.s=1,bt=!1,i=t.times(Vi(u,n+c)),bt=!0,i=ub(i),i.s=a,i};H.toPrecision=function(t,e){var r,n,i=this,a=i.constructor;return t===void 0?(r=At(i),n=Wr(i,r<=a.toExpNeg||r>=a.toExpPos)):(Pe(t,1,oi),e===void 0?e=a.rounding:Pe(e,0,8),i=vt(new a(i),t,e),r=At(i),n=Wr(i,t<=r||r<=a.toExpNeg,t)),n};H.toSignificantDigits=H.tosd=function(t,e){var r=this,n=r.constructor;return t===void 0?(t=n.precision,e=n.rounding):(Pe(t,1,oi),e===void 0?e=n.rounding:Pe(e,0,8)),vt(new n(r),t,e)};H.toString=H.valueOf=H.val=H.toJSON=H[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=this,e=At(t),r=t.constructor;return Wr(t,e<=r.toExpNeg||e>=r.toExpPos)};function ob(t,e){var r,n,i,a,o,u,s,c,l=t.constructor,f=l.precision;if(!t.s||!e.s)return e.s||(e=new l(t)),bt?vt(e,f):e;if(s=t.d,c=e.d,o=t.e,i=e.e,s=s.slice(),a=o-i,a){for(a<0?(n=s,a=-a,u=c.length):(n=c,i=o,u=s.length),o=Math.ceil(f/gt),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=s.length,a=c.length,u-a<0&&(a=u,n=c,c=s,s=n),r=0;a;)r=(s[--a]=s[a]+c[a]+r)/jt|0,s[a]%=jt;for(r&&(s.unshift(r),++i),u=s.length;s[--u]==0;)s.pop();return e.d=s,e.e=i,bt?vt(e,f):e}function Pe(t,e,r){if(t!==~~t||t<e||t>r)throw Error(Rr+t)}function xe(t){var e,r,n,i=t.length-1,a="",o=t[0];if(i>0){for(a+=o,e=1;e<i;e++)n=t[e]+"",r=gt-n.length,r&&(a+=tr(r)),a+=n;o=t[e],n=o+"",r=gt-n.length,r&&(a+=tr(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var We=function(){function t(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%jt|0,o=a/jt|0;return o&&n.unshift(o),n}function e(n,i,a,o){var u,s;if(a!=o)s=a>o?1:-1;else for(u=s=0;u<a;u++)if(n[u]!=i[u]){s=n[u]>i[u]?1:-1;break}return s}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*jt+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,s,c,l,f,h,p,d,v,y,g,b,_,w,m,x,O,S,$=n.constructor,k=n.s==i.s?1:-1,T=n.d,M=i.d;if(!n.s)return new $(n);if(!i.s)throw Error(le+"Division by zero");for(s=n.e-i.e,O=M.length,m=T.length,p=new $(k),d=p.d=[],c=0;M[c]==(T[c]||0);)++c;if(M[c]>(T[c]||0)&&--s,a==null?b=a=$.precision:o?b=a+(At(n)-At(i))+1:b=a,b<0)return new $(0);if(b=b/gt+2|0,c=0,O==1)for(l=0,M=M[0],b++;(c<m||l)&&b--;c++)_=l*jt+(T[c]||0),d[c]=_/M|0,l=_%M|0;else{for(l=jt/(M[0]+1)|0,l>1&&(M=t(M,l),T=t(T,l),O=M.length,m=T.length),w=O,v=T.slice(0,O),y=v.length;y<O;)v[y++]=0;S=M.slice(),S.unshift(0),x=M[0],M[1]>=jt/2&&++x;do l=0,u=e(M,v,O,y),u<0?(g=v[0],O!=y&&(g=g*jt+(v[1]||0)),l=g/x|0,l>1?(l>=jt&&(l=jt-1),f=t(M,l),h=f.length,y=v.length,u=e(f,v,h,y),u==1&&(l--,r(f,O<h?S:M,h))):(l==0&&(u=l=1),f=M.slice()),h=f.length,h<y&&f.unshift(0),r(v,f,y),u==-1&&(y=v.length,u=e(M,v,O,y),u<1&&(l++,r(v,O<y?S:M,y))),y=v.length):u===0&&(l++,v=[0]),d[c++]=l,u&&v[0]?v[y++]=T[w]||0:(v=[T[w]],y=1);while((w++<m||v[0]!==void 0)&&b--)}return d[0]||d.shift(),p.e=s,vt(p,o?a+At(p)+1:a)}}();function ub(t,e){var r,n,i,a,o,u,s=0,c=0,l=t.constructor,f=l.precision;if(At(t)>16)throw Error(Wf+At(t));if(!t.s)return new l(Qt);for(bt=!1,u=f,o=new l(.03125);t.abs().gte(.1);)t=t.times(o),c+=5;for(n=Math.log($r(2,c))/Math.LN10*2+5|0,u+=n,r=i=a=new l(Qt),l.precision=u;;){if(i=vt(i.times(t),u),r=r.times(++s),o=a.plus(We(i,r,u)),xe(o.d).slice(0,u)===xe(a.d).slice(0,u)){for(;c--;)a=vt(a.times(a),u);return l.precision=f,e==null?(bt=!0,vt(a,f)):a}a=o}}function At(t){for(var e=t.e*gt,r=t.d[0];r>=10;r/=10)e++;return e}function Fs(t,e,r){if(e>t.LN10.sd())throw bt=!0,r&&(t.precision=r),Error(le+"LN10 precision limit exceeded");return vt(new t(t.LN10),e)}function tr(t){for(var e="";t--;)e+="0";return e}function Vi(t,e){var r,n,i,a,o,u,s,c,l,f=1,h=10,p=t,d=p.d,v=p.constructor,y=v.precision;if(p.s<1)throw Error(le+(p.s?"NaN":"-Infinity"));if(p.eq(Qt))return new v(0);if(e==null?(bt=!1,c=y):c=e,p.eq(10))return e==null&&(bt=!0),Fs(v,c);if(c+=h,v.precision=c,r=xe(d),n=r.charAt(0),a=At(p),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)p=p.times(t),r=xe(p.d),n=r.charAt(0),f++;a=At(p),n>1?(p=new v("0."+r),a++):p=new v(n+"."+r.slice(1))}else return s=Fs(v,c+2,y).times(a+""),p=Vi(new v(n+"."+r.slice(1)),c-h).plus(s),v.precision=y,e==null?(bt=!0,vt(p,y)):p;for(u=o=p=We(p.minus(Qt),p.plus(Qt),c),l=vt(p.times(p),c),i=3;;){if(o=vt(o.times(l),c),s=u.plus(We(o,new v(i),c)),xe(s.d).slice(0,c)===xe(u.d).slice(0,c))return u=u.times(2),a!==0&&(u=u.plus(Fs(v,c+2,y).times(a+""))),u=We(u,new v(f),c),v.precision=y,e==null?(bt=!0,vt(u,y)):u;u=s,i+=2}}function Ad(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;e.charCodeAt(n)===48;)++n;for(i=e.length;e.charCodeAt(i-1)===48;)--i;if(e=e.slice(n,i),e){if(i-=n,r=r-n-1,t.e=ui(r/gt),t.d=[],n=(r+1)%gt,r<0&&(n+=gt),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=gt;n<i;)t.d.push(+e.slice(n,n+=gt));e=e.slice(n),n=gt-e.length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),bt&&(t.e>Co||t.e<-Co))throw Error(Wf+r)}else t.s=0,t.e=0,t.d=[0];return t}function vt(t,e,r){var n,i,a,o,u,s,c,l,f=t.d;for(o=1,a=f[0];a>=10;a/=10)o++;if(n=e-o,n<0)n+=gt,i=e,c=f[l=0];else{if(l=Math.ceil((n+1)/gt),a=f.length,l>=a)return t;for(c=a=f[l],o=1;a>=10;a/=10)o++;n%=gt,i=n-gt+o}if(r!==void 0&&(a=$r(10,o-i-1),u=c/a%10|0,s=e<0||f[l+1]!==void 0||c%a,s=r<4?(u||s)&&(r==0||r==(t.s<0?3:2)):u>5||u==5&&(r==4||s||r==6&&(n>0?i>0?c/$r(10,o-i):0:f[l-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return s?(a=At(t),f.length=1,e=e-a-1,f[0]=$r(10,(gt-e%gt)%gt),t.e=ui(-e/gt)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(n==0?(f.length=l,a=1,l--):(f.length=l+1,a=$r(10,gt-n),f[l]=i>0?(c/$r(10,o-i)%$r(10,i)|0)*a:0),s)for(;;)if(l==0){(f[0]+=a)==jt&&(f[0]=1,++t.e);break}else{if(f[l]+=a,f[l]!=jt)break;f[l--]=0,a=1}for(n=f.length;f[--n]===0;)f.pop();if(bt&&(t.e>Co||t.e<-Co))throw Error(Wf+At(t));return t}function sb(t,e){var r,n,i,a,o,u,s,c,l,f,h=t.constructor,p=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),bt?vt(e,p):e;if(s=t.d,f=e.d,n=e.e,c=t.e,s=s.slice(),o=c-n,o){for(l=o<0,l?(r=s,o=-o,u=f.length):(r=f,n=c,u=s.length),i=Math.max(Math.ceil(p/gt),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=s.length,u=f.length,l=i<u,l&&(u=i),i=0;i<u;i++)if(s[i]!=f[i]){l=s[i]<f[i];break}o=0}for(l&&(r=s,s=f,f=r,e.s=-e.s),u=s.length,i=f.length-u;i>0;--i)s[u++]=0;for(i=f.length;i>o;){if(s[--i]<f[i]){for(a=i;a&&s[--a]===0;)s[a]=jt-1;--s[a],s[i]+=jt}s[i]-=f[i]}for(;s[--u]===0;)s.pop();for(;s[0]===0;s.shift())--n;return s[0]?(e.d=s,e.e=n,bt?vt(e,p):e):new h(0)}function Wr(t,e,r){var n,i=At(t),a=xe(t.d),o=a.length;return e?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+tr(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+tr(-i-1)+a,r&&(n=r-o)>0&&(a+=tr(n))):i>=o?(a+=tr(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+tr(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=tr(n))),t.s<0?"-"+a:a}function Pd(t,e){if(t.length>e)return t.length=e,!0}function cb(t){var e,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Rr+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Ad(o,a.toString())}else if(typeof a!="string")throw Error(Rr+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,CD.test(a))Ad(o,a);else throw Error(Rr+a)}if(i.prototype=H,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=cb,i.config=i.set=kD,t===void 0&&(t={}),t)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],e=0;e<n.length;)t.hasOwnProperty(r=n[e++])||(t[r]=this[r]);return i.config(t),i}function kD(t){if(!t||typeof t!="object")throw Error(le+"Object expected");var e,r,n,i=["precision",1,oi,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if((n=t[r=i[e]])!==void 0)if(ui(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(Rr+r+": "+n);if((n=t[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Rr+r+": "+n);return this}var Uf=cb(MD);Qt=new Uf(1);const lt=Uf;function ID(t){return LD(t)||RD(t)||DD(t)||ND()}function ND(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DD(t,e){if(t){if(typeof t=="string")return Lc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lc(t,e)}}function RD(t){if(typeof Symbol<"u"&&Symbol.iterator in Object(t))return Array.from(t)}function LD(t){if(Array.isArray(t))return Lc(t)}function Lc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var BD=function(e){return e},lb={},fb=function(e){return e===lb},Sd=function(e){return function r(){return arguments.length===0||arguments.length===1&&fb(arguments.length<=0?void 0:arguments[0])?r:e.apply(void 0,arguments)}},zD=function t(e,r){return e===1?r:Sd(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==lb}).length;return o>=e?r.apply(void 0,i):t(e-o,Sd(function(){for(var u=arguments.length,s=new Array(u),c=0;c<u;c++)s[c]=arguments[c];var l=i.map(function(f){return fb(f)?s.shift():f});return r.apply(void 0,ID(l).concat(s))}))})},ts=function(e){return zD(e.length,e)},Bc=function(e,r){for(var n=[],i=e;i<r;++i)n[i-e]=i;return n},FD=ts(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(r){return e[r]}).map(t)}),WD=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];if(!r.length)return BD;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,s){return s(u)},a.apply(void 0,arguments))}},zc=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},hb=function(e){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,s){return u===r[s]})||(r=a,n=e.apply(void 0,a)),n}};function UD(t){var e;return t===0?e=1:e=Math.floor(new lt(t).abs().log(10).toNumber())+1,e}function KD(t,e,r){for(var n=new lt(t),i=0,a=[];n.lt(e)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var qD=ts(function(t,e,r){var n=+t,i=+e;return n+r*(i-n)}),HD=ts(function(t,e,r){var n=e-+t;return n=n||1/0,(r-t)/n}),GD=ts(function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))});const es={rangeStep:KD,getDigitCount:UD,interpolateNumber:qD,uninterpolateNumber:HD,uninterpolateTruncation:GD};function Fc(t){return YD(t)||XD(t)||pb(t)||VD()}function VD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XD(t){if(typeof Symbol<"u"&&Symbol.iterator in Object(t))return Array.from(t)}function YD(t){if(Array.isArray(t))return Wc(t)}function Xi(t,e){return QD(t)||JD(t,e)||pb(t,e)||ZD()}function ZD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pb(t,e){if(t){if(typeof t=="string")return Wc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wc(t,e)}}function Wc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function JD(t,e){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(t)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=t[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(e&&r.length===e));n=!0);}catch(s){i=!0,a=s}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function QD(t){if(Array.isArray(t))return t}function db(t){var e=Xi(t,2),r=e[0],n=e[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function vb(t,e,r){if(t.lte(0))return new lt(0);var n=es.getDigitCount(t.toNumber()),i=new lt(10).pow(n),a=t.div(i),o=n!==1?.05:.1,u=new lt(Math.ceil(a.div(o).toNumber())).add(r).mul(o),s=u.mul(i);return e?s:new lt(Math.ceil(s))}function tR(t,e,r){var n=1,i=new lt(t);if(!i.isint()&&r){var a=Math.abs(t);a<1?(n=new lt(10).pow(es.getDigitCount(t)-1),i=new lt(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new lt(Math.floor(t)))}else t===0?i=new lt(Math.floor((e-1)/2)):r||(i=new lt(Math.floor(t)));var o=Math.floor((e-1)/2),u=WD(FD(function(s){return i.add(new lt(s-o).mul(n)).toNumber()}),Bc);return u(0,e)}function yb(t,e,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new lt(0),tickMin:new lt(0),tickMax:new lt(0)};var a=vb(new lt(e).sub(t).div(r-1),n,i),o;t<=0&&e>=0?o=new lt(0):(o=new lt(t).add(e).div(2),o=o.sub(new lt(o).mod(a)));var u=Math.ceil(o.sub(t).div(a).toNumber()),s=Math.ceil(new lt(e).sub(o).div(a).toNumber()),c=u+s+1;return c>r?yb(t,e,r,n,i+1):(c<r&&(s=e>0?s+(r-c):s,u=e>0?u:u+(r-c)),{step:a,tickMin:o.sub(new lt(u).mul(a)),tickMax:o.add(new lt(s).mul(a))})}function eR(t){var e=Xi(t,2),r=e[0],n=e[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=db([r,n]),s=Xi(u,2),c=s[0],l=s[1];if(c===-1/0||l===1/0){var f=l===1/0?[c].concat(Fc(Bc(0,i-1).map(function(){return 1/0}))):[].concat(Fc(Bc(0,i-1).map(function(){return-1/0})),[l]);return r>n?zc(f):f}if(c===l)return tR(c,i,a);var h=yb(c,l,o,a),p=h.step,d=h.tickMin,v=h.tickMax,y=es.rangeStep(d,v.add(new lt(.1).mul(p)),p);return r>n?zc(y):y}function rR(t,e){var r=Xi(t,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=db([n,i]),u=Xi(o,2),s=u[0],c=u[1];if(s===-1/0||c===1/0)return[n,i];if(s===c)return[s];var l=Math.max(e,2),f=vb(new lt(c).sub(s).div(l-1),a,0),h=[].concat(Fc(es.rangeStep(new lt(s),new lt(c).sub(new lt(.99).mul(f)),f)),[c]);return n>i?zc(h):h}var nR=hb(eR),iR=hb(rR),aR="Invariant failed";function Ur(t,e){throw new Error(aR)}var oR=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Sn(t){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sn(t)}function ko(){return ko=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ko.apply(this,arguments)}function uR(t,e){return fR(t)||lR(t,e)||cR(t,e)||sR()}function sR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cR(t,e){if(t){if(typeof t=="string")return $d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $d(t,e)}}function $d(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function lR(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function fR(t){if(Array.isArray(t))return t}function hR(t,e){if(t==null)return{};var r=pR(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function pR(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function dR(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function vR(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,bb(n.key),n)}}function yR(t,e,r){return e&&vR(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function mR(t,e,r){return e=Io(e),gR(t,mb()?Reflect.construct(e,r||[],Io(t).constructor):e.apply(t,r))}function gR(t,e){if(e&&(Sn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return bR(t)}function bR(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function mb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mb=function(){return!!t})()}function Io(t){return Io=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Io(t)}function xR(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Uc(t,e)}function Uc(t,e){return Uc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Uc(t,e)}function gb(t,e,r){return e=bb(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function bb(t){var e=_R(t,"string");return Sn(e)=="symbol"?e:e+""}function _R(t,e){if(Sn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var si=function(t){function e(){return dR(this,e),mR(this,e,arguments)}return xR(e,t),yR(e,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,s=n.data,c=n.dataPointFormatter,l=n.xAxis,f=n.yAxis,h=hR(n,oR),p=q(h,!1);this.props.direction==="x"&&l.type!=="number"&&Ur();var d=s.map(function(v){var y=c(v,u),g=y.x,b=y.y,_=y.value,w=y.errorVal;if(!w)return null;var m=[],x,O;if(Array.isArray(w)){var S=uR(w,2);x=S[0],O=S[1]}else x=O=w;if(a==="vertical"){var $=l.scale,k=b+i,T=k+o,M=k-o,C=$(_-x),P=$(_+O);m.push({x1:P,y1:T,x2:P,y2:M}),m.push({x1:C,y1:k,x2:P,y2:k}),m.push({x1:C,y1:T,x2:C,y2:M})}else if(a==="horizontal"){var j=f.scale,E=g+i,I=E-o,N=E+o,R=j(_-x),B=j(_+O);m.push({x1:I,y1:B,x2:N,y2:B}),m.push({x1:E,y1:R,x2:E,y2:B}),m.push({x1:I,y1:R,x2:N,y2:R})}return A.createElement(Q,ko({className:"recharts-errorBar",key:"bar-".concat(m.map(function(U){return"".concat(U.x1,"-").concat(U.x2,"-").concat(U.y1,"-").concat(U.y2)}))},p),m.map(function(U){return A.createElement("line",ko({},U,{key:"line-".concat(U.x1,"-").concat(U.x2,"-").concat(U.y1,"-").concat(U.y2)}))}))});return A.createElement(Q,{className:"recharts-errorBars"},d)}}])}(A.Component);gb(si,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});gb(si,"displayName","ErrorBar");function Yi(t){"@babel/helpers - typeof";return Yi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yi(t)}function Ed(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function _r(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ed(Object(r),!0).forEach(function(n){wR(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ed(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function wR(t,e,r){return e=OR(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function OR(t){var e=AR(t,"string");return Yi(e)=="symbol"?e:e+""}function AR(t,e){if(Yi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Yi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var xb=function(e){var r=e.children,n=e.formattedGraphicalItems,i=e.legendWidth,a=e.legendContent,o=Jt(r,pn);if(!o)return null;var u=pn.defaultProps,s=u!==void 0?_r(_r({},u),o.props):{},c;return o.props&&o.props.payload?c=o.props&&o.props.payload:a==="children"?c=(n||[]).reduce(function(l,f){var h=f.item,p=f.props,d=p.sectors||p.data||[];return l.concat(d.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):c=(n||[]).map(function(l){var f=l.item,h=f.type.defaultProps,p=h!==void 0?_r(_r({},h),f.props):{},d=p.dataKey,v=p.name,y=p.legendType,g=p.hide;return{inactive:g,dataKey:d,type:s.iconType||y||"square",color:Kf(f),value:v||d,payload:p}}),_r(_r(_r({},s),pn.getWithHeight(o,i)),{},{payload:c,item:o})};function Zi(t){"@babel/helpers - typeof";return Zi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zi(t)}function Td(t){return ER(t)||$R(t)||SR(t)||PR()}function PR(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SR(t,e){if(t){if(typeof t=="string")return Kc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Kc(t,e)}}function $R(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ER(t){if(Array.isArray(t))return Kc(t)}function Kc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function jd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function wt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?jd(Object(r),!0).forEach(function(n){yn(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jd(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function yn(t,e,r){return e=TR(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function TR(t){var e=jR(t,"string");return Zi(e)=="symbol"?e:e+""}function jR(t,e){if(Zi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Zi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function dt(t,e,r){return X(t)||X(e)?r:Et(e)?te(t,e,r):V(e)?e(t):r}function Ti(t,e,r,n){var i=$D(t,function(u){return dt(u,e)});if(r==="number"){var a=i.filter(function(u){return F(u)||parseFloat(u)});return a.length?[Qu(a),rr(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!X(u)}):i;return o.map(function(u){return Et(u)||u instanceof Date?u:""})}var MR=function(e){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var s=a.range,c=0;c<u;c++){var l=c>0?i[c-1].coordinate:i[u-1].coordinate,f=i[c].coordinate,h=c>=u-1?i[0].coordinate:i[c+1].coordinate,p=void 0;if(Ut(f-l)!==Ut(h-f)){var d=[];if(Ut(h-f)===Ut(s[1]-s[0])){p=h;var v=f+s[1]-s[0];d[0]=Math.min(v,(v+l)/2),d[1]=Math.max(v,(v+l)/2)}else{p=l;var y=h+s[1]-s[0];d[0]=Math.min(f,(y+f)/2),d[1]=Math.max(f,(y+f)/2)}var g=[Math.min(f,(p+f)/2),Math.max(f,(p+f)/2)];if(e>g[0]&&e<=g[1]||e>=d[0]&&e<=d[1]){o=i[c].index;break}}else{var b=Math.min(l,h),_=Math.max(l,h);if(e>(b+f)/2&&e<=(_+f)/2){o=i[c].index;break}}}else for(var w=0;w<u;w++)if(w===0&&e<=(n[w].coordinate+n[w+1].coordinate)/2||w>0&&w<u-1&&e>(n[w].coordinate+n[w-1].coordinate)/2&&e<=(n[w].coordinate+n[w+1].coordinate)/2||w===u-1&&e>(n[w].coordinate+n[w-1].coordinate)/2){o=n[w].index;break}return o},Kf=function(e){var r,n=e,i=n.type.displayName,a=(r=e.type)!==null&&r!==void 0&&r.defaultProps?wt(wt({},e.type.defaultProps),e.props):e.props,o=a.stroke,u=a.fill,s;switch(i){case"Line":s=o;break;case"Area":case"Radar":s=o&&o!=="none"?o:u;break;default:s=u;break}return s},CR=function(e){var r=e.barSize,n=e.totalSize,i=e.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),s=0,c=u.length;s<c;s++)for(var l=a[u[s]].stackGroups,f=Object.keys(l),h=0,p=f.length;h<p;h++){var d=l[f[h]],v=d.items,y=d.cateAxisId,g=v.filter(function(O){return Fe(O.type).indexOf("Bar")>=0});if(g&&g.length){var b=g[0].type.defaultProps,_=b!==void 0?wt(wt({},b),g[0].props):g[0].props,w=_.barSize,m=_[y];o[m]||(o[m]=[]);var x=X(w)?r:w;o[m].push({item:g[0],stackList:g.slice(1),barSize:X(x)?void 0:Kt(x,n,0)})}}return o},kR=function(e){var r=e.barGap,n=e.barCategoryGap,i=e.bandSize,a=e.sizeList,o=a===void 0?[]:a,u=e.maxBarSize,s=o.length;if(s<1)return null;var c=Kt(r,i,0,!0),l,f=[];if(o[0].barSize===+o[0].barSize){var h=!1,p=i/s,d=o.reduce(function(w,m){return w+m.barSize||0},0);d+=(s-1)*c,d>=i&&(d-=(s-1)*c,c=0),d>=i&&p>0&&(h=!0,p*=.9,d=s*p);var v=(i-d)/2>>0,y={offset:v-c,size:0};l=o.reduce(function(w,m){var x={item:m.item,position:{offset:y.offset+y.size+c,size:h?p:m.barSize}},O=[].concat(Td(w),[x]);return y=O[O.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){O.push({item:S,position:y})}),O},f)}else{var g=Kt(n,i,0,!0);i-2*g-(s-1)*c<=0&&(c=0);var b=(i-2*g-(s-1)*c)/s;b>1&&(b>>=0);var _=u===+u?Math.min(b,u):b;l=o.reduce(function(w,m,x){var O=[].concat(Td(w),[{item:m.item,position:{offset:g+(b+c)*x+(b-_)/2,size:_}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){O.push({item:S,position:O[O.length-1].position})}),O},f)}return l},IR=function(e,r,n,i){var a=n.children,o=n.width,u=n.margin,s=o-(u.left||0)-(u.right||0),c=xb({children:a,legendWidth:s});if(c){var l=i||{},f=l.width,h=l.height,p=c.align,d=c.verticalAlign,v=c.layout;if((v==="vertical"||v==="horizontal"&&d==="middle")&&p!=="center"&&F(e[p]))return wt(wt({},e),{},yn({},p,e[p]+(f||0)));if((v==="horizontal"||v==="vertical"&&p==="center")&&d!=="middle"&&F(e[d]))return wt(wt({},e),{},yn({},d,e[d]+(h||0)))}return e},NR=function(e,r,n){return X(r)?!0:e==="horizontal"?r==="yAxis":e==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},_b=function(e,r,n,i,a){var o=r.props.children,u=Ht(o,si).filter(function(c){return NR(i,a,c.props.direction)});if(u&&u.length){var s=u.map(function(c){return c.props.dataKey});return e.reduce(function(c,l){var f=dt(l,n);if(X(f))return c;var h=Array.isArray(f)?[Qu(f),rr(f)]:[f,f],p=s.reduce(function(d,v){var y=dt(l,v,0),g=h[0]-Math.abs(Array.isArray(y)?y[0]:y),b=h[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(g,d[0]),Math.max(b,d[1])]},[1/0,-1/0]);return[Math.min(p[0],c[0]),Math.max(p[1],c[1])]},[1/0,-1/0])}return null},DR=function(e,r,n,i,a){var o=r.map(function(u){return _b(e,u,n,a,i)}).filter(function(u){return!X(u)});return o&&o.length?o.reduce(function(u,s){return[Math.min(u[0],s[0]),Math.max(u[1],s[1])]},[1/0,-1/0]):null},wb=function(e,r,n,i,a){var o=r.map(function(s){var c=s.props.dataKey;return n==="number"&&c&&_b(e,s,c,i)||Ti(e,c,n,a)});if(n==="number")return o.reduce(function(s,c){return[Math.min(s[0],c[0]),Math.max(s[1],c[1])]},[1/0,-1/0]);var u={};return o.reduce(function(s,c){for(var l=0,f=c.length;l<f;l++)u[c[l]]||(u[c[l]]=!0,s.push(c[l]));return s},[])},Ob=function(e,r){return e==="horizontal"&&r==="xAxis"||e==="vertical"&&r==="yAxis"||e==="centric"&&r==="angleAxis"||e==="radial"&&r==="radiusAxis"},Ab=function(e,r,n,i){if(i)return e.map(function(s){return s.coordinate});var a,o,u=e.map(function(s){return s.coordinate===r&&(a=!0),s.coordinate===n&&(o=!0),s.coordinate});return a||u.push(r),o||u.push(n),u},Be=function(e,r,n){if(!e)return null;var i=e.scale,a=e.duplicateDomain,o=e.type,u=e.range,s=e.realScaleType==="scaleBand"?i.bandwidth()/2:2,c=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/s:0;if(c=e.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ut(u[0]-u[1])*2*c:c,r&&(e.ticks||e.niceTicks)){var l=(e.ticks||e.niceTicks).map(function(f){var h=a?a.indexOf(f):f;return{coordinate:i(h)+c,value:f,offset:c}});return l.filter(function(f){return!ei(f.coordinate)})}return e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(f,h){return{coordinate:i(f)+c,value:f,index:h,offset:c}}):i.ticks&&!n?i.ticks(e.tickCount).map(function(f){return{coordinate:i(f)+c,value:f,offset:c}}):i.domain().map(function(f,h){return{coordinate:i(f)+c,value:a?a[f]:f,index:h,offset:c}})},Ws=new WeakMap,Ga=function(e,r){if(typeof r!="function")return e;Ws.has(e)||Ws.set(e,new WeakMap);var n=Ws.get(e);if(n.has(r))return n.get(r);var i=function(){e.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Pb=function(e,r,n){var i=e.scale,a=e.type,o=e.layout,u=e.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Ui(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Eo(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Ei(),realScaleType:"point"}:a==="category"?{scale:Ui(),realScaleType:"band"}:{scale:Eo(),realScaleType:"linear"};if(Lr(i)){var s="scale".concat(Cu(i));return{scale:(Od[s]||Ei)(),realScaleType:Od[s]?s:"point"}}return V(i)?{scale:i}:{scale:Ei(),realScaleType:"point"}},Md=1e-4,Sb=function(e){var r=e.domain();if(!(!r||r.length<=2)){var n=r.length,i=e.range(),a=Math.min(i[0],i[1])-Md,o=Math.max(i[0],i[1])+Md,u=e(r[0]),s=e(r[n-1]);(u<a||u>o||s<a||s>o)&&e.domain([r[0],r[n-1]])}},RR=function(e,r){if(!e)return null;for(var n=0,i=e.length;n<i;n++)if(e[n].item===r)return e[n].position;return null},LR=function(e,r){if(!r||r.length!==2||!F(r[0])||!F(r[1]))return e;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[e[0],e[1]];return(!F(e[0])||e[0]<n)&&(a[0]=n),(!F(e[1])||e[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},BR=function(e){var r=e.length;if(!(r<=0))for(var n=0,i=e[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var s=ei(e[u][n][1])?e[u][n][0]:e[u][n][1];s>=0?(e[u][n][0]=a,e[u][n][1]=a+s,a=e[u][n][1]):(e[u][n][0]=o,e[u][n][1]=o+s,o=e[u][n][1])}},zR=function(e){var r=e.length;if(!(r<=0))for(var n=0,i=e[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=ei(e[o][n][1])?e[o][n][0]:e[o][n][1];u>=0?(e[o][n][0]=a,e[o][n][1]=a+u,a=e[o][n][1]):(e[o][n][0]=0,e[o][n][1]=0)}},FR={sign:BR,expand:XA,none:mn,silhouette:YA,wiggle:ZA,positive:zR},WR=function(e,r,n){var i=r.map(function(u){return u.props.dataKey}),a=FR[n],o=VA().keys(i).value(function(u,s){return+dt(u,s,0)}).order(fc).offset(a);return o(e)},UR=function(e,r,n,i,a,o){if(!e)return null;var u=o?r.reverse():r,s={},c=u.reduce(function(f,h){var p,d=(p=h.type)!==null&&p!==void 0&&p.defaultProps?wt(wt({},h.type.defaultProps),h.props):h.props,v=d.stackId,y=d.hide;if(y)return f;var g=d[n],b=f[g]||{hasStack:!1,stackGroups:{}};if(Et(v)){var _=b.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};_.items.push(h),b.hasStack=!0,b.stackGroups[v]=_}else b.stackGroups[hr("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return wt(wt({},f),{},yn({},g,b))},s),l={};return Object.keys(c).reduce(function(f,h){var p=c[h];if(p.hasStack){var d={};p.stackGroups=Object.keys(p.stackGroups).reduce(function(v,y){var g=p.stackGroups[y];return wt(wt({},v),{},yn({},y,{numericAxisId:n,cateAxisId:i,items:g.items,stackedData:WR(e,g.items,a)}))},d)}return wt(wt({},f),{},yn({},h,p))},l)},$b=function(e,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,s=n||r.scale;if(s!=="auto"&&s!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var c=e.domain();if(!c.length)return null;var l=nR(c,a,u);return e.domain([Qu(l),rr(l)]),{niceTicks:l}}if(a&&i==="number"){var f=e.domain(),h=iR(f,a,u);return{niceTicks:h}}return null};function $n(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,a=t.index,o=t.dataKey;if(e.type==="category"){if(!e.allowDuplicatedCategory&&e.dataKey&&!X(i[e.dataKey])){var u=uo(r,"value",i[e.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var s=dt(i,X(o)?e.dataKey:o);return X(s)?null:e.scale(s)}var Cd=function(e){var r=e.axis,n=e.ticks,i=e.offset,a=e.bandSize,o=e.entry,u=e.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var s=dt(o,r.dataKey,r.domain[u]);return X(s)?null:r.scale(s)-a/2+i},KR=function(e){var r=e.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},qR=function(e,r){var n,i=(n=e.type)!==null&&n!==void 0&&n.defaultProps?wt(wt({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(Et(a)){var o=r[a];if(o){var u=o.items.indexOf(e);return u>=0?o.stackedData[u]:null}}return null},HR=function(e){return e.reduce(function(r,n){return[Qu(n.concat([r[0]]).filter(F)),rr(n.concat([r[1]]).filter(F))]},[1/0,-1/0])},Eb=function(e,r,n){return Object.keys(e).reduce(function(i,a){var o=e[a],u=o.stackedData,s=u.reduce(function(c,l){var f=HR(l.slice(r,n+1));return[Math.min(c[0],f[0]),Math.max(c[1],f[1])]},[1/0,-1/0]);return[Math.min(s[0],i[0]),Math.max(s[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},kd=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Id=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,qc=function(e,r,n){if(V(e))return e(r,n);if(!Array.isArray(e))return r;var i=[];if(F(e[0]))i[0]=n?e[0]:Math.min(e[0],r[0]);else if(kd.test(e[0])){var a=+kd.exec(e[0])[1];i[0]=r[0]-a}else V(e[0])?i[0]=e[0](r[0]):i[0]=r[0];if(F(e[1]))i[1]=n?e[1]:Math.max(e[1],r[1]);else if(Id.test(e[1])){var o=+Id.exec(e[1])[1];i[1]=r[1]+o}else V(e[1])?i[1]=e[1](r[1]):i[1]=r[1];return i},No=function(e,r,n){if(e&&e.scale&&e.scale.bandwidth){var i=e.scale.bandwidth();if(!n||i>0)return i}if(e&&r&&r.length>=2){for(var a=yf(r,function(f){return f.coordinate}),o=1/0,u=1,s=a.length;u<s;u++){var c=a[u],l=a[u-1];o=Math.min((c.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Nd=function(e,r,n){return!e||!e.length||He(e,te(n,"type.defaultProps.domain"))?r:e},Tb=function(e,r){var n=e.type.defaultProps?wt(wt({},e.type.defaultProps),e.props):e.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,s=n.tooltipType,c=n.chartType,l=n.hide;return wt(wt({},q(e,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:Kf(e),value:dt(r,i),type:s,payload:r,chartType:c,hide:l})};function Ji(t){"@babel/helpers - typeof";return Ji=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ji(t)}function Dd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Ie(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Dd(Object(r),!0).forEach(function(n){jb(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dd(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function jb(t,e,r){return e=GR(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function GR(t){var e=VR(t,"string");return Ji(e)=="symbol"?e:e+""}function VR(t,e){if(Ji(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Ji(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function XR(t,e){return QR(t)||JR(t,e)||ZR(t,e)||YR()}function YR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ZR(t,e){if(t){if(typeof t=="string")return Rd(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rd(t,e)}}function Rd(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function JR(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function QR(t){if(Array.isArray(t))return t}var Do=Math.PI/180,tL=function(e){return e*180/Math.PI},ot=function(e,r,n,i){return{x:e+Math.cos(-Do*i)*n,y:r+Math.sin(-Do*i)*n}},Mb=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},Cb=function(e,r,n,i,a){var o=e.width,u=e.height,s=e.startAngle,c=e.endAngle,l=Kt(e.cx,o,o/2),f=Kt(e.cy,u,u/2),h=Mb(o,u,n),p=Kt(e.innerRadius,h,0),d=Kt(e.outerRadius,h,h*.8),v=Object.keys(r);return v.reduce(function(y,g){var b=r[g],_=b.domain,w=b.reversed,m;if(X(b.range))i==="angleAxis"?m=[s,c]:i==="radiusAxis"&&(m=[p,d]),w&&(m=[m[1],m[0]]);else{m=b.range;var x=m,O=XR(x,2);s=O[0],c=O[1]}var S=Pb(b,a),$=S.realScaleType,k=S.scale;k.domain(_).range(m),Sb(k);var T=$b(k,Ie(Ie({},b),{},{realScaleType:$})),M=Ie(Ie(Ie({},b),T),{},{range:m,radius:d,realScaleType:$,scale:k,cx:l,cy:f,innerRadius:p,outerRadius:d,startAngle:s,endAngle:c});return Ie(Ie({},y),{},jb({},g,M))},{})},eL=function(e,r){var n=e.x,i=e.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},rL=function(e,r){var n=e.x,i=e.y,a=r.cx,o=r.cy,u=eL({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var s=(n-a)/u,c=Math.acos(s);return i>o&&(c=2*Math.PI-c),{radius:u,angle:tL(c),angleInRadian:c}},nL=function(e){var r=e.startAngle,n=e.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},iL=function(e,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return e+u*360},Ld=function(e,r){var n=e.x,i=e.y,a=rL({x:n,y:i},r),o=a.radius,u=a.angle,s=r.innerRadius,c=r.outerRadius;if(o<s||o>c)return!1;if(o===0)return!0;var l=nL(r),f=l.startAngle,h=l.endAngle,p=u,d;if(f<=h){for(;p>h;)p-=360;for(;p<f;)p+=360;d=p>=f&&p<=h}else{for(;p>f;)p-=360;for(;p<h;)p+=360;d=p>=h&&p<=f}return d?Ie(Ie({},r),{},{radius:o,angle:iL(p,r)}):null},kb=function(e){return!W.isValidElement(e)&&!V(e)&&typeof e!="boolean"?e.className:""};function Qi(t){"@babel/helpers - typeof";return Qi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qi(t)}var aL=["offset"];function oL(t){return lL(t)||cL(t)||sL(t)||uL()}function uL(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sL(t,e){if(t){if(typeof t=="string")return Hc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hc(t,e)}}function cL(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function lL(t){if(Array.isArray(t))return Hc(t)}function Hc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function fL(t,e){if(t==null)return{};var r=hL(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function hL(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Bd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function $t(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Bd(Object(r),!0).forEach(function(n){pL(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Bd(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function pL(t,e,r){return e=dL(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dL(t){var e=vL(t,"string");return Qi(e)=="symbol"?e:e+""}function vL(t,e){if(Qi(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Qi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ta(){return ta=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ta.apply(this,arguments)}var yL=function(e){var r=e.value,n=e.formatter,i=X(e.children)?r:e.children;return V(n)?n(i):i},mL=function(e,r){var n=Ut(r-e),i=Math.min(Math.abs(r-e),360);return n*i},gL=function(e,r,n){var i=e.position,a=e.viewBox,o=e.offset,u=e.className,s=a,c=s.cx,l=s.cy,f=s.innerRadius,h=s.outerRadius,p=s.startAngle,d=s.endAngle,v=s.clockWise,y=(f+h)/2,g=mL(p,d),b=g>=0?1:-1,_,w;i==="insideStart"?(_=p+b*o,w=v):i==="insideEnd"?(_=d-b*o,w=!v):i==="end"&&(_=d+b*o,w=v),w=g<=0?w:!w;var m=ot(c,l,y,_),x=ot(c,l,y,_+(w?1:-1)*359),O="M".concat(m.x,",").concat(m.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(w?0:1,`,
    `).concat(x.x,",").concat(x.y),S=X(e.id)?hr("recharts-radial-line-"):e.id;return A.createElement("text",ta({},n,{dominantBaseline:"central",className:J("recharts-radial-bar-label",u)}),A.createElement("defs",null,A.createElement("path",{id:S,d:O})),A.createElement("textPath",{xlinkHref:"#".concat(S)},r))},bL=function(e){var r=e.viewBox,n=e.offset,i=e.position,a=r,o=a.cx,u=a.cy,s=a.innerRadius,c=a.outerRadius,l=a.startAngle,f=a.endAngle,h=(l+f)/2;if(i==="outside"){var p=ot(o,u,c+n,h),d=p.x,v=p.y;return{x:d,y:v,textAnchor:d>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(s+c)/2,g=ot(o,u,y,h),b=g.x,_=g.y;return{x:b,y:_,textAnchor:"middle",verticalAnchor:"middle"}},xL=function(e){var r=e.viewBox,n=e.parentViewBox,i=e.offset,a=e.position,o=r,u=o.x,s=o.y,c=o.width,l=o.height,f=l>=0?1:-1,h=f*i,p=f>0?"end":"start",d=f>0?"start":"end",v=c>=0?1:-1,y=v*i,g=v>0?"end":"start",b=v>0?"start":"end";if(a==="top"){var _={x:u+c/2,y:s-f*i,textAnchor:"middle",verticalAnchor:p};return $t($t({},_),n?{height:Math.max(s-n.y,0),width:c}:{})}if(a==="bottom"){var w={x:u+c/2,y:s+l+h,textAnchor:"middle",verticalAnchor:d};return $t($t({},w),n?{height:Math.max(n.y+n.height-(s+l),0),width:c}:{})}if(a==="left"){var m={x:u-y,y:s+l/2,textAnchor:g,verticalAnchor:"middle"};return $t($t({},m),n?{width:Math.max(m.x-n.x,0),height:l}:{})}if(a==="right"){var x={x:u+c+y,y:s+l/2,textAnchor:b,verticalAnchor:"middle"};return $t($t({},x),n?{width:Math.max(n.x+n.width-x.x,0),height:l}:{})}var O=n?{width:c,height:l}:{};return a==="insideLeft"?$t({x:u+y,y:s+l/2,textAnchor:b,verticalAnchor:"middle"},O):a==="insideRight"?$t({x:u+c-y,y:s+l/2,textAnchor:g,verticalAnchor:"middle"},O):a==="insideTop"?$t({x:u+c/2,y:s+h,textAnchor:"middle",verticalAnchor:d},O):a==="insideBottom"?$t({x:u+c/2,y:s+l-h,textAnchor:"middle",verticalAnchor:p},O):a==="insideTopLeft"?$t({x:u+y,y:s+h,textAnchor:b,verticalAnchor:d},O):a==="insideTopRight"?$t({x:u+c-y,y:s+h,textAnchor:g,verticalAnchor:d},O):a==="insideBottomLeft"?$t({x:u+y,y:s+l-h,textAnchor:b,verticalAnchor:p},O):a==="insideBottomRight"?$t({x:u+c-y,y:s+l-h,textAnchor:g,verticalAnchor:p},O):Zn(a)&&(F(a.x)||jr(a.x))&&(F(a.y)||jr(a.y))?$t({x:u+Kt(a.x,c),y:s+Kt(a.y,l),textAnchor:"end",verticalAnchor:"end"},O):$t({x:u+c/2,y:s+l/2,textAnchor:"middle",verticalAnchor:"middle"},O)},_L=function(e){return"cx"in e&&F(e.cx)};function Mt(t){var e=t.offset,r=e===void 0?5:e,n=fL(t,aL),i=$t({offset:r},n),a=i.viewBox,o=i.position,u=i.value,s=i.children,c=i.content,l=i.className,f=l===void 0?"":l,h=i.textBreakAll;if(!a||X(u)&&X(s)&&!W.isValidElement(c)&&!V(c))return null;if(W.isValidElement(c))return W.cloneElement(c,i);var p;if(V(c)){if(p=W.createElement(c,i),W.isValidElement(p))return p}else p=yL(i);var d=_L(a),v=q(i,!0);if(d&&(o==="insideStart"||o==="insideEnd"||o==="end"))return gL(i,p,v);var y=d?bL(i):xL(i);return A.createElement(Br,ta({className:J("recharts-label",f)},v,y,{breakAll:h}),p)}Mt.displayName="Label";var Ib=function(e){var r=e.cx,n=e.cy,i=e.angle,a=e.startAngle,o=e.endAngle,u=e.r,s=e.radius,c=e.innerRadius,l=e.outerRadius,f=e.x,h=e.y,p=e.top,d=e.left,v=e.width,y=e.height,g=e.clockWise,b=e.labelViewBox;if(b)return b;if(F(v)&&F(y)){if(F(f)&&F(h))return{x:f,y:h,width:v,height:y};if(F(p)&&F(d))return{x:p,y:d,width:v,height:y}}return F(f)&&F(h)?{x:f,y:h,width:0,height:0}:F(r)&&F(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:c||0,outerRadius:l||s||u||0,clockWise:g}:e.viewBox?e.viewBox:{}},wL=function(e,r){return e?e===!0?A.createElement(Mt,{key:"label-implicit",viewBox:r}):Et(e)?A.createElement(Mt,{key:"label-implicit",viewBox:r,value:e}):W.isValidElement(e)?e.type===Mt?W.cloneElement(e,{key:"label-implicit",viewBox:r}):A.createElement(Mt,{key:"label-implicit",content:e,viewBox:r}):V(e)?A.createElement(Mt,{key:"label-implicit",content:e,viewBox:r}):Zn(e)?A.createElement(Mt,ta({viewBox:r},e,{key:"label-implicit"})):null:null},OL=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&n&&!e.label)return null;var i=e.children,a=Ib(e),o=Ht(i,Mt).map(function(s,c){return W.cloneElement(s,{viewBox:r||a,key:"label-".concat(c)})});if(!n)return o;var u=wL(e.label,r||a);return[u].concat(oL(o))};Mt.parseViewBox=Ib;Mt.renderCallByParent=OL;function AL(t){var e=t==null?0:t.length;return e?t[e-1]:void 0}var PL=AL;const Nb=ft(PL);function ea(t){"@babel/helpers - typeof";return ea=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ea(t)}var SL=["valueAccessor"],$L=["data","dataKey","clockWise","id","textBreakAll"];function EL(t){return CL(t)||ML(t)||jL(t)||TL()}function TL(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jL(t,e){if(t){if(typeof t=="string")return Gc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gc(t,e)}}function ML(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function CL(t){if(Array.isArray(t))return Gc(t)}function Gc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ro(){return Ro=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ro.apply(this,arguments)}function zd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Fd(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?zd(Object(r),!0).forEach(function(n){kL(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zd(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function kL(t,e,r){return e=IL(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function IL(t){var e=NL(t,"string");return ea(e)=="symbol"?e:e+""}function NL(t,e){if(ea(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ea(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Wd(t,e){if(t==null)return{};var r=DL(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function DL(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var RL=function(e){return Array.isArray(e.value)?Nb(e.value):e.value};function ce(t){var e=t.valueAccessor,r=e===void 0?RL:e,n=Wd(t,SL),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,s=n.textBreakAll,c=Wd(n,$L);return!i||!i.length?null:A.createElement(Q,{className:"recharts-label-list"},i.map(function(l,f){var h=X(a)?r(l,f):dt(l&&l.payload,a),p=X(u)?{}:{id:"".concat(u,"-").concat(f)};return A.createElement(Mt,Ro({},q(l,!0),c,p,{parentViewBox:l.parentViewBox,value:h,textBreakAll:s,viewBox:Mt.parseViewBox(X(o)?l:Fd(Fd({},l),{},{clockWise:o})),key:"label-".concat(f),index:f}))}))}ce.displayName="LabelList";function LL(t,e){return t?t===!0?A.createElement(ce,{key:"labelList-implicit",data:e}):A.isValidElement(t)||V(t)?A.createElement(ce,{key:"labelList-implicit",data:e,content:t}):Zn(t)?A.createElement(ce,Ro({data:e},t,{key:"labelList-implicit"})):null:null}function BL(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&r&&!t.label)return null;var n=t.children,i=Ht(n,ce).map(function(o,u){return W.cloneElement(o,{data:e,key:"labelList-".concat(u)})});if(!r)return i;var a=LL(t.label,e);return[a].concat(EL(i))}ce.renderCallByParent=BL;function ra(t){"@babel/helpers - typeof";return ra=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ra(t)}function Vc(){return Vc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Vc.apply(this,arguments)}function Ud(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Kd(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ud(Object(r),!0).forEach(function(n){zL(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ud(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function zL(t,e,r){return e=FL(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function FL(t){var e=WL(t,"string");return ra(e)=="symbol"?e:e+""}function WL(t,e){if(ra(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ra(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var UL=function(e,r){var n=Ut(r-e),i=Math.min(Math.abs(r-e),359.999);return n*i},Va=function(e){var r=e.cx,n=e.cy,i=e.radius,a=e.angle,o=e.sign,u=e.isExternal,s=e.cornerRadius,c=e.cornerIsExternal,l=s*(u?1:-1)+i,f=Math.asin(s/l)/Do,h=c?a:a+o*f,p=ot(r,n,l,h),d=ot(r,n,i,h),v=c?a-o*f:a,y=ot(r,n,l*Math.cos(f*Do),v);return{center:p,circleTangency:d,lineTangency:y,theta:f}},Db=function(e){var r=e.cx,n=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.startAngle,u=e.endAngle,s=UL(o,u),c=o+s,l=ot(r,n,a,o),f=ot(r,n,a,c),h="M ".concat(l.x,",").concat(l.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(s)>180),",").concat(+(o>c),`,
    `).concat(f.x,",").concat(f.y,`
  `);if(i>0){var p=ot(r,n,i,o),d=ot(r,n,i,c);h+="L ".concat(d.x,",").concat(d.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(s)>180),",").concat(+(o<=c),`,
            `).concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},KL=function(e){var r=e.cx,n=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.cornerRadius,u=e.forceCornerRadius,s=e.cornerIsExternal,c=e.startAngle,l=e.endAngle,f=Ut(l-c),h=Va({cx:r,cy:n,radius:a,angle:c,sign:f,cornerRadius:o,cornerIsExternal:s}),p=h.circleTangency,d=h.lineTangency,v=h.theta,y=Va({cx:r,cy:n,radius:a,angle:l,sign:-f,cornerRadius:o,cornerIsExternal:s}),g=y.circleTangency,b=y.lineTangency,_=y.theta,w=s?Math.abs(c-l):Math.abs(c-l)-v-_;if(w<0)return u?"M ".concat(d.x,",").concat(d.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Db({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:c,endAngle:l});var m="M ".concat(d.x,",").concat(d.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(b.x,",").concat(b.y,`
  `);if(i>0){var x=Va({cx:r,cy:n,radius:i,angle:c,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),O=x.circleTangency,S=x.lineTangency,$=x.theta,k=Va({cx:r,cy:n,radius:i,angle:l,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),T=k.circleTangency,M=k.lineTangency,C=k.theta,P=s?Math.abs(c-l):Math.abs(c-l)-$-C;if(P<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(M.x,",").concat(M.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(T.x,",").concat(T.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(P>180),",").concat(+(f>0),",").concat(O.x,",").concat(O.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(S.x,",").concat(S.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},qL={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Rb=function(e){var r=Kd(Kd({},qL),e),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,s=r.forceCornerRadius,c=r.cornerIsExternal,l=r.startAngle,f=r.endAngle,h=r.className;if(o<a||l===f)return null;var p=J("recharts-sector",h),d=o-a,v=Kt(u,d,0,!0),y;return v>0&&Math.abs(l-f)<360?y=KL({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,d/2),forceCornerRadius:s,cornerIsExternal:c,startAngle:l,endAngle:f}):y=Db({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:l,endAngle:f}),A.createElement("path",Vc({},q(r,!0),{className:p,d:y,role:"img"}))};function na(t){"@babel/helpers - typeof";return na=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},na(t)}function Xc(){return Xc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xc.apply(this,arguments)}function qd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Hd(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?qd(Object(r),!0).forEach(function(n){HL(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qd(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function HL(t,e,r){return e=GL(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function GL(t){var e=VL(t,"string");return na(e)=="symbol"?e:e+""}function VL(t,e){if(na(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(na(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Gd={curveBasisClosed:RA,curveBasisOpen:LA,curveBasis:DA,curveBumpX:Qy,curveBumpY:tm,curveLinearClosed:BA,curveLinear:Iu,curveMonotoneX:zA,curveMonotoneY:FA,curveNatural:WA,curveStep:UA,curveStepAfter:qA,curveStepBefore:KA},Xa=function(e){return e.x===+e.x&&e.y===+e.y},mi=function(e){return e.x},gi=function(e){return e.y},XL=function(e,r){if(V(e))return e;var n="curve".concat(Cu(e));return(n==="curveMonotone"||n==="curveBump")&&r?Gd["".concat(n).concat(r==="vertical"?"Y":"X")]:Gd[n]||Iu},YL=function(e){var r=e.type,n=r===void 0?"linear":r,i=e.points,a=i===void 0?[]:i,o=e.baseLine,u=e.layout,s=e.connectNulls,c=s===void 0?!1:s,l=XL(n,u),f=c?a.filter(function(v){return Xa(v)}):a,h;if(Array.isArray(o)){var p=c?o.filter(function(v){return Xa(v)}):o,d=f.map(function(v,y){return Hd(Hd({},v),{},{base:p[y]})});return u==="vertical"?h=La().y(gi).x1(mi).x0(function(v){return v.base.x}):h=La().x(mi).y1(gi).y0(function(v){return v.base.y}),h.defined(Xa).curve(l),h(d)}return u==="vertical"&&F(o)?h=La().y(gi).x1(mi).x0(o):F(o)?h=La().x(mi).y1(gi).y0(o):h=Zy().x(mi).y(gi),h.defined(Xa).curve(l),h(f)},ur=function(e){var r=e.className,n=e.points,i=e.path,a=e.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?YL(e):i;return A.createElement("path",Xc({},q(e,!1),so(e),{className:J("recharts-curve",r),d:o,ref:a}))},ZL=Object.getOwnPropertyNames,JL=Object.getOwnPropertySymbols,QL=Object.prototype.hasOwnProperty;function Vd(t,e){return function(n,i,a){return t(n,i,a)&&e(n,i,a)}}function Ya(t){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return t(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var s=t(r,n,i);return a.delete(r),a.delete(n),s}}function Xd(t){return ZL(t).concat(JL(t))}var tB=Object.hasOwn||function(t,e){return QL.call(t,e)};function Yr(t,e){return t===e||!t&&!e&&t!==t&&e!==e}var eB="__v",rB="__o",nB="_owner",Yd=Object.getOwnPropertyDescriptor,Zd=Object.keys;function iB(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function aB(t,e){return Yr(t.getTime(),e.getTime())}function oB(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function uB(t,e){return t===e}function Jd(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var i=new Array(n),a=t.entries(),o,u,s=0;(o=a.next())&&!o.done;){for(var c=e.entries(),l=!1,f=0;(u=c.next())&&!u.done;){if(i[f]){f++;continue}var h=o.value,p=u.value;if(r.equals(h[0],p[0],s,f,t,e,r)&&r.equals(h[1],p[1],h[0],p[0],t,e,r)){l=i[f]=!0;break}f++}if(!l)return!1;s++}return!0}var sB=Yr;function cB(t,e,r){var n=Zd(t),i=n.length;if(Zd(e).length!==i)return!1;for(;i-- >0;)if(!Lb(t,e,r,n[i]))return!1;return!0}function bi(t,e,r){var n=Xd(t),i=n.length;if(Xd(e).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!Lb(t,e,r,a)||(o=Yd(t,a),u=Yd(e,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function lB(t,e){return Yr(t.valueOf(),e.valueOf())}function fB(t,e){return t.source===e.source&&t.flags===e.flags}function Qd(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var i=new Array(n),a=t.values(),o,u;(o=a.next())&&!o.done;){for(var s=e.values(),c=!1,l=0;(u=s.next())&&!u.done;){if(!i[l]&&r.equals(o.value,u.value,o.value,u.value,t,e,r)){c=i[l]=!0;break}l++}if(!c)return!1}return!0}function hB(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function pB(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function Lb(t,e,r,n){return(n===nB||n===rB||n===eB)&&(t.$$typeof||e.$$typeof)?!0:tB(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var dB="[object Arguments]",vB="[object Boolean]",yB="[object Date]",mB="[object Error]",gB="[object Map]",bB="[object Number]",xB="[object Object]",_B="[object RegExp]",wB="[object Set]",OB="[object String]",AB="[object URL]",PB=Array.isArray,tv=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,ev=Object.assign,SB=Object.prototype.toString.call.bind(Object.prototype.toString);function $B(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,o=t.areNumbersEqual,u=t.areObjectsEqual,s=t.arePrimitiveWrappersEqual,c=t.areRegExpsEqual,l=t.areSetsEqual,f=t.areTypedArraysEqual,h=t.areUrlsEqual;return function(d,v,y){if(d===v)return!0;if(d==null||v==null)return!1;var g=typeof d;if(g!==typeof v)return!1;if(g!=="object")return g==="number"?o(d,v,y):g==="function"?i(d,v,y):!1;var b=d.constructor;if(b!==v.constructor)return!1;if(b===Object)return u(d,v,y);if(PB(d))return e(d,v,y);if(tv!=null&&tv(d))return f(d,v,y);if(b===Date)return r(d,v,y);if(b===RegExp)return c(d,v,y);if(b===Map)return a(d,v,y);if(b===Set)return l(d,v,y);var _=SB(d);return _===yB?r(d,v,y):_===_B?c(d,v,y):_===gB?a(d,v,y):_===wB?l(d,v,y):_===xB?typeof d.then!="function"&&typeof v.then!="function"&&u(d,v,y):_===AB?h(d,v,y):_===mB?n(d,v,y):_===dB?u(d,v,y):_===vB||_===bB||_===OB?s(d,v,y):!1}}function EB(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?bi:iB,areDatesEqual:aB,areErrorsEqual:oB,areFunctionsEqual:uB,areMapsEqual:n?Vd(Jd,bi):Jd,areNumbersEqual:sB,areObjectsEqual:n?bi:cB,arePrimitiveWrappersEqual:lB,areRegExpsEqual:fB,areSetsEqual:n?Vd(Qd,bi):Qd,areTypedArraysEqual:n?bi:hB,areUrlsEqual:pB};if(r&&(i=ev({},i,r(i))),e){var a=Ya(i.areArraysEqual),o=Ya(i.areMapsEqual),u=Ya(i.areObjectsEqual),s=Ya(i.areSetsEqual);i=ev({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:s})}return i}function TB(t){return function(e,r,n,i,a,o,u){return t(e,r,u)}}function jB(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,a=t.strict;if(n)return function(s,c){var l=n(),f=l.cache,h=f===void 0?e?new WeakMap:void 0:f,p=l.meta;return r(s,c,{cache:h,equals:i,meta:p,strict:a})};if(e)return function(s,c){return r(s,c,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(s,c){return r(s,c,o)}}var MB=yr();yr({strict:!0});yr({circular:!0});yr({circular:!0,strict:!0});yr({createInternalComparator:function(){return Yr}});yr({strict:!0,createInternalComparator:function(){return Yr}});yr({circular:!0,createInternalComparator:function(){return Yr}});yr({circular:!0,createInternalComparator:function(){return Yr},strict:!0});function yr(t){t===void 0&&(t={});var e=t.circular,r=e===void 0?!1:e,n=t.createInternalComparator,i=t.createState,a=t.strict,o=a===void 0?!1:a,u=EB(t),s=$B(u),c=n?n(s):TB(s);return jB({circular:r,comparator:s,createState:i,equals:c,strict:o})}function CB(t){typeof requestAnimationFrame<"u"&&requestAnimationFrame(t)}function rv(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>e?(t(a),r=-1):CB(i)};requestAnimationFrame(n)}function Yc(t){"@babel/helpers - typeof";return Yc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yc(t)}function kB(t){return RB(t)||DB(t)||NB(t)||IB()}function IB(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function NB(t,e){if(t){if(typeof t=="string")return nv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nv(t,e)}}function nv(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function DB(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function RB(t){if(Array.isArray(t))return t}function LB(){var t={},e=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=kB(o),s=u[0],c=u.slice(1);if(typeof s=="number"){rv(i.bind(null,c),s);return}i(s),rv(i.bind(null,c));return}Yc(a)==="object"&&(t=a,e(t)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return e=a,function(){e=function(){return null}}}}}function ia(t){"@babel/helpers - typeof";return ia=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ia(t)}function iv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function av(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?iv(Object(r),!0).forEach(function(n){Bb(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):iv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Bb(t,e,r){return e=BB(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function BB(t){var e=zB(t,"string");return ia(e)==="symbol"?e:String(e)}function zB(t,e){if(ia(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ia(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var FB=function(e,r){return[Object.keys(e),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},WB=function(e){return e},UB=function(e){return e.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},ji=function(e,r){return Object.keys(r).reduce(function(n,i){return av(av({},n),{},Bb({},i,e(i,r[i])))},{})},ov=function(e,r,n){return e.map(function(i){return"".concat(UB(i)," ").concat(r,"ms ").concat(n)}).join(",")};function KB(t,e){return GB(t)||HB(t,e)||zb(t,e)||qB()}function qB(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HB(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function GB(t){if(Array.isArray(t))return t}function VB(t){return ZB(t)||YB(t)||zb(t)||XB()}function XB(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zb(t,e){if(t){if(typeof t=="string")return Zc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zc(t,e)}}function YB(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ZB(t){if(Array.isArray(t))return Zc(t)}function Zc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Lo=1e-4,Fb=function(e,r){return[0,3*e,3*r-6*e,3*e-3*r+1]},Wb=function(e,r){return e.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},uv=function(e,r){return function(n){var i=Fb(e,r);return Wb(i,n)}},JB=function(e,r){return function(n){var i=Fb(e,r),a=[].concat(VB(i.map(function(o,u){return o*u}).slice(1)),[0]);return Wb(a,n)}},sv=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var s=r[0].split("(");if(s[0]==="cubic-bezier"&&s[1].split(")")[0].split(",").length===4){var c=s[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),l=KB(c,4);i=l[0],a=l[1],o=l[2],u=l[3]}}}var f=uv(i,o),h=uv(a,u),p=JB(i,o),d=function(g){return g>1?1:g<0?0:g},v=function(g){for(var b=g>1?1:g,_=b,w=0;w<8;++w){var m=f(_)-b,x=p(_);if(Math.abs(m-b)<Lo||x<Lo)return h(_);_=d(_-m/x)}return h(_)};return v.isStepper=!1,v},QB=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=e.stiff,n=r===void 0?100:r,i=e.damping,a=i===void 0?8:i,o=e.dt,u=o===void 0?17:o,s=function(l,f,h){var p=-(l-f)*n,d=h*a,v=h+(p-d)*u/1e3,y=h*u/1e3+l;return Math.abs(y-f)<Lo&&Math.abs(v)<Lo?[f,0]:[y,v]};return s.isStepper=!0,s.dt=u,s},t3=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return sv(i);case"spring":return QB();default:if(i.split("(")[0]==="cubic-bezier")return sv(i)}return typeof i=="function"?i:null};function aa(t){"@babel/helpers - typeof";return aa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},aa(t)}function cv(t){return n3(t)||r3(t)||Ub(t)||e3()}function e3(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function r3(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function n3(t){if(Array.isArray(t))return Qc(t)}function lv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Nt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?lv(Object(r),!0).forEach(function(n){Jc(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Jc(t,e,r){return e=i3(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i3(t){var e=a3(t,"string");return aa(e)==="symbol"?e:String(e)}function a3(t,e){if(aa(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(aa(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function o3(t,e){return c3(t)||s3(t,e)||Ub(t,e)||u3()}function u3(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ub(t,e){if(t){if(typeof t=="string")return Qc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qc(t,e)}}function Qc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function s3(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function c3(t){if(Array.isArray(t))return t}var Bo=function(e,r,n){return e+(r-e)*n},tl=function(e){var r=e.from,n=e.to;return r!==n},l3=function t(e,r,n){var i=ji(function(a,o){if(tl(o)){var u=e(o.from,o.to,o.velocity),s=o3(u,2),c=s[0],l=s[1];return Nt(Nt({},o),{},{from:c,velocity:l})}return o},r);return n<1?ji(function(a,o){return tl(o)?Nt(Nt({},o),{},{velocity:Bo(o.velocity,i[a].velocity,n),from:Bo(o.from,i[a].from,n)}):o},r):t(e,i,n-1)};const f3=function(t,e,r,n,i){var a=FB(t,e),o=a.reduce(function(y,g){return Nt(Nt({},y),{},Jc({},g,[t[g],e[g]]))},{}),u=a.reduce(function(y,g){return Nt(Nt({},y),{},Jc({},g,{from:t[g],velocity:0,to:e[g]}))},{}),s=-1,c,l,f=function(){return null},h=function(){return ji(function(g,b){return b.from},u)},p=function(){return!Object.values(u).filter(tl).length},d=function(g){c||(c=g);var b=g-c,_=b/r.dt;u=l3(r,u,_),i(Nt(Nt(Nt({},t),e),h())),c=g,p()||(s=requestAnimationFrame(f))},v=function(g){l||(l=g);var b=(g-l)/n,_=ji(function(m,x){return Bo.apply(void 0,cv(x).concat([r(b)]))},o);if(i(Nt(Nt(Nt({},t),e),_)),b<1)s=requestAnimationFrame(f);else{var w=ji(function(m,x){return Bo.apply(void 0,cv(x).concat([r(1)]))},o);i(Nt(Nt(Nt({},t),e),w))}};return f=r.isStepper?d:v,function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function En(t){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},En(t)}var h3=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function p3(t,e){if(t==null)return{};var r=d3(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function d3(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,a;for(a=0;a<n.length;a++)i=n[a],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}function Us(t){return g3(t)||m3(t)||y3(t)||v3()}function v3(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function y3(t,e){if(t){if(typeof t=="string")return el(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return el(t,e)}}function m3(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function g3(t){if(Array.isArray(t))return el(t)}function el(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function fv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function de(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?fv(Object(r),!0).forEach(function(n){Oi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Oi(t,e,r){return e=Kb(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b3(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function x3(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Kb(n.key),n)}}function _3(t,e,r){return e&&x3(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Kb(t){var e=w3(t,"string");return En(e)==="symbol"?e:String(e)}function w3(t,e){if(En(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(En(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function O3(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rl(t,e)}function rl(t,e){return rl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},rl(t,e)}function A3(t){var e=P3();return function(){var n=zo(t),i;if(e){var a=zo(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return nl(this,i)}}function nl(t,e){if(e&&(En(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return il(t)}function il(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function P3(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function zo(t){return zo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},zo(t)}var ee=function(t){O3(r,t);var e=A3(r);function r(n,i){var a;b3(this,r),a=e.call(this,n,i);var o=a.props,u=o.isActive,s=o.attributeName,c=o.from,l=o.to,f=o.steps,h=o.children,p=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(il(a)),a.changeStyle=a.changeStyle.bind(il(a)),!u||p<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:l}),nl(a);if(f&&f.length)a.state={style:f[0].style};else if(c){if(typeof h=="function")return a.state={style:c},nl(a);a.state={style:s?Oi({},s,c):c}}else a.state={style:{}};return a}return _3(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,s=a.attributeName,c=a.shouldReAnimate,l=a.to,f=a.from,h=this.state.style;if(u){if(!o){var p={style:s?Oi({},s,l):l};this.state&&h&&(s&&h[s]!==l||!s&&h!==l)&&this.setState(p);return}if(!(MB(i.to,l)&&i.canBegin&&i.isActive)){var d=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=d||c?f:i.to;if(this.state&&h){var y={style:s?Oi({},s,v):v};(s&&h[s]!==v||!s&&h!==v)&&this.setState(y)}this.runAnimation(de(de({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,s=i.duration,c=i.easing,l=i.begin,f=i.onAnimationEnd,h=i.onAnimationStart,p=f3(o,u,t3(c),s,this.changeStyle),d=function(){a.stopJSAnimation=p()};this.manager.start([h,l,d,s,f])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,s=i.onAnimationStart,c=o[0],l=c.style,f=c.duration,h=f===void 0?0:f,p=function(v,y,g){if(g===0)return v;var b=y.duration,_=y.easing,w=_===void 0?"ease":_,m=y.style,x=y.properties,O=y.onAnimationEnd,S=g>0?o[g-1]:y,$=x||Object.keys(m);if(typeof w=="function"||w==="spring")return[].concat(Us(v),[a.runJSAnimation.bind(a,{from:S.style,to:m,duration:b,easing:w}),b]);var k=ov($,b,w),T=de(de(de({},S.style),m),{},{transition:k});return[].concat(Us(v),[T,b,O]).filter(WB)};return this.manager.start([s].concat(Us(o.reduce(p,[l,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=LB());var a=i.begin,o=i.duration,u=i.attributeName,s=i.to,c=i.easing,l=i.onAnimationStart,f=i.onAnimationEnd,h=i.steps,p=i.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),typeof c=="function"||typeof p=="function"||c==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?Oi({},u,s):s,y=ov(Object.keys(v),o,c);d.start([l,a,de(de({},v),{},{transition:y}),o,f])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var s=p3(i,h3),c=W.Children.count(a),l=this.state.style;if(typeof a=="function")return a(l);if(!u||c===0||o<=0)return a;var f=function(p){var d=p.props,v=d.style,y=v===void 0?{}:v,g=d.className,b=W.cloneElement(p,de(de({},s),{},{style:de(de({},y),l),className:g}));return b};return c===1?f(W.Children.only(a)):A.createElement("div",null,W.Children.map(a,function(h){return f(h)}))}}]),r}(W.PureComponent);ee.displayName="Animate";ee.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};ee.propTypes={from:ut.oneOfType([ut.object,ut.string]),to:ut.oneOfType([ut.object,ut.string]),attributeName:ut.string,duration:ut.number,begin:ut.number,easing:ut.oneOfType([ut.string,ut.func]),steps:ut.arrayOf(ut.shape({duration:ut.number.isRequired,style:ut.object.isRequired,easing:ut.oneOfType([ut.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ut.func]),properties:ut.arrayOf("string"),onAnimationEnd:ut.func})),children:ut.oneOfType([ut.node,ut.func]),isActive:ut.bool,canBegin:ut.bool,onAnimationEnd:ut.func,shouldReAnimate:ut.bool,onAnimationStart:ut.func,onAnimationReStart:ut.func};function oa(t){"@babel/helpers - typeof";return oa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oa(t)}function Fo(){return Fo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Fo.apply(this,arguments)}function S3(t,e){return j3(t)||T3(t,e)||E3(t,e)||$3()}function $3(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function E3(t,e){if(t){if(typeof t=="string")return hv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hv(t,e)}}function hv(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function T3(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function j3(t){if(Array.isArray(t))return t}function pv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function dv(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?pv(Object(r),!0).forEach(function(n){M3(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function M3(t,e,r){return e=C3(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C3(t){var e=k3(t,"string");return oa(e)=="symbol"?e:e+""}function k3(t,e){if(oa(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(oa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var vv=function(e,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,s=n>=0?1:-1,c=i>=0&&n>=0||i<0&&n<0?1:0,l;if(o>0&&a instanceof Array){for(var f=[0,0,0,0],h=0,p=4;h<p;h++)f[h]=a[h]>o?o:a[h];l="M".concat(e,",").concat(r+u*f[0]),f[0]>0&&(l+="A ".concat(f[0],",").concat(f[0],",0,0,").concat(c,",").concat(e+s*f[0],",").concat(r)),l+="L ".concat(e+n-s*f[1],",").concat(r),f[1]>0&&(l+="A ".concat(f[1],",").concat(f[1],",0,0,").concat(c,`,
        `).concat(e+n,",").concat(r+u*f[1])),l+="L ".concat(e+n,",").concat(r+i-u*f[2]),f[2]>0&&(l+="A ".concat(f[2],",").concat(f[2],",0,0,").concat(c,`,
        `).concat(e+n-s*f[2],",").concat(r+i)),l+="L ".concat(e+s*f[3],",").concat(r+i),f[3]>0&&(l+="A ".concat(f[3],",").concat(f[3],",0,0,").concat(c,`,
        `).concat(e,",").concat(r+i-u*f[3])),l+="Z"}else if(o>0&&a===+a&&a>0){var d=Math.min(o,a);l="M ".concat(e,",").concat(r+u*d,`
            A `).concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+s*d,",").concat(r,`
            L `).concat(e+n-s*d,",").concat(r,`
            A `).concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+n,",").concat(r+u*d,`
            L `).concat(e+n,",").concat(r+i-u*d,`
            A `).concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+n-s*d,",").concat(r+i,`
            L `).concat(e+s*d,",").concat(r+i,`
            A `).concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(r+i-u*d," Z")}else l="M ".concat(e,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return l},I3=function(e,r){if(!e||!r)return!1;var n=e.x,i=e.y,a=r.x,o=r.y,u=r.width,s=r.height;if(Math.abs(u)>0&&Math.abs(s)>0){var c=Math.min(a,a+u),l=Math.max(a,a+u),f=Math.min(o,o+s),h=Math.max(o,o+s);return n>=c&&n<=l&&i>=f&&i<=h}return!1},N3={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},qf=function(e){var r=dv(dv({},N3),e),n=W.useRef(),i=W.useState(-1),a=S3(i,2),o=a[0],u=a[1];W.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var s=r.x,c=r.y,l=r.width,f=r.height,h=r.radius,p=r.className,d=r.animationEasing,v=r.animationDuration,y=r.animationBegin,g=r.isAnimationActive,b=r.isUpdateAnimationActive;if(s!==+s||c!==+c||l!==+l||f!==+f||l===0||f===0)return null;var _=J("recharts-rectangle",p);return b?A.createElement(ee,{canBegin:o>0,from:{width:l,height:f,x:s,y:c},to:{width:l,height:f,x:s,y:c},duration:v,animationEasing:d,isActive:b},function(w){var m=w.width,x=w.height,O=w.x,S=w.y;return A.createElement(ee,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:v,isActive:g,easing:d},A.createElement("path",Fo({},q(r,!0),{className:_,d:vv(O,S,m,x,h),ref:n})))}):A.createElement("path",Fo({},q(r,!0),{className:_,d:vv(s,c,l,f,h)}))},D3=["points","className","baseLinePoints","connectNulls"];function sn(){return sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},sn.apply(this,arguments)}function R3(t,e){if(t==null)return{};var r=L3(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function L3(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function yv(t){return W3(t)||F3(t)||z3(t)||B3()}function B3(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function z3(t,e){if(t){if(typeof t=="string")return al(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return al(t,e)}}function F3(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function W3(t){if(Array.isArray(t))return al(t)}function al(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var mv=function(e){return e&&e.x===+e.x&&e.y===+e.y},U3=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return e.forEach(function(n){mv(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),mv(e[0])&&r[r.length-1].push(e[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},Mi=function(e,r){var n=U3(e);r&&(n=[n.reduce(function(a,o){return[].concat(yv(a),yv(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,s){return"".concat(o).concat(s===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},K3=function(e,r,n){var i=Mi(e,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(Mi(r.reverse(),n).slice(1))},qb=function(e){var r=e.points,n=e.className,i=e.baseLinePoints,a=e.connectNulls,o=R3(e,D3);if(!r||!r.length)return null;var u=J("recharts-polygon",n);if(i&&i.length){var s=o.stroke&&o.stroke!=="none",c=K3(r,i,a);return A.createElement("g",{className:u},A.createElement("path",sn({},q(o,!0),{fill:c.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:c})),s?A.createElement("path",sn({},q(o,!0),{fill:"none",d:Mi(r,a)})):null,s?A.createElement("path",sn({},q(o,!0),{fill:"none",d:Mi(i,a)})):null)}var l=Mi(r,a);return A.createElement("path",sn({},q(o,!0),{fill:l.slice(-1)==="Z"?o.fill:"none",className:u,d:l}))};function ol(){return ol=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ol.apply(this,arguments)}var ci=function(e){var r=e.cx,n=e.cy,i=e.r,a=e.className,o=J("recharts-dot",a);return r===+r&&n===+n&&i===+i?A.createElement("circle",ol({},q(e,!1),so(e),{className:o,cx:r,cy:n,r:i})):null};function ua(t){"@babel/helpers - typeof";return ua=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ua(t)}var q3=["x","y","top","left","width","height","className"];function ul(){return ul=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ul.apply(this,arguments)}function gv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function H3(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?gv(Object(r),!0).forEach(function(n){G3(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):gv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function G3(t,e,r){return e=V3(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function V3(t){var e=X3(t,"string");return ua(e)=="symbol"?e:e+""}function X3(t,e){if(ua(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ua(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Y3(t,e){if(t==null)return{};var r=Z3(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Z3(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var J3=function(e,r,n,i,a,o){return"M".concat(e,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},Q3=function(e){var r=e.x,n=r===void 0?0:r,i=e.y,a=i===void 0?0:i,o=e.top,u=o===void 0?0:o,s=e.left,c=s===void 0?0:s,l=e.width,f=l===void 0?0:l,h=e.height,p=h===void 0?0:h,d=e.className,v=Y3(e,q3),y=H3({x:n,y:a,top:u,left:c,width:f,height:p},v);return!F(n)||!F(a)||!F(f)||!F(p)||!F(u)||!F(c)?null:A.createElement("path",ul({},q(y,!0),{className:J("recharts-cross",d),d:J3(n,a,f,p,u,c)}))},tz=["cx","cy","innerRadius","outerRadius","gridType","radialLines"];function sa(t){"@babel/helpers - typeof";return sa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sa(t)}function ez(t,e){if(t==null)return{};var r=rz(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function rz(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Ge(){return Ge=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ge.apply(this,arguments)}function bv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ca(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?bv(Object(r),!0).forEach(function(n){nz(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function nz(t,e,r){return e=iz(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function iz(t){var e=az(t,"string");return sa(e)=="symbol"?e:e+""}function az(t,e){if(sa(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(sa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var oz=function(e,r,n,i){var a="";return i.forEach(function(o,u){var s=ot(r,n,e,o);u?a+="L ".concat(s.x,",").concat(s.y):a+="M ".concat(s.x,",").concat(s.y)}),a+="Z",a},uz=function(e){var r=e.cx,n=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.polarAngles,u=e.radialLines;if(!o||!o.length||!u)return null;var s=ca({stroke:"#ccc"},q(e,!1));return A.createElement("g",{className:"recharts-polar-grid-angle"},o.map(function(c){var l=ot(r,n,i,c),f=ot(r,n,a,c);return A.createElement("line",Ge({},s,{key:"line-".concat(c),x1:l.x,y1:l.y,x2:f.x,y2:f.y}))}))},sz=function(e){var r=e.cx,n=e.cy,i=e.radius,a=e.index,o=ca(ca({stroke:"#ccc"},q(e,!1)),{},{fill:"none"});return A.createElement("circle",Ge({},o,{className:J("recharts-polar-grid-concentric-circle",e.className),key:"circle-".concat(a),cx:r,cy:n,r:i}))},cz=function(e){var r=e.radius,n=e.index,i=ca(ca({stroke:"#ccc"},q(e,!1)),{},{fill:"none"});return A.createElement("path",Ge({},i,{className:J("recharts-polar-grid-concentric-polygon",e.className),key:"path-".concat(n),d:oz(r,e.cx,e.cy,e.polarAngles)}))},lz=function(e){var r=e.polarRadius,n=e.gridType;return!r||!r.length?null:A.createElement("g",{className:"recharts-polar-grid-concentric"},r.map(function(i,a){var o=a;return n==="circle"?A.createElement(sz,Ge({key:o},e,{radius:i,index:a})):A.createElement(cz,Ge({key:o},e,{radius:i,index:a}))}))},fz=function(e){var r=e.cx,n=r===void 0?0:r,i=e.cy,a=i===void 0?0:i,o=e.innerRadius,u=o===void 0?0:o,s=e.outerRadius,c=s===void 0?0:s,l=e.gridType,f=l===void 0?"polygon":l,h=e.radialLines,p=h===void 0?!0:h,d=ez(e,tz);return c<=0?null:A.createElement("g",{className:"recharts-polar-grid"},A.createElement(uz,Ge({cx:n,cy:a,innerRadius:u,outerRadius:c,gridType:f,radialLines:p},d)),A.createElement(lz,Ge({cx:n,cy:a,innerRadius:u,outerRadius:c,gridType:f,radialLines:p},d)))};fz.displayName="PolarGrid";var hz=Ju,pz=nb,dz=$e;function vz(t,e){return t&&t.length?hz(t,dz(e),pz):void 0}var yz=vz;const mz=ft(yz);var gz=Ju,bz=$e,xz=ib;function _z(t,e){return t&&t.length?gz(t,bz(e),xz):void 0}var wz=_z;const Oz=ft(wz);var Az=["cx","cy","angle","ticks","axisLine"],Pz=["ticks","tick","angle","tickFormatter","stroke"];function Tn(t){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tn(t)}function Ci(){return Ci=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ci.apply(this,arguments)}function xv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function wr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?xv(Object(r),!0).forEach(function(n){rs(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function _v(t,e){if(t==null)return{};var r=Sz(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Sz(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function $z(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function wv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Gb(n.key),n)}}function Ez(t,e,r){return e&&wv(t.prototype,e),r&&wv(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Tz(t,e,r){return e=Wo(e),jz(t,Hb()?Reflect.construct(e,r||[],Wo(t).constructor):e.apply(t,r))}function jz(t,e){if(e&&(Tn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Mz(t)}function Mz(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Hb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Hb=function(){return!!t})()}function Wo(t){return Wo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wo(t)}function Cz(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&sl(t,e)}function sl(t,e){return sl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},sl(t,e)}function rs(t,e,r){return e=Gb(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Gb(t){var e=kz(t,"string");return Tn(e)=="symbol"?e:e+""}function kz(t,e){if(Tn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ea=function(t){function e(){return $z(this,e),Tz(this,e,arguments)}return Cz(e,t),Ez(e,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,s=a.cy;return ot(u,s,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,s=mz(u,function(l){return l.coordinate||0}),c=Oz(u,function(l){return l.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:c.coordinate||0,outerRadius:s.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,s=n.axisLine,c=_v(n,Az),l=u.reduce(function(d,v){return[Math.min(d[0],v.coordinate),Math.max(d[1],v.coordinate)]},[1/0,-1/0]),f=ot(i,a,l[0],o),h=ot(i,a,l[1],o),p=wr(wr(wr({},q(c,!1)),{},{fill:"none"},q(s,!1)),{},{x1:f.x,y1:f.y,x2:h.x,y2:h.y});return A.createElement("line",Ci({className:"recharts-polar-radius-axis-line"},p))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,s=i.tickFormatter,c=i.stroke,l=_v(i,Pz),f=this.getTickTextAnchor(),h=q(l,!1),p=q(o,!1),d=a.map(function(v,y){var g=n.getTickValueCoord(v),b=wr(wr(wr(wr({textAnchor:f,transform:"rotate(".concat(90-u,", ").concat(g.x,", ").concat(g.y,")")},h),{},{stroke:"none",fill:c},p),{},{index:y},g),{},{payload:v});return A.createElement(Q,Ci({className:J("recharts-polar-radius-axis-tick",kb(o)),key:"tick-".concat(v.coordinate)},sr(n.props,v,y)),e.renderTickItem(o,b,s?s(v.value,y):v.value))});return A.createElement(Q,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:A.createElement(Q,{className:J("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),Mt.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):V(n)?o=n(i):o=A.createElement(Br,Ci({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(W.PureComponent);rs(Ea,"displayName","PolarRadiusAxis");rs(Ea,"axisType","radiusAxis");rs(Ea,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function jn(t){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jn(t)}function Er(){return Er=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Er.apply(this,arguments)}function Ov(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Or(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ov(Object(r),!0).forEach(function(n){ns(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ov(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Iz(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Av(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Xb(n.key),n)}}function Nz(t,e,r){return e&&Av(t.prototype,e),r&&Av(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Dz(t,e,r){return e=Uo(e),Rz(t,Vb()?Reflect.construct(e,r||[],Uo(t).constructor):e.apply(t,r))}function Rz(t,e){if(e&&(jn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Lz(t)}function Lz(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Vb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vb=function(){return!!t})()}function Uo(t){return Uo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Uo(t)}function Bz(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&cl(t,e)}function cl(t,e){return cl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},cl(t,e)}function ns(t,e,r){return e=Xb(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Xb(t){var e=zz(t,"string");return jn(e)=="symbol"?e:e+""}function zz(t,e){if(jn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Fz=Math.PI/180,Wz=1e-5,Ta=function(t){function e(){return Iz(this,e),Dz(this,e,arguments)}return Bz(e,t),Nz(e,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,s=i.orientation,c=i.tickSize,l=c||8,f=ot(a,o,u,n.coordinate),h=ot(a,o,u+(s==="inner"?-1:1)*l,n.coordinate);return{x1:f.x,y1:f.y,x2:h.x,y2:h.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*Fz),o;return a>Wz?o=i==="outer"?"start":"end":a<-1e-5?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,s=n.axisLineType,c=Or(Or({},q(this.props,!1)),{},{fill:"none"},q(u,!1));if(s==="circle")return A.createElement(ci,Er({className:"recharts-polar-angle-axis-line"},c,{cx:i,cy:a,r:o}));var l=this.props.ticks,f=l.map(function(h){return ot(i,a,o,h.coordinate)});return A.createElement(qb,Er({className:"recharts-polar-angle-axis-line"},c,{points:f}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,s=i.tickFormatter,c=i.stroke,l=q(this.props,!1),f=q(o,!1),h=Or(Or({},l),{},{fill:"none"},q(u,!1)),p=a.map(function(d,v){var y=n.getTickLineCoord(d),g=n.getTickTextAnchor(d),b=Or(Or(Or({textAnchor:g},l),{},{stroke:"none",fill:c},f),{},{index:v,payload:d,x:y.x2,y:y.y2});return A.createElement(Q,Er({className:J("recharts-polar-angle-axis-tick",kb(o)),key:"tick-".concat(d.coordinate)},sr(n.props,d,v)),u&&A.createElement("line",Er({className:"recharts-polar-angle-axis-tick-line"},h,y)),o&&e.renderTickItem(o,b,s?s(d.value,v):d.value))});return A.createElement(Q,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:A.createElement(Q,{className:J("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):V(n)?o=n(i):o=A.createElement(Br,Er({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(W.PureComponent);ns(Ta,"displayName","PolarAngleAxis");ns(Ta,"axisType","angleAxis");ns(Ta,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Uz=$m,Kz=Uz(Object.getPrototypeOf,Object),qz=Kz,Hz=Xe,Gz=qz,Vz=Ye,Xz="[object Object]",Yz=Function.prototype,Zz=Object.prototype,Yb=Yz.toString,Jz=Zz.hasOwnProperty,Qz=Yb.call(Object);function tF(t){if(!Vz(t)||Hz(t)!=Xz)return!1;var e=Gz(t);if(e===null)return!0;var r=Jz.call(e,"constructor")&&e.constructor;return typeof r=="function"&&r instanceof r&&Yb.call(r)==Qz}var eF=tF;const rF=ft(eF);var nF=Xe,iF=Ye,aF="[object Boolean]";function oF(t){return t===!0||t===!1||iF(t)&&nF(t)==aF}var uF=oF;const sF=ft(uF);function la(t){"@babel/helpers - typeof";return la=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},la(t)}function Ko(){return Ko=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ko.apply(this,arguments)}function cF(t,e){return pF(t)||hF(t,e)||fF(t,e)||lF()}function lF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fF(t,e){if(t){if(typeof t=="string")return Pv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pv(t,e)}}function Pv(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function hF(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function pF(t){if(Array.isArray(t))return t}function Sv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function $v(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Sv(Object(r),!0).forEach(function(n){dF(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Sv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function dF(t,e,r){return e=vF(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vF(t){var e=yF(t,"string");return la(e)=="symbol"?e:e+""}function yF(t,e){if(la(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(la(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ev=function(e,r,n,i,a){var o=n-i,u;return u="M ".concat(e,",").concat(r),u+="L ".concat(e+n,",").concat(r),u+="L ".concat(e+n-o/2,",").concat(r+a),u+="L ".concat(e+n-o/2-i,",").concat(r+a),u+="L ".concat(e,",").concat(r," Z"),u},mF={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},gF=function(e){var r=$v($v({},mF),e),n=W.useRef(),i=W.useState(-1),a=cF(i,2),o=a[0],u=a[1];W.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var _=n.current.getTotalLength();_&&u(_)}catch{}},[]);var s=r.x,c=r.y,l=r.upperWidth,f=r.lowerWidth,h=r.height,p=r.className,d=r.animationEasing,v=r.animationDuration,y=r.animationBegin,g=r.isUpdateAnimationActive;if(s!==+s||c!==+c||l!==+l||f!==+f||h!==+h||l===0&&f===0||h===0)return null;var b=J("recharts-trapezoid",p);return g?A.createElement(ee,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:s,y:c},to:{upperWidth:l,lowerWidth:f,height:h,x:s,y:c},duration:v,animationEasing:d,isActive:g},function(_){var w=_.upperWidth,m=_.lowerWidth,x=_.height,O=_.x,S=_.y;return A.createElement(ee,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:v,easing:d},A.createElement("path",Ko({},q(r,!0),{className:b,d:Ev(O,S,w,m,x),ref:n})))}):A.createElement("g",null,A.createElement("path",Ko({},q(r,!0),{className:b,d:Ev(s,c,l,f,h)})))},bF=["option","shapeType","propTransformer","activeClassName","isActive"];function fa(t){"@babel/helpers - typeof";return fa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fa(t)}function xF(t,e){if(t==null)return{};var r=_F(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function _F(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Tv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function qo(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Tv(Object(r),!0).forEach(function(n){wF(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Tv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function wF(t,e,r){return e=OF(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function OF(t){var e=AF(t,"string");return fa(e)=="symbol"?e:e+""}function AF(t,e){if(fa(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(fa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function PF(t,e){return qo(qo({},e),t)}function SF(t,e){return t==="symbols"}function jv(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return A.createElement(qf,r);case"trapezoid":return A.createElement(gF,r);case"sector":return A.createElement(Rb,r);case"symbols":if(SF(e))return A.createElement(Ru,r);break;default:return null}}function $F(t){return W.isValidElement(t)?t.props:t}function Ho(t){var e=t.option,r=t.shapeType,n=t.propTransformer,i=n===void 0?PF:n,a=t.activeClassName,o=a===void 0?"recharts-active-shape":a,u=t.isActive,s=xF(t,bF),c;if(W.isValidElement(e))c=W.cloneElement(e,qo(qo({},s),$F(e)));else if(V(e))c=e(s);else if(rF(e)&&!sF(e)){var l=i(e,s);c=A.createElement(jv,{shapeType:r,elementProps:l})}else{var f=s;c=A.createElement(jv,{shapeType:r,elementProps:f})}return u?A.createElement(Q,{className:o},c):c}function is(t,e){return e!=null&&"trapezoids"in t.props}function as(t,e){return e!=null&&"sectors"in t.props}function ha(t,e){return e!=null&&"points"in t.props}function EF(t,e){var r,n,i=t.x===(e==null||(r=e.labelViewBox)===null||r===void 0?void 0:r.x)||t.x===e.x,a=t.y===(e==null||(n=e.labelViewBox)===null||n===void 0?void 0:n.y)||t.y===e.y;return i&&a}function TF(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function jF(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}function MF(t,e){var r;return is(t,e)?r=EF:as(t,e)?r=TF:ha(t,e)&&(r=jF),r}function CF(t,e){var r;return is(t,e)?r="trapezoids":as(t,e)?r="sectors":ha(t,e)&&(r="points"),r}function kF(t,e){if(is(t,e)){var r;return(r=e.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(as(t,e)){var n;return(n=e.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return ha(t,e)?e.payload:{}}function IF(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,i=CF(r,e),a=kF(r,e),o=n.filter(function(s,c){var l=He(a,s),f=r.props[i].filter(function(d){var v=MF(r,e);return v(d,e)}),h=r.props[i].indexOf(f[f.length-1]),p=c===h;return l&&p}),u=n.indexOf(o[o.length-1]);return u}var to;function Mn(t){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mn(t)}function cn(){return cn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},cn.apply(this,arguments)}function Mv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function yt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Mv(Object(r),!0).forEach(function(n){ue(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function NF(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Cv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Jb(n.key),n)}}function DF(t,e,r){return e&&Cv(t.prototype,e),r&&Cv(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function RF(t,e,r){return e=Go(e),LF(t,Zb()?Reflect.construct(e,r||[],Go(t).constructor):e.apply(t,r))}function LF(t,e){if(e&&(Mn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return BF(t)}function BF(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Zb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Zb=function(){return!!t})()}function Go(t){return Go=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Go(t)}function zF(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ll(t,e)}function ll(t,e){return ll=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ll(t,e)}function ue(t,e,r){return e=Jb(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Jb(t){var e=FF(t,"string");return Mn(e)=="symbol"?e:e+""}function FF(t,e){if(Mn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var mr=function(t){function e(r){var n;return NF(this,e),n=RF(this,e,[r]),ue(n,"pieRef",null),ue(n,"sectorRefs",[]),ue(n,"id",hr("recharts-pie-")),ue(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),V(i)&&i()}),ue(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),V(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return zF(e,t),DF(e,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,s=a.dataKey,c=a.valueKey,l=q(this.props,!1),f=q(o,!1),h=q(u,!1),p=o&&o.offsetRadius||20,d=n.map(function(v,y){var g=(v.startAngle+v.endAngle)/2,b=ot(v.cx,v.cy,v.outerRadius+p,g),_=yt(yt(yt(yt({},l),v),{},{stroke:"none"},f),{},{index:y,textAnchor:e.getTextAnchor(b.x,v.cx)},b),w=yt(yt(yt(yt({},l),v),{},{fill:"none",stroke:v.fill},h),{},{index:y,points:[ot(v.cx,v.cy,v.outerRadius,g),b]}),m=s;return X(s)&&X(c)?m="value":X(s)&&(m=c),A.createElement(Q,{key:"label-".concat(v.startAngle,"-").concat(v.endAngle,"-").concat(v.midAngle,"-").concat(y)},u&&e.renderLabelLineItem(u,w,"line"),e.renderLabelItem(o,_,dt(v,m)))});return A.createElement(Q,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,s=a.inactiveShape;return n.map(function(c,l){if((c==null?void 0:c.startAngle)===0&&(c==null?void 0:c.endAngle)===0&&n.length!==1)return null;var f=i.isActiveIndex(l),h=s&&i.hasActiveIndex()?s:null,p=f?o:h,d=yt(yt({},c),{},{stroke:u?c.fill:c.stroke,tabIndex:-1});return A.createElement(Q,cn({ref:function(y){y&&!i.sectorRefs.includes(y)&&i.sectorRefs.push(y)},tabIndex:-1,className:"recharts-pie-sector"},sr(i.props,c,l),{key:"sector-".concat(c==null?void 0:c.startAngle,"-").concat(c==null?void 0:c.endAngle,"-").concat(c.midAngle,"-").concat(l)}),A.createElement(Ho,cn({option:p,isActive:f,shapeType:"sector"},d)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,s=i.animationDuration,c=i.animationEasing,l=i.animationId,f=this.state,h=f.prevSectors,p=f.prevIsAnimationActive;return A.createElement(ee,{begin:u,duration:s,isActive:o,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(l,"-").concat(p),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(d){var v=d.t,y=[],g=a&&a[0],b=g.startAngle;return a.forEach(function(_,w){var m=h&&h[w],x=w>0?te(_,"paddingAngle",0):0;if(m){var O=pt(m.endAngle-m.startAngle,_.endAngle-_.startAngle),S=yt(yt({},_),{},{startAngle:b+x,endAngle:b+O(v)+x});y.push(S),b=S.endAngle}else{var $=_.endAngle,k=_.startAngle,T=pt(0,$-k),M=T(v),C=yt(yt({},_),{},{startAngle:b+x,endAngle:b+M+x});y.push(C),b=C.endAngle}}),A.createElement(Q,null,n.renderSectorsStatically(y))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!He(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,s=i.label,c=i.cx,l=i.cy,f=i.innerRadius,h=i.outerRadius,p=i.isAnimationActive,d=this.state.isAnimationFinished;if(a||!o||!o.length||!F(c)||!F(l)||!F(f)||!F(h))return null;var v=J("recharts-pie",u);return A.createElement(Q,{tabIndex:this.props.rootTabIndex,className:v,ref:function(g){n.pieRef=g}},this.renderSectors(),s&&this.renderLabels(o),Mt.renderCallByParent(this.props,null,!1),(!p||d)&&ce.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(A.isValidElement(n))return A.cloneElement(n,i);if(V(n))return n(i);var o=J("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return A.createElement(ur,cn({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(A.isValidElement(n))return A.cloneElement(n,i);var o=a;if(V(n)&&(o=n(i),A.isValidElement(o)))return o;var u=J("recharts-pie-label-text",typeof n!="boolean"&&!V(n)?n.className:"");return A.createElement(Br,cn({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(W.PureComponent);to=mr;ue(mr,"displayName","Pie");ue(mr,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!Ee.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});ue(mr,"parseDeltaAngle",function(t,e){var r=Ut(e-t),n=Math.min(Math.abs(e-t),360);return r*n});ue(mr,"getRealPieData",function(t){var e=t.data,r=t.children,n=q(t,!1),i=Ht(r,Fu);return e&&e.length?e.map(function(a,o){return yt(yt(yt({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return yt(yt({},n),a.props)}):[]});ue(mr,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,i=e.width,a=e.height,o=Mb(i,a),u=n+Kt(t.cx,i,i/2),s=r+Kt(t.cy,a,a/2),c=Kt(t.innerRadius,o,0),l=Kt(t.outerRadius,o,o*.8),f=t.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:s,innerRadius:c,outerRadius:l,maxRadius:f}});ue(mr,"getComposedData",function(t){var e=t.item,r=t.offset,n=e.type.defaultProps!==void 0?yt(yt({},e.type.defaultProps),e.props):e.props,i=to.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,s=n.paddingAngle,c=n.dataKey,l=n.nameKey,f=n.valueKey,h=n.tooltipType,p=Math.abs(n.minAngle),d=to.parseCoordinateOfPie(n,r),v=to.parseDeltaAngle(o,u),y=Math.abs(v),g=c;X(c)&&X(f)?(ge(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),g="value"):X(c)&&(ge(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),g=f);var b=i.filter(function(S){return dt(S,g,0)!==0}).length,_=(y>=360?b:b-1)*s,w=y-b*p-_,m=i.reduce(function(S,$){var k=dt($,g,0);return S+(F(k)?k:0)},0),x;if(m>0){var O;x=i.map(function(S,$){var k=dt(S,g,0),T=dt(S,l,$),M=(F(k)?k:0)/m,C;$?C=O.endAngle+Ut(v)*s*(k!==0?1:0):C=o;var P=C+Ut(v)*((k!==0?p:0)+M*w),j=(C+P)/2,E=(d.innerRadius+d.outerRadius)/2,I=[{name:T,value:k,payload:S,dataKey:g,type:h}],N=ot(d.cx,d.cy,E,j);return O=yt(yt(yt({percent:M,cornerRadius:a,name:T,tooltipPayload:I,midAngle:j,middleRadius:E,tooltipPosition:N},S),d),{},{value:dt(S,g),startAngle:C,endAngle:P,payload:S,paddingAngle:Ut(v)*s}),O})}return yt(yt({},d),{},{sectors:x,data:i})});function WF(t){return t&&t.length?t[0]:void 0}var UF=WF,KF=UF;const qF=ft(KF);var HF=["key"];function Cn(t){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Cn(t)}function GF(t,e){if(t==null)return{};var r=VF(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function VF(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Vo(){return Vo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Vo.apply(this,arguments)}function kv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Wt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?kv(Object(r),!0).forEach(function(n){Ne(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function XF(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Iv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t0(n.key),n)}}function YF(t,e,r){return e&&Iv(t.prototype,e),r&&Iv(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function ZF(t,e,r){return e=Xo(e),JF(t,Qb()?Reflect.construct(e,r||[],Xo(t).constructor):e.apply(t,r))}function JF(t,e){if(e&&(Cn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QF(t)}function QF(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Qb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Qb=function(){return!!t})()}function Xo(t){return Xo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Xo(t)}function tW(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fl(t,e)}function fl(t,e){return fl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},fl(t,e)}function Ne(t,e,r){return e=t0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t0(t){var e=eW(t,"string");return Cn(e)=="symbol"?e:e+""}function eW(t,e){if(Cn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var os=function(t){function e(){var r;XF(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=ZF(this,e,[].concat(i)),Ne(r,"state",{isAnimationFinished:!1}),Ne(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),V(o)&&o()}),Ne(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),V(o)&&o()}),Ne(r,"handleMouseEnter",function(o){var u=r.props.onMouseEnter;u&&u(r.props,o)}),Ne(r,"handleMouseLeave",function(o){var u=r.props.onMouseLeave;u&&u(r.props,o)}),r}return tW(e,t),YF(e,[{key:"renderDots",value:function(n){var i=this.props,a=i.dot,o=i.dataKey,u=q(this.props,!1),s=q(a,!0),c=n.map(function(l,f){var h=Wt(Wt(Wt({key:"dot-".concat(f),r:3},u),s),{},{dataKey:o,cx:l.x,cy:l.y,index:f,payload:l});return e.renderDotItem(a,h)});return A.createElement(Q,{className:"recharts-radar-dots"},c)}},{key:"renderPolygonStatically",value:function(n){var i=this.props,a=i.shape,o=i.dot,u=i.isRange,s=i.baseLinePoints,c=i.connectNulls,l;return A.isValidElement(a)?l=A.cloneElement(a,Wt(Wt({},this.props),{},{points:n})):V(a)?l=a(Wt(Wt({},this.props),{},{points:n})):l=A.createElement(qb,Vo({},q(this.props,!0),{onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,points:n,baseLinePoints:u?s:null,connectNulls:c})),A.createElement(Q,{className:"recharts-radar-polygon"},l,o?this.renderDots(n):null)}},{key:"renderPolygonWithAnimation",value:function(){var n=this,i=this.props,a=i.points,o=i.isAnimationActive,u=i.animationBegin,s=i.animationDuration,c=i.animationEasing,l=i.animationId,f=this.state.prevPoints;return A.createElement(ee,{begin:u,duration:s,isActive:o,easing:c,from:{t:0},to:{t:1},key:"radar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var p=h.t,d=f&&f.length/a.length,v=a.map(function(y,g){var b=f&&f[Math.floor(g*d)];if(b){var _=pt(b.x,y.x),w=pt(b.y,y.y);return Wt(Wt({},y),{},{x:_(p),y:w(p)})}var m=pt(y.cx,y.x),x=pt(y.cy,y.y);return Wt(Wt({},y),{},{x:m(p),y:x(p)})});return n.renderPolygonStatically(v)})}},{key:"renderPolygon",value:function(){var n=this.props,i=n.points,a=n.isAnimationActive,o=n.isRange,u=this.state.prevPoints;return a&&i&&i.length&&!o&&(!u||!He(u,i))?this.renderPolygonWithAnimation():this.renderPolygonStatically(i)}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.className,o=n.points,u=n.isAnimationActive;if(i||!o||!o.length)return null;var s=this.state.isAnimationFinished,c=J("recharts-radar",a);return A.createElement(Q,{className:c},this.renderPolygon(),(!u||s)&&ce.renderCallByParent(this.props,o))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"renderDotItem",value:function(n,i){var a;if(A.isValidElement(n))a=A.cloneElement(n,i);else if(V(n))a=n(i);else{var o=i.key,u=GF(i,HF);a=A.createElement(ci,Vo({},u,{key:o,className:J("recharts-radar-dot",typeof n!="boolean"?n.className:"")}))}return a}}])}(W.PureComponent);Ne(os,"displayName","Radar");Ne(os,"defaultProps",{angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!Ee.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});Ne(os,"getComposedData",function(t){var e=t.radiusAxis,r=t.angleAxis,n=t.displayedData,i=t.dataKey,a=t.bandSize,o=r.cx,u=r.cy,s=!1,c=[],l=r.type!=="number"?a??0:0;n.forEach(function(h,p){var d=dt(h,r.dataKey,p),v=dt(h,i),y=r.scale(d)+l,g=Array.isArray(v)?Nb(v):v,b=X(g)?void 0:e.scale(g);Array.isArray(v)&&v.length>=2&&(s=!0),c.push(Wt(Wt({},ot(o,u,b,y)),{},{name:d,value:v,cx:o,cy:u,radius:b,angle:y,payload:h}))});var f=[];return s&&c.forEach(function(h){if(Array.isArray(h.value)){var p=qF(h.value),d=X(p)?void 0:e.scale(p);f.push(Wt(Wt({},h),{},{radius:d},ot(o,u,d,h.angle)))}else f.push(h)}),{points:c,isRange:s,baseLinePoints:f}});var rW=Math.ceil,nW=Math.max;function iW(t,e,r,n){for(var i=-1,a=nW(rW((e-t)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=t,t+=r;return o}var aW=iW,oW=Hm,uW=1/0,sW=17976931348623157e292;function cW(t){if(!t)return t===0?t:0;if(t=oW(t),t===uW||t===-1/0){var e=t<0?-1:1;return e*sW}return t===t?t:0}var e0=cW,lW=aW,fW=zu,Ks=e0;function hW(t){return function(e,r,n){return n&&typeof n!="number"&&fW(e,r,n)&&(r=n=void 0),e=Ks(e),r===void 0?(r=e,e=0):r=Ks(r),n=n===void 0?e<r?1:-1:Ks(n),lW(e,r,n,t)}}var pW=hW,dW=pW,vW=dW(),yW=vW;const Yo=ft(yW);function pa(t){"@babel/helpers - typeof";return pa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pa(t)}function Nv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Dv(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Nv(Object(r),!0).forEach(function(n){r0(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function r0(t,e,r){return e=mW(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mW(t){var e=gW(t,"string");return pa(e)=="symbol"?e:e+""}function gW(t,e){if(pa(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(pa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var bW=["Webkit","Moz","O","ms"],xW=function(e,r){var n=e.replace(/(\w)/,function(a){return a.toUpperCase()}),i=bW.reduce(function(a,o){return Dv(Dv({},a),{},r0({},o+n,r))},{});return i[e]=r,i};function kn(t){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kn(t)}function Zo(){return Zo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Zo.apply(this,arguments)}function Rv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function qs(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Rv(Object(r),!0).forEach(function(n){Zt(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function _W(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Lv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i0(n.key),n)}}function wW(t,e,r){return e&&Lv(t.prototype,e),r&&Lv(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function OW(t,e,r){return e=Jo(e),AW(t,n0()?Reflect.construct(e,r||[],Jo(t).constructor):e.apply(t,r))}function AW(t,e){if(e&&(kn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return PW(t)}function PW(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function n0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(n0=function(){return!!t})()}function Jo(t){return Jo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Jo(t)}function SW(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hl(t,e)}function hl(t,e){return hl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},hl(t,e)}function Zt(t,e,r){return e=i0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i0(t){var e=$W(t,"string");return kn(e)=="symbol"?e:e+""}function $W(t,e){if(kn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var EW=function(e){var r=e.data,n=e.startIndex,i=e.endIndex,a=e.x,o=e.width,u=e.travellerWidth;if(!r||!r.length)return{};var s=r.length,c=Ei().domain(Yo(0,s)).range([a,a+o-u]),l=c.domain().map(function(f){return c(f)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(n),endX:c(i),scale:c,scaleValues:l}},Bv=function(e){return e.changedTouches&&!!e.changedTouches.length},In=function(t){function e(r){var n;return _W(this,e),n=OW(this,e,[r]),Zt(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Zt(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Zt(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Zt(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Zt(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Zt(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Zt(n,"handleSlideDragStart",function(i){var a=Bv(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return SW(e,t),wW(e,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,s=u.gap,c=u.data,l=c.length-1,f=Math.min(i,a),h=Math.max(i,a),p=e.getIndexInRange(o,f),d=e.getIndexInRange(o,h);return{startIndex:p-p%s,endIndex:d===l?l:d-d%s}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,s=dt(a[n],u,n);return V(o)?o(s,n):s}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,s=this.props,c=s.x,l=s.width,f=s.travellerWidth,h=s.startIndex,p=s.endIndex,d=s.onChange,v=n.pageX-a;v>0?v=Math.min(v,c+l-f-u,c+l-f-o):v<0&&(v=Math.max(v,c-o,c-u));var y=this.getIndex({startX:o+v,endX:u+v});(y.startIndex!==h||y.endIndex!==p)&&d&&d(y),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Bv(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,s=i.startX,c=this.state[o],l=this.props,f=l.x,h=l.width,p=l.travellerWidth,d=l.onChange,v=l.gap,y=l.data,g={startX:this.state.startX,endX:this.state.endX},b=n.pageX-a;b>0?b=Math.min(b,f+h-p-c):b<0&&(b=Math.max(b,f-c)),g[o]=c+b;var _=this.getIndex(g),w=_.startIndex,m=_.endIndex,x=function(){var S=y.length-1;return o==="startX"&&(u>s?w%v===0:m%v===0)||u<s&&m===S||o==="endX"&&(u>s?m%v===0:w%v===0)||u>s&&m===S};this.setState(Zt(Zt({},o,c+b),"brushMoveStartX",n.pageX),function(){d&&x()&&d(_)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,s=o.startX,c=o.endX,l=this.state[i],f=u.indexOf(l);if(f!==-1){var h=f+n;if(!(h===-1||h>=u.length)){var p=u[h];i==="startX"&&p>=c||i==="endX"&&p<=s||this.setState(Zt({},i,p),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.fill,c=n.stroke;return A.createElement("rect",{stroke:c,fill:s,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.data,c=n.children,l=n.padding,f=W.Children.only(c);return f?A.cloneElement(f,{x:i,y:a,width:o,height:u,margin:l,compact:!0,data:s}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,s=this.props,c=s.y,l=s.travellerWidth,f=s.height,h=s.traveller,p=s.ariaLabel,d=s.data,v=s.startIndex,y=s.endIndex,g=Math.max(n,this.props.x),b=qs(qs({},q(this.props,!1)),{},{x:g,y:c,width:l,height:f}),_=p||"Min value: ".concat((a=d[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=d[y])===null||o===void 0?void 0:o.name);return A.createElement(Q,{tabIndex:0,role:"slider","aria-label":_,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(h,b))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,s=a.stroke,c=a.travellerWidth,l=Math.min(n,i)+c,f=Math.max(Math.abs(i-n)-c,0);return A.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:s,fillOpacity:.2,x:l,y:o,width:f,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,s=n.travellerWidth,c=n.stroke,l=this.state,f=l.startX,h=l.endX,p=5,d={pointerEvents:"none",fill:c};return A.createElement(Q,{className:"recharts-brush-texts"},A.createElement(Br,Zo({textAnchor:"end",verticalAnchor:"middle",x:Math.min(f,h)-p,y:o+u/2},d),this.getTextOfTick(i)),A.createElement(Br,Zo({textAnchor:"start",verticalAnchor:"middle",x:Math.max(f,h)+s+p,y:o+u/2},d),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,s=n.y,c=n.width,l=n.height,f=n.alwaysShowText,h=this.state,p=h.startX,d=h.endX,v=h.isTextActive,y=h.isSlideMoving,g=h.isTravellerMoving,b=h.isTravellerFocused;if(!i||!i.length||!F(u)||!F(s)||!F(c)||!F(l)||c<=0||l<=0)return null;var _=J("recharts-brush",a),w=A.Children.count(o)===1,m=xW("userSelect","none");return A.createElement(Q,{className:_,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),w&&this.renderPanorama(),this.renderSlide(p,d),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(d,"endX"),(v||y||g||b||f)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,s=n.stroke,c=Math.floor(a+u/2)-1;return A.createElement(A.Fragment,null,A.createElement("rect",{x:i,y:a,width:o,height:u,fill:s,stroke:"none"}),A.createElement("line",{x1:i+1,y1:c,x2:i+o-1,y2:c,fill:"none",stroke:"#fff"}),A.createElement("line",{x1:i+1,y1:c+2,x2:i+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return A.isValidElement(n)?a=A.cloneElement(n,i):V(n)?a=n(i):a=e.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,s=n.travellerWidth,c=n.updateId,l=n.startIndex,f=n.endIndex;if(a!==i.prevData||c!==i.prevUpdateId)return qs({prevData:a,prevTravellerWidth:s,prevUpdateId:c,prevX:u,prevWidth:o},a&&a.length?EW({data:a,width:o,x:u,travellerWidth:s,startIndex:l,endIndex:f}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||s!==i.prevTravellerWidth)){i.scale.range([u,u+o-s]);var h=i.scale.domain().map(function(p){return i.scale(p)});return{prevData:a,prevTravellerWidth:s,prevUpdateId:c,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var s=Math.floor((o+u)/2);n[s]>i?u=s:o=s}return i>=n[u]?u:o}}])}(W.PureComponent);Zt(In,"displayName","Brush");Zt(In,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var TW=vf;function jW(t,e){var r;return TW(t,function(n,i,a){return r=e(n,i,a),!r}),!!r}var MW=jW,CW=bm,kW=$e,IW=MW,NW=Xt,DW=zu;function RW(t,e,r){var n=NW(t)?CW:IW;return r&&DW(t,e,r)&&(e=void 0),n(t,kW(e))}var LW=RW;const BW=ft(LW);var Ae=function(e,r){var n=e.alwaysShow,i=e.ifOverflow;return n&&(i="extendDomain"),i===r},zv=Fm;function zW(t,e,r){e=="__proto__"&&zv?zv(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}var FW=zW,WW=FW,UW=Bm,KW=$e;function qW(t,e){var r={};return e=KW(e),UW(t,function(n,i,a){WW(r,i,e(n,i,a))}),r}var HW=qW;const GW=ft(HW);function VW(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}var XW=VW,YW=vf;function ZW(t,e){var r=!0;return YW(t,function(n,i,a){return r=!!e(n,i,a),r}),r}var JW=ZW,QW=XW,t5=JW,e5=$e,r5=Xt,n5=zu;function i5(t,e,r){var n=r5(t)?QW:t5;return r&&n5(t,e,r)&&(e=void 0),n(t,e5(e))}var a5=i5;const a0=ft(a5);var o5=["x","y"];function da(t){"@babel/helpers - typeof";return da=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},da(t)}function pl(){return pl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pl.apply(this,arguments)}function Fv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function xi(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Fv(Object(r),!0).forEach(function(n){u5(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function u5(t,e,r){return e=s5(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s5(t){var e=c5(t,"string");return da(e)=="symbol"?e:e+""}function c5(t,e){if(da(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(da(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function l5(t,e){if(t==null)return{};var r=f5(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function f5(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function h5(t,e){var r=t.x,n=t.y,i=l5(t,o5),a="".concat(r),o=parseInt(a,10),u="".concat(n),s=parseInt(u,10),c="".concat(e.height||i.height),l=parseInt(c,10),f="".concat(e.width||i.width),h=parseInt(f,10);return xi(xi(xi(xi(xi({},e),i),o?{x:o}:{}),s?{y:s}:{}),{},{height:l,width:h,name:e.name,radius:e.radius})}function Wv(t){return A.createElement(Ho,pl({shapeType:"rectangle",propTransformer:h5,activeClassName:"recharts-active-bar"},t))}var p5=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof e=="number")return e;var a=typeof n=="number";return a?e(n,i):(a||Ur(),r)}},d5=["value","background"],o0;function Nn(t){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nn(t)}function v5(t,e){if(t==null)return{};var r=y5(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function y5(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Qo(){return Qo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Qo.apply(this,arguments)}function Uv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Ot(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Uv(Object(r),!0).forEach(function(n){nr(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Uv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function m5(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Kv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,s0(n.key),n)}}function g5(t,e,r){return e&&Kv(t.prototype,e),r&&Kv(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function b5(t,e,r){return e=tu(e),x5(t,u0()?Reflect.construct(e,r||[],tu(t).constructor):e.apply(t,r))}function x5(t,e){if(e&&(Nn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _5(t)}function _5(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function u0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(u0=function(){return!!t})()}function tu(t){return tu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},tu(t)}function w5(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dl(t,e)}function dl(t,e){return dl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},dl(t,e)}function nr(t,e,r){return e=s0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s0(t){var e=O5(t,"string");return Nn(e)=="symbol"?e:e+""}function O5(t,e){if(Nn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Zr=function(t){function e(){var r;m5(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=b5(this,e,[].concat(i)),nr(r,"state",{isAnimationFinished:!1}),nr(r,"id",hr("recharts-bar-")),nr(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),nr(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return w5(e,t),g5(e,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,s=a.activeIndex,c=a.activeBar,l=q(this.props,!1);return n&&n.map(function(f,h){var p=h===s,d=p?c:o,v=Ot(Ot(Ot({},l),f),{},{isActive:p,option:d,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return A.createElement(Q,Qo({className:"recharts-bar-rectangle"},sr(i.props,f,h),{key:"rectangle-".concat(f==null?void 0:f.x,"-").concat(f==null?void 0:f.y,"-").concat(f==null?void 0:f.value,"-").concat(h)}),A.createElement(Wv,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,s=i.animationBegin,c=i.animationDuration,l=i.animationEasing,f=i.animationId,h=this.state.prevData;return A.createElement(ee,{begin:s,duration:c,isActive:u,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var d=p.t,v=a.map(function(y,g){var b=h&&h[g];if(b){var _=pt(b.x,y.x),w=pt(b.y,y.y),m=pt(b.width,y.width),x=pt(b.height,y.height);return Ot(Ot({},y),{},{x:_(d),y:w(d),width:m(d),height:x(d)})}if(o==="horizontal"){var O=pt(0,y.height),S=O(d);return Ot(Ot({},y),{},{y:y.y+y.height-S,height:S})}var $=pt(0,y.width),k=$(d);return Ot(Ot({},y),{},{width:k})});return A.createElement(Q,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!He(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,s=q(this.props.background,!1);return a.map(function(c,l){c.value;var f=c.background,h=v5(c,d5);if(!f)return null;var p=Ot(Ot(Ot(Ot(Ot({},h),{},{fill:"#eee"},f),s),sr(n.props,c,l)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:l,className:"recharts-bar-background-rectangle"});return A.createElement(Wv,Qo({key:"background-bar-".concat(l),option:n.props.background,isActive:l===u},p))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,s=a.yAxis,c=a.layout,l=a.children,f=Ht(l,si);if(!f)return null;var h=c==="vertical"?o[0].height/2:o[0].width/2,p=function(y,g){var b=Array.isArray(y.value)?y.value[1]:y.value;return{x:y.x,y:y.y,value:b,errorVal:dt(y,g)}},d={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(Q,d,f.map(function(v){return A.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:s,layout:c,offset:h,dataPointFormatter:p})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,s=n.yAxis,c=n.left,l=n.top,f=n.width,h=n.height,p=n.isAnimationActive,d=n.background,v=n.id;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,g=J("recharts-bar",o),b=u&&u.allowDataOverflow,_=s&&s.allowDataOverflow,w=b||_,m=X(v)?this.id:v;return A.createElement(Q,{className:g},b||_?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(m)},A.createElement("rect",{x:b?c:c-f/2,y:_?l:l-h/2,width:b?f:f*2,height:_?h:h*2}))):null,A.createElement(Q,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(m,")"):null},d?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,m),(!p||y)&&ce.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(W.PureComponent);o0=Zr;nr(Zr,"displayName","Bar");nr(Zr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Ee.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});nr(Zr,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,a=t.xAxis,o=t.yAxis,u=t.xAxisTicks,s=t.yAxisTicks,c=t.stackedData,l=t.dataStartIndex,f=t.displayedData,h=t.offset,p=RR(n,r);if(!p)return null;var d=e.layout,v=r.type.defaultProps,y=v!==void 0?Ot(Ot({},v),r.props):r.props,g=y.dataKey,b=y.children,_=y.minPointSize,w=d==="horizontal"?o:a,m=c?w.scale.domain():null,x=KR({numericAxis:w}),O=Ht(b,Fu),S=f.map(function($,k){var T,M,C,P,j,E;c?T=LR(c[l+k],m):(T=dt($,g),Array.isArray(T)||(T=[x,T]));var I=p5(_,o0.defaultProps.minPointSize)(T[1],k);if(d==="horizontal"){var N,R=[o.scale(T[0]),o.scale(T[1])],B=R[0],U=R[1];M=Cd({axis:a,ticks:u,bandSize:i,offset:p.offset,entry:$,index:k}),C=(N=U??B)!==null&&N!==void 0?N:void 0,P=p.size;var z=B-U;if(j=Number.isNaN(z)?0:z,E={x:M,y:o.y,width:P,height:o.height},Math.abs(I)>0&&Math.abs(j)<Math.abs(I)){var G=Ut(j||I)*(Math.abs(I)-Math.abs(j));C-=G,j+=G}}else{var Z=[a.scale(T[0]),a.scale(T[1])],rt=Z[0],ht=Z[1];if(M=rt,C=Cd({axis:o,ticks:s,bandSize:i,offset:p.offset,entry:$,index:k}),P=ht-rt,j=p.size,E={x:a.x,y:C,width:a.width,height:j},Math.abs(I)>0&&Math.abs(P)<Math.abs(I)){var xt=Ut(P||I)*(Math.abs(I)-Math.abs(P));P+=xt}}return Ot(Ot(Ot({},$),{},{x:M,y:C,width:P,height:j,value:c?T:T[1],payload:$,background:E},O&&O[k]&&O[k].props),{},{tooltipPayload:[Tb(r,$)],tooltipPosition:{x:M+P/2,y:C+j/2}})});return Ot({data:S,layout:d},h)});function va(t){"@babel/helpers - typeof";return va=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},va(t)}function A5(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function qv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,c0(n.key),n)}}function P5(t,e,r){return e&&qv(t.prototype,e),r&&qv(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Hv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ve(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Hv(Object(r),!0).forEach(function(n){us(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Hv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function us(t,e,r){return e=c0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function c0(t){var e=S5(t,"string");return va(e)=="symbol"?e:e+""}function S5(t,e){if(va(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(va(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ja=function(e,r,n,i,a){var o=e.width,u=e.height,s=e.layout,c=e.children,l=Object.keys(r),f={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!Jt(c,Zr);return l.reduce(function(p,d){var v=r[d],y=v.orientation,g=v.domain,b=v.padding,_=b===void 0?{}:b,w=v.mirror,m=v.reversed,x="".concat(y).concat(w?"Mirror":""),O,S,$,k,T;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var M=g[1]-g[0],C=1/0,P=v.categoricalDomain.sort(tO);if(P.forEach(function(Z,rt){rt>0&&(C=Math.min((Z||0)-(P[rt-1]||0),C))}),Number.isFinite(C)){var j=C/M,E=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(O=j*E/2),v.padding==="no-gap"){var I=Kt(e.barCategoryGap,j*E),N=j*E/2;O=N-I-(N-I)/E*I}}}i==="xAxis"?S=[n.left+(_.left||0)+(O||0),n.left+n.width-(_.right||0)-(O||0)]:i==="yAxis"?S=s==="horizontal"?[n.top+n.height-(_.bottom||0),n.top+(_.top||0)]:[n.top+(_.top||0)+(O||0),n.top+n.height-(_.bottom||0)-(O||0)]:S=v.range,m&&(S=[S[1],S[0]]);var R=Pb(v,a,h),B=R.scale,U=R.realScaleType;B.domain(g).range(S),Sb(B);var z=$b(B,ve(ve({},v),{},{realScaleType:U}));i==="xAxis"?(T=y==="top"&&!w||y==="bottom"&&w,$=n.left,k=f[x]-T*v.height):i==="yAxis"&&(T=y==="left"&&!w||y==="right"&&w,$=f[x]-T*v.width,k=n.top);var G=ve(ve(ve({},v),z),{},{realScaleType:U,x:$,y:k,scale:B,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return G.bandSize=No(G,z),!v.hide&&i==="xAxis"?f[x]+=(T?-1:1)*G.height:v.hide||(f[x]+=(T?-1:1)*G.width),ve(ve({},p),{},us({},d,G))},{})},l0=function(e,r){var n=e.x,i=e.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},$5=function(e){var r=e.x1,n=e.y1,i=e.x2,a=e.y2;return l0({x:r,y:n},{x:i,y:a})},f0=function(){function t(e){A5(this,t),this.scale=e}return P5(t,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var s=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+s}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new t(r)}}])}();us(f0,"EPS",1e-4);var Hf=function(e){var r=Object.keys(e).reduce(function(n,i){return ve(ve({},n),{},us({},i,f0.create(e[i])))},{});return ve(ve({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return GW(i,function(s,c){return r[c].apply(s,{bandAware:o,position:u})})},isInRange:function(i){return a0(i,function(a,o){return r[o].isInRange(a)})}})};function E5(t){return(t%180+180)%180}var T5=function(e){var r=e.width,n=e.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=E5(i),o=a*Math.PI/180,u=Math.atan(n/r),s=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(s)},j5=$e,M5=Oa,C5=Lu;function k5(t){return function(e,r,n){var i=Object(e);if(!M5(e)){var a=j5(r);e=C5(e),r=function(u){return a(i[u],u,i)}}var o=t(e,r,n);return o>-1?i[a?e[o]:o]:void 0}}var I5=k5,N5=e0;function D5(t){var e=N5(t),r=e%1;return e===e?r?e-r:e:0}var R5=D5,L5=km,B5=$e,z5=R5,F5=Math.max;function W5(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:z5(r);return i<0&&(i=F5(n+i,0)),L5(t,B5(e),i)}var U5=W5,K5=I5,q5=U5,H5=K5(q5),G5=H5;const V5=ft(G5);var X5=n1(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),Gf=W.createContext(void 0),Vf=W.createContext(void 0),h0=W.createContext(void 0),p0=W.createContext({}),d0=W.createContext(void 0),v0=W.createContext(0),y0=W.createContext(0),Gv=function(e){var r=e.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=e.clipPathId,u=e.children,s=e.width,c=e.height,l=X5(a);return A.createElement(Gf.Provider,{value:n},A.createElement(Vf.Provider,{value:i},A.createElement(p0.Provider,{value:a},A.createElement(h0.Provider,{value:l},A.createElement(d0.Provider,{value:o},A.createElement(v0.Provider,{value:c},A.createElement(y0.Provider,{value:s},u)))))))},Y5=function(){return W.useContext(d0)},m0=function(e){var r=W.useContext(Gf);r==null&&Ur();var n=r[e];return n==null&&Ur(),n},Z5=function(){var e=W.useContext(Gf);return er(e)},J5=function(){var e=W.useContext(Vf),r=V5(e,function(n){return a0(n.domain,Number.isFinite)});return r||er(e)},g0=function(e){var r=W.useContext(Vf);r==null&&Ur();var n=r[e];return n==null&&Ur(),n},Q5=function(){var e=W.useContext(h0);return e},tU=function(){return W.useContext(p0)},Xf=function(){return W.useContext(y0)},Yf=function(){return W.useContext(v0)};function Dn(t){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dn(t)}function eU(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function rU(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,x0(n.key),n)}}function nU(t,e,r){return e&&rU(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function iU(t,e,r){return e=eu(e),aU(t,b0()?Reflect.construct(e,r||[],eu(t).constructor):e.apply(t,r))}function aU(t,e){if(e&&(Dn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oU(t)}function oU(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function b0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(b0=function(){return!!t})()}function eu(t){return eu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},eu(t)}function uU(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&vl(t,e)}function vl(t,e){return vl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},vl(t,e)}function Vv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Xv(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Vv(Object(r),!0).forEach(function(n){Zf(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Zf(t,e,r){return e=x0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x0(t){var e=sU(t,"string");return Dn(e)=="symbol"?e:e+""}function sU(t,e){if(Dn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function cU(t,e){return pU(t)||hU(t,e)||fU(t,e)||lU()}function lU(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fU(t,e){if(t){if(typeof t=="string")return Yv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yv(t,e)}}function Yv(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function hU(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function pU(t){if(Array.isArray(t))return t}function yl(){return yl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yl.apply(this,arguments)}var dU=function(e,r){var n;return A.isValidElement(e)?n=A.cloneElement(e,r):V(e)?n=e(r):n=A.createElement("line",yl({},r,{className:"recharts-reference-line-line"})),n},vU=function(e,r,n,i,a,o,u,s,c){var l=a.x,f=a.y,h=a.width,p=a.height;if(n){var d=c.y,v=e.y.apply(d,{position:o});if(Ae(c,"discard")&&!e.y.isInRange(v))return null;var y=[{x:l+h,y:v},{x:l,y:v}];return s==="left"?y.reverse():y}if(r){var g=c.x,b=e.x.apply(g,{position:o});if(Ae(c,"discard")&&!e.x.isInRange(b))return null;var _=[{x:b,y:f+p},{x:b,y:f}];return u==="top"?_.reverse():_}if(i){var w=c.segment,m=w.map(function(x){return e.apply(x,{position:o})});return Ae(c,"discard")&&BW(m,function(x){return!e.isInRange(x)})?null:m}return null};function yU(t){var e=t.x,r=t.y,n=t.segment,i=t.xAxisId,a=t.yAxisId,o=t.shape,u=t.className,s=t.alwaysShow,c=Y5(),l=m0(i),f=g0(a),h=Q5();if(!c||!h)return null;ge(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var p=Hf({x:l.scale,y:f.scale}),d=Et(e),v=Et(r),y=n&&n.length===2,g=vU(p,d,v,y,h,t.position,l.orientation,f.orientation,t);if(!g)return null;var b=cU(g,2),_=b[0],w=_.x,m=_.y,x=b[1],O=x.x,S=x.y,$=Ae(t,"hidden")?"url(#".concat(c,")"):void 0,k=Xv(Xv({clipPath:$},q(t,!0)),{},{x1:w,y1:m,x2:O,y2:S});return A.createElement(Q,{className:J("recharts-reference-line",u)},dU(o,k),Mt.renderCallByParent(t,$5({x1:w,y1:m,x2:O,y2:S})))}var Jf=function(t){function e(){return eU(this,e),iU(this,e,arguments)}return uU(e,t),nU(e,[{key:"render",value:function(){return A.createElement(yU,this.props)}}])}(A.Component);Zf(Jf,"displayName","ReferenceLine");Zf(Jf,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function ml(){return ml=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ml.apply(this,arguments)}function Rn(t){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Rn(t)}function Zv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Jv(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Zv(Object(r),!0).forEach(function(n){ss(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function mU(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gU(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,w0(n.key),n)}}function bU(t,e,r){return e&&gU(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function xU(t,e,r){return e=ru(e),_U(t,_0()?Reflect.construct(e,r||[],ru(t).constructor):e.apply(t,r))}function _U(t,e){if(e&&(Rn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wU(t)}function wU(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(_0=function(){return!!t})()}function ru(t){return ru=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ru(t)}function OU(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gl(t,e)}function gl(t,e){return gl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gl(t,e)}function ss(t,e,r){return e=w0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w0(t){var e=AU(t,"string");return Rn(e)=="symbol"?e:e+""}function AU(t,e){if(Rn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var PU=function(e){var r=e.x,n=e.y,i=e.xAxis,a=e.yAxis,o=Hf({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return Ae(e,"discard")&&!o.isInRange(u)?null:u},cs=function(t){function e(){return mU(this,e),xU(this,e,arguments)}return OU(e,t),bU(e,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,s=n.clipPathId,c=Et(i),l=Et(a);if(ge(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var f=PU(this.props);if(!f)return null;var h=f.x,p=f.y,d=this.props,v=d.shape,y=d.className,g=Ae(this.props,"hidden")?"url(#".concat(s,")"):void 0,b=Jv(Jv({clipPath:g},q(this.props,!0)),{},{cx:h,cy:p});return A.createElement(Q,{className:J("recharts-reference-dot",y)},e.renderDot(v,b),Mt.renderCallByParent(this.props,{x:h-o,y:p-o,width:2*o,height:2*o}))}}])}(A.Component);ss(cs,"displayName","ReferenceDot");ss(cs,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});ss(cs,"renderDot",function(t,e){var r;return A.isValidElement(t)?r=A.cloneElement(t,e):V(t)?r=t(e):r=A.createElement(ci,ml({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"})),r});function bl(){return bl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},bl.apply(this,arguments)}function Ln(t){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ln(t)}function Qv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ty(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Qv(Object(r),!0).forEach(function(n){ls(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Qv(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function SU(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function $U(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,A0(n.key),n)}}function EU(t,e,r){return e&&$U(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function TU(t,e,r){return e=nu(e),jU(t,O0()?Reflect.construct(e,r||[],nu(t).constructor):e.apply(t,r))}function jU(t,e){if(e&&(Ln(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return MU(t)}function MU(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function O0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(O0=function(){return!!t})()}function nu(t){return nu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},nu(t)}function CU(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&xl(t,e)}function xl(t,e){return xl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},xl(t,e)}function ls(t,e,r){return e=A0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A0(t){var e=kU(t,"string");return Ln(e)=="symbol"?e:e+""}function kU(t,e){if(Ln(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var IU=function(e,r,n,i,a){var o=a.x1,u=a.x2,s=a.y1,c=a.y2,l=a.xAxis,f=a.yAxis;if(!l||!f)return null;var h=Hf({x:l.scale,y:f.scale}),p={x:e?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(s,{position:"start"}):h.y.rangeMin},d={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(c,{position:"end"}):h.y.rangeMax};return Ae(a,"discard")&&(!h.isInRange(p)||!h.isInRange(d))?null:l0(p,d)},fs=function(t){function e(){return SU(this,e),TU(this,e,arguments)}return CU(e,t),EU(e,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,s=n.className,c=n.alwaysShow,l=n.clipPathId;ge(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=Et(i),h=Et(a),p=Et(o),d=Et(u),v=this.props.shape;if(!f&&!h&&!p&&!d&&!v)return null;var y=IU(f,h,p,d,this.props);if(!y&&!v)return null;var g=Ae(this.props,"hidden")?"url(#".concat(l,")"):void 0;return A.createElement(Q,{className:J("recharts-reference-area",s)},e.renderRect(v,ty(ty({clipPath:g},q(this.props,!0)),y)),Mt.renderCallByParent(this.props,y))}}])}(A.Component);ls(fs,"displayName","ReferenceArea");ls(fs,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});ls(fs,"renderRect",function(t,e){var r;return A.isValidElement(t)?r=A.cloneElement(t,e):V(t)?r=t(e):r=A.createElement(qf,bl({},e,{className:"recharts-reference-area-rect"})),r});function P0(t,e,r){if(e<1)return[];if(e===1&&r===void 0)return t;for(var n=[],i=0;i<t.length;i+=e)n.push(t[i]);return n}function NU(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return T5(n,r)}function DU(t,e,r){var n=r==="width",i=t.x,a=t.y,o=t.width,u=t.height;return e===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function iu(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var a=r();return t*(e-t*a/2-n)>=0&&t*(e+t*a/2-i)<=0}function RU(t,e){return P0(t,e+1)}function LU(t,e,r,n,i){for(var a=(n||[]).slice(),o=e.start,u=e.end,s=0,c=1,l=o,f=function(){var d=n==null?void 0:n[s];if(d===void 0)return{v:P0(n,c)};var v=s,y,g=function(){return y===void 0&&(y=r(d,v)),y},b=d.coordinate,_=s===0||iu(t,b,g,l,u);_||(s=0,l=o,c+=1),_&&(l=b+t*(g()/2+i),s+=c)},h;c<=a.length;)if(h=f(),h)return h.v;return[]}function ya(t){"@babel/helpers - typeof";return ya=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ya(t)}function ey(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Bt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ey(Object(r),!0).forEach(function(n){BU(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function BU(t,e,r){return e=zU(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function zU(t){var e=FU(t,"string");return ya(e)=="symbol"?e:e+""}function FU(t,e){if(ya(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ya(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function WU(t,e,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=e.start,s=e.end,c=function(h){var p=a[h],d,v=function(){return d===void 0&&(d=r(p,h)),d};if(h===o-1){var y=t*(p.coordinate+t*v()/2-s);a[h]=p=Bt(Bt({},p),{},{tickCoord:y>0?p.coordinate-y*t:p.coordinate})}else a[h]=p=Bt(Bt({},p),{},{tickCoord:p.coordinate});var g=iu(t,p.tickCoord,v,u,s);g&&(s=p.tickCoord-t*(v()/2+i),a[h]=Bt(Bt({},p),{},{isShow:!0}))},l=o-1;l>=0;l--)c(l);return a}function UU(t,e,r,n,i,a){var o=(n||[]).slice(),u=o.length,s=e.start,c=e.end;if(a){var l=n[u-1],f=r(l,u-1),h=t*(l.coordinate+t*f/2-c);o[u-1]=l=Bt(Bt({},l),{},{tickCoord:h>0?l.coordinate-h*t:l.coordinate});var p=iu(t,l.tickCoord,function(){return f},s,c);p&&(c=l.tickCoord-t*(f/2+i),o[u-1]=Bt(Bt({},l),{},{isShow:!0}))}for(var d=a?u-1:u,v=function(b){var _=o[b],w,m=function(){return w===void 0&&(w=r(_,b)),w};if(b===0){var x=t*(_.coordinate-t*m()/2-s);o[b]=_=Bt(Bt({},_),{},{tickCoord:x<0?_.coordinate-x*t:_.coordinate})}else o[b]=_=Bt(Bt({},_),{},{tickCoord:_.coordinate});var O=iu(t,_.tickCoord,m,s,c);O&&(s=_.tickCoord+t*(m()/2+i),o[b]=Bt(Bt({},_),{},{isShow:!0}))},y=0;y<d;y++)v(y);return o}function Qf(t,e,r){var n=t.tick,i=t.ticks,a=t.viewBox,o=t.minTickGap,u=t.orientation,s=t.interval,c=t.tickFormatter,l=t.unit,f=t.angle;if(!i||!i.length||!n)return[];if(F(s)||Ee.isSsr)return RU(i,typeof s=="number"&&F(s)?s:0);var h=[],p=u==="top"||u==="bottom"?"width":"height",d=l&&p==="width"?$i(l,{fontSize:e,letterSpacing:r}):{width:0,height:0},v=function(_,w){var m=V(c)?c(_.value,w):_.value;return p==="width"?NU($i(m,{fontSize:e,letterSpacing:r}),d,f):$i(m,{fontSize:e,letterSpacing:r})[p]},y=i.length>=2?Ut(i[1].coordinate-i[0].coordinate):1,g=DU(a,y,p);return s==="equidistantPreserveStart"?LU(y,g,v,i,o):(s==="preserveStart"||s==="preserveStartEnd"?h=UU(y,g,v,i,o,s==="preserveStartEnd"):h=WU(y,g,v,i,o),h.filter(function(b){return b.isShow}))}var KU=["viewBox"],qU=["viewBox"],HU=["ticks"];function Bn(t){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bn(t)}function ln(){return ln=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ln.apply(this,arguments)}function ry(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Ft(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ry(Object(r),!0).forEach(function(n){th(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ry(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Hs(t,e){if(t==null)return{};var r=GU(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function GU(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function VU(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ny(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,$0(n.key),n)}}function XU(t,e,r){return e&&ny(t.prototype,e),r&&ny(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function YU(t,e,r){return e=au(e),ZU(t,S0()?Reflect.construct(e,r||[],au(t).constructor):e.apply(t,r))}function ZU(t,e){if(e&&(Bn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return JU(t)}function JU(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function S0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(S0=function(){return!!t})()}function au(t){return au=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},au(t)}function QU(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_l(t,e)}function _l(t,e){return _l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_l(t,e)}function th(t,e,r){return e=$0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $0(t){var e=t8(t,"string");return Bn(e)=="symbol"?e:e+""}function t8(t,e){if(Bn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var li=function(t){function e(r){var n;return VU(this,e),n=YU(this,e,[r]),n.state={fontSize:"",letterSpacing:""},n}return QU(e,t),XU(e,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=Hs(n,KU),u=this.props,s=u.viewBox,c=Hs(u,qU);return!hn(a,s)||!hn(o,c)||!hn(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,s=i.height,c=i.orientation,l=i.tickSize,f=i.mirror,h=i.tickMargin,p,d,v,y,g,b,_=f?-1:1,w=n.tickSize||l,m=F(n.tickCoord)?n.tickCoord:n.coordinate;switch(c){case"top":p=d=n.coordinate,y=o+ +!f*s,v=y-_*w,b=v-_*h,g=m;break;case"left":v=y=n.coordinate,d=a+ +!f*u,p=d-_*w,g=p-_*h,b=m;break;case"right":v=y=n.coordinate,d=a+ +f*u,p=d+_*w,g=p+_*h,b=m;break;default:p=d=n.coordinate,y=o+ +f*s,v=y+_*w,b=v+_*h,g=m;break}return{line:{x1:p,y1:v,x2:d,y2:y},tick:{x:g,y:b}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.orientation,c=n.mirror,l=n.axisLine,f=Ft(Ft(Ft({},q(this.props,!1)),q(l,!1)),{},{fill:"none"});if(s==="top"||s==="bottom"){var h=+(s==="top"&&!c||s==="bottom"&&c);f=Ft(Ft({},f),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var p=+(s==="left"&&!c||s==="right"&&c);f=Ft(Ft({},f),{},{x1:i+p*o,y1:a,x2:i+p*o,y2:a+u})}return A.createElement("line",ln({},f,{className:J("recharts-cartesian-axis-line",te(l,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,s=u.tickLine,c=u.stroke,l=u.tick,f=u.tickFormatter,h=u.unit,p=Qf(Ft(Ft({},this.props),{},{ticks:n}),i,a),d=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),y=q(this.props,!1),g=q(l,!1),b=Ft(Ft({},y),{},{fill:"none"},q(s,!1)),_=p.map(function(w,m){var x=o.getTickLineCoord(w),O=x.line,S=x.tick,$=Ft(Ft(Ft(Ft({textAnchor:d,verticalAnchor:v},y),{},{stroke:"none",fill:c},g),S),{},{index:m,payload:w,visibleTicksCount:p.length,tickFormatter:f});return A.createElement(Q,ln({className:"recharts-cartesian-axis-tick",key:"tick-".concat(w.value,"-").concat(w.coordinate,"-").concat(w.tickCoord)},sr(o.props,w,m)),s&&A.createElement("line",ln({},b,O,{className:J("recharts-cartesian-axis-tick-line",te(s,"className"))})),l&&e.renderTickItem(l,$,"".concat(V(f)?f(w.value,m):w.value).concat(h||"")))});return A.createElement("g",{className:"recharts-cartesian-axis-ticks"},_)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,s=i.ticksGenerator,c=i.className,l=i.hide;if(l)return null;var f=this.props,h=f.ticks,p=Hs(f,HU),d=h;return V(s)&&(d=h&&h.length>0?s(this.props):s(p)),o<=0||u<=0||!d||!d.length?null:A.createElement(Q,{className:J("recharts-cartesian-axis",c),ref:function(y){n.layerReference=y}},a&&this.renderAxisLine(),this.renderTicks(d,this.state.fontSize,this.state.letterSpacing),Mt.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):V(n)?o=n(i):o=A.createElement(Br,ln({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(W.Component);th(li,"displayName","CartesianAxis");th(li,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var e8=["x1","y1","x2","y2","key"],r8=["offset"];function Kr(t){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kr(t)}function iy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function zt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?iy(Object(r),!0).forEach(function(n){n8(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):iy(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function n8(t,e,r){return e=i8(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i8(t){var e=a8(t,"string");return Kr(e)=="symbol"?e:e+""}function a8(t,e){if(Kr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function kr(){return kr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kr.apply(this,arguments)}function ay(t,e){if(t==null)return{};var r=o8(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function o8(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var u8=function(e){var r=e.fill;if(!r||r==="none")return null;var n=e.fillOpacity,i=e.x,a=e.y,o=e.width,u=e.height,s=e.ry;return A.createElement("rect",{x:i,y:a,ry:s,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function E0(t,e){var r;if(A.isValidElement(t))r=A.cloneElement(t,e);else if(V(t))r=t(e);else{var n=e.x1,i=e.y1,a=e.x2,o=e.y2,u=e.key,s=ay(e,e8),c=q(s,!1);c.offset;var l=ay(c,r8);r=A.createElement("line",kr({},l,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function s8(t){var e=t.x,r=t.width,n=t.horizontal,i=n===void 0?!0:n,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,s){var c=zt(zt({},t),{},{x1:e,y1:u,x2:e+r,y2:u,key:"line-".concat(s),index:s});return E0(i,c)});return A.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function c8(t){var e=t.y,r=t.height,n=t.vertical,i=n===void 0?!0:n,a=t.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,s){var c=zt(zt({},t),{},{x1:u,y1:e,x2:u,y2:e+r,key:"line-".concat(s),index:s});return E0(i,c)});return A.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function l8(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,a=t.width,o=t.height,u=t.horizontalPoints,s=t.horizontal,c=s===void 0?!0:s;if(!c||!e||!e.length)return null;var l=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,p){return h-p});i!==l[0]&&l.unshift(0);var f=l.map(function(h,p){var d=!l[p+1],v=d?i+o-h:l[p+1]-h;if(v<=0)return null;var y=p%e.length;return A.createElement("rect",{key:"react-".concat(p),y:h,x:n,height:v,width:a,stroke:"none",fill:e[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function f8(t){var e=t.vertical,r=e===void 0?!0:e,n=t.verticalFill,i=t.fillOpacity,a=t.x,o=t.y,u=t.width,s=t.height,c=t.verticalPoints;if(!r||!n||!n.length)return null;var l=c.map(function(h){return Math.round(h+a-a)}).sort(function(h,p){return h-p});a!==l[0]&&l.unshift(0);var f=l.map(function(h,p){var d=!l[p+1],v=d?a+u-h:l[p+1]-h;if(v<=0)return null;var y=p%n.length;return A.createElement("rect",{key:"react-".concat(p),x:h,y:o,width:v,height:s,stroke:"none",fill:n[y],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var h8=function(e,r){var n=e.xAxis,i=e.width,a=e.height,o=e.offset;return Ab(Qf(zt(zt(zt({},li.defaultProps),n),{},{ticks:Be(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},p8=function(e,r){var n=e.yAxis,i=e.width,a=e.height,o=e.offset;return Ab(Qf(zt(zt(zt({},li.defaultProps),n),{},{ticks:Be(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},an={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function d8(t){var e,r,n,i,a,o,u=Xf(),s=Yf(),c=tU(),l=zt(zt({},t),{},{stroke:(e=t.stroke)!==null&&e!==void 0?e:an.stroke,fill:(r=t.fill)!==null&&r!==void 0?r:an.fill,horizontal:(n=t.horizontal)!==null&&n!==void 0?n:an.horizontal,horizontalFill:(i=t.horizontalFill)!==null&&i!==void 0?i:an.horizontalFill,vertical:(a=t.vertical)!==null&&a!==void 0?a:an.vertical,verticalFill:(o=t.verticalFill)!==null&&o!==void 0?o:an.verticalFill,x:F(t.x)?t.x:c.left,y:F(t.y)?t.y:c.top,width:F(t.width)?t.width:c.width,height:F(t.height)?t.height:c.height}),f=l.x,h=l.y,p=l.width,d=l.height,v=l.syncWithTicks,y=l.horizontalValues,g=l.verticalValues,b=Z5(),_=J5();if(!F(p)||p<=0||!F(d)||d<=0||!F(f)||f!==+f||!F(h)||h!==+h)return null;var w=l.verticalCoordinatesGenerator||h8,m=l.horizontalCoordinatesGenerator||p8,x=l.horizontalPoints,O=l.verticalPoints;if((!x||!x.length)&&V(m)){var S=y&&y.length,$=m({yAxis:_?zt(zt({},_),{},{ticks:S?y:_.ticks}):void 0,width:u,height:s,offset:c},S?!0:v);ge(Array.isArray($),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Kr($),"]")),Array.isArray($)&&(x=$)}if((!O||!O.length)&&V(w)){var k=g&&g.length,T=w({xAxis:b?zt(zt({},b),{},{ticks:k?g:b.ticks}):void 0,width:u,height:s,offset:c},k?!0:v);ge(Array.isArray(T),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Kr(T),"]")),Array.isArray(T)&&(O=T)}return A.createElement("g",{className:"recharts-cartesian-grid"},A.createElement(u8,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),A.createElement(s8,kr({},l,{offset:c,horizontalPoints:x,xAxis:b,yAxis:_})),A.createElement(c8,kr({},l,{offset:c,verticalPoints:O,xAxis:b,yAxis:_})),A.createElement(l8,kr({},l,{horizontalPoints:x})),A.createElement(f8,kr({},l,{verticalPoints:O})))}d8.displayName="CartesianGrid";var v8=["type","layout","connectNulls","ref"],y8=["key"];function zn(t){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zn(t)}function oy(t,e){if(t==null)return{};var r=m8(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function m8(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function ki(){return ki=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ki.apply(this,arguments)}function uy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Yt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?uy(Object(r),!0).forEach(function(n){ye(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uy(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function on(t){return _8(t)||x8(t)||b8(t)||g8()}function g8(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function b8(t,e){if(t){if(typeof t=="string")return wl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wl(t,e)}}function x8(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function _8(t){if(Array.isArray(t))return wl(t)}function wl(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function w8(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function sy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,j0(n.key),n)}}function O8(t,e,r){return e&&sy(t.prototype,e),r&&sy(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function A8(t,e,r){return e=ou(e),P8(t,T0()?Reflect.construct(e,r||[],ou(t).constructor):e.apply(t,r))}function P8(t,e){if(e&&(zn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return S8(t)}function S8(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function T0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(T0=function(){return!!t})()}function ou(t){return ou=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ou(t)}function $8(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ol(t,e)}function Ol(t,e){return Ol=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ol(t,e)}function ye(t,e,r){return e=j0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j0(t){var e=E8(t,"string");return zn(e)=="symbol"?e:e+""}function E8(t,e){if(zn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ma=function(t){function e(){var r;w8(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=A8(this,e,[].concat(i)),ye(r,"state",{isAnimationFinished:!0,totalLength:0}),ye(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),ye(r,"getStrokeDasharray",function(o,u,s){var c=s.reduce(function(g,b){return g+b});if(!c)return r.generateSimpleStrokeDasharray(u,o);for(var l=Math.floor(o/c),f=o%c,h=u-o,p=[],d=0,v=0;d<s.length;v+=s[d],++d)if(v+s[d]>f){p=[].concat(on(s.slice(0,d)),[f-v]);break}var y=p.length%2===0?[0,h]:[h];return[].concat(on(e.repeat(s,l)),on(p),y).map(function(g){return"".concat(g,"px")}).join(", ")}),ye(r,"id",hr("recharts-line-")),ye(r,"pathRef",function(o){r.mainCurve=o}),ye(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),ye(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return $8(e,t),O8(e,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,s=a.yAxis,c=a.layout,l=a.children,f=Ht(l,si);if(!f)return null;var h=function(v,y){return{x:v.x,y:v.y,value:v.value,errorVal:dt(v.payload,y)}},p={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(Q,p,f.map(function(d){return A.cloneElement(d,{key:"bar-".concat(d.props.dataKey),data:o,xAxis:u,yAxis:s,layout:c,dataPointFormatter:h})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,s=u.dot,c=u.points,l=u.dataKey,f=q(this.props,!1),h=q(s,!0),p=c.map(function(v,y){var g=Yt(Yt(Yt({key:"dot-".concat(y),r:3},f),h),{},{index:y,cx:v.x,cy:v.y,value:v.value,dataKey:l,payload:v.payload,points:c});return e.renderDotItem(s,g)}),d={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return A.createElement(Q,ki({className:"recharts-line-dots",key:"dots"},d),p)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,s=u.type,c=u.layout,l=u.connectNulls;u.ref;var f=oy(u,v8),h=Yt(Yt(Yt({},q(f,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:s,layout:c,connectNulls:l});return A.createElement(ur,ki({},h,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,s=o.strokeDasharray,c=o.isAnimationActive,l=o.animationBegin,f=o.animationDuration,h=o.animationEasing,p=o.animationId,d=o.animateNewValues,v=o.width,y=o.height,g=this.state,b=g.prevPoints,_=g.totalLength;return A.createElement(ee,{begin:l,duration:f,isActive:c,easing:h,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(w){var m=w.t;if(b){var x=b.length/u.length,O=u.map(function(M,C){var P=Math.floor(C*x);if(b[P]){var j=b[P],E=pt(j.x,M.x),I=pt(j.y,M.y);return Yt(Yt({},M),{},{x:E(m),y:I(m)})}if(d){var N=pt(v*2,M.x),R=pt(y/2,M.y);return Yt(Yt({},M),{},{x:N(m),y:R(m)})}return Yt(Yt({},M),{},{x:M.x,y:M.y})});return a.renderCurveStatically(O,n,i)}var S=pt(0,_),$=S(m),k;if(s){var T="".concat(s).split(/[,\s]+/gim).map(function(M){return parseFloat(M)});k=a.getStrokeDasharray($,_,T)}else k=a.generateSimpleStrokeDasharray(_,$);return a.renderCurveStatically(u,n,i,{strokeDasharray:k})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,s=this.state,c=s.prevPoints,l=s.totalLength;return u&&o&&o.length&&(!c&&l>0||!He(c,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,s=i.className,c=i.xAxis,l=i.yAxis,f=i.top,h=i.left,p=i.width,d=i.height,v=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var g=this.state.isAnimationFinished,b=u.length===1,_=J("recharts-line",s),w=c&&c.allowDataOverflow,m=l&&l.allowDataOverflow,x=w||m,O=X(y)?this.id:y,S=(n=q(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},$=S.r,k=$===void 0?3:$,T=S.strokeWidth,M=T===void 0?2:T,C=Fy(o)?o:{},P=C.clipDot,j=P===void 0?!0:P,E=k*2+M;return A.createElement(Q,{className:_},w||m?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(O)},A.createElement("rect",{x:w?h:h-p/2,y:m?f:f-d/2,width:w?p:p*2,height:m?d:d*2})),!j&&A.createElement("clipPath",{id:"clipPath-dots-".concat(O)},A.createElement("rect",{x:h-E/2,y:f-E/2,width:p+E,height:d+E}))):null,!b&&this.renderCurve(x,O),this.renderErrorBar(x,O),(b||o)&&this.renderDots(x,j,O),(!v||g)&&ce.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(on(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(on(o),on(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(A.isValidElement(n))a=A.cloneElement(n,i);else if(V(n))a=n(i);else{var o=i.key,u=oy(i,y8),s=J("recharts-line-dot",typeof n!="boolean"?n.className:"");a=A.createElement(ci,ki({key:o},u,{className:s}))}return a}}])}(W.PureComponent);ye(Ma,"displayName","Line");ye(Ma,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!Ee.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});ye(Ma,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,i=t.xAxisTicks,a=t.yAxisTicks,o=t.dataKey,u=t.bandSize,s=t.displayedData,c=t.offset,l=e.layout,f=s.map(function(h,p){var d=dt(h,o);return l==="horizontal"?{x:$n({axis:r,ticks:i,bandSize:u,entry:h,index:p}),y:X(d)?null:n.scale(d),value:d,payload:h}:{x:X(d)?null:r.scale(d),y:$n({axis:n,ticks:a,bandSize:u,entry:h,index:p}),value:d,payload:h}});return Yt({points:f,layout:l},c)});var T8=["layout","type","stroke","connectNulls","isRange","ref"],j8=["key"],M0;function Fn(t){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fn(t)}function C0(t,e){if(t==null)return{};var r=M8(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function M8(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Ir(){return Ir=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ir.apply(this,arguments)}function cy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Qe(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?cy(Object(r),!0).forEach(function(n){_e(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cy(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function C8(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ly(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,I0(n.key),n)}}function k8(t,e,r){return e&&ly(t.prototype,e),r&&ly(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function I8(t,e,r){return e=uu(e),N8(t,k0()?Reflect.construct(e,r||[],uu(t).constructor):e.apply(t,r))}function N8(t,e){if(e&&(Fn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return D8(t)}function D8(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function k0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(k0=function(){return!!t})()}function uu(t){return uu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},uu(t)}function R8(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Al(t,e)}function Al(t,e){return Al=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Al(t,e)}function _e(t,e,r){return e=I0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I0(t){var e=L8(t,"string");return Fn(e)=="symbol"?e:e+""}function L8(t,e){if(Fn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var gr=function(t){function e(){var r;C8(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=I8(this,e,[].concat(i)),_e(r,"state",{isAnimationFinished:!0}),_e(r,"id",hr("recharts-area-")),_e(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),V(o)&&o()}),_e(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),V(o)&&o()}),r}return R8(e,t),k8(e,[{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive,u=this.state.isAnimationFinished;if(o&&!u)return null;var s=this.props,c=s.dot,l=s.points,f=s.dataKey,h=q(this.props,!1),p=q(c,!0),d=l.map(function(y,g){var b=Qe(Qe(Qe({key:"dot-".concat(g),r:3},h),p),{},{index:g,cx:y.x,cy:y.y,dataKey:f,value:y.value,payload:y.payload,points:l});return e.renderDotItem(c,b)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return A.createElement(Q,Ir({className:"recharts-area-dots"},v),d)}},{key:"renderHorizontalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,s=o[0].x,c=o[o.length-1].x,l=n*Math.abs(s-c),f=rr(o.map(function(h){return h.y||0}));return F(a)&&typeof a=="number"?f=Math.max(a,f):a&&Array.isArray(a)&&a.length&&(f=Math.max(rr(a.map(function(h){return h.y||0})),f)),F(f)?A.createElement("rect",{x:s<c?s:s-l,y:0,width:l,height:Math.floor(f+(u?parseInt("".concat(u),10):1))}):null}},{key:"renderVerticalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,s=o[0].y,c=o[o.length-1].y,l=n*Math.abs(s-c),f=rr(o.map(function(h){return h.x||0}));return F(a)&&typeof a=="number"?f=Math.max(a,f):a&&Array.isArray(a)&&a.length&&(f=Math.max(rr(a.map(function(h){return h.x||0})),f)),F(f)?A.createElement("rect",{x:0,y:s<c?s:s-l,width:f+(u?parseInt("".concat(u),10):1),height:Math.floor(l)}):null}},{key:"renderClipRect",value:function(n){var i=this.props.layout;return i==="vertical"?this.renderVerticalRect(n):this.renderHorizontalRect(n)}},{key:"renderAreaStatically",value:function(n,i,a,o){var u=this.props,s=u.layout,c=u.type,l=u.stroke,f=u.connectNulls,h=u.isRange;u.ref;var p=C0(u,T8);return A.createElement(Q,{clipPath:a?"url(#clipPath-".concat(o,")"):null},A.createElement(ur,Ir({},q(p,!0),{points:n,connectNulls:f,type:c,baseLine:i,layout:s,stroke:"none",className:"recharts-area-area"})),l!=="none"&&A.createElement(ur,Ir({},q(this.props,!1),{className:"recharts-area-curve",layout:s,type:c,connectNulls:f,fill:"none",points:n})),l!=="none"&&h&&A.createElement(ur,Ir({},q(this.props,!1),{className:"recharts-area-curve",layout:s,type:c,connectNulls:f,fill:"none",points:i})))}},{key:"renderAreaWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,s=o.baseLine,c=o.isAnimationActive,l=o.animationBegin,f=o.animationDuration,h=o.animationEasing,p=o.animationId,d=this.state,v=d.prevPoints,y=d.prevBaseLine;return A.createElement(ee,{begin:l,duration:f,isActive:c,easing:h,from:{t:0},to:{t:1},key:"area-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(g){var b=g.t;if(v){var _=v.length/u.length,w=u.map(function(S,$){var k=Math.floor($*_);if(v[k]){var T=v[k],M=pt(T.x,S.x),C=pt(T.y,S.y);return Qe(Qe({},S),{},{x:M(b),y:C(b)})}return S}),m;if(F(s)&&typeof s=="number"){var x=pt(y,s);m=x(b)}else if(X(s)||ei(s)){var O=pt(y,0);m=O(b)}else m=s.map(function(S,$){var k=Math.floor($*_);if(y[k]){var T=y[k],M=pt(T.x,S.x),C=pt(T.y,S.y);return Qe(Qe({},S),{},{x:M(b),y:C(b)})}return S});return a.renderAreaStatically(w,m,n,i)}return A.createElement(Q,null,A.createElement("defs",null,A.createElement("clipPath",{id:"animationClipPath-".concat(i)},a.renderClipRect(b))),A.createElement(Q,{clipPath:"url(#animationClipPath-".concat(i,")")},a.renderAreaStatically(u,s,n,i)))})}},{key:"renderArea",value:function(n,i){var a=this.props,o=a.points,u=a.baseLine,s=a.isAnimationActive,c=this.state,l=c.prevPoints,f=c.prevBaseLine,h=c.totalLength;return s&&o&&o.length&&(!l&&h>0||!He(l,o)||!He(f,u))?this.renderAreaWithAnimation(n,i):this.renderAreaStatically(o,u,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,s=i.className,c=i.top,l=i.left,f=i.xAxis,h=i.yAxis,p=i.width,d=i.height,v=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var g=this.state.isAnimationFinished,b=u.length===1,_=J("recharts-area",s),w=f&&f.allowDataOverflow,m=h&&h.allowDataOverflow,x=w||m,O=X(y)?this.id:y,S=(n=q(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},$=S.r,k=$===void 0?3:$,T=S.strokeWidth,M=T===void 0?2:T,C=Fy(o)?o:{},P=C.clipDot,j=P===void 0?!0:P,E=k*2+M;return A.createElement(Q,{className:_},w||m?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(O)},A.createElement("rect",{x:w?l:l-p/2,y:m?c:c-d/2,width:w?p:p*2,height:m?d:d*2})),!j&&A.createElement("clipPath",{id:"clipPath-dots-".concat(O)},A.createElement("rect",{x:l-E/2,y:c-E/2,width:p+E,height:d+E}))):null,b?null:this.renderArea(x,O),(o||b)&&this.renderDots(x,j,O),(!v||g)&&ce.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,curBaseLine:n.baseLine,prevPoints:i.curPoints,prevBaseLine:i.curBaseLine}:n.points!==i.curPoints||n.baseLine!==i.curBaseLine?{curPoints:n.points,curBaseLine:n.baseLine}:null}}])}(W.PureComponent);M0=gr;_e(gr,"displayName","Area");_e(gr,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!Ee.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});_e(gr,"getBaseValue",function(t,e,r,n){var i=t.layout,a=t.baseValue,o=e.props.baseValue,u=o??a;if(F(u)&&typeof u=="number")return u;var s=i==="horizontal"?n:r,c=s.scale.domain();if(s.type==="number"){var l=Math.max(c[0],c[1]),f=Math.min(c[0],c[1]);return u==="dataMin"?f:u==="dataMax"||l<0?l:Math.max(Math.min(c[0],c[1]),0)}return u==="dataMin"?c[0]:u==="dataMax"?c[1]:c[0]});_e(gr,"getComposedData",function(t){var e=t.props,r=t.item,n=t.xAxis,i=t.yAxis,a=t.xAxisTicks,o=t.yAxisTicks,u=t.bandSize,s=t.dataKey,c=t.stackedData,l=t.dataStartIndex,f=t.displayedData,h=t.offset,p=e.layout,d=c&&c.length,v=M0.getBaseValue(e,r,n,i),y=p==="horizontal",g=!1,b=f.map(function(w,m){var x;d?x=c[l+m]:(x=dt(w,s),Array.isArray(x)?g=!0:x=[v,x]);var O=x[1]==null||d&&dt(w,s)==null;return y?{x:$n({axis:n,ticks:a,bandSize:u,entry:w,index:m}),y:O?null:i.scale(x[1]),value:x,payload:w}:{x:O?null:n.scale(x[1]),y:$n({axis:i,ticks:o,bandSize:u,entry:w,index:m}),value:x,payload:w}}),_;return d||g?_=b.map(function(w){var m=Array.isArray(w.value)?w.value[0]:null;return y?{x:w.x,y:m!=null&&w.y!=null?i.scale(m):null}:{x:m!=null?n.scale(m):null,y:w.y}}):_=y?i.scale(v):n.scale(v),Qe({points:b,baseLine:_,layout:p,isRange:g},h)});_e(gr,"renderDotItem",function(t,e){var r;if(A.isValidElement(t))r=A.cloneElement(t,e);else if(V(t))r=t(e);else{var n=J("recharts-area-dot",typeof t!="boolean"?t.className:""),i=e.key,a=C0(e,j8);r=A.createElement(ci,Ir({},a,{key:i,className:n}))}return r});function Wn(t){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wn(t)}function B8(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function z8(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,R0(n.key),n)}}function F8(t,e,r){return e&&z8(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function W8(t,e,r){return e=su(e),U8(t,N0()?Reflect.construct(e,r||[],su(t).constructor):e.apply(t,r))}function U8(t,e){if(e&&(Wn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return K8(t)}function K8(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function N0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(N0=function(){return!!t})()}function su(t){return su=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},su(t)}function q8(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pl(t,e)}function Pl(t,e){return Pl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pl(t,e)}function D0(t,e,r){return e=R0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function R0(t){var e=H8(t,"string");return Wn(e)=="symbol"?e:e+""}function H8(t,e){if(Wn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ca=function(t){function e(){return B8(this,e),W8(this,e,arguments)}return q8(e,t),F8(e,[{key:"render",value:function(){return null}}])}(A.Component);D0(Ca,"displayName","ZAxis");D0(Ca,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var G8=["option","isActive"];function Ii(){return Ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ii.apply(this,arguments)}function V8(t,e){if(t==null)return{};var r=X8(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function X8(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Y8(t){var e=t.option,r=t.isActive,n=V8(t,G8);return typeof e=="string"?A.createElement(Ho,Ii({option:A.createElement(Ru,Ii({type:e},n)),isActive:r,shapeType:"symbols"},n)):A.createElement(Ho,Ii({option:e,isActive:r,shapeType:"symbols"},n))}function Un(t){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Un(t)}function Ni(){return Ni=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ni.apply(this,arguments)}function fy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ae(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?fy(Object(r),!0).forEach(function(n){ir(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fy(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Z8(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function hy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B0(n.key),n)}}function J8(t,e,r){return e&&hy(t.prototype,e),r&&hy(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Q8(t,e,r){return e=cu(e),tK(t,L0()?Reflect.construct(e,r||[],cu(t).constructor):e.apply(t,r))}function tK(t,e){if(e&&(Un(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return eK(t)}function eK(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function L0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(L0=function(){return!!t})()}function cu(t){return cu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},cu(t)}function rK(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Sl(t,e)}function Sl(t,e){return Sl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Sl(t,e)}function ir(t,e,r){return e=B0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B0(t){var e=nK(t,"string");return Un(e)=="symbol"?e:e+""}function nK(t,e){if(Un(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var ka=function(t){function e(){var r;Z8(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Q8(this,e,[].concat(i)),ir(r,"state",{isAnimationFinished:!1}),ir(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0})}),ir(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1})}),ir(r,"id",hr("recharts-scatter-")),r}return rK(e,t),J8(e,[{key:"renderSymbolsStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.activeShape,s=a.activeIndex,c=q(this.props,!1);return n.map(function(l,f){var h=s===f,p=h?u:o,d=ae(ae({},c),l);return A.createElement(Q,Ni({className:"recharts-scatter-symbol",key:"symbol-".concat(l==null?void 0:l.cx,"-").concat(l==null?void 0:l.cy,"-").concat(l==null?void 0:l.size,"-").concat(f)},sr(i.props,l,f),{role:"img"}),A.createElement(Y8,Ni({option:p,isActive:h,key:"symbol-".concat(f)},d)))})}},{key:"renderSymbolsWithAnimation",value:function(){var n=this,i=this.props,a=i.points,o=i.isAnimationActive,u=i.animationBegin,s=i.animationDuration,c=i.animationEasing,l=i.animationId,f=this.state.prevPoints;return A.createElement(ee,{begin:u,duration:s,isActive:o,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var p=h.t,d=a.map(function(v,y){var g=f&&f[y];if(g){var b=pt(g.cx,v.cx),_=pt(g.cy,v.cy),w=pt(g.size,v.size);return ae(ae({},v),{},{cx:b(p),cy:_(p),size:w(p)})}var m=pt(0,v.size);return ae(ae({},v),{},{size:m(p)})});return A.createElement(Q,null,n.renderSymbolsStatically(d))})}},{key:"renderSymbols",value:function(){var n=this.props,i=n.points,a=n.isAnimationActive,o=this.state.prevPoints;return a&&i&&i.length&&(!o||!He(o,i))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(i)}},{key:"renderErrorBar",value:function(){var n=this.props.isAnimationActive;if(n&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.points,o=i.xAxis,u=i.yAxis,s=i.children,c=Ht(s,si);return c?c.map(function(l,f){var h=l.props,p=h.direction,d=h.dataKey;return A.cloneElement(l,{key:"".concat(p,"-").concat(d,"-").concat(a[f]),data:a,xAxis:o,yAxis:u,layout:p==="x"?"vertical":"horizontal",dataPointFormatter:function(y,g){return{x:y.cx,y:y.cy,value:p==="x"?+y.node.x:+y.node.y,errorVal:dt(y,g)}}})}):null}},{key:"renderLine",value:function(){var n=this.props,i=n.points,a=n.line,o=n.lineType,u=n.lineJointType,s=q(this.props,!1),c=q(a,!1),l,f;if(o==="joint")l=i.map(function(_){return{x:_.cx,y:_.cy}});else if(o==="fitting"){var h=Q1(i),p=h.xmin,d=h.xmax,v=h.a,y=h.b,g=function(w){return v*w+y};l=[{x:p,y:g(p)},{x:d,y:g(d)}]}var b=ae(ae(ae({},s),{},{fill:"none",stroke:s&&s.fill},c),{},{points:l});return A.isValidElement(a)?f=A.cloneElement(a,b):V(a)?f=a(b):f=A.createElement(ur,Ni({},b,{type:u})),A.createElement(Q,{className:"recharts-scatter-line",key:"recharts-scatter-line"},f)}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.points,o=n.line,u=n.className,s=n.xAxis,c=n.yAxis,l=n.left,f=n.top,h=n.width,p=n.height,d=n.id,v=n.isAnimationActive;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,g=J("recharts-scatter",u),b=s&&s.allowDataOverflow,_=c&&c.allowDataOverflow,w=b||_,m=X(d)?this.id:d;return A.createElement(Q,{className:g,clipPath:w?"url(#clipPath-".concat(m,")"):null},b||_?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(m)},A.createElement("rect",{x:b?l:l-h/2,y:_?f:f-p/2,width:b?h:h*2,height:_?p:p*2}))):null,o&&this.renderLine(),this.renderErrorBar(),A.createElement(Q,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!v||y)&&ce.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}}])}(W.PureComponent);ir(ka,"displayName","Scatter");ir(ka,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!Ee.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"});ir(ka,"getComposedData",function(t){var e=t.xAxis,r=t.yAxis,n=t.zAxis,i=t.item,a=t.displayedData,o=t.xAxisTicks,u=t.yAxisTicks,s=t.offset,c=i.props.tooltipType,l=Ht(i.props.children,Fu),f=X(e.dataKey)?i.props.dataKey:e.dataKey,h=X(r.dataKey)?i.props.dataKey:r.dataKey,p=n&&n.dataKey,d=n?n.range:Ca.defaultProps.range,v=d&&d[0],y=e.scale.bandwidth?e.scale.bandwidth():0,g=r.scale.bandwidth?r.scale.bandwidth():0,b=a.map(function(_,w){var m=dt(_,f),x=dt(_,h),O=!X(p)&&dt(_,p)||"-",S=[{name:X(e.dataKey)?i.props.name:e.name||e.dataKey,unit:e.unit||"",value:m,payload:_,dataKey:f,type:c},{name:X(r.dataKey)?i.props.name:r.name||r.dataKey,unit:r.unit||"",value:x,payload:_,dataKey:h,type:c}];O!=="-"&&S.push({name:n.name||n.dataKey,unit:n.unit||"",value:O,payload:_,dataKey:p,type:c});var $=$n({axis:e,ticks:o,bandSize:y,entry:_,index:w,dataKey:f}),k=$n({axis:r,ticks:u,bandSize:g,entry:_,index:w,dataKey:h}),T=O!=="-"?n.scale(O):v,M=Math.sqrt(Math.max(T,0)/Math.PI);return ae(ae({},_),{},{cx:$,cy:k,x:$-M,y:k-M,xAxis:e,yAxis:r,zAxis:n,width:2*M,height:2*M,size:T,node:{x:m,y:x,z:O},tooltipPayload:S,tooltipPosition:{x:$,y:k},payload:_},l&&l[w]&&l[w].props)});return ae({points:b},s)});function Kn(t){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kn(t)}function iK(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function aK(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,W0(n.key),n)}}function oK(t,e,r){return e&&aK(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function uK(t,e,r){return e=lu(e),sK(t,z0()?Reflect.construct(e,r||[],lu(t).constructor):e.apply(t,r))}function sK(t,e){if(e&&(Kn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cK(t)}function cK(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function z0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(z0=function(){return!!t})()}function lu(t){return lu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},lu(t)}function lK(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$l(t,e)}function $l(t,e){return $l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$l(t,e)}function F0(t,e,r){return e=W0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function W0(t){var e=fK(t,"string");return Kn(e)=="symbol"?e:e+""}function fK(t,e){if(Kn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function El(){return El=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},El.apply(this,arguments)}function hK(t){var e=t.xAxisId,r=Xf(),n=Yf(),i=m0(e);return i==null?null:A.createElement(li,El({},i,{className:J("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return Be(o,!0)}}))}var Jr=function(t){function e(){return iK(this,e),uK(this,e,arguments)}return lK(e,t),oK(e,[{key:"render",value:function(){return A.createElement(hK,this.props)}}])}(A.Component);F0(Jr,"displayName","XAxis");F0(Jr,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function qn(t){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qn(t)}function pK(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function dK(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q0(n.key),n)}}function vK(t,e,r){return e&&dK(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function yK(t,e,r){return e=fu(e),mK(t,U0()?Reflect.construct(e,r||[],fu(t).constructor):e.apply(t,r))}function mK(t,e){if(e&&(qn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return gK(t)}function gK(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function U0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(U0=function(){return!!t})()}function fu(t){return fu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fu(t)}function bK(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Tl(t,e)}function Tl(t,e){return Tl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tl(t,e)}function K0(t,e,r){return e=q0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q0(t){var e=xK(t,"string");return qn(e)=="symbol"?e:e+""}function xK(t,e){if(qn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function jl(){return jl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},jl.apply(this,arguments)}var _K=function(e){var r=e.yAxisId,n=Xf(),i=Yf(),a=g0(r);return a==null?null:A.createElement(li,jl({},a,{className:J("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return Be(u,!0)}}))},Qr=function(t){function e(){return pK(this,e),yK(this,e,arguments)}return bK(e,t),vK(e,[{key:"render",value:function(){return A.createElement(_K,this.props)}}])}(A.Component);K0(Qr,"displayName","YAxis");K0(Qr,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function py(t){return PK(t)||AK(t)||OK(t)||wK()}function wK(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OK(t,e){if(t){if(typeof t=="string")return Ml(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ml(t,e)}}function AK(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function PK(t){if(Array.isArray(t))return Ml(t)}function Ml(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Cl=function(e,r,n,i,a){var o=Ht(e,Jf),u=Ht(e,cs),s=[].concat(py(o),py(u)),c=Ht(e,fs),l="".concat(i,"Id"),f=i[0],h=r;if(s.length&&(h=s.reduce(function(v,y){if(y.props[l]===n&&Ae(y.props,"extendDomain")&&F(y.props[f])){var g=y.props[f];return[Math.min(v[0],g),Math.max(v[1],g)]}return v},h)),c.length){var p="".concat(f,"1"),d="".concat(f,"2");h=c.reduce(function(v,y){if(y.props[l]===n&&Ae(y.props,"extendDomain")&&F(y.props[p])&&F(y.props[d])){var g=y.props[p],b=y.props[d];return[Math.min(v[0],g,b),Math.max(v[1],g,b)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,y){return F(y)?[Math.min(v[0],y),Math.max(v[1],y)]:v},h)),h},H0={exports:{}};(function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(s,c,l){this.fn=s,this.context=c,this.once=l||!1}function a(s,c,l,f,h){if(typeof l!="function")throw new TypeError("The listener must be a function");var p=new i(l,f||s,h),d=r?r+c:c;return s._events[d]?s._events[d].fn?s._events[d]=[s._events[d],p]:s._events[d].push(p):(s._events[d]=p,s._eventsCount++),s}function o(s,c){--s._eventsCount===0?s._events=new n:delete s._events[c]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var c=[],l,f;if(this._eventsCount===0)return c;for(f in l=this._events)e.call(l,f)&&c.push(r?f.slice(1):f);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(l)):c},u.prototype.listeners=function(c){var l=r?r+c:c,f=this._events[l];if(!f)return[];if(f.fn)return[f.fn];for(var h=0,p=f.length,d=new Array(p);h<p;h++)d[h]=f[h].fn;return d},u.prototype.listenerCount=function(c){var l=r?r+c:c,f=this._events[l];return f?f.fn?1:f.length:0},u.prototype.emit=function(c,l,f,h,p,d){var v=r?r+c:c;if(!this._events[v])return!1;var y=this._events[v],g=arguments.length,b,_;if(y.fn){switch(y.once&&this.removeListener(c,y.fn,void 0,!0),g){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,l),!0;case 3:return y.fn.call(y.context,l,f),!0;case 4:return y.fn.call(y.context,l,f,h),!0;case 5:return y.fn.call(y.context,l,f,h,p),!0;case 6:return y.fn.call(y.context,l,f,h,p,d),!0}for(_=1,b=new Array(g-1);_<g;_++)b[_-1]=arguments[_];y.fn.apply(y.context,b)}else{var w=y.length,m;for(_=0;_<w;_++)switch(y[_].once&&this.removeListener(c,y[_].fn,void 0,!0),g){case 1:y[_].fn.call(y[_].context);break;case 2:y[_].fn.call(y[_].context,l);break;case 3:y[_].fn.call(y[_].context,l,f);break;case 4:y[_].fn.call(y[_].context,l,f,h);break;default:if(!b)for(m=1,b=new Array(g-1);m<g;m++)b[m-1]=arguments[m];y[_].fn.apply(y[_].context,b)}}return!0},u.prototype.on=function(c,l,f){return a(this,c,l,f,!1)},u.prototype.once=function(c,l,f){return a(this,c,l,f,!0)},u.prototype.removeListener=function(c,l,f,h){var p=r?r+c:c;if(!this._events[p])return this;if(!l)return o(this,p),this;var d=this._events[p];if(d.fn)d.fn===l&&(!h||d.once)&&(!f||d.context===f)&&o(this,p);else{for(var v=0,y=[],g=d.length;v<g;v++)(d[v].fn!==l||h&&!d[v].once||f&&d[v].context!==f)&&y.push(d[v]);y.length?this._events[p]=y.length===1?y[0]:y:o(this,p)}return this},u.prototype.removeAllListeners=function(c){var l;return c?(l=r?r+c:c,this._events[l]&&o(this,l)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u})(H0);var SK=H0.exports;const $K=ft(SK);var Gs=new $K,Vs="recharts.syncMouseEvents";function ma(t){"@babel/helpers - typeof";return ma=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ma(t)}function EK(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function TK(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,G0(n.key),n)}}function jK(t,e,r){return e&&TK(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Xs(t,e,r){return e=G0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function G0(t){var e=MK(t,"string");return ma(e)=="symbol"?e:e+""}function MK(t,e){if(ma(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ma(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var CK=function(){function t(){EK(this,t),Xs(this,"activeIndex",0),Xs(this,"coordinateList",[]),Xs(this,"layout","horizontal")}return jK(t,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,s=r.layout,c=s===void 0?null:s,l=r.offset,f=l===void 0?null:l,h=r.mouseHandlerCallback,p=h===void 0?null:h;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=c??this.layout,this.offset=f??this.offset,this.mouseHandlerCallback=p??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,s=this.coordinateList[this.activeIndex].coordinate,c=((r=window)===null||r===void 0?void 0:r.scrollX)||0,l=((n=window)===null||n===void 0?void 0:n.scrollY)||0,f=a+s+c,h=o+this.offset.top+u/2+l;this.mouseHandlerCallback({pageX:f,pageY:h})}}}])}();function kK(t,e,r){if(r==="number"&&e===!0&&Array.isArray(t)){var n=t==null?void 0:t[0],i=t==null?void 0:t[1];if(n&&i&&F(n)&&F(i))return!0}return!1}function IK(t,e,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:t==="horizontal"?e.x-i:r.left+.5,y:t==="horizontal"?r.top+.5:e.y-i,width:t==="horizontal"?n:r.width-1,height:t==="horizontal"?r.height-1:n}}function V0(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,a=t.endAngle,o=ot(e,r,n,i),u=ot(e,r,n,a);return{points:[o,u],cx:e,cy:r,radius:n,startAngle:i,endAngle:a}}function NK(t,e,r){var n,i,a,o;if(t==="horizontal")n=e.x,a=n,i=r.top,o=r.top+r.height;else if(t==="vertical")i=e.y,o=i,n=r.left,a=r.left+r.width;else if(e.cx!=null&&e.cy!=null)if(t==="centric"){var u=e.cx,s=e.cy,c=e.innerRadius,l=e.outerRadius,f=e.angle,h=ot(u,s,c,f),p=ot(u,s,l,f);n=h.x,i=h.y,a=p.x,o=p.y}else return V0(e);return[{x:n,y:i},{x:a,y:o}]}function ga(t){"@babel/helpers - typeof";return ga=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ga(t)}function dy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Za(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?dy(Object(r),!0).forEach(function(n){DK(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dy(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function DK(t,e,r){return e=RK(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function RK(t){var e=LK(t,"string");return ga(e)=="symbol"?e:e+""}function LK(t,e){if(ga(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(ga(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function BK(t){var e,r,n=t.element,i=t.tooltipEventType,a=t.isActive,o=t.activeCoordinate,u=t.activePayload,s=t.offset,c=t.activeTooltipIndex,l=t.tooltipAxisBandSize,f=t.layout,h=t.chartName,p=(e=n.props.cursor)!==null&&e!==void 0?e:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!p||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var d,v=ur;if(h==="ScatterChart")d=o,v=Q3;else if(h==="BarChart")d=IK(f,o,s,l),v=qf;else if(f==="radial"){var y=V0(o),g=y.cx,b=y.cy,_=y.radius,w=y.startAngle,m=y.endAngle;d={cx:g,cy:b,startAngle:w,endAngle:m,innerRadius:_,outerRadius:_},v=Rb}else d={points:NK(f,o,s)},v=ur;var x=Za(Za(Za(Za({stroke:"#ccc",pointerEvents:"none"},s),d),q(p,!1)),{},{payload:u,payloadIndex:c,className:J("recharts-tooltip-cursor",p.className)});return W.isValidElement(p)?W.cloneElement(p,x):W.createElement(v,x)}var zK=["item"],FK=["children","className","width","height","style","compact","title","desc"];function Hn(t){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Hn(t)}function fn(){return fn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fn.apply(this,arguments)}function vy(t,e){return KK(t)||UK(t,e)||Y0(t,e)||WK()}function WK(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function UK(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,e!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(l){c=!0,i=l}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function KK(t){if(Array.isArray(t))return t}function yy(t,e){if(t==null)return{};var r=qK(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function qK(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function HK(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function GK(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Z0(n.key),n)}}function VK(t,e,r){return e&&GK(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function XK(t,e,r){return e=hu(e),YK(t,X0()?Reflect.construct(e,r||[],hu(t).constructor):e.apply(t,r))}function YK(t,e){if(e&&(Hn(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ZK(t)}function ZK(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function X0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(X0=function(){return!!t})()}function hu(t){return hu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},hu(t)}function JK(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&kl(t,e)}function kl(t,e){return kl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},kl(t,e)}function Gn(t){return eq(t)||tq(t)||Y0(t)||QK()}function QK(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Y0(t,e){if(t){if(typeof t=="string")return Il(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Il(t,e)}}function tq(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function eq(t){if(Array.isArray(t))return Il(t)}function Il(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function my(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?my(Object(r),!0).forEach(function(n){Y(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):my(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Y(t,e,r){return e=Z0(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Z0(t){var e=rq(t,"string");return Hn(e)=="symbol"?e:e+""}function rq(t,e){if(Hn(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var nq={xAxis:["bottom","top"],yAxis:["left","right"]},iq={width:"100%",height:"100%"},J0={x:0,y:0};function Ja(t){return t}var aq=function(e,r){return r==="horizontal"?e.x:r==="vertical"?e.y:r==="centric"?e.angle:e.radius},oq=function(e,r,n,i){var a=r.find(function(l){return l&&l.index===n});if(a){if(e==="horizontal")return{x:a.coordinate,y:i.y};if(e==="vertical")return{x:i.x,y:a.coordinate};if(e==="centric"){var o=a.coordinate,u=i.radius;return D(D(D({},i),ot(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var s=a.coordinate,c=i.angle;return D(D(D({},i),ot(i.cx,i.cy,s,c)),{},{angle:c,radius:s})}return J0},hs=function(e,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,s){var c=s.props.data;return c&&c.length?[].concat(Gn(u),Gn(c)):u},[]);return o.length>0?o:e&&e.length&&F(i)&&F(a)?e.slice(i,a+1):[]};function Q0(t){return t==="number"?[0,"auto"]:void 0}var Nl=function(e,r,n,i){var a=e.graphicalItems,o=e.tooltipAxis,u=hs(r,e);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(s,c){var l,f=(l=c.props.data)!==null&&l!==void 0?l:r;f&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=n&&(f=f.slice(e.dataStartIndex,e.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var p=f===void 0?u:f;h=uo(p,o.dataKey,i)}else h=f&&f[n]||u[n];return h?[].concat(Gn(s),[Tb(c,h)]):s},[])},gy=function(e,r,n,i){var a=i||{x:e.chartX,y:e.chartY},o=aq(a,n),u=e.orderedTooltipTicks,s=e.tooltipAxis,c=e.tooltipTicks,l=MR(o,u,c,s);if(l>=0&&c){var f=c[l]&&c[l].value,h=Nl(e,r,l,f),p=oq(n,u,l,a);return{activeTooltipIndex:l,activeLabel:f,activePayload:h,activeCoordinate:p}}return null},uq=function(e,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,l=e.layout,f=e.children,h=e.stackOffset,p=Ob(l,a);return n.reduce(function(d,v){var y,g=v.type.defaultProps!==void 0?D(D({},v.type.defaultProps),v.props):v.props,b=g.type,_=g.dataKey,w=g.allowDataOverflow,m=g.allowDuplicatedCategory,x=g.scale,O=g.ticks,S=g.includeHidden,$=g[o];if(d[$])return d;var k=hs(e.data,{graphicalItems:i.filter(function(z){var G,Z=o in z.props?z.props[o]:(G=z.type.defaultProps)===null||G===void 0?void 0:G[o];return Z===$}),dataStartIndex:s,dataEndIndex:c}),T=k.length,M,C,P;kK(g.domain,w,b)&&(M=qc(g.domain,null,w),p&&(b==="number"||x!=="auto")&&(P=Ti(k,_,"category")));var j=Q0(b);if(!M||M.length===0){var E,I=(E=g.domain)!==null&&E!==void 0?E:j;if(_){if(M=Ti(k,_,b),b==="category"&&p){var N=J1(M);m&&N?(C=M,M=Yo(0,T)):m||(M=Nd(I,M,v).reduce(function(z,G){return z.indexOf(G)>=0?z:[].concat(Gn(z),[G])},[]))}else if(b==="category")m?M=M.filter(function(z){return z!==""&&!X(z)}):M=Nd(I,M,v).reduce(function(z,G){return z.indexOf(G)>=0||G===""||X(G)?z:[].concat(Gn(z),[G])},[]);else if(b==="number"){var R=DR(k,i.filter(function(z){var G,Z,rt=o in z.props?z.props[o]:(G=z.type.defaultProps)===null||G===void 0?void 0:G[o],ht="hide"in z.props?z.props.hide:(Z=z.type.defaultProps)===null||Z===void 0?void 0:Z.hide;return rt===$&&(S||!ht)}),_,a,l);R&&(M=R)}p&&(b==="number"||x!=="auto")&&(P=Ti(k,_,"category"))}else p?M=Yo(0,T):u&&u[$]&&u[$].hasStack&&b==="number"?M=h==="expand"?[0,1]:Eb(u[$].stackGroups,s,c):M=wb(k,i.filter(function(z){var G=o in z.props?z.props[o]:z.type.defaultProps[o],Z="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return G===$&&(S||!Z)}),b,l,!0);if(b==="number")M=Cl(f,M,$,a,O),I&&(M=qc(I,M,w));else if(b==="category"&&I){var B=I,U=M.every(function(z){return B.indexOf(z)>=0});U&&(M=B)}}return D(D({},d),{},Y({},$,D(D({},g),{},{axisType:a,domain:M,categoricalDomain:P,duplicateDomain:C,originalDomain:(y=g.domain)!==null&&y!==void 0?y:j,isCategorical:p,layout:l})))},{})},sq=function(e,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,l=e.layout,f=e.children,h=hs(e.data,{graphicalItems:n,dataStartIndex:s,dataEndIndex:c}),p=h.length,d=Ob(l,a),v=-1;return n.reduce(function(y,g){var b=g.type.defaultProps!==void 0?D(D({},g.type.defaultProps),g.props):g.props,_=b[o],w=Q0("number");if(!y[_]){v++;var m;return d?m=Yo(0,p):u&&u[_]&&u[_].hasStack?(m=Eb(u[_].stackGroups,s,c),m=Cl(f,m,_,a)):(m=qc(w,wb(h,n.filter(function(x){var O,S,$=o in x.props?x.props[o]:(O=x.type.defaultProps)===null||O===void 0?void 0:O[o],k="hide"in x.props?x.props.hide:(S=x.type.defaultProps)===null||S===void 0?void 0:S.hide;return $===_&&!k}),"number",l),i.defaultProps.allowDataOverflow),m=Cl(f,m,_,a)),D(D({},y),{},Y({},_,D(D({axisType:a},i.defaultProps),{},{hide:!0,orientation:te(nq,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:w,isCategorical:d,layout:l})))}return y},{})},cq=function(e,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,l=e.children,f="".concat(i,"Id"),h=Ht(l,a),p={};return h&&h.length?p=uq(e,{axes:h,graphicalItems:o,axisType:i,axisIdKey:f,stackGroups:u,dataStartIndex:s,dataEndIndex:c}):o&&o.length&&(p=sq(e,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:f,stackGroups:u,dataStartIndex:s,dataEndIndex:c})),p},lq=function(e){var r=er(e),n=Be(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:yf(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:No(r,n)}},by=function(e){var r=e.children,n=e.defaultShowTooltip,i=Jt(r,In),a=0,o=0;return e.data&&e.data.length!==0&&(o=e.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},fq=function(e){return!e||!e.length?!1:e.some(function(r){var n=Fe(r&&r.type);return n&&n.indexOf("Bar")>=0})},xy=function(e){return e==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:e==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:e==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hq=function(e,r){var n=e.props,i=e.graphicalItems,a=e.xAxisMap,o=a===void 0?{}:a,u=e.yAxisMap,s=u===void 0?{}:u,c=n.width,l=n.height,f=n.children,h=n.margin||{},p=Jt(f,In),d=Jt(f,pn),v=Object.keys(s).reduce(function(m,x){var O=s[x],S=O.orientation;return!O.mirror&&!O.hide?D(D({},m),{},Y({},S,m[S]+O.width)):m},{left:h.left||0,right:h.right||0}),y=Object.keys(o).reduce(function(m,x){var O=o[x],S=O.orientation;return!O.mirror&&!O.hide?D(D({},m),{},Y({},S,te(m,"".concat(S))+O.height)):m},{top:h.top||0,bottom:h.bottom||0}),g=D(D({},y),v),b=g.bottom;p&&(g.bottom+=p.props.height||In.defaultProps.height),d&&r&&(g=IR(g,i,n,r));var _=c-g.left-g.right,w=l-g.top-g.bottom;return D(D({brushBottom:b},g),{},{width:Math.max(_,0),height:Math.max(w,0)})},pq=function(e,r){if(r==="xAxis")return e[r].width;if(r==="yAxis")return e[r].height},tn=function(e){var r=e.chartName,n=e.GraphicalChild,i=e.defaultTooltipEventType,a=i===void 0?"axis":i,o=e.validateTooltipEventTypes,u=o===void 0?["axis"]:o,s=e.axisComponents,c=e.legendContent,l=e.formatAxisMap,f=e.defaultProps,h=function(g,b){var _=b.graphicalItems,w=b.stackGroups,m=b.offset,x=b.updateId,O=b.dataStartIndex,S=b.dataEndIndex,$=g.barSize,k=g.layout,T=g.barGap,M=g.barCategoryGap,C=g.maxBarSize,P=xy(k),j=P.numericAxisName,E=P.cateAxisName,I=fq(_),N=[];return _.forEach(function(R,B){var U=hs(g.data,{graphicalItems:[R],dataStartIndex:O,dataEndIndex:S}),z=R.type.defaultProps!==void 0?D(D({},R.type.defaultProps),R.props):R.props,G=z.dataKey,Z=z.maxBarSize,rt=z["".concat(j,"Id")],ht=z["".concat(E,"Id")],xt={},ct=s.reduce(function(br,xr){var ys=b["".concat(xr.axisType,"Map")],sh=z["".concat(xr.axisType,"Id")];ys&&ys[sh]||xr.axisType==="zAxis"||Ur();var ch=ys[sh];return D(D({},br),{},Y(Y({},xr.axisType,ch),"".concat(xr.axisType,"Ticks"),Be(ch)))},xt),K=ct[E],tt=ct["".concat(E,"Ticks")],nt=w&&w[rt]&&w[rt].hasStack&&qR(R,w[rt].stackGroups),L=Fe(R.type).indexOf("Bar")>=0,_t=No(K,tt),it=[],Pt=I&&CR({barSize:$,stackGroups:w,totalSize:pq(ct,E)});if(L){var St,Gt,Je=X(Z)?C:Z,en=(St=(Gt=No(K,tt,!0))!==null&&Gt!==void 0?Gt:Je)!==null&&St!==void 0?St:0;it=kR({barGap:T,barCategoryGap:M,bandSize:en!==_t?en:_t,sizeList:Pt[ht],maxBarSize:Je}),en!==_t&&(it=it.map(function(br){return D(D({},br),{},{position:D(D({},br.position),{},{offset:br.position.offset-en/2})})}))}var Na=R&&R.type&&R.type.getComposedData;Na&&N.push({props:D(D({},Na(D(D({},ct),{},{displayedData:U,props:g,dataKey:G,item:R,bandSize:_t,barPosition:it,offset:m,stackedData:nt,layout:k,dataStartIndex:O,dataEndIndex:S}))),{},Y(Y(Y({key:R.key||"item-".concat(B)},j,ct[j]),E,ct[E]),"animationId",x)),childIndex:fO(R,g.children),item:R})}),N},p=function(g,b){var _=g.props,w=g.dataStartIndex,m=g.dataEndIndex,x=g.updateId;if(!Oh({props:_}))return null;var O=_.children,S=_.layout,$=_.stackOffset,k=_.data,T=_.reverseStackOrder,M=xy(S),C=M.numericAxisName,P=M.cateAxisName,j=Ht(O,n),E=UR(k,j,"".concat(C,"Id"),"".concat(P,"Id"),$,T),I=s.reduce(function(z,G){var Z="".concat(G.axisType,"Map");return D(D({},z),{},Y({},Z,cq(_,D(D({},G),{},{graphicalItems:j,stackGroups:G.axisType===C&&E,dataStartIndex:w,dataEndIndex:m}))))},{}),N=hq(D(D({},I),{},{props:_,graphicalItems:j}),b==null?void 0:b.legendBBox);Object.keys(I).forEach(function(z){I[z]=l(_,I[z],N,z.replace("Map",""),r)});var R=I["".concat(P,"Map")],B=lq(R),U=h(_,D(D({},I),{},{dataStartIndex:w,dataEndIndex:m,updateId:x,graphicalItems:j,stackGroups:E,offset:N}));return D(D({formattedGraphicalItems:U,graphicalItems:j,offset:N,stackGroups:E},B),I)},d=function(y){function g(b){var _,w,m;return HK(this,g),m=XK(this,g,[b]),Y(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),Y(m,"accessibilityManager",new CK),Y(m,"handleLegendBBoxUpdate",function(x){if(x){var O=m.state,S=O.dataStartIndex,$=O.dataEndIndex,k=O.updateId;m.setState(D({legendBBox:x},p({props:m.props,dataStartIndex:S,dataEndIndex:$,updateId:k},D(D({},m.state),{},{legendBBox:x}))))}}),Y(m,"handleReceiveSyncEvent",function(x,O,S){if(m.props.syncId===x){if(S===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(O)}}),Y(m,"handleBrushChange",function(x){var O=x.startIndex,S=x.endIndex;if(O!==m.state.dataStartIndex||S!==m.state.dataEndIndex){var $=m.state.updateId;m.setState(function(){return D({dataStartIndex:O,dataEndIndex:S},p({props:m.props,dataStartIndex:O,dataEndIndex:S,updateId:$},m.state))}),m.triggerSyncEvent({dataStartIndex:O,dataEndIndex:S})}}),Y(m,"handleMouseEnter",function(x){var O=m.getMouseInfo(x);if(O){var S=D(D({},O),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var $=m.props.onMouseEnter;V($)&&$(S,x)}}),Y(m,"triggeredAfterMouseMove",function(x){var O=m.getMouseInfo(x),S=O?D(D({},O),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var $=m.props.onMouseMove;V($)&&$(S,x)}),Y(m,"handleItemMouseEnter",function(x){m.setState(function(){return{isTooltipActive:!0,activeItem:x,activePayload:x.tooltipPayload,activeCoordinate:x.tooltipPosition||{x:x.cx,y:x.cy}}})}),Y(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),Y(m,"handleMouseMove",function(x){x.persist(),m.throttleTriggeredAfterMouseMove(x)}),Y(m,"handleMouseLeave",function(x){m.throttleTriggeredAfterMouseMove.cancel();var O={isTooltipActive:!1};m.setState(O),m.triggerSyncEvent(O);var S=m.props.onMouseLeave;V(S)&&S(O,x)}),Y(m,"handleOuterEvent",function(x){var O=lO(x),S=te(m.props,"".concat(O));if(O&&V(S)){var $,k;/.*touch.*/i.test(O)?k=m.getMouseInfo(x.changedTouches[0]):k=m.getMouseInfo(x),S(($=k)!==null&&$!==void 0?$:{},x)}}),Y(m,"handleClick",function(x){var O=m.getMouseInfo(x);if(O){var S=D(D({},O),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var $=m.props.onClick;V($)&&$(S,x)}}),Y(m,"handleMouseDown",function(x){var O=m.props.onMouseDown;if(V(O)){var S=m.getMouseInfo(x);O(S,x)}}),Y(m,"handleMouseUp",function(x){var O=m.props.onMouseUp;if(V(O)){var S=m.getMouseInfo(x);O(S,x)}}),Y(m,"handleTouchMove",function(x){x.changedTouches!=null&&x.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(x.changedTouches[0])}),Y(m,"handleTouchStart",function(x){x.changedTouches!=null&&x.changedTouches.length>0&&m.handleMouseDown(x.changedTouches[0])}),Y(m,"handleTouchEnd",function(x){x.changedTouches!=null&&x.changedTouches.length>0&&m.handleMouseUp(x.changedTouches[0])}),Y(m,"handleDoubleClick",function(x){var O=m.props.onDoubleClick;if(V(O)){var S=m.getMouseInfo(x);O(S,x)}}),Y(m,"handleContextMenu",function(x){var O=m.props.onContextMenu;if(V(O)){var S=m.getMouseInfo(x);O(S,x)}}),Y(m,"triggerSyncEvent",function(x){m.props.syncId!==void 0&&Gs.emit(Vs,m.props.syncId,x,m.eventEmitterSymbol)}),Y(m,"applySyncEvent",function(x){var O=m.props,S=O.layout,$=O.syncMethod,k=m.state.updateId,T=x.dataStartIndex,M=x.dataEndIndex;if(x.dataStartIndex!==void 0||x.dataEndIndex!==void 0)m.setState(D({dataStartIndex:T,dataEndIndex:M},p({props:m.props,dataStartIndex:T,dataEndIndex:M,updateId:k},m.state)));else if(x.activeTooltipIndex!==void 0){var C=x.chartX,P=x.chartY,j=x.activeTooltipIndex,E=m.state,I=E.offset,N=E.tooltipTicks;if(!I)return;if(typeof $=="function")j=$(N,x);else if($==="value"){j=-1;for(var R=0;R<N.length;R++)if(N[R].value===x.activeLabel){j=R;break}}var B=D(D({},I),{},{x:I.left,y:I.top}),U=Math.min(C,B.x+B.width),z=Math.min(P,B.y+B.height),G=N[j]&&N[j].value,Z=Nl(m.state,m.props.data,j),rt=N[j]?{x:S==="horizontal"?N[j].coordinate:U,y:S==="horizontal"?z:N[j].coordinate}:J0;m.setState(D(D({},x),{},{activeLabel:G,activeCoordinate:rt,activePayload:Z,activeTooltipIndex:j}))}else m.setState(x)}),Y(m,"renderCursor",function(x){var O,S=m.state,$=S.isTooltipActive,k=S.activeCoordinate,T=S.activePayload,M=S.offset,C=S.activeTooltipIndex,P=S.tooltipAxisBandSize,j=m.getTooltipEventType(),E=(O=x.props.active)!==null&&O!==void 0?O:$,I=m.props.layout,N=x.key||"_recharts-cursor";return A.createElement(BK,{key:N,activeCoordinate:k,activePayload:T,activeTooltipIndex:C,chartName:r,element:x,isActive:E,layout:I,offset:M,tooltipAxisBandSize:P,tooltipEventType:j})}),Y(m,"renderPolarAxis",function(x,O,S){var $=te(x,"type.axisType"),k=te(m.state,"".concat($,"Map")),T=x.type.defaultProps,M=T!==void 0?D(D({},T),x.props):x.props,C=k&&k[M["".concat($,"Id")]];return W.cloneElement(x,D(D({},C),{},{className:J($,C.className),key:x.key||"".concat(O,"-").concat(S),ticks:Be(C,!0)}))}),Y(m,"renderPolarGrid",function(x){var O=x.props,S=O.radialLines,$=O.polarAngles,k=O.polarRadius,T=m.state,M=T.radiusAxisMap,C=T.angleAxisMap,P=er(M),j=er(C),E=j.cx,I=j.cy,N=j.innerRadius,R=j.outerRadius;return W.cloneElement(x,{polarAngles:Array.isArray($)?$:Be(j,!0).map(function(B){return B.coordinate}),polarRadius:Array.isArray(k)?k:Be(P,!0).map(function(B){return B.coordinate}),cx:E,cy:I,innerRadius:N,outerRadius:R,key:x.key||"polar-grid",radialLines:S})}),Y(m,"renderLegend",function(){var x=m.state.formattedGraphicalItems,O=m.props,S=O.children,$=O.width,k=O.height,T=m.props.margin||{},M=$-(T.left||0)-(T.right||0),C=xb({children:S,formattedGraphicalItems:x,legendWidth:M,legendContent:c});if(!C)return null;var P=C.item,j=yy(C,zK);return W.cloneElement(P,D(D({},j),{},{chartWidth:$,chartHeight:k,margin:T,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),Y(m,"renderTooltip",function(){var x,O=m.props,S=O.children,$=O.accessibilityLayer,k=Jt(S,ke);if(!k)return null;var T=m.state,M=T.isTooltipActive,C=T.activeCoordinate,P=T.activePayload,j=T.activeLabel,E=T.offset,I=(x=k.props.active)!==null&&x!==void 0?x:M;return W.cloneElement(k,{viewBox:D(D({},E),{},{x:E.left,y:E.top}),active:I,label:j,payload:I?P:[],coordinate:C,accessibilityLayer:$})}),Y(m,"renderBrush",function(x){var O=m.props,S=O.margin,$=O.data,k=m.state,T=k.offset,M=k.dataStartIndex,C=k.dataEndIndex,P=k.updateId;return W.cloneElement(x,{key:x.key||"_recharts-brush",onChange:Ga(m.handleBrushChange,x.props.onChange),data:$,x:F(x.props.x)?x.props.x:T.left,y:F(x.props.y)?x.props.y:T.top+T.height+T.brushBottom-(S.bottom||0),width:F(x.props.width)?x.props.width:T.width,startIndex:M,endIndex:C,updateId:"brush-".concat(P)})}),Y(m,"renderReferenceElement",function(x,O,S){if(!x)return null;var $=m,k=$.clipPathId,T=m.state,M=T.xAxisMap,C=T.yAxisMap,P=T.offset,j=x.type.defaultProps||{},E=x.props,I=E.xAxisId,N=I===void 0?j.xAxisId:I,R=E.yAxisId,B=R===void 0?j.yAxisId:R;return W.cloneElement(x,{key:x.key||"".concat(O,"-").concat(S),xAxis:M[N],yAxis:C[B],viewBox:{x:P.left,y:P.top,width:P.width,height:P.height},clipPathId:k})}),Y(m,"renderActivePoints",function(x){var O=x.item,S=x.activePoint,$=x.basePoint,k=x.childIndex,T=x.isRange,M=[],C=O.props.key,P=O.item.type.defaultProps!==void 0?D(D({},O.item.type.defaultProps),O.item.props):O.item.props,j=P.activeDot,E=P.dataKey,I=D(D({index:k,dataKey:E,cx:S.x,cy:S.y,r:4,fill:Kf(O.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},q(j,!1)),so(j));return M.push(g.renderActiveDot(j,I,"".concat(C,"-activePoint-").concat(k))),$?M.push(g.renderActiveDot(j,D(D({},I),{},{cx:$.x,cy:$.y}),"".concat(C,"-basePoint-").concat(k))):T&&M.push(null),M}),Y(m,"renderGraphicChild",function(x,O,S){var $=m.filterFormatItem(x,O,S);if(!$)return null;var k=m.getTooltipEventType(),T=m.state,M=T.isTooltipActive,C=T.tooltipAxis,P=T.activeTooltipIndex,j=T.activeLabel,E=m.props.children,I=Jt(E,ke),N=$.props,R=N.points,B=N.isRange,U=N.baseLine,z=$.item.type.defaultProps!==void 0?D(D({},$.item.type.defaultProps),$.item.props):$.item.props,G=z.activeDot,Z=z.hide,rt=z.activeBar,ht=z.activeShape,xt=!!(!Z&&M&&I&&(G||rt||ht)),ct={};k!=="axis"&&I&&I.props.trigger==="click"?ct={onClick:Ga(m.handleItemMouseEnter,x.props.onClick)}:k!=="axis"&&(ct={onMouseLeave:Ga(m.handleItemMouseLeave,x.props.onMouseLeave),onMouseEnter:Ga(m.handleItemMouseEnter,x.props.onMouseEnter)});var K=W.cloneElement(x,D(D({},$.props),ct));function tt(xr){return typeof C.dataKey=="function"?C.dataKey(xr.payload):null}if(xt)if(P>=0){var nt,L;if(C.dataKey&&!C.allowDuplicatedCategory){var _t=typeof C.dataKey=="function"?tt:"payload.".concat(C.dataKey.toString());nt=uo(R,_t,j),L=B&&U&&uo(U,_t,j)}else nt=R==null?void 0:R[P],L=B&&U&&U[P];if(ht||rt){var it=x.props.activeIndex!==void 0?x.props.activeIndex:P;return[W.cloneElement(x,D(D(D({},$.props),ct),{},{activeIndex:it})),null,null]}if(!X(nt))return[K].concat(Gn(m.renderActivePoints({item:$,activePoint:nt,basePoint:L,childIndex:P,isRange:B})))}else{var Pt,St=(Pt=m.getItemByXY(m.state.activeCoordinate))!==null&&Pt!==void 0?Pt:{graphicalItem:K},Gt=St.graphicalItem,Je=Gt.item,en=Je===void 0?x:Je,Na=Gt.childIndex,br=D(D(D({},$.props),ct),{},{activeIndex:Na});return[W.cloneElement(en,br),null,null]}return B?[K,null,null]:[K,null]}),Y(m,"renderCustomized",function(x,O,S){return W.cloneElement(x,D(D({key:"recharts-customized-".concat(S)},m.props),m.state))}),Y(m,"renderMap",{CartesianGrid:{handler:Ja,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:Ja},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:Ja},YAxis:{handler:Ja},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((_=b.id)!==null&&_!==void 0?_:hr("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=Gm(m.triggeredAfterMouseMove,(w=b.throttleDelay)!==null&&w!==void 0?w:1e3/60),m.state={},m}return JK(g,y),VK(g,[{key:"componentDidMount",value:function(){var _,w;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(_=this.props.margin.left)!==null&&_!==void 0?_:0,top:(w=this.props.margin.top)!==null&&w!==void 0?w:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var _=this.props,w=_.children,m=_.data,x=_.height,O=_.layout,S=Jt(w,ke);if(S){var $=S.props.defaultIndex;if(!(typeof $!="number"||$<0||$>this.state.tooltipTicks.length-1)){var k=this.state.tooltipTicks[$]&&this.state.tooltipTicks[$].value,T=Nl(this.state,m,$,k),M=this.state.tooltipTicks[$].coordinate,C=(this.state.offset.top+x)/2,P=O==="horizontal",j=P?{x:M,y:C}:{y:M,x:C},E=this.state.formattedGraphicalItems.find(function(N){var R=N.item;return R.type.name==="Scatter"});E&&(j=D(D({},j),E.props.points[$].tooltipPosition),T=E.props.points[$].tooltipPayload);var I={activeTooltipIndex:$,isTooltipActive:!0,activeLabel:k,activePayload:T,activeCoordinate:j};this.setState(I),this.renderCursor(S),this.accessibilityManager.setIndex($)}}}},{key:"getSnapshotBeforeUpdate",value:function(_,w){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==w.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==_.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==_.margin){var m,x;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(x=this.props.margin.top)!==null&&x!==void 0?x:0}})}return null}},{key:"componentDidUpdate",value:function(_){rc([Jt(_.children,ke)],[Jt(this.props.children,ke)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var _=Jt(this.props.children,ke);if(_&&typeof _.props.shared=="boolean"){var w=_.props.shared?"axis":"item";return u.indexOf(w)>=0?w:a}return a}},{key:"getMouseInfo",value:function(_){if(!this.container)return null;var w=this.container,m=w.getBoundingClientRect(),x=VC(m),O={chartX:Math.round(_.pageX-x.left),chartY:Math.round(_.pageY-x.top)},S=m.width/w.offsetWidth||1,$=this.inRange(O.chartX,O.chartY,S);if(!$)return null;var k=this.state,T=k.xAxisMap,M=k.yAxisMap,C=this.getTooltipEventType(),P=gy(this.state,this.props.data,this.props.layout,$);if(C!=="axis"&&T&&M){var j=er(T).scale,E=er(M).scale,I=j&&j.invert?j.invert(O.chartX):null,N=E&&E.invert?E.invert(O.chartY):null;return D(D({},O),{},{xValue:I,yValue:N},P)}return P?D(D({},O),P):null}},{key:"inRange",value:function(_,w){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,x=this.props.layout,O=_/m,S=w/m;if(x==="horizontal"||x==="vertical"){var $=this.state.offset,k=O>=$.left&&O<=$.left+$.width&&S>=$.top&&S<=$.top+$.height;return k?{x:O,y:S}:null}var T=this.state,M=T.angleAxisMap,C=T.radiusAxisMap;if(M&&C){var P=er(M);return Ld({x:O,y:S},P)}return null}},{key:"parseEventsOfWrapper",value:function(){var _=this.props.children,w=this.getTooltipEventType(),m=Jt(_,ke),x={};m&&w==="axis"&&(m.props.trigger==="click"?x={onClick:this.handleClick}:x={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var O=so(this.props,this.handleOuterEvent);return D(D({},O),x)}},{key:"addListener",value:function(){Gs.on(Vs,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Gs.removeListener(Vs,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(_,w,m){for(var x=this.state.formattedGraphicalItems,O=0,S=x.length;O<S;O++){var $=x[O];if($.item===_||$.props.key===_.key||w===Fe($.item.type)&&m===$.childIndex)return $}return null}},{key:"renderClipPath",value:function(){var _=this.clipPathId,w=this.state.offset,m=w.left,x=w.top,O=w.height,S=w.width;return A.createElement("defs",null,A.createElement("clipPath",{id:_},A.createElement("rect",{x:m,y:x,height:O,width:S})))}},{key:"getXScales",value:function(){var _=this.state.xAxisMap;return _?Object.entries(_).reduce(function(w,m){var x=vy(m,2),O=x[0],S=x[1];return D(D({},w),{},Y({},O,S.scale))},{}):null}},{key:"getYScales",value:function(){var _=this.state.yAxisMap;return _?Object.entries(_).reduce(function(w,m){var x=vy(m,2),O=x[0],S=x[1];return D(D({},w),{},Y({},O,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(_){var w;return(w=this.state.xAxisMap)===null||w===void 0||(w=w[_])===null||w===void 0?void 0:w.scale}},{key:"getYScaleByAxisId",value:function(_){var w;return(w=this.state.yAxisMap)===null||w===void 0||(w=w[_])===null||w===void 0?void 0:w.scale}},{key:"getItemByXY",value:function(_){var w=this.state,m=w.formattedGraphicalItems,x=w.activeItem;if(m&&m.length)for(var O=0,S=m.length;O<S;O++){var $=m[O],k=$.props,T=$.item,M=T.type.defaultProps!==void 0?D(D({},T.type.defaultProps),T.props):T.props,C=Fe(T.type);if(C==="Bar"){var P=(k.data||[]).find(function(N){return I3(_,N)});if(P)return{graphicalItem:$,payload:P}}else if(C==="RadialBar"){var j=(k.data||[]).find(function(N){return Ld(_,N)});if(j)return{graphicalItem:$,payload:j}}else if(is($,x)||as($,x)||ha($,x)){var E=IF({graphicalItem:$,activeTooltipItem:x,itemData:M.data}),I=M.activeIndex===void 0?E:M.activeIndex;return{graphicalItem:D(D({},$),{},{childIndex:I}),payload:ha($,x)?M.data[E]:$.props.data[E]}}}return null}},{key:"render",value:function(){var _=this;if(!Oh(this))return null;var w=this.props,m=w.children,x=w.className,O=w.width,S=w.height,$=w.style,k=w.compact,T=w.title,M=w.desc,C=yy(w,FK),P=q(C,!1);if(k)return A.createElement(Gv,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement(ic,fn({},P,{width:O,height:S,title:T,desc:M}),this.renderClipPath(),Ph(m,this.renderMap)));if(this.props.accessibilityLayer){var j,E;P.tabIndex=(j=this.props.tabIndex)!==null&&j!==void 0?j:0,P.role=(E=this.props.role)!==null&&E!==void 0?E:"application",P.onKeyDown=function(N){_.accessibilityManager.keyboardEvent(N)},P.onFocus=function(){_.accessibilityManager.focus()}}var I=this.parseEventsOfWrapper();return A.createElement(Gv,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement("div",fn({className:J("recharts-wrapper",x),style:D({position:"relative",cursor:"default",width:O,height:S},$)},I,{ref:function(R){_.container=R}}),A.createElement(ic,fn({},P,{width:O,height:S,title:T,desc:M,style:iq}),this.renderClipPath(),Ph(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(W.Component);Y(d,"displayName",r),Y(d,"defaultProps",D({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},f)),Y(d,"getDerivedStateFromProps",function(y,g){var b=y.dataKey,_=y.data,w=y.children,m=y.width,x=y.height,O=y.layout,S=y.stackOffset,$=y.margin,k=g.dataStartIndex,T=g.dataEndIndex;if(g.updateId===void 0){var M=by(y);return D(D(D({},M),{},{updateId:0},p(D(D({props:y},M),{},{updateId:0}),g)),{},{prevDataKey:b,prevData:_,prevWidth:m,prevHeight:x,prevLayout:O,prevStackOffset:S,prevMargin:$,prevChildren:w})}if(b!==g.prevDataKey||_!==g.prevData||m!==g.prevWidth||x!==g.prevHeight||O!==g.prevLayout||S!==g.prevStackOffset||!hn($,g.prevMargin)){var C=by(y),P={chartX:g.chartX,chartY:g.chartY,isTooltipActive:g.isTooltipActive},j=D(D({},gy(g,_,O)),{},{updateId:g.updateId+1}),E=D(D(D({},C),P),j);return D(D(D({},E),p(D({props:y},E),g)),{},{prevDataKey:b,prevData:_,prevWidth:m,prevHeight:x,prevLayout:O,prevStackOffset:S,prevMargin:$,prevChildren:w})}if(!rc(w,g.prevChildren)){var I,N,R,B,U=Jt(w,In),z=U&&(I=(N=U.props)===null||N===void 0?void 0:N.startIndex)!==null&&I!==void 0?I:k,G=U&&(R=(B=U.props)===null||B===void 0?void 0:B.endIndex)!==null&&R!==void 0?R:T,Z=z!==k||G!==T,rt=!X(_),ht=rt&&!Z?g.updateId:g.updateId+1;return D(D({updateId:ht},p(D(D({props:y},g),{},{updateId:ht,dataStartIndex:z,dataEndIndex:G}),g)),{},{prevChildren:w,dataStartIndex:z,dataEndIndex:G})}return null}),Y(d,"renderActiveDot",function(y,g,b){var _;return W.isValidElement(y)?_=W.cloneElement(y,g):V(y)?_=y(g):_=A.createElement(ci,g),A.createElement(Q,{className:"recharts-active-dot",key:b},_)});var v=W.forwardRef(function(g,b){return A.createElement(d,fn({},g,{ref:b}))});return v.displayName=d.displayName,v},lG=tn({chartName:"LineChart",GraphicalChild:Ma,axisComponents:[{axisType:"xAxis",AxisComp:Jr},{axisType:"yAxis",AxisComp:Qr}],formatAxisMap:ja}),fG=tn({chartName:"BarChart",GraphicalChild:Zr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Jr},{axisType:"yAxis",AxisComp:Qr}],formatAxisMap:ja}),hG=tn({chartName:"PieChart",GraphicalChild:mr,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:Ta},{axisType:"radiusAxis",AxisComp:Ea}],formatAxisMap:Cb,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),pG=tn({chartName:"RadarChart",GraphicalChild:os,axisComponents:[{axisType:"angleAxis",AxisComp:Ta},{axisType:"radiusAxis",AxisComp:Ea}],formatAxisMap:Cb,defaultProps:{layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),dG=tn({chartName:"ScatterChart",GraphicalChild:ka,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:Jr},{axisType:"yAxis",AxisComp:Qr},{axisType:"zAxis",AxisComp:Ca}],formatAxisMap:ja}),vG=tn({chartName:"AreaChart",GraphicalChild:gr,axisComponents:[{axisType:"xAxis",AxisComp:Jr},{axisType:"yAxis",AxisComp:Qr}],formatAxisMap:ja}),yG=tn({chartName:"ComposedChart",GraphicalChild:[Ma,gr,Zr,ka],axisComponents:[{axisType:"xAxis",AxisComp:Jr},{axisType:"yAxis",AxisComp:Qr},{axisType:"zAxis",AxisComp:Ca}],formatAxisMap:ja});function dq(t){return t}var eo=1,Ys=2,Dl=3,Ai=4,_y=1e-6;function vq(t){return"translate("+t+",0)"}function yq(t){return"translate(0,"+t+")"}function mq(t){return e=>+t(e)}function gq(t,e){return e=Math.max(0,t.bandwidth()-e*2)/2,t.round()&&(e=Math.round(e)),r=>+t(r)+e}function bq(){return!this.__axis}function eh(t,e){var r=[],n=null,i=null,a=6,o=6,u=3,s=typeof window<"u"&&window.devicePixelRatio>1?0:.5,c=t===eo||t===Ai?-1:1,l=t===Ai||t===Ys?"x":"y",f=t===eo||t===Dl?vq:yq;function h(p){var d=n??(e.ticks?e.ticks.apply(e,r):e.domain()),v=i??(e.tickFormat?e.tickFormat.apply(e,r):dq),y=Math.max(a,0)+u,g=e.range(),b=+g[0]+s,_=+g[g.length-1]+s,w=(e.bandwidth?gq:mq)(e.copy(),s),m=p.selection?p.selection():p,x=m.selectAll(".domain").data([null]),O=m.selectAll(".tick").data(d,e).order(),S=O.exit(),$=O.enter().append("g").attr("class","tick"),k=O.select("line"),T=O.select("text");x=x.merge(x.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),O=O.merge($),k=k.merge($.append("line").attr("stroke","currentColor").attr(l+"2",c*a)),T=T.merge($.append("text").attr("fill","currentColor").attr(l,c*y).attr("dy",t===eo?"0em":t===Dl?"0.71em":"0.32em")),p!==m&&(x=x.transition(p),O=O.transition(p),k=k.transition(p),T=T.transition(p),S=S.transition(p).attr("opacity",_y).attr("transform",function(M){return isFinite(M=w(M))?f(M+s):this.getAttribute("transform")}),$.attr("opacity",_y).attr("transform",function(M){var C=this.parentNode.__axis;return f((C&&isFinite(C=C(M))?C:w(M))+s)})),S.remove(),x.attr("d",t===Ai||t===Ys?o?"M"+c*o+","+b+"H"+s+"V"+_+"H"+c*o:"M"+s+","+b+"V"+_:o?"M"+b+","+c*o+"V"+s+"H"+_+"V"+c*o:"M"+b+","+s+"H"+_),O.attr("opacity",1).attr("transform",function(M){return f(w(M)+s)}),k.attr(l+"2",c*a),T.attr(l,c*y).text(v),m.filter(bq).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===Ys?"start":t===Ai?"end":"middle"),m.each(function(){this.__axis=w})}return h.scale=function(p){return arguments.length?(e=p,h):e},h.ticks=function(){return r=Array.from(arguments),h},h.tickArguments=function(p){return arguments.length?(r=p==null?[]:Array.from(p),h):r.slice()},h.tickValues=function(p){return arguments.length?(n=p==null?null:Array.from(p),h):n&&n.slice()},h.tickFormat=function(p){return arguments.length?(i=p,h):i},h.tickSize=function(p){return arguments.length?(a=o=+p,h):a},h.tickSizeInner=function(p){return arguments.length?(a=+p,h):a},h.tickSizeOuter=function(p){return arguments.length?(o=+p,h):o},h.tickPadding=function(p){return arguments.length?(u=+p,h):u},h.offset=function(p){return arguments.length?(s=+p,h):s},h}function mG(t){return eh(eo,t)}function gG(t){return eh(Dl,t)}function bG(t){return eh(Ai,t)}var xq={value:()=>{}};function rh(){for(var t=0,e=arguments.length,r={},n;t<e;++t){if(!(n=arguments[t]+"")||n in r||/[\s.]/.test(n))throw new Error("illegal type: "+n);r[n]=[]}return new ro(r)}function ro(t){this._=t}function _q(t,e){return t.trim().split(/^|\s+/).map(function(r){var n="",i=r.indexOf(".");if(i>=0&&(n=r.slice(i+1),r=r.slice(0,i)),r&&!e.hasOwnProperty(r))throw new Error("unknown type: "+r);return{type:r,name:n}})}ro.prototype=rh.prototype={constructor:ro,on:function(t,e){var r=this._,n=_q(t+"",r),i,a=-1,o=n.length;if(arguments.length<2){for(;++a<o;)if((i=(t=n[a]).type)&&(i=wq(r[i],t.name)))return i;return}if(e!=null&&typeof e!="function")throw new Error("invalid callback: "+e);for(;++a<o;)if(i=(t=n[a]).type)r[i]=wy(r[i],t.name,e);else if(e==null)for(i in r)r[i]=wy(r[i],t.name,null);return this},copy:function(){var t={},e=this._;for(var r in e)t[r]=e[r].slice();return new ro(t)},call:function(t,e){if((i=arguments.length-2)>0)for(var r=new Array(i),n=0,i,a;n<i;++n)r[n]=arguments[n+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=this._[t],n=0,i=a.length;n<i;++n)a[n].value.apply(e,r)},apply:function(t,e,r){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var n=this._[t],i=0,a=n.length;i<a;++i)n[i].value.apply(e,r)}};function wq(t,e){for(var r=0,n=t.length,i;r<n;++r)if((i=t[r]).name===e)return i.value}function wy(t,e,r){for(var n=0,i=t.length;n<i;++n)if(t[n].name===e){t[n]=xq,t=t.slice(0,n).concat(t.slice(n+1));break}return r!=null&&t.push({name:e,value:r}),t}var Rl="http://www.w3.org/1999/xhtml";const Oy={svg:"http://www.w3.org/2000/svg",xhtml:Rl,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function ps(t){var e=t+="",r=e.indexOf(":");return r>=0&&(e=t.slice(0,r))!=="xmlns"&&(t=t.slice(r+1)),Oy.hasOwnProperty(e)?{space:Oy[e],local:t}:t}function Oq(t){return function(){var e=this.ownerDocument,r=this.namespaceURI;return r===Rl&&e.documentElement.namespaceURI===Rl?e.createElement(t):e.createElementNS(r,t)}}function Aq(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function tx(t){var e=ps(t);return(e.local?Aq:Oq)(e)}function Pq(){}function nh(t){return t==null?Pq:function(){return this.querySelector(t)}}function Sq(t){typeof t!="function"&&(t=nh(t));for(var e=this._groups,r=e.length,n=new Array(r),i=0;i<r;++i)for(var a=e[i],o=a.length,u=n[i]=new Array(o),s,c,l=0;l<o;++l)(s=a[l])&&(c=t.call(s,s.__data__,l,a))&&("__data__"in s&&(c.__data__=s.__data__),u[l]=c);return new re(n,this._parents)}function $q(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}function Eq(){return[]}function ex(t){return t==null?Eq:function(){return this.querySelectorAll(t)}}function Tq(t){return function(){return $q(t.apply(this,arguments))}}function jq(t){typeof t=="function"?t=Tq(t):t=ex(t);for(var e=this._groups,r=e.length,n=[],i=[],a=0;a<r;++a)for(var o=e[a],u=o.length,s,c=0;c<u;++c)(s=o[c])&&(n.push(t.call(s,s.__data__,c,o)),i.push(s));return new re(n,i)}function rx(t){return function(){return this.matches(t)}}function nx(t){return function(e){return e.matches(t)}}var Mq=Array.prototype.find;function Cq(t){return function(){return Mq.call(this.children,t)}}function kq(){return this.firstElementChild}function Iq(t){return this.select(t==null?kq:Cq(typeof t=="function"?t:nx(t)))}var Nq=Array.prototype.filter;function Dq(){return Array.from(this.children)}function Rq(t){return function(){return Nq.call(this.children,t)}}function Lq(t){return this.selectAll(t==null?Dq:Rq(typeof t=="function"?t:nx(t)))}function Bq(t){typeof t!="function"&&(t=rx(t));for(var e=this._groups,r=e.length,n=new Array(r),i=0;i<r;++i)for(var a=e[i],o=a.length,u=n[i]=[],s,c=0;c<o;++c)(s=a[c])&&t.call(s,s.__data__,c,a)&&u.push(s);return new re(n,this._parents)}function ix(t){return new Array(t.length)}function zq(){return new re(this._enter||this._groups.map(ix),this._parents)}function pu(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}pu.prototype={constructor:pu,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function Fq(t){return function(){return t}}function Wq(t,e,r,n,i,a){for(var o=0,u,s=e.length,c=a.length;o<c;++o)(u=e[o])?(u.__data__=a[o],n[o]=u):r[o]=new pu(t,a[o]);for(;o<s;++o)(u=e[o])&&(i[o]=u)}function Uq(t,e,r,n,i,a,o){var u,s,c=new Map,l=e.length,f=a.length,h=new Array(l),p;for(u=0;u<l;++u)(s=e[u])&&(h[u]=p=o.call(s,s.__data__,u,e)+"",c.has(p)?i[u]=s:c.set(p,s));for(u=0;u<f;++u)p=o.call(t,a[u],u,a)+"",(s=c.get(p))?(n[u]=s,s.__data__=a[u],c.delete(p)):r[u]=new pu(t,a[u]);for(u=0;u<l;++u)(s=e[u])&&c.get(h[u])===s&&(i[u]=s)}function Kq(t){return t.__data__}function qq(t,e){if(!arguments.length)return Array.from(this,Kq);var r=e?Uq:Wq,n=this._parents,i=this._groups;typeof t!="function"&&(t=Fq(t));for(var a=i.length,o=new Array(a),u=new Array(a),s=new Array(a),c=0;c<a;++c){var l=n[c],f=i[c],h=f.length,p=Hq(t.call(l,l&&l.__data__,c,n)),d=p.length,v=u[c]=new Array(d),y=o[c]=new Array(d),g=s[c]=new Array(h);r(l,f,v,y,g,p,e);for(var b=0,_=0,w,m;b<d;++b)if(w=v[b]){for(b>=_&&(_=b+1);!(m=y[_])&&++_<d;);w._next=m||null}}return o=new re(o,n),o._enter=u,o._exit=s,o}function Hq(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function Gq(){return new re(this._exit||this._groups.map(ix),this._parents)}function Vq(t,e,r){var n=this.enter(),i=this,a=this.exit();return typeof t=="function"?(n=t(n),n&&(n=n.selection())):n=n.append(t+""),e!=null&&(i=e(i),i&&(i=i.selection())),r==null?a.remove():r(a),n&&i?n.merge(i).order():i}function Xq(t){for(var e=t.selection?t.selection():t,r=this._groups,n=e._groups,i=r.length,a=n.length,o=Math.min(i,a),u=new Array(i),s=0;s<o;++s)for(var c=r[s],l=n[s],f=c.length,h=u[s]=new Array(f),p,d=0;d<f;++d)(p=c[d]||l[d])&&(h[d]=p);for(;s<i;++s)u[s]=r[s];return new re(u,this._parents)}function Yq(){for(var t=this._groups,e=-1,r=t.length;++e<r;)for(var n=t[e],i=n.length-1,a=n[i],o;--i>=0;)(o=n[i])&&(a&&o.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(o,a),a=o);return this}function Zq(t){t||(t=Jq);function e(f,h){return f&&h?t(f.__data__,h.__data__):!f-!h}for(var r=this._groups,n=r.length,i=new Array(n),a=0;a<n;++a){for(var o=r[a],u=o.length,s=i[a]=new Array(u),c,l=0;l<u;++l)(c=o[l])&&(s[l]=c);s.sort(e)}return new re(i,this._parents).order()}function Jq(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function Qq(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this}function t6(){return Array.from(this)}function e6(){for(var t=this._groups,e=0,r=t.length;e<r;++e)for(var n=t[e],i=0,a=n.length;i<a;++i){var o=n[i];if(o)return o}return null}function r6(){let t=0;for(const e of this)++t;return t}function n6(){return!this.node()}function i6(t){for(var e=this._groups,r=0,n=e.length;r<n;++r)for(var i=e[r],a=0,o=i.length,u;a<o;++a)(u=i[a])&&t.call(u,u.__data__,a,i);return this}function a6(t){return function(){this.removeAttribute(t)}}function o6(t){return function(){this.removeAttributeNS(t.space,t.local)}}function u6(t,e){return function(){this.setAttribute(t,e)}}function s6(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function c6(t,e){return function(){var r=e.apply(this,arguments);r==null?this.removeAttribute(t):this.setAttribute(t,r)}}function l6(t,e){return function(){var r=e.apply(this,arguments);r==null?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,r)}}function f6(t,e){var r=ps(t);if(arguments.length<2){var n=this.node();return r.local?n.getAttributeNS(r.space,r.local):n.getAttribute(r)}return this.each((e==null?r.local?o6:a6:typeof e=="function"?r.local?l6:c6:r.local?s6:u6)(r,e))}function ax(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function h6(t){return function(){this.style.removeProperty(t)}}function p6(t,e,r){return function(){this.style.setProperty(t,e,r)}}function d6(t,e,r){return function(){var n=e.apply(this,arguments);n==null?this.style.removeProperty(t):this.style.setProperty(t,n,r)}}function v6(t,e,r){return arguments.length>1?this.each((e==null?h6:typeof e=="function"?d6:p6)(t,e,r??"")):Vn(this.node(),t)}function Vn(t,e){return t.style.getPropertyValue(e)||ax(t).getComputedStyle(t,null).getPropertyValue(e)}function y6(t){return function(){delete this[t]}}function m6(t,e){return function(){this[t]=e}}function g6(t,e){return function(){var r=e.apply(this,arguments);r==null?delete this[t]:this[t]=r}}function b6(t,e){return arguments.length>1?this.each((e==null?y6:typeof e=="function"?g6:m6)(t,e)):this.node()[t]}function ox(t){return t.trim().split(/^|\s+/)}function ih(t){return t.classList||new ux(t)}function ux(t){this._node=t,this._names=ox(t.getAttribute("class")||"")}ux.prototype={add:function(t){var e=this._names.indexOf(t);e<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function sx(t,e){for(var r=ih(t),n=-1,i=e.length;++n<i;)r.add(e[n])}function cx(t,e){for(var r=ih(t),n=-1,i=e.length;++n<i;)r.remove(e[n])}function x6(t){return function(){sx(this,t)}}function _6(t){return function(){cx(this,t)}}function w6(t,e){return function(){(e.apply(this,arguments)?sx:cx)(this,t)}}function O6(t,e){var r=ox(t+"");if(arguments.length<2){for(var n=ih(this.node()),i=-1,a=r.length;++i<a;)if(!n.contains(r[i]))return!1;return!0}return this.each((typeof e=="function"?w6:e?x6:_6)(r,e))}function A6(){this.textContent=""}function P6(t){return function(){this.textContent=t}}function S6(t){return function(){var e=t.apply(this,arguments);this.textContent=e??""}}function $6(t){return arguments.length?this.each(t==null?A6:(typeof t=="function"?S6:P6)(t)):this.node().textContent}function E6(){this.innerHTML=""}function T6(t){return function(){this.innerHTML=t}}function j6(t){return function(){var e=t.apply(this,arguments);this.innerHTML=e??""}}function M6(t){return arguments.length?this.each(t==null?E6:(typeof t=="function"?j6:T6)(t)):this.node().innerHTML}function C6(){this.nextSibling&&this.parentNode.appendChild(this)}function k6(){return this.each(C6)}function I6(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function N6(){return this.each(I6)}function D6(t){var e=typeof t=="function"?t:tx(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})}function R6(){return null}function L6(t,e){var r=typeof t=="function"?t:tx(t),n=e==null?R6:typeof e=="function"?e:nh(e);return this.select(function(){return this.insertBefore(r.apply(this,arguments),n.apply(this,arguments)||null)})}function B6(){var t=this.parentNode;t&&t.removeChild(this)}function z6(){return this.each(B6)}function F6(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function W6(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function U6(t){return this.select(t?W6:F6)}function K6(t){return arguments.length?this.property("__data__",t):this.node().__data__}function q6(t){return function(e){t.call(this,e,this.__data__)}}function H6(t){return t.trim().split(/^|\s+/).map(function(e){var r="",n=e.indexOf(".");return n>=0&&(r=e.slice(n+1),e=e.slice(0,n)),{type:e,name:r}})}function G6(t){return function(){var e=this.__on;if(e){for(var r=0,n=-1,i=e.length,a;r<i;++r)a=e[r],(!t.type||a.type===t.type)&&a.name===t.name?this.removeEventListener(a.type,a.listener,a.options):e[++n]=a;++n?e.length=n:delete this.__on}}}function V6(t,e,r){return function(){var n=this.__on,i,a=q6(e);if(n){for(var o=0,u=n.length;o<u;++o)if((i=n[o]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=a,i.options=r),i.value=e;return}}this.addEventListener(t.type,a,r),i={type:t.type,name:t.name,value:e,listener:a,options:r},n?n.push(i):this.__on=[i]}}function X6(t,e,r){var n=H6(t+""),i,a=n.length,o;if(arguments.length<2){var u=this.node().__on;if(u){for(var s=0,c=u.length,l;s<c;++s)for(i=0,l=u[s];i<a;++i)if((o=n[i]).type===l.type&&o.name===l.name)return l.value}return}for(u=e?V6:G6,i=0;i<a;++i)this.each(u(n[i],e,r));return this}function lx(t,e,r){var n=ax(t),i=n.CustomEvent;typeof i=="function"?i=new i(e,r):(i=n.document.createEvent("Event"),r?(i.initEvent(e,r.bubbles,r.cancelable),i.detail=r.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}function Y6(t,e){return function(){return lx(this,t,e)}}function Z6(t,e){return function(){return lx(this,t,e.apply(this,arguments))}}function J6(t,e){return this.each((typeof e=="function"?Z6:Y6)(t,e))}function*Q6(){for(var t=this._groups,e=0,r=t.length;e<r;++e)for(var n=t[e],i=0,a=n.length,o;i<a;++i)(o=n[i])&&(yield o)}var fx=[null];function re(t,e){this._groups=t,this._parents=e}function Ia(){return new re([[document.documentElement]],fx)}function tH(){return this}re.prototype=Ia.prototype={constructor:re,select:Sq,selectAll:jq,selectChild:Iq,selectChildren:Lq,filter:Bq,data:qq,enter:zq,exit:Gq,join:Vq,merge:Xq,selection:tH,order:Yq,sort:Zq,call:Qq,nodes:t6,node:e6,size:r6,empty:n6,each:i6,attr:f6,style:v6,property:b6,classed:O6,text:$6,html:M6,raise:k6,lower:N6,append:D6,insert:L6,remove:z6,clone:U6,datum:K6,on:X6,dispatch:J6,[Symbol.iterator]:Q6};function Tr(t){return typeof t=="string"?new re([[document.querySelector(t)]],[document.documentElement]):new re([[t]],fx)}function eH(t){let e;for(;e=t.sourceEvent;)t=e;return t}function Ar(t,e){if(t=eH(t),e===void 0&&(e=t.currentTarget),e){var r=e.ownerSVGElement||e;if(r.createSVGPoint){var n=r.createSVGPoint();return n.x=t.clientX,n.y=t.clientY,n=n.matrixTransform(e.getScreenCTM().inverse()),[n.x,n.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}const Ll={capture:!0,passive:!1};function Bl(t){t.preventDefault(),t.stopImmediatePropagation()}function rH(t){var e=t.document.documentElement,r=Tr(t).on("dragstart.drag",Bl,Ll);"onselectstart"in e?r.on("selectstart.drag",Bl,Ll):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function nH(t,e){var r=t.document.documentElement,n=Tr(t).on("dragstart.drag",null);e&&(n.on("click.drag",Bl,Ll),setTimeout(function(){n.on("click.drag",null)},0)),"onselectstart"in r?n.on("selectstart.drag",null):(r.style.MozUserSelect=r.__noselect,delete r.__noselect)}var Xn=0,Pi=0,_i=0,hx=1e3,du,Si,vu=0,qr=0,ds=0,ba=typeof performance=="object"&&performance.now?performance:Date,px=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function ah(){return qr||(px(iH),qr=ba.now()+ds)}function iH(){qr=0}function yu(){this._call=this._time=this._next=null}yu.prototype=dx.prototype={constructor:yu,restart:function(t,e,r){if(typeof t!="function")throw new TypeError("callback is not a function");r=(r==null?ah():+r)+(e==null?0:+e),!this._next&&Si!==this&&(Si?Si._next=this:du=this,Si=this),this._call=t,this._time=r,zl()},stop:function(){this._call&&(this._call=null,this._time=1/0,zl())}};function dx(t,e,r){var n=new yu;return n.restart(t,e,r),n}function aH(){ah(),++Xn;for(var t=du,e;t;)(e=qr-t._time)>=0&&t._call.call(void 0,e),t=t._next;--Xn}function Ay(){qr=(vu=ba.now())+ds,Xn=Pi=0;try{aH()}finally{Xn=0,uH(),qr=0}}function oH(){var t=ba.now(),e=t-vu;e>hx&&(ds-=e,vu=t)}function uH(){for(var t,e=du,r,n=1/0;e;)e._call?(n>e._time&&(n=e._time),t=e,e=e._next):(r=e._next,e._next=null,e=t?t._next=r:du=r);Si=t,zl(n)}function zl(t){if(!Xn){Pi&&(Pi=clearTimeout(Pi));var e=t-qr;e>24?(t<1/0&&(Pi=setTimeout(Ay,t-ba.now()-ds)),_i&&(_i=clearInterval(_i))):(_i||(vu=ba.now(),_i=setInterval(oH,hx)),Xn=1,px(Ay))}}function Py(t,e,r){var n=new yu;return e=e==null?0:+e,n.restart(i=>{n.stop(),t(i+e)},e,r),n}var sH=rh("start","end","cancel","interrupt"),cH=[],vx=0,Sy=1,Fl=2,no=3,$y=4,Wl=5,io=6;function vs(t,e,r,n,i,a){var o=t.__transition;if(!o)t.__transition={};else if(r in o)return;lH(t,r,{name:e,index:n,group:i,on:sH,tween:cH,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:vx})}function oh(t,e){var r=be(t,e);if(r.state>vx)throw new Error("too late; already scheduled");return r}function Te(t,e){var r=be(t,e);if(r.state>no)throw new Error("too late; already running");return r}function be(t,e){var r=t.__transition;if(!r||!(r=r[e]))throw new Error("transition not found");return r}function lH(t,e,r){var n=t.__transition,i;n[e]=r,r.timer=dx(a,0,r.time);function a(c){r.state=Sy,r.timer.restart(o,r.delay,r.time),r.delay<=c&&o(c-r.delay)}function o(c){var l,f,h,p;if(r.state!==Sy)return s();for(l in n)if(p=n[l],p.name===r.name){if(p.state===no)return Py(o);p.state===$y?(p.state=io,p.timer.stop(),p.on.call("interrupt",t,t.__data__,p.index,p.group),delete n[l]):+l<e&&(p.state=io,p.timer.stop(),p.on.call("cancel",t,t.__data__,p.index,p.group),delete n[l])}if(Py(function(){r.state===no&&(r.state=$y,r.timer.restart(u,r.delay,r.time),u(c))}),r.state=Fl,r.on.call("start",t,t.__data__,r.index,r.group),r.state===Fl){for(r.state=no,i=new Array(h=r.tween.length),l=0,f=-1;l<h;++l)(p=r.tween[l].value.call(t,t.__data__,r.index,r.group))&&(i[++f]=p);i.length=f+1}}function u(c){for(var l=c<r.duration?r.ease.call(null,c/r.duration):(r.timer.restart(s),r.state=Wl,1),f=-1,h=i.length;++f<h;)i[f].call(t,l);r.state===Wl&&(r.on.call("end",t,t.__data__,r.index,r.group),s())}function s(){r.state=io,r.timer.stop(),delete n[e];for(var c in n)return;delete t.__transition}}function ao(t,e){var r=t.__transition,n,i,a=!0,o;if(r){e=e==null?null:e+"";for(o in r){if((n=r[o]).name!==e){a=!1;continue}i=n.state>Fl&&n.state<Wl,n.state=io,n.timer.stop(),n.on.call(i?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete r[o]}a&&delete t.__transition}}function fH(t){return this.each(function(){ao(this,t)})}function hH(t,e){var r,n;return function(){var i=Te(this,t),a=i.tween;if(a!==r){n=r=a;for(var o=0,u=n.length;o<u;++o)if(n[o].name===e){n=n.slice(),n.splice(o,1);break}}i.tween=n}}function pH(t,e,r){var n,i;if(typeof r!="function")throw new Error;return function(){var a=Te(this,t),o=a.tween;if(o!==n){i=(n=o).slice();for(var u={name:e,value:r},s=0,c=i.length;s<c;++s)if(i[s].name===e){i[s]=u;break}s===c&&i.push(u)}a.tween=i}}function dH(t,e){var r=this._id;if(t+="",arguments.length<2){for(var n=be(this.node(),r).tween,i=0,a=n.length,o;i<a;++i)if((o=n[i]).name===t)return o.value;return null}return this.each((e==null?hH:pH)(r,t,e))}function uh(t,e,r){var n=t._id;return t.each(function(){var i=Te(this,n);(i.value||(i.value={}))[e]=r.apply(this,arguments)}),function(i){return be(i,n).value[e]}}function yx(t,e){var r;return(typeof e=="number"?oe:e instanceof Fr?Hi:(r=Fr(e))?(e=r,Hi):Af)(t,e)}function vH(t){return function(){this.removeAttribute(t)}}function yH(t){return function(){this.removeAttributeNS(t.space,t.local)}}function mH(t,e,r){var n,i=r+"",a;return function(){var o=this.getAttribute(t);return o===i?null:o===n?a:a=e(n=o,r)}}function gH(t,e,r){var n,i=r+"",a;return function(){var o=this.getAttributeNS(t.space,t.local);return o===i?null:o===n?a:a=e(n=o,r)}}function bH(t,e,r){var n,i,a;return function(){var o,u=r(this),s;return u==null?void this.removeAttribute(t):(o=this.getAttribute(t),s=u+"",o===s?null:o===n&&s===i?a:(i=s,a=e(n=o,u)))}}function xH(t,e,r){var n,i,a;return function(){var o,u=r(this),s;return u==null?void this.removeAttributeNS(t.space,t.local):(o=this.getAttributeNS(t.space,t.local),s=u+"",o===s?null:o===n&&s===i?a:(i=s,a=e(n=o,u)))}}function _H(t,e){var r=ps(t),n=r==="transform"?Pg:yx;return this.attrTween(t,typeof e=="function"?(r.local?xH:bH)(r,n,uh(this,"attr."+t,e)):e==null?(r.local?yH:vH)(r):(r.local?gH:mH)(r,n,e))}function wH(t,e){return function(r){this.setAttribute(t,e.call(this,r))}}function OH(t,e){return function(r){this.setAttributeNS(t.space,t.local,e.call(this,r))}}function AH(t,e){var r,n;function i(){var a=e.apply(this,arguments);return a!==n&&(r=(n=a)&&OH(t,a)),r}return i._value=e,i}function PH(t,e){var r,n;function i(){var a=e.apply(this,arguments);return a!==n&&(r=(n=a)&&wH(t,a)),r}return i._value=e,i}function SH(t,e){var r="attr."+t;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(e==null)return this.tween(r,null);if(typeof e!="function")throw new Error;var n=ps(t);return this.tween(r,(n.local?AH:PH)(n,e))}function $H(t,e){return function(){oh(this,t).delay=+e.apply(this,arguments)}}function EH(t,e){return e=+e,function(){oh(this,t).delay=e}}function TH(t){var e=this._id;return arguments.length?this.each((typeof t=="function"?$H:EH)(e,t)):be(this.node(),e).delay}function jH(t,e){return function(){Te(this,t).duration=+e.apply(this,arguments)}}function MH(t,e){return e=+e,function(){Te(this,t).duration=e}}function CH(t){var e=this._id;return arguments.length?this.each((typeof t=="function"?jH:MH)(e,t)):be(this.node(),e).duration}function kH(t,e){if(typeof e!="function")throw new Error;return function(){Te(this,t).ease=e}}function IH(t){var e=this._id;return arguments.length?this.each(kH(e,t)):be(this.node(),e).ease}function NH(t,e){return function(){var r=e.apply(this,arguments);if(typeof r!="function")throw new Error;Te(this,t).ease=r}}function DH(t){if(typeof t!="function")throw new Error;return this.each(NH(this._id,t))}function RH(t){typeof t!="function"&&(t=rx(t));for(var e=this._groups,r=e.length,n=new Array(r),i=0;i<r;++i)for(var a=e[i],o=a.length,u=n[i]=[],s,c=0;c<o;++c)(s=a[c])&&t.call(s,s.__data__,c,a)&&u.push(s);return new Ve(n,this._parents,this._name,this._id)}function LH(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,r=t._groups,n=e.length,i=r.length,a=Math.min(n,i),o=new Array(n),u=0;u<a;++u)for(var s=e[u],c=r[u],l=s.length,f=o[u]=new Array(l),h,p=0;p<l;++p)(h=s[p]||c[p])&&(f[p]=h);for(;u<n;++u)o[u]=e[u];return new Ve(o,this._parents,this._name,this._id)}function BH(t){return(t+"").trim().split(/^|\s+/).every(function(e){var r=e.indexOf(".");return r>=0&&(e=e.slice(0,r)),!e||e==="start"})}function zH(t,e,r){var n,i,a=BH(e)?oh:Te;return function(){var o=a(this,t),u=o.on;u!==n&&(i=(n=u).copy()).on(e,r),o.on=i}}function FH(t,e){var r=this._id;return arguments.length<2?be(this.node(),r).on.on(t):this.each(zH(r,t,e))}function WH(t){return function(){var e=this.parentNode;for(var r in this.__transition)if(+r!==t)return;e&&e.removeChild(this)}}function UH(){return this.on("end.remove",WH(this._id))}function KH(t){var e=this._name,r=this._id;typeof t!="function"&&(t=nh(t));for(var n=this._groups,i=n.length,a=new Array(i),o=0;o<i;++o)for(var u=n[o],s=u.length,c=a[o]=new Array(s),l,f,h=0;h<s;++h)(l=u[h])&&(f=t.call(l,l.__data__,h,u))&&("__data__"in l&&(f.__data__=l.__data__),c[h]=f,vs(c[h],e,r,h,c,be(l,r)));return new Ve(a,this._parents,e,r)}function qH(t){var e=this._name,r=this._id;typeof t!="function"&&(t=ex(t));for(var n=this._groups,i=n.length,a=[],o=[],u=0;u<i;++u)for(var s=n[u],c=s.length,l,f=0;f<c;++f)if(l=s[f]){for(var h=t.call(l,l.__data__,f,s),p,d=be(l,r),v=0,y=h.length;v<y;++v)(p=h[v])&&vs(p,e,r,v,h,d);a.push(h),o.push(l)}return new Ve(a,o,e,r)}var HH=Ia.prototype.constructor;function GH(){return new HH(this._groups,this._parents)}function VH(t,e){var r,n,i;return function(){var a=Vn(this,t),o=(this.style.removeProperty(t),Vn(this,t));return a===o?null:a===r&&o===n?i:i=e(r=a,n=o)}}function mx(t){return function(){this.style.removeProperty(t)}}function XH(t,e,r){var n,i=r+"",a;return function(){var o=Vn(this,t);return o===i?null:o===n?a:a=e(n=o,r)}}function YH(t,e,r){var n,i,a;return function(){var o=Vn(this,t),u=r(this),s=u+"";return u==null&&(s=u=(this.style.removeProperty(t),Vn(this,t))),o===s?null:o===n&&s===i?a:(i=s,a=e(n=o,u))}}function ZH(t,e){var r,n,i,a="style."+e,o="end."+a,u;return function(){var s=Te(this,t),c=s.on,l=s.value[a]==null?u||(u=mx(e)):void 0;(c!==r||i!==l)&&(n=(r=c).copy()).on(o,i=l),s.on=n}}function JH(t,e,r){var n=(t+="")=="transform"?Ag:yx;return e==null?this.styleTween(t,VH(t,n)).on("end.style."+t,mx(t)):typeof e=="function"?this.styleTween(t,YH(t,n,uh(this,"style."+t,e))).each(ZH(this._id,t)):this.styleTween(t,XH(t,n,e),r).on("end.style."+t,null)}function QH(t,e,r){return function(n){this.style.setProperty(t,e.call(this,n),r)}}function t4(t,e,r){var n,i;function a(){var o=e.apply(this,arguments);return o!==i&&(n=(i=o)&&QH(t,o,r)),n}return a._value=e,a}function e4(t,e,r){var n="style."+(t+="");if(arguments.length<2)return(n=this.tween(n))&&n._value;if(e==null)return this.tween(n,null);if(typeof e!="function")throw new Error;return this.tween(n,t4(t,e,r??""))}function r4(t){return function(){this.textContent=t}}function n4(t){return function(){var e=t(this);this.textContent=e??""}}function i4(t){return this.tween("text",typeof t=="function"?n4(uh(this,"text",t)):r4(t==null?"":t+""))}function a4(t){return function(e){this.textContent=t.call(this,e)}}function o4(t){var e,r;function n(){var i=t.apply(this,arguments);return i!==r&&(e=(r=i)&&a4(i)),e}return n._value=t,n}function u4(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(t==null)return this.tween(e,null);if(typeof t!="function")throw new Error;return this.tween(e,o4(t))}function s4(){for(var t=this._name,e=this._id,r=gx(),n=this._groups,i=n.length,a=0;a<i;++a)for(var o=n[a],u=o.length,s,c=0;c<u;++c)if(s=o[c]){var l=be(s,e);vs(s,t,r,c,o,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new Ve(n,this._parents,t,r)}function c4(){var t,e,r=this,n=r._id,i=r.size();return new Promise(function(a,o){var u={value:o},s={value:function(){--i===0&&a()}};r.each(function(){var c=Te(this,n),l=c.on;l!==t&&(e=(t=l).copy(),e._.cancel.push(u),e._.interrupt.push(u),e._.end.push(s)),c.on=e}),i===0&&a()})}var l4=0;function Ve(t,e,r,n){this._groups=t,this._parents=e,this._name=r,this._id=n}function gx(){return++l4}var Me=Ia.prototype;Ve.prototype={constructor:Ve,select:KH,selectAll:qH,selectChild:Me.selectChild,selectChildren:Me.selectChildren,filter:RH,merge:LH,selection:GH,transition:s4,call:Me.call,nodes:Me.nodes,node:Me.node,size:Me.size,empty:Me.empty,each:Me.each,on:FH,attr:_H,attrTween:SH,style:JH,styleTween:e4,text:i4,textTween:u4,remove:UH,tween:dH,delay:TH,duration:CH,ease:IH,easeVarying:DH,end:c4,[Symbol.iterator]:Me[Symbol.iterator]};function f4(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var h4={time:null,delay:0,duration:250,ease:f4};function p4(t,e){for(var r;!(r=t.__transition)||!(r=r[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return r}function d4(t){var e,r;t instanceof Ve?(e=t._id,t=t._name):(e=gx(),(r=h4).time=ah(),t=t==null?null:t+"");for(var n=this._groups,i=n.length,a=0;a<i;++a)for(var o=n[a],u=o.length,s,c=0;c<u;++c)(s=o[c])&&vs(s,t,e,c,o,r||p4(s,e));return new Ve(n,this._parents,t,e)}Ia.prototype.interrupt=fH;Ia.prototype.transition=d4;function v4(t){var e=0,r=t.children,n=r&&r.length;if(!n)e=1;else for(;--n>=0;)e+=r[n].value;t.value=e}function y4(){return this.eachAfter(v4)}function m4(t,e){let r=-1;for(const n of this)t.call(e,n,++r,this);return this}function g4(t,e){for(var r=this,n=[r],i,a,o=-1;r=n.pop();)if(t.call(e,r,++o,this),i=r.children)for(a=i.length-1;a>=0;--a)n.push(i[a]);return this}function b4(t,e){for(var r=this,n=[r],i=[],a,o,u,s=-1;r=n.pop();)if(i.push(r),a=r.children)for(o=0,u=a.length;o<u;++o)n.push(a[o]);for(;r=i.pop();)t.call(e,r,++s,this);return this}function x4(t,e){let r=-1;for(const n of this)if(t.call(e,n,++r,this))return n}function _4(t){return this.eachAfter(function(e){for(var r=+t(e.data)||0,n=e.children,i=n&&n.length;--i>=0;)r+=n[i].value;e.value=r})}function w4(t){return this.eachBefore(function(e){e.children&&e.children.sort(t)})}function O4(t){for(var e=this,r=A4(e,t),n=[e];e!==r;)e=e.parent,n.push(e);for(var i=n.length;t!==r;)n.splice(i,0,t),t=t.parent;return n}function A4(t,e){if(t===e)return t;var r=t.ancestors(),n=e.ancestors(),i=null;for(t=r.pop(),e=n.pop();t===e;)i=t,t=r.pop(),e=n.pop();return i}function P4(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e}function S4(){return Array.from(this)}function $4(){var t=[];return this.eachBefore(function(e){e.children||t.push(e)}),t}function E4(){var t=this,e=[];return t.each(function(r){r!==t&&e.push({source:r.parent,target:r})}),e}function*T4(){var t=this,e,r=[t],n,i,a;do for(e=r.reverse(),r=[];t=e.pop();)if(yield t,n=t.children)for(i=0,a=n.length;i<a;++i)r.push(n[i]);while(r.length)}function bx(t,e){t instanceof Map?(t=[void 0,t],e===void 0&&(e=C4)):e===void 0&&(e=M4);for(var r=new xa(t),n,i=[r],a,o,u,s;n=i.pop();)if((o=e(n.data))&&(s=(o=Array.from(o)).length))for(n.children=o,u=s-1;u>=0;--u)i.push(a=o[u]=new xa(o[u])),a.parent=n,a.depth=n.depth+1;return r.eachBefore(I4)}function j4(){return bx(this).eachBefore(k4)}function M4(t){return t.children}function C4(t){return Array.isArray(t)?t[1]:null}function k4(t){t.data.value!==void 0&&(t.value=t.data.value),t.data=t.data.data}function I4(t){var e=0;do t.height=e;while((t=t.parent)&&t.height<++e)}function xa(t){this.data=t,this.depth=this.height=0,this.parent=null}xa.prototype=bx.prototype={constructor:xa,count:y4,each:m4,eachAfter:b4,eachBefore:g4,find:x4,sum:_4,sort:w4,path:O4,ancestors:P4,descendants:S4,leaves:$4,links:E4,copy:j4,[Symbol.iterator]:T4};function N4(t,e){return t.parent===e.parent?1:2}function Zs(t){var e=t.children;return e?e[0]:t.t}function Js(t){var e=t.children;return e?e[e.length-1]:t.t}function D4(t,e,r){var n=r/(e.i-t.i);e.c-=n,e.s+=r,t.c+=n,e.z+=r,e.m+=r}function R4(t){for(var e=0,r=0,n=t.children,i=n.length,a;--i>=0;)a=n[i],a.z+=e,a.m+=e,e+=a.s+(r+=a.c)}function L4(t,e,r){return t.a.parent===e.parent?t.a:r}function oo(t,e){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=e}oo.prototype=Object.create(xa.prototype);function B4(t){for(var e=new oo(t,0),r,n=[e],i,a,o,u;r=n.pop();)if(a=r._.children)for(r.children=new Array(u=a.length),o=u-1;o>=0;--o)n.push(i=r.children[o]=new oo(a[o],o)),i.parent=r;return(e.parent=new oo(null,0)).children=[e],e}function xG(){var t=N4,e=1,r=1,n=null;function i(c){var l=B4(c);if(l.eachAfter(a),l.parent.m=-l.z,l.eachBefore(o),n)c.eachBefore(s);else{var f=c,h=c,p=c;c.eachBefore(function(b){b.x<f.x&&(f=b),b.x>h.x&&(h=b),b.depth>p.depth&&(p=b)});var d=f===h?1:t(f,h)/2,v=d-f.x,y=e/(h.x+d+v),g=r/(p.depth||1);c.eachBefore(function(b){b.x=(b.x+v)*y,b.y=b.depth*g})}return c}function a(c){var l=c.children,f=c.parent.children,h=c.i?f[c.i-1]:null;if(l){R4(c);var p=(l[0].z+l[l.length-1].z)/2;h?(c.z=h.z+t(c._,h._),c.m=c.z-p):c.z=p}else h&&(c.z=h.z+t(c._,h._));c.parent.A=u(c,h,c.parent.A||f[0])}function o(c){c._.x=c.z+c.parent.m,c.m+=c.parent.m}function u(c,l,f){if(l){for(var h=c,p=c,d=l,v=h.parent.children[0],y=h.m,g=p.m,b=d.m,_=v.m,w;d=Js(d),h=Zs(h),d&&h;)v=Zs(v),p=Js(p),p.a=c,w=d.z+b-h.z-y+t(d._,h._),w>0&&(D4(L4(d,c,f),c,w),y+=w,g+=w),b+=d.m,y+=h.m,_+=v.m,g+=p.m;d&&!Js(p)&&(p.t=d,p.m+=b-g),h&&!Zs(v)&&(v.t=h,v.m+=y-_,f=c)}return f}function s(c){c.x*=e,c.y=c.depth*r}return i.separation=function(c){return arguments.length?(t=c,i):t},i.size=function(c){return arguments.length?(n=!1,e=+c[0],r=+c[1],i):n?null:[e,r]},i.nodeSize=function(c){return arguments.length?(n=!0,e=+c[0],r=+c[1],i):n?[e,r]:null},i}const z4=Math.random,_G=function t(e){function r(n,i){var a,o;return n=n==null?0:+n,i=i==null?1:+i,function(){var u;if(a!=null)u=a,a=null;else do a=e()*2-1,u=e()*2-1,o=a*a+u*u;while(!o||o>1);return n+i*u*Math.sqrt(-2*Math.log(o)/o)}}return r.source=t,r}(z4);function F4(t){for(var e=t.length/6|0,r=new Array(e),n=0;n<e;)r[n]="#"+t.slice(n*6,++n*6);return r}const wG=F4("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),Qa=t=>()=>t;function W4(t,{sourceEvent:e,target:r,transform:n,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},transform:{value:n,enumerable:!0,configurable:!0},_:{value:i}})}function ze(t,e,r){this.k=t,this.x=e,this.y=r}ze.prototype={constructor:ze,scale:function(t){return t===1?this:new ze(this.k*t,this.x,this.y)},translate:function(t,e){return t===0&e===0?this:new ze(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var xx=new ze(1,0,0);ze.prototype;function Qs(t){t.stopImmediatePropagation()}function wi(t){t.preventDefault(),t.stopImmediatePropagation()}function U4(t){return(!t.ctrlKey||t.type==="wheel")&&!t.button}function K4(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t,t.hasAttribute("viewBox")?(t=t.viewBox.baseVal,[[t.x,t.y],[t.x+t.width,t.y+t.height]]):[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]):[[0,0],[t.clientWidth,t.clientHeight]]}function Ey(){return this.__zoom||xx}function q4(t){return-t.deltaY*(t.deltaMode===1?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function H4(){return navigator.maxTouchPoints||"ontouchstart"in this}function G4(t,e,r){var n=t.invertX(e[0][0])-r[0][0],i=t.invertX(e[1][0])-r[1][0],a=t.invertY(e[0][1])-r[0][1],o=t.invertY(e[1][1])-r[1][1];return t.translate(i>n?(n+i)/2:Math.min(0,n)||Math.max(0,i),o>a?(a+o)/2:Math.min(0,a)||Math.max(0,o))}function OG(){var t=U4,e=K4,r=G4,n=q4,i=H4,a=[0,1/0],o=[[-1/0,-1/0],[1/0,1/0]],u=250,s=Sg,c=rh("start","zoom","end"),l,f,h,p=500,d=150,v=0,y=10;function g(P){P.property("__zoom",Ey).on("wheel.zoom",S,{passive:!1}).on("mousedown.zoom",$).on("dblclick.zoom",k).filter(i).on("touchstart.zoom",T).on("touchmove.zoom",M).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}g.transform=function(P,j,E,I){var N=P.selection?P.selection():P;N.property("__zoom",Ey),P!==N?m(P,j,E,I):N.interrupt().each(function(){x(this,arguments).event(I).start().zoom(null,typeof j=="function"?j.apply(this,arguments):j).end()})},g.scaleBy=function(P,j,E,I){g.scaleTo(P,function(){var N=this.__zoom.k,R=typeof j=="function"?j.apply(this,arguments):j;return N*R},E,I)},g.scaleTo=function(P,j,E,I){g.transform(P,function(){var N=e.apply(this,arguments),R=this.__zoom,B=E==null?w(N):typeof E=="function"?E.apply(this,arguments):E,U=R.invert(B),z=typeof j=="function"?j.apply(this,arguments):j;return r(_(b(R,z),B,U),N,o)},E,I)},g.translateBy=function(P,j,E,I){g.transform(P,function(){return r(this.__zoom.translate(typeof j=="function"?j.apply(this,arguments):j,typeof E=="function"?E.apply(this,arguments):E),e.apply(this,arguments),o)},null,I)},g.translateTo=function(P,j,E,I,N){g.transform(P,function(){var R=e.apply(this,arguments),B=this.__zoom,U=I==null?w(R):typeof I=="function"?I.apply(this,arguments):I;return r(xx.translate(U[0],U[1]).scale(B.k).translate(typeof j=="function"?-j.apply(this,arguments):-j,typeof E=="function"?-E.apply(this,arguments):-E),R,o)},I,N)};function b(P,j){return j=Math.max(a[0],Math.min(a[1],j)),j===P.k?P:new ze(j,P.x,P.y)}function _(P,j,E){var I=j[0]-E[0]*P.k,N=j[1]-E[1]*P.k;return I===P.x&&N===P.y?P:new ze(P.k,I,N)}function w(P){return[(+P[0][0]+ +P[1][0])/2,(+P[0][1]+ +P[1][1])/2]}function m(P,j,E,I){P.on("start.zoom",function(){x(this,arguments).event(I).start()}).on("interrupt.zoom end.zoom",function(){x(this,arguments).event(I).end()}).tween("zoom",function(){var N=this,R=arguments,B=x(N,R).event(I),U=e.apply(N,R),z=E==null?w(U):typeof E=="function"?E.apply(N,R):E,G=Math.max(U[1][0]-U[0][0],U[1][1]-U[0][1]),Z=N.__zoom,rt=typeof j=="function"?j.apply(N,R):j,ht=s(Z.invert(z).concat(G/Z.k),rt.invert(z).concat(G/rt.k));return function(xt){if(xt===1)xt=rt;else{var ct=ht(xt),K=G/ct[2];xt=new ze(K,z[0]-ct[0]*K,z[1]-ct[1]*K)}B.zoom(null,xt)}})}function x(P,j,E){return!E&&P.__zooming||new O(P,j)}function O(P,j){this.that=P,this.args=j,this.active=0,this.sourceEvent=null,this.extent=e.apply(P,j),this.taps=0}O.prototype={event:function(P){return P&&(this.sourceEvent=P),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(P,j){return this.mouse&&P!=="mouse"&&(this.mouse[1]=j.invert(this.mouse[0])),this.touch0&&P!=="touch"&&(this.touch0[1]=j.invert(this.touch0[0])),this.touch1&&P!=="touch"&&(this.touch1[1]=j.invert(this.touch1[0])),this.that.__zoom=j,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(P){var j=Tr(this.that).datum();c.call(P,this.that,new W4(P,{sourceEvent:this.sourceEvent,target:g,transform:this.that.__zoom,dispatch:c}),j)}};function S(P,...j){if(!t.apply(this,arguments))return;var E=x(this,j).event(P),I=this.__zoom,N=Math.max(a[0],Math.min(a[1],I.k*Math.pow(2,n.apply(this,arguments)))),R=Ar(P);if(E.wheel)(E.mouse[0][0]!==R[0]||E.mouse[0][1]!==R[1])&&(E.mouse[1]=I.invert(E.mouse[0]=R)),clearTimeout(E.wheel);else{if(I.k===N)return;E.mouse=[R,I.invert(R)],ao(this),E.start()}wi(P),E.wheel=setTimeout(B,d),E.zoom("mouse",r(_(b(I,N),E.mouse[0],E.mouse[1]),E.extent,o));function B(){E.wheel=null,E.end()}}function $(P,...j){if(h||!t.apply(this,arguments))return;var E=P.currentTarget,I=x(this,j,!0).event(P),N=Tr(P.view).on("mousemove.zoom",z,!0).on("mouseup.zoom",G,!0),R=Ar(P,E),B=P.clientX,U=P.clientY;rH(P.view),Qs(P),I.mouse=[R,this.__zoom.invert(R)],ao(this),I.start();function z(Z){if(wi(Z),!I.moved){var rt=Z.clientX-B,ht=Z.clientY-U;I.moved=rt*rt+ht*ht>v}I.event(Z).zoom("mouse",r(_(I.that.__zoom,I.mouse[0]=Ar(Z,E),I.mouse[1]),I.extent,o))}function G(Z){N.on("mousemove.zoom mouseup.zoom",null),nH(Z.view,I.moved),wi(Z),I.event(Z).end()}}function k(P,...j){if(t.apply(this,arguments)){var E=this.__zoom,I=Ar(P.changedTouches?P.changedTouches[0]:P,this),N=E.invert(I),R=E.k*(P.shiftKey?.5:2),B=r(_(b(E,R),I,N),e.apply(this,j),o);wi(P),u>0?Tr(this).transition().duration(u).call(m,B,I,P):Tr(this).call(g.transform,B,I,P)}}function T(P,...j){if(t.apply(this,arguments)){var E=P.touches,I=E.length,N=x(this,j,P.changedTouches.length===I).event(P),R,B,U,z;for(Qs(P),B=0;B<I;++B)U=E[B],z=Ar(U,this),z=[z,this.__zoom.invert(z),U.identifier],N.touch0?!N.touch1&&N.touch0[2]!==z[2]&&(N.touch1=z,N.taps=0):(N.touch0=z,R=!0,N.taps=1+!!l);l&&(l=clearTimeout(l)),R&&(N.taps<2&&(f=z[0],l=setTimeout(function(){l=null},p)),ao(this),N.start())}}function M(P,...j){if(this.__zooming){var E=x(this,j).event(P),I=P.changedTouches,N=I.length,R,B,U,z;for(wi(P),R=0;R<N;++R)B=I[R],U=Ar(B,this),E.touch0&&E.touch0[2]===B.identifier?E.touch0[0]=U:E.touch1&&E.touch1[2]===B.identifier&&(E.touch1[0]=U);if(B=E.that.__zoom,E.touch1){var G=E.touch0[0],Z=E.touch0[1],rt=E.touch1[0],ht=E.touch1[1],xt=(xt=rt[0]-G[0])*xt+(xt=rt[1]-G[1])*xt,ct=(ct=ht[0]-Z[0])*ct+(ct=ht[1]-Z[1])*ct;B=b(B,Math.sqrt(xt/ct)),U=[(G[0]+rt[0])/2,(G[1]+rt[1])/2],z=[(Z[0]+ht[0])/2,(Z[1]+ht[1])/2]}else if(E.touch0)U=E.touch0[0],z=E.touch0[1];else return;E.zoom("touch",r(_(B,U,z),E.extent,o))}}function C(P,...j){if(this.__zooming){var E=x(this,j).event(P),I=P.changedTouches,N=I.length,R,B;for(Qs(P),h&&clearTimeout(h),h=setTimeout(function(){h=null},p),R=0;R<N;++R)B=I[R],E.touch0&&E.touch0[2]===B.identifier?delete E.touch0:E.touch1&&E.touch1[2]===B.identifier&&delete E.touch1;if(E.touch1&&!E.touch0&&(E.touch0=E.touch1,delete E.touch1),E.touch0)E.touch0[1]=this.__zoom.invert(E.touch0[0]);else if(E.end(),E.taps===2&&(B=Ar(B,this),Math.hypot(f[0]-B[0],f[1]-B[1])<y)){var U=Tr(this).on("dblclick.zoom");U&&U.apply(this,arguments)}}}return g.wheelDelta=function(P){return arguments.length?(n=typeof P=="function"?P:Qa(+P),g):n},g.filter=function(P){return arguments.length?(t=typeof P=="function"?P:Qa(!!P),g):t},g.touchable=function(P){return arguments.length?(i=typeof P=="function"?P:Qa(!!P),g):i},g.extent=function(P){return arguments.length?(e=typeof P=="function"?P:Qa([[+P[0][0],+P[0][1]],[+P[1][0],+P[1][1]]]),g):e},g.scaleExtent=function(P){return arguments.length?(a[0]=+P[0],a[1]=+P[1],g):[a[0],a[1]]},g.translateExtent=function(P){return arguments.length?(o[0][0]=+P[0][0],o[1][0]=+P[1][0],o[0][1]=+P[0][1],o[1][1]=+P[1][1],g):[[o[0][0],o[0][1]],[o[1][0],o[1][1]]]},g.constrain=function(P){return arguments.length?(r=P,g):r},g.duration=function(P){return arguments.length?(u=+P,g):u},g.interpolate=function(P){return arguments.length?(s=P,g):s},g.on=function(){var P=c.on.apply(c,arguments);return P===c?g:P},g.clickDistance=function(P){return arguments.length?(v=(P=+P)*P,g):Math.sqrt(v)},g.tapDistance=function(P){return arguments.length?(y=+P,g):y},g}export{cI as $,bx as A,fG as B,d8 as C,xx as D,si as E,Q4 as F,J4 as G,pG as H,fz as I,Ta as J,Ea as K,pn as L,os as M,yG as N,Mt as O,hG as P,vG as Q,uG as R,NA as S,ke as T,gr as U,dG as V,ka as W,Jr as X,Qr as Y,Rb as Z,QN as _,Zr as a,Vg as a0,Rf as a1,Vu as a2,FI as a3,zI as a4,An as a5,BI as a6,LI as a7,jo as a8,$a as a9,Nf as aa,kf as ab,Cr as ac,To as ad,mG as ae,wG as af,KA as ag,qA as ah,UA as ai,WA as aj,FA as ak,zA as al,BA as am,Iu as an,iG as ao,oG as ap,aG as aq,eG as ar,nG as as,rG as at,tG as au,tm as av,Qy as aw,LA as ax,RA as ay,Fu as b,Jf as c,lG as d,Ma as e,mr as f,Tr as g,Lp as h,gG as i,SA as j,sG as k,Eo as l,Bp as m,bG as n,Zy as o,_G as p,jk as q,Ck as r,cG as s,Y4 as t,bf as u,Z4 as v,Ui as w,DA as x,xG as y,OG as z};
