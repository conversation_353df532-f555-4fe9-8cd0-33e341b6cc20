import{u as te,j as e,B as l,e as n,D as oe,R as u,i as c,N as de,f as x,bi as D,ai as F,ba as V,bb as b,O as A,aE as f,I as y,bj as B,aD as K,bk as ce,a3 as M,aF as xe,aJ as me,bl as he,h as v,b9 as Q,be as X,bm as je,a_ as pe,aj as ue,ak as be,bn as fe,bo as ye,G as Y,k as ge,bg as Z}from"./mui-libs-CfwFIaTD.js";import{r as L}from"./react-libs-Cr2nE3UY.js";import{a as ve}from"./index-Bpan7Tbe.js";const we=({value:q,onChange:P,showEmpty:j=!1,emptyMessage:p="Please select a dataset",filterDataTypes:H=["all"],minRows:ee=0,minColumns:se=0,label:m="Dataset",helperText:t,variant:O="default",showActions:S=!0,error:z=!1,required:h=!1,size:I="medium"})=>{var T,U;const{datasets:C,currentDataset:_,setCurrentDataset:le,removeDataset:ie}=ve(),a=te(),[W,J]=L.useState(O==="card"?"grid":"list"),[k,ne]=L.useState(!1),[w,ae]=L.useState(!1),o=C.filter(s=>!(s.data.length<ee||s.columns.length<se||!H.includes("all")&&!H.some(d=>s.columns.some(G=>{switch(d){case"numeric":return G.type==="numeric";case"categorical":return G.type==="categorical";case"date":return G.type==="date";default:return!0}})))),R=s=>{const i=C.find(d=>d.id===s);i&&(le(i),P&&P(s))},r=q||(_?_.id:""),N=s=>{R(s)},$=(s,i,d)=>{s.stopPropagation(),window.confirm(`Are you sure you want to delete the dataset "${d}"? This action cannot be undone.`)&&ie(i)},re=s=>{R(s.target.value)},E=s=>{R(s.target.value)},g=()=>{window.location.hash="#/data-management/import"};switch(O){case"card":return e.jsxs(l,{sx:{mb:2},children:[e.jsxs(l,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(n,{variant:"subtitle1",fontWeight:"medium",children:[m,h&&" *"]}),e.jsxs(l,{children:[e.jsx(f,{title:"List view",children:e.jsx(y,{size:"small",onClick:()=>J("list"),color:W==="list"?"primary":"default",children:e.jsx(fe,{fontSize:"small"})})}),e.jsx(f,{title:"Grid view",children:e.jsx(y,{size:"small",onClick:()=>J("grid"),color:W==="grid"?"primary":"default",children:e.jsx(ye,{fontSize:"small"})})}),o.length===0&&e.jsx(x,{startIcon:e.jsx(D,{}),size:"small",variant:"outlined",onClick:g,sx:{ml:1},children:"Import Data"})]})]}),t&&e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:2},children:t}),o.length===0?e.jsxs(u,{elevation:0,sx:{p:3,textAlign:"center",borderStyle:"dashed",borderWidth:1,borderColor:"divider",borderRadius:2,backgroundColor:c(a.palette.background.default,.5)},children:[e.jsx(n,{variant:"body1",color:"text.secondary",sx:{mb:2},children:p}),e.jsx(x,{startIcon:e.jsx(D,{}),variant:"outlined",onClick:g,children:"Import Data"})]}):W==="grid"?e.jsx(Y,{container:!0,spacing:2,children:o.map(s=>e.jsx(Y,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(ge,{sx:{height:"100%",transition:"all 0.2s",border:s.id===r?`2px solid ${a.palette.primary.main}`:"2px solid transparent","&:hover":{boxShadow:a.shadows[4]},display:"flex",flexDirection:"column"},children:[e.jsxs(l,{onClick:()=>N(s.id),sx:{flexGrow:1,cursor:"pointer",p:2,display:"flex",flexDirection:"column",justifyContent:"space-between","&:hover":{backgroundColor:c(a.palette.action.hover,.04)}},children:[e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle1",noWrap:!0,sx:{mb:1,maxWidth:"100%"},children:s.name}),e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:1.5},children:[e.jsx(A,{sx:{color:"text.secondary",fontSize:18,mr:.5}}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:[s.data.length," rows × ",s.columns.length," columns"]})]})]}),e.jsxs(l,{sx:{display:"flex",flexWrap:"wrap",gap:.5,mt:"auto"},children:[s.columns.filter((i,d)=>d<5).map((i,d)=>e.jsx(v,{label:i.name,size:"small",color:i.type==="numeric"?"primary":i.type==="categorical"?"secondary":i.type==="date"?"info":"default",variant:"outlined",sx:{height:"20px",fontSize:"0.75rem"}},d)),s.columns.length>5&&e.jsx(v,{label:`+${s.columns.length-5} more`,size:"small",sx:{height:"20px",fontSize:"0.75rem"}})]})]}),e.jsxs(l,{sx:{p:1,display:"flex",justifyContent:"flex-end",alignItems:"center",borderTop:`1px solid ${a.palette.divider}`},children:[s.id===r&&e.jsx(Z,{color:"primary",sx:{verticalAlign:"middle",mr:1}}),e.jsx(f,{title:"Delete dataset",children:e.jsx(y,{size:"small",onClick:i=>$(i,s.id,s.name),children:e.jsx(B,{fontSize:"small"})})})]})]})},s.id))}):e.jsx(l,{children:o.map(s=>e.jsxs(u,{sx:{mb:1,display:"flex",alignItems:"center",justifyContent:"space-between",backgroundColor:s.id===r?c(a.palette.primary.main,.05):a.palette.background.paper,border:s.id===r?`1px solid ${a.palette.primary.main}`:`1px solid ${a.palette.divider}`,"&:hover":{backgroundColor:s.id===r?c(a.palette.primary.main,.1):c(a.palette.action.hover,.04)}},children:[e.jsxs(l,{onClick:()=>N(s.id),sx:{display:"flex",alignItems:"center",flexGrow:1,cursor:"pointer",p:2},children:[e.jsx(A,{sx:{color:s.id===r?"primary.main":"text.secondary",mr:2}}),e.jsxs(l,{sx:{flex:1},children:[e.jsx(n,{variant:"subtitle2",children:s.name}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:[s.data.length," rows × ",s.columns.length," columns"]})]})]}),e.jsxs(l,{sx:{p:2,pl:0,display:"flex",alignItems:"center"},children:[s.id===r&&e.jsx(Z,{color:"primary",sx:{verticalAlign:"middle",mr:.5}}),e.jsx(f,{title:"Delete dataset",children:e.jsx(y,{size:"small",onClick:i=>$(i,s.id,s.name),children:e.jsx(B,{fontSize:"small"})})})]})]},s.id))})]});case"radio":return e.jsxs(l,{sx:{mb:2},children:[e.jsxs(n,{variant:"subtitle1",fontWeight:"medium",sx:{mb:1},children:[m,h&&" *"]}),t&&e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:1},children:t}),o.length===0?e.jsxs(u,{elevation:0,sx:{p:3,textAlign:"center",borderStyle:"dashed",borderWidth:1,borderColor:"divider",borderRadius:1},children:[e.jsx(n,{variant:"body1",color:"text.secondary",sx:{mb:2},children:p}),e.jsx(x,{startIcon:e.jsx(D,{}),variant:"outlined",onClick:g,children:"Import Data"})]}):e.jsx(pe,{value:r,onChange:re,children:o.map(s=>e.jsx(u,{sx:{p:1,mb:1,backgroundColor:s.id===r?c(a.palette.primary.main,.05):a.palette.background.paper,border:s.id===r?`1px solid ${a.palette.primary.main}`:`1px solid ${a.palette.divider}`},children:e.jsx(ue,{value:s.id,control:e.jsx(be,{}),label:e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle2",children:s.name}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:[s.data.length," rows × ",s.columns.length," columns"]})]}),sx:{width:"100%",m:0}})},s.id))})]});case"compact":return e.jsxs(l,{children:[e.jsxs(l,{sx:{display:"flex",alignItems:"flex-start"},children:[e.jsxs(F,{fullWidth:!0,size:I,error:z,sx:{maxWidth:"300px"},children:[e.jsxs(Q,{id:"dataset-select-label",children:[m,h&&" *"]}),e.jsxs(V,{labelId:"dataset-select-label",value:r,onChange:E,label:m+(h?" *":""),displayEmpty:j,children:[j&&e.jsx(b,{value:"",children:e.jsx("em",{children:p})}),o.map(s=>e.jsx(b,{value:s.id,children:e.jsxs(l,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(n,{children:s.name}),e.jsx(v,{label:`${s.data.length} rows`,size:"small",sx:{ml:1,height:"20px"}})]})},s.id))]}),t&&e.jsx(X,{children:t})]}),S&&e.jsx(l,{sx:{ml:1,mt:I==="small"?.5:1},children:e.jsx(f,{title:"Import new dataset",children:e.jsx(y,{size:"small",onClick:g,color:"primary",children:e.jsx(je,{fontSize:I==="small"?"small":"medium"})})})})]}),r&&S&&e.jsxs(l,{sx:{mt:1},children:[e.jsx(x,{size:"small",variant:"text",startIcon:e.jsx(K,{sx:{transform:k?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s"}}),onClick:()=>ne(!k),children:k?"Hide details":"Show details"}),e.jsx(M,{in:k,timeout:"auto",unmountOnExit:!0,children:e.jsx(l,{sx:{mt:1,p:1.5,bgcolor:c(a.palette.background.default,.5),borderRadius:1},children:(T=C.find(s=>s.id===r))==null?void 0:T.columns.map((s,i)=>e.jsxs(l,{sx:{mb:.5,display:"flex",alignItems:"center"},children:[e.jsx(v,{label:s.type,size:"small",color:s.type==="numeric"?"primary":s.type==="categorical"?"secondary":s.type==="date"?"info":"default",sx:{minWidth:90,mr:1}}),e.jsx(n,{variant:"body2",children:s.name})]},i))})})]})]});case"minimal":return e.jsxs(F,{fullWidth:!0,size:I,error:z,variant:"outlined",children:[e.jsxs(Q,{id:"dataset-select-minimal-label",children:[m,h&&" *"]}),e.jsxs(V,{labelId:"dataset-select-minimal-label",value:r,onChange:E,label:m+(h?" *":""),displayEmpty:j,children:[j&&e.jsx(b,{value:"",children:e.jsx("em",{children:p})}),o.map(s=>e.jsx(b,{value:s.id,children:s.name},s.id))]}),t&&e.jsx(X,{children:t})]});case"default":default:return e.jsxs(l,{children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsxs(n,{variant:"subtitle1",fontWeight:"medium",children:[m,h&&" *"]}),e.jsx(oe,{sx:{flex:1,ml:2}})]}),t&&e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:2},children:t}),o.length===0?e.jsxs(u,{elevation:0,sx:{p:3,textAlign:"center",borderStyle:"dashed",borderWidth:1,borderColor:"divider",borderRadius:2,backgroundColor:c(a.palette.background.default,.5)},children:[e.jsx(l,{sx:{display:"flex",justifyContent:"center",mb:2},children:e.jsx(de,{sx:{fontSize:40,color:"text.secondary",opacity:.5}})}),e.jsx(n,{variant:"body1",color:"text.secondary",sx:{mb:2},children:p}),e.jsx(x,{startIcon:e.jsx(D,{}),variant:"contained",onClick:g,children:"Import Data"})]}):e.jsxs(l,{children:[e.jsx(F,{fullWidth:!0,error:z,children:e.jsxs(V,{value:r,onChange:E,displayEmpty:j,sx:{mb:2},children:[j&&e.jsx(b,{value:"",children:e.jsx("em",{children:p})}),o.map(s=>e.jsx(b,{value:s.id,children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(A,{sx:{mr:1,color:"text.secondary",fontSize:20}}),e.jsxs(l,{sx:{flexGrow:1},children:[e.jsx(n,{children:s.name}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:["(",s.data.length," rows, ",s.columns.length," columns)"]})]}),e.jsx(f,{title:"Delete dataset",children:e.jsx(y,{size:"small",onClick:i=>$(i,s.id,s.name),sx:{ml:1},children:e.jsx(B,{fontSize:"small"})})})]})},s.id))]})}),r&&e.jsxs(l,{sx:{mt:2},children:[" ",e.jsxs(x,{onClick:()=>ae(!w),startIcon:w?e.jsx(K,{}):e.jsx(ce,{}),size:"small",sx:{mb:1,textTransform:"none"},children:[w?"Hide":"Show"," Dataset Information"]}),e.jsx(M,{in:w,timeout:"auto",unmountOnExit:!0,children:e.jsxs(u,{sx:{p:2,bgcolor:c(a.palette.primary.main,.03),borderRadius:1},children:[e.jsxs(l,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsxs(n,{variant:"subtitle2",sx:{display:"flex",alignItems:"center"},children:[e.jsx(xe,{fontSize:"small",sx:{mr:.5,color:"primary.main"}}),"Selected Dataset Details"]}),S&&e.jsxs(l,{children:[e.jsx(x,{size:"small",variant:"outlined",startIcon:e.jsx(me,{}),onClick:()=>window.location.hash="#/data-management/editor",sx:{mr:1},children:"View Data"}),e.jsx(x,{size:"small",variant:"outlined",startIcon:e.jsx(he,{}),onClick:()=>window.location.hash="#/data-management/transform",children:"Transform"})]})]}),(U=C.find(s=>s.id===r))==null?void 0:U.columns.map((s,i)=>e.jsxs(l,{sx:{mb:.5,display:"flex",alignItems:"center"},children:[e.jsx(v,{label:s.type,size:"small",color:s.type==="numeric"?"primary":s.type==="categorical"?"secondary":s.type==="date"?"info":"default",sx:{minWidth:90,mr:1}}),e.jsx(n,{variant:"body2",children:s.name})]},i))]})})," "]})]})]})}};export{we as D};
