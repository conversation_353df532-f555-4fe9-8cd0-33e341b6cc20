import{j as a,u as C,C as w,R as n,i as s,e as r,B as o,h as l,G as c,k as D,bz as I,aE as B,I as z,aF as d,d as S,l as P,f as k,bA as A,W as u,an as h,bt as x,a8 as E,ab as R,$ as m,ad as g}from"./mui-libs-CfwFIaTD.js";import{r as W}from"./react-libs-Cr2nE3UY.js";const p=[{name:"Bar Chart",shortDescription:"Compare values across categories",detailedDescription:"Visualize the distribution of categorical data or compare numerical values across different categories using vertical or horizontal bars.",path:"charts/bar",icon:a.jsx(u,{}),category:"Categorical",color:"#4CAF50"},{name:"Box Plot",shortDescription:"Show distribution and outliers",detailedDescription:"Display the distribution of a dataset, showing median, quartiles, and potential outliers. Useful for comparing distributions between groups.",path:"charts/boxplot",icon:a.jsx(h,{}),category:"Distribution",color:"#FF9800"},{name:"Histogram",shortDescription:"Visualize frequency distribution",detailedDescription:"Represent the frequency distribution of numerical data using bins. Helps understand the shape, center, and spread of the data.",path:"charts/histogram",icon:a.jsx(x,{}),category:"Distribution",color:"#2196F3"},{name:"Pie Chart",shortDescription:"Show parts of a whole",detailedDescription:"Illustrate the proportion of each category relative to the whole. Best used for a small number of categories.",path:"charts/pie",icon:a.jsx(E,{}),category:"Categorical",color:"#9C27B0"},{name:"RainCloud Plot",shortDescription:"Combine box plot, scatter plot, and density",detailedDescription:"A hybrid visualization combining a box plot, scatter plot (or jittered points), and a half-violin or density plot to show the distribution and individual data points.",path:"charts/raincloud",icon:a.jsx(R,{}),category:"Distribution",color:"#E91E63"},{name:"Scatter Plot",shortDescription:"Show relationship between two variables",detailedDescription:"Plot individual data points on a two-dimensional graph to show the relationship or correlation between two numerical variables.",path:"charts/scatter",icon:a.jsx(m,{}),category:"Relationship",color:"#009688"},{name:"Error Bar Chart",shortDescription:"Display means with uncertainty measures",detailedDescription:"Show mean values with error bars indicating variability or uncertainty. Error bars can represent standard error, standard deviation, or confidence intervals.",path:"charts/errorbar",icon:a.jsx(g,{}),category:"Distribution",color:"#795548"},{name:"Sankey Diagram",shortDescription:"Visualize flow relationships",detailedDescription:"Show flow relationships between categorical variables with proportional link widths. Ideal for visualizing transitions, pathways, or multi-level categorical relationships.",path:"charts/sankey",icon:a.jsx(h,{}),category:"Other",color:"#607D8B"}],O=({onNavigate:b})=>{const e=C(),[i,j]=W.useState("All"),y=["All","Categorical","Distribution","Relationship","Other"],f=i==="All"?p:p.filter(t=>t.category===i),v=t=>{switch(t){case"Categorical":return a.jsx(u,{});case"Distribution":return a.jsx(x,{});case"Relationship":return a.jsx(m,{});default:return a.jsx(g,{})}};return a.jsxs(w,{maxWidth:"lg",sx:{py:4},children:[a.jsxs(n,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${s(e.palette.primary.main,.1)} 0%, ${s(e.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[a.jsx(r,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Data Visualization Tools"}),a.jsx(r,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Create insightful charts and plots from your data"}),a.jsx(r,{variant:"body1",color:"text.secondary",children:"Explore various visualization options to understand patterns, distributions, and relationships within your datasets."})]}),a.jsxs(o,{sx:{mb:4},children:[a.jsx(r,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),a.jsx(o,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:y.map(t=>a.jsx(l,{label:t,onClick:()=>j(t),variant:i===t?"filled":"outlined",color:i===t?"primary":"default",icon:t!=="All"?v(t):void 0,sx:{"&:hover":{backgroundColor:i===t?e.palette.primary.dark:s(e.palette.primary.main,.1)}}},t))})]}),a.jsx(c,{container:!0,spacing:3,children:f.map((t,F)=>a.jsx(c,{item:!0,xs:12,md:6,lg:4,children:a.jsxs(D,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:e.shadows[8],"& .launch-button":{backgroundColor:t.color,color:"white"}}},children:[a.jsx(I,{avatar:a.jsx(S,{sx:{bgcolor:t.color,width:48,height:48},children:t.icon}),title:a.jsx(r,{variant:"h6",fontWeight:"bold",children:t.name}),subheader:a.jsx(o,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:a.jsx(l,{label:t.category,size:"small",variant:"outlined",color:"primary"})}),action:a.jsx(B,{title:"More information",children:a.jsx(z,{size:"small",children:a.jsx(d,{})})})}),a.jsxs(P,{sx:{flexGrow:1,pt:0},children:[a.jsx(r,{variant:"body2",color:"text.secondary",paragraph:!0,children:t.shortDescription}),a.jsx(r,{variant:"body2",paragraph:!0,children:t.detailedDescription})]}),a.jsx(o,{sx:{p:2,pt:0},children:a.jsxs(k,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>b(t.path),endIcon:a.jsx(A,{}),sx:{borderColor:t.color,color:t.color,fontWeight:"bold","&:hover":{borderColor:t.color}},children:["Launch ",t.name]})})]})},t.name))}),a.jsx(n,{elevation:1,sx:{p:3,mt:4,backgroundColor:s(e.palette.info.main,.05),border:`1px solid ${s(e.palette.info.main,.2)}`},children:a.jsxs(o,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[a.jsx(d,{color:"info"}),a.jsxs(o,{children:[a.jsx(r,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),a.jsxs(r,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Comparing categories?"})," Try Bar Chart or Pie Chart"]}),a.jsxs(r,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Understanding data distribution?"})," Use Histogram, Box Plot, or RainCloud Plot"]}),a.jsxs(r,{variant:"body2",color:"text.secondary",children:["• ",a.jsx("strong",{children:"Exploring relationships between variables?"})," Scatter Plot is the tool for you"]})]})]})})]})};export{O as D,p as d};
