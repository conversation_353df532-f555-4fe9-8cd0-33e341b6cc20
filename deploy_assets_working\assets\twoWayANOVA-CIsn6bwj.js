import{j as D}from"./other-utils-CR9xr_gI.js";const j=t=>{if(t.length<=1)return 0;const c=t.reduce((a,o)=>a+o,0)/t.length,r=t.reduce((a,o)=>a+Math.pow(o-c,2),0)/(t.length-1);return Math.sqrt(r)},x=(t,c)=>c>0?t/Math.sqrt(c):0,at=(t,c="Factor A",r="Factor B")=>{if(!t||t.length===0)throw new Error("Data must be a non-empty array");const a=[...new Set(t.map(e=>e.factorA))].sort(),o=[...new Set(t.map(e=>e.factorB))].sort(),h={};a.forEach(e=>{o.forEach(n=>{h[`${e}_${n}`]=[]})}),t.forEach(e=>{const n=`${e.factorA}_${e.factorB}`;h[n]&&h[n].push(e.value)});const M=[],p={},m={};a.forEach(e=>{o.forEach(n=>{const s=`${e}_${n}`,l=h[s];if(l.length>0){const d=l.length,A=l.reduce((Z,tt)=>Z+tt,0)/d,E=j(l),F=x(E,d),X=A-1.96*F,Y=A+1.96*F;p[s]=A,m[s]=d,M.push({factor1Level:e,factor2Level:n,N:d,mean:A,sd:E,se:F,ci95Lower:X,ci95Upper:Y})}else p[s]=NaN,m[s]=0,M.push({factor1Level:e,factor2Level:n,N:0,mean:NaN,sd:NaN,se:NaN,ci95Lower:NaN,ci95Upper:NaN})})});const g={};a.forEach(e=>{const n=t.filter(s=>s.factorA===e).map(s=>s.value);if(n.length>0){const s=n.reduce((E,F)=>E+F,0)/n.length,l=j(n),d=n.length,A=x(l,d);g[e]={mean:s,sd:l,n:d,se:A}}});const N={};o.forEach(e=>{const n=t.filter(s=>s.factorB===e).map(s=>s.value);if(n.length>0){const s=n.reduce((E,F)=>E+F,0)/n.length,l=j(n),d=n.length,A=x(l,d);N[e]={mean:s,sd:l,n:d,se:A}}});const i=t.map(e=>e.value),f=i.length,u=i.reduce((e,n)=>e+n,0)/f;let S=0,w=0,y=0,T=0,W=0;S=i.reduce((e,n)=>e+Math.pow(n-u,2),0),a.forEach(e=>{const n=t.filter(l=>l.factorA===e),s=g[e].mean;w+=n.length*Math.pow(s-u,2)}),o.forEach(e=>{const n=t.filter(l=>l.factorB===e),s=N[e].mean;y+=n.length*Math.pow(s-u,2)}),t.forEach(e=>{const n=`${e.factorA}_${e.factorB}`,s=p[n];isNaN(s)||(W+=Math.pow(e.value-s,2))}),a.forEach(e=>{o.forEach(n=>{const s=`${e}_${n}`,l=p[s],d=m[s];if(!isNaN(l)&&d>0){const A=g[e].mean,E=N[n].mean;T+=d*Math.pow(l-A-E+u,2)}})});const $=a.length-1,q=o.length-1,L=$*q,B=f-a.length*o.length,z=f-1,C=$>0?w/$:0,U=q>0?y/q:0,b=L>0?T/L:0,v=B>0?W/B:0,k=v>0?C/v:0,V=v>0?U/v:0,_=v>0?b/v:0,H=$>0&&B>0&&k>0?1-D.centralF.cdf(k,$,B):1,J=q>0&&B>0&&V>0?1-D.centralF.cdf(V,q,B):1,G=L>0&&B>0&&_>0?1-D.centralF.cdf(_,L,B):1,I=S>0?w/S:0,K=S>0?y/S:0,P=S>0?T/S:0,Q=[{source:c,SS:w,df:$,MS:C,F:k,p:H,etaSquared:I},{source:r,SS:y,df:q,MS:U,F:V,p:J,etaSquared:K},{source:`${c} × ${r}`,SS:T,df:L,MS:b,F:_,p:G,etaSquared:P},{source:"Error",SS:W,df:B,MS:v,F:null,p:null,etaSquared:null},{source:"Total",SS:S,df:z,MS:null,F:null,p:null,etaSquared:null}],R=et(t,a,o,h);return{anovaTable:Q,descriptiveStats:M,marginalMeansFactor1:g,marginalMeansFactor2:N,grandMean:u,totalN:f,assumptions:R}},et=(t,c,r,a)=>{const o=[];t.forEach(p=>{const m=`${p.factorA}_${p.factorB}`,g=a[m];if(g.length>0){const N=g.reduce((i,f)=>i+f,0)/g.length;o.push(p.value-N)}});const h=ct(o),M=ot(a);return{normality:{passed:h.p>.05,statistic:h.statistic,p:h.p,test:"Shapiro-Wilk (approximation)"},homogeneity:{passed:M.p>.05,statistic:M.statistic,p:M.p,test:"Levene's Test"},independence:{passed:!0,note:"Assumed based on study design"}}},O=t=>t.length===0?NaN:t.reduce((c,r)=>c+r,0)/t.length,nt=t=>{if(t.length===0)return NaN;const c=[...t].sort((a,o)=>a-o),r=Math.floor(c.length/2);return c.length%2===0?(c[r-1]+c[r])/2:c[r]},ct=t=>{if(t.length<3)return{statistic:NaN,p:1};const c=t.length,r=O(t),a=t.reduce((i,f)=>i+Math.pow(f-r,2),0)/(c-1),o=Math.sqrt(a);if(o===0)return{statistic:1,p:0};const h=t.map(i=>(i-r)/o),M=h.reduce((i,f)=>i+Math.pow(f,3),0)/c,p=h.reduce((i,f)=>i+Math.pow(f,4),0)/c,m=c/6*(Math.pow(M,2)+Math.pow(p-3,2)/4),g=1-D.chisquare.cdf(m,2);return{statistic:Math.exp(-m/c),p:g}},ot=t=>{const c=Object.values(t).filter(u=>u.length>0);if(c.length<2)return{statistic:NaN,p:1};const r=c.map(u=>{const S=nt(u);return u.map(w=>Math.abs(w-S))}),a=r.flat(),o=O(a);let h=0,M=0;r.forEach(u=>{const S=O(u);h+=u.length*Math.pow(S-o,2),M+=u.reduce((w,y)=>w+Math.pow(y-S,2),0)});const p=c.length-1,m=a.length-c.length,g=h/p,N=M/m,i=N>0?g/N:0,f=1-D.centralF.cdf(i,p,m);return{statistic:i,p:f}},rt=(t,c,r,a)=>t.map(o=>({value:Number(o[c]),factorA:String(o[r]),factorB:String(o[a])})).filter(o=>!isNaN(o.value));export{rt as p,at as t};
