import{u as re,j as a,ai as se,b9 as te,ba as ne,bb as b,B as t,Z as w,e as c,bc as k,r as D,an as H,bd as Z,h as U,be as ie,aU as le,ah as de,aE as ce,I as xe,aD as oe,R as _,i as A,a3 as me,D as he,G as R,k as je,bf as fe,bg as pe,bh as ye}from"./mui-libs-CfwFIaTD.js";import{r as T}from"./react-libs-Cr2nE3UY.js";import{a as be,D as s}from"./index-Bpan7Tbe.js";const ge=le(ye)(({theme:h})=>({"& .MuiAutocomplete-tag":{margin:2}})),Ae=({label:h="Variables",helperText:p,value:O="",onChange:z,datasetId:P,multiple:i=!1,required:C=!1,allowedTypes:J=[s.NUMERIC,s.CATEGORICAL,s.DATE],excludeTypes:K=[],size:L="medium",disabled:M=!1,error:v=!1,variant:Q="default",chipColor:V="primary",columnFilter:W,minSelections:$=0,maxSelections:m,showTypeIcons:g=!0,placeholder:X="Select variables",fullWidth:F=!0,grouped:G=!1})=>{const{datasets:Y,currentDataset:q}=be(),x=re(),[N,ee]=T.useState(!1),B=P?Y.find(e=>e.id===P):q,l=B?B.columns.filter(e=>!(!J.includes(e.type)||K.includes(e.type)||W&&!W(e))):[],y=G?{[s.NUMERIC]:l.filter(e=>e.type===s.NUMERIC),[s.CATEGORICAL]:l.filter(e=>e.type===s.CATEGORICAL),[s.DATE]:l.filter(e=>e.type===s.DATE)}:null,[d,S]=T.useState(O);T.useEffect(()=>{S(O)},[O]);const j=e=>{S(e),z&&z(e)},f=e=>{switch(e){case s.NUMERIC:return a.jsx(w,{fontSize:"small",sx:{color:x.palette.primary.main}});case s.CATEGORICAL:return a.jsx(H,{fontSize:"small",sx:{color:x.palette.secondary.main}});case s.DATE:return a.jsx(Z,{fontSize:"small",sx:{color:x.palette.info.main}});default:return null}},I=e=>a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[g&&a.jsx(t,{sx:{mr:1},children:f(e.type)}),a.jsx(c,{children:e.name})]}),o=e=>i&&Array.isArray(d)?d.includes(e):d===e;switch(Q){case"card":return a.jsxs(t,{sx:{mt:1,mb:2},children:[a.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[a.jsxs(c,{variant:"subtitle1",fontWeight:"medium",children:[h,C&&" *"]}),l.length>0&&a.jsx(t,{children:a.jsx(ce,{title:"Variable information",children:a.jsx(xe,{size:"small",onClick:()=>ee(!N),children:a.jsx(oe,{sx:{transform:N?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s"}})})})})]}),p&&a.jsx(c,{variant:"body2",color:"text.secondary",sx:{mb:1},children:p}),l.length===0?a.jsx(_,{elevation:0,sx:{p:2,borderStyle:"dashed",borderWidth:1,borderColor:x.palette.divider,backgroundColor:A(x.palette.background.default,.5),borderRadius:1,textAlign:"center"},children:a.jsx(c,{variant:"body2",color:"text.secondary",children:"No variables available for selection"})}):a.jsxs(a.Fragment,{children:[a.jsx(me,{in:N,timeout:"auto",unmountOnExit:!0,children:a.jsxs(_,{sx:{p:2,mb:2,backgroundColor:A(x.palette.background.default,.5)},children:[a.jsx(c,{variant:"body2",fontWeight:"medium",children:"Available Variables"}),a.jsx(he,{sx:{my:1}}),a.jsx(R,{container:!0,spacing:1,children:l.map(e=>a.jsx(R,{item:!0,xs:12,sm:6,md:4,children:a.jsxs(t,{sx:{display:"flex",alignItems:"center",p:.5},children:[f(e.type),a.jsx(c,{variant:"body2",sx:{ml:1},children:e.name})]})},e.name))})]})}),a.jsx(R,{container:!0,spacing:1,children:l.map(e=>a.jsx(R,{item:!0,xs:12,sm:6,md:4,children:a.jsx(je,{sx:{cursor:"pointer",border:o(e.name)?`2px solid ${x.palette.primary.main}`:"2px solid transparent",transition:"all 0.2s",backgroundColor:o(e.name)?A(x.palette.primary.main,.05):x.palette.background.paper,"&:hover":{backgroundColor:o(e.name)?A(x.palette.primary.main,.1):A(x.palette.background.default,.5)}},onClick:()=>{if(i){const r=Array.isArray(d)?d:[];r.includes(e.name)?j(r.filter(n=>n!==e.name)):(!m||r.length<m)&&j([...r,e.name])}else j(e.name)},children:a.jsx(fe,{sx:{p:1},children:a.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[f(e.type),a.jsx(c,{variant:"body2",sx:{ml:1,fontWeight:o(e.name)?500:400},children:e.name})]}),o(e.name)&&a.jsx(pe,{color:"primary",fontSize:"small"})]})})})},e.name))}),i&&a.jsx(t,{sx:{mt:2},children:a.jsxs(c,{variant:"body2",color:"text.secondary",children:["Selected: ",Array.isArray(d)?d.length:0,m?` / ${m}`:""]})}),v&&a.jsx(c,{variant:"caption",color:"error",sx:{mt:1,display:"block"},children:i?`Please select ${$} to ${m||"unlimited"} variables`:"Please select a variable"})]})]});case"chip":return a.jsxs(t,{sx:{mb:2},children:[a.jsxs(c,{variant:"subtitle2",gutterBottom:!0,children:[h,C&&" *"]}),p&&a.jsx(c,{variant:"body2",color:"text.secondary",sx:{mb:1},children:p}),a.jsx(t,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:l.map(e=>a.jsx(U,{label:e.name,color:o(e.name)?V:"default",variant:o(e.name)?"filled":"outlined",onClick:()=>{if(i){const r=Array.isArray(d)?d:[];r.includes(e.name)?j(r.filter(n=>n!==e.name)):(!m||r.length<m)&&j([...r,e.name])}else j(o(e.name)?"":e.name)},icon:g&&f(e.type)||void 0,disabled:M||(i&&m?Array.isArray(d)&&d.length>=m&&!o(e.name):!1)},e.name))}),v&&a.jsx(c,{variant:"caption",color:"error",sx:{mt:1,display:"block"},children:i?`Please select ${$} to ${m||"unlimited"} variables`:"Please select a variable"})]});case"autocomplete":return a.jsx(ge,{multiple:i,options:l.map(e=>e.name),value:i?Array.isArray(d)?d:[]:d||null,onChange:(e,r)=>j(r===null?"":r),groupBy:G?e=>{const r=l.find(n=>n.name===e);if(r)switch(r.type){case s.NUMERIC:return"Numeric";case s.CATEGORICAL:return"Categorical";case s.DATE:return"Date";default:return"Other"}return"Other"}:void 0,renderTags:(e,r)=>e.map((n,u)=>{const E=l.find(ae=>ae.name===n);return a.jsx(U,{...r({index:u}),label:n,size:L,color:V,icon:g&&E&&f(E.type)||void 0},n)}),renderOption:(e,r)=>{const n=l.find(u=>u.name===r);return T.createElement("li",{...e,key:r}," ",a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[g&&n&&a.jsx(t,{sx:{mr:1},children:f(n.type)}),r]}))},renderInput:e=>a.jsx(de,{...e,label:h+(C?" *":""),placeholder:X,size:L,error:v,helperText:v?i?`Please select ${$} to ${m||"unlimited"} variables`:"Please select a variable":p,required:C,fullWidth:F}),disabled:M,fullWidth:F,disableCloseOnSelect:i,limitTags:3});case"default":default:return a.jsxs(se,{fullWidth:F,size:L,error:v,required:C,disabled:M,sx:{mb:2},children:[a.jsx(te,{id:`variable-select-${h.replace(/\s+/g,"-").toLowerCase()}`,children:h}),a.jsx(ne,{labelId:`variable-select-${h.replace(/\s+/g,"-").toLowerCase()}`,value:d,onChange:e=>j(e.target.value),label:h,multiple:i,renderValue:e=>{if(i&&Array.isArray(e))return a.jsx(t,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:e.map(n=>{const u=l.find(E=>E.name===n);return a.jsx(U,{label:n,size:"small",color:V,icon:(g&&u?f(u.type):void 0)||void 0},n)})});const r=l.find(n=>n.name===e);return g&&r?a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[f(r.type),a.jsx(t,{sx:{ml:1},children:e})]}):e},children:G&&y?a.jsxs(a.Fragment,{children:[y[s.NUMERIC].length>0&&[a.jsx(b,{disabled:!0,divider:!0,children:a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[a.jsx(w,{fontSize:"small",sx:{mr:1,color:x.palette.primary.main}}),a.jsx(c,{variant:"body2",fontWeight:"medium",children:"Numeric Variables"})]})},"numeric-header"),...y[s.NUMERIC].map(e=>a.jsx(b,{value:e.name,children:i?a.jsxs(a.Fragment,{children:[a.jsx(k,{checked:o(e.name)}),a.jsx(D,{primary:e.name})]}):I(e)},e.name))],y[s.CATEGORICAL].length>0&&[a.jsx(b,{disabled:!0,divider:!0,children:a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[a.jsx(H,{fontSize:"small",sx:{mr:1,color:x.palette.secondary.main}}),a.jsx(c,{variant:"body2",fontWeight:"medium",children:"Categorical Variables"})]})},"categorical-header"),...y[s.CATEGORICAL].map(e=>a.jsx(b,{value:e.name,children:i?a.jsxs(a.Fragment,{children:[a.jsx(k,{checked:o(e.name)}),a.jsx(D,{primary:e.name})]}):I(e)},e.name))],y[s.DATE].length>0&&[a.jsx(b,{disabled:!0,divider:!0,children:a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[a.jsx(Z,{fontSize:"small",sx:{mr:1,color:x.palette.info.main}}),a.jsx(c,{variant:"body2",fontWeight:"medium",children:"Date Variables"})]})},"date-header"),...y[s.DATE].map(e=>a.jsx(b,{value:e.name,children:i?a.jsxs(a.Fragment,{children:[a.jsx(k,{checked:o(e.name)}),a.jsx(D,{primary:e.name})]}):I(e)},e.name))]]}):l.map(e=>a.jsx(b,{value:e.name,children:i?a.jsxs(a.Fragment,{children:[a.jsx(k,{checked:o(e.name)}),a.jsx(D,{primary:e.name})]}):I(e)},e.name))}),p&&a.jsx(ie,{children:p})]})}};export{Ae as V};
