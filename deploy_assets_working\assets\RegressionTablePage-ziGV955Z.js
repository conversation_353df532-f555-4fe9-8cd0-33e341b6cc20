import{j as r,B as o}from"./mui-libs-CfwFIaTD.js";import t from"./RegressionTable-BuRxA8Iu.js";import"./react-libs-Cr2nE3UY.js";import"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";import"./AddToResultsButton-BwSXKCt2.js";import"./PublicationReadyGate-BGFbKbJc.js";import"./regression-BKqSTW2N.js";import"./math-setup-BTRs7Kau.js";import"./math-lib-BOZ-XUok.js";import"./descriptive-Djo0s6H4.js";import"./coxRegressionService-CqIkroID.js";const d=()=>r.jsx(o,{sx:{p:3},children:r.jsx(t,{})});export{d as default};
