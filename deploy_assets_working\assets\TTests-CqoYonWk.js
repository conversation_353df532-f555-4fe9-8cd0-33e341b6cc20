import{u as sa,j as e,B as V,e as d,R as W,G as x,ai as q,b9 as w,ba as E,bb as f,aj as we,bc as Ee,ah as ia,f as na,X as Me,ae as la,g as Q,bp as ra,ad as ke,ao as _,ap as H,aq as U,ar as v,as as i,at as J,h as ae,bq as Ae,a2 as De,aF as ve,aP as Re,Z as oa,aX as da}from"./mui-libs-CfwFIaTD.js";import{r as T,b as ca}from"./react-libs-Cr2nE3UY.js";import{a as ua,D as le,e as ma,i as Be}from"./index-Bpan7Tbe.js";import{S as re}from"./StatsCard-op8tGQ0a.js";import{e as te,c as M,a as A,b as L}from"./descriptive-Djo0s6H4.js";import{o as ha,i as xa,p as pa}from"./t-tests-DXw1R1jD.js";import{p as fa}from"./anova-DbTY6dHK.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{w as Ge,m as ga}from"./non-parametric-Cf6Ds91x.js";import{c as Le}from"./normality-CwHD6Rjl.js";import{R as ja,B as va,C as ba,X as ya,Y as Sa,T as Va,a as Ta,b as Fa,E as Ia}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./math-lib-BOZ-XUok.js";const Oa=({initialTab:oe})=>{var ze,We;const{datasets:se,currentDataset:j,setCurrentDataset:Oe}=ua(),K=sa(),[Pe,_e]=T.useState((j==null?void 0:j.id)||""),[h,be]=T.useState(oe==="independent"?"independentSamples":"oneSample");ca.useEffect(()=>{oe==="independent"&&be("independentSamples")},[oe]);const[D,ye]=T.useState(""),[X,Se]=T.useState(""),[R,Ve]=T.useState(""),[O,Te]=T.useState(0),[P,He]=T.useState("twoSided"),[ee,Ue]=T.useState(.95),[k,Je]=T.useState(!1),[Y,de]=T.useState(""),[Z,ce]=T.useState(""),[Fe,Ke]=T.useState(!0),[ie,ue]=T.useState(!1),[Ie,me]=T.useState(null),[s,B]=T.useState(null),[r,G]=T.useState(null);T.useEffect(()=>{const a=localStorage.getItem(`ttest_results_${h}`),o=localStorage.getItem(`ttest_assumptions_${h}`);if(a)try{B(JSON.parse(a))}catch(p){console.error("Error parsing saved test results:",p)}if(o)try{G(JSON.parse(o))}catch(p){console.error("Error parsing saved assumption results:",p)}},[h]);const F=(j==null?void 0:j.columns.filter(a=>a.type===le.NUMERIC))||[],he=(j==null?void 0:j.columns.filter(a=>a.type===le.CATEGORICAL||a.type===le.ORDINAL||a.type===le.BOOLEAN))||[],ne=(()=>{if(!j||!R)return[];const a=j.columns.find(b=>b.id===R);if(!a)return[];const o=ma(j.data,a);return[...new Set(o)]})(),Xe=a=>{const o=a.target.value;_e(o),Ce();const p=se.find(b=>b.id===o);p&&Oe(p)},Ce=()=>{ye(""),Se(""),Ve(""),Te(0),de(""),ce(""),localStorage.removeItem(`ttest_results_${h}`),localStorage.removeItem(`ttest_assumptions_${h}`),B(null),G(null)},Ye=a=>{be(a.target.value),Ce()},xe=a=>{ye(a.target.value),B(null),G(null)},Ne=a=>{Se(a.target.value),B(null),G(null)},Ze=a=>{Ve(a.target.value),de(""),ce(""),B(null),G(null)},Qe=a=>{de(a.target.value),B(null),G(null)},ea=a=>{ce(a.target.value),B(null),G(null)},$e=()=>{if(!j)return!1;switch(h){case"oneSample":return!!D;case"independentSamples":return R?!!D&&!!Y&&!!Z&&Y!==Z:!!D&&!!X;case"pairedSamples":return!!D&&!!X;default:return!1}},aa=()=>{var a,o,p,b,C,N,fe,y;if(!j||!$e()){me("Please select all required variables for the selected test.");return}ue(!0),me(null),B(null),G(null);try{const g=j.columns.find(t=>t.id===D),z=X?j.columns.find(t=>t.id===X):null,l=R?j.columns.find(t=>t.id===R):null;if(!g)throw new Error("Selected variable not found in dataset.");let n=[],m=[],S=[];switch(h){case"oneSample":n=te(j.data,g),S=[g.name];break;case"independentSamples":if(R&&l){if(!Y||!Z)throw new Error("Please select both group values.");const c=j.data.filter(u=>String(u[l.name])===Y);n=te(c.map(u=>({[g.name]:u[g.name]})),g);const $=j.data.filter(u=>String(u[l.name])===Z);m=te($.map(u=>({[g.name]:u[g.name]})),g),S=[`${g.name} (${Y})`,`${g.name} (${Z})`]}else if(z)n=te(j.data,g),m=te(j.data,z),S=[g.name,z.name];else throw new Error("Invalid variable selection for independent samples t-test.");break;case"pairedSamples":if(!z)throw new Error("Please select both variables for paired samples t-test.");const t=j.data.map(c=>({value1:c[g.name],value2:c[z.name],row:c})).filter(c=>{const $=Be(c.value1,g),u=Be(c.value2,z);return!$.isMissing&&!u.isMissing&&typeof c.value1=="number"&&!isNaN(c.value1)&&typeof c.value2=="number"&&!isNaN(c.value2)});n=t.map(c=>c.value1),m=t.map(c=>c.value2),S=[g.name,z.name];break}if(n.length===0)throw new Error("No valid numeric data found for the selected variable(s).");if((h==="independentSamples"||h==="pairedSamples")&&m.length===0)throw new Error("No valid numeric data found for the second variable/group.");if(Fe){const t={valid:!0,details:{}},c=Le(n,.05,["auto"]);if(t.details.normality=[{group:S[0],isNormal:c.overallAssessment.isNormal,pValue:((a=c.tests.shapiroWilk)==null?void 0:a.pValue)||((o=c.tests.kolmogorovSmirnov)==null?void 0:o.pValue)||((p=c.tests.jarqueBera)==null?void 0:p.pValue)||NaN}],h!=="oneSample"){const u=Le(m,.05,["auto"]);t.details.normality.push({group:S[1],isNormal:u.overallAssessment.isNormal,pValue:((b=u.tests.shapiroWilk)==null?void 0:b.pValue)||((C=u.tests.kolmogorovSmirnov)==null?void 0:C.pValue)||((N=u.tests.jarqueBera)==null?void 0:N.pValue)||NaN}),t.details.normalityValid=c.overallAssessment.isNormal&&u.overallAssessment.isNormal,t.valid=t.valid&&t.details.normalityValid}else t.details.normalityValid=c.overallAssessment.isNormal,t.valid=t.valid&&t.details.normalityValid;t.details.sampleSize=[{group:S[0],size:n.length,sufficient:n.length>=30}],h!=="oneSample"&&t.details.sampleSize.push({group:S[1],size:m.length,sufficient:m.length>=30});const $=t.details.sampleSize.every(u=>u.sufficient);if(h==="independentSamples")try{const u=fa(n,m);t.details.equalVariances={levene:{statistic:u.statistic,pValue:u.pValue,df1:u.df?u.df[0]:null,df2:u.df?u.df[1]:null,rejected:u.rejected,alpha:u.alpha,method:u.method,variant:u.variant},isEqual:!u.rejected},t.valid=t.valid&&t.details.equalVariances.isEqual}catch(u){const ge=M(n)**2,je=M(m)**2,qe=Math.max(ge,je)/Math.min(ge,je);t.details.equalVariances={var1:ge,var2:je,ratio:qe,isEqual:qe<4,error:u instanceof Error?u.message:"Error performing Levene's test"},t.valid=t.valid&&t.details.equalVariances.isEqual}t.valid=t.valid||$,G(t),localStorage.setItem(`ttest_assumptions_${h}`,JSON.stringify(t))}let I={};if(h==="oneSample")if(k){const t=Ge(n,n.map(()=>O));I={test:"Wilcoxon Signed Rank Test (One Sample)",statistic:t.W,statName:"W",pValue:t.pValue,n:t.n,effectSize:null,median:A(n),testValue:O,groups:[{name:S[0],n:n.length,mean:L(n),median:A(n),sd:M(n)}],confidenceInterval:null,alternative:P}}else{const t=ha(n,O),c=Math.abs(t.mean-O)/t.se;I={test:"One-Sample t-Test",statistic:t.t,statName:"t",df:t.df,pValue:t.pValue,effectSize:c,mean:t.mean,standardError:t.se,testValue:O,groups:[{name:S[0],n:n.length,mean:t.mean,median:A(n),sd:M(n)}],confidenceInterval:t.ci95,alternative:P}}else if(h==="independentSamples")if(k){const t=ga(n,m),c=t.n1+t.n2,$=Math.abs(t.z)/Math.sqrt(c);I={test:"Mann-Whitney U Test",statistic:t.U,statName:"U",pValue:t.pValue,effectSize:$,groups:[{name:S[0],n:t.n1,mean:L(n),median:A(n),sd:M(n)},{name:S[1],n:t.n2,mean:L(m),median:A(m),sd:M(m)}],confidenceInterval:null,alternative:P}}else{const t=xa(n,m);let c=null;if((y=(fe=r==null?void 0:r.details)==null?void 0:fe.equalVariances)!=null&&y.levene&&!r.details.equalVariances.error){const $=r.details.equalVariances.levene;c={columns:["Test","F","df1","df2","p-value","Equal Variances"],rows:[["Levene's Test",$.statistic,$.df1,$.df2,$.pValue,$.rejected?"No":"Yes"]]}}I={test:"Independent Samples t-Test",statistic:t.t,statName:"t",df:t.df,pValue:t.pValue,effectSize:t.cohensD,meanDifference:t.meanDifference,standardError:t.se,groups:[{name:S[0],n:n.length,mean:L(n),median:A(n),sd:M(n)},{name:S[1],n:m.length,mean:L(m),median:A(m),sd:M(m)}],confidenceInterval:t.ci,alternative:P,homogeneityTestsTable:c}}else if(h==="pairedSamples")if(k){const t=Ge(n,m),c=Math.abs(t.z)/Math.sqrt(t.n);I={test:"Wilcoxon Signed Rank Test (Paired)",statistic:t.W,statName:"W",pValue:t.pValue,effectSize:c,groups:[{name:S[0],n:n.length,mean:L(n),median:A(n),sd:M(n)},{name:S[1],n:m.length,mean:L(m),median:A(m),sd:M(m)}],pairs:n.length,confidenceInterval:null,alternative:P}}else{const t=pa(n,m);I={test:"Paired Samples t-Test",statistic:t.t,statName:"t",df:t.df,pValue:t.pValue,effectSize:t.cohensD,meanDifference:t.meanDifference,standardError:t.se,groups:[{name:S[0],n:n.length,mean:L(n),median:A(n),sd:M(n)},{name:S[1],n:m.length,mean:L(m),median:A(m),sd:M(m)}],pairs:n.length,confidenceInterval:t.ci95,alternative:P}}h==="oneSample"?I.chartData=[{name:"Sample",value:I.groups[0].mean,error:I.standardError?I.standardError*1.96:null},{name:"Test Value",value:O}]:I.chartData=I.groups.map(t=>({name:t.name,value:t.mean,error:t.sd/Math.sqrt(t.n)*1.96})),I.interpretation=ta(I),B(I),localStorage.setItem(`ttest_results_${h}`,JSON.stringify(I)),ue(!1)}catch(g){me(`Error running test: ${g instanceof Error?g.message:String(g)}`),ue(!1)}},ta=a=>{if(!a)return"";const{test:o,pValue:p,statistic:b,df:C,effectSize:N,alternative:fe}=a;let y="";const g=p<.05;if(o.includes("One-Sample")){const{mean:l,testValue:n}=a,m=l>n?"greater than":l<n?"less than":"equal to";y=`The ${o} was conducted to determine if the sample mean (${l.toFixed(2)}) is significantly different from the test value (${n}).`,g?y+=` The results show a statistically significant difference (${a.statName} = ${b.toFixed(2)}, ${C?`df = ${C}, `:""}p = ${p<.001?"< 0.001":p.toFixed(3)}). The sample mean is ${m} the test value.`:y+=` The results show no statistically significant difference (${a.statName} = ${b.toFixed(2)}, ${C?`df = ${C}, `:""}p = ${p.toFixed(3)}). We cannot reject the null hypothesis that the population mean is equal to ${n}.`}else if(o.includes("Independent Samples")){const{groups:l,meanDifference:n}=a;Math.abs(n||l[0].mean-l[1].mean),y=`The ${o} was conducted to compare ${l[0].name} (M = ${l[0].mean.toFixed(2)}, SD = ${l[0].sd.toFixed(2)}) and ${l[1].name} (M = ${l[1].mean.toFixed(2)}, SD = ${l[1].sd.toFixed(2)}).`,g?y+=` The results show a statistically significant difference (${a.statName} = ${b.toFixed(2)}, ${C?`df = ${C}, `:""}p = ${p<.001?"< 0.001":p.toFixed(3)}).`:y+=` The results show no statistically significant difference (${a.statName} = ${b.toFixed(2)}, ${C?`df = ${C}, `:""}p = ${p.toFixed(3)}).`}else if(o.includes("Paired")){const{groups:l,meanDifference:n}=a;y=`The ${o} was conducted to compare ${l[0].name} (M = ${l[0].mean.toFixed(2)}, SD = ${l[0].sd.toFixed(2)}) and ${l[1].name} (M = ${l[1].mean.toFixed(2)}, SD = ${l[1].sd.toFixed(2)}) in a within-subjects design with ${a.pairs} paired observations.`,g?y+=` The results show a statistically significant difference (${a.statName} = ${b.toFixed(2)}, ${C?`df = ${C}, `:""}p = ${p<.001?"< 0.001":p.toFixed(3)}).`:y+=` The results show no statistically significant difference (${a.statName} = ${b.toFixed(2)}, ${C?`df = ${C}, `:""}p = ${p.toFixed(3)}).`}if(N!=null){let l;o.includes("t-Test")?(N<.2?l="very small":N<.5?l="small":N<.8?l="medium":l="large",y+=` The effect size (Cohen's d = ${N.toFixed(2)}) indicates a ${l} effect.`):(o.includes("Mann-Whitney")||o.includes("Wilcoxon"))&&(N<.1?l="very small":N<.3?l="small":N<.5?l="medium":l="large",y+=` The effect size (r = ${N.toFixed(2)}) indicates a ${l} effect.`)}const z=a.confidenceInterval;if(Array.isArray(z)&&z.length>=2){const l=z[0],n=z[1];typeof l=="number"&&!isNaN(l)&&typeof n=="number"&&!isNaN(n)&&(y+=` The 95% confidence interval of the difference is [${l.toFixed(2)}, ${n.toFixed(2)}].`,l<=0&&n>=0?y+=" Since the confidence interval includes zero, this supports the non-significant result.":y+=" Since the confidence interval does not include zero, this supports the significant result.")}return y},pe=(a,o)=>o.includes("Cohen")?a<.2?"Very Small":a<.5?"Small":a<.8?"Medium":"Large":a<.1?"Very Small":a<.3?"Small":a<.5?"Medium":"Large";return e.jsxs(V,{p:3,children:[e.jsx(d,{variant:"h5",gutterBottom:!0,children:"T-Tests and Alternatives"}),e.jsxs(W,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(d,{variant:"subtitle1",gutterBottom:!0,children:"Test Selection"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"dataset-select-label",children:"Dataset"}),e.jsx(E,{labelId:"dataset-select-label",id:"dataset-select",value:Pe,label:"Dataset",onChange:Xe,disabled:se.length===0,children:se.length===0?e.jsx(f,{value:"",disabled:!0,children:"No datasets available"}):se.map(a=>e.jsxs(f,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"test-type-label",children:"Test Type"}),e.jsxs(E,{labelId:"test-type-label",id:"test-type",value:h,label:"Test Type",onChange:Ye,children:[e.jsxs(f,{value:"oneSample",children:["One-Sample T-Test",e.jsx(d,{variant:"caption",display:"block",color:"text.secondary",children:"Compare one variable to a hypothesized value"})]}),e.jsxs(f,{value:"independentSamples",children:["Independent Samples T-Test",e.jsx(d,{variant:"caption",display:"block",color:"text.secondary",children:"Compare two unrelated groups"})]}),e.jsxs(f,{value:"pairedSamples",children:["Paired Samples T-Test",e.jsx(d,{variant:"caption",display:"block",color:"text.secondary",children:"Compare two related measurements"})]})]})]})})]}),e.jsx(V,{mt:2,children:e.jsx(we,{control:e.jsx(Ee,{checked:k,onChange:a=>Je(a.target.checked)}),label:e.jsxs(V,{children:["Use non-parametric alternative",e.jsxs(d,{variant:"caption",display:"block",color:"text.secondary",children:[h==="oneSample"&&"Wilcoxon Signed Rank Test (One Sample)",h==="independentSamples"&&"Mann-Whitney U Test",h==="pairedSamples"&&"Wilcoxon Signed Rank Test (Paired)"]})]})})})]}),e.jsxs(W,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(d,{variant:"subtitle1",gutterBottom:!0,children:"Variable Selection"}),e.jsxs(x,{container:!0,spacing:2,children:[h==="oneSample"&&e.jsxs(e.Fragment,{children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"variable1-label",children:"Variable"}),e.jsx(E,{labelId:"variable1-label",id:"variable1",value:D,label:"Variable",onChange:xe,disabled:F.length===0,children:F.length===0?e.jsx(f,{value:"",disabled:!0,children:"No numeric variables available"}):F.map(a=>e.jsx(f,{value:a.id,children:a.name},a.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsx(ia,{label:"Test Value (Hypothesized Mean)",type:"number",value:O,onChange:a=>Te(Number(a.target.value)),fullWidth:!0,margin:"normal"})})]}),h==="independentSamples"&&e.jsxs(e.Fragment,{children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"variable1-label",children:"Variable"}),e.jsx(E,{labelId:"variable1-label",id:"variable1",value:D,label:"Variable",onChange:xe,disabled:F.length===0,children:F.length===0?e.jsx(f,{value:"",disabled:!0,children:"No numeric variables available"}):F.map(a=>e.jsx(f,{value:a.id,children:a.name},a.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"grouping-label",children:"Grouping Variable"}),e.jsxs(E,{labelId:"grouping-label",id:"grouping",value:R,label:"Grouping Variable",onChange:Ze,disabled:he.length===0,children:[e.jsx(f,{value:"",children:e.jsx("em",{children:"None (use two separate variables)"})}),he.map(a=>e.jsx(f,{value:a.id,children:a.name},a.id))]})]})}),R?e.jsxs(e.Fragment,{children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"group1-label",children:"Group 1"}),e.jsx(E,{labelId:"group1-label",id:"group1",value:Y,label:"Group 1",onChange:Qe,disabled:ne.length===0,children:ne.map(a=>e.jsx(f,{value:a,children:a},a))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"group2-label",children:"Group 2"}),e.jsx(E,{labelId:"group2-label",id:"group2",value:Z,label:"Group 2",onChange:ea,disabled:ne.length===0,children:ne.map(a=>e.jsx(f,{value:a,children:a},a))})]})})]}):e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"variable2-label",children:"Second Variable"}),e.jsx(E,{labelId:"variable2-label",id:"variable2",value:X,label:"Second Variable",onChange:Ne,disabled:F.length===0,children:F.length===0?e.jsx(f,{value:"",disabled:!0,children:"No numeric variables available"}):F.map(a=>e.jsx(f,{value:a.id,children:a.name},a.id))})]})})]}),h==="pairedSamples"&&e.jsxs(e.Fragment,{children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"variable1-label",children:"First Variable"}),e.jsx(E,{labelId:"variable1-label",id:"variable1",value:D,label:"First Variable",onChange:xe,disabled:F.length===0,children:F.length===0?e.jsx(f,{value:"",disabled:!0,children:"No numeric variables available"}):F.map(a=>e.jsx(f,{value:a.id,children:a.name},a.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"variable2-label",children:"Second Variable"}),e.jsx(E,{labelId:"variable2-label",id:"variable2",value:X,label:"Second Variable",onChange:Ne,disabled:F.length===0,children:F.length===0?e.jsx(f,{value:"",disabled:!0,children:"No numeric variables available"}):F.map(a=>e.jsx(f,{value:a.id,children:a.name},a.id))})]})})]})]}),e.jsx(V,{mt:2,children:e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"alternative-label",children:"Alternative Hypothesis"}),e.jsxs(E,{labelId:"alternative-label",id:"alternative",value:P,label:"Alternative Hypothesis",onChange:a=>He(a.target.value),children:[e.jsx(f,{value:"twoSided",children:"Two-Sided (≠)"}),e.jsx(f,{value:"less",children:"One-Sided (<)"}),e.jsx(f,{value:"greater",children:"One-Sided (>)"})]})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(q,{fullWidth:!0,margin:"normal",children:[e.jsx(w,{id:"confidence-label",children:"Confidence Level"}),e.jsxs(E,{labelId:"confidence-label",id:"confidence",value:ee,label:"Confidence Level",onChange:a=>Ue(Number(a.target.value)),children:[e.jsx(f,{value:.9,children:"90%"}),e.jsx(f,{value:.95,children:"95%"}),e.jsx(f,{value:.99,children:"99%"})]})]})})]})}),e.jsxs(V,{mt:3,children:[e.jsx(d,{variant:"subtitle2",gutterBottom:!0,children:"Options"}),e.jsx(we,{control:e.jsx(Ee,{checked:Fe,onChange:a=>Ke(a.target.checked)}),label:"Check test assumptions"})]}),e.jsx(V,{mt:2,children:e.jsxs(na,{variant:"contained",color:"primary",startIcon:e.jsx(Me,{}),onClick:aa,disabled:ie||!$e(),children:["Run ",k?"Non-parametric Test":"T-Test"]})})]}),ie&&e.jsx(V,{display:"flex",justifyContent:"center",my:4,children:e.jsx(la,{})}),Ie&&e.jsx(Q,{severity:"error",sx:{mb:3},children:Ie}),r&&!ie&&e.jsxs(W,{elevation:2,sx:{p:3,mb:3,bgcolor:"background.paper"},children:[e.jsxs(d,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:3},children:[e.jsx(ra,{color:"primary"}),"Assumption Checks"]}),e.jsxs(x,{container:!0,spacing:3,children:[e.jsx(x,{item:!0,xs:12,lg:6,children:e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:2,height:"fit-content"},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(ke,{fontSize:"small"}),"Normality Assumption"]}),e.jsx(_,{children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsxs(v,{children:[e.jsx(i,{children:"Group"}),e.jsx(i,{align:"right",children:"p-value"}),e.jsx(i,{align:"right",children:"Status"})]})}),e.jsx(J,{children:r.details.normality.map((a,o)=>e.jsxs(v,{sx:{"&:nth-of-type(odd)":{bgcolor:"grey.25"}},children:[e.jsx(i,{sx:{fontWeight:500},children:a.group}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:a.pValue.toFixed(4)}),e.jsx(i,{align:"right",children:e.jsx(ae,{label:a.isNormal?"Normal":"Non-normal",color:a.isNormal?"success":"error",size:"small",variant:"outlined"})})]},o))})]})}),e.jsx(Q,{severity:r.details.normalityValid?"success":"warning",sx:{mt:2},children:e.jsx(d,{variant:"body2",children:r.details.normalityValid?"Normality assumption is met for all groups.":`Normality assumption is violated. Consider using the non-parametric alternative: ${h==="oneSample"?"Wilcoxon Signed Rank Test":h==="independentSamples"?"Mann-Whitney U Test":"Wilcoxon Signed Rank Test for paired samples"}.`})})]})}),e.jsx(x,{item:!0,xs:12,lg:6,children:e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:2,height:"fit-content"},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Ae,{fontSize:"small"}),"Sample Size Check"]}),e.jsx(_,{children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsxs(v,{children:[e.jsx(i,{children:"Group"}),e.jsx(i,{align:"right",children:"Size"}),e.jsx(i,{align:"right",children:"Status"})]})}),e.jsx(J,{children:r.details.sampleSize.map((a,o)=>e.jsxs(v,{sx:{"&:nth-of-type(odd)":{bgcolor:"grey.25"}},children:[e.jsx(i,{sx:{fontWeight:500},children:a.group}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:a.size}),e.jsx(i,{align:"right",children:e.jsx(ae,{label:a.sufficient?"Sufficient":"Small",color:a.sufficient?"success":"warning",size:"small",variant:"outlined"})})]},o))})]})}),e.jsx(Q,{severity:r.details.sampleSize.every(a=>a.sufficient)?"success":"info",sx:{mt:2},children:e.jsx(d,{variant:"body2",children:r.details.sampleSize.every(a=>a.sufficient)?"Sample sizes are sufficient (n ≥ 30) for the t-test to be robust against normality violations.":"Small sample size detected (n < 30). The t-test requires normality assumption to be met with small samples."})})]})}),h==="independentSamples"&&r.details.equalVariances&&e.jsx(x,{item:!0,xs:12,children:e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(De,{fontSize:"small"}),"Homogeneity of Variances (Levene's Test)"]}),e.jsxs(d,{variant:"caption",display:"block",gutterBottom:!0,sx:{mb:2,color:"text.secondary"},children:["For Dependent Variable: ",((ze=F.find(a=>a.id===D))==null?void 0:ze.name)||"N/A"," (across groups of ",((We=he.find(a=>a.id===R))==null?void 0:We.name)||"N/A",")"]}),r.details.equalVariances.levene?e.jsxs(e.Fragment,{children:[e.jsx(_,{children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsxs(v,{children:[e.jsx(i,{children:"Test"}),e.jsx(i,{align:"right",children:"F"}),e.jsx(i,{align:"right",children:"df1"}),e.jsx(i,{align:"right",children:"df2"}),e.jsx(i,{align:"right",children:"p-value"})]})}),e.jsx(J,{children:e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"Levene's Test"}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:r.details.equalVariances.levene.statistic.toFixed(3)}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:r.details.equalVariances.levene.df1}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:r.details.equalVariances.levene.df2}),e.jsx(i,{align:"right",sx:{color:r.details.equalVariances.levene.pValue<1-ee?"error.main":"success.main",fontFamily:"monospace",fontWeight:500},children:r.details.equalVariances.levene.pValue<.001?"< 0.001":r.details.equalVariances.levene.pValue.toFixed(3)})]})})]})}),e.jsx(Q,{severity:r.details.equalVariances.levene.pValue>=1-ee?"success":"warning",sx:{mt:2},children:e.jsxs(d,{variant:"body2",children:["Levene's test for homogeneity of variances (α = ",(1-ee).toFixed(2),"): F(",r.details.equalVariances.levene.df1,", ",r.details.equalVariances.levene.df2,") = ",r.details.equalVariances.levene.statistic.toFixed(3),", p = ",r.details.equalVariances.levene.pValue<.001?"< 0.001":r.details.equalVariances.levene.pValue.toFixed(3),". Assumption of equal variances is ",e.jsx("strong",{children:r.details.equalVariances.levene.pValue>=1-ee?"met":"not met"}),"."]})})]}):e.jsxs(e.Fragment,{children:[e.jsx(_,{children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsxs(v,{children:[e.jsx(i,{children:"Measure"}),e.jsx(i,{align:"right",children:"Value"})]})}),e.jsxs(J,{children:[e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"Variance Ratio"}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:typeof r.details.equalVariances.ratio=="number"?r.details.equalVariances.ratio.toFixed(4):"N/A"})]}),e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"Status"}),e.jsx(i,{align:"right",children:e.jsx(ae,{label:r.details.equalVariances.isEqual?"Approximately Equal":"Unequal",color:r.details.equalVariances.isEqual?"success":"error",size:"small",variant:"outlined"})})]})]})]})}),e.jsx(Q,{severity:r.details.equalVariances.isEqual?"success":"warning",sx:{mt:2},children:e.jsxs(d,{variant:"body2",children:["Variance ratio test: The variances are ",r.details.equalVariances.isEqual?"approximately equal":"unequal"," (ratio < 4 rule of thumb)."]})})]})]})}),e.jsx(x,{item:!0,xs:12,children:e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:r.valid?"success.50":"warning.50"},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ve,{fontSize:"small"}),"Overall Assessment"]}),e.jsx(Q,{severity:r.valid?"success":"warning",sx:{mt:1},children:e.jsx(d,{variant:"body2",children:r.valid?"All assumptions required for the selected test are adequately met.":`Some assumptions are violated. The ${k?"non-parametric test":"t-test"} may not be appropriate. ${k?"":"Consider using a non-parametric alternative."}`})})]})})]})]}),s&&!ie&&e.jsxs(W,{elevation:2,sx:{p:3,mb:3,bgcolor:"background.paper"},children:[e.jsx(V,{display:"flex",justifyContent:"flex-start",alignItems:"center",mb:3,children:e.jsxs(d,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Me,{color:"primary"}),s.test," Results"]})}),e.jsxs(V,{sx:{mb:3},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Re,{fontSize:"small"}),"Key Statistics"]}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(re,{title:`${s.statName} Statistic`,value:s.statistic.toFixed(4),description:"Test statistic value",color:"primary",variant:"outlined",icon:e.jsx(Re,{})})}),e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(re,{title:"p-value",value:s.pValue<.001?"< 0.001":s.pValue.toFixed(4),description:s.pValue<.05?"Statistically significant":"Not significant",color:s.pValue<.05?"success":"warning",variant:"outlined",icon:e.jsx(ke,{})})}),s.df!==void 0&&e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(re,{title:"Degrees of Freedom",value:s.df,description:"Statistical degrees of freedom",color:"info",variant:"outlined",icon:e.jsx(oa,{})})}),s.effectSize!==null&&s.effectSize!==void 0&&e.jsx(x,{item:!0,xs:12,sm:6,md:3,children:e.jsx(re,{title:`Effect Size ${s.test.includes("t-Test")?"(Cohen's d)":"(r)"}`,value:s.effectSize.toFixed(4),description:pe(s.effectSize,s.test.includes("t-Test")?"Cohen's d":"r"),color:"secondary",variant:"outlined",icon:e.jsx(da,{})})})]})]}),e.jsxs(x,{container:!0,spacing:3,children:[e.jsx(x,{item:!0,xs:12,lg:6,children:e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:2,height:"fit-content"},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(ve,{fontSize:"small"}),"Test Statistics"]}),e.jsx(_,{children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsxs(v,{children:[e.jsx(i,{children:"Statistic"}),e.jsx(i,{align:"right",children:"Value"})]})}),e.jsxs(J,{children:[e.jsxs(v,{children:[e.jsxs(i,{sx:{fontWeight:500},children:[s.statName," Value"]}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:s.statistic.toFixed(4)})]}),s.df!==void 0&&e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"Degrees of Freedom"}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:s.df})]}),e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"p-value"}),e.jsx(i,{align:"right",sx:{color:s.pValue<.05?"success.main":"text.primary",fontWeight:s.pValue<.05?600:400,fontFamily:"monospace"},children:s.pValue<.001?"< 0.001":s.pValue.toFixed(4)})]}),s.meanDifference!==void 0&&e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"Mean Difference"}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:s.meanDifference.toFixed(4)})]}),s.standardError!==void 0&&e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"Standard Error"}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:s.standardError.toFixed(4)})]}),Array.isArray(s.confidenceInterval)&&s.confidenceInterval.length>=2&&typeof s.confidenceInterval[0]=="number"&&!isNaN(s.confidenceInterval[0])&&typeof s.confidenceInterval[1]=="number"&&!isNaN(s.confidenceInterval[1])&&e.jsxs(v,{children:[e.jsx(i,{sx:{fontWeight:500},children:"95% Confidence Interval"}),e.jsxs(i,{align:"right",sx:{fontFamily:"monospace"},children:["[",s.confidenceInterval[0].toFixed(4),", ",s.confidenceInterval[1].toFixed(4),"]"]})]})]})]})})]})}),e.jsx(x,{item:!0,xs:12,lg:6,children:e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:2,height:"fit-content"},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(Ae,{fontSize:"small"}),"Descriptive Statistics"]}),e.jsx(_,{sx:{mb:2},children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsxs(v,{children:[e.jsx(i,{children:"Group"}),e.jsx(i,{align:"right",children:"N"}),e.jsx(i,{align:"right",children:"Mean"}),e.jsx(i,{align:"right",children:k?"Median":"SD"})]})}),e.jsxs(J,{children:[s.groups.map((a,o)=>e.jsxs(v,{sx:{"&:nth-of-type(odd)":{bgcolor:"grey.25"}},children:[e.jsx(i,{sx:{fontWeight:500},children:a.name}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:a.n}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:a.mean.toFixed(4)}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:k?a.median.toFixed(4):a.sd.toFixed(4)})]},o)),h==="oneSample"&&e.jsxs(v,{sx:{bgcolor:"primary.50","& .MuiTableCell-root":{fontWeight:500}},children:[e.jsx(i,{children:"Test Value"}),e.jsx(i,{align:"right",children:"-"}),e.jsx(i,{align:"right",sx:{fontFamily:"monospace"},children:s.testValue}),e.jsx(i,{align:"right",children:"-"})]})]})]})}),e.jsxs(V,{sx:{height:220,mt:2},children:[e.jsx(d,{variant:"caption",color:"text.secondary",sx:{mb:1,display:"block"},children:"Group Comparison Chart"}),e.jsx(ja,{width:"100%",height:"100%",children:e.jsxs(va,{data:s.chartData,margin:{top:20,right:30,left:20,bottom:40},children:[e.jsx(ba,{strokeDasharray:"3 3",stroke:K.palette.grey[300]}),e.jsx(ya,{dataKey:"name",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(Sa,{tick:{fontSize:12}}),e.jsx(Va,{contentStyle:{backgroundColor:K.palette.background.paper,border:`1px solid ${K.palette.grey[300]}`,borderRadius:4}}),e.jsxs(Ta,{dataKey:"value",name:k?"Median":"Mean",radius:[4,4,0,0],children:[s.chartData.map((a,o)=>e.jsx(Fa,{fill:o%2===0?K.palette.primary.main:K.palette.secondary.main},`cell-${o}`)),!k&&e.jsx(Ia,{dataKey:"error",width:4,strokeWidth:2,stroke:K.palette.error.main})]})]})})]})]})})]}),h==="independentSamples"&&s.homogeneityTestsTable&&e.jsxs(V,{sx:{mt:3},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(De,{fontSize:"small"}),"Homogeneity of Variances Test"]}),e.jsx(W,{elevation:0,variant:"outlined",sx:{p:2},children:e.jsx(_,{children:e.jsxs(H,{size:"small",sx:{"& .MuiTableCell-head":{bgcolor:"grey.50",fontWeight:600}},children:[e.jsx(U,{children:e.jsx(v,{children:s.homogeneityTestsTable.columns.map((a,o)=>e.jsx(i,{align:o===0?"left":"right",children:a},o))})}),e.jsx(J,{children:s.homogeneityTestsTable.rows.map((a,o)=>e.jsx(v,{children:a.map((p,b)=>e.jsx(i,{align:b===0?"left":"right",sx:{fontFamily:b>0?"monospace":"inherit",fontWeight:b===0?500:400},children:typeof p=="number"?p.toFixed(4):p},b))},o))})]})})})]}),e.jsxs(V,{sx:{mt:3},children:[e.jsxs(d,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(ve,{fontSize:"small"}),"Statistical Interpretation"]}),e.jsxs(W,{elevation:0,variant:"outlined",sx:{p:3,bgcolor:"grey.50"},children:[e.jsxs(V,{sx:{mb:2},children:[e.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Summary"}),e.jsx(d,{variant:"body2",sx:{lineHeight:1.6},children:s.interpretation})]}),e.jsxs(V,{sx:{mb:2},children:[e.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Statistical Significance"}),e.jsxs(V,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[e.jsx(ae,{label:s.pValue<.05?"Significant":"Not Significant",color:s.pValue<.05?"success":"default",size:"small"}),e.jsx(d,{variant:"body2",color:"text.secondary",children:"(α = 0.05)"})]}),e.jsx(d,{variant:"body2",sx:{lineHeight:1.6},children:s.pValue<.05?`The p-value (${s.pValue<.001?"< 0.001":s.pValue.toFixed(4)}) is less than the significance level of 0.05, indicating a statistically significant result.`:`The p-value (${s.pValue.toFixed(4)}) is greater than the significance level of 0.05, indicating no statistically significant difference.`})]}),s.effectSize!==null&&s.effectSize!==void 0&&e.jsxs(V,{sx:{mb:2},children:[e.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Effect Size"}),e.jsxs(V,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[e.jsx(ae,{label:pe(s.effectSize,s.test.includes("t-Test")?"Cohen's d":"r"),color:"secondary",size:"small"}),e.jsxs(d,{variant:"body2",color:"text.secondary",children:["(",s.test.includes("t-Test")?"Cohen's d":"r"," = ",s.effectSize.toFixed(4),")"]})]}),e.jsxs(d,{variant:"body2",sx:{lineHeight:1.6},children:["The effect size indicates a ",pe(s.effectSize,s.test.includes("t-Test")?"Cohen's d":"r").toLowerCase()," practical significance of the observed difference."]})]}),Array.isArray(s.confidenceInterval)&&s.confidenceInterval.length>=2&&typeof s.confidenceInterval[0]=="number"&&!isNaN(s.confidenceInterval[0])&&typeof s.confidenceInterval[1]=="number"&&!isNaN(s.confidenceInterval[1])&&e.jsxs(V,{children:[e.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"primary",children:"Confidence Interval"}),e.jsxs(d,{variant:"body2",sx:{lineHeight:1.6},children:["The 95% confidence interval [",s.confidenceInterval[0].toFixed(4),", ",s.confidenceInterval[1].toFixed(4),"]",s.confidenceInterval[0]<=0&&s.confidenceInterval[1]>=0?" includes zero, which supports the non-significant result.":" does not include zero, which supports the significant result."]})]})]})]})]})]})};export{Oa as default};
