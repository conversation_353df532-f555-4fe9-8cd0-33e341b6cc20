import{u as C,j as a,C as v,R as p,i,e as o,B as t,ah as S,am as I,d as u,I as j,aC as w,aD as A,a3 as W,G as f,k as O,bz as z,aE as B,aF as D,h as F,l as E,f as L,bA as P,O as R,ad as k,bt as M,a0 as G,J as T,W as $,aP as Q,X as V,N as X}from"./mui-libs-CfwFIaTD.js";import{r as h,b as H}from"./react-libs-Cr2nE3UY.js";import{b as J}from"./index-Bpan7Tbe.js";import{d as N}from"./DescriptiveStatsOptions-MCsgZr0u.js";import{i as Y}from"./InferentialStatsOptions-Cv31a_py.js";import{c as q}from"./CorrelationAnalysisOptions-X530BGye.js";import{a as K}from"./AdvancedAnalysisOptions-BMl4jxNG.js";import{e as U}from"./EpiCalcOptions-RMQOqOWt.js";import{sampleSizeCalculatorOptions as Z}from"./SampleSizeCalculatorsOptions-DlONI60r.js";import{d as _}from"./DataVisualizationOptions-B6r9UVDP.js";import{d as aa}from"./DataManagementOptions-BATjkCpy.js";import{p as ea}from"./PublicationReadyOptions-BG4CsEaB.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const ya=({onNavigate:b})=>{const r=C(),[n,y]=h.useState(""),{canAccessProFeatures:m}=J(),l=h.useMemo(()=>{const s=[{name:"Data Management",icon:a.jsx(R,{}),color:"#795548",options:aa},{name:"Descriptive Statistics",icon:a.jsx(k,{}),color:"#4CAF50",options:N},{name:"Inferential Statistics",icon:a.jsx(M,{}),color:"#2196F3",options:Y},{name:"Correlation Analysis",icon:a.jsx(G,{}),color:"#FF9800",options:q},{name:"Advanced Analysis",icon:a.jsx(T,{}),color:"#9C27B0",options:K},{name:"Data Visualization",icon:a.jsx($,{}),color:"#00BCD4",options:_},{name:"Sample Size Calculator",icon:a.jsx(Q,{}),color:"#FF5722",options:Z},{name:"Epi Calculator",icon:a.jsx(V,{}),color:"#607D8B",options:U},{name:"Publication Ready",icon:a.jsx(X,{}),color:"#3F51B5",options:ea}];return n.trim()?s.map(e=>({...e,options:e.options.filter(d=>d.name.toLowerCase().includes(n.toLowerCase())||d.shortDescription.toLowerCase().includes(n.toLowerCase())||d.detailedDescription.toLowerCase().includes(n.toLowerCase()))})).filter(e=>e.options.length>0):s},[n]),[c,x]=h.useState(new Set),g=s=>{const e=new Set(c);e.has(s)?e.delete(s):e.add(s),x(e)};return H.useEffect(()=>{const s=l.map(e=>e.name);x(new Set(s))},[l]),a.jsxs(v,{maxWidth:"lg",sx:{py:4},children:[a.jsxs(p,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${i(r.palette.primary.main,.1)} 0%, ${i(r.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[a.jsx(o,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Analysis Index"}),a.jsx(o,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Browse and launch all available statistical analysis and data management tools"}),a.jsx(o,{variant:"body1",color:"text.secondary",children:"Find the right tool for your data analysis needs by exploring our comprehensive index of statistical tests, calculators, visualization options, and data management utilities."})]}),a.jsx(t,{sx:{mb:2},children:a.jsx(S,{label:"Search Analyses",variant:"outlined",fullWidth:!0,value:n,onChange:s=>y(s.target.value),InputProps:{startAdornment:a.jsx(I,{color:"action"})}})}),l.map(s=>a.jsxs(t,{sx:{mb:4},children:[a.jsx(p,{elevation:1,sx:{p:2,mb:2,backgroundColor:i(s.color,.05),borderLeft:`4px solid ${s.color}`,cursor:"pointer",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:i(s.color,.1),transform:"translateX(4px)"}},onClick:()=>g(s.name),children:a.jsxs(t,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[a.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:2},children:[a.jsx(u,{sx:{bgcolor:s.color,width:40,height:40},children:s.icon}),a.jsxs(t,{children:[a.jsx(o,{variant:"h5",fontWeight:"bold",color:s.color,children:s.name}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:[s.options.length," analysis",s.options.length!==1?"es":""," available"]})]})]}),a.jsx(j,{size:"small",sx:{color:s.color},children:c.has(s.name)?a.jsx(w,{}):a.jsx(A,{})})]})}),a.jsx(W,{in:c.has(s.name),timeout:"auto",unmountOnExit:!0,children:a.jsx(f,{container:!0,spacing:3,children:s.options.map(e=>a.jsx(f,{item:!0,xs:12,md:6,lg:4,children:a.jsxs(O,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:r.shadows[8],"& .launch-button":{backgroundColor:e.color,color:"white"}}},children:[a.jsx(z,{avatar:a.jsx(u,{sx:{bgcolor:e.color,width:48,height:48},children:e.icon}),title:a.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[a.jsx(o,{variant:"h6",fontWeight:"bold",children:e.name}),(e.path.startsWith("advanced-analysis")||e.path.startsWith("publication-ready"))&&!m&&a.jsx(t,{sx:{ml:1,bgcolor:r.palette.warning.main,color:"white",fontSize:"0.65rem",px:.7,borderRadius:1,display:"inline-flex",alignItems:"center",height:16},children:"PRO"})]}),subheader:a.jsx(t,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:a.jsx(F,{label:e.category,size:"small",variant:"outlined",color:"primary"})}),action:a.jsx(B,{title:"More information",children:a.jsx(j,{size:"small",children:a.jsx(D,{})})})}),a.jsxs(E,{sx:{flexGrow:1,pt:0},children:[a.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:e.shortDescription}),a.jsx(o,{variant:"body2",paragraph:!0,children:e.detailedDescription})]}),a.jsx(t,{sx:{p:2,pt:0},children:a.jsxs(L,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>b(e.path),endIcon:a.jsx(P,{}),disabled:(e.path.startsWith("advanced-analysis")||e.path.startsWith("publication-ready"))&&!m,sx:{borderColor:e.color,color:e.color,fontWeight:"bold","&:hover":{borderColor:e.color}},children:["Launch ",e.name]})})]})},e.name))})})]},s.name))]})};export{ya as default};
