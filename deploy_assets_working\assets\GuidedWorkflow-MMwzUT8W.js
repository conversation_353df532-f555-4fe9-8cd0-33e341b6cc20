import{u as Ue,j as e,B as t,ax as lr,R as P,bs as ee,e as l,I as G,aD as Le,D as xe,f as p,cl as ar,bS as He,bT as Pe,cm as or,bU as qe,L as ir,m as cr,n as ce,r as de,bV as Oe,aE as Ne,g as Te,af as Me,bW as dr,bX as xr,i as b,bY as hr,aF as Ye,cn as pr,co as tr,cp as ur,cq as mr,U as sr,k as Ge,ae as jr,l as Ve,cr as fr,cs as gr,bb as ue,ct as _e,b7 as Je,cu as br,cv as yr,aH as vr,bw as Cr,bR as kr,a6 as Ir,a7 as Ie,Z as Sr,W as wr,N as zr,cw as Ar,h as Xe,aB as je,cx as Qe,ap as We,aq as Re,ar as ie,as as z,at as $e,ao as Ze,a3 as Ke,cy as Br,cz as Tr,c<PERSON> as Dr,cB as Er,cC as Wr,aj as Rr,bc as $r,cD as er,cE as rr,cF as Fr,cG as nr,cH as Se,cI as we,cJ as Lr,cK as ze,bg as me,cL as Ae,cM as Fe,s as Hr}from"./mui-libs-CfwFIaTD.js";import{r as f,b as Pr}from"./react-libs-Cr2nE3UY.js";import{h as qr}from"./other-utils-CR9xr_gI.js";import{a as Or}from"./index-Bpan7Tbe.js";const Ur=({items:c,title:S="Help & Tips",initiallyExpanded:D=!1,variant:$="panel",placement:j="inline",triggerIcon:I=e.jsx(lr,{}),triggerText:E="Help",maxItems:w=3,collapsible:W=!0,showIcons:v=!0,width:A="auto",dense:d=!1})=>{const x=Ue(),[a,R]=f.useState(D),[C,Q]=f.useState(!1),[q,te]=f.useState(!1),Z=()=>{R(!a)},K=()=>{Q(!C)},V=o=>(h,g)=>{te(g?o:!1)},F=(o,h)=>{if(h)return h;switch(o){case"info":return e.jsx(Ye,{color:"info"});case"tip":return e.jsx(sr,{color:"warning"});case"warning":return e.jsx(mr,{color:"error"});case"note":return e.jsx(ur,{color:"action"});case"quote":return e.jsx(tr,{color:"secondary"});case"example":return e.jsx(pr,{color:"primary"});default:return e.jsx(Ye,{color:"info"})}},U=o=>{switch(o){case"info":return b(x.palette.info.main,.05);case"tip":return b(x.palette.warning.main,.05);case"warning":return b(x.palette.error.main,.05);case"note":return b(x.palette.grey[500],.05);case"quote":return b(x.palette.secondary.main,.05);case"example":return b(x.palette.primary.main,.05);default:return b(x.palette.info.main,.05)}},y=o=>{switch(o){case"info":return b(x.palette.info.main,.2);case"tip":return b(x.palette.warning.main,.2);case"warning":return b(x.palette.error.main,.2);case"note":return b(x.palette.grey[500],.2);case"quote":return b(x.palette.secondary.main,.2);case"example":return b(x.palette.primary.main,.2);default:return b(x.palette.info.main,.2)}},re=()=>{const o=a?c:c.slice(0,w);return e.jsxs(P,{sx:{p:d?1.5:2,width:A,boxShadow:"0 2px 8px rgba(0,0,0,0.05)",border:`1px solid ${x.palette.divider}`},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(ee,{fontSize:"small",sx:{mr:1,color:x.palette.primary.main}}),e.jsx(l,{variant:"subtitle1",fontWeight:"medium",children:S}),W&&e.jsx(G,{size:"small",onClick:Z,sx:{ml:"auto"},"aria-label":a?"Collapse help":"Expand help",children:e.jsx(Le,{sx:{transform:a?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s"}})})]}),e.jsx(xe,{sx:{mb:2}}),e.jsx(t,{sx:{mb:1},children:o.map((h,g)=>e.jsx(t,{sx:{mb:2,p:d?1:1.5,borderRadius:1,backgroundColor:U(h.type),border:`1px solid ${y(h.type)}`},children:e.jsxs(t,{sx:{display:"flex",alignItems:"flex-start"},children:[v&&e.jsx(t,{sx:{mr:1.5,mt:.25},children:F(h.type,h.icon)}),e.jsxs(t,{children:[e.jsx(l,{variant:d?"body2":"subtitle2",gutterBottom:!0,children:h.title}),e.jsx(t,{sx:{color:x.palette.text.secondary,fontSize:x.typography.body2.fontSize},children:h.content})]})]})},g))}),c.length>w&&!a&&e.jsxs(p,{size:"small",onClick:Z,endIcon:e.jsx(ar,{}),children:["Show ",c.length-w," more"]})]})},O=()=>e.jsxs(t,{sx:{width:A},children:[e.jsxs(l,{variant:"subtitle1",fontWeight:"medium",sx:{mb:1,display:"flex",alignItems:"center"},children:[e.jsx(ee,{fontSize:"small",sx:{mr:1,color:x.palette.primary.main}}),S]}),c.map((o,h)=>e.jsxs(dr,{expanded:q===`panel-${h}`,onChange:V(`panel-${h}`),sx:{mb:1,boxShadow:"none","&:before":{display:"none"},border:`1px solid ${x.palette.divider}`,borderRadius:"4px !important",overflow:"hidden"},children:[e.jsx(xr,{expandIcon:e.jsx(Le,{}),sx:{backgroundColor:q===`panel-${h}`?U(o.type):b(x.palette.background.default,.5)},children:e.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[v&&e.jsx(t,{sx:{mr:1.5,display:"flex",alignItems:"center"},children:F(o.type,o.icon)}),e.jsx(l,{variant:"subtitle2",children:o.title})]})}),e.jsx(hr,{sx:{backgroundColor:U(o.type),pt:0},children:e.jsx(t,{sx:{color:x.palette.text.secondary,fontSize:x.typography.body2.fontSize},children:o.content})})]},h))]}),Y=()=>e.jsx(t,{sx:{width:A,mb:2},children:c.map((o,h)=>e.jsxs(Te,{severity:o.type==="warning"?"error":o.type==="tip"?"warning":(o.type==="info","info"),sx:{mb:2},icon:v?F(o.type,o.icon):void 0,children:[e.jsx(Me,{children:o.title}),o.content]},h))}),N=()=>e.jsx(t,{sx:{display:"inline-block"},children:e.jsx(Ne,{title:e.jsxs(t,{sx:{maxWidth:300},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:S}),e.jsx(xe,{sx:{my:1}}),c.map((o,h)=>e.jsxs(t,{sx:{mb:h<c.length-1?1.5:0,mt:1},children:[e.jsx(l,{variant:"body2",fontWeight:"medium",gutterBottom:!0,children:o.title}),e.jsx(t,{sx:{fontSize:x.typography.caption.fontSize},children:o.content})]},h))]}),arrow:!0,placement:"top",children:e.jsx(G,{color:"primary",size:"small","aria-label":"Help",children:I})})}),se=()=>e.jsxs(e.Fragment,{children:[e.jsx(p,{startIcon:I,size:"small",variant:"outlined",onClick:K,children:E}),e.jsxs(He,{open:C,onClose:K,maxWidth:"sm",fullWidth:!0,children:[e.jsxs(Pe,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(ee,{sx:{mr:1,color:x.palette.primary.main}}),S,e.jsx(G,{"aria-label":"close",onClick:K,sx:{ml:"auto"},children:e.jsx(or,{})})]}),e.jsx(qe,{dividers:!0,children:e.jsx(ir,{disablePadding:!0,children:c.map((o,h)=>e.jsx(Pr.Fragment,{children:e.jsxs(cr,{alignItems:"flex-start",sx:{backgroundColor:U(o.type),mb:1,borderRadius:1,border:`1px solid ${y(o.type)}`,py:1.5},children:[v&&e.jsx(ce,{sx:{mt:.5,minWidth:40},children:F(o.type,o.icon)}),e.jsx(de,{primary:o.title,secondary:o.content,primaryTypographyProps:{fontWeight:500},secondaryTypographyProps:{color:"text.secondary"}})]})},h))})}),e.jsx(Oe,{children:e.jsx(p,{onClick:K,variant:"contained",children:"Close"})})]})]}),_=()=>{switch($){case"accordion":return O();case"inline":return Y();case"tooltip":return N();case"dialog":return se();case"panel":default:return re()}};return $==="tooltip"||$==="dialog"?_():e.jsx(t,{sx:{width:A,float:j==="left"||j==="right"?j:void 0,mb:j==="top"||j==="inline"?2:0,mt:j==="bottom"?2:0,mr:j==="left"?2:0,ml:j==="right"?2:0,clear:j==="left"||j==="right"?"both":void 0},children:_()})},Be=({children:c,value:S,index:D,...$})=>e.jsx("div",{role:"tabpanel",hidden:S!==D,id:`result-tabpanel-${D}`,"aria-labelledby":`result-tab-${D}`,...$,children:S===D&&e.jsx(t,{sx:{py:2},children:c})}),Yr=({title:c,description:S,timestamp:D,stats:$,statisticalTests:j,pValue:I,significance:E=.05,confidenceInterval:w,chart:W,chartTitle:v,table:A,interpretations:d,assumptions:x,footnotes:a,code:R,citations:C,onSave:Q,onCopy:q,onShare:te,onExport:Z,variant:K="default",showCitations:V=!0,showCode:F=!1,showAssumptions:U=!0})=>{const y=Ue(),re=f.useRef(null),[O,Y]=f.useState(0),[N,se]=f.useState(!1),[_,o]=f.useState(null),[h,g]=f.useState(!1),[L,H]=f.useState(""),[le,ae]=f.useState(!1),oe=(n,i)=>{Y(i)},he=n=>{o(n.currentTarget)},M=()=>{o(null)},J=()=>{se(!N)},pe=()=>{if(q)q();else{let n=`${c}
`;S&&(n+=`${S}

`),j&&j.length>0&&(n+=`Statistical Tests:
`,j.forEach(i=>{n+=`- ${i.name}: ${i.value}
`}),n+=`
`),d&&d.length>0&&(n+=`Interpretations:
`,d.forEach(i=>{n+=`- ${i}
`})),navigator.clipboard.writeText(n).then(()=>{H("Results copied to clipboard"),g(!0)}).catch(()=>{H("Failed to copy results"),g(!0)})}M()},B=()=>{Q?Q():(H("Save functionality not implemented"),g(!0)),M()},fe=async()=>{if(re.current){ae(!0);try{const i=(await qr(re.current,{backgroundColor:y.palette.background.paper,scale:2,logging:!1})).toDataURL("image/png"),k=document.createElement("a");k.download=`${c.replace(/\s+/g,"_")}_result.png`,k.href=i,k.click(),H("Image exported successfully"),g(!0)}catch(n){console.error("Error exporting image:",n),H("Failed to export image"),g(!0)}finally{ae(!1)}}M()},X=n=>{Z?Z(n):n==="png"?fe():(H(`Export to ${n.toUpperCase()} not implemented`),g(!0)),M()},ne=()=>{g(!1)},ge=(n,i)=>n===void 0?y.palette.text.secondary:n<=i?y.palette.success.main:y.palette.error.main,De=()=>{if(I===void 0)return null;const n=I<=E;return e.jsx(Xe,{label:n?"Significant":"Not Significant",color:n?"success":"error",size:"small",icon:n?e.jsx(je,{}):e.jsx(Qe,{}),sx:{fontWeight:500}})},be=()=>!$||$.length===0?null:e.jsx(t,{sx:{display:"flex",flexWrap:"wrap",gap:1,mb:2},children:$.map((n,i)=>e.jsx(Ne,{title:n.tooltip||"",arrow:!0,children:e.jsx(Ge,{sx:{minWidth:120,boxShadow:"none",border:`1px solid ${y.palette.divider}`},children:e.jsxs(Ve,{sx:{p:1.5,"&:last-child":{pb:1.5}},children:[e.jsx(l,{variant:"caption",color:"text.secondary",display:"block",children:n.label}),e.jsx(l,{variant:"h6",fontWeight:"medium",sx:{color:n.color?n.color:"inherit"},children:typeof n.value=="number"?n.value.toLocaleString(void 0,{maximumFractionDigits:4}):n.value})]})})},i))}),ye=()=>!x||x.length===0?null:e.jsxs(t,{sx:{mb:3},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Assumption Checks"}),e.jsx(Ze,{component:P,variant:"outlined",children:e.jsxs(We,{size:"small",children:[e.jsx(Re,{children:e.jsxs(ie,{children:[e.jsx(z,{children:"Assumption"}),e.jsx(z,{align:"center",children:"Status"}),e.jsx(z,{children:"Details"})]})}),e.jsx($e,{children:x.map((n,i)=>{let k,T;switch(n.status){case"passed":k=e.jsx(je,{fontSize:"small",sx:{verticalAlign:"middle",mr:.5}}),T=y.palette.success.main;break;case"warning":k=e.jsx(Dr,{fontSize:"small",sx:{verticalAlign:"middle",mr:.5}}),T=y.palette.warning.main;break;case"failed":k=e.jsx(Qe,{fontSize:"small",sx:{verticalAlign:"middle",mr:.5}}),T=y.palette.error.main;break;default:k=e.jsx(Tr,{fontSize:"small",sx:{verticalAlign:"middle",mr:.5}}),T=y.palette.text.secondary}return e.jsxs(ie,{children:[e.jsx(z,{component:"th",scope:"row",children:e.jsx(l,{variant:"body2",fontWeight:"medium",children:n.name})}),e.jsx(z,{align:"center",children:e.jsx(Xe,{icon:k,label:n.status,size:"small",sx:{bgcolor:b(T,.1),color:T,fontWeight:500,textTransform:"capitalize"}})}),e.jsx(z,{children:n.message&&e.jsx(l,{variant:"caption",color:"text.secondary",children:n.message})})]},i)})})]})})]}),ve=()=>!j||j.length===0?null:e.jsxs(t,{sx:{mb:3},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Statistical Tests"}),e.jsxs(We,{size:"small",children:[e.jsx(Re,{children:e.jsxs(ie,{children:[e.jsx(z,{children:"Test"}),e.jsx(z,{align:"right",children:"Value"}),j.some(n=>n.df!==void 0&&n.name.toLowerCase()!=="p-value")&&e.jsx(z,{align:"right",children:"df"})]})}),e.jsx($e,{children:j.map((n,i)=>e.jsxs(ie,{children:[e.jsx(z,{component:"th",scope:"row",children:e.jsx(Ne,{title:n.description||"",arrow:!0,children:e.jsx(l,{variant:"body2",children:n.name})})}),e.jsx(z,{align:"right",sx:{color:n.name.toLowerCase()==="p-value"&&n.value!==void 0?ge(n.value,E):"inherit",fontWeight:n.name.toLowerCase()==="p-value"&&n.value!==void 0&&n.value<=E?"bold":"normal"},children:typeof n.value=="number"?n.name.toLowerCase()==="p-value"&&n.value<.001?"< 0.001":n.value.toLocaleString(void 0,{maximumFractionDigits:4}):n.value}),j.some(k=>k.df!==void 0&&k.name.toLowerCase()!=="p-value")&&e.jsx(z,{align:"right",children:n.df!==void 0?n.df:n.name.toLowerCase()!=="p-value"?"-":""})]},i))})]})]}),Ce=()=>!A||!A.columns||!A.rows?null:e.jsxs(t,{sx:{mb:3},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Data Summary"}),e.jsx(Ze,{component:P,variant:"outlined",children:e.jsxs(We,{size:"small",children:[e.jsx(Re,{children:e.jsx(ie,{children:A.columns.map((n,i)=>e.jsx(z,{align:i===0?"left":"right",children:n},i))})}),e.jsx($e,{children:A.rows.map((n,i)=>e.jsx(ie,{children:n.map((k,T)=>e.jsx(z,{align:T===0?"left":"right",sx:{fontWeight:T===0?"medium":"normal"},children:typeof k=="number"?k.toLocaleString(void 0,{maximumFractionDigits:4}):k},T))},i))})]})})]}),ke=()=>!d||d.length===0?null:e.jsxs(t,{sx:{mb:3},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(tr,{color:"primary",sx:{mr:1,transform:"scaleX(-1)"}}),e.jsx(l,{variant:"subtitle2",children:"Interpretation"})]}),e.jsx(P,{variant:"outlined",sx:{p:2,bgcolor:b(y.palette.primary.main,.03),borderLeft:`4px solid ${y.palette.primary.main}`},children:d.map((n,i)=>e.jsx(l,{variant:"body2",paragraph:i<d.length-1,sx:{mb:i<d.length-1?1.5:0},children:n},i))})]}),r=()=>!R||!F?null:e.jsx(Ke,{in:N,children:e.jsxs(t,{sx:{mb:3},children:[e.jsxs(l,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Br,{sx:{mr:1,fontSize:"1rem"}}),"Code"]}),e.jsx(P,{variant:"outlined",sx:{p:2,bgcolor:b(y.palette.grey[900],.05),fontFamily:"monospace",fontSize:"0.85rem",overflow:"auto",maxHeight:200},children:e.jsx("pre",{style:{margin:0,whiteSpace:"pre-wrap"},children:R})})]})}),s=()=>!C||C.length===0||!V?null:e.jsx(Ke,{in:N,children:e.jsxs(t,{sx:{mb:2},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,children:"Citations"}),e.jsx(P,{variant:"outlined",sx:{p:1.5,bgcolor:b(y.palette.background.default,.5)},children:C.map((n,i)=>e.jsxs(l,{variant:"caption",display:"block",sx:{mb:i<C.length-1?1:0,pb:i<C.length-1?1:0,borderBottom:i<C.length-1?`1px solid ${y.palette.divider}`:"none"},children:[e.jsxs(l,{component:"span",variant:"caption",fontWeight:"medium",children:["[",i+1,"]"]})," ",n]},i))})]})}),m=()=>!a||a.length===0?null:e.jsxs(t,{sx:{mt:3},children:[e.jsx(xe,{sx:{mb:2}}),a.map((n,i)=>e.jsxs(l,{variant:"caption",color:"text.secondary",display:"block",sx:{mb:.5},children:["* ",n]},i))]}),u=()=>e.jsxs(t,{sx:{width:"100%"},children:[e.jsx(t,{sx:{borderBottom:1,borderColor:"divider"},children:e.jsxs(Ir,{value:O,onChange:oe,"aria-label":"result tabs",variant:"scrollable",scrollButtons:"auto",children:[e.jsx(Ie,{label:"Results",id:"result-tab-0","aria-controls":"result-tabpanel-0",icon:e.jsx(Sr,{}),iconPosition:"start"}),e.jsx(Ie,{label:"Visualization",id:"result-tab-1","aria-controls":"result-tabpanel-1",icon:e.jsx(wr,{}),iconPosition:"start",disabled:!W}),e.jsx(Ie,{label:"Data",id:"result-tab-2","aria-controls":"result-tabpanel-2",icon:e.jsx(zr,{}),iconPosition:"start",disabled:!A}),e.jsx(Ie,{label:"Interpretation",id:"result-tab-3","aria-controls":"result-tabpanel-3",icon:e.jsx(Ar,{}),iconPosition:"start",disabled:!d||d.length===0})]})}),e.jsxs(Be,{value:O,index:0,children:[be(),ve(),U&&ye()]}),e.jsx(Be,{value:O,index:1,children:W&&e.jsxs(t,{sx:{mb:2},children:[v&&e.jsx(l,{variant:"subtitle2",gutterBottom:!0,align:"center",children:v}),W]})}),e.jsx(Be,{value:O,index:2,children:Ce()}),e.jsx(Be,{value:O,index:3,children:ke()})]});return e.jsxs("div",{ref:re,children:[e.jsxs(Ge,{elevation:3,sx:{position:"relative",overflow:"visible",mb:3},children:[le&&e.jsxs(t,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:b(y.palette.background.paper,.7),zIndex:10,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:[e.jsx(jr,{size:40}),e.jsx(l,{variant:"body2",sx:{mt:2},children:"Exporting..."})]}),e.jsxs(Ve,{children:[e.jsxs(t,{sx:{display:"flex",alignItems:"flex-start",mb:2},children:[e.jsxs(t,{sx:{flex:1},children:[e.jsx(l,{variant:"h6",component:"h2",gutterBottom:!0,fontWeight:"medium",children:c}),S&&e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:S}),I!==void 0&&e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:1},children:[De(),e.jsxs(l,{variant:"body2",sx:{ml:1,color:ge(I,E),fontWeight:I<=E?"medium":"normal"},children:["p ",I<.001?"< 0.001":`= ${I.toFixed(3)}`]}),w&&e.jsxs(l,{variant:"body2",sx:{ml:2},children:["95% CI [",w[0].toFixed(2),", ",w[1].toFixed(2),"]"]})]}),D&&e.jsx(l,{variant:"caption",color:"text.secondary",display:"block",children:D.toLocaleString()})]}),e.jsxs(t,{children:[e.jsx(G,{"aria-label":"more","aria-controls":"result-menu","aria-haspopup":"true",onClick:he,size:"small",children:e.jsx(fr,{fontSize:"small"})}),e.jsxs(gr,{id:"result-menu",anchorEl:_,keepMounted:!0,open:!!_,onClose:M,children:[e.jsxs(ue,{onClick:pe,children:[e.jsx(ce,{children:e.jsx(_e,{fontSize:"small"})}),e.jsx(de,{children:"Copy to Clipboard"})]}),e.jsxs(ue,{onClick:B,children:[e.jsx(ce,{children:e.jsx(Je,{fontSize:"small"})}),e.jsx(de,{children:"Save Analysis"})]}),e.jsx(xe,{}),e.jsxs(ue,{onClick:()=>X("png"),children:[e.jsx(ce,{children:e.jsx(br,{fontSize:"small"})}),e.jsx(de,{children:"Export as Image"})]}),e.jsxs(ue,{onClick:()=>X("csv"),children:[e.jsx(ce,{children:e.jsx(yr,{fontSize:"small"})}),e.jsx(de,{children:"Export as CSV"})]}),e.jsxs(ue,{onClick:()=>X("pdf"),children:[e.jsx(ce,{children:e.jsx(vr,{fontSize:"small"})}),e.jsx(de,{children:"Export as PDF"})]})]})]})]}),e.jsx(xe,{sx:{mb:2}}),K==="compact"?u():e.jsx(e.Fragment,{children:e.jsxs(t,{sx:{display:"flex",flexDirection:"column",mb:3},children:[be(),ve(),W&&e.jsxs(t,{sx:{mb:3},children:[v&&e.jsx(l,{variant:"subtitle2",gutterBottom:!0,align:"center",children:v}),W]}),Ce(),ke(),U&&ye(),r(),s(),(R&&F||C&&C.length>0&&V)&&e.jsx(p,{variant:"text",size:"small",onClick:J,endIcon:e.jsx(Le,{sx:{transform:N?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s"}}),sx:{alignSelf:"flex-start",mt:1},children:N?"Show Less":"Show More"}),m()]})}),e.jsxs(t,{sx:{display:"flex",justifyContent:"flex-end",mt:2,pt:2,borderTop:`1px solid ${y.palette.divider}`},children:[e.jsx(p,{startIcon:e.jsx(_e,{}),size:"small",onClick:pe,sx:{mr:1},children:"Copy"}),e.jsx(p,{startIcon:e.jsx(Je,{}),size:"small",variant:"outlined",onClick:B,sx:{mr:1},children:"Save"}),e.jsx(p,{startIcon:e.jsx(Cr,{}),size:"small",variant:"contained",color:"primary",onClick:()=>X("png"),children:"Export"})]})]})]}),e.jsx(kr,{open:h,autoHideDuration:4e3,onClose:ne,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:e.jsx(Te,{onClose:ne,severity:"success",elevation:6,variant:"filled",children:L})})]})},_r=({steps:c,title:S="Analysis Workflow",description:D,variant:$="vertical",saveProgress:j=!0,persistenceKey:I="guided-workflow",onComplete:E,enableBookmarking:w=!1,showStepNavigation:W=!0,allowSkipSteps:v=!1,initialStep:A=0})=>{const d=Ue(),{activeDataset:x}=Or(),[a,R]=f.useState(A),[C,Q]=f.useState({}),[q,te]=f.useState({}),[Z,K]=f.useState(!1),[V,F]=f.useState([]),[U,y]=f.useState(!0),[re,O]=f.useState(!1),[Y,N]=f.useState(!1),[se,_]=f.useState(null),[o,h]=f.useState(!1);f.useEffect(()=>{if(j&&I){const r=localStorage.getItem(`${I}-progress`);if(r)try{const{completed:s,skipped:m,active:u,bookmarked:n,dontShowTips:i}=JSON.parse(r);Q(s||{}),te(m||{}),R(u||0),F(n||[]),N(i||!1)}catch(s){console.error("Failed to load workflow progress:",s)}}},[j,I]),f.useEffect(()=>{if(j&&I){const r={completed:C,skipped:q,active:a,bookmarked:V,dontShowTips:Y};localStorage.setItem(`${I}-progress`,JSON.stringify(r))}},[j,I,C,q,a,V,Y]),f.useEffect(()=>{var r;if(!Y&&((r=c[a])!=null&&r.tip)){const s=setTimeout(()=>{O(!0)},500);return()=>clearTimeout(s)}},[a,Y,c]);const g=a===c.length-1;c.every((r,s)=>C[s]||q[s]);const L=()=>{const r=c[a];return r!=null&&r.prerequisite?!r.prerequisite.condition:!1},H=()=>{const r=c[a];if(r.validation){const s=r.validation();if(s!==!0){_(typeof s=="string"?s:"Validation failed");return}}_(null),Q(s=>({...s,[a]:!0})),g?(h(!0),E&&E()):R(s=>s+1)},le=()=>{R(r=>r-1)},ae=r=>{W&&R(r)},oe=()=>{v&&(te(r=>({...r,[a]:!0})),g?(h(!0),E&&E()):R(r=>r+1))},he=()=>{R(0),Q({}),te({}),h(!1),_(null)},M=r=>{V.includes(r)?F(s=>s.filter(m=>m!==r)):F(s=>[...s,r])},J=()=>{K(!Z)},pe=()=>{O(!1)},B=r=>V.includes(r),fe=r=>C[r]?"completed":q[r]?"skipped":r===a?"current":"upcoming",X=()=>{const r=c[a];if(!(r!=null&&r.prerequisite)||!L()||!U)return null;const{message:s,severity:m="warning",action:u}=r.prerequisite;return e.jsxs(Te,{severity:m,sx:{mb:2},onClose:()=>y(!1),action:u?e.jsx(p,{color:"inherit",size:"small",onClick:u.onClick,children:u.label}):void 0,children:[e.jsx(Me,{children:m==="error"?"Required":"Recommended"}),s]})},ne=()=>se?e.jsxs(Te,{severity:"error",sx:{mb:2},children:[e.jsx(Me,{children:"Validation Error"}),se]}):null,ge=()=>{const r=c[a];return r!=null&&r.tip?e.jsxs(He,{open:re,onClose:pe,maxWidth:"sm",children:[e.jsxs(Pe,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(sr,{color:"warning",sx:{mr:1}}),"Tip for This Step"]}),e.jsxs(qe,{children:[e.jsx(Wr,{children:r.tip}),e.jsx(Rr,{control:e.jsx($r,{checked:Y,onChange:s=>N(s.target.checked),color:"primary"}),label:"Don't show tips anymore",sx:{mt:2}})]}),e.jsx(Oe,{children:e.jsx(p,{onClick:pe,color:"primary",children:"Got it"})})]}):null},De=()=>{const r=c[a];return e.jsxs(He,{open:Z,onClose:J,maxWidth:"md",fullWidth:!0,children:[e.jsxs(Pe,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(ee,{color:"primary",sx:{mr:1}}),"Help for ",(r==null?void 0:r.title)||"This Step",e.jsx(G,{"aria-label":"close",onClick:J,sx:{position:"absolute",right:8,top:8},children:e.jsx(Er,{})})]}),e.jsx(qe,{dividers:!0,children:r!=null&&r.helpContent?r.helpContent:e.jsx(l,{variant:"body1",children:"No additional help content is available for this step."})}),e.jsx(Oe,{children:e.jsx(p,{onClick:J,color:"primary",children:"Close"})})]})},be=()=>e.jsxs(t,{sx:{maxWidth:"100%"},children:[e.jsx(er,{activeStep:a,orientation:"vertical",children:c.map((r,s)=>{const m={},u={};return r.optional&&(u.optional=e.jsx(l,{variant:"caption",children:"Optional"})),C[s]&&(m.completed=!0),e.jsxs(rr,{...m,children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(Fr,{onClick:()=>ae(s),disabled:!W,sx:{flexGrow:1,textAlign:"left",mr:w?1:0},children:e.jsx(nr,{optional:u.optional,children:r.title})}),w&&e.jsx(G,{size:"small",onClick:n=>{n.stopPropagation(),M(s)},color:B(s)?"primary":"default","aria-label":B(s)?"Remove bookmark":"Add bookmark",children:B(s)?e.jsx(Se,{fontSize:"small"}):e.jsx(we,{fontSize:"small"})})]}),e.jsxs(Lr,{children:[r.description&&e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:r.description}),s===a&&X(),s===a&&ne(),e.jsx(t,{sx:{mb:2},children:r.content}),e.jsxs(t,{sx:{mb:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs(t,{children:[e.jsx(p,{disabled:s===0,onClick:le,sx:{mr:1},startIcon:e.jsx(ze,{}),children:"Back"}),e.jsx(p,{variant:"contained",color:"primary",onClick:H,endIcon:g?e.jsx(me,{}):e.jsx(Ae,{}),disabled:L()&&!v,children:g?"Finish":r.nextButton||"Next"}),v&&L()&&e.jsx(p,{sx:{ml:1},variant:"text",color:"warning",onClick:oe,children:"Skip"})]}),r.helpContent&&e.jsx(p,{startIcon:e.jsx(ee,{}),color:"inherit",onClick:J,children:"Help"})]})]})]},r.id)})}),o&&e.jsxs(P,{square:!0,elevation:0,sx:{p:3,mt:3,borderRadius:1},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(je,{color:"success",sx:{mr:1}}),e.jsx(l,{variant:"h6",component:"h3",children:"All steps completed!"})]}),e.jsx(l,{paragraph:!0,children:"You have successfully completed all necessary steps in this workflow."}),e.jsx(p,{onClick:he,startIcon:e.jsx(Fe,{}),children:"Reset Workflow"})]})]}),ye=()=>{var r,s,m,u,n;return e.jsxs(t,{sx:{width:"100%"},children:[e.jsx(er,{activeStep:a,alternativeLabel:!0,children:c.map((i,k)=>{const T={},Ee={};return i.optional&&(Ee.optional=e.jsx(l,{variant:"caption",children:"Optional"})),C[k]&&(T.completed=!0),e.jsx(rr,{...T,children:e.jsx(nr,{...Ee,optional:Ee.optional,children:e.jsx(l,{variant:"body2",children:i.title})})},i.id)})}),e.jsxs(t,{sx:{mt:4,mb:2},children:[X(),ne(),e.jsxs(P,{variant:"outlined",sx:{p:3},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2,justifyContent:"space-between"},children:[e.jsx(l,{variant:"h6",component:"h3",children:((r=c[a])==null?void 0:r.title)||""}),w&&e.jsx(G,{size:"small",onClick:()=>M(a),color:B(a)?"primary":"default",children:B(a)?e.jsx(Se,{}):e.jsx(we,{})})]}),((s=c[a])==null?void 0:s.description)&&e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:c[a].description}),e.jsx(xe,{sx:{my:2}}),e.jsx(t,{sx:{mb:2},children:((m=c[a])==null?void 0:m.content)||"No content available for this step."})]})]}),e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",mt:2},children:[e.jsxs(t,{children:[e.jsx(p,{disabled:a===0,onClick:le,sx:{mr:1},startIcon:e.jsx(ze,{}),children:"Back"}),e.jsx(p,{variant:"contained",color:"primary",onClick:H,endIcon:g?e.jsx(me,{}):e.jsx(Ae,{}),disabled:L()&&!v,children:g?"Finish":((u=c[a])==null?void 0:u.nextButton)||"Next"}),v&&L()&&e.jsx(p,{sx:{ml:1},variant:"text",color:"warning",onClick:oe,children:"Skip"})]}),((n=c[a])==null?void 0:n.helpContent)&&e.jsx(p,{startIcon:e.jsx(ee,{}),color:"inherit",onClick:J,children:"Help"})]}),o&&e.jsxs(P,{square:!0,elevation:0,sx:{p:3,mt:3,borderRadius:1},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(je,{color:"success",sx:{mr:1}}),e.jsx(l,{variant:"h6",component:"h3",children:"All steps completed!"})]}),e.jsx(l,{paragraph:!0,children:"You have successfully completed all necessary steps in this workflow."}),e.jsx(p,{onClick:he,startIcon:e.jsx(Fe,{}),children:"Reset Workflow"})]})]})},ve=()=>e.jsxs(t,{sx:{width:"100%"},children:[e.jsx(t,{sx:{mb:3},children:c.map((r,s)=>{const m=s===a,u=fe(s);return e.jsxs(t,{sx:{mb:2,position:"relative",opacity:u==="upcoming"?.7:1},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"flex-start"},children:[e.jsx(t,{sx:{flexGrow:1,position:"relative",cursor:W?"pointer":"default",mr:w?1:0},onClick:()=>ae(s),children:e.jsxs(P,{variant:m?"elevation":"outlined",elevation:m?2:0,sx:{p:2,borderLeft:`4px solid ${u==="completed"?d.palette.success.main:u==="skipped"?d.palette.warning.main:u==="current"?d.palette.primary.main:d.palette.grey[300]}`,backgroundColor:m?b(d.palette.primary.main,.05):"inherit"},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{width:28,height:28,borderRadius:"50%",bgcolor:u==="completed"?d.palette.success.main:u==="skipped"?d.palette.warning.main:u==="current"?d.palette.primary.main:d.palette.grey[300],color:u==="upcoming"?d.palette.getContrastText(d.palette.grey[300]):"#fff",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:2},children:u==="completed"?e.jsx(me,{fontSize:"small"}):s+1}),e.jsxs(l,{variant:"subtitle1",fontWeight:m?"medium":"normal",sx:{flexGrow:1},children:[r.title,r.optional&&e.jsx(l,{component:"span",variant:"caption",sx:{ml:1,color:"text.secondary"},children:"(Optional)"})]})]}),r.description&&e.jsx(l,{variant:"body2",color:"text.secondary",sx:{mt:1,ml:5},children:r.description})]})}),w&&e.jsx(G,{size:"small",onClick:()=>M(s),sx:{mt:2,flexShrink:0},color:B(s)?"primary":"default","aria-label":B(s)?"Remove bookmark":"Add bookmark",children:B(s)?e.jsx(Se,{fontSize:"small"}):e.jsx(we,{fontSize:"small"})})]}),m&&e.jsxs(t,{sx:{mt:2,ml:5},children:[X(),ne(),e.jsx(t,{sx:{mb:3},children:r.content}),e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs(t,{children:[e.jsx(p,{disabled:s===0,onClick:le,sx:{mr:1},startIcon:e.jsx(ze,{}),children:"Back"}),e.jsx(p,{variant:"contained",color:"primary",onClick:H,endIcon:g?e.jsx(me,{}):e.jsx(Ae,{}),disabled:L()&&!v,children:g?"Finish":r.nextButton||"Next"}),v&&L()&&e.jsx(p,{sx:{ml:1},variant:"text",color:"warning",onClick:oe,children:"Skip"})]}),r.helpContent&&e.jsx(p,{startIcon:e.jsx(ee,{}),color:"inherit",onClick:J,children:"Help"})]})]}),s<c.length-1&&!m&&e.jsx(t,{sx:{position:"absolute",left:14,top:38,bottom:-16,width:2,bgcolor:d.palette.divider}})]},r.id)})}),o&&e.jsxs(P,{square:!0,elevation:0,sx:{p:3,borderRadius:1},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(je,{color:"success",sx:{mr:1}}),e.jsx(l,{variant:"h6",component:"h3",children:"All steps completed!"})]}),e.jsx(l,{paragraph:!0,children:"You have successfully completed all necessary steps in this workflow."}),e.jsx(p,{onClick:he,startIcon:e.jsx(Fe,{}),children:"Reset Workflow"})]})]}),Ce=()=>{const r=c[a];return e.jsxs(t,{sx:{width:"100%"},children:[e.jsxs(Ge,{elevation:3,children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",px:3,py:2,borderBottom:`1px solid ${d.palette.divider}`,backgroundColor:b(d.palette.primary.main,.05)},children:[e.jsx(l,{variant:"h6",component:"h3",children:(r==null?void 0:r.title)||""}),e.jsxs(t,{sx:{ml:"auto",display:"flex",alignItems:"center"},children:[e.jsxs(l,{variant:"body2",sx:{mr:1},children:["Step ",a+1," of ",c.length]}),w&&e.jsx(G,{size:"small",onClick:()=>M(a),color:B(a)?"primary":"default",children:B(a)?e.jsx(Se,{}):e.jsx(we,{})}),(r==null?void 0:r.helpContent)&&e.jsx(G,{size:"small",onClick:J,color:"primary",children:e.jsx(ee,{})})]})]}),e.jsxs(Ve,{children:[(r==null?void 0:r.description)&&e.jsx(l,{variant:"body2",color:"text.secondary",paragraph:!0,children:r.description}),X(),ne(),e.jsx(t,{sx:{my:2},children:(r==null?void 0:r.content)||"No content available for this step."})]}),e.jsxs(Hr,{sx:{p:2,borderTop:`1px solid ${d.palette.divider}`},children:[e.jsx(p,{disabled:a===0,onClick:le,startIcon:e.jsx(ze,{}),children:"Back"}),e.jsx(t,{sx:{flex:1}}),v&&L()&&e.jsx(p,{variant:"text",color:"warning",onClick:oe,children:"Skip"}),e.jsx(p,{variant:"contained",color:"primary",onClick:H,endIcon:g?e.jsx(me,{}):e.jsx(Ae,{}),disabled:L()&&!v,children:g?"Finish":(r==null?void 0:r.nextButton)||"Next"})]})]}),e.jsx(t,{sx:{display:"flex",mt:3,mb:2},children:c.map((s,m)=>{const u=fe(m);return e.jsx(t,{sx:{height:8,flex:1,mx:.5,borderRadius:4,bgcolor:u==="completed"?d.palette.success.main:u==="skipped"?d.palette.warning.main:u==="current"?d.palette.primary.main:d.palette.grey[300],cursor:W?"pointer":"default"},onClick:()=>ae(m)},m)})})]})},ke=()=>{switch($){case"horizontal":return ye();case"numbered":return ve();case"wizard":return Ce();case"vertical":default:return be()}};return e.jsxs(t,{sx:{width:"100%"},children:[e.jsxs(t,{sx:{mb:4},children:[e.jsx(l,{variant:"h5",component:"h2",gutterBottom:!0,children:S}),D&&e.jsx(l,{variant:"body1",color:"text.secondary",paragraph:!0,children:D})]}),ke(),De(),ge()]})};export{Ur as C,Yr as E,_r as G};
