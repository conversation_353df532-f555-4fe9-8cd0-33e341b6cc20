import{j as e,u as j,C,R as n,i,e as o,B as t,h as l,G as c,k as b,bz as v,aE as w,I as S,aF as d,d as D,l as z,f as I,bA as A,Y as p,bC as F,bD as k,bE as B,bF as u,aP as E}from"./mui-libs-CfwFIaTD.js";import{r as W}from"./react-libs-Cr2nE3UY.js";const h=[{name:"Case-Control Calculator",shortDescription:"Analyze data from case-control studies",detailedDescription:"Calculate odds ratios, confidence intervals, and other measures of association for case-control study data.",path:"epicalc/case-control",icon:e.jsx(p,{}),category:"Study Design",color:"#FF9800"},{name:"Cohort Calculator",shortDescription:"Analyze data from cohort studies",detailedDescription:"Calculate risk ratios, rate ratios, confidence intervals, and other measures of association for cohort study data.",path:"epicalc/cohort",icon:e.jsx(F,{}),category:"Study Design",color:"#4CAF50"},{name:"Cross-Sectional Calculator",shortDescription:"Analyze data from cross-sectional studies",detailedDescription:"Calculate prevalence ratios, odds ratios, confidence intervals, and other measures of association for cross-sectional study data.",path:"epicalc/cross-sectional",icon:e.jsx(k,{}),category:"Study Design",color:"#2196F3"},{name:"Matched Case-Control Calculator",shortDescription:"Analyze data from matched case-control studies",detailedDescription:"Calculate matched odds ratios and confidence intervals for data from matched case-control studies.",path:"epicalc/matched-case-control",icon:e.jsx(B,{}),category:"Study Design",color:"#9C27B0"},{name:"Sample Size / Power Calculator",shortDescription:"Calculate sample size or power for various study designs",detailedDescription:"Determine the required sample size for a study or the power of a study given a specific sample size, for different epidemiological study designs.",path:"epicalc/sample-size-power",icon:e.jsx(u,{}),category:"Sample Size",color:"#F44336"}],G=({onNavigate:x})=>{const s=j(),[r,m]=W.useState("All"),g=["All","Study Design","Sample Size"],f=r==="All"?h:h.filter(a=>a.category===r),y=a=>{switch(a){case"Study Design":return e.jsx(p,{});case"Sample Size":return e.jsx(u,{});default:return e.jsx(E,{})}};return e.jsxs(C,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(n,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${i(s.palette.success.main,.1)} 0%, ${i(s.palette.info.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(o,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Epidemiological Calculators"}),e.jsx(o,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Tools for analyzing data from various epidemiological study designs"}),e.jsx(o,{variant:"body1",color:"text.secondary",children:"Access a collection of calculators to assist with the analysis of case-control, cohort, cross-sectional, and matched case-control studies, as well as sample size and power calculations."})]}),e.jsxs(t,{sx:{mb:4},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),e.jsx(t,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:g.map(a=>e.jsx(l,{label:a,onClick:()=>m(a),variant:r===a?"filled":"outlined",color:r===a?"primary":"default",icon:a!=="All"?y(a):void 0,sx:{"&:hover":{backgroundColor:r===a?s.palette.primary.dark:i(s.palette.primary.main,.1)}}},a))})]}),e.jsx(c,{container:!0,spacing:3,children:f.map((a,P)=>e.jsx(c,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(b,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:s.shadows[8],"& .launch-button":{backgroundColor:a.color,color:"white"}}},children:[e.jsx(v,{avatar:e.jsx(D,{sx:{bgcolor:a.color,width:48,height:48},children:a.icon}),title:e.jsx(o,{variant:"h6",fontWeight:"bold",children:a.name}),subheader:e.jsx(t,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:e.jsx(l,{label:a.category,size:"small",variant:"outlined",color:"primary"})}),action:e.jsx(w,{title:"More information",children:e.jsx(S,{size:"small",children:e.jsx(d,{})})})}),e.jsxs(z,{sx:{flexGrow:1,pt:0},children:[e.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:a.shortDescription}),e.jsx(o,{variant:"body2",paragraph:!0,children:a.detailedDescription})]}),e.jsx(t,{sx:{p:2,pt:0},children:e.jsxs(I,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>x(a.path),endIcon:e.jsx(A,{}),sx:{borderColor:a.color,color:a.color,fontWeight:"bold","&:hover":{borderColor:a.color}},children:["Launch ",a.name]})})]})},a.name))}),e.jsx(n,{elevation:1,sx:{p:3,mt:4,backgroundColor:i(s.palette.info.main,.05),border:`1px solid ${i(s.palette.info.main,.2)}`},children:e.jsxs(t,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[e.jsx(d,{color:"info"}),e.jsxs(t,{children:[e.jsx(o,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),e.jsxs(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Case-Control:"})," For studies comparing exposure in people with and without a disease."]}),e.jsxs(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Cohort:"})," For studies following groups with and without an exposure to see who develops a disease."]}),e.jsxs(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Cross-Sectional:"})," For studies assessing exposure and disease status at a single point in time."]}),e.jsxs(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Matched Case-Control:"})," For case-control studies where cases are matched to controls on specific characteristics."]}),e.jsxs(o,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Sample Size / Power:"})," To determine study size or evaluate the likelihood of detecting an effect."]})]})]})})]})};export{G as E,h as e};
