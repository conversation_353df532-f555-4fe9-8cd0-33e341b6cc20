import{u as Xe,j as e,B as Y,e as w,R as J,G as $,ai as D,b9 as q,ba as A,bb as b,bc as Je,r as Ye,h as Qe,f as Ze,X as ea,ae as aa,g as Ie,ao as ie,ap as se,aq as ge,ar as z,as as h,at as re,cz as ta}from"./mui-libs-CfwFIaTD.js";import{r as G}from"./react-libs-Cr2nE3UY.js";import{a as na,D as xe}from"./index-Bpan7Tbe.js";import{f as le,c as oe,b as ce,a as de,o as ia}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{m as sa,w as ra,k as la,c as oa}from"./non-parametric-Cf6Ds91x.js";import{R as We,B as Me,C as ze,X as Ee,Y as Re,T as De,L as ca,a as qe,b as da}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./math-lib-BOZ-XUok.js";var Ae=(c=>(c.AVERAGE="average",c.MIN="min",c.MAX="max",c.ORDINAL="ordinal",c))(Ae||{});const ha={strategy:"average",ascending:!0};function ua(c,i,T){const S={...ha,...i},F=(x,k)=>{if(typeof x=="number")return x;throw new Error(`Data at index ${k} is not a number and no valueAccessor was provided.`)},j=c.map((x,k)=>({originalValue:x,originalIndex:k,numericValue:F(x,k)}));j.sort((x,k)=>{const C=S.ascending?x.numericValue-k.numericValue:k.numericValue-x.numericValue;return C===0?x.originalIndex-k.originalIndex:C});const f=new Array(c.length);let R=0;for(;R<j.length;){let x=R;for(;x<j.length&&j[x].numericValue===j[R].numericValue;)x++;const k=x-R,C=R+1;let O;switch(S.strategy){case"average":let I=0;for(let E=0;E<k;E++)I+=C+E;O=I/k;break;case"min":O=C;break;case"max":O=C+k-1;break;case"ordinal":break;default:throw new Error(`Unknown tie strategy: ${S.strategy}`)}for(let I=0;I<k;I++){const E=j[R+I],U=S.strategy==="ordinal"?C+I:O;f[E.originalIndex]={originalValue:E.originalValue,originalIndex:E.originalIndex,rank:U}}R=x}return f}function ma(c){const i=[76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18];let T=c,S=c+5.5;S-=(c+.5)*Math.log(S);let F=1.000000000190015;for(let j=0;j<6;j++)F+=i[j]/++T;return-S+Math.log(2.5066282746310007*F/c)}function fa(c,i){if(i<0||c<=0||i===0)return 0;const T=ma(c);if(i<c+1){let S=c,F=1/c,j=F;for(let f=0;f<100&&(S+=1,F*=i/S,j+=F,!(Math.abs(F)<Math.abs(j)*1e-7));f++);return j*Math.exp(-i+c*Math.log(i)-T)}else{let S=i+1-c,F=1/1e-30,j=1/S,f=j;for(let R=0;R<100;R++){const x=-R*(R-c+1);S+=2,j=x*j+S,Math.abs(j)<1e-30&&(j=1e-30),F=S+x/F,Math.abs(F)<1e-30&&(F=1e-30),j=1/j;const k=j*F;if(f*=k,Math.abs(k-1)<1e-7)break}return 1-Math.exp(-i+c*Math.log(i)-T)*f}}function ga(c,i){if(isNaN(c)||i<=0||c<0)return NaN;if(c===0)return 1;if(i===1&&c>1e3||i>1e3&&c>i+100*Math.sqrt(2*i))return 0;const T=1-fa(i/2,c/2);return Math.max(0,Math.min(1,T))}function xa(c){var _;if(!c||c.length===0)throw new Error("Input data cannot be empty.");const i=c.length;if(i<2)throw new Error("Friedman test requires at least 2 subjects/blocks.");const T=((_=c[0])==null?void 0:_.length)||0;if(T<3)throw new Error("Friedman test requires at least 3 conditions/groups.");for(let v=0;v<i;v++){if(c[v].length!==T)throw new Error(`All subjects must have the same number of conditions. Subject ${v+1} has ${c[v].length}, expected ${T}.`);for(let W=0;W<T;W++)if(typeof c[v][W]!="number"||isNaN(c[v][W]))throw new Error(`Data for subject ${v+1}, condition ${W+1} is not a valid number.`)}const S=[];for(let v=0;v<i;v++){const W=ua(c[v],{strategy:Ae.AVERAGE});S.push(W.map(ee=>ee.rank))}const F=new Array(T).fill(0);for(let v=0;v<T;v++)for(let W=0;W<i;W++)F[v]+=S[W][v];const j=F.map(v=>v/i);let f=0;for(let v=0;v<T;v++)f+=Math.pow(F[v],2);const R=12*f,x=i*T*(T+1),k=3*i*(T+1),C=R/x-k,O=Number.isFinite(C)?C:0,I=T-1,E=ga(O,I),U=O.toFixed(3),Q=typeof E=="number"&&!isNaN(E)?E<.001?"< 0.001":E.toFixed(3):"undefined",K=E<.05?"There was a statistically significant difference":"There was no statistically significant difference";let Z=`A Friedman Test was conducted to compare the ranks of ${T} related conditions/variables for ${i} subjects. ${K} among the mean ranks of the conditions/variables (χ²(${I}) = ${U}, p = ${Q}).`;return(i<10||T<4)&&(Z+=" Note: For small sample sizes, the Chi-Squared approximation may be less accurate. Exact p-values might be preferred if available."),{statistic:O,df:I,pValue:E,n:i,k:T,interpretation:Z,rankSums:F,meanRanks:j}}const Va=()=>{var Se,Fe;const{datasets:c,currentDataset:i,setCurrentDataset:T}=na(),S=Xe(),[F,j]=G.useState((i==null?void 0:i.id)||""),[f,R]=G.useState("mannWhitney"),[x,k]=G.useState(""),[C,O]=G.useState(""),[I,E]=G.useState(""),[U,Q]=G.useState([]),[K,Z]=G.useState(""),[_,v]=G.useState(""),[W,ee]=G.useState(""),[X,ye]=G.useState([]),[he,pe]=G.useState(!1),[we,be]=G.useState(null),[s,B]=G.useState(null);G.useEffect(()=>{const a=localStorage.getItem(`nonparametric_${f}_results`);if(a)try{B(JSON.parse(a))}catch(n){console.error("Error parsing saved test results:",n)}},[f]);const M=(i==null?void 0:i.columns.filter(a=>a.type===xe.NUMERIC))||[],L=(i==null?void 0:i.columns.filter(a=>a.type===xe.CATEGORICAL||a.type===xe.ORDINAL||a.type===xe.BOOLEAN))||[],ue=(()=>{if(!i||!K)return[];const a=i.columns.find(t=>t.id===K);return a?i.data.map(t=>String(t[a.name])).filter((t,d,o)=>o.indexOf(t)===d):[]})(),me=(()=>{if(!i||!I)return[];const a=i.columns.find(t=>t.id===I);return a?i.data.map(t=>String(t[a.name])).filter((t,d,o)=>o.indexOf(t)===d&&t!==null&&t!==void 0&&t!=="").sort():[]})(),Ge=a=>{const n=a.target.value;j(n),$e();const t=c.find(d=>d.id===n);t&&T(t)},$e=()=>{k(""),O(""),E(""),Q([]),Z(""),v(""),ee(""),localStorage.removeItem(`nonparametric_${f}_results`),B(null),ye([])},Be=a=>{R(a.target.value),$e()},fe=a=>{k(a.target.value),B(null)},je=a=>{O(a.target.value),B(null)},Oe=a=>{Z(a.target.value),v(""),ee(""),B(null)},Le=a=>{E(a.target.value),Q([]),B(null)},Pe=a=>{v(a.target.value),B(null)},Ue=a=>{ee(a.target.value),B(null)},Ke=a=>{const n=a.target.value;Q(typeof n=="string"?n.split(","):n),B(null)},_e=a=>{const n=a.target.value;ye(typeof n=="string"?n.split(","):n),B(null)},Te=()=>{if(!i)return!1;switch(f){case"mannWhitney":return K?!!x&&!!_&&!!W&&_!==W:!!x&&!!C;case"wilcoxon":return!!x&&!!C;case"kruskalWallis":return!!x&&!!I&&(U.length>=3||me.length>=3&&U.length===0);case"chiSquare":return!!x&&!!C;case"friedman":return X.length>=3;default:return!1}},He=()=>{if(!i||!Te()){be("Please select all required variables for the selected test.");return}pe(!0),be(null),B(null);try{let a={};if(f==="mannWhitney"){const n=i.columns.find(r=>r.id===x);if(!n)throw new Error("Selected variable not found in dataset.");let t=[],d=[],o=[];if(K){const r=i.columns.find(y=>y.id===K);if(!r||!_||!W)throw new Error("Please select both group values.");const l=i.data.filter(y=>String(y[r.name])===_).map(y=>y[n.name]);t=le(l);const V=i.data.filter(y=>String(y[r.name])===W).map(y=>y[n.name]);d=le(V),o=[`${n.name} (${_})`,`${n.name} (${W})`]}else{const r=i.columns.find(y=>y.id===C);if(!r)throw new Error("Selected second variable not found in dataset.");const l=i.data.map(y=>y[n.name]);t=le(l);const V=i.data.map(y=>y[r.name]);d=le(V),o=[n.name,r.name]}if(t.length===0||d.length===0)throw new Error("Not enough valid data in one or both groups.");const u=sa(t,d),m=u.n1+u.n2,p=Math.abs(u.z)/Math.sqrt(m);a={test:"Mann-Whitney U Test",description:"Non-parametric test for comparing two independent samples",statistic:u.U,statName:"U",z:u.z,pValue:u.pValue,effectSize:p,effectSizeName:"r",groups:[{name:o[0],n:t.length,median:de(t),mean:ce(t),sd:oe(t)},{name:o[1],n:d.length,median:de(d),mean:ce(d),sd:oe(d)}]},a.chartData=a.groups.map(r=>({name:r.name,value:r.median})),a.interpretation=te(a)}else if(f==="wilcoxon"){const n=i.columns.find(r=>r.id===x),t=i.columns.find(r=>r.id===C);if(!n||!t)throw new Error("Selected variables not found in dataset.");const d=i.data.map(r=>({value1:r[n.name],value2:r[t.name]})).filter(r=>typeof r.value1=="number"&&!isNaN(r.value1)&&typeof r.value2=="number"&&!isNaN(r.value2)),o=d.map(r=>r.value1),u=d.map(r=>r.value2);if(o.length<5)throw new Error("Not enough valid paired data. Wilcoxon test requires at least 5 pairs.");const m=ra(o,u),p=Math.abs(m.z)/Math.sqrt(m.n);a={test:"Wilcoxon Signed Rank Test",description:"Non-parametric test for comparing two related samples",statistic:m.W,statName:"W",z:m.z,n:m.n,pValue:m.pValue,effectSize:p,effectSizeName:"r",groups:[{name:n.name,n:o.length,median:de(o),mean:ce(o),sd:oe(o)},{name:t.name,n:u.length,median:de(u),mean:ce(u),sd:oe(u)}]},a.chartData=a.groups.map(r=>({name:r.name,value:r.median})),a.interpretation=te(a)}else if(f==="kruskalWallis"){const n=i.columns.find(r=>r.id===x),t=i.columns.find(r=>r.id===I);if(!n||!t)throw new Error("Selected variables not found.");const d=U.length>0?U:me;if(d.length<3)throw new Error("Kruskal-Wallis test requires at least 3 groups. For 2 groups, use Mann-Whitney U test instead.");const o=[],u=[];if(d.forEach(r=>{const l=i.data.filter(y=>String(y[t.name])===r).map(y=>y[n.name]),V=le(l);V.length>0&&(o.push(V),u.push(r))}),o.length<3)throw new Error("Not enough groups with valid data. Kruskal-Wallis test requires at least 3 groups.");const m=la(o),p=(m.H-(o.length-1))/(m.n-o.length);a={test:"Kruskal-Wallis Test",description:"Non-parametric alternative to one-way ANOVA",statistic:m.H,statName:"H",df:m.df,n:m.n,pValue:m.pValue,effectSize:p,effectSizeName:"η²",groups:u.map((r,l)=>({name:r,n:o[l].length,median:de(o[l]),mean:ce(o[l]),sd:oe(o[l])}))},a.chartData=a.groups.map(r=>({name:r.name,value:r.median})),a.interpretation=te(a)}else if(f==="friedman"){if(X.length<3)throw new Error("Friedman test requires at least 3 numeric/ordinal variables.");if(!i)throw new Error("Dataset not available");const n=[],t=i.data.length;for(let d=0;d<t;d++){const o=[];for(const u of X){const m=i.columns.find(r=>r.id===u);if(!m)throw new Error(`Column with id ${u} not found.`);const p=i.data[d][m.name];if(typeof p!="number"||isNaN(p)){const r=parseFloat(String(p));if(isNaN(r))throw new Error(`Invalid data for subject ${d+1}, variable ${m.name}. Expected a number, got ${p}.`);o.push(r)}else o.push(p)}n.push(o)}a=xa(n),a.summary=`Friedman Test for repeated measures on variables: ${X.map(d=>{var o;return((o=i.columns.find(u=>u.id===d))==null?void 0:o.name)||d}).join(", ")}`,a.test="Friedman Test"}else if(f==="chiSquare"){const n=i.columns.find(N=>N.id===x),t=i.columns.find(N=>N.id===C);if(!n||!t)throw new Error("Selected variables not found in dataset.");const d=i.data.map(N=>String(N[n.name]??"Missing")),o=i.data.map(N=>String(N[t.name]??"Missing")),u=ia(d,o),m=Object.keys(u),p=Object.keys(m.length>0?u[m[0]]:{});if(m.length<2||p.length<2)throw new Error("Chi-Square test requires at least 2 categories for each variable.");const r=m.map(N=>p.map(P=>u[N][P]||0)),l=oa(r),V={},y={};let ne=0;m.forEach(N=>{V[N]=0,p.forEach(P=>{const H=u[N][P]||0;V[N]+=H,y[P]=(y[P]||0)+H,ne+=H})}),a={test:"Chi-Square Test of Independence",description:"Test association between categorical variables",statistic:l.chiSquare,statName:"χ²",df:l.df,pValue:l.pValue,effectSize:l.cramersV,effectSizeName:"Cramer's V",contingencyTable:{rowCategories:m,columnCategories:p,frequencies:u,rowTotals:V,columnTotals:y,grandTotal:ne},variables:[{name:n.name,categories:m},{name:t.name,categories:p}]},a.chartData=m.map(N=>{const P={category:N};return p.forEach(H=>{P[H]=u[N][H]||0}),P}),a.interpretation=te(a)}B(a),localStorage.setItem(`nonparametric_${f}_results`,JSON.stringify(a)),pe(!1)}catch(a){be(`Error running test: ${a instanceof Error?a.message:String(a)}`),pe(!1)}},ae=(a,n)=>n==="r"?a<.1?"Very Small":a<.3?"Small":a<.5?"Medium":"Large":n==="η²"?a<.01?"Very Small":a<.06?"Small":a<.14?"Medium":"Large":n==="Cramer's V"?a<.1?"Very Small":a<.3?"Small":a<.5?"Medium":"Large":"Unknown",te=a=>{var y,ne,N,P,H,ke,Ce,Ve;if(!a)return"";const{test:n,pValue:t,statistic:d,statName:o,effectSize:u,effectSizeName:m,groups:p,variables:r}=a;let l="";const V=t<.05;if(n==="Mann-Whitney U Test"){l=`A ${n} was conducted to compare ${p[0].name} (Median = ${p[0].median.toFixed(2)}) and ${p[1].name} (Median = ${p[1].median.toFixed(2)}).`,V?l+=` There was a statistically significant difference between the groups (${o} = ${d.toFixed(2)}, z = ${a.z.toFixed(2)}, p = ${t<.001?"< 0.001":t.toFixed(3)}).`:l+=` There was no statistically significant difference between the groups (${o} = ${d.toFixed(2)}, z = ${a.z.toFixed(2)}, p = ${t.toFixed(3)}).`;const g=ae(u,m);l+=` The effect size (${m} = ${u.toFixed(3)}) indicates a ${g.toLowerCase()} effect.`}else if(n==="Friedman Test"){const g=a;l=`A ${n} was conducted to compare the ranks of ${g.k} related conditions/variables for ${g.n} subjects.`,V?(l+=` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(y=g.statistic)==null?void 0:y.toFixed(2)}, p = ${t<.001?"< 0.001":t==null?void 0:t.toFixed(3)}).`,l+=" Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ."):l+=` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(ne=g.statistic)==null?void 0:ne.toFixed(2)}, p = ${t==null?void 0:t.toFixed(3)}).`}else if(n==="Wilcoxon Signed Rank Test"){l=`A ${n} was conducted to compare ${p[0].name} (Median = ${p[0].median.toFixed(2)}) and ${p[1].name} (Median = ${p[1].median.toFixed(2)}) in a within-subjects design with ${a.n} paired observations.`,V?l+=` There was a statistically significant difference between the paired measurements (${o} = ${d.toFixed(2)}, z = ${a.z.toFixed(2)}, p = ${t<.001?"< 0.001":t.toFixed(3)}).`:l+=` There was no statistically significant difference between the paired measurements (${o} = ${d.toFixed(2)}, z = ${a.z.toFixed(2)}, p = ${t.toFixed(3)}).`;const g=ae(u,m);l+=` The effect size (${m} = ${u.toFixed(3)}) indicates a ${g.toLowerCase()} effect.`}else if(n==="Friedman Test"){const g=a;l=`A ${n} was conducted to compare the ranks of ${g.k} related conditions/variables for ${g.n} subjects.`,V?(l+=` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(N=g.statistic)==null?void 0:N.toFixed(2)}, p = ${t<.001?"< 0.001":t==null?void 0:t.toFixed(3)}).`,l+=" Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ."):l+=` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(P=g.statistic)==null?void 0:P.toFixed(2)}, p = ${t==null?void 0:t.toFixed(3)}).`}else if(n==="Kruskal-Wallis Test"){if(l=`A ${n} was conducted to compare the effect of ${p.length} groups (${p.map(ve=>ve.name).join(", ")}) on the dependent variable.`,V){l+=` There was a statistically significant difference between the groups (${o} = ${d.toFixed(2)}, df = ${a.df}, p = ${t<.001?"< 0.001":t.toFixed(3)}).`;const ve=p.map(Ne=>`${Ne.name} (Median = ${Ne.median.toFixed(2)})`).join(", ");l+=` The medians were: ${ve}.`,l+=" Post-hoc pairwise comparisons would be needed to determine which specific groups differ from each other."}else l+=` There was no statistically significant difference between the groups (${o} = ${d.toFixed(2)}, df = ${a.df}, p = ${t.toFixed(3)}).`;const g=ae(u,m);l+=` The effect size (${m} = ${u.toFixed(3)}) indicates a ${g.toLowerCase()} effect.`}else if(n==="Friedman Test"){const g=a;l=`A ${n} was conducted to compare the ranks of ${g.k} related conditions/variables for ${g.n} subjects.`,V?(l+=` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(H=g.statistic)==null?void 0:H.toFixed(2)}, p = ${t<.001?"< 0.001":t==null?void 0:t.toFixed(3)}).`,l+=" Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ."):l+=` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(ke=g.statistic)==null?void 0:ke.toFixed(2)}, p = ${t==null?void 0:t.toFixed(3)}).`}else if(n==="Chi-Square Test of Independence"){l=`A ${n} was conducted to examine the relationship between ${r[0].name} and ${r[1].name}.`,V?(l+=` There was a statistically significant association between the variables (${o} = ${d.toFixed(2)}, df = ${a.df}, p = ${t<.001?"< 0.001":t.toFixed(3)}).`,l+=` This suggests that the distribution of ${r[1].name} differs depending on the category of ${r[0].name}.`):(l+=` There was no statistically significant association between the variables (${o} = ${d.toFixed(2)}, df = ${a.df}, p = ${t.toFixed(3)}).`,l+=` This suggests that the distribution of ${r[1].name} does not differ significantly based on the category of ${r[0].name}.`);const g=ae(u,m);l+=` The effect size (${m} = ${u.toFixed(3)}) indicates a ${g.toLowerCase()} effect.`}else if(n==="Friedman Test"){const g=a;l=`A ${n} was conducted to compare the ranks of ${g.k} related conditions/variables for ${g.n} subjects.`,V?(l+=` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(Ce=g.statistic)==null?void 0:Ce.toFixed(2)}, p = ${t<.001?"< 0.001":t==null?void 0:t.toFixed(3)}).`,l+=" Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ."):l+=` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${g.df}) = ${(Ve=g.statistic)==null?void 0:Ve.toFixed(2)}, p = ${t==null?void 0:t.toFixed(3)}).`}return l};return e.jsxs(Y,{p:3,children:[e.jsx(w,{variant:"h5",gutterBottom:!0,children:"Non-Parametric Tests"}),e.jsxs(J,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(w,{variant:"subtitle1",gutterBottom:!0,children:"Test Selection"}),e.jsxs($,{container:!0,spacing:2,children:[e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"dataset-select-label",children:"Dataset"}),e.jsx(A,{labelId:"dataset-select-label",id:"dataset-select",value:F,label:"Dataset",onChange:Ge,disabled:c.length===0,children:c.length===0?e.jsx(b,{value:"",disabled:!0,children:"No datasets available"}):c.map(a=>e.jsxs(b,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]})}),e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"test-type-label",children:"Test Type"}),e.jsxs(A,{labelId:"test-type-label",id:"test-type",value:f,label:"Test Type",onChange:Be,children:[e.jsxs(b,{value:"mannWhitney",children:["Mann-Whitney U Test",e.jsx(w,{variant:"caption",display:"block",color:"text.secondary",children:"Compare two independent groups (non-parametric alternative to t-test)"})]}),e.jsxs(b,{value:"wilcoxon",children:["Wilcoxon Signed-Rank Test",e.jsx(w,{variant:"caption",display:"block",color:"text.secondary",children:"Compare two related samples (non-parametric alternative to paired t-test)"})]}),e.jsxs(b,{value:"kruskalWallis",children:["Kruskal-Wallis Test",e.jsx(w,{variant:"caption",display:"block",color:"text.secondary",children:"Compare three or more independent groups (non-parametric alternative to ANOVA)"})]}),e.jsxs(b,{value:"chiSquare",children:["Chi-Square Test",e.jsx(w,{variant:"caption",display:"block",color:"text.secondary",children:"Test association between categorical variables"})]}),e.jsxs(b,{value:"friedman",children:["Friedman Test",e.jsx(w,{variant:"caption",display:"block",color:"text.secondary",children:"Compare three or more related groups (non-parametric Repeated Measures ANOVA)"})]})]})]})})]})]}),e.jsxs(J,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(w,{variant:"subtitle1",gutterBottom:!0,children:"Variable Selection"}),f==="mannWhitney"&&e.jsxs($,{container:!0,spacing:2,children:[e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable1-label",children:"Variable"}),e.jsx(A,{labelId:"variable1-label",id:"variable1",value:x,label:"Variable",onChange:fe,disabled:M.length===0,children:M.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables available"}):M.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]})}),e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"grouping-label",children:"Grouping Variable"}),e.jsxs(A,{labelId:"grouping-label",id:"grouping",value:K,label:"Grouping Variable",onChange:Oe,disabled:L.length===0,children:[e.jsx(b,{value:"",children:e.jsx("em",{children:"None (use two separate variables)"})}),L.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))]})]})}),K?e.jsxs(e.Fragment,{children:[e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"group1-label",children:"Group 1"}),e.jsx(A,{labelId:"group1-label",id:"group1",value:_,label:"Group 1",onChange:Pe,disabled:ue.length===0,children:ue.map(a=>e.jsx(b,{value:a,children:a},a))})]})}),e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"group2-label",children:"Group 2"}),e.jsx(A,{labelId:"group2-label",id:"group2",value:W,label:"Group 2",onChange:Ue,disabled:ue.length===0,children:ue.map(a=>e.jsx(b,{value:a,children:a},a))})]})})]}):e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable2-label",children:"Second Variable"}),e.jsx(A,{labelId:"variable2-label",id:"variable2",value:C,label:"Second Variable",onChange:je,disabled:M.length===0,children:M.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables available"}):M.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]})})]}),f==="wilcoxon"&&e.jsxs($,{container:!0,spacing:2,children:[e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable1-label",children:"First Variable"}),e.jsx(A,{labelId:"variable1-label",id:"variable1",value:x,label:"First Variable",onChange:fe,disabled:M.length===0,children:M.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables available"}):M.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]})}),e.jsxs($,{item:!0,xs:12,md:6,children:[e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable2-label",children:"Second Variable"}),e.jsx(A,{labelId:"variable2-label",id:"variable2",value:C,label:"Second Variable",onChange:je,disabled:M.length===0,children:M.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables available"}):M.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]}),e.jsx(w,{variant:"caption",color:"text.secondary",children:"Note: Wilcoxon test requires paired observations. Values from both variables will be paired by row."})]})]}),f==="chiSquare"&&e.jsxs($,{container:!0,spacing:2,children:[e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable1-label",children:"First Categorical Variable"}),e.jsx(A,{labelId:"variable1-label",id:"variable1",value:x,label:"First Categorical Variable",onChange:fe,disabled:L.length===0,children:L.length===0?e.jsx(b,{value:"",disabled:!0,children:"No categorical variables available"}):L.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]})}),e.jsxs($,{item:!0,xs:12,md:6,children:[e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable2-label",children:"Second Categorical Variable"}),e.jsx(A,{labelId:"variable2-label",id:"variable2",value:C,label:"Second Categorical Variable",onChange:je,disabled:L.length===0,children:L.length===0?e.jsx(b,{value:"",disabled:!0,children:"No categorical variables available"}):L.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]}),e.jsx(w,{variant:"caption",color:"text.secondary",children:"Note: Chi-Square test requires categorical variables with at least 2 categories each."})]})]}),f==="kruskalWallis"&&e.jsxs($,{container:!0,spacing:2,children:[e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"variable1-label",children:"Dependent Variable (Numeric)"}),e.jsx(A,{labelId:"variable1-label",id:"variable1",value:x,label:"Dependent Variable (Numeric)",onChange:fe,disabled:M.length===0,children:M.length===0?e.jsx(b,{value:"",disabled:!0,children:"No numeric variables available"}):M.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]})}),e.jsx($,{item:!0,xs:12,md:6,children:e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"factor-label",children:"Factor (Categorical)"}),e.jsx(A,{labelId:"factor-label",id:"factor",value:I,label:"Factor (Categorical)",onChange:Le,disabled:L.length===0,children:L.length===0?e.jsx(b,{value:"",disabled:!0,children:"No categorical variables available"}):L.map(a=>e.jsx(b,{value:a.id,children:a.name},a.id))})]})}),I&&me.length>0&&e.jsxs($,{item:!0,xs:12,md:6,children:[e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"groups-label",children:"Groups to Include (Optional)"}),e.jsx(A,{labelId:"groups-label",id:"groups",multiple:!0,value:U,label:"Groups to Include (Optional)",onChange:Ke,renderValue:a=>a.join(", "),children:me.map(a=>e.jsx(b,{value:a,children:a},a))})]}),e.jsx(w,{variant:"caption",color:"text.secondary",children:"If no groups are selected, all groups will be included. Kruskal-Wallis test requires at least 3 groups."})]})]}),f==="friedman"&&e.jsx($,{container:!0,spacing:2,children:e.jsxs($,{item:!0,xs:12,children:[e.jsxs(D,{fullWidth:!0,margin:"normal",children:[e.jsx(q,{id:"friedman-variables-label",children:"Repeated Measures Variables (≥3)"}),e.jsx(A,{labelId:"friedman-variables-label",id:"friedman-variables",multiple:!0,value:X,onChange:_e,label:"Repeated Measures Variables (≥3)",renderValue:a=>e.jsx(Y,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:a.map(n=>{var d;const t=((d=M.find(o=>o.id===n))==null?void 0:d.name)||n;return e.jsx(Qe,{label:t},n)})}),disabled:M.length<3,children:M.map(a=>e.jsxs(b,{value:a.id,children:[e.jsx(Je,{checked:X.indexOf(a.id)>-1}),e.jsx(Ye,{primary:a.name})]},a.id))})]}),e.jsx(w,{variant:"caption",color:"text.secondary",children:"Friedman test requires at least 3 numeric variables representing repeated measurements on the same subjects."})]})}),e.jsx(Y,{mt:2,children:e.jsxs(Ze,{variant:"contained",color:"primary",startIcon:e.jsx(ea,{}),onClick:He,disabled:he||!Te(),children:["Run ",f==="mannWhitney"?"Mann-Whitney U Test":f==="wilcoxon"?"Wilcoxon Signed-Rank Test":f==="kruskalWallis"?"Kruskal-Wallis Test":f==="chiSquare"?"Chi-Square Test":f==="friedman"?"Friedman Test":"Test"]})})]}),he&&e.jsx(Y,{display:"flex",justifyContent:"center",my:4,children:e.jsx(aa,{})}),we&&e.jsx(Ie,{severity:"error",sx:{mb:3},children:we}),s&&!he&&f!=="friedman"&&e.jsxs(J,{elevation:2,sx:{p:2,mb:3},children:[e.jsxs(w,{variant:"h6",gutterBottom:!0,children:[s.test," Results"]}),e.jsx(w,{variant:"body2",color:"text.secondary",sx:{mb:2},children:s.description}),e.jsxs($,{container:!0,spacing:3,children:[e.jsxs($,{item:!0,xs:12,md:6,children:[e.jsx(w,{variant:"subtitle2",gutterBottom:!0,children:"Test Statistics"}),e.jsx(ie,{children:e.jsxs(se,{size:"small",children:[e.jsx(ge,{children:e.jsxs(z,{children:[e.jsx(h,{children:"Statistic"}),e.jsx(h,{align:"right",children:"Value"})]})}),e.jsxs(re,{children:[e.jsxs(z,{children:[e.jsxs(h,{children:[s.statName,"-statistic"]}),e.jsx(h,{align:"right",children:s.statistic.toFixed(4)})]}),s.z!==void 0&&e.jsxs(z,{children:[e.jsx(h,{children:"z-score"}),e.jsx(h,{align:"right",children:s.z.toFixed(4)})]}),s.df!==void 0&&e.jsxs(z,{children:[e.jsx(h,{children:"Degrees of Freedom"}),e.jsx(h,{align:"right",children:s.df})]}),e.jsxs(z,{children:[e.jsx(h,{children:"p-value"}),e.jsx(h,{align:"right",sx:{color:s.pValue<.05?"success.main":"inherit",fontWeight:s.pValue<.05?"bold":"normal"},children:s.pValue<.001?"< 0.001":s.pValue.toFixed(4)})]}),e.jsxs(z,{children:[e.jsxs(h,{children:["Effect Size (",s.effectSizeName,")"]}),e.jsxs(h,{align:"right",children:[s.effectSize.toFixed(4),e.jsx(w,{variant:"caption",display:"block",color:"text.secondary",children:ae(s.effectSize,s.effectSizeName)})]})]})]})]})})]}),e.jsx($,{item:!0,xs:12,md:6,children:s.test==="Chi-Square Test of Independence"?e.jsxs(e.Fragment,{children:[e.jsx(w,{variant:"subtitle2",gutterBottom:!0,children:"Contingency Table"}),s.hasLowExpectedFrequencies&&e.jsx(Ie,{severity:"warning",sx:{mb:2},children:e.jsx(w,{variant:"body2",children:"Some expected cell frequencies are less than 5, which may make the Chi-Square approximation inaccurate. Consider combining categories or using Fisher's exact test for small samples."})}),s.contingencyTable&&e.jsx(ie,{children:e.jsxs(se,{size:"small",children:[e.jsx(ge,{children:e.jsxs(z,{children:[e.jsxs(h,{children:[s.variables[0].name," \\ ",s.variables[1].name]}),s.contingencyTable.columnCategories.map(a=>e.jsx(h,{align:"right",children:a},a)),e.jsx(h,{align:"right",children:"Total"})]})}),e.jsxs(re,{children:[s.contingencyTable.rowCategories.map(a=>e.jsxs(z,{children:[e.jsx(h,{children:a}),s.contingencyTable.columnCategories.map(n=>e.jsx(h,{align:"right",children:s.contingencyTable.frequencies[a][n]||0},n)),e.jsx(h,{align:"right",children:s.contingencyTable.rowTotals[a]})]},a)),e.jsxs(z,{children:[e.jsx(h,{children:e.jsx("strong",{children:"Total"})}),s.contingencyTable.columnCategories.map(a=>e.jsx(h,{align:"right",children:e.jsx("strong",{children:s.contingencyTable.columnTotals[a]})},a)),e.jsx(h,{align:"right",children:e.jsx("strong",{children:s.contingencyTable.grandTotal})})]})]})]})}),s.chartData&&s.chartData.length>0&&e.jsx(Y,{height:300,mt:2,children:e.jsx(We,{width:"100%",height:"100%",children:e.jsxs(Me,{data:s.chartData,margin:{top:20,right:30,left:20,bottom:10},children:[e.jsx(ze,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"category"}),e.jsx(Re,{}),e.jsx(De,{}),e.jsx(ca,{}),s.contingencyTable&&s.contingencyTable.columnCategories.map((a,n)=>e.jsx(qe,{dataKey:a,stackId:"a",fill:`hsl(${n*137.5%360}, 70%, 50%)`},a))]})})})]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{variant:"subtitle2",gutterBottom:!0,children:"Descriptive Statistics"}),e.jsx(ie,{children:e.jsxs(se,{size:"small",children:[e.jsx(ge,{children:e.jsxs(z,{children:[e.jsx(h,{children:"Group"}),e.jsx(h,{align:"right",children:"N"}),e.jsx(h,{align:"right",children:"Median"}),e.jsx(h,{align:"right",children:"Mean"})]})}),e.jsx(re,{children:s.groups&&s.groups.map((a,n)=>e.jsxs(z,{children:[e.jsx(h,{children:a.name}),e.jsx(h,{align:"right",children:a.n}),e.jsx(h,{align:"right",children:a.median.toFixed(4)}),e.jsx(h,{align:"right",children:a.mean.toFixed(4)})]},n))})]})}),s.chartData&&s.chartData.length>0&&e.jsx(Y,{height:200,mt:2,children:e.jsx(We,{width:"100%",height:"100%",children:e.jsxs(Me,{data:s.chartData,margin:{top:20,right:30,left:20,bottom:10},children:[e.jsx(ze,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"name"}),e.jsx(Re,{}),e.jsx(De,{}),e.jsx(qe,{dataKey:"value",fill:S.palette.primary.main,name:"Median",children:s.chartData.map((a,n)=>e.jsx(da,{fill:`${S.palette.primary.main}${n%2?"CC":"FF"}`},`cell-${n}`))})]})})})]})}),e.jsx($,{item:!0,xs:12,children:e.jsxs(J,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(w,{variant:"subtitle2",gutterBottom:!0,children:"Interpretation"}),e.jsx(w,{variant:"body2",children:s.interpretation})]})})]})]}),s&&!he&&f==="friedman"&&e.jsxs(J,{elevation:2,sx:{p:2,mb:3},children:[e.jsxs(w,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(ta,{sx:{mr:1,color:S.palette.info.main}}),"Friedman Test Results"]}),e.jsxs(w,{variant:"body1",gutterBottom:!0,children:[e.jsx("strong",{children:"Interpretation:"})," ",s.interpretation]}),e.jsx(ie,{component:J,sx:{mt:2,mb:2},children:e.jsx(se,{size:"small",children:e.jsxs(re,{children:[e.jsxs(z,{children:[e.jsx(h,{children:e.jsx("strong",{children:"Statistic (Chi-squared)"})}),e.jsx(h,{children:(Se=s.statistic)==null?void 0:Se.toFixed(3)})]}),e.jsxs(z,{children:[e.jsx(h,{children:e.jsx("strong",{children:"Degrees of Freedom (df)"})}),e.jsx(h,{children:s.df})]}),e.jsxs(z,{children:[e.jsx(h,{children:e.jsx("strong",{children:"P-value"})}),e.jsx(h,{sx:{color:s.pValue<.05?"success.main":"inherit",fontWeight:s.pValue<.05?"bold":"normal"},children:s.pValue<.001?"< 0.001":(Fe=s.pValue)==null?void 0:Fe.toFixed(5)})]}),e.jsxs(z,{children:[e.jsx(h,{children:e.jsx("strong",{children:"Number of Subjects (N)"})}),e.jsx(h,{children:s.n})]}),e.jsxs(z,{children:[e.jsx(h,{children:e.jsx("strong",{children:"Number of Conditions (k)"})}),e.jsx(h,{children:s.k})]})]})})}),s.meanRanks&&s.meanRanks.length>0&&i&&X.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(w,{variant:"subtitle1",gutterBottom:!0,sx:{mt:2},children:"Mean Ranks for Variables:"}),e.jsx(ie,{component:J,sx:{mb:2},children:e.jsxs(se,{size:"small",children:[e.jsx(ge,{children:e.jsxs(z,{children:[e.jsx(h,{children:"Variable"}),e.jsx(h,{align:"right",children:"Mean Rank"})]})}),e.jsx(re,{children:X.map((a,n)=>{var t,d;if(s.meanRanks&&n<s.meanRanks.length){const o=((t=i.columns.find(u=>u.id===a))==null?void 0:t.name)||`Variable ${n+1}`;return e.jsxs(z,{children:[e.jsx(h,{children:o}),e.jsx(h,{align:"right",children:(d=s.meanRanks[n])==null?void 0:d.toFixed(2)})]},a)}return null})})]})})]}),e.jsxs(J,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(w,{variant:"subtitle2",gutterBottom:!0,children:"Interpretation"}),e.jsx(w,{variant:"body2",children:te(s)})]})]})]})};export{Va as default};
