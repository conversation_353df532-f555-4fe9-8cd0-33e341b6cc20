import{u as Ne,j as e,B as f,R as J,aF as Te,e as I,G as x,ai as W,b9 as $,ba as M,bb as C,aE as L,I as O,Q as _e,br as Ee,h as De,c9 as Re,g as ue,af as he,f as We,ae as me,ac as $e,ca as Me,b7 as Le,ah as B,c8 as Oe,aj as Be,bc as Ge,bH as qe}from"./mui-libs-CfwFIaTD.js";import{r as b,b as ge}from"./react-libs-Cr2nE3UY.js";import{a as Fe,D as Ke}from"./index-Bpan7Tbe.js";import{l as F,P as Ye}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./other-utils-CR9xr_gI.js";import"./charts-recharts-d3-BEF1Y_jn.js";const He={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],muted:["#6b7280","#9ca3af","#d1d5db","#374151","#4b5563","#6b7280","#9ca3af","#d1d5db"],categorical:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5"],viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],plasma:["#0d0887","#5302a3","#8b0aa5","#b83289","#db5c68","#f48849","#febd2a","#f0f921"]},Ue={title:"Sankey Diagram",nodeThickness:20,nodePadding:10,linkOpacity:.6,colorScheme:"muted",showValues:!0,fontSize:12,arrangement:"snap"},Z="plotlySankeyDiv",rt=()=>{const{datasets:K,currentDataset:S}=Fe(),fe=Ne(),k=b.useRef(null),[_,ee]=b.useState((S==null?void 0:S.id)||""),[A,Y]=b.useState(""),[u,E]=b.useState([]),[N,H]=b.useState(""),[l,xe]=b.useState(Ue),[U,be]=b.useState(!1),[z,w]=b.useState(null),[D,te]=b.useState(!1),[Q,v]=b.useState(null),h=ge.useMemo(()=>_&&K.find(t=>t.id===_)||null,[K,_]),V=ge.useMemo(()=>(h==null?void 0:h.columns.filter(t=>t.type===Ke.CATEGORICAL))||[],[h]);b.useEffect(()=>{S!=null&&S.id&&S.id!==_&&(ee(S.id),Y(""),E([]),H(""),w(null),v(null))},[S]),b.useEffect(()=>{if(z&&k.current){const t={responsive:!0};F.newPlot(Z,z.data,z.layout,t)}return()=>{k.current&&typeof Ye<"u"&&F.purge&&F.purge(k.current)}},[z]);const pe=t=>{ee(t.target.value),Y(""),E([]),H(""),w(null),v(null)},ve=t=>{Y(t.target.value),w(null),v(null)},je=(t,n)=>{const i=[...u];i[t]=n,E(i),w(null),v(null)},ye=()=>{u.length<5&&E([...u,""])},Se=t=>{const n=u.filter((i,c)=>c!==t);E(n),w(null),v(null)},Ce=t=>{H(t.target.value),w(null),v(null)},P=(t,n)=>{xe(i=>({...i,[t]:n}))},we=()=>{if(!h)return"Please select a dataset.";if(!A)return"Please select a source variable.";if(!N)return"Please select a target variable.";const t=[A,...u.filter(r=>r),N];if(new Set(t).size!==t.length)return"All selected variables must be different.";u.filter(r=>r);const i=t.map(r=>V.find(o=>o.id===r));if(i.some(r=>!r))return"One or more selected variables not found.";const c=h.data.filter(r=>i.every(o=>o&&r[o.name]!=null&&r[o.name]!==""));return c.length<2?"Insufficient data. Need at least 2 valid rows across all selected variables.":i.reduce((r,o)=>{if(!o)return r;const T=new Set(c.map(R=>String(R[o.name])));return r+T.size},0)<Math.max(2,t.length)?"Need more unique categories across all variables for meaningful flow visualization.":null},ae=()=>{const t=we();if(t){v(t);return}te(!0),v(null),w(null);try{const n=u.filter(a=>a),c=[A,...n,N].map(a=>V.find(s=>s.id===a)),se=h.data.filter(a=>c.every(s=>a[s.name]!=null&&a[s.name]!=="")),r=new Map,o=new Set;se.forEach(a=>{if(c.length===2){const s=c[0],m=c[1],j=String(a[s.name]),p=String(a[m.name]),y=`0_${j}`,d=`1_${p}`;o.add(y),o.add(d);const g=`${y}→${d}`;r.set(g,(r.get(g)||0)+1)}else for(let s=0;s<c.length-1;s++){const m=c[s],j=c[s+1],p=String(a[m.name]),y=String(a[j.name]),d=`${s}_${p}`,g=`${s+1}_${y}`;o.add(d),o.add(g);const q=`${d}→${g}`;r.set(q,(r.get(q)||0)+1)}});const T=Array.from(o).sort((a,s)=>{const[m,j]=a.split("_",2),[p,y]=s.split("_",2),d=parseInt(m),g=parseInt(p);return d!==g?d-g:j.localeCompare(y)}),R=new Map;T.forEach((a,s)=>{R.set(a,s)});const X=[],re=[],le=[],ne=[];r.forEach((a,s)=>{const[m,j]=s.split("→"),p=R.get(m),y=R.get(j);X.push(p),re.push(y),le.push(a);const d=m.split("_",2)[1],g=j.split("_",2)[1];ne.push(`${d} → ${g}: ${a}`)});const G=He[l.colorScheme],ie=[],oe=[],de=[],ce=[],ke=c.length;T.forEach((a,s)=>{const[m,j]=a.split("_",2),p=parseInt(m),y=p/(ke-1);de.push(y);const d=T.filter(Ae=>Ae.startsWith(`${p}_`)),g=d.indexOf(a),q=d.length>1?g/(d.length-1):.5;ce.push(q),ie.push(G[p%G.length]),oe.push(j)});const ze=X.map(a=>{const s=T[a],m=parseInt(s.split("_",2)[0]);return G[m%G.length]+Math.round(l.linkOpacity*255).toString(16).padStart(2,"0")}),Ve=[{type:"sankey",node:{pad:l.nodePadding,thickness:l.nodeThickness,line:{color:fe.palette.divider,width:1},label:oe,color:ie,x:de,y:ce},link:{source:X,target:re,value:le,color:ze,label:l.showValues?ne:void 0}}],Pe={title:{text:l.title,font:{size:l.fontSize+4}},font:{size:l.fontSize},paper_bgcolor:"transparent",plot_bgcolor:"transparent",margin:{l:50,r:50,t:80,b:50}};w({data:Ve,layout:Pe})}catch(n){console.error("Sankey generation error:",n),v(n instanceof Error?n.message:"An error occurred while generating the Sankey diagram")}finally{te(!1)}},Ie=()=>{if(k.current&&z){const t={format:"svg",filename:l.title.replace(/\s+/g,"_")||"sankey_diagram",width:k.current.offsetWidth||800,height:k.current.offsetHeight||600};F.downloadImage(Z,t)}else v("Chart data not available for download.")};return e.jsxs(f,{sx:{width:"100%"},children:[e.jsx(J,{elevation:0,variant:"outlined",sx:{p:2,mb:2,bgcolor:"grey.50"},children:e.jsxs(f,{sx:{display:"flex",alignItems:"flex-start",gap:1},children:[e.jsx(Te,{color:"primary",sx:{mt:.5,fontSize:20}}),e.jsxs(f,{children:[e.jsx(I,{variant:"subtitle2",color:"primary",gutterBottom:!0,children:"About Multi-Level Sankey Diagrams"}),e.jsx(I,{variant:"body2",color:"text.secondary",children:"Sankey diagrams visualize flow relationships between categorical variables. Select a source and target variable to create basic flow diagrams, or add intermediate variables for multi-stage pathway visualizations. The width of each flow represents the frequency of cases moving through each stage. Perfect for analyzing career progressions, customer journeys, academic pathways, and other multi-step processes."})]})]})}),e.jsxs(J,{elevation:0,variant:"outlined",sx:{p:2},children:[e.jsxs(x,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(x,{item:!0,xs:12,children:e.jsxs(W,{fullWidth:!0,size:"small",children:[e.jsx($,{children:"Dataset"}),e.jsx(M,{value:_,label:"Dataset",onChange:pe,children:K.map(t=>e.jsx(C,{value:t.id,children:t.name},t.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(W,{fullWidth:!0,size:"small",disabled:!h,children:[e.jsx($,{children:"Source Variable (Start)"}),e.jsx(M,{value:A,label:"Source Variable (Start)",onChange:ve,children:V.map(t=>e.jsx(C,{value:t.id,children:t.name},t.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(W,{fullWidth:!0,size:"small",disabled:!h,children:[e.jsx($,{children:"Target Variable (End)"}),e.jsx(M,{value:N,label:"Target Variable (End)",onChange:Ce,children:V.map(t=>e.jsx(C,{value:t.id,children:t.name},t.id))})]})})]}),e.jsxs(f,{sx:{mb:2},children:[e.jsxs(f,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1},children:[e.jsx(I,{variant:"subtitle2",color:"text.primary",children:"Intermediate Variables (Pathway Steps) - Optional"}),e.jsx(f,{children:e.jsx(L,{title:"Add intermediate variable",children:e.jsx("span",{children:e.jsx(O,{onClick:ye,disabled:!h||u.length>=5,size:"small",color:"primary",children:e.jsx(_e,{})})})})})]}),u.length>0?e.jsx(Ee,{spacing:1,children:u.map((t,n)=>e.jsxs(f,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(De,{label:`Step ${n+1}`,size:"small",color:"primary",variant:"outlined",sx:{minWidth:70}}),e.jsxs(W,{fullWidth:!0,size:"small",disabled:!h,children:[e.jsx($,{children:"Select intermediate variable"}),e.jsx(M,{value:t,label:"Select intermediate variable",onChange:i=>je(n,i.target.value),children:V.map(i=>e.jsx(C,{value:i.id,children:i.name},i.id))})]}),e.jsx(L,{title:"Remove this intermediate variable",children:e.jsx("span",{children:e.jsx(O,{onClick:()=>Se(n),size:"small",color:"error",children:e.jsx(Re,{})})})})]},n))}):e.jsx(f,{sx:{p:2,border:"1px dashed",borderColor:"divider",borderRadius:1,textAlign:"center",bgcolor:"grey.50"},children:e.jsx(I,{variant:"body2",color:"text.secondary",children:"No intermediate variables added. Click the + button to add pathway steps, or generate a direct flow diagram."})}),e.jsx(I,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:u.length>0?"Add more intermediate variables to create multi-stage flow paths. Each step will show transitions from the previous stage.":"Intermediate variables are optional. Without them, you'll get a direct flow from source to target variable."})]}),h&&V.length<2&&e.jsxs(ue,{severity:"warning",sx:{mb:2},children:[e.jsx(he,{children:"Insufficient Categorical Variables"}),"This dataset has only ",V.length," categorical variable(s). Sankey diagrams require at least 2 categorical variables (source + target). Add intermediate variables for multi-level flow visualization."]}),e.jsxs(f,{sx:{display:"flex",gap:1,mb:2,alignItems:"center"},children:[e.jsx(We,{variant:"contained",onClick:ae,disabled:!A||!N||D,startIcon:D?e.jsx(me,{size:16}):e.jsx($e,{}),children:D?"Generating...":u.filter(t=>t).length>0?"Generate Multi-Level Sankey":"Generate Sankey Diagram"}),e.jsx(L,{title:"Chart Settings",children:e.jsx(O,{onClick:()=>be(!U),color:U?"primary":"default",size:"small",children:e.jsx(Me,{})})}),e.jsx(L,{title:"Download SVG",children:e.jsx("span",{children:e.jsx(O,{onClick:Ie,disabled:!z,size:"small",children:e.jsx(Le,{})})})})]}),U&&e.jsxs(J,{elevation:0,variant:"outlined",sx:{p:2,mb:2,bgcolor:"grey.50"},children:[e.jsx(I,{variant:"subtitle2",gutterBottom:!0,children:"Chart Settings"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsx(B,{fullWidth:!0,label:"Chart Title",value:l.title,onChange:t=>P("title",t.target.value),size:"small"})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(W,{fullWidth:!0,size:"small",children:[e.jsx($,{children:"Color Scheme"}),e.jsxs(M,{value:l.colorScheme,label:"Color Scheme",onChange:t=>P("colorScheme",t.target.value),children:[e.jsx(C,{value:"muted",children:"Muted"}),e.jsx(C,{value:"default",children:"Default"}),e.jsx(C,{value:"categorical",children:"Categorical"}),e.jsx(C,{value:"viridis",children:"Viridis"}),e.jsx(C,{value:"plasma",children:"Plasma"})]})]})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(B,{fullWidth:!0,label:"Node Thickness",type:"number",value:l.nodeThickness,onChange:t=>P("nodeThickness",Number(t.target.value)),size:"small",inputProps:{min:5,max:50}})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(B,{fullWidth:!0,label:"Node Padding",type:"number",value:l.nodePadding,onChange:t=>P("nodePadding",Number(t.target.value)),size:"small",inputProps:{min:5,max:50}})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(B,{fullWidth:!0,label:"Link Opacity",type:"number",value:l.linkOpacity,onChange:t=>P("linkOpacity",Number(t.target.value)),size:"small",inputProps:{min:.1,max:1,step:.1}})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(B,{fullWidth:!0,label:"Font Size",type:"number",value:l.fontSize,onChange:t=>P("fontSize",Number(t.target.value)),size:"small",inputProps:{min:8,max:24}})}),e.jsx(x,{item:!0,xs:12,children:e.jsx(Oe,{children:e.jsx(Be,{control:e.jsx(Ge,{checked:l.showValues,onChange:t=>P("showValues",t.target.checked)}),label:"Show flow values on hover"})})})]})]}),e.jsxs(f,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(I,{variant:"h6",children:l.title}),e.jsx(f,{children:e.jsx(L,{title:"Refresh Chart",children:e.jsx(O,{onClick:ae,size:"small",disabled:!A||!N,children:e.jsx(qe,{})})})})]}),e.jsxs(f,{ref:k,id:Z,sx:{height:500,width:"100%",display:"flex",justifyContent:"center",alignItems:"center",border:"1px solid",borderColor:"divider",borderRadius:1,bgcolor:"background.paper"},children:[D&&e.jsx(me,{}),!D&&!z&&!Q&&e.jsx(I,{variant:"body1",color:"text.secondary",children:"Select source and target variables, then generate the diagram. Add intermediate variables for multi-level flows."}),Q&&e.jsxs(ue,{severity:"error",sx:{m:2,maxWidth:"80%"},children:[e.jsx(he,{children:"Error"}),Q]})]})]})]})};export{rt as default};
