import{j as L}from"./other-utils-CR9xr_gI.js";import{b as V,d as B}from"./descriptive-Djo0s6H4.js";const W=o=>{if(o.length<2)throw new Error("At least 2 groups required for ANOVA");const l=o.map(t=>t.length),s=o.map(t=>V(t)),d=o.map(t=>B(t,!0)),f=l.reduce((t,v)=>t+v,0);let m=0;o.forEach(t=>{t.forEach(v=>{m+=v})});const a=m/f;let c=0;for(let t=0;t<o.length;t++)c+=l[t]*Math.pow(s[t]-a,2);let i=0;for(let t=0;t<o.length;t++)i+=(l[t]-1)*d[t];const u=c+i,p=o.length-1,r=f-o.length,w=c/p,g=i/r,A=w/g,M=1-L.centralF.cdf(A,p,r),b=c/u;return{F:A,dfBetween:p,dfWithin:r,pValue:M,etaSquared:b,means:s,grandMean:a}},O=(...o)=>{const l=o[o.length-1];let s,d={};Array.isArray(l)&&typeof l[0]=="number"?s=o:(s=o.slice(0,-1),d=l);const f=d.variant||"median",m=d.alpha||.05;if(s.length<2)throw new Error("At least 2 groups are required for Levene's test.");const a=s.length,c=s.reduce((e,n)=>e+n.length,0);if(a<2)throw new Error("At least two groups are required to calculate Levene's test degrees of freedom.");if(c<=a)throw new Error("Total observations must be greater than the number of groups for valid Levene's test df.");const i=a-1,u=c-a,p=s.map(e=>{if(f==="mean")return V(e);{const n=[...e].sort((j,q)=>j-q),h=Math.floor(n.length/2);return n.length%2===0?(n[h-1]+n[h])/2:n[h]}}),r=s.map((e,n)=>e.map(h=>Math.abs(h-p[n]))),w=r.map(e=>e.reduce((n,h)=>n+h,0)/e.length),g=r.flat(),A=g.reduce((e,n)=>e+n,0)/g.length;let M=0;for(let e=0;e<a;e++)M+=s[e].length*Math.pow(w[e]-A,2);let b=0;for(let e=0;e<a;e++)for(let n=0;n<r[e].length;n++)b+=Math.pow(r[e][n]-w[e],2);const t=M/i,v=b/u,S=t/v,E=1-L.centralF.cdf(S,i,u),y=f==="mean"?"Levene test based on mean":"Levene test based on median (Brown-Forsythe variant)";return{statistic:S,pValue:E,df:[i,u],rejected:E<m,alpha:m,method:y,variant:f}};export{W as o,O as p};
