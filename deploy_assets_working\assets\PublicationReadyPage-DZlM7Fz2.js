import{j as a,B as e,e as r,C as n}from"./mui-libs-CfwFIaTD.js";import{r as c}from"./react-libs-Cr2nE3UY.js";import{b as u,u as d}from"./index-Bpan7Tbe.js";import{P as l}from"./PublicationReadyOptions-BG4CsEaB.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const g=({onNavigate:o})=>{const{canAccessPublicationReady:t,loading:s}=u(),i=d();return c.useEffect(()=>{!s&&!t&&i("/dashboard",{state:{message:"Access to Publication Ready features requires Pro or Educational account, or use as Guest with sample data."}})},[t,s,i]),s?a.jsx(e,{sx:{p:3},children:a.jsx(r,{variant:"h6",children:"Loading..."})}):t?a.jsx(n,{maxWidth:"lg",children:a.jsx(e,{sx:{mt:4,mb:4},children:a.jsx(l,{onNavigate:o})})}):a.jsxs(e,{sx:{p:3},children:[a.jsx(r,{variant:"h6",children:"Access Denied"}),a.jsx(r,{variant:"body2",color:"text.secondary",children:"Publication Ready features require Pro or Educational account, or use as Guest with sample data."})]})};export{g as default};
