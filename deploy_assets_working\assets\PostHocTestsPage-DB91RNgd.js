import{j as t}from"./mui-libs-CfwFIaTD.js";import o from"./PostHocTests-BRrhMOQ0.js";import"./react-libs-Cr2nE3UY.js";import"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";import"./AddToResultsButton-BwSXKCt2.js";import"./PublicationReadyGate-BGFbKbJc.js";const d=()=>t.jsxs("div",{children:[t.jsx("h1",{children:"PostHoc Tests"}),t.jsx(o,{})]});export{d as default};
