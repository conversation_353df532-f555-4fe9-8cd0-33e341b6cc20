const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DashboardPage-48jtFBKD.js","./mui-libs-CfwFIaTD.js","./react-libs-Cr2nE3UY.js","./index-Bpan7Tbe.js","./supabase-lib-B3goak-P.js","./ml-tensorflow-D19WVUQh.js","./charts-plotly-BhN4fPIu.js","./charts-recharts-d3-BEF1Y_jn.js","./charts-plotly-CuCRB34y.css","./other-utils-CR9xr_gI.js","./index-DyRZcmzc.css","./HomePage-DHzfMv48.js","./AnalysisSteps-CmfAFU5C.js","./PageTitle-DA3BXQ4x.js","./StatsCard-op8tGQ0a.js","./VariableSelector-CPdlCsJ2.js","./Auth-_Z-L5t-3.js","./countries-BGaMXFfG.js","./FeatureComparisonTable-D0Q0fTUa.js","./UserProfile-_iqLfjt2.js","./Settings-CC7UG-TA.js","./UpdatePassword-CUJo7SRB.js","./AnalysisAssistantPage-CqoSPBfZ.js","./descriptive-Djo0s6H4.js","./math-setup-BTRs7Kau.js","./math-lib-BOZ-XUok.js","./normality-CwHD6Rjl.js","./trainingDataInitializer-DhlysJr9.js","./WhichTestPage-BVqGiyoM.js","./VisualizationGuidePage-CGxTob5K.js","./StatisticalMethodsPage-DO96Z5ro.js","./KnowledgeBasePage-1nzuPqIf.js","./TutorialPage-D7kG4YnK.js","./katex-ChWnQ-fc.js","./VideoTutorialsPage-CPJt_uWM.js","./AnalysisIndexPage-BvKbf9Gr.js","./DescriptiveStatsOptions-MCsgZr0u.js","./InferentialStatsOptions-Cv31a_py.js","./CorrelationAnalysisOptions-X530BGye.js","./AdvancedAnalysisOptions-BMl4jxNG.js","./EpiCalcOptions-RMQOqOWt.js","./SampleSizeCalculatorsOptions-DlONI60r.js","./DataVisualizationOptions-B6r9UVDP.js","./DataManagementOptions-BATjkCpy.js","./PublicationReadyOptions-BG4CsEaB.js","./PricingPage-gZ3rfXmZ.js","./NotificationsPage-CJRFU61k.js","./RouterTest-C8CpCslx.js","./DataManagementPage-BRk0DtCj.js","./index-v_pkX9nf.js","./DatasetSelector-G08QHuaN.js","./TabPanel-CVuv1-VX.js","./DescriptiveStatsPage-Dax3LBrL.js","./index-DqymCIs-.js","./InferentialStatsPage-CnZG1DdU.js","./index-DjpOF40k.js","./TTests-CqoYonWk.js","./t-tests-DXw1R1jD.js","./anova-DbTY6dHK.js","./non-parametric-Cf6Ds91x.js","./ANOVA-VqPPZ-TR.js","./OneWayANOVA-ai9Q6gnX.js","./TwoWayANOVA-CiVErBBN.js","./twoWayANOVA-CIsn6bwj.js","./RepeatedMeasuresANOVA-DwrjssMW.js","./repeatedMeasuresANOVA-B8bv3NGM.js","./NonParametricTests-CnQbYnts.js","./TTestWorkflowPage-CdvNLcMj.js","./GuidedWorkflow-MMwzUT8W.js","./ANOVAWorkflowPage-BelY7RhI.js","./DataVisualizationPage-BkCc12HU.js","./index-Bd7Staio.js","./CorrelationAnalysisPage-C4yIGAB2.js","./index-BHSDXLLS.js","./regression-BKqSTW2N.js","./PivotAnalysisViewer-CUgNzlbS.js","./PivotAnalysisViewer-ByF4D01r.css","./OneSampleCalculator-DdSwydDx.js","./sampleSize-nX7GCAZC.js","./TwoSampleCalculator-cqqzlW-p.js","./PairedSampleCalculator-B2azJaVa.js","./MoreThanTwoGroupsCalculator-DCYA-CIl.js","./EpiCalcPage-BSSz3cCG.js","./EpiCalc-BuVTzyEC.js","./CrossSectionalCalculator-CiCHocwD.js","./CaseControlCalculator-BzLsSUO6.js","./CohortCalculator-Ceh0DWHw.js","./MatchedCaseControlCalculator-XX9Fsb_b.js","./SampleSizePowerCalculator-Czye5N6w.js","./AdvancedAnalysisPage-BJQTFL6B.js","./SurvivalAnalysis-Dzi5rTjL.js","./coxRegressionService-CqIkroID.js","./ExploratoryFactorAnalysis-DKexHXUh.js","./ConfirmatoryFactorAnalysis-D0HSoWGO.js","./MetaAnalysis-C_ep_tOv.js","./ReliabilityAnalysis-BP7J0pqF.js","./ClusterAnalysis-Dwkqf03e.js","./MediationModeration-BLkQPilK.js","./VariableTree-BL5rEhkE.js","./PublicationReadyPage-DZlM7Fz2.js","./Table1-B7BcroXM.js","./AddToResultsButton-BwSXKCt2.js","./PublicationReadyGate-BGFbKbJc.js","./ConvertToAPA-C3MGbZit.js","./SMDTable-BZazxrfM.js","./Table1a-1M0jF3L-.js","./Table1b-ByG5YXGI.js","./FlowDiagram-CHRbazj7.js","./FlowDiagram-DX7sLWOj.css","./RegressionTable-BuRxA8Iu.js","./StatisticalMethodsGenerator-C5cIAvRq.js","./ResultsManagerPage-BSs6I6Q7.js","./Table2-Bf2a6rrV.js","./PostHocTests-BRrhMOQ0.js","./DevTrainingPage-A92mzUCf.js","./RouteTestPage-DN2MxV_Q.js","./AuthTestPage-DhFDKCY6.js","./EducationalTierTest-BNrDUfgg.js","./AdminDashboardPage-SPi3xD-N.js"])))=>i.map(i=>d[i]);
import{r as d}from"./index-Bpan7Tbe.js";import{_ as t}from"./supabase-lib-B3goak-P.js";import{r as e}from"./react-libs-Cr2nE3UY.js";import"./mui-libs-CfwFIaTD.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";import"./ml-tensorflow-D19WVUQh.js";const b=e.lazy(()=>t(()=>import("./DashboardPage-48jtFBKD.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]),import.meta.url));e.lazy(()=>t(()=>import("./HomePage-DHzfMv48.js"),__vite__mapDeps([11,1,2,3,4,5,6,7,8,9,10,12,13,14,15]),import.meta.url));const h=e.lazy(()=>t(()=>import("./Auth-_Z-L5t-3.js"),__vite__mapDeps([16,1,2,3,4,5,6,7,8,9,10,17,18]),import.meta.url)),q=e.lazy(()=>t(()=>import("./UserProfile-_iqLfjt2.js"),__vite__mapDeps([19,1,2,3,4,5,6,7,8,9,10,17]),import.meta.url)),z=e.lazy(()=>t(()=>import("./Settings-CC7UG-TA.js"),__vite__mapDeps([20,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),R=e.lazy(()=>t(()=>import("./UpdatePassword-CUJo7SRB.js"),__vite__mapDeps([21,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),f=e.lazy(()=>t(()=>import("./AnalysisAssistantPage-CqoSPBfZ.js"),__vite__mapDeps([22,1,2,3,4,5,6,7,8,9,10,23,24,25,26,27]),import.meta.url)),D=e.lazy(()=>t(()=>import("./WhichTestPage-BVqGiyoM.js"),__vite__mapDeps([28,1,2,3,4,5,6,7,8,9,10,13]),import.meta.url)),V=e.lazy(()=>t(()=>import("./VisualizationGuidePage-CGxTob5K.js"),__vite__mapDeps([29,1,2,3,4,5,6,7,8,9,10,13]),import.meta.url)),O=e.lazy(()=>t(()=>import("./StatisticalMethodsPage-DO96Z5ro.js"),__vite__mapDeps([30,1,2,3,4,5,6,7,8,9,10,13]),import.meta.url)),L=e.lazy(()=>t(()=>import("./KnowledgeBasePage-1nzuPqIf.js"),__vite__mapDeps([31,1,2]),import.meta.url)),C=e.lazy(()=>t(()=>import("./TutorialPage-D7kG4YnK.js"),__vite__mapDeps([32,1,2,3,4,5,6,7,8,9,10,31,33]),import.meta.url)),I=e.lazy(()=>t(()=>import("./VideoTutorialsPage-CPJt_uWM.js"),__vite__mapDeps([34,1,2,3,4,5,6,7,8,9,10,13]),import.meta.url)),S=e.lazy(()=>t(()=>import("./AnalysisIndexPage-BvKbf9Gr.js"),__vite__mapDeps([35,1,2,3,4,5,6,7,8,9,10,36,37,38,39,40,41,42,43,44]),import.meta.url)),M=e.lazy(()=>t(()=>import("./PricingPage-gZ3rfXmZ.js"),__vite__mapDeps([45,1,2,18,3,4,5,6,7,8,9,10]),import.meta.url)),x=e.lazy(()=>t(()=>import("./NotificationsPage-CJRFU61k.js"),__vite__mapDeps([46,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),N=e.lazy(()=>t(()=>import("./RouterTest-C8CpCslx.js"),__vite__mapDeps([47,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),k=[{path:"dashboard",component:b,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Dashboard",description:"Main application dashboard",category:"core",icon:"Dashboard",order:0}},{path:"home",component:b,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Home",description:"Application home page",category:"core",hidden:!0}},{path:"auth",component:h,requiresAuth:!1,allowGuest:!1,allowPublic:!0,props:{initialView:"login"},metadata:{title:"Authentication",description:"Login and registration",category:"auth",hidden:!0},children:[{path:"auth/login",component:h,requiresAuth:!1,allowPublic:!0,props:{initialView:"login"},metadata:{title:"Login",category:"auth"}},{path:"auth/register",component:h,requiresAuth:!1,allowPublic:!0,props:{initialView:"register"},metadata:{title:"Register",category:"auth"}}]},{path:"reset-password",component:R,requiresAuth:!1,allowGuest:!1,allowPublic:!0,metadata:{title:"Reset Password",description:"Reset your password",category:"auth",hidden:!0}},{path:"profile",component:q,requiresAuth:!0,allowGuest:!1,allowPublic:!1,metadata:{title:"User Profile",description:"Manage your profile settings",category:"user",icon:"Person"}},{path:"settings",component:z,requiresAuth:!0,allowGuest:!1,allowPublic:!1,metadata:{title:"Settings",description:"Application settings and preferences",category:"user",icon:"Settings"}},{path:"notifications",component:x,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Notifications",description:"View and manage all your notifications",category:"core",icon:"Notifications",order:5}},{path:"assistant",component:f,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Analysis Assistant",description:"Get help choosing the right statistical test",category:"help",icon:"Help",order:10}},{path:"analysis-assistant",component:f,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Analysis Assistant",description:"Get help choosing the right statistical test",category:"help",icon:"Help",hidden:!0}},{path:"whichtest",component:D,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Which Test?",description:"Find the right statistical test for your data",category:"help",icon:"Quiz"}},{path:"visualizationguide",component:V,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Visualization Guide",description:"Learn about data visualization best practices",category:"help",icon:"BarChart"}},{path:"statisticalmethods",component:O,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Statistical Methods",description:"Reference guide for statistical methods",category:"help",icon:"MenuBook"}},{path:"knowledge-base",component:L,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Knowledge Base",description:"Comprehensive help and documentation",category:"help",icon:"LibraryBooks"},children:[{path:":id",component:C,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Tutorial",category:"help"}}]},{path:"video-tutorials",component:I,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Video Tutorials",description:"Video guides and tutorials",category:"help",icon:"VideoLibrary"}},{path:"analysis-index",component:S,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Analysis Index",description:"Browse all available analyses",category:"analysis",icon:"List"}},{path:"pricing",component:M,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Pricing",description:"Choose your DataStatPro plan",category:"core",icon:"AttachMoney",order:10}},{path:"router-test",component:N,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Router Test",description:"Test the new routing system",category:"dev",icon:"BugReport",hidden:!0}}],W=e.lazy(()=>t(()=>import("./DataManagementPage-BRk0DtCj.js"),__vite__mapDeps([48,1,2,43]),import.meta.url)),l=e.lazy(()=>t(()=>import("./index-v_pkX9nf.js"),__vite__mapDeps([49,1,2,3,4,5,6,7,8,9,10,13,12,14,50,15,51]),import.meta.url)),F=[{path:"data-management",component:W,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{},metadata:{title:"Data Management",description:"Import, export, and manage your datasets",category:"data",icon:"Storage",order:1},children:[{path:"data-management/import",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"import"},metadata:{title:"Import Data",description:"Import data from CSV, Excel, or other formats",category:"data",order:1}},{path:"data-management/datasets",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"datasets"},metadata:{title:"Dataset Management",description:"Manage and organize your datasets",category:"data",order:2}},{path:"data-management/export",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"export"},metadata:{title:"Export Data",description:"Export your data in various formats",category:"data",order:3}},{path:"data-management/editor",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"editor"},metadata:{title:"Data Editor",description:"Edit and transform your data",category:"data",order:4}},{path:"data-management/variables",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"variables"},metadata:{title:"Variable Editor",description:"Edit variable properties and metadata",category:"data",order:5}},{path:"data-management/transform",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"transform"},metadata:{title:"Data Transformation",description:"Transform and recode your variables",category:"data",order:6}},{path:"data-management/sample",component:l,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"sample"},metadata:{title:"Sample Data",description:"Generate sample datasets for learning",category:"data",order:7}}]}],B=e.lazy(()=>t(()=>import("./DescriptiveStatsPage-Dax3LBrL.js"),__vite__mapDeps([52,1,2,36]),import.meta.url)),n=e.lazy(()=>t(()=>import("./index-DqymCIs-.js"),__vite__mapDeps([53,1,2,4,5,6,7,8,3,9,10,14,23,24,25,26]),import.meta.url)),$=e.lazy(()=>t(()=>import("./InferentialStatsPage-CnZG1DdU.js"),__vite__mapDeps([54,1,2,37]),import.meta.url)),P=e.lazy(()=>t(()=>import("./index-DjpOF40k.js"),__vite__mapDeps([55,1,2,56,3,4,5,6,7,8,9,10,14,23,57,58,24,25,59,26,60,61,62,63,64,12,13,50,15,65,66]),import.meta.url)),m=e.lazy(()=>t(()=>import("./TTests-CqoYonWk.js"),__vite__mapDeps([56,1,2,3,4,5,6,7,8,9,10,14,23,57,58,24,25,59,26]),import.meta.url)),u=e.lazy(()=>t(()=>import("./NonParametricTests-CnQbYnts.js"),__vite__mapDeps([66,1,2,3,4,5,6,7,8,9,10,23,24,25,59]),import.meta.url)),H=e.lazy(()=>t(()=>import("./OneWayANOVA-ai9Q6gnX.js"),__vite__mapDeps([61,1,2,3,4,5,6,7,8,9,10,14,23,58,24,25,59,26]),import.meta.url)),U=e.lazy(()=>t(()=>import("./RepeatedMeasuresANOVA-DwrjssMW.js"),__vite__mapDeps([64,1,2,3,4,5,6,7,8,9,10,12,13,14,50,15,65]),import.meta.url).then(a=>({default:a.RepeatedMeasuresANOVA}))),K=e.lazy(()=>t(()=>import("./TwoWayANOVA-CiVErBBN.js"),__vite__mapDeps([62,1,2,3,4,5,6,7,8,9,10,63]),import.meta.url)),Q=e.lazy(()=>t(()=>import("./ANOVA-VqPPZ-TR.js"),__vite__mapDeps([60,1,2,61,3,4,5,6,7,8,9,10,14,23,58,24,25,59,26,62,63,64,12,13,50,15,65]),import.meta.url)),j=e.lazy(()=>t(()=>import("./TTestWorkflowPage-CdvNLcMj.js"),__vite__mapDeps([67,1,2,3,4,5,6,7,8,9,10,12,13,14,50,15,68,57,23,24,25,26]),import.meta.url)),J=e.lazy(()=>t(()=>import("./ANOVAWorkflowPage-BelY7RhI.js"),__vite__mapDeps([69,1,2,3,4,5,6,7,8,9,10,12,13,14,50,15,68,58,23,24,25,26,65,63]),import.meta.url)),X=[{path:"inference",component:P,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Inferential Statistics (Legacy)",description:"Legacy inferential statistics interface",category:"statistics",hidden:!0},children:[{path:"inference/workflow",component:j,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"T-Test Workflow",description:"Guided t-test analysis workflow",category:"statistics"}},{path:"inference/anovaworkflow",component:J,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"ANOVA Workflow",description:"Guided ANOVA analysis workflow",category:"statistics"}},{path:"inference/ttest",component:m,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"oneSample"},metadata:{title:"T-Tests (Legacy)",description:"Legacy t-test interface",category:"statistics"}},{path:"inference/nonparametric",component:u,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"mannWhitney"},metadata:{title:"Non-parametric Tests (Legacy)",description:"Legacy non-parametric tests interface",category:"statistics"}},{path:"inference/anova",component:Q,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"ANOVA (Legacy)",description:"Legacy ANOVA interface",category:"statistics"}},{path:"inference/assumptions",component:P,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"assumptions"},metadata:{title:"Test Assumptions (Legacy)",description:"Legacy test assumptions interface",category:"statistics"}}]},{path:"stats",component:B,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Descriptive Statistics",description:"Explore and summarize your data",category:"statistics",icon:"Assessment",order:2},children:[{path:"stats/descriptives",component:n,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"descriptives"},metadata:{title:"Descriptive Analysis",description:"Calculate means, medians, and other descriptive statistics",category:"statistics",order:1}},{path:"stats/frequencies",component:n,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"frequencies"},metadata:{title:"Frequency Tables",description:"Generate frequency tables and distributions",category:"statistics",order:2}},{path:"stats/crosstabs",component:n,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"crosstabs"},metadata:{title:"Cross-Tabulations",description:"Create cross-tabulation tables and chi-square tests",category:"statistics",order:3}},{path:"stats/normality",component:n,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"normality"},metadata:{title:"Normality Tests",description:"Test data for normal distribution",category:"statistics",order:4}},{path:"stats/frequency",component:n,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"frequencies"},metadata:{title:"Frequency Tables (Legacy)",description:"Generate frequency tables and cross-tabulations",category:"statistics"}},{path:"stats/descriptive",component:n,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"descriptives"},metadata:{title:"Descriptive Measures (Legacy)",description:"Calculate means, medians, and other descriptive statistics",category:"statistics"}}]},{path:"inferential-stats",component:$,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Inferential Statistics",description:"Perform statistical tests and hypothesis testing",category:"statistics",icon:"TrendingUp",order:3},children:[{path:"inferential-stats/one-sample-ttest",component:m,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"oneSample"},metadata:{title:"One-Sample t-test",description:"Test if a sample mean differs from a population mean",category:"statistics"}},{path:"inferential-stats/independent-samples-ttest",component:m,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"independent"},metadata:{title:"Independent Samples t-test",description:"Compare means between two independent groups",category:"statistics"}},{path:"inferential-stats/paired-samples-ttest",component:m,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"paired"},metadata:{title:"Paired Samples t-test",description:"Compare means for paired observations",category:"statistics"}},{path:"inferential-stats/mann-whitney-u-test",component:u,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"mannWhitney"},metadata:{title:"Mann-Whitney U Test",description:"Non-parametric test for comparing two independent groups",category:"statistics"}},{path:"inferential-stats/wilcoxon-signed-rank-test",component:u,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"wilcoxon"},metadata:{title:"Wilcoxon Signed-Rank Test",description:"Non-parametric test for paired samples",category:"statistics"}},{path:"inferential-stats/kruskal-wallis-test",component:u,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"kruskalWallis"},metadata:{title:"Kruskal-Wallis Test",description:"Non-parametric alternative to one-way ANOVA",category:"statistics"}},{path:"inferential-stats/friedman-test",component:u,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"friedman"},metadata:{title:"Friedman Test",description:"Non-parametric test for repeated measures",category:"statistics"}},{path:"inferential-stats/chi-square-test",component:u,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"chiSquare"},metadata:{title:"Chi-Square Test",description:"Test for independence and goodness of fit",category:"statistics"}},{path:"inferential-stats/one-way-anova",component:H,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"One-Way ANOVA",description:"Compare means across multiple groups",category:"statistics"}},{path:"inferential-stats/repeated-measures-anova",component:U,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Repeated Measures ANOVA",description:"ANOVA for repeated measurements",category:"statistics"}},{path:"inferential-stats/two-way-anova",component:K,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Two-Way ANOVA",description:"ANOVA with two factors",category:"statistics"}}]}],Y=e.lazy(()=>t(()=>import("./DataVisualizationPage-BkCc12HU.js"),__vite__mapDeps([70,1,2,42]),import.meta.url)),r=e.lazy(()=>t(()=>import("./index-Bd7Staio.js"),__vite__mapDeps([71,4,2,5,6,1,7,8]),import.meta.url)),Z=[{path:"charts",component:Y,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Data Visualization",description:"Create charts and graphs from your data",category:"visualization",icon:"BarChart",order:4},children:[{path:"charts/bar",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"bar"},metadata:{title:"Bar Charts",description:"Create bar charts and column charts",category:"visualization"}},{path:"charts/pie",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"pie"},metadata:{title:"Pie Charts",description:"Create pie charts and donut charts",category:"visualization"}},{path:"charts/histogram",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"histogram"},metadata:{title:"Histograms",description:"Create histograms for distribution analysis",category:"visualization"}},{path:"charts/boxplot",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"boxplot"},metadata:{title:"Box Plots",description:"Create box plots for distribution comparison",category:"visualization"}},{path:"charts/scatter",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"scatter"},metadata:{title:"Scatter Plots",description:"Create scatter plots for correlation analysis",category:"visualization"}},{path:"charts/line",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"line"},metadata:{title:"Line Charts",description:"Create line charts for time series data",category:"visualization"}},{path:"charts/raincloud",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"raincloud"},metadata:{title:"Rain Cloud Plots",description:"Combine box plot, scatter plot, and density visualization",category:"visualization"}},{path:"charts/sankey",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"sankey"},metadata:{title:"Sankey Diagrams",description:"Visualize flow relationships between categorical variables",category:"visualization"}},{path:"charts/errorbar",component:r,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"errorbar"},metadata:{title:"Error Bar Charts",description:"Display means with error bars showing variability or uncertainty",category:"visualization"}}]}],tt=e.lazy(()=>t(()=>import("./CorrelationAnalysisPage-C4yIGAB2.js"),__vite__mapDeps([72,1,2,38]),import.meta.url)),i=e.lazy(()=>t(()=>import("./index-BHSDXLLS.js"),__vite__mapDeps([73,1,2,3,4,5,6,7,8,9,10,23,24,25,14,74]),import.meta.url)),et=[{path:"correlation",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Correlation Analysis (Legacy)",description:"Legacy correlation analysis interface",category:"analysis",hidden:!0},children:[{path:"correlation/pearson",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"pearson"},metadata:{title:"Pearson Correlation (Legacy)",description:"Calculate Pearson correlation coefficients",category:"analysis"}},{path:"correlation/linear",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"regression"},metadata:{title:"Linear Regression (Legacy)",description:"Perform linear regression analysis",category:"analysis"}},{path:"correlation/logistic",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"logistic"},metadata:{title:"Logistic Regression (Legacy)",description:"Perform logistic regression analysis",category:"analysis"}}]},{path:"correlation-analysis",component:tt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Correlation Analysis",description:"Analyze relationships between variables",category:"analysis",icon:"ScatterPlot",order:5},children:[{path:"correlation-analysis/pearson",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"pearson"},metadata:{title:"Pearson Correlation",description:"Calculate Pearson correlation coefficients",category:"analysis"}},{path:"correlation-analysis/spearman",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"spearman"},metadata:{title:"Spearman Correlation",description:"Calculate Spearman rank correlation",category:"analysis"}},{path:"correlation-analysis/partial",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"partial"},metadata:{title:"Partial Correlation",description:"Calculate partial correlation coefficients",category:"analysis"}},{path:"correlation-analysis/matrix",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"matrix"},metadata:{title:"Correlation Matrix",description:"Generate correlation matrices",category:"analysis"}},{path:"correlation-analysis/regression",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"regression"},metadata:{title:"Linear Regression",description:"Perform linear regression analysis",category:"analysis"}},{path:"correlation-analysis/logistic",component:i,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"logistic"},metadata:{title:"Logistic Regression",description:"Perform logistic regression analysis",category:"analysis"}}]}],at=e.lazy(()=>t(()=>import("./PivotAnalysisViewer-CUgNzlbS.js").then(a=>a.P),__vite__mapDeps([75,4,2,5,6,1,7,8,3,9,10,13,50,76]),import.meta.url)),it=e.lazy(()=>t(()=>import("./SampleSizeCalculatorsOptions-DlONI60r.js"),__vite__mapDeps([41,1,2]),import.meta.url)),ot=e.lazy(()=>t(()=>import("./OneSampleCalculator-DdSwydDx.js"),__vite__mapDeps([77,1,2,78,9,7]),import.meta.url)),rt=e.lazy(()=>t(()=>import("./TwoSampleCalculator-cqqzlW-p.js"),__vite__mapDeps([79,1,2,9,78,7]),import.meta.url)),lt=e.lazy(()=>t(()=>import("./PairedSampleCalculator-B2azJaVa.js"),__vite__mapDeps([80,1,2,9,78,7]),import.meta.url)),st=e.lazy(()=>t(()=>import("./MoreThanTwoGroupsCalculator-DCYA-CIl.js"),__vite__mapDeps([81,1,2,78,9,7]),import.meta.url)),nt=e.lazy(()=>t(()=>import("./EpiCalcPage-BSSz3cCG.js"),__vite__mapDeps([82,1,2,40]),import.meta.url)),_=e.lazy(()=>t(()=>import("./EpiCalc-BuVTzyEC.js"),__vite__mapDeps([83,1,2,12,3,4,5,6,7,8,9,10,13,51,14,15,84,59,85,86,87,88]),import.meta.url)),ut=e.lazy(()=>t(()=>import("./AdvancedAnalysisPage-BJQTFL6B.js"),__vite__mapDeps([89,1,2,39]),import.meta.url)),g=e.lazy(()=>t(()=>import("./SurvivalAnalysis-Dzi5rTjL.js"),__vite__mapDeps([90,1,2,3,4,5,6,7,8,9,10,91]),import.meta.url)),ct=e.lazy(()=>t(()=>import("./ExploratoryFactorAnalysis-DKexHXUh.js"),__vite__mapDeps([92,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),pt=e.lazy(()=>t(()=>import("./ConfirmatoryFactorAnalysis-D0HSoWGO.js"),__vite__mapDeps([93,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),mt=e.lazy(()=>t(()=>import("./MetaAnalysis-C_ep_tOv.js"),__vite__mapDeps([94,1,2,7,3,4,5,6,8,9,10]),import.meta.url)),T=e.lazy(()=>t(()=>import("./ReliabilityAnalysis-BP7J0pqF.js"),__vite__mapDeps([95,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),dt=e.lazy(()=>t(()=>import("./ClusterAnalysis-Dwkqf03e.js"),__vite__mapDeps([96,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),yt=e.lazy(()=>t(()=>import("./MediationModeration-BLkQPilK.js"),__vite__mapDeps([97,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),ht=e.lazy(()=>t(()=>import("./VariableTree-BL5rEhkE.js"),__vite__mapDeps([98,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),_t=e.lazy(()=>t(()=>import("./PublicationReadyPage-DZlM7Fz2.js"),__vite__mapDeps([99,1,2,3,4,5,6,7,8,9,10,44]),import.meta.url)),gt=e.lazy(()=>t(()=>import("./Table1-B7BcroXM.js"),__vite__mapDeps([100,1,2,3,4,5,6,7,8,9,10,101,102,23]),import.meta.url)),wt=e.lazy(()=>t(()=>import("./ConvertToAPA-C3MGbZit.js"),__vite__mapDeps([103,1,2,102,3,4,5,6,7,8,9,10]),import.meta.url)),At=e.lazy(()=>t(()=>import("./SMDTable-BZazxrfM.js"),__vite__mapDeps([104,1,2,3,4,5,6,7,8,9,10,101,102,23]),import.meta.url)),bt=e.lazy(()=>t(()=>import("./Table1a-1M0jF3L-.js"),__vite__mapDeps([105,1,2,3,4,5,6,7,8,9,10,101,102,23]),import.meta.url)),ft=e.lazy(()=>t(()=>import("./Table1b-ByG5YXGI.js"),__vite__mapDeps([106,1,2,3,4,5,6,7,8,9,10,101,102,23,26]),import.meta.url)),Pt=e.lazy(()=>t(()=>import("./FlowDiagram-CHRbazj7.js").then(a=>a.b4),__vite__mapDeps([107,1,2,4,5,6,7,8,102,3,9,10,108]),import.meta.url)),Tt=e.lazy(()=>t(()=>import("./RegressionTable-BuRxA8Iu.js"),__vite__mapDeps([109,1,2,3,4,5,6,7,8,9,10,101,102,74,24,25,23,91]),import.meta.url)),vt=e.lazy(()=>t(()=>import("./StatisticalMethodsGenerator-C5cIAvRq.js"),__vite__mapDeps([110,1,2,3,4,5,6,7,8,9,10,101,15,102]),import.meta.url)),Et=e.lazy(()=>t(()=>import("./ResultsManagerPage-BSs6I6Q7.js"),__vite__mapDeps([111,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),w=[{path:"pivot",component:at,requiresAuth:!1,allowGuest:!0,allowPublic:!1,metadata:{title:"Pivot Analysis",description:"Create pivot tables and cross-tabulations",category:"analysis",icon:"TableChart",order:6}},{path:"samplesize",component:it,requiresAuth:!1,allowGuest:!0,allowPublic:!1,metadata:{title:"Sample Size Calculators",description:"Calculate required sample sizes for studies",category:"tools",icon:"Calculate",order:7},children:[{path:"samplesize/one-sample",component:ot,requiresAuth:!1,allowGuest:!0,metadata:{title:"One Sample Calculator",description:"Calculate sample size for one sample tests",category:"tools"}},{path:"samplesize/two-sample",component:rt,requiresAuth:!1,allowGuest:!0,metadata:{title:"Two Sample Calculator",description:"Calculate sample size for two sample comparisons",category:"tools"}},{path:"samplesize/paired-sample",component:lt,requiresAuth:!1,allowGuest:!0,metadata:{title:"Paired Sample Calculator",description:"Calculate sample size for paired sample tests",category:"tools"}},{path:"samplesize/more-than-two-groups",component:st,requiresAuth:!1,allowGuest:!0,metadata:{title:"More Than Two Groups Calculator",description:"Calculate sample size for multiple group comparisons",category:"tools"}}]},{path:"epicalc",component:nt,requiresAuth:!1,allowGuest:!0,allowPublic:!1,metadata:{title:"Epidemiological Calculators",description:"Calculate epidemiological measures and statistics",category:"tools",icon:"LocalHospital",order:8},children:[{path:"epicalc/2x2",component:_,requiresAuth:!1,allowGuest:!0,props:{initialTab:"2x2"},metadata:{title:"2x2 Table Analysis",description:"Analyze 2x2 contingency tables",category:"tools"}},{path:"epicalc/diagnostic",component:_,requiresAuth:!1,allowGuest:!0,props:{initialTab:"diagnostic"},metadata:{title:"Diagnostic Test Evaluation",description:"Evaluate diagnostic test performance",category:"tools"}},{path:"epicalc/screening",component:_,requiresAuth:!1,allowGuest:!0,props:{initialTab:"screening"},metadata:{title:"Screening Test Analysis",description:"Analyze screening test characteristics",category:"tools"}}]},{path:"advanced-analysis",component:ut,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Advanced Analysis",description:"Advanced statistical analysis methods",category:"analysis",icon:"Psychology",order:9},children:[{path:"advanced-analysis/survival",component:g,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Survival Analysis",description:"Kaplan-Meier and Cox regression analysis",category:"analysis"}},{path:"advanced-analysis/efa",component:ct,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Exploratory Factor Analysis",description:"Exploratory factor analysis",category:"analysis"}},{path:"advanced-analysis/cfa",component:pt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Confirmatory Factor Analysis",description:"Confirmatory factor analysis",category:"analysis"}},{path:"advanced-analysis/cluster",component:dt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Cluster Analysis",description:"Cluster analysis and classification",category:"analysis"}},{path:"advanced-analysis/mediation",component:yt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Mediation/Moderation Analysis",description:"Mediation and moderation analysis",category:"analysis"}},{path:"advanced-analysis/reliability",component:T,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Reliability Analysis",description:"Assess reliability and internal consistency",category:"analysis"}},{path:"advanced-analysis/meta-analysis",component:mt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Meta-Analysis",description:"Combine results from multiple studies",category:"analysis"}},{path:"advanced-analysis/variable-tree",component:ht,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Variable Tree Analysis",description:"Create hierarchical tree visualizations of variable relationships",category:"analysis"}}]},{path:"advanced",component:g,requiresAuth:!1,allowGuest:!0,allowPublic:!1,metadata:{title:"Advanced (Legacy)",description:"Legacy advanced analysis route",category:"analysis",hidden:!0}},{path:"survival",component:g,requiresAuth:!1,allowGuest:!0,allowPublic:!1,metadata:{title:"Survival Analysis (Legacy)",description:"Legacy survival analysis route",category:"analysis",hidden:!0}},{path:"reliability",component:T,requiresAuth:!1,allowGuest:!0,allowPublic:!1,metadata:{title:"Reliability Analysis",description:"Assess reliability and internal consistency",category:"analysis",icon:"VerifiedUser"}},{path:"publication-ready",component:_t,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Publication Ready",description:"Generate publication-ready tables and results",category:"tools",icon:"Article",order:10},children:[{path:"publication-ready/table1",component:gt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Table 1 Generator",description:"Generate descriptive statistics tables",category:"tools"}},{path:"publication-ready/apa",component:wt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"APA Formatter",description:"Format results in APA style",category:"tools"}},{path:"publication-ready/smd-table",component:At,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"SMD Table",description:"Standardized mean difference tables",category:"tools"}},{path:"publication-ready/table1a",component:bt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Table 1a",description:"Advanced descriptive statistics table",category:"tools"}},{path:"publication-ready/table1b",component:ft,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Table 1b",description:"Comprehensive descriptive statistics for numerical variables",category:"tools"}},{path:"publication-ready/flow-diagram",component:Pt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Flow Diagram",description:"CONSORT flow diagrams",category:"tools"}},{path:"publication-ready/regression-table",component:Tt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Regression Table",description:"Publication-ready regression tables",category:"tools"}},{path:"publication-ready/statistical-methods",component:vt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Statistical Methods Generator",description:"Generate publication-ready methods sections",category:"tools"}},{path:"publication-ready/results-manager",component:Et,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Results Manager",description:"Manage and export analysis results",category:"tools"}}]}],v=e.lazy(()=>t(()=>import("./PivotAnalysisViewer-CUgNzlbS.js").then(a=>a.P),__vite__mapDeps([75,4,2,5,6,1,7,8,3,9,10,13,50,76]),import.meta.url)),E=e.lazy(()=>t(()=>import("./SampleSizeCalculatorsOptions-DlONI60r.js"),__vite__mapDeps([41,1,2]),import.meta.url)),Gt=e.lazy(()=>t(()=>import("./OneSampleCalculator-DdSwydDx.js"),__vite__mapDeps([77,1,2,78,9,7]),import.meta.url)),qt=e.lazy(()=>t(()=>import("./TwoSampleCalculator-cqqzlW-p.js"),__vite__mapDeps([79,1,2,9,78,7]),import.meta.url)),zt=e.lazy(()=>t(()=>import("./PairedSampleCalculator-B2azJaVa.js"),__vite__mapDeps([80,1,2,9,78,7]),import.meta.url)),Rt=e.lazy(()=>t(()=>import("./MoreThanTwoGroupsCalculator-DCYA-CIl.js"),__vite__mapDeps([81,1,2,78,9,7]),import.meta.url)),Dt=e.lazy(()=>t(()=>import("./EpiCalcPage-BSSz3cCG.js"),__vite__mapDeps([82,1,2,40]),import.meta.url)),p=e.lazy(()=>t(()=>import("./EpiCalc-BuVTzyEC.js"),__vite__mapDeps([83,1,2,12,3,4,5,6,7,8,9,10,13,51,14,15,84,59,85,86,87,88]),import.meta.url));e.lazy(()=>t(()=>import("./CaseControlCalculator-BzLsSUO6.js"),__vite__mapDeps([85,1,2,12,3,4,5,6,7,8,9,10,13,14,15,59]),import.meta.url));e.lazy(()=>t(()=>import("./CohortCalculator-Ceh0DWHw.js"),__vite__mapDeps([86,1,2,12,3,4,5,6,7,8,9,10,13,14,15]),import.meta.url));e.lazy(()=>t(()=>import("./CrossSectionalCalculator-CiCHocwD.js"),__vite__mapDeps([84,1,2,12,3,4,5,6,7,8,9,10,13,14,15,59]),import.meta.url));e.lazy(()=>t(()=>import("./MatchedCaseControlCalculator-XX9Fsb_b.js"),__vite__mapDeps([87,1,2,12,3,4,5,6,7,8,9,10,13,14,15]),import.meta.url));e.lazy(()=>t(()=>import("./SampleSizePowerCalculator-Czye5N6w.js"),__vite__mapDeps([88,1,2,12,3,4,5,6,7,8,9,10,13,14,15]),import.meta.url));const Vt=e.lazy(()=>t(()=>import("./PublicationReadyPage-DZlM7Fz2.js"),__vite__mapDeps([99,1,2,3,4,5,6,7,8,9,10,44]),import.meta.url)),Ot=e.lazy(()=>t(()=>import("./Table2-Bf2a6rrV.js"),__vite__mapDeps([112,1,2,3,4,5,6,7,8,9,10,101,102,23,26,57,58,59]),import.meta.url)),Lt=e.lazy(()=>t(()=>import("./PostHocTests-BRrhMOQ0.js"),__vite__mapDeps([113,1,2,3,4,5,6,7,8,9,10,101,102]),import.meta.url)),Ct=e.lazy(()=>t(()=>import("./ResultsManagerPage-BSs6I6Q7.js"),__vite__mapDeps([111,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),It=[{path:"pivot",component:v,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Pivot Analysis",description:"Create pivot tables and cross-tabulations",category:"analysis",icon:"TableChart",order:6},children:[{path:"pivot/tables",component:v,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"tables"},metadata:{title:"Pivot Tables",description:"Create and customize pivot tables",category:"analysis"}}]},{path:"samplesize",component:E,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Sample Size Calculators",description:"Calculate required sample sizes for studies",category:"tools",icon:"Calculate",order:7},children:[{path:"samplesize/options",component:E,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Sample Size Options",description:"Choose sample size calculation method",category:"tools"}},{path:"samplesize/one-sample",component:Gt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"One Sample Calculator",description:"Calculate sample size for one sample tests",category:"tools"}},{path:"samplesize/two-sample",component:qt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Two Sample Calculator",description:"Calculate sample size for two sample comparisons",category:"tools"}},{path:"samplesize/paired-sample",component:zt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Paired Sample Calculator",description:"Calculate sample size for paired sample tests",category:"tools"}},{path:"samplesize/more-than-two-groups",component:Rt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"More Than Two Groups Calculator",description:"Calculate sample size for multiple group comparisons",category:"tools"}}]},{path:"epicalc",component:Dt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Epidemiological Calculators",description:"Calculate epidemiological measures and statistics",category:"tools",icon:"LocalHospital",order:8},children:[{path:"epicalc/case-control",component:p,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"case_control"},metadata:{title:"Case-Control Studies",description:"Calculate measures for case-control studies",category:"tools"}},{path:"epicalc/cohort",component:p,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"cohort"},metadata:{title:"Cohort Studies",description:"Calculate measures for cohort studies",category:"tools"}},{path:"epicalc/cross-sectional",component:p,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"cross_sectional"},metadata:{title:"Cross-Sectional Studies",description:"Calculate measures for cross-sectional studies",category:"tools"}},{path:"epicalc/matched-case-control",component:p,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"matched_case_control"},metadata:{title:"Matched Case-Control",description:"Calculate measures for matched case-control studies",category:"tools"}},{path:"epicalc/sample-size-power",component:p,requiresAuth:!1,allowGuest:!0,allowPublic:!0,props:{initialTab:"sample_size_power"},metadata:{title:"Sample Size & Power",description:"Calculate sample size and power for epidemiological studies",category:"tools"}}]},{path:"publication-ready",component:Vt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Publication Ready",description:"Generate publication-ready tables and results",category:"tools",icon:"Article",order:10},children:[{path:"publication-ready/table2",component:Ot,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Table 2 Generator",description:"Generate publication-ready Table 2",category:"tools"}},{path:"publication-ready/posthoc-tests",component:Lt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Post-hoc Tests",description:"Generate publication-ready post-hoc test results",category:"tools"}},{path:"publication-ready/results-manager",component:Ct,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Results Manager",description:"Manage and export analysis results",category:"tools"}}]}],St=e.lazy(()=>t(()=>import("./DevTrainingPage-A92mzUCf.js"),__vite__mapDeps([114,1,2,27]),import.meta.url)),Mt=e.lazy(()=>t(()=>import("./RouteTestPage-DN2MxV_Q.js"),__vite__mapDeps([115,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),xt=e.lazy(()=>t(()=>import("./AuthTestPage-DhFDKCY6.js"),__vite__mapDeps([116,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),Nt=e.lazy(()=>t(()=>import("./EducationalTierTest-BNrDUfgg.js"),__vite__mapDeps([117,1,2,3,4,5,6,7,8,9,10,102]),import.meta.url)),y=()=>typeof window<"u"&&(window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"||window.location.port==="5173"||window.location.port==="5174"||window.location.port==="3000"),G=[{path:"dev-training",component:St,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Development Training System",description:"Analysis Assistant training data management (Development Only)",category:"development",icon:"Code",order:999,hidden:!y(),developmentOnly:!0}},{path:"route-test",component:Mt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Route Testing & Debugging",description:"Test and debug routing functionality (Development Only)",category:"development",icon:"Route",order:998,hidden:!y(),developmentOnly:!0}},{path:"auth-test",component:xt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Authentication Testing & Debugging",description:"Test and debug authentication functionality",category:"development",icon:"Security",order:997,hidden:!1,developmentOnly:!1}},{path:"edu-tier-test",component:Nt,requiresAuth:!1,allowGuest:!0,allowPublic:!0,metadata:{title:"Educational Tier Testing",description:"Test and verify educational tier implementation (Development Only)",category:"development",icon:"School",order:996,hidden:!y(),developmentOnly:!0}}],kt=()=>y()?G:G.filter(a=>a.path==="auth-test"),Wt=e.lazy(()=>t(()=>import("./AdminDashboardPage-SPi3xD-N.js"),__vite__mapDeps([118,1,2,3,4,5,6,7,8,9,10]),import.meta.url)),Ft=[{path:"admin-dashboard",component:Wt,requiresAuth:!0,requiresAdmin:!0,allowGuest:!1,allowPublic:!1,props:{},metadata:{title:"Admin Dashboard",description:"System administration and management console",category:"admin",icon:"Security",order:999,hidden:!1,adminOnly:!0}}];function Yt(){d.clear(),console.log("🔍 Registering advanced routes:",w.length,"routes"),console.log("🔍 Advanced routes paths:",w.map(a=>a.path)),d.register([...k,...F,...X,...Z,...et,...w,...It,...Ft,...kt()]),console.log(`Registered ${d.getAllRoutes().length} routes`)}function Bt(){return d.getAllRoutes().filter(a=>{var o;return!((o=a.metadata)!=null&&o.hidden)}).sort((a,o)=>{var c,s;return(((c=a.metadata)==null?void 0:c.order)||999)-(((s=o.metadata)==null?void 0:s.order)||999)})}function Zt(){const a=Bt(),o={};return a.forEach(c=>{var A;const s=((A=c.metadata)==null?void 0:A.category)||"other";o[s]||(o[s]=[]),o[s].push(c)}),o}export{Bt as getNavigationRoutes,Zt as getRoutesByCategory,Yt as initializeRoutes,d as routeRegistry};
