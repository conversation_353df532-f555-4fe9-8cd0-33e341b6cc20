import{ck as c,j as e,u as k,aU as T,B as u,e as d,aE as D,R as q}from"./mui-libs-CfwFIaTD.js";import{l as f}from"./index-Bpan7Tbe.js";var x={},P=c;Object.defineProperty(x,"__esModule",{value:!0});var $=x.default=void 0,W=P(f()),L=e;$=x.default=(0,W.default)((0,L.jsx)("path",{d:"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"}),"TrendingUp");var m={},E=c;Object.defineProperty(m,"__esModule",{value:!0});var S=m.default=void 0,F=E(f()),M=e;S=m.default=(0,F.default)((0,M.jsx)("path",{d:"m16 18 2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z"}),"TrendingDown");var g={},O=c;Object.defineProperty(g,"__esModule",{value:!0});var R=g.default=void 0,U=O(f()),B=e;R=g.default=(0,U.default)((0,B.jsx)("path",{d:"m22 12-4-4v3H3v2h15v3z"}),"TrendingFlat");const H=T(q,{shouldForwardProp:t=>!["cardColor","textColor","variant"].includes(t)})(({theme:t,cardColor:n,textColor:o,variant:l})=>({padding:t.spacing(2.5),borderRadius:t.spacing(1.5),height:"100%",position:"relative",overflow:"hidden",...l==="default"&&{backgroundColor:n,color:o},...l==="gradient"&&{background:`linear-gradient(135deg, ${n} 0%, ${t.palette.background.paper} 100%)`,color:o},...l==="outlined"&&{backgroundColor:t.palette.background.paper,color:t.palette.text.primary,border:`1px solid ${n}`,borderLeft:`4px solid ${n}`}})),A=T(u)(({theme:t})=>({position:"absolute",top:10,right:10,opacity:.3,fontSize:60,lineHeight:1})),G=t=>{switch(t){case"up":return e.jsx($,{fontSize:"small"});case"down":return e.jsx(S,{fontSize:"small"});case"neutral":default:return e.jsx(R,{fontSize:"small"})}},J=(t,n)=>{switch(t){case"up":return n.palette.success.main;case"down":return n.palette.error.main;case"neutral":default:return n.palette.grey[500]}},Q=({title:t,value:n,description:o,tooltip:l,icon:h,trend:p,trendValue:v,trendLabel:j,color:i="primary",variant:a="default"})=>{var y;const r=k(),w=()=>{var s,b,_;return a==="outlined"?((s=r.palette[i])==null?void 0:s.main)||r.palette.primary.main:a==="default"?((b=r.palette[i])==null?void 0:b.main)||r.palette.primary.main:((_=r.palette[i])==null?void 0:_.light)||r.palette.primary.light},C=()=>{var s;return a==="outlined"?r.palette.text.primary:a==="default"?((s=r.palette[i])==null?void 0:s.contrastText)||"#ffffff":r.palette.text.primary},I=w(),z=C();return e.jsxs(H,{elevation:a==="outlined"?0:1,cardColor:I,textColor:z,variant:a,theme:r,children:[e.jsxs(u,{sx:{position:"relative",zIndex:1},children:[e.jsx(d,{variant:"subtitle2",component:"h2",color:a==="outlined"?"text.secondary":"inherit",sx:{opacity:a==="default"?.8:.7,fontWeight:500,mb:1},children:t}),e.jsxs(u,{sx:{display:"flex",alignItems:"flex-end",mb:o?1:0},children:[e.jsx(d,{variant:"h4",component:"div",sx:{fontWeight:600,mr:1,color:a==="outlined"?(y=r.palette[i])==null?void 0:y.main:"inherit"},children:n}),p&&v&&e.jsxs(u,{sx:{display:"flex",alignItems:"center",color:J(p,r),mb:.5},children:[G(p),e.jsx(d,{variant:"body2",component:"span",sx:{ml:.5,fontWeight:500},children:v})]})]}),o&&e.jsx(D,{title:l||"",arrow:!0,placement:"top",children:e.jsx(d,{variant:"body2",component:"p",sx:{opacity:a==="default"?.7:.6,maxWidth:"80%"},children:o})}),j&&e.jsx(d,{variant:"caption",sx:{display:"block",mt:1,opacity:.7},children:j})]}),h&&e.jsx(A,{color:a==="outlined"?"rgba(0,0,0,0.08)":"inherit",children:h})]})};export{Q as S};
