import{j as e,B as g,e as x,R as C,g as O,G as q,ai as U,b9 as P,ba as W,bb as T,f as se,ae as te,ao as ne,ap as re,aq as ie,ar as f,as as d,at as le,bR as oe}from"./mui-libs-CfwFIaTD.js";import{r as h,b as ce}from"./react-libs-Cr2nE3UY.js";import{a as de,D as v,g as V}from"./index-Bpan7Tbe.js";import{A as ue}from"./AddToResultsButton-BwSXKCt2.js";import{P as he}from"./PublicationReadyGate-BGFbKbJc.js";import{b as pe,c as me,g as be,h as ge,i as xe}from"./descriptive-Djo0s6H4.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const qe=()=>{const{datasets:m,currentDataset:S,setCurrentDataset:L}=de(),[F,A]=h.useState((S==null?void 0:S.id)||""),[b,I]=h.useState([]),[D,E]=h.useState(!1),[B,M]=h.useState(null),[R,j]=h.useState(null),[N,y]=h.useState(null),[G,$]=h.useState(!1),[z,k]=h.useState(""),n=m.find(a=>a.id===F),p=(n==null?void 0:n.columns)||[],H=a=>{const r=a.target.value;A(r),I([]),j(null),y(null);const t=m.find(i=>i.id===r);t&&L(t)},J=a=>{const r=a.target.value;I(typeof r=="string"?r.split(","):r),j(null),y(null)},K=()=>{if(!n||b.length===0){M("Please select a dataset and at least one variable to analyze."),j(null),y(null);return}E(!0),M(null);const a=[];b.forEach(r=>{const t=p.find(s=>s.id===r);if(!t){console.error(`Column with ID ${r} not found in selected dataset.`);return}const i=n.data.map(s=>s[t.name]);if(t.type===v.NUMERIC){const s=i.filter(o=>typeof o=="number"&&!isNaN(o));if(s.length>0){const o=pe(s),u=me(s),l=be(s);a.push({variableName:t.name,dataType:v.NUMERIC,results:{mean:o,standardDeviation:u,range:l}})}else a.push({variableName:t.name,dataType:v.NUMERIC,results:{}})}else{const s=i.map(String);if(s.length>0){const o=ge(s),u=xe(s),l={};Object.keys(u).forEach(c=>{l[c]=u[c]*100}),a.push({variableName:t.name,dataType:t.type,results:{frequencies:o,percentages:l}})}else a.push({variableName:t.name,dataType:t.type,results:{}})}}),j(a),y(Q(a,n.data.length)),E(!1)},Q=(a,r)=>{let t=`Table 1 presents the descriptive statistics for various variables from a dataset of ${r} observations. `;const i=[];return a.forEach(s=>{if(s.dataType===v.NUMERIC)s.results.mean!==void 0&&s.results.standardDeviation!==void 0&&s.results.range!==void 0&&i.push(`The mean ${s.variableName.toLowerCase()} is ${s.results.mean.toFixed(2)}, with a standard deviation of ${s.results.standardDeviation.toFixed(2)} and a range of ${s.results.range.toFixed(2)}`);else if(s.results.frequencies&&Object.keys(s.results.frequencies).length>0){const o=n==null?void 0:n.columns.find(l=>l.name===s.variableName),u=o&&n?V(o.id,n):Object.keys(s.results.frequencies);if(u.length===2){const l=u.sort((_,ee)=>{const ae=s.results.frequencies[_];return s.results.frequencies[ee]-ae}),c=l[0],w=l[1],Y=s.results.percentages[c],Z=s.results.percentages[w];i.push(`${Y.toFixed(0)}% of the sample is ${c.toLowerCase()}, while ${Z.toFixed(0)}% is ${w.toLowerCase()}`)}else{const l=u.map(c=>`${s.results.percentages[c].toFixed(0)}% have ${c.toLowerCase()}`);if(l.length>1){const c=l.pop();i.push(`${s.variableName} levels are distributed as follows: ${l.join(", ")}, and ${c}`)}else i.push(`${s.variableName}: ${l[0]}`)}}}),i.length>0&&(t+=i.join(". ")+". "),t+="Overall, the table provides a comprehensive overview of the demographic and characteristic variables of the sample, highlighting the central tendencies and distributions of these variables.",t},X=()=>{$(!1)};return e.jsx(he,{children:e.jsxs(g,{p:3,children:[e.jsx(x,{variant:"h5",gutterBottom:!0,children:"Publication Ready Table 1"}),e.jsxs(C,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(x,{variant:"subtitle1",gutterBottom:!0,children:"Select Data and Variables"}),e.jsx(O,{severity:"info",sx:{mb:2},children:"This table provides descriptive statistics (mean, standard deviation, range for numerical variables; frequencies and percentages for categorical variables) for selected variables in your dataset."}),e.jsxs(q,{container:!0,spacing:2,children:[e.jsx(q,{item:!0,xs:12,children:e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(P,{id:"dataset-select-label",children:"Dataset"}),e.jsx(W,{labelId:"dataset-select-label",id:"dataset-select",value:F,label:"Dataset",onChange:H,disabled:m.length===0,children:m.length===0?e.jsx(T,{value:"",disabled:!0,children:"No datasets available"}):m.map(a=>e.jsxs(T,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]})}),e.jsx(q,{item:!0,xs:12,children:e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(P,{id:"variable-select-label",children:"Variables"}),e.jsx(W,{labelId:"variable-select-label",id:"variable-select",multiple:!0,value:b,onChange:J,label:"Variables",disabled:p.length===0,renderValue:a=>a.map(r=>{var t;return((t=p.find(i=>i.id===r))==null?void 0:t.name)||""}).join(", "),children:p.length===0?e.jsx(T,{value:"",disabled:!0,children:"No variables available in selected dataset"}):p.map(a=>e.jsxs(T,{value:a.id,children:[a.name," (",a.type,")"]},a.id))})]})})]}),e.jsx(g,{mt:2,children:e.jsx(se,{variant:"contained",color:"primary",onClick:K,disabled:D||b.length===0||!n,children:"Generate Table 1"})})]}),D&&e.jsx(g,{display:"flex",justifyContent:"center",my:4,children:e.jsx(te,{})}),B&&e.jsx(O,{severity:"error",sx:{mb:3},children:B}),R&&!D&&n&&e.jsxs(g,{children:[e.jsxs(C,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(x,{variant:"h6",gutterBottom:!0,children:"Table 1. Descriptive Statistics"}),e.jsx(ne,{component:C,variant:"outlined",children:e.jsxs(re,{size:"small",children:[e.jsx(ie,{children:e.jsxs(f,{children:[e.jsx(d,{sx:{fontWeight:"bold"},children:"Variable"}),e.jsxs(d,{sx:{fontWeight:"bold"},align:"right",children:["Overall (n=",n.data.length||0,")"]})]})}),e.jsx(le,{children:R.map((a,r)=>e.jsxs(ce.Fragment,{children:[e.jsxs(f,{children:[e.jsx(d,{sx:{fontWeight:"bold"},children:a.variableName}),e.jsx(d,{align:"right"})," "]}),a.dataType===v.NUMERIC?e.jsxs(e.Fragment,{children:[a.results.mean!==void 0&&a.results.standardDeviation!==void 0&&e.jsxs(f,{children:[e.jsx(d,{sx:{pl:4},children:"Mean (SD)"}),e.jsxs(d,{align:"right",children:[a.results.mean.toFixed(2)," (",a.results.standardDeviation.toFixed(2),")"]})]}),a.results.range!==void 0&&e.jsxs(f,{children:[e.jsx(d,{sx:{pl:4},children:"Range"}),e.jsx(d,{align:"right",children:a.results.range.toFixed(2)})]})]}):a.results.frequencies&&(()=>{const t=n==null?void 0:n.columns.find(s=>s.name===a.variableName);return(t&&n?V(t.id,n):Object.keys(a.results.frequencies)).map((s,o)=>e.jsxs(f,{children:[e.jsx(d,{sx:{pl:4},children:s}),e.jsxs(d,{align:"right",children:[a.results.frequencies[s]," (",a.results.percentages[s].toFixed(1),"%)"]})]},o))})()]},r))})]})})]}),N&&e.jsxs(C,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(x,{variant:"h6",gutterBottom:!0,children:"Table Write-up"}),e.jsx(x,{variant:"body1",component:"pre",sx:{whiteSpace:"pre-wrap"},children:N})]}),e.jsx(g,{sx:{display:"flex",justifyContent:"center",mt:2},children:e.jsx(ue,{resultData:{title:`Table 1 - Descriptive Statistics (${n.name})`,type:"descriptive",component:"Table1",data:{dataset:n.name,variables:b.map(a=>{var r;return((r=p.find(t=>t.id===a))==null?void 0:r.name)||a}),results:R,writeUp:N,timestamp:new Date().toISOString(),totalSampleSize:n.data.length}},onSuccess:()=>{k("Results successfully added to Results Manager!"),$(!0)},onError:a=>{k(`Error adding results to Results Manager: ${a}`),$(!0)}})})]}),e.jsx(oe,{open:G,autoHideDuration:4e3,onClose:X,message:z})]})})};export{qe as default};
