import{j as t,B as K,e as q,R as _,g as Ve,G as J,ai as se,b9 as ne,ba as ie,bb as E,f as Ue,ae as Be,ao as ze,ap as He,aq as Ke,ar as Q,as as b,at as _e,bR as Je}from"./mui-libs-CfwFIaTD.js";import{r as V,b as Qe}from"./react-libs-Cr2nE3UY.js";import{a as Xe,D as $,g as re}from"./index-Bpan7Tbe.js";import{A as Ye}from"./AddToResultsButton-BwSXKCt2.js";import{P as Ze}from"./PublicationReadyGate-BGFbKbJc.js";import{b as $e,c as Oe,g as ye,h as Re,i as Ae}from"./descriptive-Djo0s6H4.js";import{c as et}from"./normality-CwHD6Rjl.js";import{i as tt}from"./t-tests-DXw1R1jD.js";import{o as at}from"./anova-DbTY6dHK.js";import{c as st,m as nt,k as it}from"./non-parametric-Cf6Ds91x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const St=()=>{var Te;const{datasets:k,currentDataset:le,setCurrentDataset:De}=Xe(),[be,Ee]=V.useState((le==null?void 0:le.id)||""),[A,xe]=V.useState(""),[L,oe]=V.useState([]),[ce,de]=V.useState("row"),[he,X]=V.useState(!1),[je,Y]=V.useState(null),[Z,C]=V.useState(null),[pe,M]=V.useState(null),[W,ee]=V.useState([]),[U,te]=V.useState({}),[Se,ae]=V.useState({}),[Fe,fe]=V.useState(!1),[Ie,Ne]=V.useState(""),h=k.find(e=>e.id===be),D=(h==null?void 0:h.columns)||[],me=D.filter(e=>e.type===$.CATEGORICAL),ue=D.filter(e=>e.id!==A),ve=D.find(e=>e.id===A),Pe=e=>{const l=e.target.value;Ee(l),xe(""),oe([]),C(null),M(null),ee([]),te({}),de("row"),ae({});const s=k.find(u=>u.id===l);s&&De(s)},Ge=e=>{const l=e.target.value;if(xe(l),oe([]),C(null),M(null),ee([]),te({}),de("row"),ae({}),h&&l){const s=D.find(u=>u.id===l);if(s&&s.type===$.CATEGORICAL){const u=Array.from(new Set(h.data.map(n=>String(n[s.name]))));ee(u);const g={};u.forEach(n=>{g[n]=h.data.filter(v=>String(v[s.name])===n).length}),te(g)}}},We=e=>{const l=e.target.value;oe(typeof l=="string"?l.split(","):l),C(null),M(null),ae({})},qe=e=>{de(e.target.value),Z&&we()},Le=e=>{const l=e.reduce((s,u)=>s+u,0);return l===0?e.map(()=>0):e.map(s=>s/l*100)},ke=e=>{if(e.length===0||e[0].length===0)return[];const l=e.length,s=e[0].length,u=new Array(s).fill(0);for(let n=0;n<s;n++)for(let v=0;v<l;v++)u[n]+=e[v][n];const g=[];for(let n=0;n<l;n++){const v=[];for(let x=0;x<s;x++)u[x]===0?v.push(0):v.push(e[n][x]/u[x]*100);g.push(v)}return g},we=()=>{if(!h||!A||L.length===0){Y("Please select a dataset, a grouping variable, and at least one row variable to analyze."),C(null),M(null);return}X(!0),Y(null);const e=[],l={};let s=0;const u="abcdefghijklmnopqrstuvwxyz",g=D.find(T=>T.id===A);if(!g||g.type!==$.CATEGORICAL){Y("Selected grouping variable is not categorical."),X(!1);return}const n=re(A,h);ee(n);const v=n.length===2,x=n.length>2;if(!v&&!x){Y("Grouping variable must have at least two categories."),X(!1);return}const y={};n.forEach(T=>{y[T]=h.data.filter(c=>String(c[g.name])===T).length}),te(y),L.forEach(T=>{const c=D.find(a=>a.id===T);if(!c){console.error(`Row variable with ID ${T} not found in selected dataset.`);return}const F=h.data.map(a=>a[c.name]),r={Overall:{n:h.data.length}};if(c.type===$.NUMERIC){const a=F.filter(f=>typeof f=="number"&&!isNaN(f));a.length>0?(r.Overall.mean=$e(a),r.Overall.standardDeviation=Oe(a),r.Overall.range=ye(a),r.Overall.n=a.length):r.Overall.n=0}else{const a=F.map(String);a.length>0?(r.Overall.frequencies=Re(a),r.Overall.percentages=Ae(a),Object.keys(r.Overall.percentages).forEach(f=>{r.Overall.percentages[f]*=100}),r.Overall.n=a.length):r.Overall.n=0}if(n.forEach(a=>{const f=h.data.filter(i=>String(i[g.name])===a).map(i=>i[c.name]);if(r[a]={n:f.length},c.type===$.NUMERIC){const i=f.filter(m=>typeof m=="number"&&!isNaN(m));i.length>0?(r[a].mean=$e(i),r[a].standardDeviation=Oe(i),r[a].range=ye(i),r[a].n=i.length):r[a].n=0}else{const i=f.map(String);i.length>0?(r[a].frequencies=Re(i),r[a].percentages=Ae(i),Object.keys(r[a].percentages).forEach(m=>{r[a].percentages[m]*=100}),r[a].n=i.length):r[a].n=0}}),c.type===$.CATEGORICAL){const a=re(c.id,h),f=[];a.forEach(m=>{const d=[];n.forEach(R=>{const I=h.data.filter(O=>String(O[g.name])===R&&String(O[c.name])===m).length;d.push(I)}),f.push(d)});const i=ke(f);a.forEach((m,d)=>{var B,z;n.forEach((j,N)=>{r[j].columnPercentages||(r[j].columnPercentages={}),r[j].columnPercentages[m]=i[d][N]});const R=n.map(j=>{var N,w;return((w=(N=r[j])==null?void 0:N.frequencies)==null?void 0:w[m])||0}),I=((z=(B=r.Overall)==null?void 0:B.frequencies)==null?void 0:z[m])||0,O=Le([...R,I]);n.forEach((j,N)=>{r[j].rowPercentages||(r[j].rowPercentages={}),r[j].rowPercentages[m]=O[N]}),r.Overall.rowPercentages||(r.Overall.rowPercentages={}),r.Overall.rowPercentages[m]=O[O.length-1]})}let p;if(c.type===$.CATEGORICAL){const a=re(c.id,h);if(n.length>=2&&a.length>=2)try{const f=[];a.forEach(m=>{const d=[];n.forEach(R=>{const I=h.data.filter(O=>String(O[g.name])===R&&String(O[c.name])===m).length;d.push(I)}),f.push(d)});const i=st(f);p={testName:"Chi-Square Test",pValue:i.pValue,statistic:i.chiSquare,df:i.df}}catch(f){console.error(`Error performing Chi-Square test for ${c.name}:`,f),p={testName:"Chi-Square Test",pValue:NaN}}}else if(c.type===$.NUMERIC){const a=n.map(i=>h.data.filter(d=>String(d[g.name])===i).map(d=>d[c.name]).filter(d=>typeof d=="number"&&!isNaN(d))),f=a.every(i=>{if(i.length<3)return!1;try{return et(i,.05,["auto"]).overallAssessment.isNormal}catch(m){return console.error(`Error checking normality for a group in ${c.name}:`,m),!1}});if(v){if(a.length===2){const i=a[0],m=a[1];if(i.length>=2&&m.length>=2)if(f)try{const d=tt(i,m,{equalVariances:!0});p={testName:"Independent Samples t-Test",pValue:d.pValue,statistic:d.t,df:d.df}}catch(d){console.error(`Error performing t-test for ${c.name}:`,d),p={testName:"Independent Samples t-Test",pValue:NaN}}else try{const d=nt(i,m);p={testName:"Mann-Whitney U Test",pValue:d.pValue,statistic:d.U}}catch(d){console.error(`Error performing Mann-Whitney U test for ${c.name}:`,d),p={testName:"Mann-Whitney U Test",pValue:NaN}}}}else if(x&&a.length>=2&&a.every(i=>i.length>0))if(f)try{const i=at(a);p={testName:"One-Way ANOVA",pValue:i.pValue,statistic:i.F,df:[i.dfBetween,i.dfWithin]}}catch(i){console.error(`Error performing One-Way ANOVA for ${c.name}:`,i),p={testName:"One-Way ANOVA",pValue:NaN}}else try{const i=it(a);p={testName:"Kruskal-Wallis Test",pValue:i.pValue,statistic:i.H,df:i.df}}catch(i){console.error(`Error performing Kruskal-Wallis test for ${c.name}:`,i),p={testName:"Kruskal-Wallis Test",pValue:NaN}}}const o={rowVariableName:c.name,rowDataType:c.type,descriptives:r,testResult:p};c.type===$.CATEGORICAL&&(o.categories=re(c.id,h)),p!=null&&p.testName&&!l[p.testName]&&(l[p.testName]=u[s%u.length],s++),p!=null&&p.testName&&(o.testSuperscript=l[p.testName]),e.push(o)}),C(e),ae(l),M(Ce(e,h,g,n)),X(!1)},Ce=(e,l,s,u)=>{var T,c,F,r,p;const g=l.data.length;let n=`Table 2 presents the descriptive statistics and group comparisons for various variables across different ${((T=s==null?void 0:s.name)==null?void 0:T.toLowerCase())||"groups"} from a dataset of ${g} observations. `;if(u.length>0){const o=u.map(a=>{const i=((U[a]||0)/g*100).toFixed(1);return`${a} (${i}%)`});n+=`The sample is divided into ${u.length} ${((c=s==null?void 0:s.name)==null?void 0:c.toLowerCase())||"groups"}: ${o.join(", ")}. `}const v=[];e.forEach(o=>{var a,f,i,m,d,R,I,O,B,z;if(o.rowDataType===$.NUMERIC){const j=[],N=[];if(u.forEach(w=>{const S=o.descriptives[w];(S==null?void 0:S.mean)!==void 0&&(S==null?void 0:S.standardDeviation)!==void 0&&(j.push(`${w} having a mean of ${S.mean.toFixed(2)} (SD ${S.standardDeviation.toFixed(2)})`),N.push({category:w,mean:S.mean,sd:S.standardDeviation}))}),N.length>0){const w=[...N].sort((G,H)=>H.mean-G.mean),S=((f=(a=o.descriptives.Overall)==null?void 0:a.mean)==null?void 0:f.toFixed(2))||"N/A",ge=((m=(i=o.descriptives.Overall)==null?void 0:i.standardDeviation)==null?void 0:m.toFixed(2))||"N/A";let P=`The mean ${o.rowVariableName.toLowerCase()} varies`;if(((d=o.testResult)==null?void 0:d.pValue)!==void 0&&o.testResult.pValue<.05&&(P+=" significantly"),P+=` across ${((R=s==null?void 0:s.name)==null?void 0:R.toLowerCase())||"groups"}, with ${w[0].category} having the highest mean of ${w[0].mean.toFixed(2)} (SD ${w[0].sd.toFixed(2)})`,w.length>1){const G=w.slice(1);G.length===1?P+=`, while ${G[0].category} has a lower mean of ${G[0].mean.toFixed(2)} (SD ${G[0].sd.toFixed(2)})`:P+=`, while other groups have means ranging from ${Math.min(...G.map(H=>H.mean)).toFixed(2)} to ${Math.max(...G.map(H=>H.mean)).toFixed(2)}`}P+=`. The overall mean ${o.rowVariableName.toLowerCase()} is ${S} (SD ${ge})`,((I=o.testResult)==null?void 0:I.pValue)!==void 0&&(o.testResult.pValue<.05?P+=`, and the difference is statistically significant (p=${o.testResult.pValue.toFixed(3)})`:P+=`, and the difference is not statistically significant (p=${o.testResult.pValue.toFixed(3)})`),v.push(P)}}else if(((O=o.testResult)==null?void 0:O.pValue)!==void 0){let j=`The distribution of ${o.rowVariableName.toLowerCase()} is`;if(o.testResult.pValue>=.05?j+=` relatively consistent across ${((B=s==null?void 0:s.name)==null?void 0:B.toLowerCase())||"groups"}, with no significant differences observed (p=${o.testResult.pValue.toFixed(3)})`:j+=` significantly different across ${((z=s==null?void 0:s.name)==null?void 0:z.toLowerCase())||"groups"} (p=${o.testResult.pValue.toFixed(3)})`,o.categories&&o.categories.length>0){const N=o.descriptives.Overall;if(N!=null&&N.frequencies){const w=Object.entries(N.frequencies).sort(([,S],[,ge])=>ge-S);if(w.length>0){const S=w[0][0];j+=`. ${S} is the most common category across all groups`}}}v.push(j)}}),v.length>0&&(n+=v.join(". ")+". ");const x=e.filter(o=>{var a;return((a=o.testResult)==null?void 0:a.pValue)!==void 0&&o.testResult.pValue<.05}),y=e.filter(o=>{var a;return((a=o.testResult)==null?void 0:a.pValue)!==void 0&&o.testResult.pValue>=.05});if(n+="Overall, the table highlights",x.length>0){const o=x.map(a=>a.rowVariableName.toLowerCase()).join(", ");if(n+=` significant differences in ${o} across ${((F=s==null?void 0:s.name)==null?void 0:F.toLowerCase())||"groups"}`,y.length>0){const a=y.map(f=>f.rowVariableName.toLowerCase()).join(", ");n+=`. Other variables, such as ${a}, show no significant differences across ${((r=s==null?void 0:s.name)==null?void 0:r.toLowerCase())||"groups"}`}}else n+=` no significant differences across ${((p=s==null?void 0:s.name)==null?void 0:p.toLowerCase())||"groups"} for the variables examined`;return n+=".",n},Me=()=>{fe(!1)};return t.jsx(Ze,{children:t.jsxs(K,{p:3,children:[t.jsx(q,{variant:"h5",gutterBottom:!0,children:"Publication Ready Table 2"}),t.jsxs(_,{elevation:2,sx:{p:2,mb:3},children:[t.jsx(q,{variant:"subtitle1",gutterBottom:!0,children:"Select Data and Variables"}),t.jsx(Ve,{severity:"info",sx:{mb:2},children:"This table presents descriptive statistics for selected variables, grouped by a chosen categorical variable. It also includes statistical tests to compare variables across groups (e.g., t-tests, ANOVA, Chi-Square)."}),t.jsxs(J,{container:!0,spacing:2,children:[t.jsx(J,{item:!0,xs:12,children:t.jsxs(se,{fullWidth:!0,margin:"normal",children:[t.jsx(ne,{id:"dataset-select-label",children:"Dataset"}),t.jsx(ie,{labelId:"dataset-select-label",id:"dataset-select",value:be,label:"Dataset",onChange:Pe,disabled:k.length===0,children:k.length===0?t.jsx(E,{value:"",disabled:!0,children:"No datasets available"}):k.map(e=>t.jsxs(E,{value:e.id,children:[e.name," (",e.data.length," rows)"]},e.id))})]})}),t.jsx(J,{item:!0,xs:12,children:t.jsxs(se,{fullWidth:!0,margin:"normal",children:[t.jsx(ne,{id:"grouping-variable-select-label",children:"Grouping Variable (Column)"}),t.jsxs(ie,{labelId:"grouping-variable-select-label",id:"grouping-variable-select",value:A,onChange:Ge,label:"Grouping Variable (Column)",disabled:me.length===0||!h,children:[t.jsx(E,{value:"",children:t.jsx("em",{children:"None"})}),me.length===0?t.jsx(E,{value:"",disabled:!0,children:"No categorical variables available"}):me.map(e=>t.jsx(E,{value:e.id,children:e.name},e.id))]})]})}),t.jsx(J,{item:!0,xs:12,children:t.jsxs(se,{fullWidth:!0,margin:"normal",children:[t.jsx(ne,{id:"row-variables-select-label",children:"Row Variables"}),t.jsx(ie,{labelId:"row-variables-select-label",id:"row-variables-select",multiple:!0,value:L,onChange:We,label:"Row Variables",disabled:ue.length===0||!A,renderValue:e=>e.map(l=>{var s;return((s=D.find(u=>u.id===l))==null?void 0:s.name)||""}).join(", "),children:ue.length===0?t.jsx(E,{value:"",disabled:!0,children:"Select a grouping variable first or no other variables available"}):ue.map(e=>t.jsxs(E,{value:e.id,children:[e.name," (",e.type,")"]},e.id))})]})}),L.some(e=>{var l;return((l=D.find(s=>s.id===e))==null?void 0:l.type)===$.CATEGORICAL})&&t.jsx(J,{item:!0,xs:12,children:t.jsxs(se,{fullWidth:!0,margin:"normal",children:[t.jsx(ne,{id:"percentage-type-select-label",children:"Percentage Type (Categorical)"}),t.jsxs(ie,{labelId:"percentage-type-select-label",id:"percentage-type-select",value:ce,onChange:qe,label:"Percentage Type (Categorical)",children:[t.jsx(E,{value:"row",children:"Row Percentage"}),t.jsx(E,{value:"column",children:"Column Percentage"})]})]})})]}),t.jsx(K,{mt:2,children:t.jsx(Ue,{variant:"contained",color:"primary",onClick:we,disabled:he||!h||!A||L.length===0,children:"Generate Table 2"})})]}),he&&t.jsx(K,{display:"flex",justifyContent:"center",my:4,children:t.jsx(Be,{})}),je&&t.jsx(Ve,{severity:"error",sx:{mb:3},children:je}),Z&&!he&&h&&A&&W.length>0&&t.jsxs(K,{children:[t.jsxs(_,{elevation:2,sx:{p:2,mb:3},children:[t.jsx(q,{variant:"h6",gutterBottom:!0,children:"Table 2. Descriptive Statistics and Group Comparisons"}),t.jsx(ze,{component:_,variant:"outlined",children:t.jsxs(He,{size:"small",children:[t.jsxs(Ke,{children:[t.jsxs(Q,{children:[t.jsx(b,{sx:{fontWeight:"bold"},rowSpan:2,children:"Variable"}),t.jsx(b,{sx:{fontWeight:"bold"},align:"center",colSpan:W.length+1,children:(ve==null?void 0:ve.name)||"Grouping Variable"}),t.jsx(b,{sx:{fontWeight:"bold"},align:"center",rowSpan:2,children:"p"})]}),t.jsxs(Q,{children:[W.map((e,l)=>t.jsxs(b,{sx:{fontWeight:"bold"},align:"center",children:[e," (n=",U[e]||0,")"]},l)),t.jsxs(b,{sx:{fontWeight:"bold"},align:"center",children:["Total (n=",h.data.length,")"]})]})]}),t.jsxs(_e,{children:[t.jsxs(Q,{children:[t.jsx(b,{sx:{fontWeight:"bold"},children:"Total n (%)"}),W.map((e,l)=>t.jsx(b,{align:"center",children:U[e]!==void 0?`${U[e]} (${(U[e]/h.data.length*100).toFixed(1)}%)`:"N/A"},l)),t.jsxs(b,{align:"center",children:[h.data.length," (100.0%)"]}),t.jsx(b,{})]}),Z.map((e,l)=>{var s,u,g;return t.jsxs(Qe.Fragment,{children:[t.jsxs(Q,{children:[t.jsx(b,{sx:{fontWeight:"bold"},children:e.rowVariableName}),e.rowDataType===$.NUMERIC?t.jsxs(t.Fragment,{children:[W.map((n,v)=>{var x,y;return t.jsx(b,{align:"center",children:((x=e.descriptives[n])==null?void 0:x.mean)!==void 0?`${e.descriptives[n].mean.toFixed(2)} (${((y=e.descriptives[n].standardDeviation)==null?void 0:y.toFixed(2))||"N/A"})`:"N/A"},v)}),t.jsx(b,{align:"center",children:((s=e.descriptives.Overall)==null?void 0:s.mean)!==void 0?`${e.descriptives.Overall.mean.toFixed(2)} (${((u=e.descriptives.Overall.standardDeviation)==null?void 0:u.toFixed(2))||"N/A"})`:"N/A"})]}):W.map((n,v)=>t.jsx(b,{},v)).concat(t.jsx(b,{},"total-empty")),t.jsx(b,{align:"center",children:((g=e.testResult)==null?void 0:g.pValue)!==void 0?`${e.testResult.pValue.toFixed(3)}${e.testSuperscript||""}`:"N/A"})]}),e.rowDataType===$.CATEGORICAL&&e.categories&&e.descriptives.Overall.frequencies&&e.descriptives.Overall.percentages&&e.categories.map((n,v)=>{var x,y,T,c,F,r;return t.jsxs(Q,{children:[t.jsx(b,{sx:{pl:4},children:n}),W.map((p,o)=>{var a,f,i,m,d,R;return t.jsx(b,{align:"center",children:((f=(a=e.descriptives[p])==null?void 0:a.frequencies)==null?void 0:f[n])!==void 0?`${e.descriptives[p].frequencies[n]} (${ce==="row"?(e.descriptives[p].frequencies[n]/(((m=(i=e.descriptives.Overall)==null?void 0:i.frequencies)==null?void 0:m[n])||1)*100).toFixed(1):((R=(d=e.descriptives[p].columnPercentages)==null?void 0:d[n])==null?void 0:R.toFixed(1))||"N/A"}%)`:"N/A"},o)}),t.jsx(b,{align:"center",children:((y=(x=e.descriptives.Overall)==null?void 0:x.frequencies)==null?void 0:y[n])!==void 0?`${e.descriptives.Overall.frequencies[n]} (${ce==="row"?(e.descriptives.Overall.frequencies[n]/(((c=(T=e.descriptives.Overall)==null?void 0:T.frequencies)==null?void 0:c[n])||1)*100).toFixed(1):((r=(F=e.descriptives.Overall.percentages)==null?void 0:F[n])==null?void 0:r.toFixed(1))||"N/A"}%)`:"N/A"}),t.jsx(b,{})]},`${l}-${v}`)})]},l)})]})]})})]}),Object.keys(Se).length>0&&t.jsxs(_,{elevation:2,sx:{p:2,mb:3},children:[t.jsx(q,{variant:"h6",gutterBottom:!0,children:"Statistical Tests Used"}),t.jsx(q,{variant:"body2",component:"div",children:Object.entries(Se).map(([e,l])=>t.jsxs("div",{children:[t.jsx("sup",{children:l}),": ",e]},e))})]}),pe&&t.jsxs(_,{elevation:2,sx:{p:2,mb:3},children:[t.jsx(q,{variant:"h6",gutterBottom:!0,children:"Table Write-up"}),t.jsx(q,{variant:"body1",component:"pre",sx:{whiteSpace:"pre-wrap"},children:pe})]}),t.jsx(K,{sx:{display:"flex",justifyContent:"center",mt:2},children:t.jsx(Ye,{resultData:{title:`Table 2 - Group Comparisons (${h.name})`,type:"other",component:"Table2",data:{dataset:h.name,groupingVariable:((Te=D.find(e=>e.id===A))==null?void 0:Te.name)||"Unknown",rowVariables:L.map(e=>{var l;return((l=D.find(s=>s.id===e))==null?void 0:l.name)||e}),results:Z,writeUp:pe,timestamp:new Date().toISOString(),totalSampleSize:h.data.length}},onSuccess:()=>{Ne("Results successfully added to Results Manager!"),fe(!0)},onError:e=>{Ne(`Error adding results to Results Manager: ${e}`),fe(!0)}})})]}),t.jsx(Je,{open:Fe,autoHideDuration:4e3,onClose:Me,message:Ie})]})})};export{St as default};
