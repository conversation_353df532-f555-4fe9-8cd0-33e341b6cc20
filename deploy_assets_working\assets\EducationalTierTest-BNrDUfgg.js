import{j as s,B as f,e,f as R,g as h,G as p,R as m,L as g,m as r,n as a,r as c,h as E,J as A,E as S,o as i,q as l,D as w,aF as v}from"./mui-libs-CfwFIaTD.js";import{b as T}from"./index-Bpan7Tbe.js";import{F,P as B}from"./PublicationReadyGate-BGFbKbJc.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const D=({children:x,fallback:j,showPrompt:t=!0,promptTitle:d,promptDescription:u})=>{const{canAccessAdvancedAnalysis:n,isEducationalUser:o,accountType:y,user:P}=T();if(n)return s.jsx(s.Fragment,{children:x});if(j)return s.jsx(s.Fragment,{children:j});if(!t)return null;const C=()=>P?o?"Advanced Analysis is included free with your educational account!":"Upgrade to Pro to access Advanced Analysis features including advanced statistical tests, data visualization, and analytical tools.":"Sign in to access Advanced Analysis features. Educational users (.edu emails) get free access!",b=()=>["Advanced statistical tests (ANOVA, Regression, etc.)","Interactive data visualizations","Correlation and factor analysis","Non-parametric tests","Custom analysis workflows","Export analysis results"];return s.jsx(F,{featureName:d||"Advanced Analysis",isEducationalUser:o,upgradeMessage:C(),description:u||"Unlock powerful statistical analysis tools and advanced visualizations for deeper data insights.",features:b(),showUpgradeButton:!o||y!=="edu"})},q=()=>{const{user:x,accountType:j,isEducationalUser:t,educationalTier:d,canAccessAdvancedAnalysis:u,canAccessPublicationReady:n,canAccessCloudStorage:o,canAccessProFeatures:y,refreshProfile:P}=T();console.log("🔍 Educational Tier Debug Info:",{user:x==null?void 0:x.email,accountType:j,isEducationalUser:t,educationalTier:d,canAccessAdvancedAnalysis:u,canAccessPublicationReady:n,canAccessCloudStorage:o,canAccessProFeatures:y});const b=(()=>{switch(j){case"edu":return{label:"Educational Free",color:"secondary",icon:s.jsx(A,{})};case"edu_pro":return{label:"Educational Pro",color:"primary",icon:s.jsx(A,{})};case"pro":return{label:"Pro",color:"primary",icon:s.jsx(S,{})};case"standard":return{label:"Standard",color:"default",icon:s.jsx(v,{})};default:return{label:"Guest",color:"default",icon:s.jsx(v,{})}}})(),I=async()=>{console.log("🔄 Manually refreshing profile..."),await P(),console.log("✅ Profile refresh completed")};return s.jsxs(f,{sx:{p:3},children:[s.jsxs(f,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[s.jsx(e,{variant:"h4",children:"Educational Tier Implementation Test"}),s.jsx(R,{variant:"contained",color:"primary",onClick:I,children:"🔄 Refresh Profile Data"})]}),s.jsx(h,{severity:"info",sx:{mb:3},children:s.jsxs(e,{variant:"body2",children:[s.jsx("strong",{children:"Testing Instructions:"}),s.jsx("br",{}),"1. Manually change accounttype and edu_subscription_type in Supabase",s.jsx("br",{}),'2. Click "Refresh Profile Data" button to fetch latest values',s.jsx("br",{}),"3. Check the debug information below to see if changes are reflected",s.jsx("br",{}),"4. Open browser console for detailed logging"]})}),s.jsxs(p,{container:!0,spacing:3,children:[s.jsx(p,{item:!0,xs:12,children:s.jsxs(m,{sx:{p:3,border:"2px solid",borderColor:"warning.main"},children:[s.jsx(e,{variant:"h6",gutterBottom:!0,color:"warning.main",children:"🔍 Database vs AuthContext Debug"}),s.jsx(h,{severity:"warning",sx:{mb:2},children:s.jsxs(e,{variant:"body2",children:[s.jsx("strong",{children:"Issue:"})," Manual database changes not reflected in UI",s.jsx("br",{}),s.jsx("strong",{children:"Cause:"})," AuthContext caches profile data and doesn't auto-refresh",s.jsx("br",{}),s.jsx("strong",{children:"Solution:"}),' Click "Refresh Profile Data" after making database changes']})}),s.jsx(e,{variant:"subtitle2",gutterBottom:!0,children:"Current AuthContext Values:"}),s.jsx(f,{sx:{bgcolor:"background.default",p:2,borderRadius:1,mb:2},children:s.jsx(e,{variant:"body2",component:"pre",sx:{fontFamily:"monospace"},children:`User Email: ${(x==null?void 0:x.email)||"Not logged in"}
Account Type: ${j||"null"}
Educational Tier: ${d||"null"}
Is Educational User: ${t}
Can Access Advanced Analysis: ${u}
Can Access Publication Ready: ${n}
Can Access Cloud Storage: ${o}
Legacy Can Access Pro Features: ${y}`})}),s.jsx(e,{variant:"body2",color:"text.secondary",children:"To test: Change values in Supabase → Click refresh button → Check if values update above"})]})}),s.jsx(p,{item:!0,xs:12,md:6,children:s.jsxs(m,{sx:{p:3},children:[s.jsx(e,{variant:"h6",gutterBottom:!0,children:"Account Information"}),s.jsxs(g,{children:[s.jsxs(r,{children:[s.jsx(a,{children:b.icon}),s.jsx(c,{primary:"Account Type",secondary:s.jsx(E,{label:b.label,color:b.color,size:"small"})})]}),s.jsxs(r,{children:[s.jsx(a,{children:s.jsx(A,{})}),s.jsx(c,{primary:"Educational User",secondary:s.jsx(E,{label:t?"Yes":"No",color:t?"success":"default",size:"small"})})]}),t&&s.jsxs(r,{children:[s.jsx(a,{children:s.jsx(S,{})}),s.jsx(c,{primary:"Educational Tier",secondary:s.jsx(E,{label:d||"None",color:d==="pro"?"primary":"secondary",size:"small"})})]}),s.jsxs(r,{children:[s.jsx(a,{children:x?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Authentication Status",secondary:x?`Logged in as ${x.email}`:"Not logged in"})]})]})]})}),s.jsx(p,{item:!0,xs:12,md:6,children:s.jsxs(m,{sx:{p:3},children:[s.jsx(e,{variant:"h6",gutterBottom:!0,children:"Feature Access Permissions"}),s.jsxs(g,{children:[s.jsxs(r,{children:[s.jsx(a,{children:u?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Advanced Analysis",secondary:u?"Access granted":"Access denied"})]}),s.jsxs(r,{children:[s.jsx(a,{children:n?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Publication Ready",secondary:n?"Access granted":"Access denied"})]}),s.jsxs(r,{children:[s.jsx(a,{children:o?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Cloud Storage",secondary:o?"Access granted":"Access denied"})]}),s.jsxs(r,{children:[s.jsx(a,{children:y?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Legacy Pro Features",secondary:y?"Access granted (backward compatibility)":"Access denied"})]})]})]})}),s.jsx(p,{item:!0,xs:12,children:s.jsxs(m,{sx:{p:3,border:"2px solid",borderColor:"error.main"},children:[s.jsx(e,{variant:"h6",gutterBottom:!0,color:"error",children:"🚨 Critical Issue Debug: Educational User Sidebar Access"}),s.jsx(h,{severity:"info",sx:{mb:2},children:s.jsxs(e,{variant:"body2",children:[s.jsx("strong",{children:"Issue:"})," Educational users should have access to Advanced Analysis features in the sidebar navigation.",s.jsx("br",{}),s.jsx("strong",{children:"Expected:"})," Educational users (accounttype = 'edu') should see enabled Advanced Analysis menu items.",s.jsx("br",{}),s.jsx("strong",{children:"Root Cause:"})," Sidebar was using legacy `canAccessProFeatures` instead of granular permissions."]})}),s.jsxs(f,{sx:{mb:2},children:[s.jsx(e,{variant:"subtitle2",gutterBottom:!0,children:"Permission Calculation Debug:"}),s.jsxs(g,{dense:!0,children:[s.jsxs(r,{children:[s.jsx(a,{children:u?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:`canAccessAdvancedAnalysis: ${u}`,secondary:"Should be TRUE for educational users (accounttype = 'edu')"})]}),s.jsxs(r,{children:[s.jsx(a,{children:n?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:`canAccessPublicationReady: ${n}`,secondary:"Should be FALSE for educational free users (requires upgrade)"})]}),s.jsxs(r,{children:[s.jsx(a,{children:y?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:`canAccessProFeatures (legacy): ${y}`,secondary:"Fixed to use canAccessAdvancedAnalysis for backward compatibility"})]})]})]}),t&&j==="edu"&&s.jsx(h,{severity:u?"success":"error",sx:{mb:2},children:s.jsx(e,{variant:"body2",children:u?"✅ FIXED: Educational user can access Advanced Analysis features!":"❌ ISSUE: Educational user cannot access Advanced Analysis features!"})})]})}),s.jsx(p,{item:!0,xs:12,children:s.jsxs(m,{sx:{p:3},children:[s.jsx(e,{variant:"h6",gutterBottom:!0,children:"Feature Gate Component Tests"}),s.jsx(e,{variant:"subtitle1",gutterBottom:!0,sx:{mt:3},children:"Advanced Analysis Gate Test:"}),s.jsx(D,{showPrompt:!1,children:s.jsx(h,{severity:"success",children:"✅ Advanced Analysis Gate: Access granted! This content is visible."})}),s.jsx(e,{variant:"subtitle1",gutterBottom:!0,sx:{mt:3},children:"Publication Ready Gate Test:"}),s.jsx(B,{showPrompt:!1,children:s.jsx(h,{severity:"success",children:"✅ Publication Ready Gate: Access granted! This content is visible."})})]})}),t&&s.jsx(p,{item:!0,xs:12,children:s.jsxs(m,{sx:{p:3},children:[s.jsxs(e,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(A,{}),"Educational Account Benefits"]}),j==="edu"&&s.jsx(h,{severity:"info",sx:{mb:2},children:s.jsxs(e,{variant:"body2",children:[s.jsx("strong",{children:"Educational Free Tier:"}),s.jsx("br",{}),"• You have free access to Advanced Analysis features",s.jsx("br",{}),"• Upgrade to Pro ($10/month) for Publication Ready tools and Cloud Storage",s.jsx("br",{}),"• No educational discount - same Pro pricing applies"]})}),j==="edu_pro"&&s.jsx(h,{severity:"success",sx:{mb:2},children:s.jsxs(e,{variant:"body2",children:[s.jsx("strong",{children:"Educational Pro Tier:"}),s.jsx("br",{}),"• You have full Pro feature access",s.jsx("br",{}),"• Advanced Analysis + Publication Ready + Cloud Storage",s.jsx("br",{}),"• Maintaining educational account status"]})}),s.jsx(w,{sx:{my:2}}),s.jsx(e,{variant:"subtitle2",gutterBottom:!0,children:"Feature Access Summary:"}),s.jsxs(g,{dense:!0,children:[s.jsxs(r,{children:[s.jsx(a,{children:s.jsx(i,{color:"success",fontSize:"small"})}),s.jsx(c,{primary:"Data Import & Local Storage"})]}),s.jsxs(r,{children:[s.jsx(a,{children:u?s.jsx(i,{color:"success",fontSize:"small"}):s.jsx(l,{color:"error",fontSize:"small"})}),s.jsx(c,{primary:"Advanced Analysis",secondary:u?"Included free":"Not available"})]}),s.jsxs(r,{children:[s.jsx(a,{children:n?s.jsx(i,{color:"success",fontSize:"small"}):s.jsx(l,{color:"error",fontSize:"small"})}),s.jsx(c,{primary:"Publication Ready",secondary:n?"Pro subscription active":"Requires Pro upgrade"})]}),s.jsxs(r,{children:[s.jsx(a,{children:o?s.jsx(i,{color:"success",fontSize:"small"}):s.jsx(l,{color:"error",fontSize:"small"})}),s.jsx(c,{primary:"Cloud Storage",secondary:o?"Pro subscription active":"Requires Pro upgrade"})]})]})]})}),s.jsx(p,{item:!0,xs:12,children:s.jsxs(m,{sx:{p:3},children:[s.jsx(e,{variant:"h6",gutterBottom:!0,children:"🔧 Educational Subscription Type Fix Test"}),s.jsx(e,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"This section tests the fix for educational users with edu_subscription_type = 'pro'"}),s.jsx(h,{severity:t&&d==="pro"&&n&&o?"success":t&&d==="pro"?"error":"info",sx:{mb:2},children:s.jsx(e,{variant:"body2",children:t&&d==="pro"&&n&&o?"✅ FIX WORKING: Educational user with Pro subscription has full Pro access!":t&&d==="pro"?"❌ FIX NEEDED: Educational user with Pro subscription lacks Pro features!":t&&d==="free"?"ℹ️ Educational user with free tier (expected limited access)":"ℹ️ Not an educational user or no subscription data"})}),s.jsxs(g,{dense:!0,children:[s.jsxs(r,{children:[s.jsx(a,{children:s.jsx(v,{color:"primary"})}),s.jsx(c,{primary:"Account Type",secondary:j||"Not set"})]}),s.jsxs(r,{children:[s.jsx(a,{children:s.jsx(v,{color:"primary"})}),s.jsx(c,{primary:"Educational Tier",secondary:d||"Not set"})]}),s.jsxs(r,{children:[s.jsx(a,{children:n?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Publication Ready Access",secondary:n?"✅ Access granted":"❌ Access denied"})]}),s.jsxs(r,{children:[s.jsx(a,{children:o?s.jsx(i,{color:"success"}):s.jsx(l,{color:"error"})}),s.jsx(c,{primary:"Cloud Storage Access",secondary:o?"✅ Access granted":"❌ Access denied"})]})]}),s.jsx(e,{variant:"body2",color:"text.secondary",sx:{mt:2,fontStyle:"italic"},children:"Expected behavior: Educational users with edu_subscription_type = 'pro' should have access to both Publication Ready and Cloud Storage features."})]})})]})]})};export{q as default};
