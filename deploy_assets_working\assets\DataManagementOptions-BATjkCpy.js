import{j as a,u as D,C,R as l,i as n,e,B as o,h as d,G as c,k as I,bz as E,aE as w,I as M,aF as p,d as k,l as T,f as B,bA as A,aG as i,aH as x,aI as m,aK as g,b$ as u,av as S}from"./mui-libs-CfwFIaTD.js";import{r as U}from"./react-libs-Cr2nE3UY.js";const h=[{name:"Data Import",shortDescription:"Import data from various sources",detailedDescription:"Import your datasets from local files (CSV, Excel, etc.) or connect to external sources like Google Sheets to begin your analysis.",path:"data-management/import",icon:a.jsx(i,{}),category:"Import",color:"#4CAF50"},{name:"Data Export",shortDescription:"Export your processed data",detailedDescription:"Save your cleaned and transformed datasets to various file formats for use in other applications or for sharing.",path:"data-management/export",icon:a.jsx(x,{}),category:"Export",color:"#2196F3"},{name:"Data Editor",shortDescription:"View and edit your datasets",detailedDescription:"Inspect and modify your data directly within the application. Make corrections, add new entries, or adjust variable properties.",path:"data-management/editor",icon:a.jsx(m,{}),category:"Edit",color:"#FF9800"},{name:"Data Transform",shortDescription:"Apply transformations to your data",detailedDescription:"Perform common data transformations such as creating new variables, recoding existing ones, or handling missing values.",path:"data-management/transform",icon:a.jsx(g,{}),category:"Transform",color:"#9C27B0"},{name:"Dataset Manager",shortDescription:"Manage your imported datasets",detailedDescription:"View a list of all your imported datasets, rename them, or remove them from your workspace.",path:"data-management/datasets",icon:a.jsx(u,{}),category:"Manage",color:"#795548"},{name:"Variable Editor",shortDescription:"Edit variable properties",detailedDescription:"Modify variable names, types, labels, and other properties.",path:"data-management/variable-editor",icon:a.jsx(S,{}),category:"Edit",color:"#00BCD4"}],G=({onNavigate:y})=>{const t=D(),[s,j]=U.useState("All"),f=["All","Import","Export","Edit","Transform","Manage"],b=s==="All"?h:h.filter(r=>r.category===s),v=r=>{switch(r){case"Import":return a.jsx(i,{});case"Export":return a.jsx(x,{});case"Edit":return a.jsx(m,{});case"Transform":return a.jsx(g,{});case"Manage":return a.jsx(u,{});default:return a.jsx(i,{})}};return a.jsxs(C,{maxWidth:"lg",sx:{py:4},children:[a.jsxs(l,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${n(t.palette.primary.main,.1)} 0%, ${n(t.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[a.jsx(e,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Data Management Tools"}),a.jsx(e,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Tools for importing, cleaning, transforming, and managing your data"}),a.jsx(e,{variant:"body1",color:"text.secondary",children:"Prepare your data for analysis with a comprehensive suite of data management tools."})]}),a.jsxs(o,{sx:{mb:4},children:[a.jsx(e,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),a.jsx(o,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:f.map(r=>a.jsx(d,{label:r,onClick:()=>j(r),variant:s===r?"filled":"outlined",color:s===r?"primary":"default",icon:r!=="All"?v(r):void 0,sx:{"&:hover":{backgroundColor:s===r?t.palette.primary.dark:n(t.palette.primary.main,.1)}}},r))})]}),a.jsx(c,{container:!0,spacing:3,children:b.map((r,V)=>a.jsx(c,{item:!0,xs:12,md:6,lg:4,children:a.jsxs(I,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:t.shadows[8],"& .launch-button":{backgroundColor:r.color,color:"white"}}},children:[a.jsx(E,{avatar:a.jsx(k,{sx:{bgcolor:r.color,width:48,height:48},children:r.icon}),title:a.jsx(e,{variant:"h6",fontWeight:"bold",children:r.name}),subheader:a.jsx(o,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:a.jsx(d,{label:r.category,size:"small",variant:"outlined",color:"primary"})}),action:a.jsx(w,{title:"More information",children:a.jsx(M,{size:"small",children:a.jsx(p,{})})})}),a.jsxs(T,{sx:{flexGrow:1,pt:0},children:[a.jsx(e,{variant:"body2",color:"text.secondary",paragraph:!0,children:r.shortDescription}),a.jsx(e,{variant:"body2",paragraph:!0,children:r.detailedDescription})]}),a.jsx(o,{sx:{p:2,pt:0},children:a.jsxs(B,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>y(r.path),endIcon:a.jsx(A,{}),sx:{borderColor:r.color,color:r.color,fontWeight:"bold","&:hover":{borderColor:r.color}},children:["Launch ",r.name]})})]})},r.name))}),a.jsx(l,{elevation:1,sx:{p:3,mt:4,backgroundColor:n(t.palette.info.main,.05),border:`1px solid ${n(t.palette.info.main,.2)}`},children:a.jsxs(o,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[a.jsx(p,{color:"info"}),a.jsxs(o,{children:[a.jsx(e,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),a.jsxs(e,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Bringing data in?"})," Use Data Import"]}),a.jsxs(e,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Saving your work?"})," Use Data Export"]}),a.jsxs(e,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Correcting values?"})," Use the Data Editor"]}),a.jsxs(e,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Modifying variables?"})," Use Data Transform"]}),a.jsxs(e,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Modifying variable properties?"})," Use the Variable Editor"]}),a.jsxs(e,{variant:"body2",color:"text.secondary",children:["• ",a.jsx("strong",{children:"Viewing/deleting datasets?"})," Use the Dataset Manager"]})]})]})})]})};export{G as D,h as d};
