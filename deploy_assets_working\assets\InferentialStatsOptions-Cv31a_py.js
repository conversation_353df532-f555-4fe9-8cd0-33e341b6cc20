import{j as e,u as T,C,R as p,i as o,e as a,B as s,h as m,G as h,k as N,bz as D,aE as O,I as V,aF as f,d as W,l as k,f as I,bA as P,X as u,a0 as S,ad as F,Z as i,N as g,c3 as l,an as B}from"./mui-libs-CfwFIaTD.js";import{r as U}from"./react-libs-Cr2nE3UY.js";const x=[{name:"One-Sample T-Test",shortDescription:"Compare one sample mean to a hypothesized value",detailedDescription:"Perform a one-sample t-test to determine if the mean of a single sample is significantly different from a known or hypothesized population mean.",path:"inferential-stats/one-sample-ttest",icon:e.jsx(u,{}),category:"T-Tests",color:"#2196F3"},{name:"Independent Samples T-Test",shortDescription:"Compare means of two independent groups",detailedDescription:"Perform an independent samples t-test to compare the means of two unrelated groups and determine if there is a statistically significant difference between them.",path:"inferential-stats/independent-samples-ttest",icon:e.jsx(S,{}),category:"T-Tests",color:"#4CAF50"},{name:"Paired Samples T-Test",shortDescription:"Compare means of two related samples",detailedDescription:"Perform a paired samples t-test to compare the means of two related samples or repeated measurements on the same subjects and determine if there is a statistically significant difference.",path:"inferential-stats/paired-samples-ttest",icon:e.jsx(F,{}),category:"T-Tests",color:"#FF9800"},{name:"Mann-Whitney U Test",shortDescription:"Non-parametric alternative to Independent T-Test",detailedDescription:"Use the Mann-Whitney U test (also known as the Wilcoxon rank-sum test) to compare two independent groups when the assumptions for the independent samples t-test are not met.",path:"inferential-stats/mann-whitney-u-test",icon:e.jsx(i,{}),category:"Non-Parametric",color:"#9C27B0"},{name:"Wilcoxon Signed-Rank Test",shortDescription:"Non-parametric alternative to Paired T-Test",detailedDescription:"Use the Wilcoxon signed-rank test to compare two related samples or repeated measurements when the assumptions for the paired samples t-test are not met.",path:"inferential-stats/wilcoxon-signed-rank-test",icon:e.jsx(i,{}),category:"Non-Parametric",color:"#E91E63"},{name:"Kruskal-Wallis Test",shortDescription:"Non-parametric alternative to One-Way ANOVA",detailedDescription:"Use the Kruskal-Wallis test to compare three or more independent groups when the assumptions for one-way ANOVA are not met.",path:"inferential-stats/kruskal-wallis-test",icon:e.jsx(i,{}),category:"Non-Parametric",color:"#00BCD4"},{name:"Friedman Test",shortDescription:"Non-parametric alternative to Repeated Measures ANOVA",detailedDescription:"Use the Friedman test to compare three or more related groups or repeated measurements when the assumptions for repeated measures ANOVA are not met.",path:"inferential-stats/friedman-test",icon:e.jsx(i,{}),category:"Non-Parametric",color:"#795548"},{name:"Chi-Square Test",shortDescription:"Test association between categorical variables",detailedDescription:"Perform a Chi-Square test of independence to determine if there is a statistically significant association between two categorical variables.",path:"inferential-stats/chi-square-test",icon:e.jsx(g,{}),category:"Association",color:"#607D8B"},{name:"One-Way ANOVA",shortDescription:"Compare means of three or more independent groups",detailedDescription:"Perform a one-way Analysis of Variance (ANOVA) to determine if there are any statistically significant differences between the means of three or more independent groups.",path:"inferential-stats/one-way-anova",icon:e.jsx(l,{}),category:"ANOVA",color:"#FFC107"},{name:"Repeated Measures ANOVA",shortDescription:"Compare means of three or more related samples",detailedDescription:"Perform a repeated measures Analysis of Variance (ANOVA) to determine if there are any statistically significant differences between the means of three or more related samples or repeated measurements.",path:"inferential-stats/repeated-measures-anova",icon:e.jsx(l,{}),category:"ANOVA",color:"#FF5722"},{name:"Two-Way ANOVA",shortDescription:"Examine effects of two factors on a dependent variable",detailedDescription:"Perform a two-way Analysis of Variance (ANOVA) to examine the main effects of two independent categorical variables (factors) and their interaction effect on a continuous dependent variable.",path:"inferential-stats/two-way-anova",icon:e.jsx(l,{}),category:"ANOVA",color:"#3F51B5"}],R=({onNavigate:y,initialCategory:c})=>{const r=T(),d={"t-tests":"T-Tests","non-parametric-tests":"Non-Parametric",anova:"ANOVA"},j=c&&d[c]?d[c]:"All",[n,A]=U.useState(j),b=["All","T-Tests","ANOVA","Non-Parametric","Association"],w=n==="All"?x:x.filter(t=>t.category===n),v=t=>{switch(t){case"T-Tests":return e.jsx(u,{});case"ANOVA":return e.jsx(l,{});case"Non-Parametric":return e.jsx(i,{});case"Association":return e.jsx(g,{});default:return e.jsx(B,{})}};return e.jsxs(C,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(p,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${o(r.palette.primary.main,.1)} 0%, ${o(r.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(a,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Inferential Statistics Tools"}),e.jsx(a,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Draw conclusions and make predictions about populations"}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Access a suite of tools for hypothesis testing and inferring properties of a population from a sample, including t-tests, ANOVA, non-parametric tests, and tests of association."})]}),e.jsxs(s,{sx:{mb:4},children:[e.jsx(a,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),e.jsx(s,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:b.map(t=>e.jsx(m,{label:t,onClick:()=>A(t),variant:n===t?"filled":"outlined",color:n===t?"primary":"default",icon:t!=="All"?v(t):void 0,sx:{"&:hover":{backgroundColor:n===t?r.palette.primary.dark:o(r.palette.primary.main,.1)}}},t))})]}),e.jsx(h,{container:!0,spacing:3,children:w.map((t,E)=>e.jsx(h,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(N,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:r.shadows[8],"& .launch-button":{backgroundColor:t.color,color:"white"}}},children:[e.jsx(D,{avatar:e.jsx(W,{sx:{bgcolor:t.color,width:48,height:48},children:t.icon}),title:e.jsx(a,{variant:"h6",fontWeight:"bold",children:t.name}),subheader:e.jsx(s,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:e.jsx(m,{label:t.category,size:"small",variant:"outlined",color:"primary"})}),action:e.jsx(O,{title:"More information",children:e.jsx(V,{size:"small",children:e.jsx(f,{})})})}),e.jsxs(k,{sx:{flexGrow:1,pt:0},children:[e.jsx(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:t.shortDescription}),e.jsx(a,{variant:"body2",paragraph:!0,children:t.detailedDescription})]}),e.jsx(s,{sx:{p:2,pt:0},children:e.jsxs(I,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>y(t.path),endIcon:e.jsx(P,{}),sx:{borderColor:t.color,color:t.color,fontWeight:"bold","&:hover":{borderColor:t.color}},children:["Launch ",t.name]})})]})},t.name))}),e.jsx(p,{elevation:1,sx:{p:3,mt:4,backgroundColor:o(r.palette.info.main,.05),border:`1px solid ${o(r.palette.info.main,.2)}`},children:e.jsxs(s,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[e.jsx(f,{color:"info"}),e.jsxs(s,{children:[e.jsx(a,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),e.jsxs(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Comparing two groups?"})," Use T-Tests (parametric) or Mann-Whitney/Wilcoxon (non-parametric)."]}),e.jsxs(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Comparing three or more groups?"})," Use ANOVA (parametric) or Kruskal-Wallis/Friedman (non-parametric)."]}),e.jsxs(a,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Examining relationships between categorical variables?"})," Use the Chi-Square Test."]}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Analyzing effects of multiple factors?"})," Consider Two-Way ANOVA."]})]})]})})]})};export{R as I,x as i};
