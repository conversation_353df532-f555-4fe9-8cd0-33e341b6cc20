import { saveDataset, loadDataset, listDatasets, deleteDataset } from '../datasetService';
import { supabase } from '../../supabaseClient';

// Mock Supabase
jest.mock('../../supabaseClient', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
      getSession: jest.fn()
    },
    storage: {
      from: jest.fn().mockReturnValue({
        upload: jest.fn()
      })
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis()
  }
}));

describe('datasetService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('saveDataset', () => {
    it('should check authentication before saving', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await saveDataset('Test Dataset', { data: [], variableInfo: [] });

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });

    it('should deny access for Standard users (cloud storage restriction)', async () => {
      // Mock authenticated Standard user
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: {} } });
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } }
      });

      // Mock profile fetch returning Standard account
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { accounttype: 'standard', edu_subscription_type: null },
              error: null
            })
          })
        })
      });

      const result = await saveDataset('Test Dataset', { data: [], variableInfo: [] });

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('Cloud storage access requires a Pro account');
    });

    it('should allow access for Pro users', async () => {
      // Mock authenticated Pro user
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: {} } });
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } }
      });

      // Mock profile fetch returning Pro account
      const mockProfileQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { accounttype: 'pro', edu_subscription_type: null },
              error: null
            })
          })
        })
      };

      // Mock dataset existence check
      const mockDatasetQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' } // Not found
              })
            })
          })
        })
      };

      // Mock storage upload
      const mockStorageUpload = jest.fn().mockResolvedValue({
        data: { path: 'test-path' },
        error: null
      });

      // Mock database insert
      const mockInsert = {
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'test-id', dataset_name: 'Test Dataset' },
              error: null
            })
          })
        })
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(mockProfileQuery) // First call for profile
        .mockReturnValueOnce(mockDatasetQuery) // Second call for dataset existence
        .mockReturnValueOnce(mockInsert); // Third call for insert

      (supabase.storage.from as jest.Mock).mockReturnValue({
        upload: mockStorageUpload
      });

      const result = await saveDataset('Test Dataset', { data: [{ test: 'data' }], variableInfo: [] });

      expect(result.error).toBeFalsy();
      expect(result.data).toBeTruthy();
    });

    it('should allow access for Educational Pro users', async () => {
      // Mock authenticated Educational Pro user
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: {} } });
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } }
      });

      // Mock profile fetch returning Educational Pro account
      const mockProfileQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { accounttype: 'edu_pro', edu_subscription_type: 'pro' },
              error: null
            })
          })
        })
      };

      // Mock other required queries (similar to Pro user test)
      const mockDatasetQuery = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' }
              })
            })
          })
        })
      };

      const mockStorageUpload = jest.fn().mockResolvedValue({
        data: { path: 'test-path' },
        error: null
      });

      const mockInsert = {
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'test-id', dataset_name: 'Test Dataset' },
              error: null
            })
          })
        })
      };

      (supabase.from as jest.Mock)
        .mockReturnValueOnce(mockProfileQuery)
        .mockReturnValueOnce(mockDatasetQuery)
        .mockReturnValueOnce(mockInsert);

      (supabase.storage.from as jest.Mock).mockReturnValue({
        upload: mockStorageUpload
      });

      const result = await saveDataset('Test Dataset', { data: [{ test: 'data' }], variableInfo: [] });

      expect(result.error).toBeFalsy();
      expect(result.data).toBeTruthy();
    });

    it('should deny access for Educational users without Pro subscription', async () => {
      // Mock authenticated Educational user (free tier)
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: {} } });
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } }
      });

      // Mock profile fetch returning Educational free account
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { accounttype: 'edu', edu_subscription_type: 'free' },
              error: null
            })
          })
        })
      });

      const result = await saveDataset('Test Dataset', { data: [], variableInfo: [] });

      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('Cloud storage access requires a Pro account');
    });

    it('should check dataset size before saving', async () => {
      // Mock authenticated user
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ 
        data: { user: { id: 'test-user-id' } } 
      });

      // Create a large dataset that exceeds 2MB
      const largeData = Array(100000).fill({ value: 'x'.repeat(1000) });
      
      const result = await saveDataset('Large Dataset', { 
        data: largeData, 
        variableInfo: [] 
      });
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('2MB limit');
    });

    it('should check dataset count before saving a new dataset', async () => {
      // Mock authenticated user
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ 
        data: { user: { id: 'test-user-id' } } 
      });

      // Mock listDatasets to return 2 datasets
      jest.spyOn(global, 'listDatasets').mockResolvedValue({
        data: [
          { id: 'dataset1', dataset_name: 'Dataset 1' },
          { id: 'dataset2', dataset_name: 'Dataset 2' }
        ],
        error: null
      });

      // Mock dataset not existing
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' }
              })
            })
          })
        })
      });

      const result = await saveDataset('New Dataset', { 
        data: [{ value: 'test' }], 
        variableInfo: [] 
      });
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('maximum limit of 2');
    });
  });

  describe('loadDataset', () => {
    it('should check authentication before loading', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await loadDataset('test-dataset-id');
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });
  });

  describe('listDatasets', () => {
    it('should check authentication before listing', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await listDatasets();
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });
  });

  describe('deleteDataset', () => {
    it('should check authentication before deleting', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await deleteDataset('test-dataset-id');
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });
  });
});