import <PERSON> from 'papaparse';
import * as XLSX from 'xlsx';
import {
  Dataset,
  Column,
  DataType,
  VariableRole,
  DataValue,
  DataRow,
  FilterCondition,
  SortCondition,
  MissingValueReport,
  OutlierReport
} from '../types';
import { v4 as uuidv4 } from 'uuid';
import { extractCategoricalValuesWithMissingCodes } from './missingDataUtils';

/**
 * Data Import/Export Functions
 */

// Import CSV data
export const importCSV = (
  file: File,
  options: {
    header?: boolean;
    skipEmptyLines?: boolean;
    dynamicTyping?: boolean;
  } = {}
): Promise<Dataset> => {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true,
      ...options,
    };

    Papa.parse(file, {
      ...defaultOptions,
      complete: (results) => {
        try {
          const papaData = results.data as any[];
          if (!papaData || papaData.length === 0) {
            throw new Error('CSV file is empty or could not be parsed.');
          }

          const headers = defaultOptions.header
            ? Object.keys(papaData[0] || {})
            : (papaData[0] as any[]).map((_, i) => `Column ${i + 1}`);

          const data = papaData.map((row: any) => {
            if (Array.isArray(row)) {
              const obj: Record<string, any> = {};
              headers.forEach((header, index) => {
                obj[header] = row[index];
              });
              return obj;
            }
            return row as Record<string, any>;
          }).filter(row => headers.some(header => row[header] !== undefined && row[header] !== null && String(row[header]).trim() !== "")); // Filter out completely empty rows based on headers


          const dataset: Dataset = {
            id: uuidv4(),
            name: file.name.replace(/\.[^/.]+$/, ''), // Remove file extension
            dateCreated: new Date(),
            dateModified: new Date(),
            columns: [],
            data: data,
          };

          dataset.columns = headers.map((headerName) => { // Renamed header to headerName for clarity
            const values = data.map((row) =>
              defaultOptions.header ? row[headerName] : row[headers.indexOf(headerName)]
            );
            const detectedType = detectDataType(values);

            return {
              id: uuidv4(),
              name: headerName,
              type: detectedType,
              role: VariableRole.NONE,
            };
          });

          resolve(dataset);
        } catch (error) {
          reject(error);
        }
      },
      error: (error) => {
        reject(error);
      },
    });
  });
};

// Import Excel data
export const importExcel = (
  file: File,
  options: {
    sheetName?: string; // Optional: specify sheet name
    sheetIndex?: number; // Optional: specify sheet index (0-based)
  } = {}
): Promise<Dataset> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const arrayBuffer = event.target?.result;
        if (!arrayBuffer) {
          throw new Error('Could not read file buffer.');
        }
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });

        let determinedSheetName = options.sheetName;
        if (!determinedSheetName && options.sheetIndex !== undefined && workbook.SheetNames[options.sheetIndex]) {
          determinedSheetName = workbook.SheetNames[options.sheetIndex];
        }
        if (!determinedSheetName) {
          determinedSheetName = workbook.SheetNames[0];
        }

        if (!determinedSheetName || !workbook.Sheets[determinedSheetName]) {
          throw new Error(`Sheet "${determinedSheetName || (options.sheetIndex !== undefined ? `index ${options.sheetIndex}` : 'default')}" not found or workbook is empty.`);
        }

        const worksheet = workbook.Sheets[determinedSheetName];
        const jsonData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: "" });

        if (jsonData.length === 0) {
          throw new Error('Sheet is empty or could not be parsed.');
        }

        const headers: string[] = jsonData[0].map(String);
        const dataRows: any[][] = jsonData.slice(1);

        const data: Record<string, any>[] = dataRows.map((rowArray: any[]): Record<string, any> => {
          const rowObject: Record<string, any> = {};
          headers.forEach((header, index) => {
            rowObject[header] = rowArray[index];
          });
          return rowObject;
        }).filter(row => headers.some(header => row[header] !== undefined && row[header] !== null && String(row[header]).trim() !== "")); // Filter out completely empty rows


        const dataset: Dataset = {
          id: uuidv4(),
          name: file.name.replace(/\.[^/.]+$/, ''),
          dateCreated: new Date(),
          dateModified: new Date(),
          columns: [],
          data: data,
        };

        dataset.columns = headers.map((headerName) => {
          const values = data.map((row) => row[headerName]);
          const detectedType = detectDataType(values);

          return {
            id: uuidv4(),
            name: headerName,
            type: detectedType,
            role: VariableRole.NONE,
          };
        });

        resolve(dataset);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => {
      reject(error);
    };

    reader.readAsArrayBuffer(file);
  });
};

// Export dataset to CSV
export const exportCSV = (dataset: Dataset): string => {
  return Papa.unparse(dataset.data);
};

// Export dataset to Excel
export const exportExcel = (dataset: Dataset, fileName?: string): void => {
  try {
    const worksheet = XLSX.utils.json_to_sheet(dataset.data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    const finalFileName = fileName || `${dataset.name || 'dataset'}.xlsx`;

    XLSX.writeFile(workbook, finalFileName);
  } catch (error) {
    console.error("Failed to export Excel file:", error);
    throw new Error(`Failed to export to Excel: ${error instanceof Error ? error.message : String(error)}`);
  }
};

// Import JSON data
export const importJSON = (file: File): Promise<Dataset> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const textContent = event.target?.result as string;
        if (!textContent) {
          throw new Error('Could not read file content.');
        }

        const jsonData = JSON.parse(textContent);

        if (!Array.isArray(jsonData) || jsonData.length === 0) {
          throw new Error('JSON file must contain an array of objects.');
        }

        // Assuming the JSON is an array of objects, use keys from the first object as headers
        const headers = Object.keys(jsonData[0] || {});

        if (headers.length === 0) {
             throw new Error('JSON objects must have properties to be used as headers.');
        }

        const data: Record<string, any>[] = jsonData.map((row: any) => {
            const rowObject: Record<string, any> = {};
            headers.forEach(header => {
                rowObject[header] = row[header];
            });
            return rowObject;
        });


        const dataset: Dataset = {
          id: uuidv4(),
          name: file.name.replace(/\.[^/.]+$/, ''), // Remove file extension
          dateCreated: new Date(),
          dateModified: new Date(),
          columns: [],
          data: data,
        };

        dataset.columns = headers.map((headerName) => {
          const values = data.map((row) => row[headerName]);
          const detectedType = detectDataType(values);

          return {
            id: uuidv4(),
            name: headerName,
            type: detectedType,
            role: VariableRole.NONE,
          };
        });

        resolve(dataset);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => {
      reject(error);
    };

    reader.readAsText(file); // Read as text for JSON
  });
};


/**
 * Data Type Detection
 */
export const detectDataType = (values: DataValue[]): DataType => {
  const nonNullValues = values.filter((value): value is NonNullable<DataValue> =>
    value !== null && value !== undefined && String(value).trim() !== ''
  );

  if (nonNullValues.length === 0) {
    return DataType.TEXT;
  }

  // Check if values are strictly 0 or 1 (should be numeric)
  const allZeroOne = nonNullValues.every((value) => {
    const numValue = Number(value);
    return (numValue === 0 || numValue === 1) && !isNaN(numValue);
  });

  if (allZeroOne) {
    return DataType.NUMERIC;
  }

  // Check if all values are Yes/No (should be categorical)
  const allYesNo = nonNullValues.every((value) => {
    const lowerValue = String(value).toLowerCase();
    return lowerValue === 'yes' || lowerValue === 'no';
  });

  if (allYesNo) {
    return DataType.CATEGORICAL;
  }

  // Check if all values are boolean (true/false only)
  const allBoolean = nonNullValues.every((value) => {
    if (typeof value === 'boolean') return true;
    const lowerValue = String(value).toLowerCase();
    return lowerValue === 'true' || lowerValue === 'false';
  });

  if (allBoolean) {
    return DataType.BOOLEAN;
  }

  // Check for date patterns - be more conservative to avoid false positives
  const allDates = nonNullValues.every((value) => {
    const stringValue = String(value).trim();

    // Skip pure numeric values that are likely not dates (like age, scores, etc.)
    if (typeof value === 'number' || /^\d+(\.\d+)?$/.test(stringValue)) {
      // Only consider as potential date if it looks like a timestamp or Excel serial
      if (typeof value === 'number') {
        // Unix timestamp check - must be in reasonable timestamp range
        if (stringValue.length === 10 && value > 946684800 && value < 4102444800) { // 2000-2100 range
          const date = new Date(value * 1000);
          return !isNaN(date.getTime()) && date.getFullYear() >= 2000 && date.getFullYear() <= 2100;
        }
        // Excel date serial number - must be in reasonable range (36526 = 2000-01-01)
        if (stringValue.length === 5 && value >= 36526 && value <= 73050) { // 2000-2100 range
          const excelEpoch = new Date(1899, 11, 30);
          const date = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000);
          return !isNaN(date.getTime()) && date.getFullYear() >= 2000 && date.getFullYear() <= 2100;
        }
        // Other numeric values are not dates
        return false;
      }
    }

    // For string values, check common date patterns
    if (typeof value === 'string') {
      // Common date patterns: YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, etc.
      const datePatterns = [
        /^\d{4}-\d{1,2}-\d{1,2}$/,           // YYYY-MM-DD
        /^\d{1,2}\/\d{1,2}\/\d{4}$/,         // MM/DD/YYYY or DD/MM/YYYY
        /^\d{1,2}-\d{1,2}-\d{4}$/,           // MM-DD-YYYY or DD-MM-YYYY
        /^\d{4}\/\d{1,2}\/\d{1,2}$/,         // YYYY/MM/DD
        /^\d{1,2}\.\d{1,2}\.\d{4}$/,         // DD.MM.YYYY
        /^\w{3}\s+\d{1,2},?\s+\d{4}$/,       // Mon DD, YYYY
        /^\d{1,2}\s+\w{3}\s+\d{4}$/,         // DD Mon YYYY
      ];

      const matchesDatePattern = datePatterns.some(pattern => pattern.test(stringValue));
      if (matchesDatePattern) {
        const parsedDate = new Date(stringValue);
        return !isNaN(parsedDate.getTime()) && parsedDate.getFullYear() >= 1900 && parsedDate.getFullYear() <= 2100;
      }

      // Try general date parsing only if it looks like it could be a date
      if (stringValue.includes('/') || stringValue.includes('-') || stringValue.includes('.') ||
          /\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i.test(stringValue)) {
        const parsedDate = new Date(stringValue);
        return !isNaN(parsedDate.getTime()) && parsedDate.getFullYear() >= 1900 && parsedDate.getFullYear() <= 2100;
      }
    }

    return false;
  });

  if (allDates && nonNullValues.length > 0) {
    return DataType.DATE;
  }

  const allNumeric = nonNullValues.every((value) => {
    const sValue = String(value).trim();
    if (sValue === "") return false; // Should have been caught by filter, but double check
    return !isNaN(Number(sValue));
  });

  if (allNumeric) {
    const uniqueValues = new Set(nonNullValues.map(v => Number(v))); // Convert to number for proper Set comparison
    // If there are few unique numeric values compared to total values, it might be categorical (e.g., rating 1-5)
    // Increased threshold for categorical detection for numeric types
    if (uniqueValues.size <= 15 && uniqueValues.size < nonNullValues.length * 0.5 && nonNullValues.length > 5) {
        // Check if values are integers, more likely to be categorical if so
        const allIntegers = nonNullValues.every(v => Number.isInteger(Number(v)));
        if (allIntegers) return DataType.CATEGORICAL;
    }
    return DataType.NUMERIC;
  }

  // If not clearly boolean, date, or numeric, check for categorical based on unique value count
  const uniqueStringValues = new Set(nonNullValues.map(String));
  if (uniqueStringValues.size <= 20 && uniqueStringValues.size < nonNullValues.length * 0.7 && nonNullValues.length > 5) {
      return DataType.CATEGORICAL;
  }

  return DataType.TEXT;
};


/**
 * Data Transformation Functions
 */

// Recode values
export const recodeValues = (
  data: DataRow[],
  column: string,
  mappings: Record<string, DataValue>
): DataRow[] => {
  return data.map((row) => {
    const newRow = { ...row };
    const value = row[column];
    // Iterate through mapping keys to find a strict match
    for (const key in mappings) {
      // Need to handle potential type differences between data value and mapping key
      // Attempt to match based on original type if possible, otherwise use strict equality
      let keyToCompare: DataValue = key;
      // Attempt to convert key to number if value is number and key is numeric string
      if (typeof value === 'number' && !isNaN(value) && !isNaN(Number(key))) {
          keyToCompare = Number(key);
      } else if (typeof value === 'boolean' && (key.toLowerCase() === 'true' || key.toLowerCase() === 'false')) {
         keyToCompare = key.toLowerCase() === 'true';
      }


      if (value === keyToCompare) {
        newRow[column] = mappings[key];
        break; // Found a match, no need to check other keys
      }
    }
    return newRow;
  });
};

// Standardize numeric data (z-score)
export const standardizeValues = (
  data: DataRow[],
  column: string
): DataRow[] => {
  const values = data.map((row) => row[column]).filter((val): val is number => typeof val === 'number' && !isNaN(val));

  if(values.length === 0) return data;

  const sum = values.reduce((acc, val) => acc + val, 0);
  const mean = sum / values.length;
  const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
  const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / values.length;
  const stdDev = Math.sqrt(variance);

  return data.map((row) => {
    const newRow = { ...row };
    if (typeof row[column] === 'number' && !isNaN(row[column])) {
      newRow[column] = stdDev === 0 ? 0 : (row[column] - mean) / stdDev;
    }
    return newRow;
  });
};

// Log transform numeric data
export const logTransform = (
  data: DataRow[],
  column: string,
  base: number = Math.E // Default to natural log
): DataRow[] => {
  return data.map((row) => {
    const newRow = { ...row };
    const value = row[column];
    if (typeof value === 'number' && !isNaN(value) && value > 0) {
      newRow[column] = Math.log(value) / Math.log(base);
    }
    return newRow;
  });
};

// Bin numeric data into categories
export const binValues = (
  data: DataRow[],
  column: string,
  numBins: number
): DataRow[] => {
  const values = data.map((row) => row[column]).filter((val): val is number => typeof val === 'number' && !isNaN(val));

  if(values.length === 0 || numBins <=0) return data;

  const min = Math.min(...values);
  const max = Math.max(...values);

  if (min === max) { // All values are the same, put them in one bin
    return data.map(row => ({...row, [column]: typeof row[column] === 'number' ? `Bin 1 (${min})` : row[column]}));
  }

  const binWidth = (max - min) / numBins;

  const bins = Array.from({ length: numBins }, (_, i) => {
    const binMin = min + i * binWidth;
    const binMax = min + (i + 1) * binWidth;
    // Adjust label for clarity, especially for the last bin
    const label = i === numBins - 1
        ? `${binMin.toFixed(2)} - ${binMax.toFixed(2)}`
        : `${binMin.toFixed(2)} - <${binMax.toFixed(2)}`;
    return { min: binMin, max: binMax, label };
  });

  return data.map((row) => {
    const newRow = { ...row };
    const value = row[column];
    if (typeof value === 'number' && !isNaN(value)) {
      let foundBin = false;
      for (let i = 0; i < bins.length; i++) {
        // Ensure the last bin includes the max value
        if (i === bins.length - 1) {
          if (value >= bins[i].min && value <= bins[i].max) {
            newRow[column] = bins[i].label;
            foundBin = true;
            break;
          }
        } else {
          if (value >= bins[i].min && value < bins[i].max) {
            newRow[column] = bins[i].label;
            foundBin = true;
            break;
          }
        }
      }
      if(!foundBin && value === max) { // Edge case for max value if not caught by loop logic
        newRow[column] = bins[bins.length -1].label;
      }
    }
    return newRow;
  });
};

// Create dummy variables from categorical data
export const createDummyVariables = (
  data: DataRow[],
  column: string
): {
  newData: DataRow[];
  newColumns: Column[]; // Return Column definitions for new dummy variables
} => {
  const uniqueValues = [...new Set(data.map((row) => String(row[column])))].sort();

  const newDummyColumns: Column[] = uniqueValues.map((value) => ({
    id: uuidv4(),
    name: `${column}_${value.replace(/\s+/g, '_')}`, // Sanitize name
    type: DataType.BOOLEAN, // Dummy variables are typically boolean (0 or 1)
    role: VariableRole.NONE,
  }));

  const newData = data.map((row) => {
    const newRow = { ...row };
    newDummyColumns.forEach((dummyCol, index) => {
      newRow[dummyCol.name] = String(row[column]) === uniqueValues[index] ? 1 : 0;
    });
    return newRow;
  });

  return { newData, newColumns: newDummyColumns };
};

// Compute variable from multiple columns
export const computeVariable = (
  data: DataRow[],
  columns: string[],
  method: 'sum' | 'mean' | 'count' | 'std' | 'min' | 'max',
  newVariableName: string
): DataRow[] => {
  return data.map(row => {
    const values = columns
      .map(col => row[col])
      .filter((val): val is number => typeof val === 'number' && !isNaN(val));

    let computedValue: number;

    switch (method) {
      case 'sum':
        computedValue = values.reduce((sum, val) => sum + val, 0);
        break;
      case 'mean':
        computedValue = values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
        break;
      case 'count':
        computedValue = values.length;
        break;
      case 'std':
        if (values.length <= 1) {
          computedValue = 0;
        } else {
          const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
          const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
          computedValue = Math.sqrt(variance);
        }
        break;
      case 'min':
        computedValue = values.length > 0 ? Math.min(...values) : 0;
        break;
      case 'max':
        computedValue = values.length > 0 ? Math.max(...values) : 0;
        break;
      default:
        computedValue = 0;
    }

    return {
      ...row,
      [newVariableName]: computedValue
    };
  });
};

/**
 * Data Filtering and Sorting
 */

// Filter data based on conditions with support for AND/OR logic
export const filterData = (
  data: DataRow[],
  conditions: FilterCondition[],
  logic: 'AND' | 'OR' = 'AND'
): DataRow[] => {
  if (conditions.length === 0) return data;

  return data.filter((row) => {
    const results = conditions.map((condition) => {
      const cellValue = row[condition.column];

      switch (condition.operator) {
        case 'eq':
          return String(cellValue) === String(condition.value);
        case 'neq':
          return String(cellValue) !== String(condition.value);
        case 'gt':
          return Number(cellValue) > Number(condition.value);
        case 'gte':
          return Number(cellValue) >= Number(condition.value);
        case 'lt':
          return Number(cellValue) < Number(condition.value);
        case 'lte':
          return Number(cellValue) <= Number(condition.value);
        case 'between':
          const numValue = Number(cellValue);
          const min = Number(condition.value);
          const max = Number(condition.value2);
          return numValue >= min && numValue <= max;
        case 'notBetween':
          const numVal = Number(cellValue);
          const minVal = Number(condition.value);
          const maxVal = Number(condition.value2);
          return !(numVal >= minVal && numVal <= maxVal);
        case 'contains':
          return String(cellValue).toLowerCase().includes(String(condition.value).toLowerCase());
        case 'startsWith':
          return String(cellValue).toLowerCase().startsWith(String(condition.value).toLowerCase());
        case 'endsWith':
          return String(cellValue).toLowerCase().endsWith(String(condition.value).toLowerCase());
        case 'in':
          return condition.values?.some(val => String(cellValue) === String(val)) || false;
        case 'notIn':
          return !condition.values?.some(val => String(cellValue) === String(val)) || true;
        case 'isEmpty':
          return cellValue === null || cellValue === undefined || String(cellValue).trim() === '';
        case 'isNotEmpty':
          return !(cellValue === null || cellValue === undefined || String(cellValue).trim() === '');
        default:
          return true;
      }
    });

    return logic === 'AND' ? results.every(Boolean) : results.some(Boolean);
  });
};

// Sort data
export const sortData = (
  data: DataRow[],
  sortColumns: SortCondition[]
): DataRow[] => {
  return [...data].sort((a, b) => {
    for (const sort of sortColumns) {
      const valueA = a[sort.column];
      const valueB = b[sort.column];

      if (valueA === null || valueA === undefined) return sort.direction === 'asc' ? -1 : 1;
      if (valueB === null || valueB === undefined) return sort.direction === 'asc' ? 1 : -1;

      // Attempt numeric comparison first
      const numA = Number(valueA);
      const numB = Number(valueB);

      if (!isNaN(numA) && !isNaN(numB)) {
        if (numA < numB) return sort.direction === 'asc' ? -1 : 1;
        if (numA > numB) return sort.direction === 'asc' ? 1 : -1;
      } else { // Fallback to string comparison
        const strA = String(valueA).toLowerCase();
        const strB = String(valueB).toLowerCase();
        if (strA < strB) return sort.direction === 'asc' ? -1 : 1;
        if (strA > strB) return sort.direction === 'asc' ? 1 : -1;
      }
    }
    return 0;
  });
};

/**
 * Data Validation
 */

// Check for missing values
export const checkMissingValues = (
  data: DataRow[],
  columnsToCheck?: string[]
): MissingValueReport => {
  const cols = columnsToCheck && columnsToCheck.length > 0
    ? columnsToCheck
    : (data.length > 0 ? Object.keys(data[0]) : []);

  const byColumn: Record<string, number> = {};
  cols.forEach(col => byColumn[col] = 0);

  const byRow: number[] = new Array(data.length).fill(0);

  data.forEach((row, rowIndex) => {
    cols.forEach((column) => {
      if (row[column] === null || row[column] === undefined || String(row[column]).trim() === '') {
        byColumn[column]++;
        byRow[rowIndex]++;
      }
    });
  });

  const total = Object.values(byColumn).reduce((acc, val) => acc + val, 0);

  return { total, byColumn, byRow };
};

// Check for outliers using IQR method
export const checkOutliers = (
  data: DataRow[],
  column: string,
  multiplier: number = 1.5
): OutlierReport => {
  const values = data.map((row) => row[column])
    .filter((val): val is number => typeof val === 'number' && !isNaN(val))
    .sort((a, b) => a - b);

  if (values.length < 4) { // Not enough data for meaningful IQR
    return { outliers: [], lowerBound: NaN, upperBound: NaN, q1: NaN, q3: NaN, iqr: NaN };
  }

  const q1Index = Math.floor((values.length -1) * 0.25); // (n-1)*p for percentile index
  const q3Index = Math.floor((values.length-1) * 0.75);

  const q1 = values[q1Index];
  const q3 = values[q3Index];

  const iqr = q3 - q1;
  const lowerBound = q1 - multiplier * iqr;
  const upperBound = q3 + multiplier * iqr;

  const outliers: Array<{ value: DataValue; rowIndex: number }> = [];

  data.forEach((row, index) => {
    const value = row[column];
    if (typeof value === 'number' && !isNaN(value) && (value < lowerBound || value > upperBound)) {
      outliers.push({ value, rowIndex: index });
    }
  });

  return { outliers, lowerBound, upperBound, q1, q3, iqr };
};

// Random data generation options
interface RandomDataOptions {
  min?: number;
  max?: number;
  decimals?: number;
  categories?: string[];
  startDate?: string;
  endDate?: string;
}

// Generate random dataset for testing
export const generateRandomDataset = (
  numRows: number,
  columnsConfig: Array<{ // Renamed for clarity
    name: string;
    type: DataType;
    role?: VariableRole; // Optional role
    options?: RandomDataOptions;
  }>
): Dataset => {
  const data: DataRow[] = [];

  for (let i = 0; i < numRows; i++) {
    const row: DataRow = {};
    columnsConfig.forEach((column) => {
      switch (column.type) {
        case DataType.NUMERIC:
          const min = column.options?.min ?? 0;
          const max = column.options?.max ?? 100;
          const decimals = column.options?.decimals ?? 2;
          row[column.name] = parseFloat((min + Math.random() * (max - min)).toFixed(decimals));
          break;
        case DataType.CATEGORICAL:
          const categories = column.options?.categories ?? ['A', 'B', 'C'];
          row[column.name] = categories[Math.floor(Math.random() * categories.length)];
          break;
        case DataType.BOOLEAN:
          row[column.name] = Math.random() > 0.5;
          break;
        case DataType.DATE:
          const start = column.options?.startDate ? new Date(column.options.startDate) : new Date(2000, 0, 1);
          const end = column.options?.endDate ? new Date(column.options.endDate) : new Date();
          row[column.name] = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString().split('T')[0]; // YYYY-MM-DD
          break;
        default: // DataType.TEXT
          row[column.name] = `Sample Text ${i + 1}-${column.name}`;
          break;
      }
    });
    data.push(row);
  }

  const datasetColumns: Column[] = columnsConfig.map(cc => ({
    id: uuidv4(),
    name: cc.name,
    type: cc.type,
    role: cc.role || VariableRole.NONE,
  }));

  return {
    id: uuidv4(),
    name: 'Random Dataset',
    dateCreated: new Date(),
    dateModified: new Date(),
    columns: datasetColumns,
    data,
  };
};

export const createCrossTabulation = (rowValues: DataValue[], colValues: DataValue[]) => {
  if (rowValues.length !== colValues.length) {
    throw new Error('Variables must have the same length for cross-tabulation.');
  }

  const uniqueRowValues = [...new Set(rowValues.map(String))].sort();
  const uniqueColValues = [...new Set(colValues.map(String))].sort();

  const crossTab: Record<string, Record<string, number>> = {};
  uniqueRowValues.forEach(rowVal => {
    crossTab[rowVal] = {};
    uniqueColValues.forEach(colVal => {
      crossTab[rowVal][colVal] = 0;
    });
  });

  for (let i = 0; i < rowValues.length; i++) {
    const rowVal = String(rowValues[i]);
    const colVal = String(colValues[i]);
    if (crossTab[rowVal] && crossTab[rowVal][colVal] !== undefined) {
         crossTab[rowVal][colVal]++;
    }
  }
  return crossTab;
};

export const calculateFrequencies = (values: DataValue[]): Record<string, number> => {
  const counts: Record<string, number> = {};
  values.forEach(value => {
    const strValue = String(value);
    counts[strValue] = (counts[strValue] || 0) + 1;
  });
  return counts;
};

export const calculateProportions = (values: DataValue[]): Record<string, number> => {
  const counts = calculateFrequencies(values);
  const total = values.length;
  if (total === 0) return {};
  const proportions: Record<string, number> = {};
  for (const key in counts) {
    proportions[key] = counts[key] / total;
  }
  return proportions;
};

// Legacy support for old function signature
export const calculateFrequenciesFromData = (data: DataRow[], column: string) => {
  const values = data.map(row => row[column]);
  return calculateFrequencies(values);
};

export const calculateProportionsFromData = (data: DataRow[], column: string) => {
  const values = data.map(row => row[column]);
  return calculateProportions(values);
};


/**
 * Category Ordering Utilities
 */

// Get categories in the correct order for a column
export const getOrderedCategories = (column: Column, data: DataRow[]): string[] => {
  // If custom category order is defined, use it
  if (column.categoryOrder && column.categoryOrder.length > 0) {
    // Get unique values from data using enhanced missing data handling
    const dataValues = [...new Set(extractCategoricalValuesWithMissingCodes(data, column))];

    // Start with the defined order
    const orderedCategories = [...column.categoryOrder];

    // Add any categories from data that aren't in the defined order (at the end)
    dataValues.forEach(value => {
      if (!orderedCategories.includes(value)) {
        orderedCategories.push(value);
      }
    });

    // Filter to only include categories that actually exist in the data
    return orderedCategories.filter(category => dataValues.includes(category));
  }

  // Fallback to alphabetical order if no custom order is defined
  const values = extractCategoricalValuesWithMissingCodes(data, column);

  return [...new Set(values)].sort();
};

// Get ordered frequencies for a categorical column
export const getOrderedFrequencies = (column: Column, data: DataRow[]): Record<string, number> => {
  const frequencies = calculateFrequenciesFromData(data, column.name);
  const orderedCategories = getOrderedCategories(column, data);

  const orderedFrequencies: Record<string, number> = {};
  orderedCategories.forEach(category => {
    orderedFrequencies[category] = frequencies[category] || 0;
  });

  return orderedFrequencies;
};

// Get ordered proportions for a categorical column
export const getOrderedProportions = (column: Column, data: DataRow[]): Record<string, number> => {
  const proportions = calculateProportionsFromData(data, column.name);
  const orderedCategories = getOrderedCategories(column, data);

  const orderedProportions: Record<string, number> = {};
  orderedCategories.forEach(category => {
    orderedProportions[category] = proportions[category] || 0;
  });

  return orderedProportions;
};

/**
 * Centralized Categorical Ordering System
 *
 * This system provides a unified approach for handling categorical variable ordering
 * across all analysis components in the application.
 */

// Get ordered unique categories for any categorical column by column ID
export const getOrderedCategoriesByColumnId = (
  columnId: string,
  dataset: Dataset
): string[] => {
  const column = dataset.columns.find(col => col.id === columnId);
  if (!column) return [];

  return getOrderedCategories(column, dataset.data);
};

// Get ordered unique categories for any categorical column by column name
export const getOrderedCategoriesByColumnName = (
  columnName: string,
  dataset: Dataset
): string[] => {
  const column = dataset.columns.find(col => col.name === columnName);
  if (!column) return [];

  return getOrderedCategories(column, dataset.data);
};

// Sort any array of categorical values according to the defined order
export const sortCategoricalValues = (
  values: string[],
  column: Column,
  dataset: Dataset
): string[] => {
  const orderedCategories = getOrderedCategories(column, dataset.data);

  // Create a map for efficient lookup of category positions
  const orderMap = new Map<string, number>();
  orderedCategories.forEach((category, index) => {
    orderMap.set(category, index);
  });

  return values.sort((a, b) => {
    const posA = orderMap.get(a) ?? Number.MAX_SAFE_INTEGER;
    const posB = orderMap.get(b) ?? Number.MAX_SAFE_INTEGER;
    return posA - posB;
  });
};

// Sort any array of objects by a categorical property according to the defined order
export const sortObjectsByCategoricalProperty = <T>(
  objects: T[],
  propertyAccessor: (obj: T) => string,
  column: Column,
  dataset: Dataset
): T[] => {
  const orderedCategories = getOrderedCategories(column, dataset.data);

  // Create a map for efficient lookup of category positions
  const orderMap = new Map<string, number>();
  orderedCategories.forEach((category, index) => {
    orderMap.set(category, index);
  });

  return objects.sort((a, b) => {
    const valueA = propertyAccessor(a);
    const valueB = propertyAccessor(b);
    const posA = orderMap.get(valueA) ?? Number.MAX_SAFE_INTEGER;
    const posB = orderMap.get(valueB) ?? Number.MAX_SAFE_INTEGER;
    return posA - posB;
  });
};

// Enhanced frequency calculation that respects category ordering
export const calculateOrderedFrequencies = (
  columnId: string,
  dataset: Dataset
): { categories: string[]; frequencies: Record<string, number> } => {
  const column = dataset.columns.find(col => col.id === columnId);
  if (!column) return { categories: [], frequencies: {} };

  const frequencies = calculateFrequenciesFromData(dataset.data, column.name);
  const orderedCategories = getOrderedCategories(column, dataset.data);

  return {
    categories: orderedCategories,
    frequencies
  };
};

// Enhanced cross-tabulation that respects category ordering for both dimensions
export const createOrderedCrossTabulation = (
  rowColumnId: string,
  colColumnId: string,
  dataset: Dataset
): {
  rowCategories: string[];
  columnCategories: string[];
  frequencies: Record<string, Record<string, number>>;
} => {
  const rowColumn = dataset.columns.find(col => col.id === rowColumnId);
  const colColumn = dataset.columns.find(col => col.id === colColumnId);

  if (!rowColumn || !colColumn) {
    return { rowCategories: [], columnCategories: [], frequencies: {} };
  }

  // Use enhanced categorical extraction to respect missing value codes
  const rowValues = extractCategoricalValuesWithMissingCodes(dataset.data, rowColumn);
  const colValues = extractCategoricalValuesWithMissingCodes(dataset.data, colColumn);

  const frequencies = createCrossTabulation(rowValues, colValues);
  const rowCategories = getOrderedCategories(rowColumn, dataset.data);
  const columnCategories = getOrderedCategories(colColumn, dataset.data);

  return {
    rowCategories,
    columnCategories,
    frequencies
  };
};

// Utility to check if a column should use ordered categories
export const shouldUseOrderedCategories = (column: Column): boolean => {
  return (column.type === DataType.CATEGORICAL || column.type === DataType.ORDINAL) &&
         !!column.categoryOrder &&
         column.categoryOrder.length > 0;
};

// Get chart data with proper category ordering for visualizations
export const getOrderedChartData = (
  categoryColumnId: string,
  valueColumnId: string | null,
  dataset: Dataset,
  aggregationMethod: 'sum' | 'average' | 'count' | 'max' | 'min' = 'count'
): Array<{ category: string; value: number; [key: string]: any }> => {
  const categoryColumn = dataset.columns.find(col => col.id === categoryColumnId);
  if (!categoryColumn) return [];

  const orderedCategories = getOrderedCategories(categoryColumn, dataset.data);

  if (!valueColumnId) {
    // Frequency/count mode
    const frequencies = calculateFrequenciesFromData(dataset.data, categoryColumn.name);
    return orderedCategories.map(category => ({
      category,
      value: frequencies[category] || 0
    }));
  }

  // Aggregation mode
  const valueColumn = dataset.columns.find(col => col.id === valueColumnId);
  if (!valueColumn) return [];

  const groupedData: Record<string, number[]> = {};
  orderedCategories.forEach(category => {
    groupedData[category] = [];
  });

  dataset.data.forEach(row => {
    const categoryValue = String(row[categoryColumn.name]);
    const numericValue = row[valueColumn.name];

    if (groupedData[categoryValue] && typeof numericValue === 'number' && !isNaN(numericValue)) {
      groupedData[categoryValue].push(numericValue);
    }
  });

  return orderedCategories.map(category => {
    const values = groupedData[category];
    let aggregatedValue = 0;

    if (values.length > 0) {
      switch (aggregationMethod) {
        case 'sum':
          aggregatedValue = values.reduce((sum, val) => sum + val, 0);
          break;
        case 'average':
          aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
          break;
        case 'count':
          aggregatedValue = values.length;
          break;
        case 'max':
          aggregatedValue = Math.max(...values);
          break;
        case 'min':
          aggregatedValue = Math.min(...values);
          break;
      }
    }

    return {
      category,
      value: aggregatedValue
    };
  });
};

export default {
  importCSV,
  importExcel,
  importJSON,
  exportCSV,
  exportExcel,
  detectDataType,
  recodeValues,
  standardizeValues,
  logTransform,
  binValues,
  createDummyVariables,
  filterData,
  sortData,
  checkMissingValues,
  checkOutliers,
  generateRandomDataset,
  createCrossTabulation,
  calculateFrequencies,
  calculateProportions,
  getOrderedCategories,
  getOrderedFrequencies,
  getOrderedProportions,
  getOrderedCategoriesByColumnId,
  getOrderedCategoriesByColumnName,
  sortCategoricalValues,
  sortObjectsByCategoricalProperty,
  calculateOrderedFrequencies,
  createOrderedCrossTabulation,
  shouldUseOrderedCategories,
  getOrderedChartData,
};
