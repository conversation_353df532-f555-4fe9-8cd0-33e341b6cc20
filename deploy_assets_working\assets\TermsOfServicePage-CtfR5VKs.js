import{ck as l,j as e,u as d,C as m,B as r,e as t,L as h,m as s,n as a,r as o}from"./mui-libs-CfwFIaTD.js";import{l as u,H as x}from"./index-Bpan7Tbe.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";var n={},p=l;Object.defineProperty(n,"__esModule",{value:!0});var i=n.default=void 0,j=p(u()),y=e;i=n.default=(0,j.default)((0,y.jsx)("path",{d:"M16.59 7.58 10 14.17l-3.59-3.58L5 12l5 5 8-8zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"}),"CheckCircleOutline");const C=()=>{const c=d();return e.jsxs(m,{maxWidth:"md",sx:{py:4},children:[e.jsxs(x,{children:[e.jsx("title",{children:"Terms of Service - DataStatPro"}),e.jsx("meta",{name:"description",content:"DataStatPro Terms of Service"})]}),e.jsxs(r,{sx:{mb:4},children:[e.jsx(t,{variant:"h3",component:"h1",gutterBottom:!0,sx:{fontWeight:"bold",color:c.palette.primary.main},children:"DataStatPro Terms of Service"}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Effective: June 20, 2025"})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"1. Acceptance"}),e.jsx(t,{variant:"body1",children:"By accessing DataStatPro (www.datastatpro.com) or its subdomains, you agree to these terms. If you disagree, discontinue use immediately. Content is protected by copyright/trademark laws."})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"2. Permitted Use"}),e.jsx(t,{variant:"body1",children:"You may download materials for personal/non-commercial use, including installing the PWA on personal devices. Automatic updates may modify/remove features. License terminates if terms are violated; destroy downloaded materials except legally installed PWA copies."})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"3. Prohibited Activities"}),e.jsx(t,{variant:"body1",sx:{mb:1},children:"You agree not to:"}),e.jsxs(h,{dense:!0,children:[e.jsxs(s,{children:[e.jsx(a,{children:e.jsx(i,{fontSize:"small",color:"primary"})}),e.jsx(o,{primary:"Use for illegal/fraudulent purposes;"})]}),e.jsxs(s,{children:[e.jsx(a,{children:e.jsx(i,{fontSize:"small",color:"primary"})}),e.jsx(o,{primary:"Reverse engineer, scrape, or disrupt services;"})]}),e.jsxs(s,{children:[e.jsx(a,{children:e.jsx(i,{fontSize:"small",color:"primary"})}),e.jsx(o,{primary:"Use commercially, modify content, or redistribute materials."})]})]})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"4. Intellectual Property"}),e.jsx(t,{variant:"body1",children:"All content, trademarks, and software remain DataStatPro’s property. Limited access granted; no derivatives allowed without permission."})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"5. Privacy"}),e.jsx(t,{variant:"body1",children:"Data use governed by our Privacy Policy."})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"6. Disclaimer & Liability"}),e.jsx(t,{variant:"body1",children:"Content provided “as is.” No warranties for accuracy, fitness, or non-infringement. We’re not liable for damages from website use."})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"7. Third-Party Content"}),e.jsx(t,{variant:"body1",children:"Links or integrations to third-party services are subject to their terms. We disclaim responsibility for their content/functionality."})]}),e.jsxs(r,{sx:{mb:3},children:[e.jsx(t,{variant:"h5",component:"h2",gutterBottom:!0,children:"8. Modifications"}),e.jsx(t,{variant:"body1",children:"Terms may be updated anytime. Service access may be modified/terminated at our discretion."})]})]})};export{C as default};
