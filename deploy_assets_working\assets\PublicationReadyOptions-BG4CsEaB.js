import{j as e,u as C,C as D,R as l,i as o,e as t,B as i,h as c,G as d,k as A,bz as T,aE as w,I,aF as p,d as z,l as S,f as F,bA as B,N as n,a0 as h,Z as R,aX as g,aN as u,ac as b,X as P,bB as M}from"./mui-libs-CfwFIaTD.js";import{r as O}from"./react-libs-Cr2nE3UY.js";const m=[{name:"Table 1",shortDescription:"Descriptive statistics table for baseline characteristics",detailedDescription:"Generate a comprehensive Table 1 showing descriptive statistics for all baseline characteristics of your study participants. This is the standard first table in most research papers that presents demographic and clinical characteristics.",path:"publication-ready/table1",icon:e.jsx(n,{}),category:"Tables",color:"#2196F3"},{name:"Table 1a",shortDescription:"Alternative descriptive statistics with advanced formatting",detailedDescription:"Create an enhanced version of Table 1 with additional statistical measures, custom formatting options, and advanced grouping capabilities. Ideal for complex studies requiring detailed baseline comparisons.",path:"publication-ready/table1a",icon:e.jsx(n,{}),category:"Tables",color:"#2196F3"},{name:"Table 1b",shortDescription:"Comprehensive descriptive statistics for numerical variables",detailedDescription:"Generate a comprehensive descriptive statistics table specifically designed for multiple numerical variables. Includes mean, standard deviation, median, quartiles, range, and normality tests with intelligent interpretation of distribution characteristics and variability patterns.",path:"publication-ready/table1b",icon:e.jsx(n,{}),category:"Tables",color:"#1976D2"},{name:"Table 2",shortDescription:"Comparative statistics and outcome analysis table",detailedDescription:"Generate Table 2 for presenting primary and secondary outcomes with between-group comparisons. Includes statistical tests, p-values, confidence intervals, and effect sizes appropriate for your study design.",path:"publication-ready/table2",icon:e.jsx(h,{}),category:"Tables",color:"#4CAF50"},{name:"SMD Table",shortDescription:"Standardized Mean Differences for effect size reporting",detailedDescription:"Calculate and present Standardized Mean Differences (Cohen's d, Hedge's g) with confidence intervals. Essential for meta-analyses and systematic reviews, or when reporting effect sizes for continuous outcomes.",path:"publication-ready/smd-table",icon:e.jsx(R,{}),category:"Analysis",color:"#FF9800"},{name:"Regression Table",shortDescription:"Formatted tables for regression analysis results",detailedDescription:"Create publication-ready tables for linear, logistic, or Cox regression models. Automatically formats coefficients, odds ratios, hazard ratios, confidence intervals, and p-values according to journal standards.",path:"publication-ready/regression-table",icon:e.jsx(g,{}),category:"Analysis",color:"#9C27B0"},{name:"Regression Interpretation",shortDescription:"AI-assisted interpretation of regression results",detailedDescription:"Get intelligent assistance in interpreting your regression analysis results. Provides plain-language explanations of coefficients, statistical significance, clinical significance, and suggests appropriate conclusions.",path:"publication-ready/regression-interpretation",icon:e.jsx(u,{}),category:"Analysis",color:"#607D8B"},{name:"Flow Diagram",shortDescription:"CONSORT-style participant flow diagrams",detailedDescription:"Create professional participant flow diagrams following CONSORT, STROBE, or PRISMA guidelines. Visualize enrollment, randomization, follow-up, and analysis phases with customizable design options.",path:"publication-ready/flow-diagram",icon:e.jsx(b,{}),category:"Visualization",color:"#00BCD4"},{name:"Convert to APA",shortDescription:"Convert raw data tables to APA-style format",detailedDescription:"Transform your raw data tables into properly formatted APA-style tables for academic publications. This tool helps ensure your tables meet the strict guidelines of the APA 7th edition.",path:"publication-ready/convert-to-apa",icon:e.jsx(u,{}),category:"Tables",color:"#8BC34A"},{name:"PostHoc Tests",shortDescription:"Perform multiple comparisons after ANOVA",detailedDescription:"Conduct various post-hoc tests (e.g., Tukey's HSD, Bonferroni, Holm) to identify specific group differences after a significant ANOVA result. Includes detailed tables and visualizations.",path:"publication-ready/posthoc-tests",icon:e.jsx(h,{}),category:"Analysis",color:"#FF5722"},{name:"Statistical Methods Generator",shortDescription:"Generate publication-ready methods sections",detailedDescription:"Automatically create comprehensive Statistical Methods sections based on your completed analyses. Select multiple analyses, customize the generated text, and export in various formats for publication.",path:"publication-ready/statistical-methods",icon:e.jsx(P,{}),category:"Analysis",color:"#4CAF50"},{name:"Results Manager",shortDescription:"Manage and export analysis results",detailedDescription:"Organize, filter, and export your analysis results in various formats. Collect results from multiple analyses and create comprehensive reports for publication or presentation.",path:"publication-ready/results-manager",icon:e.jsx(M,{}),category:"Analysis",color:"#9C27B0"}],W=({onNavigate:y})=>{const s=C(),[r,f]=O.useState("All"),x=["All","Tables","Analysis","Visualization","Learning"],v=r==="All"?m:m.filter(a=>a.category===r),j=a=>{switch(a){case"Tables":return e.jsx(n,{});case"Analysis":return e.jsx(g,{});case"Visualization":return e.jsx(b,{});default:return e.jsx(n,{})}};return e.jsxs(D,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(l,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${o(s.palette.primary.main,.1)} 0%, ${o(s.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(t,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Publication-Ready Tools"}),e.jsx(t,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Generate publication-quality tables, analyses, and visualizations"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Choose from our collection of tools designed to help you create professional, journal-ready outputs for your research. Each tool follows established reporting guidelines and best practices."})]}),e.jsxs(i,{sx:{mb:4},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),e.jsx(i,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:x.map(a=>e.jsx(c,{label:a,onClick:()=>f(a),variant:r===a?"filled":"outlined",color:r===a?"primary":"default",icon:a!=="All"?j(a):void 0,sx:{"&:hover":{backgroundColor:r===a?s.palette.primary.dark:o(s.palette.primary.main,.1)}}},a))})]}),e.jsx(d,{container:!0,spacing:3,children:v.map((a,G)=>e.jsx(d,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(A,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:s.shadows[8],"& .launch-button":{backgroundColor:a.color,color:"white"}}},children:[e.jsx(T,{avatar:e.jsx(z,{sx:{bgcolor:a.color,width:48,height:48},children:a.icon}),title:e.jsx(t,{variant:"h6",fontWeight:"bold",children:a.name}),subheader:e.jsx(i,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:e.jsx(c,{label:a.category,size:"small",variant:"outlined",color:"primary"})}),action:e.jsx(w,{title:"More information",children:e.jsx(I,{size:"small",children:e.jsx(p,{})})})}),e.jsxs(S,{sx:{flexGrow:1,pt:0},children:[e.jsx(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:a.shortDescription}),e.jsx(t,{variant:"body2",paragraph:!0,children:a.detailedDescription})]}),e.jsx(i,{sx:{p:2,pt:0},children:e.jsxs(F,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>y(a.path),endIcon:e.jsx(B,{}),sx:{borderColor:a.color,color:a.color,fontWeight:"bold","&:hover":{borderColor:a.color}},children:["Launch ",a.name]})})]})},a.name))}),e.jsx(l,{elevation:1,sx:{p:3,mt:4,backgroundColor:o(s.palette.info.main,.05),border:`1px solid ${o(s.palette.info.main,.2)}`},children:e.jsxs(i,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[e.jsx(p,{color:"info"}),e.jsxs(i,{children:[e.jsx(t,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),e.jsxs(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"New to research publishing?"})," Start with Table 1 or Flow Diagram"]}),e.jsxs(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Analyzing interventions?"})," Use Table 2 for outcomes and SMD Table for effect sizes"]}),e.jsxs(t,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",e.jsx("strong",{children:"Complex modeling?"})," Try Regression Table and Regression Interpretation"]}),e.jsxs(t,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"Systematic reviews?"})," SMD Table and Flow Diagram are essential tools"]})]})]})})]})};export{W as P,m as p};
