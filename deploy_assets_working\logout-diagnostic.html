<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logout Diagnostic Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-good { color: #4CAF50; font-weight: bold; }
        .status-warning { color: #FF9800; font-weight: bold; }
        .status-error { color: #F44336; font-weight: bold; }
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover { background: #1565c0; }
        .test-button:disabled { background: #ccc; cursor: not-allowed; }
        .result-box {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        h1 { color: #1976d2; }
        h2 { color: #333; border-bottom: 2px solid #1976d2; padding-bottom: 5px; }
    </style>
</head>
<body>
    <h1>🔐 Logout Diagnostic Tool</h1>
    <p>This tool helps diagnose logout functionality issues in DataStatPro.</p>

    <div class="test-section">
        <h2>🔍 Current Authentication State</h2>
        <button class="test-button" onclick="checkAuthState()">Check Current State</button>
        <div id="auth-state-results"></div>
    </div>

    <div class="test-section">
        <h2>🔐 Test Login & Logout Flow</h2>
        <div class="input-group">
            <label for="test-email">Test Email:</label>
            <input type="email" id="test-email" placeholder="<EMAIL>">
        </div>
        <div class="input-group">
            <label for="test-password">Test Password:</label>
            <input type="password" id="test-password" placeholder="your-test-password">
        </div>
        <button class="test-button" onclick="testLoginLogoutFlow()">Test Complete Flow</button>
        <button class="test-button" onclick="testLogoutOnly()">Test Logout Only</button>
        <div id="flow-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🌐 Network & Connectivity Tests</h2>
        <button class="test-button" onclick="testSupabaseConnectivity()">Test Supabase Connection</button>
        <button class="test-button" onclick="testLogoutEndpoint()">Test Logout Endpoint</button>
        <button class="test-button" onclick="testNetworkTimeout()">Test Network Timeout</button>
        <div id="network-test-results"></div>
    </div>

    <div class="test-section">
        <h2>💾 Storage & State Tests</h2>
        <button class="test-button" onclick="testStorageClearing()">Test Storage Clearing</button>
        <button class="test-button" onclick="testCrossTabSync()">Test Cross-Tab Sync</button>
        <button class="test-button" onclick="clearAllAuthData()">Clear All Auth Data</button>
        <div id="storage-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🦊 Firefox-Specific Logout Tests</h2>
        <button class="test-button" onclick="testFirefoxLogout()">Test Firefox Logout</button>
        <button class="test-button" onclick="testContentBlockingLogout()">Test with Content Blocking</button>
        <div id="firefox-test-results"></div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.OCeU38SWqhgeprIekN8qoaPdCzu04RaU-ktdNOci4rs';

        let currentSession = null;
        let currentUser = null;

        function checkAuthState() {
            const resultsEl = document.getElementById('auth-state-results');
            
            const authState = {
                localStorage: {
                    'datastatpro-auth-flow-id': localStorage.getItem('datastatpro-auth-flow-id'),
                    'datastatpro-logout-signal': localStorage.getItem('datastatpro-logout-signal'),
                    'datastatpro-login-signal': localStorage.getItem('datastatpro-login-signal'),
                    'firefox-use-fallback-auth': localStorage.getItem('firefox-use-fallback-auth'),
                    'firefox-content-blocked': localStorage.getItem('firefox-content-blocked')
                },
                sessionStorage: {
                    'isGuest': sessionStorage.getItem('isGuest'),
                    'showSignupSuccess': sessionStorage.getItem('showSignupSuccess')
                },
                currentSession: currentSession,
                currentUser: currentUser,
                browser: navigator.userAgent,
                isFirefox: navigator.userAgent.includes('Firefox'),
                timestamp: new Date().toISOString()
            };

            resultsEl.innerHTML = `
                <div class="result-box">
                    <strong>Current Authentication State:</strong>
                    ${JSON.stringify(authState, null, 2)}
                </div>
            `;
        }

        async function testSupabaseConnectivity() {
            const resultsEl = document.getElementById('network-test-results');
            resultsEl.innerHTML = '<div class="result-box">Testing Supabase connectivity...</div>';

            try {
                // Test basic connectivity
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    method: 'HEAD',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    ok: response.ok
                };

                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="${response.ok ? 'status-good' : 'status-error'}">
                            ${response.ok ? '✅' : '❌'} Supabase Connectivity
                        </span>
                        ${JSON.stringify(result, null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Supabase Connectivity Failed</span>
                        Error: ${error.message}
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }

        async function testLogoutEndpoint() {
            const resultsEl = document.getElementById('network-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                const response = await fetch(`${SUPABASE_URL}/auth/v1/logout`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok
                };

                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="${response.ok ? 'status-good' : 'status-warning'}">
                            ${response.ok ? '✅' : '⚠️'} Logout Endpoint Test
                        </span>
                        ${JSON.stringify(result, null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Logout Endpoint Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        async function testNetworkTimeout() {
            const resultsEl = document.getElementById('network-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);

                const response = await fetch(`${SUPABASE_URL}/auth/v1/logout`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json'
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-good">✅ Network Timeout Test</span>
                        No timeout occurred, response received: ${response.status}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="${error.name === 'AbortError' ? 'status-warning' : 'status-error'}">
                            ${error.name === 'AbortError' ? '⚠️' : '❌'} Network Timeout Test
                        </span>
                        Error: ${error.message}
                        Type: ${error.name}
                    </div>
                `;
            }
        }

        async function testLoginLogoutFlow() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const resultsEl = document.getElementById('flow-test-results');

            if (!email || !password) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-warning">⚠️ Please enter test email and password</span>
                    </div>
                `;
                return;
            }

            resultsEl.innerHTML = '<div class="result-box">Testing complete login/logout flow...</div>';

            try {
                // Step 1: Login
                const loginResponse = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });

                const loginData = await loginResponse.json();

                if (!loginResponse.ok) {
                    throw new Error(`Login failed: ${loginData.error_description || loginData.message}`);
                }

                currentSession = loginData;
                currentUser = loginData.user;

                // Step 2: Wait a moment
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Step 3: Logout
                const logoutResponse = await fetch(`${SUPABASE_URL}/auth/v1/logout`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${loginData.access_token}`
                    }
                });

                const flowResult = {
                    loginStatus: loginResponse.status,
                    loginSuccess: loginResponse.ok,
                    logoutStatus: logoutResponse.status,
                    logoutSuccess: logoutResponse.ok,
                    userEmail: loginData.user?.email,
                    accessToken: loginData.access_token ? 'Present' : 'Missing'
                };

                currentSession = null;
                currentUser = null;

                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="${loginResponse.ok && logoutResponse.ok ? 'status-good' : 'status-error'}">
                            ${loginResponse.ok && logoutResponse.ok ? '✅' : '❌'} Complete Login/Logout Flow
                        </span>
                        ${JSON.stringify(flowResult, null, 2)}
                    </div>
                `;

            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Login/Logout Flow Failed</span>
                        Error: ${error.message}
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }

        async function testLogoutOnly() {
            const resultsEl = document.getElementById('flow-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                const response = await fetch(`${SUPABASE_URL}/auth/v1/logout`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json'
                    }
                });

                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="${response.ok ? 'status-good' : 'status-warning'}">
                            ${response.ok ? '✅' : '⚠️'} Logout Only Test
                        </span>
                        Status: ${response.status}
                        Note: ${response.ok ? 'Logout succeeded' : 'Expected if not logged in'}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Logout Only Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function testStorageClearing() {
            const resultsEl = document.getElementById('storage-test-results');

            try {
                // Test setting and clearing auth-related storage
                const testData = {
                    'datastatpro-test': 'test-value',
                    'datastatpro-auth-flow-id': 'test-flow-123',
                    'datastatpro-logout-signal': JSON.stringify({timestamp: Date.now()})
                };

                // Set test data
                Object.entries(testData).forEach(([key, value]) => {
                    localStorage.setItem(key, value);
                });

                sessionStorage.setItem('isGuest', 'false');
                sessionStorage.setItem('showSignupSuccess', 'true');

                // Verify data was set
                const dataSet = Object.keys(testData).every(key => 
                    localStorage.getItem(key) === testData[key]
                );

                // Clear auth-related data
                Object.keys(testData).forEach(key => {
                    localStorage.removeItem(key);
                });
                sessionStorage.removeItem('isGuest');
                sessionStorage.removeItem('showSignupSuccess');

                // Verify data was cleared
                const dataCleared = Object.keys(testData).every(key => 
                    localStorage.getItem(key) === null
                );

                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="${dataSet && dataCleared ? 'status-good' : 'status-error'}">
                            ${dataSet && dataCleared ? '✅' : '❌'} Storage Clearing Test
                        </span>
                        Data Set: ${dataSet}
                        Data Cleared: ${dataCleared}
                        Session Storage Cleared: ${sessionStorage.getItem('isGuest') === null}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Storage Clearing Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function testCrossTabSync() {
            const resultsEl = document.getElementById('storage-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                // Simulate cross-tab logout signal
                const logoutSignal = {
                    timestamp: Date.now(),
                    reason: 'test-logout'
                };

                localStorage.setItem('datastatpro-logout-signal', JSON.stringify(logoutSignal));

                // Set up listener for storage events
                const handleStorageEvent = (event) => {
                    if (event.key === 'datastatpro-logout-signal') {
                        resultsEl.innerHTML = currentContent + `
                            <div class="result-box">
                                <span class="status-good">✅ Cross-Tab Sync Test</span>
                                Storage event received: ${event.newValue}
                                Event type: ${event.type}
                            </div>
                        `;
                        window.removeEventListener('storage', handleStorageEvent);
                    }
                };

                window.addEventListener('storage', handleStorageEvent);

                // Clean up after 3 seconds
                setTimeout(() => {
                    localStorage.removeItem('datastatpro-logout-signal');
                    window.removeEventListener('storage', handleStorageEvent);
                    
                    if (!resultsEl.innerHTML.includes('Cross-Tab Sync Test')) {
                        resultsEl.innerHTML = currentContent + `
                            <div class="result-box">
                                <span class="status-warning">⚠️ Cross-Tab Sync Test</span>
                                No storage event received (expected in same tab)
                                Signal was set and removed successfully
                            </div>
                        `;
                    }
                }, 3000);

            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Cross-Tab Sync Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function clearAllAuthData() {
            const resultsEl = document.getElementById('storage-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                // Clear all authentication-related data
                const authKeys = [
                    'datastatpro-auth-flow-id',
                    'datastatpro-logout-signal',
                    'datastatpro-login-signal',
                    'firefox-use-fallback-auth',
                    'firefox-content-blocked',
                    'firefox-realtime-blocked'
                ];

                authKeys.forEach(key => localStorage.removeItem(key));

                const sessionKeys = ['isGuest', 'showSignupSuccess'];
                sessionKeys.forEach(key => sessionStorage.removeItem(key));

                // Reset current session
                currentSession = null;
                currentUser = null;

                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-good">✅ All Auth Data Cleared</span>
                        Cleared localStorage keys: ${authKeys.join(', ')}
                        Cleared sessionStorage keys: ${sessionKeys.join(', ')}
                        Reset current session variables
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Clear Auth Data Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function testFirefoxLogout() {
            const resultsEl = document.getElementById('firefox-test-results');
            const isFirefox = navigator.userAgent.includes('Firefox');

            const firefoxInfo = {
                isFirefox: isFirefox,
                userAgent: navigator.userAgent,
                firefoxVersion: isFirefox ? navigator.userAgent.match(/Firefox\/(\d+)/)?.[1] : 'N/A',
                secureContext: window.isSecureContext,
                serviceWorkerSupport: 'serviceWorker' in navigator,
                fetchSupport: 'fetch' in window,
                webSocketSupport: 'WebSocket' in window
            };

            resultsEl.innerHTML = `
                <div class="result-box">
                    <strong>Firefox Logout Environment:</strong>
                    ${JSON.stringify(firefoxInfo, null, 2)}
                    
                    <strong>Firefox-Specific Considerations:</strong>
                    - Enhanced Tracking Protection: ${isFirefox ? 'May be active' : 'N/A'}
                    - Content Blocking: ${isFirefox ? 'May affect logout requests' : 'N/A'}
                    - WebSocket Blocking: ${isFirefox ? 'May affect real-time logout sync' : 'N/A'}
                </div>
            `;
        }

        function testContentBlockingLogout() {
            const resultsEl = document.getElementById('firefox-test-results');
            const currentContent = resultsEl.innerHTML;

            if (!navigator.userAgent.includes('Firefox')) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-warning">⚠️ Content Blocking Test</span>
                        Not Firefox browser - content blocking test not applicable
                    </div>
                `;
                return;
            }

            try {
                // Test if content blocking affects logout
                const testUrls = [
                    `${SUPABASE_URL}/auth/v1/logout`,
                    'https://www.google-analytics.com/analytics.js',
                    'https://connect.facebook.net/en_US/fbevents.js'
                ];

                Promise.allSettled(testUrls.map(url => 
                    fetch(url, { method: 'HEAD', mode: 'no-cors' })
                        .then(() => ({ url, blocked: false }))
                        .catch(() => ({ url, blocked: true }))
                )).then(results => {
                    const testResults = results.map(result => 
                        result.status === 'fulfilled' ? result.value : { url: 'unknown', blocked: true }
                    );

                    resultsEl.innerHTML = currentContent + `
                        <div class="result-box">
                            <span class="status-good">✅ Content Blocking Test</span>
                            ${testResults.map(r => `${r.url}: ${r.blocked ? 'BLOCKED' : 'ALLOWED'}`).join('\n')}
                        </div>
                    `;
                });

            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Content Blocking Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthState();
        });
    </script>
</body>
</html>
