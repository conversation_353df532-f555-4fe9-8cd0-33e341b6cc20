import"./other-utils-CR9xr_gI.js";const h=n=>{if(n<=0)return-1/0;if(n>=1)return 1/0;if(n===.5)return 0;const o=2.515517,a=.802853,c=.010328,e=1.432788,r=.189269,s=.001308,i=n<.5?n:1-n,t=Math.sqrt(-2*Math.log(i)),u=t-(o+a*t+c*t*t)/(1+e*t+r*t*t+s*t*t*t);return n<.5?-u:u},l=(n,o,a,c,e)=>{const r=h(1-c/2),s=h(e);if(o===0||a===0)return 1/0;const i=Math.pow(r+s,2)*Math.pow(a,2)*n,t=Math.pow(o,2);return Math.ceil(i/t)},p=(n,o,a,c,e,r)=>{const s=h(1-e/2),i=h(r);if(o===0||a===0)return 1/0;const t=Math.pow(s+i,2)*Math.pow(a,2)*(1-c),u=Math.pow(o,2)*n;return Math.ceil(t/u)};export{l as a,p as c,h as g};
