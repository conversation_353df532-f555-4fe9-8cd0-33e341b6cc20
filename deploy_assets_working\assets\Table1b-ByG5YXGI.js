import{j as e,B as h,e as b,R as v,g as B,G as V,ai as P,b9 as E,ba as Q,bb as y,h as se,f as le,ae as re,ao as oe,ap as de,aq as ce,ar as A,as as n,at as he,bR as me}from"./mui-libs-CfwFIaTD.js";import{r as o}from"./react-libs-Cr2nE3UY.js";import{a as xe,f as be,D as ue}from"./index-Bpan7Tbe.js";import{A as pe}from"./AddToResultsButton-BwSXKCt2.js";import{P as ge}from"./PublicationReadyGate-BGFbKbJc.js";import{b as fe,c as je,a as ve,j as ye,l as Se}from"./descriptive-Djo0s6H4.js";import{c as Te}from"./normality-CwHD6Rjl.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const Be=()=>{const{datasets:m,currentDataset:S,setCurrentDataset:L}=xe(),{addResult:Ne}=be(),[F,z]=o.useState((S==null?void 0:S.id)||""),[x,$]=o.useState([]),[T,R]=o.useState(!1),[W,q]=o.useState(null),[N,u]=o.useState(null),[C,p]=o.useState(null),[G,D]=o.useState(!1),[O,I]=o.useState(""),l=m.find(a=>a.id===F),c=(l==null?void 0:l.columns.filter(a=>a.type===ue.NUMERIC))||[],H=a=>{const i=a.target.value;z(i),$([]),u(null),p(null);const s=m.find(t=>t.id===i);s&&L(s)},U=a=>{const i=a.target.value;$(typeof i=="string"?i.split(","):i),u(null),p(null)},J=()=>{if(!l||x.length===0){q("Please select a dataset and at least one numerical variable to analyze."),u(null),p(null);return}R(!0),q(null);const a=[];x.forEach(i=>{var g,f,j,w,M,k;const s=c.find(r=>r.id===i);if(!s){console.error(`Column with ID ${i} not found in selected dataset.`);return}const t=l.data.map(r=>r[s.name]).filter(r=>typeof r=="number"&&!isNaN(r));if(t.length>0){const r=fe(t),Y=je(t),Z=ve(t),[_,,ee]=ye(t),ae=Se(t),te=Math.min(...t),ie=Math.max(...t),d=Te(t,.05,["auto"]),ne={isNormal:d.overallAssessment.isNormal,pValue:((g=d.tests.shapiroWilk)==null?void 0:g.pValue)||((f=d.tests.kolmogorovSmirnov)==null?void 0:f.pValue)||((j=d.tests.jarqueBera)==null?void 0:j.pValue)||NaN,statistic:((w=d.tests.shapiroWilk)==null?void 0:w.statistic)||((M=d.tests.kolmogorovSmirnov)==null?void 0:M.statistic)||((k=d.tests.jarqueBera)==null?void 0:k.statistic)||NaN};a.push({variableName:s.name,n:t.length,mean:r,standardDeviation:Y,median:Z,q1:_,q3:ee,iqr:ae,min:te,max:ie,normalityPValue:ne.pValue})}}),u(a),p(K(a)),R(!1)},K=a=>{var s;if(a.length===0)return"";let i=`Table 1b presents the descriptive statistics for ${a.length} numerical variable${a.length>1?"s":""} from a dataset of ${((s=a[0])==null?void 0:s.n)||0} observations. `;return i+=`The table provides both measures of central tendency and dispersion, tailored to the distribution of each variable.

`,a.forEach((t,g)=>{const f=t.normalityPValue<.001?"less than 0.001":t.normalityPValue.toFixed(3),j=t.normalityPValue>=.05?"indicating that the distribution is normally distributed":"indicating that the distribution is not normal";i+=`**${t.variableName}:** The mean ${t.variableName.toLowerCase()} is ${t.mean.toFixed(2)} with a standard deviation of ${t.standardDeviation.toFixed(2)}. `,i+=`The median ${t.variableName.toLowerCase()} is ${t.median.toFixed(2)}, with the first quartile (Q1) at ${t.q1.toFixed(2)} and the third quartile (Q3) at ${t.q3.toFixed(2)}. `,i+=`The interquartile range (IQR) is ${t.iqr.toFixed(2)}, and the ${t.variableName.toLowerCase()} ranges from a minimum of ${t.min.toFixed(2)} to a maximum of ${t.max.toFixed(2)}. `,i+=`The p-value for normality is ${f}, ${j}.`,g<a.length-1&&(i+=`

`)}),i+=`

Overall, the table provides a comprehensive overview of the central tendencies and dispersions of the variables, highlighting the normality or non-normality of their distributions.`,i},X=()=>{D(!1)};return e.jsx(ge,{children:e.jsxs(h,{p:3,children:[e.jsx(b,{variant:"h5",gutterBottom:!0,children:"Table 1b: Comprehensive Descriptive Statistics"}),e.jsxs(v,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(b,{variant:"subtitle1",gutterBottom:!0,children:"Select Data and Numerical Variables"}),e.jsx(B,{severity:"info",sx:{mb:2},children:"This table provides comprehensive descriptive statistics (mean, standard deviation, median, quartiles, range, and normality tests) for multiple numerical variables in a publication-ready format."}),e.jsxs(V,{container:!0,spacing:2,children:[e.jsx(V,{item:!0,xs:12,children:e.jsxs(P,{fullWidth:!0,margin:"normal",children:[e.jsx(E,{id:"dataset-select-label",children:"Dataset"}),e.jsx(Q,{labelId:"dataset-select-label",id:"dataset-select",value:F,label:"Dataset",onChange:H,disabled:m.length===0,children:m.length===0?e.jsx(y,{value:"",disabled:!0,children:"No datasets available"}):m.map(a=>e.jsxs(y,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]})}),e.jsx(V,{item:!0,xs:12,children:e.jsxs(P,{fullWidth:!0,margin:"normal",children:[e.jsx(E,{id:"variable-select-label",children:"Numerical Variables"}),e.jsx(Q,{labelId:"variable-select-label",id:"variable-select",multiple:!0,value:x,onChange:U,label:"Numerical Variables",disabled:c.length===0,renderValue:a=>e.jsx(h,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:a.map(i=>{const s=c.find(t=>t.id===i);return e.jsx(se,{label:(s==null?void 0:s.name)||"",size:"small"},i)})}),children:c.length===0?e.jsx(y,{value:"",disabled:!0,children:"No numerical variables available in selected dataset"}):c.map(a=>e.jsx(y,{value:a.id,children:a.name},a.id))})]})})]}),e.jsx(h,{mt:2,children:e.jsx(le,{variant:"contained",color:"primary",onClick:J,disabled:T||x.length===0||!l,children:"Generate Descriptive Statistics Table"})})]}),T&&e.jsx(h,{display:"flex",justifyContent:"center",my:4,children:e.jsx(re,{})}),W&&e.jsx(B,{severity:"error",sx:{mb:3},children:W}),N&&!T&&l&&e.jsxs(h,{children:[e.jsxs(v,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(b,{variant:"h6",gutterBottom:!0,children:"Table 1b. Descriptive Statistics for Numerical Variables"}),e.jsx(oe,{component:v,variant:"outlined",children:e.jsxs(de,{size:"small",children:[e.jsx(ce,{children:e.jsxs(A,{children:[e.jsx(n,{sx:{fontWeight:"bold"},children:"Variable"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"N"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Mean"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"SD"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Median"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Q1"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Q3"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"IQR"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Min"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Max"}),e.jsx(n,{sx:{fontWeight:"bold"},align:"center",children:"Normality (p-value)"})]})}),e.jsx(he,{children:N.map((a,i)=>e.jsxs(A,{children:[e.jsx(n,{sx:{fontWeight:"bold"},children:a.variableName}),e.jsx(n,{align:"center",children:a.n}),e.jsx(n,{align:"center",children:a.mean.toFixed(2)}),e.jsx(n,{align:"center",children:a.standardDeviation.toFixed(2)}),e.jsx(n,{align:"center",children:a.median.toFixed(2)}),e.jsx(n,{align:"center",children:a.q1.toFixed(2)}),e.jsx(n,{align:"center",children:a.q3.toFixed(2)}),e.jsx(n,{align:"center",children:a.iqr.toFixed(2)}),e.jsx(n,{align:"center",children:a.min.toFixed(2)}),e.jsx(n,{align:"center",children:a.max.toFixed(2)}),e.jsx(n,{align:"center",children:a.normalityPValue<.001?"<0.001":a.normalityPValue.toFixed(3)})]},i))})]})})]}),C&&e.jsxs(v,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(b,{variant:"h6",gutterBottom:!0,children:"Statistical Interpretation"}),e.jsx(b,{variant:"body1",component:"pre",sx:{whiteSpace:"pre-wrap",fontFamily:"inherit"},children:C})]}),e.jsx(h,{sx:{display:"flex",justifyContent:"center",mt:2},children:e.jsx(pe,{resultData:{title:`Table 1b - Comprehensive Descriptive Statistics (${l.name})`,type:"descriptive",component:"Table1b",data:{dataset:l.name,variables:x.map(a=>{var i;return((i=c.find(s=>s.id===a))==null?void 0:i.name)||a}),results:N,interpretation:C,timestamp:new Date().toISOString(),totalSampleSize:l.data.length}},onSuccess:()=>{I("Results successfully added to Results Manager!"),D(!0)},onError:a=>{I(`Error adding results to Results Manager: ${a}`),D(!0)}})})]}),e.jsx(me,{open:G,autoHideDuration:4e3,onClose:X,message:O})]})})};export{Be as default};
