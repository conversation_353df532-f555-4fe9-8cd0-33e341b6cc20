import{u as Yr,j as z,C as _e,ae as Kr,R as ie,i as Se,e as wn,B as Vn,K as ve,c5 as Jr}from"./mui-libs-CfwFIaTD.js";import{g as Zr,r as Wn}from"./react-libs-Cr2nE3UY.js";import{H as na}from"./index-Bpan7Tbe.js";import{tutorialOptions as ea}from"./KnowledgeBasePage-1nzuPqIf.js";import St from"./katex-ChWnQ-fc.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";function vt(n){if(n)throw n}var ce=Object.prototype.hasOwnProperty,ki=Object.prototype.toString,kt=Object.defineProperty,wt=Object.getOwnPropertyDescriptor,Ct=function(t){return typeof Array.isArray=="function"?Array.isArray(t):ki.call(t)==="[object Array]"},At=function(t){if(!t||ki.call(t)!=="[object Object]")return!1;var e=ce.call(t,"constructor"),i=t.constructor&&t.constructor.prototype&&ce.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!e&&!i)return!1;var r;for(r in t);return typeof r>"u"||ce.call(t,r)},Tt=function(t,e){kt&&e.name==="__proto__"?kt(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},Rt=function(t,e){if(e==="__proto__")if(ce.call(t,e)){if(wt)return wt(t,e).value}else return;return t[e]},ta=function n(){var t,e,i,r,o,a,s=arguments[0],l=1,u=arguments.length,d=!1;for(typeof s=="boolean"&&(d=s,s=arguments[1]||{},l=2),(s==null||typeof s!="object"&&typeof s!="function")&&(s={});l<u;++l)if(t=arguments[l],t!=null)for(e in t)i=Rt(s,e),r=Rt(t,e),s!==r&&(d&&r&&(At(r)||(o=Ct(r)))?(o?(o=!1,a=i&&Ct(i)?i:[]):a=i&&At(i)?i:{},Tt(s,{name:e,newValue:n(d,a,r)})):typeof r<"u"&&Tt(s,{name:e,newValue:r}));return s};const ke=Zr(ta);function je(n){if(typeof n!="object"||n===null)return!1;const t=Object.getPrototypeOf(n);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function ia(){const n=[],t={run:e,use:i};return t;function e(...r){let o=-1;const a=r.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);s(null,...r);function s(l,...u){const d=n[++o];let c=-1;if(l){a(l);return}for(;++c<r.length;)(u[c]===null||u[c]===void 0)&&(u[c]=r[c]);r=u,d?ra(d,s)(...u):a(null,...u)}}function i(r){if(typeof r!="function")throw new TypeError("Expected `middelware` to be a function, not "+r);return n.push(r),t}}function ra(n,t){let e;return i;function i(...a){const s=n.length>a.length;let l;s&&a.push(r);try{l=n.apply(this,a)}catch(u){const d=u;if(s&&e)throw d;return r(d)}s||(l&&l.then&&typeof l.then=="function"?l.then(o,r):l instanceof Error?r(l):o(l))}function r(a,...s){e||(e=!0,t(a,...s))}function o(a){r(null,a)}}function Xn(n){return!n||typeof n!="object"?"":"position"in n||"type"in n?Mt(n.position):"start"in n||"end"in n?Mt(n):"line"in n||"column"in n?Be(n):""}function Be(n){return Et(n&&n.line)+":"+Et(n&&n.column)}function Mt(n){return Be(n&&n.start)+"-"+Be(n&&n.end)}function Et(n){return n&&typeof n=="number"?n:1}class en extends Error{constructor(t,e,i){super(),typeof e=="string"&&(i=e,e=void 0);let r="",o={},a=!1;if(e&&("line"in e&&"column"in e?o={place:e}:"start"in e&&"end"in e?o={place:e}:"type"in e?o={ancestors:[e],place:e.position}:o={...e}),typeof t=="string"?r=t:!o.cause&&t&&(a=!0,r=t.message,o.cause=t),!o.ruleId&&!o.source&&typeof i=="string"){const l=i.indexOf(":");l===-1?o.ruleId=i:(o.source=i.slice(0,l),o.ruleId=i.slice(l+1))}if(!o.place&&o.ancestors&&o.ancestors){const l=o.ancestors[o.ancestors.length-1];l&&(o.place=l.position)}const s=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=s?s.line:void 0,this.name=Xn(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=a&&o.cause&&typeof o.cause.stack=="string"?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}en.prototype.file="";en.prototype.name="";en.prototype.reason="";en.prototype.message="";en.prototype.stack="";en.prototype.column=void 0;en.prototype.line=void 0;en.prototype.ancestors=void 0;en.prototype.cause=void 0;en.prototype.fatal=void 0;en.prototype.place=void 0;en.prototype.ruleId=void 0;en.prototype.source=void 0;const mn={basename:aa,dirname:oa,extname:sa,join:la,sep:"/"};function aa(n,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');Jn(n);let e=0,i=-1,r=n.length,o;if(t===void 0||t.length===0||t.length>n.length){for(;r--;)if(n.codePointAt(r)===47){if(o){e=r+1;break}}else i<0&&(o=!0,i=r+1);return i<0?"":n.slice(e,i)}if(t===n)return"";let a=-1,s=t.length-1;for(;r--;)if(n.codePointAt(r)===47){if(o){e=r+1;break}}else a<0&&(o=!0,a=r+1),s>-1&&(n.codePointAt(r)===t.codePointAt(s--)?s<0&&(i=r):(s=-1,i=a));return e===i?i=a:i<0&&(i=n.length),n.slice(e,i)}function oa(n){if(Jn(n),n.length===0)return".";let t=-1,e=n.length,i;for(;--e;)if(n.codePointAt(e)===47){if(i){t=e;break}}else i||(i=!0);return t<0?n.codePointAt(0)===47?"/":".":t===1&&n.codePointAt(0)===47?"//":n.slice(0,t)}function sa(n){Jn(n);let t=n.length,e=-1,i=0,r=-1,o=0,a;for(;t--;){const s=n.codePointAt(t);if(s===47){if(a){i=t+1;break}continue}e<0&&(a=!0,e=t+1),s===46?r<0?r=t:o!==1&&(o=1):r>-1&&(o=-1)}return r<0||e<0||o===0||o===1&&r===e-1&&r===i+1?"":n.slice(r,e)}function la(...n){let t=-1,e;for(;++t<n.length;)Jn(n[t]),n[t]&&(e=e===void 0?n[t]:e+"/"+n[t]);return e===void 0?".":ua(e)}function ua(n){Jn(n);const t=n.codePointAt(0)===47;let e=ca(n,!t);return e.length===0&&!t&&(e="."),e.length>0&&n.codePointAt(n.length-1)===47&&(e+="/"),t?"/"+e:e}function ca(n,t){let e="",i=0,r=-1,o=0,a=-1,s,l;for(;++a<=n.length;){if(a<n.length)s=n.codePointAt(a);else{if(s===47)break;s=47}if(s===47){if(!(r===a-1||o===1))if(r!==a-1&&o===2){if(e.length<2||i!==2||e.codePointAt(e.length-1)!==46||e.codePointAt(e.length-2)!==46){if(e.length>2){if(l=e.lastIndexOf("/"),l!==e.length-1){l<0?(e="",i=0):(e=e.slice(0,l),i=e.length-1-e.lastIndexOf("/")),r=a,o=0;continue}}else if(e.length>0){e="",i=0,r=a,o=0;continue}}t&&(e=e.length>0?e+"/..":"..",i=2)}else e.length>0?e+="/"+n.slice(r+1,a):e=n.slice(r+1,a),i=a-r-1;r=a,o=0}else s===46&&o>-1?o++:o=-1}return e}function Jn(n){if(typeof n!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(n))}const da={cwd:fa};function fa(){return"/"}function Ve(n){return!!(n!==null&&typeof n=="object"&&"href"in n&&n.href&&"protocol"in n&&n.protocol&&n.auth===void 0)}function ma(n){if(typeof n=="string")n=new URL(n);else if(!Ve(n)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+n+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(n.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return pa(n)}function pa(n){if(n.hostname!==""){const i=new TypeError('File URL host must be "localhost" or empty on darwin');throw i.code="ERR_INVALID_FILE_URL_HOST",i}const t=n.pathname;let e=-1;for(;++e<t.length;)if(t.codePointAt(e)===37&&t.codePointAt(e+1)===50){const i=t.codePointAt(e+2);if(i===70||i===102){const r=new TypeError("File URL path must not include encoded / characters");throw r.code="ERR_INVALID_FILE_URL_PATH",r}}return decodeURIComponent(t)}const we=["history","path","basename","stem","extname","dirname"];class ha{constructor(t){let e;t?Ve(t)?e={path:t}:typeof t=="string"||ga(t)?e={value:t}:e=t:e={},this.cwd="cwd"in e?"":da.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let i=-1;for(;++i<we.length;){const o=we[i];o in e&&e[o]!==void 0&&e[o]!==null&&(this[o]=o==="history"?[...e[o]]:e[o])}let r;for(r in e)we.includes(r)||(this[r]=e[r])}get basename(){return typeof this.path=="string"?mn.basename(this.path):void 0}set basename(t){Ae(t,"basename"),Ce(t,"basename"),this.path=mn.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?mn.dirname(this.path):void 0}set dirname(t){Pt(this.basename,"dirname"),this.path=mn.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?mn.extname(this.path):void 0}set extname(t){if(Ce(t,"extname"),Pt(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=mn.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){Ve(t)&&(t=ma(t)),Ae(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?mn.basename(this.path,this.extname):void 0}set stem(t){Ae(t,"stem"),Ce(t,"stem"),this.path=mn.join(this.dirname||"",t+(this.extname||""))}fail(t,e,i){const r=this.message(t,e,i);throw r.fatal=!0,r}info(t,e,i){const r=this.message(t,e,i);return r.fatal=void 0,r}message(t,e,i){const r=new en(t,e,i);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function Ce(n,t){if(n&&n.includes(mn.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+mn.sep+"`")}function Ae(n,t){if(!n)throw new Error("`"+t+"` cannot be empty")}function Pt(n,t){if(!n)throw new Error("Setting `"+t+"` requires `path` to be set too")}function ga(n){return!!(n&&typeof n=="object"&&"byteLength"in n&&"byteOffset"in n)}const ba=function(n){const i=this.constructor.prototype,r=i[n],o=function(){return r.apply(o,arguments)};return Object.setPrototypeOf(o,i),o},$a={}.hasOwnProperty;class nt extends ba{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=ia()}copy(){const t=new nt;let e=-1;for(;++e<this.attachers.length;){const i=this.attachers[e];t.use(...i)}return t.data(ke(!0,{},this.namespace)),t}data(t,e){return typeof t=="string"?arguments.length===2?(Me("data",this.frozen),this.namespace[t]=e,this):$a.call(this.namespace,t)&&this.namespace[t]||void 0:t?(Me("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[e,...i]=this.attachers[this.freezeIndex];if(i[0]===!1)continue;i[0]===!0&&(i[0]=void 0);const r=e.call(t,...i);typeof r=="function"&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const e=re(t),i=this.parser||this.Parser;return Te("parse",i),i(String(e),e)}process(t,e){const i=this;return this.freeze(),Te("process",this.parser||this.Parser),Re("process",this.compiler||this.Compiler),e?r(void 0,e):new Promise(r);function r(o,a){const s=re(t),l=i.parse(s);i.run(l,s,function(d,c,m){if(d||!c||!m)return u(d);const f=c,g=i.stringify(f,m);_a(g)?m.value=g:m.result=g,u(d,m)});function u(d,c){d||!c?a(d):o?o(c):e(void 0,c)}}}processSync(t){let e=!1,i;return this.freeze(),Te("processSync",this.parser||this.Parser),Re("processSync",this.compiler||this.Compiler),this.process(t,r),zt("processSync","process",e),i;function r(o,a){e=!0,vt(o),i=a}}run(t,e,i){It(t),this.freeze();const r=this.transformers;return!i&&typeof e=="function"&&(i=e,e=void 0),i?o(void 0,i):new Promise(o);function o(a,s){const l=re(e);r.run(t,l,u);function u(d,c,m){const f=c||t;d?s(d):a?a(f):i(void 0,f,m)}}}runSync(t,e){let i=!1,r;return this.run(t,e,o),zt("runSync","run",i),r;function o(a,s){vt(a),r=s,i=!0}}stringify(t,e){this.freeze();const i=re(e),r=this.compiler||this.Compiler;return Re("stringify",r),It(t),r(t,i)}use(t,...e){const i=this.attachers,r=this.namespace;if(Me("use",this.frozen),t!=null)if(typeof t=="function")l(t,e);else if(typeof t=="object")Array.isArray(t)?s(t):a(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function o(u){if(typeof u=="function")l(u,[]);else if(typeof u=="object")if(Array.isArray(u)){const[d,...c]=u;l(d,c)}else a(u);else throw new TypeError("Expected usable value, not `"+u+"`")}function a(u){if(!("plugins"in u)&&!("settings"in u))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");s(u.plugins),u.settings&&(r.settings=ke(!0,r.settings,u.settings))}function s(u){let d=-1;if(u!=null)if(Array.isArray(u))for(;++d<u.length;){const c=u[d];o(c)}else throw new TypeError("Expected a list of plugins, not `"+u+"`")}function l(u,d){let c=-1,m=-1;for(;++c<i.length;)if(i[c][0]===u){m=c;break}if(m===-1)i.push([u,...d]);else if(d.length>0){let[f,...g]=d;const y=i[m][1];je(y)&&je(f)&&(f=ke(!0,y,f)),i[m]=[u,f,...g]}}}}const ya=new nt().freeze();function Te(n,t){if(typeof t!="function")throw new TypeError("Cannot `"+n+"` without `parser`")}function Re(n,t){if(typeof t!="function")throw new TypeError("Cannot `"+n+"` without `compiler`")}function Me(n,t){if(t)throw new Error("Cannot call `"+n+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function It(n){if(!je(n)||typeof n.type!="string")throw new TypeError("Expected node, got `"+n+"`")}function zt(n,t,e){if(!e)throw new Error("`"+n+"` finished async. Use `"+t+"` instead")}function re(n){return xa(n)?n:new ha(n)}function xa(n){return!!(n&&typeof n=="object"&&"message"in n&&"messages"in n)}function _a(n){return typeof n=="string"||Sa(n)}function Sa(n){return!!(n&&typeof n=="object"&&"byteLength"in n&&"byteOffset"in n)}const va={};function et(n,t){const e=va,i=typeof e.includeImageAlt=="boolean"?e.includeImageAlt:!0,r=typeof e.includeHtml=="boolean"?e.includeHtml:!0;return wi(n,i,r)}function wi(n,t,e){if(ka(n)){if("value"in n)return n.type==="html"&&!e?"":n.value;if(t&&"alt"in n&&n.alt)return n.alt;if("children"in n)return Ft(n.children,t,e)}return Array.isArray(n)?Ft(n,t,e):""}function Ft(n,t,e){const i=[];let r=-1;for(;++r<n.length;)i[r]=wi(n[r],t,e);return i.join("")}function ka(n){return!!(n&&typeof n=="object")}const Dt=document.createElement("i");function tt(n){const t="&"+n+";";Dt.innerHTML=t;const e=Dt.textContent;return e.charCodeAt(e.length-1)===59&&n!=="semi"||e===t?!1:e}function on(n,t,e,i){const r=n.length;let o=0,a;if(t<0?t=-t>r?0:r+t:t=t>r?r:t,e=e>0?e:0,i.length<1e4)a=Array.from(i),a.unshift(t,e),n.splice(...a);else for(e&&n.splice(t,e);o<i.length;)a=i.slice(o,o+1e4),a.unshift(t,0),n.splice(...a),o+=1e4,t+=1e4}function sn(n,t){return n.length>0?(on(n,n.length,0,t),n):t}const Nt={}.hasOwnProperty;function Ci(n){const t={};let e=-1;for(;++e<n.length;)wa(t,n[e]);return t}function wa(n,t){let e;for(e in t){const r=(Nt.call(n,e)?n[e]:void 0)||(n[e]={}),o=t[e];let a;if(o)for(a in o){Nt.call(r,a)||(r[a]=[]);const s=o[a];Ca(r[a],Array.isArray(s)?s:s?[s]:[])}}}function Ca(n,t){let e=-1;const i=[];for(;++e<t.length;)(t[e].add==="after"?n:i).push(t[e]);on(n,0,0,i)}function Ai(n,t){const e=Number.parseInt(n,t);return e<9||e===11||e>13&&e<32||e>126&&e<160||e>55295&&e<57344||e>64975&&e<65008||(e&65535)===65535||(e&65535)===65534||e>1114111?"�":String.fromCodePoint(e)}function fn(n){return n.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Z=Sn(/[A-Za-z]/),J=Sn(/[\dA-Za-z]/),Aa=Sn(/[#-'*+\--9=?A-Z^-~]/);function fe(n){return n!==null&&(n<32||n===127)}const We=Sn(/\d/),Ta=Sn(/[\dA-Fa-f]/),Ra=Sn(/[!-/:-@[-`{-~]/);function A(n){return n!==null&&n<-2}function W(n){return n!==null&&(n<0||n===32)}function N(n){return n===-2||n===-1||n===32}const be=Sn(new RegExp("\\p{P}|\\p{S}","u")),An=Sn(/\s/);function Sn(n){return t;function t(e){return e!==null&&e>-1&&n.test(String.fromCharCode(e))}}function Ln(n){const t=[];let e=-1,i=0,r=0;for(;++e<n.length;){const o=n.charCodeAt(e);let a="";if(o===37&&J(n.charCodeAt(e+1))&&J(n.charCodeAt(e+2)))r=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(a=String.fromCharCode(o));else if(o>55295&&o<57344){const s=n.charCodeAt(e+1);o<56320&&s>56319&&s<57344?(a=String.fromCharCode(o,s),r=1):a="�"}else a=String.fromCharCode(o);a&&(t.push(n.slice(i,e),encodeURIComponent(a)),i=e+r+1,a=""),r&&(e+=r,r=0)}return t.join("")+n.slice(i)}function D(n,t,e,i){const r=i?i-1:Number.POSITIVE_INFINITY;let o=0;return a;function a(l){return N(l)?(n.enter(e),s(l)):t(l)}function s(l){return N(l)&&o++<r?(n.consume(l),s):(n.exit(e),t(l))}}const Ma={tokenize:Ea};function Ea(n){const t=n.attempt(this.parser.constructs.contentInitial,i,r);let e;return t;function i(s){if(s===null){n.consume(s);return}return n.enter("lineEnding"),n.consume(s),n.exit("lineEnding"),D(n,t,"linePrefix")}function r(s){return n.enter("paragraph"),o(s)}function o(s){const l=n.enter("chunkText",{contentType:"text",previous:e});return e&&(e.next=l),e=l,a(s)}function a(s){if(s===null){n.exit("chunkText"),n.exit("paragraph"),n.consume(s);return}return A(s)?(n.consume(s),n.exit("chunkText"),o):(n.consume(s),a)}}const Pa={tokenize:Ia},Lt={tokenize:za};function Ia(n){const t=this,e=[];let i=0,r,o,a;return s;function s(k){if(i<e.length){const E=e[i];return t.containerState=E[1],n.attempt(E[0].continuation,l,u)(k)}return u(k)}function l(k){if(i++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,r&&x();const E=t.events.length;let P=E,S;for(;P--;)if(t.events[P][0]==="exit"&&t.events[P][1].type==="chunkFlow"){S=t.events[P][1].end;break}b(i);let L=E;for(;L<t.events.length;)t.events[L][1].end={...S},L++;return on(t.events,P+1,0,t.events.slice(E)),t.events.length=L,u(k)}return s(k)}function u(k){if(i===e.length){if(!r)return m(k);if(r.currentConstruct&&r.currentConstruct.concrete)return g(k);t.interrupt=!!(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return t.containerState={},n.check(Lt,d,c)(k)}function d(k){return r&&x(),b(i),m(k)}function c(k){return t.parser.lazy[t.now().line]=i!==e.length,a=t.now().offset,g(k)}function m(k){return t.containerState={},n.attempt(Lt,f,g)(k)}function f(k){return i++,e.push([t.currentConstruct,t.containerState]),m(k)}function g(k){if(k===null){r&&x(),b(0),n.consume(k);return}return r=r||t.parser.flow(t.now()),n.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),y(k)}function y(k){if(k===null){w(n.exit("chunkFlow"),!0),b(0),n.consume(k);return}return A(k)?(n.consume(k),w(n.exit("chunkFlow")),i=0,t.interrupt=void 0,s):(n.consume(k),y)}function w(k,E){const P=t.sliceStream(k);if(E&&P.push(null),k.previous=o,o&&(o.next=k),o=k,r.defineSkip(k.start),r.write(P),t.parser.lazy[k.start.line]){let S=r.events.length;for(;S--;)if(r.events[S][1].start.offset<a&&(!r.events[S][1].end||r.events[S][1].end.offset>a))return;const L=t.events.length;let j=L,I,$;for(;j--;)if(t.events[j][0]==="exit"&&t.events[j][1].type==="chunkFlow"){if(I){$=t.events[j][1].end;break}I=!0}for(b(i),S=L;S<t.events.length;)t.events[S][1].end={...$},S++;on(t.events,j+1,0,t.events.slice(L)),t.events.length=S}}function b(k){let E=e.length;for(;E-- >k;){const P=e[E];t.containerState=P[1],P[0].exit.call(t,n)}e.length=k}function x(){r.write([null]),o=void 0,r=void 0,t.containerState._closeFlow=void 0}}function za(n,t,e){return D(n,n.attempt(this.parser.constructs.document,t,e),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function Nn(n){if(n===null||W(n)||An(n))return 1;if(be(n))return 2}function $e(n,t,e){const i=[];let r=-1;for(;++r<n.length;){const o=n[r].resolveAll;o&&!i.includes(o)&&(t=o(t,e),i.push(o))}return t}const He={name:"attention",resolveAll:Fa,tokenize:Da};function Fa(n,t){let e=-1,i,r,o,a,s,l,u,d;for(;++e<n.length;)if(n[e][0]==="enter"&&n[e][1].type==="attentionSequence"&&n[e][1]._close){for(i=e;i--;)if(n[i][0]==="exit"&&n[i][1].type==="attentionSequence"&&n[i][1]._open&&t.sliceSerialize(n[i][1]).charCodeAt(0)===t.sliceSerialize(n[e][1]).charCodeAt(0)){if((n[i][1]._close||n[e][1]._open)&&(n[e][1].end.offset-n[e][1].start.offset)%3&&!((n[i][1].end.offset-n[i][1].start.offset+n[e][1].end.offset-n[e][1].start.offset)%3))continue;l=n[i][1].end.offset-n[i][1].start.offset>1&&n[e][1].end.offset-n[e][1].start.offset>1?2:1;const c={...n[i][1].end},m={...n[e][1].start};Ot(c,-l),Ot(m,l),a={type:l>1?"strongSequence":"emphasisSequence",start:c,end:{...n[i][1].end}},s={type:l>1?"strongSequence":"emphasisSequence",start:{...n[e][1].start},end:m},o={type:l>1?"strongText":"emphasisText",start:{...n[i][1].end},end:{...n[e][1].start}},r={type:l>1?"strong":"emphasis",start:{...a.start},end:{...s.end}},n[i][1].end={...a.start},n[e][1].start={...s.end},u=[],n[i][1].end.offset-n[i][1].start.offset&&(u=sn(u,[["enter",n[i][1],t],["exit",n[i][1],t]])),u=sn(u,[["enter",r,t],["enter",a,t],["exit",a,t],["enter",o,t]]),u=sn(u,$e(t.parser.constructs.insideSpan.null,n.slice(i+1,e),t)),u=sn(u,[["exit",o,t],["enter",s,t],["exit",s,t],["exit",r,t]]),n[e][1].end.offset-n[e][1].start.offset?(d=2,u=sn(u,[["enter",n[e][1],t],["exit",n[e][1],t]])):d=0,on(n,i-1,e-i+3,u),e=i+u.length-d-2;break}}for(e=-1;++e<n.length;)n[e][1].type==="attentionSequence"&&(n[e][1].type="data");return n}function Da(n,t){const e=this.parser.constructs.attentionMarkers.null,i=this.previous,r=Nn(i);let o;return a;function a(l){return o=l,n.enter("attentionSequence"),s(l)}function s(l){if(l===o)return n.consume(l),s;const u=n.exit("attentionSequence"),d=Nn(l),c=!d||d===2&&r||e.includes(l),m=!r||r===2&&d||e.includes(i);return u._open=!!(o===42?c:c&&(r||!m)),u._close=!!(o===42?m:m&&(d||!c)),t(l)}}function Ot(n,t){n.column+=t,n.offset+=t,n._bufferIndex+=t}const Na={name:"autolink",tokenize:La};function La(n,t,e){let i=0;return r;function r(f){return n.enter("autolink"),n.enter("autolinkMarker"),n.consume(f),n.exit("autolinkMarker"),n.enter("autolinkProtocol"),o}function o(f){return Z(f)?(n.consume(f),a):f===64?e(f):u(f)}function a(f){return f===43||f===45||f===46||J(f)?(i=1,s(f)):u(f)}function s(f){return f===58?(n.consume(f),i=0,l):(f===43||f===45||f===46||J(f))&&i++<32?(n.consume(f),s):(i=0,u(f))}function l(f){return f===62?(n.exit("autolinkProtocol"),n.enter("autolinkMarker"),n.consume(f),n.exit("autolinkMarker"),n.exit("autolink"),t):f===null||f===32||f===60||fe(f)?e(f):(n.consume(f),l)}function u(f){return f===64?(n.consume(f),d):Aa(f)?(n.consume(f),u):e(f)}function d(f){return J(f)?c(f):e(f)}function c(f){return f===46?(n.consume(f),i=0,d):f===62?(n.exit("autolinkProtocol").type="autolinkEmail",n.enter("autolinkMarker"),n.consume(f),n.exit("autolinkMarker"),n.exit("autolink"),t):m(f)}function m(f){if((f===45||J(f))&&i++<63){const g=f===45?m:c;return n.consume(f),g}return e(f)}}const Zn={partial:!0,tokenize:Oa};function Oa(n,t,e){return i;function i(o){return N(o)?D(n,r,"linePrefix")(o):r(o)}function r(o){return o===null||A(o)?t(o):e(o)}}const Ti={continuation:{tokenize:ja},exit:Ba,name:"blockQuote",tokenize:qa};function qa(n,t,e){const i=this;return r;function r(a){if(a===62){const s=i.containerState;return s.open||(n.enter("blockQuote",{_container:!0}),s.open=!0),n.enter("blockQuotePrefix"),n.enter("blockQuoteMarker"),n.consume(a),n.exit("blockQuoteMarker"),o}return e(a)}function o(a){return N(a)?(n.enter("blockQuotePrefixWhitespace"),n.consume(a),n.exit("blockQuotePrefixWhitespace"),n.exit("blockQuotePrefix"),t):(n.exit("blockQuotePrefix"),t(a))}}function ja(n,t,e){const i=this;return r;function r(a){return N(a)?D(n,o,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):o(a)}function o(a){return n.attempt(Ti,t,e)(a)}}function Ba(n){n.exit("blockQuote")}const Ri={name:"characterEscape",tokenize:Va};function Va(n,t,e){return i;function i(o){return n.enter("characterEscape"),n.enter("escapeMarker"),n.consume(o),n.exit("escapeMarker"),r}function r(o){return Ra(o)?(n.enter("characterEscapeValue"),n.consume(o),n.exit("characterEscapeValue"),n.exit("characterEscape"),t):e(o)}}const Mi={name:"characterReference",tokenize:Wa};function Wa(n,t,e){const i=this;let r=0,o,a;return s;function s(c){return n.enter("characterReference"),n.enter("characterReferenceMarker"),n.consume(c),n.exit("characterReferenceMarker"),l}function l(c){return c===35?(n.enter("characterReferenceMarkerNumeric"),n.consume(c),n.exit("characterReferenceMarkerNumeric"),u):(n.enter("characterReferenceValue"),o=31,a=J,d(c))}function u(c){return c===88||c===120?(n.enter("characterReferenceMarkerHexadecimal"),n.consume(c),n.exit("characterReferenceMarkerHexadecimal"),n.enter("characterReferenceValue"),o=6,a=Ta,d):(n.enter("characterReferenceValue"),o=7,a=We,d(c))}function d(c){if(c===59&&r){const m=n.exit("characterReferenceValue");return a===J&&!tt(i.sliceSerialize(m))?e(c):(n.enter("characterReferenceMarker"),n.consume(c),n.exit("characterReferenceMarker"),n.exit("characterReference"),t)}return a(c)&&r++<o?(n.consume(c),d):e(c)}}const qt={partial:!0,tokenize:Ua},jt={concrete:!0,name:"codeFenced",tokenize:Ha};function Ha(n,t,e){const i=this,r={partial:!0,tokenize:P};let o=0,a=0,s;return l;function l(S){return u(S)}function u(S){const L=i.events[i.events.length-1];return o=L&&L[1].type==="linePrefix"?L[2].sliceSerialize(L[1],!0).length:0,s=S,n.enter("codeFenced"),n.enter("codeFencedFence"),n.enter("codeFencedFenceSequence"),d(S)}function d(S){return S===s?(a++,n.consume(S),d):a<3?e(S):(n.exit("codeFencedFenceSequence"),N(S)?D(n,c,"whitespace")(S):c(S))}function c(S){return S===null||A(S)?(n.exit("codeFencedFence"),i.interrupt?t(S):n.check(qt,y,E)(S)):(n.enter("codeFencedFenceInfo"),n.enter("chunkString",{contentType:"string"}),m(S))}function m(S){return S===null||A(S)?(n.exit("chunkString"),n.exit("codeFencedFenceInfo"),c(S)):N(S)?(n.exit("chunkString"),n.exit("codeFencedFenceInfo"),D(n,f,"whitespace")(S)):S===96&&S===s?e(S):(n.consume(S),m)}function f(S){return S===null||A(S)?c(S):(n.enter("codeFencedFenceMeta"),n.enter("chunkString",{contentType:"string"}),g(S))}function g(S){return S===null||A(S)?(n.exit("chunkString"),n.exit("codeFencedFenceMeta"),c(S)):S===96&&S===s?e(S):(n.consume(S),g)}function y(S){return n.attempt(r,E,w)(S)}function w(S){return n.enter("lineEnding"),n.consume(S),n.exit("lineEnding"),b}function b(S){return o>0&&N(S)?D(n,x,"linePrefix",o+1)(S):x(S)}function x(S){return S===null||A(S)?n.check(qt,y,E)(S):(n.enter("codeFlowValue"),k(S))}function k(S){return S===null||A(S)?(n.exit("codeFlowValue"),x(S)):(n.consume(S),k)}function E(S){return n.exit("codeFenced"),t(S)}function P(S,L,j){let I=0;return $;function $(q){return S.enter("lineEnding"),S.consume(q),S.exit("lineEnding"),T}function T(q){return S.enter("codeFencedFence"),N(q)?D(S,R,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(q):R(q)}function R(q){return q===s?(S.enter("codeFencedFenceSequence"),B(q)):j(q)}function B(q){return q===s?(I++,S.consume(q),B):I>=a?(S.exit("codeFencedFenceSequence"),N(q)?D(S,U,"whitespace")(q):U(q)):j(q)}function U(q){return q===null||A(q)?(S.exit("codeFencedFence"),L(q)):j(q)}}}function Ua(n,t,e){const i=this;return r;function r(a){return a===null?e(a):(n.enter("lineEnding"),n.consume(a),n.exit("lineEnding"),o)}function o(a){return i.parser.lazy[i.now().line]?e(a):t(a)}}const Ee={name:"codeIndented",tokenize:Ga},Xa={partial:!0,tokenize:Qa};function Ga(n,t,e){const i=this;return r;function r(u){return n.enter("codeIndented"),D(n,o,"linePrefix",5)(u)}function o(u){const d=i.events[i.events.length-1];return d&&d[1].type==="linePrefix"&&d[2].sliceSerialize(d[1],!0).length>=4?a(u):e(u)}function a(u){return u===null?l(u):A(u)?n.attempt(Xa,a,l)(u):(n.enter("codeFlowValue"),s(u))}function s(u){return u===null||A(u)?(n.exit("codeFlowValue"),a(u)):(n.consume(u),s)}function l(u){return n.exit("codeIndented"),t(u)}}function Qa(n,t,e){const i=this;return r;function r(a){return i.parser.lazy[i.now().line]?e(a):A(a)?(n.enter("lineEnding"),n.consume(a),n.exit("lineEnding"),r):D(n,o,"linePrefix",5)(a)}function o(a){const s=i.events[i.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(a):A(a)?r(a):e(a)}}const Ya={name:"codeText",previous:Ja,resolve:Ka,tokenize:Za};function Ka(n){let t=n.length-4,e=3,i,r;if((n[e][1].type==="lineEnding"||n[e][1].type==="space")&&(n[t][1].type==="lineEnding"||n[t][1].type==="space")){for(i=e;++i<t;)if(n[i][1].type==="codeTextData"){n[e][1].type="codeTextPadding",n[t][1].type="codeTextPadding",e+=2,t-=2;break}}for(i=e-1,t++;++i<=t;)r===void 0?i!==t&&n[i][1].type!=="lineEnding"&&(r=i):(i===t||n[i][1].type==="lineEnding")&&(n[r][1].type="codeTextData",i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),t-=i-r-2,i=r+2),r=void 0);return n}function Ja(n){return n!==96||this.events[this.events.length-1][1].type==="characterEscape"}function Za(n,t,e){let i=0,r,o;return a;function a(c){return n.enter("codeText"),n.enter("codeTextSequence"),s(c)}function s(c){return c===96?(n.consume(c),i++,s):(n.exit("codeTextSequence"),l(c))}function l(c){return c===null?e(c):c===32?(n.enter("space"),n.consume(c),n.exit("space"),l):c===96?(o=n.enter("codeTextSequence"),r=0,d(c)):A(c)?(n.enter("lineEnding"),n.consume(c),n.exit("lineEnding"),l):(n.enter("codeTextData"),u(c))}function u(c){return c===null||c===32||c===96||A(c)?(n.exit("codeTextData"),l(c)):(n.consume(c),u)}function d(c){return c===96?(n.consume(c),r++,d):r===i?(n.exit("codeTextSequence"),n.exit("codeText"),t(c)):(o.type="codeTextData",u(c))}}class no{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,e){const i=e??Number.POSITIVE_INFINITY;return i<this.left.length?this.left.slice(t,i):t>this.left.length?this.right.slice(this.right.length-i+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-i+this.left.length).reverse())}splice(t,e,i){const r=e||0;this.setCursor(Math.trunc(t));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return i&&Hn(this.left,i),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),Hn(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),Hn(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const e=this.left.splice(t,Number.POSITIVE_INFINITY);Hn(this.right,e.reverse())}else{const e=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);Hn(this.left,e.reverse())}}}function Hn(n,t){let e=0;if(t.length<1e4)n.push(...t);else for(;e<t.length;)n.push(...t.slice(e,e+1e4)),e+=1e4}function Ei(n){const t={};let e=-1,i,r,o,a,s,l,u;const d=new no(n);for(;++e<d.length;){for(;e in t;)e=t[e];if(i=d.get(e),e&&i[1].type==="chunkFlow"&&d.get(e-1)[1].type==="listItemPrefix"&&(l=i[1]._tokenizer.events,o=0,o<l.length&&l[o][1].type==="lineEndingBlank"&&(o+=2),o<l.length&&l[o][1].type==="content"))for(;++o<l.length&&l[o][1].type!=="content";)l[o][1].type==="chunkText"&&(l[o][1]._isInFirstContentOfListItem=!0,o++);if(i[0]==="enter")i[1].contentType&&(Object.assign(t,eo(d,e)),e=t[e],u=!0);else if(i[1]._container){for(o=e,r=void 0;o--;)if(a=d.get(o),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank")a[0]==="enter"&&(r&&(d.get(r)[1].type="lineEndingBlank"),a[1].type="lineEnding",r=o);else if(!(a[1].type==="linePrefix"||a[1].type==="listItemIndent"))break;r&&(i[1].end={...d.get(r)[1].start},s=d.slice(r,e),s.unshift(i),d.splice(r,e-r+1,s))}}return on(n,0,Number.POSITIVE_INFINITY,d.slice(0)),!u}function eo(n,t){const e=n.get(t)[1],i=n.get(t)[2];let r=t-1;const o=[];let a=e._tokenizer;a||(a=i.parser[e.contentType](e.start),e._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));const s=a.events,l=[],u={};let d,c,m=-1,f=e,g=0,y=0;const w=[y];for(;f;){for(;n.get(++r)[1]!==f;);o.push(r),f._tokenizer||(d=i.sliceStream(f),f.next||d.push(null),c&&a.defineSkip(f.start),f._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(d),f._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),c=f,f=f.next}for(f=e;++m<s.length;)s[m][0]==="exit"&&s[m-1][0]==="enter"&&s[m][1].type===s[m-1][1].type&&s[m][1].start.line!==s[m][1].end.line&&(y=m+1,w.push(y),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(a.events=[],f?(f._tokenizer=void 0,f.previous=void 0):w.pop(),m=w.length;m--;){const b=s.slice(w[m],w[m+1]),x=o.pop();l.push([x,x+b.length-1]),n.splice(x,2,b)}for(l.reverse(),m=-1;++m<l.length;)u[g+l[m][0]]=g+l[m][1],g+=l[m][1]-l[m][0]-1;return u}const to={resolve:ro,tokenize:ao},io={partial:!0,tokenize:oo};function ro(n){return Ei(n),n}function ao(n,t){let e;return i;function i(s){return n.enter("content"),e=n.enter("chunkContent",{contentType:"content"}),r(s)}function r(s){return s===null?o(s):A(s)?n.check(io,a,o)(s):(n.consume(s),r)}function o(s){return n.exit("chunkContent"),n.exit("content"),t(s)}function a(s){return n.consume(s),n.exit("chunkContent"),e.next=n.enter("chunkContent",{contentType:"content",previous:e}),e=e.next,r}}function oo(n,t,e){const i=this;return r;function r(a){return n.exit("chunkContent"),n.enter("lineEnding"),n.consume(a),n.exit("lineEnding"),D(n,o,"linePrefix")}function o(a){if(a===null||A(a))return e(a);const s=i.events[i.events.length-1];return!i.parser.constructs.disable.null.includes("codeIndented")&&s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(a):n.interrupt(i.parser.constructs.flow,e,t)(a)}}function Pi(n,t,e,i,r,o,a,s,l){const u=l||Number.POSITIVE_INFINITY;let d=0;return c;function c(b){return b===60?(n.enter(i),n.enter(r),n.enter(o),n.consume(b),n.exit(o),m):b===null||b===32||b===41||fe(b)?e(b):(n.enter(i),n.enter(a),n.enter(s),n.enter("chunkString",{contentType:"string"}),y(b))}function m(b){return b===62?(n.enter(o),n.consume(b),n.exit(o),n.exit(r),n.exit(i),t):(n.enter(s),n.enter("chunkString",{contentType:"string"}),f(b))}function f(b){return b===62?(n.exit("chunkString"),n.exit(s),m(b)):b===null||b===60||A(b)?e(b):(n.consume(b),b===92?g:f)}function g(b){return b===60||b===62||b===92?(n.consume(b),f):f(b)}function y(b){return!d&&(b===null||b===41||W(b))?(n.exit("chunkString"),n.exit(s),n.exit(a),n.exit(i),t(b)):d<u&&b===40?(n.consume(b),d++,y):b===41?(n.consume(b),d--,y):b===null||b===32||b===40||fe(b)?e(b):(n.consume(b),b===92?w:y)}function w(b){return b===40||b===41||b===92?(n.consume(b),y):y(b)}}function Ii(n,t,e,i,r,o){const a=this;let s=0,l;return u;function u(f){return n.enter(i),n.enter(r),n.consume(f),n.exit(r),n.enter(o),d}function d(f){return s>999||f===null||f===91||f===93&&!l||f===94&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?e(f):f===93?(n.exit(o),n.enter(r),n.consume(f),n.exit(r),n.exit(i),t):A(f)?(n.enter("lineEnding"),n.consume(f),n.exit("lineEnding"),d):(n.enter("chunkString",{contentType:"string"}),c(f))}function c(f){return f===null||f===91||f===93||A(f)||s++>999?(n.exit("chunkString"),d(f)):(n.consume(f),l||(l=!N(f)),f===92?m:c)}function m(f){return f===91||f===92||f===93?(n.consume(f),s++,c):c(f)}}function zi(n,t,e,i,r,o){let a;return s;function s(m){return m===34||m===39||m===40?(n.enter(i),n.enter(r),n.consume(m),n.exit(r),a=m===40?41:m,l):e(m)}function l(m){return m===a?(n.enter(r),n.consume(m),n.exit(r),n.exit(i),t):(n.enter(o),u(m))}function u(m){return m===a?(n.exit(o),l(a)):m===null?e(m):A(m)?(n.enter("lineEnding"),n.consume(m),n.exit("lineEnding"),D(n,u,"linePrefix")):(n.enter("chunkString",{contentType:"string"}),d(m))}function d(m){return m===a||m===null||A(m)?(n.exit("chunkString"),u(m)):(n.consume(m),m===92?c:d)}function c(m){return m===a||m===92?(n.consume(m),d):d(m)}}function Gn(n,t){let e;return i;function i(r){return A(r)?(n.enter("lineEnding"),n.consume(r),n.exit("lineEnding"),e=!0,i):N(r)?D(n,i,e?"linePrefix":"lineSuffix")(r):t(r)}}const so={name:"definition",tokenize:uo},lo={partial:!0,tokenize:co};function uo(n,t,e){const i=this;let r;return o;function o(f){return n.enter("definition"),a(f)}function a(f){return Ii.call(i,n,s,e,"definitionLabel","definitionLabelMarker","definitionLabelString")(f)}function s(f){return r=fn(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),f===58?(n.enter("definitionMarker"),n.consume(f),n.exit("definitionMarker"),l):e(f)}function l(f){return W(f)?Gn(n,u)(f):u(f)}function u(f){return Pi(n,d,e,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(f)}function d(f){return n.attempt(lo,c,c)(f)}function c(f){return N(f)?D(n,m,"whitespace")(f):m(f)}function m(f){return f===null||A(f)?(n.exit("definition"),i.parser.defined.push(r),t(f)):e(f)}}function co(n,t,e){return i;function i(s){return W(s)?Gn(n,r)(s):e(s)}function r(s){return zi(n,o,e,"definitionTitle","definitionTitleMarker","definitionTitleString")(s)}function o(s){return N(s)?D(n,a,"whitespace")(s):a(s)}function a(s){return s===null||A(s)?t(s):e(s)}}const fo={name:"hardBreakEscape",tokenize:mo};function mo(n,t,e){return i;function i(o){return n.enter("hardBreakEscape"),n.consume(o),r}function r(o){return A(o)?(n.exit("hardBreakEscape"),t(o)):e(o)}}const po={name:"headingAtx",resolve:ho,tokenize:go};function ho(n,t){let e=n.length-2,i=3,r,o;return n[i][1].type==="whitespace"&&(i+=2),e-2>i&&n[e][1].type==="whitespace"&&(e-=2),n[e][1].type==="atxHeadingSequence"&&(i===e-1||e-4>i&&n[e-2][1].type==="whitespace")&&(e-=i+1===e?2:4),e>i&&(r={type:"atxHeadingText",start:n[i][1].start,end:n[e][1].end},o={type:"chunkText",start:n[i][1].start,end:n[e][1].end,contentType:"text"},on(n,i,e-i+1,[["enter",r,t],["enter",o,t],["exit",o,t],["exit",r,t]])),n}function go(n,t,e){let i=0;return r;function r(d){return n.enter("atxHeading"),o(d)}function o(d){return n.enter("atxHeadingSequence"),a(d)}function a(d){return d===35&&i++<6?(n.consume(d),a):d===null||W(d)?(n.exit("atxHeadingSequence"),s(d)):e(d)}function s(d){return d===35?(n.enter("atxHeadingSequence"),l(d)):d===null||A(d)?(n.exit("atxHeading"),t(d)):N(d)?D(n,s,"whitespace")(d):(n.enter("atxHeadingText"),u(d))}function l(d){return d===35?(n.consume(d),l):(n.exit("atxHeadingSequence"),s(d))}function u(d){return d===null||d===35||W(d)?(n.exit("atxHeadingText"),s(d)):(n.consume(d),u)}}const bo=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Bt=["pre","script","style","textarea"],$o={concrete:!0,name:"htmlFlow",resolveTo:_o,tokenize:So},yo={partial:!0,tokenize:ko},xo={partial:!0,tokenize:vo};function _o(n){let t=n.length;for(;t--&&!(n[t][0]==="enter"&&n[t][1].type==="htmlFlow"););return t>1&&n[t-2][1].type==="linePrefix"&&(n[t][1].start=n[t-2][1].start,n[t+1][1].start=n[t-2][1].start,n.splice(t-2,2)),n}function So(n,t,e){const i=this;let r,o,a,s,l;return u;function u(h){return d(h)}function d(h){return n.enter("htmlFlow"),n.enter("htmlFlowData"),n.consume(h),c}function c(h){return h===33?(n.consume(h),m):h===47?(n.consume(h),o=!0,y):h===63?(n.consume(h),r=3,i.interrupt?t:p):Z(h)?(n.consume(h),a=String.fromCharCode(h),w):e(h)}function m(h){return h===45?(n.consume(h),r=2,f):h===91?(n.consume(h),r=5,s=0,g):Z(h)?(n.consume(h),r=4,i.interrupt?t:p):e(h)}function f(h){return h===45?(n.consume(h),i.interrupt?t:p):e(h)}function g(h){const cn="CDATA[";return h===cn.charCodeAt(s++)?(n.consume(h),s===cn.length?i.interrupt?t:R:g):e(h)}function y(h){return Z(h)?(n.consume(h),a=String.fromCharCode(h),w):e(h)}function w(h){if(h===null||h===47||h===62||W(h)){const cn=h===47,vn=a.toLowerCase();return!cn&&!o&&Bt.includes(vn)?(r=1,i.interrupt?t(h):R(h)):bo.includes(a.toLowerCase())?(r=6,cn?(n.consume(h),b):i.interrupt?t(h):R(h)):(r=7,i.interrupt&&!i.parser.lazy[i.now().line]?e(h):o?x(h):k(h))}return h===45||J(h)?(n.consume(h),a+=String.fromCharCode(h),w):e(h)}function b(h){return h===62?(n.consume(h),i.interrupt?t:R):e(h)}function x(h){return N(h)?(n.consume(h),x):$(h)}function k(h){return h===47?(n.consume(h),$):h===58||h===95||Z(h)?(n.consume(h),E):N(h)?(n.consume(h),k):$(h)}function E(h){return h===45||h===46||h===58||h===95||J(h)?(n.consume(h),E):P(h)}function P(h){return h===61?(n.consume(h),S):N(h)?(n.consume(h),P):k(h)}function S(h){return h===null||h===60||h===61||h===62||h===96?e(h):h===34||h===39?(n.consume(h),l=h,L):N(h)?(n.consume(h),S):j(h)}function L(h){return h===l?(n.consume(h),l=null,I):h===null||A(h)?e(h):(n.consume(h),L)}function j(h){return h===null||h===34||h===39||h===47||h===60||h===61||h===62||h===96||W(h)?P(h):(n.consume(h),j)}function I(h){return h===47||h===62||N(h)?k(h):e(h)}function $(h){return h===62?(n.consume(h),T):e(h)}function T(h){return h===null||A(h)?R(h):N(h)?(n.consume(h),T):e(h)}function R(h){return h===45&&r===2?(n.consume(h),K):h===60&&r===1?(n.consume(h),Q):h===62&&r===4?(n.consume(h),un):h===63&&r===3?(n.consume(h),p):h===93&&r===5?(n.consume(h),hn):A(h)&&(r===6||r===7)?(n.exit("htmlFlowData"),n.check(yo,gn,B)(h)):h===null||A(h)?(n.exit("htmlFlowData"),B(h)):(n.consume(h),R)}function B(h){return n.check(xo,U,gn)(h)}function U(h){return n.enter("lineEnding"),n.consume(h),n.exit("lineEnding"),q}function q(h){return h===null||A(h)?B(h):(n.enter("htmlFlowData"),R(h))}function K(h){return h===45?(n.consume(h),p):R(h)}function Q(h){return h===47?(n.consume(h),a="",ln):R(h)}function ln(h){if(h===62){const cn=a.toLowerCase();return Bt.includes(cn)?(n.consume(h),un):R(h)}return Z(h)&&a.length<8?(n.consume(h),a+=String.fromCharCode(h),ln):R(h)}function hn(h){return h===93?(n.consume(h),p):R(h)}function p(h){return h===62?(n.consume(h),un):h===45&&r===2?(n.consume(h),p):R(h)}function un(h){return h===null||A(h)?(n.exit("htmlFlowData"),gn(h)):(n.consume(h),un)}function gn(h){return n.exit("htmlFlow"),t(h)}}function vo(n,t,e){const i=this;return r;function r(a){return A(a)?(n.enter("lineEnding"),n.consume(a),n.exit("lineEnding"),o):e(a)}function o(a){return i.parser.lazy[i.now().line]?e(a):t(a)}}function ko(n,t,e){return i;function i(r){return n.enter("lineEnding"),n.consume(r),n.exit("lineEnding"),n.attempt(Zn,t,e)}}const wo={name:"htmlText",tokenize:Co};function Co(n,t,e){const i=this;let r,o,a;return s;function s(p){return n.enter("htmlText"),n.enter("htmlTextData"),n.consume(p),l}function l(p){return p===33?(n.consume(p),u):p===47?(n.consume(p),P):p===63?(n.consume(p),k):Z(p)?(n.consume(p),j):e(p)}function u(p){return p===45?(n.consume(p),d):p===91?(n.consume(p),o=0,g):Z(p)?(n.consume(p),x):e(p)}function d(p){return p===45?(n.consume(p),f):e(p)}function c(p){return p===null?e(p):p===45?(n.consume(p),m):A(p)?(a=c,Q(p)):(n.consume(p),c)}function m(p){return p===45?(n.consume(p),f):c(p)}function f(p){return p===62?K(p):p===45?m(p):c(p)}function g(p){const un="CDATA[";return p===un.charCodeAt(o++)?(n.consume(p),o===un.length?y:g):e(p)}function y(p){return p===null?e(p):p===93?(n.consume(p),w):A(p)?(a=y,Q(p)):(n.consume(p),y)}function w(p){return p===93?(n.consume(p),b):y(p)}function b(p){return p===62?K(p):p===93?(n.consume(p),b):y(p)}function x(p){return p===null||p===62?K(p):A(p)?(a=x,Q(p)):(n.consume(p),x)}function k(p){return p===null?e(p):p===63?(n.consume(p),E):A(p)?(a=k,Q(p)):(n.consume(p),k)}function E(p){return p===62?K(p):k(p)}function P(p){return Z(p)?(n.consume(p),S):e(p)}function S(p){return p===45||J(p)?(n.consume(p),S):L(p)}function L(p){return A(p)?(a=L,Q(p)):N(p)?(n.consume(p),L):K(p)}function j(p){return p===45||J(p)?(n.consume(p),j):p===47||p===62||W(p)?I(p):e(p)}function I(p){return p===47?(n.consume(p),K):p===58||p===95||Z(p)?(n.consume(p),$):A(p)?(a=I,Q(p)):N(p)?(n.consume(p),I):K(p)}function $(p){return p===45||p===46||p===58||p===95||J(p)?(n.consume(p),$):T(p)}function T(p){return p===61?(n.consume(p),R):A(p)?(a=T,Q(p)):N(p)?(n.consume(p),T):I(p)}function R(p){return p===null||p===60||p===61||p===62||p===96?e(p):p===34||p===39?(n.consume(p),r=p,B):A(p)?(a=R,Q(p)):N(p)?(n.consume(p),R):(n.consume(p),U)}function B(p){return p===r?(n.consume(p),r=void 0,q):p===null?e(p):A(p)?(a=B,Q(p)):(n.consume(p),B)}function U(p){return p===null||p===34||p===39||p===60||p===61||p===96?e(p):p===47||p===62||W(p)?I(p):(n.consume(p),U)}function q(p){return p===47||p===62||W(p)?I(p):e(p)}function K(p){return p===62?(n.consume(p),n.exit("htmlTextData"),n.exit("htmlText"),t):e(p)}function Q(p){return n.exit("htmlTextData"),n.enter("lineEnding"),n.consume(p),n.exit("lineEnding"),ln}function ln(p){return N(p)?D(n,hn,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(p):hn(p)}function hn(p){return n.enter("htmlTextData"),a(p)}}const it={name:"labelEnd",resolveAll:Mo,resolveTo:Eo,tokenize:Po},Ao={tokenize:Io},To={tokenize:zo},Ro={tokenize:Fo};function Mo(n){let t=-1;const e=[];for(;++t<n.length;){const i=n[t][1];if(e.push(n[t]),i.type==="labelImage"||i.type==="labelLink"||i.type==="labelEnd"){const r=i.type==="labelImage"?4:2;i.type="data",t+=r}}return n.length!==e.length&&on(n,0,n.length,e),n}function Eo(n,t){let e=n.length,i=0,r,o,a,s;for(;e--;)if(r=n[e][1],o){if(r.type==="link"||r.type==="labelLink"&&r._inactive)break;n[e][0]==="enter"&&r.type==="labelLink"&&(r._inactive=!0)}else if(a){if(n[e][0]==="enter"&&(r.type==="labelImage"||r.type==="labelLink")&&!r._balanced&&(o=e,r.type!=="labelLink")){i=2;break}}else r.type==="labelEnd"&&(a=e);const l={type:n[o][1].type==="labelLink"?"link":"image",start:{...n[o][1].start},end:{...n[n.length-1][1].end}},u={type:"label",start:{...n[o][1].start},end:{...n[a][1].end}},d={type:"labelText",start:{...n[o+i+2][1].end},end:{...n[a-2][1].start}};return s=[["enter",l,t],["enter",u,t]],s=sn(s,n.slice(o+1,o+i+3)),s=sn(s,[["enter",d,t]]),s=sn(s,$e(t.parser.constructs.insideSpan.null,n.slice(o+i+4,a-3),t)),s=sn(s,[["exit",d,t],n[a-2],n[a-1],["exit",u,t]]),s=sn(s,n.slice(a+1)),s=sn(s,[["exit",l,t]]),on(n,o,n.length,s),n}function Po(n,t,e){const i=this;let r=i.events.length,o,a;for(;r--;)if((i.events[r][1].type==="labelImage"||i.events[r][1].type==="labelLink")&&!i.events[r][1]._balanced){o=i.events[r][1];break}return s;function s(m){return o?o._inactive?c(m):(a=i.parser.defined.includes(fn(i.sliceSerialize({start:o.end,end:i.now()}))),n.enter("labelEnd"),n.enter("labelMarker"),n.consume(m),n.exit("labelMarker"),n.exit("labelEnd"),l):e(m)}function l(m){return m===40?n.attempt(Ao,d,a?d:c)(m):m===91?n.attempt(To,d,a?u:c)(m):a?d(m):c(m)}function u(m){return n.attempt(Ro,d,c)(m)}function d(m){return t(m)}function c(m){return o._balanced=!0,e(m)}}function Io(n,t,e){return i;function i(c){return n.enter("resource"),n.enter("resourceMarker"),n.consume(c),n.exit("resourceMarker"),r}function r(c){return W(c)?Gn(n,o)(c):o(c)}function o(c){return c===41?d(c):Pi(n,a,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(c)}function a(c){return W(c)?Gn(n,l)(c):d(c)}function s(c){return e(c)}function l(c){return c===34||c===39||c===40?zi(n,u,e,"resourceTitle","resourceTitleMarker","resourceTitleString")(c):d(c)}function u(c){return W(c)?Gn(n,d)(c):d(c)}function d(c){return c===41?(n.enter("resourceMarker"),n.consume(c),n.exit("resourceMarker"),n.exit("resource"),t):e(c)}}function zo(n,t,e){const i=this;return r;function r(s){return Ii.call(i,n,o,a,"reference","referenceMarker","referenceString")(s)}function o(s){return i.parser.defined.includes(fn(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)))?t(s):e(s)}function a(s){return e(s)}}function Fo(n,t,e){return i;function i(o){return n.enter("reference"),n.enter("referenceMarker"),n.consume(o),n.exit("referenceMarker"),r}function r(o){return o===93?(n.enter("referenceMarker"),n.consume(o),n.exit("referenceMarker"),n.exit("reference"),t):e(o)}}const Do={name:"labelStartImage",resolveAll:it.resolveAll,tokenize:No};function No(n,t,e){const i=this;return r;function r(s){return n.enter("labelImage"),n.enter("labelImageMarker"),n.consume(s),n.exit("labelImageMarker"),o}function o(s){return s===91?(n.enter("labelMarker"),n.consume(s),n.exit("labelMarker"),n.exit("labelImage"),a):e(s)}function a(s){return s===94&&"_hiddenFootnoteSupport"in i.parser.constructs?e(s):t(s)}}const Lo={name:"labelStartLink",resolveAll:it.resolveAll,tokenize:Oo};function Oo(n,t,e){const i=this;return r;function r(a){return n.enter("labelLink"),n.enter("labelMarker"),n.consume(a),n.exit("labelMarker"),n.exit("labelLink"),o}function o(a){return a===94&&"_hiddenFootnoteSupport"in i.parser.constructs?e(a):t(a)}}const Pe={name:"lineEnding",tokenize:qo};function qo(n,t){return e;function e(i){return n.enter("lineEnding"),n.consume(i),n.exit("lineEnding"),D(n,t,"linePrefix")}}const de={name:"thematicBreak",tokenize:jo};function jo(n,t,e){let i=0,r;return o;function o(u){return n.enter("thematicBreak"),a(u)}function a(u){return r=u,s(u)}function s(u){return u===r?(n.enter("thematicBreakSequence"),l(u)):i>=3&&(u===null||A(u))?(n.exit("thematicBreak"),t(u)):e(u)}function l(u){return u===r?(n.consume(u),i++,l):(n.exit("thematicBreakSequence"),N(u)?D(n,s,"whitespace")(u):s(u))}}const nn={continuation:{tokenize:Ho},exit:Xo,name:"list",tokenize:Wo},Bo={partial:!0,tokenize:Go},Vo={partial:!0,tokenize:Uo};function Wo(n,t,e){const i=this,r=i.events[i.events.length-1];let o=r&&r[1].type==="linePrefix"?r[2].sliceSerialize(r[1],!0).length:0,a=0;return s;function s(f){const g=i.containerState.type||(f===42||f===43||f===45?"listUnordered":"listOrdered");if(g==="listUnordered"?!i.containerState.marker||f===i.containerState.marker:We(f)){if(i.containerState.type||(i.containerState.type=g,n.enter(g,{_container:!0})),g==="listUnordered")return n.enter("listItemPrefix"),f===42||f===45?n.check(de,e,u)(f):u(f);if(!i.interrupt||f===49)return n.enter("listItemPrefix"),n.enter("listItemValue"),l(f)}return e(f)}function l(f){return We(f)&&++a<10?(n.consume(f),l):(!i.interrupt||a<2)&&(i.containerState.marker?f===i.containerState.marker:f===41||f===46)?(n.exit("listItemValue"),u(f)):e(f)}function u(f){return n.enter("listItemMarker"),n.consume(f),n.exit("listItemMarker"),i.containerState.marker=i.containerState.marker||f,n.check(Zn,i.interrupt?e:d,n.attempt(Bo,m,c))}function d(f){return i.containerState.initialBlankLine=!0,o++,m(f)}function c(f){return N(f)?(n.enter("listItemPrefixWhitespace"),n.consume(f),n.exit("listItemPrefixWhitespace"),m):e(f)}function m(f){return i.containerState.size=o+i.sliceSerialize(n.exit("listItemPrefix"),!0).length,t(f)}}function Ho(n,t,e){const i=this;return i.containerState._closeFlow=void 0,n.check(Zn,r,o);function r(s){return i.containerState.furtherBlankLines=i.containerState.furtherBlankLines||i.containerState.initialBlankLine,D(n,t,"listItemIndent",i.containerState.size+1)(s)}function o(s){return i.containerState.furtherBlankLines||!N(s)?(i.containerState.furtherBlankLines=void 0,i.containerState.initialBlankLine=void 0,a(s)):(i.containerState.furtherBlankLines=void 0,i.containerState.initialBlankLine=void 0,n.attempt(Vo,t,a)(s))}function a(s){return i.containerState._closeFlow=!0,i.interrupt=void 0,D(n,n.attempt(nn,t,e),"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s)}}function Uo(n,t,e){const i=this;return D(n,r,"listItemIndent",i.containerState.size+1);function r(o){const a=i.events[i.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===i.containerState.size?t(o):e(o)}}function Xo(n){n.exit(this.containerState.type)}function Go(n,t,e){const i=this;return D(n,r,"listItemPrefixWhitespace",i.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function r(o){const a=i.events[i.events.length-1];return!N(o)&&a&&a[1].type==="listItemPrefixWhitespace"?t(o):e(o)}}const Vt={name:"setextUnderline",resolveTo:Qo,tokenize:Yo};function Qo(n,t){let e=n.length,i,r,o;for(;e--;)if(n[e][0]==="enter"){if(n[e][1].type==="content"){i=e;break}n[e][1].type==="paragraph"&&(r=e)}else n[e][1].type==="content"&&n.splice(e,1),!o&&n[e][1].type==="definition"&&(o=e);const a={type:"setextHeading",start:{...n[i][1].start},end:{...n[n.length-1][1].end}};return n[r][1].type="setextHeadingText",o?(n.splice(r,0,["enter",a,t]),n.splice(o+1,0,["exit",n[i][1],t]),n[i][1].end={...n[o][1].end}):n[i][1]=a,n.push(["exit",a,t]),n}function Yo(n,t,e){const i=this;let r;return o;function o(u){let d=i.events.length,c;for(;d--;)if(i.events[d][1].type!=="lineEnding"&&i.events[d][1].type!=="linePrefix"&&i.events[d][1].type!=="content"){c=i.events[d][1].type==="paragraph";break}return!i.parser.lazy[i.now().line]&&(i.interrupt||c)?(n.enter("setextHeadingLine"),r=u,a(u)):e(u)}function a(u){return n.enter("setextHeadingLineSequence"),s(u)}function s(u){return u===r?(n.consume(u),s):(n.exit("setextHeadingLineSequence"),N(u)?D(n,l,"lineSuffix")(u):l(u))}function l(u){return u===null||A(u)?(n.exit("setextHeadingLine"),t(u)):e(u)}}const Ko={tokenize:Jo};function Jo(n){const t=this,e=n.attempt(Zn,i,n.attempt(this.parser.constructs.flowInitial,r,D(n,n.attempt(this.parser.constructs.flow,r,n.attempt(to,r)),"linePrefix")));return e;function i(o){if(o===null){n.consume(o);return}return n.enter("lineEndingBlank"),n.consume(o),n.exit("lineEndingBlank"),t.currentConstruct=void 0,e}function r(o){if(o===null){n.consume(o);return}return n.enter("lineEnding"),n.consume(o),n.exit("lineEnding"),t.currentConstruct=void 0,e}}const Zo={resolveAll:Di()},ns=Fi("string"),es=Fi("text");function Fi(n){return{resolveAll:Di(n==="text"?ts:void 0),tokenize:t};function t(e){const i=this,r=this.parser.constructs[n],o=e.attempt(r,a,s);return a;function a(d){return u(d)?o(d):s(d)}function s(d){if(d===null){e.consume(d);return}return e.enter("data"),e.consume(d),l}function l(d){return u(d)?(e.exit("data"),o(d)):(e.consume(d),l)}function u(d){if(d===null)return!0;const c=r[d];let m=-1;if(c)for(;++m<c.length;){const f=c[m];if(!f.previous||f.previous.call(i,i.previous))return!0}return!1}}}function Di(n){return t;function t(e,i){let r=-1,o;for(;++r<=e.length;)o===void 0?e[r]&&e[r][1].type==="data"&&(o=r,r++):(!e[r]||e[r][1].type!=="data")&&(r!==o+2&&(e[o][1].end=e[r-1][1].end,e.splice(o+2,r-o-2),r=o+2),o=void 0);return n?n(e,i):e}}function ts(n,t){let e=0;for(;++e<=n.length;)if((e===n.length||n[e][1].type==="lineEnding")&&n[e-1][1].type==="data"){const i=n[e-1][1],r=t.sliceStream(i);let o=r.length,a=-1,s=0,l;for(;o--;){const u=r[o];if(typeof u=="string"){for(a=u.length;u.charCodeAt(a-1)===32;)s++,a--;if(a)break;a=-1}else if(u===-2)l=!0,s++;else if(u!==-1){o++;break}}if(t._contentTypeTextTrailing&&e===n.length&&(s=0),s){const u={type:e===n.length||l||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...u.start},i.start.offset===i.end.offset?Object.assign(i,u):(n.splice(e,0,["enter",u,t],["exit",u,t]),e+=2)}e++}return n}const is={42:nn,43:nn,45:nn,48:nn,49:nn,50:nn,51:nn,52:nn,53:nn,54:nn,55:nn,56:nn,57:nn,62:Ti},rs={91:so},as={[-2]:Ee,[-1]:Ee,32:Ee},os={35:po,42:de,45:[Vt,de],60:$o,61:Vt,95:de,96:jt,126:jt},ss={38:Mi,92:Ri},ls={[-5]:Pe,[-4]:Pe,[-3]:Pe,33:Do,38:Mi,42:He,60:[Na,wo],91:Lo,92:[fo,Ri],93:it,95:He,96:Ya},us={null:[He,Zo]},cs={null:[42,95]},ds={null:[]},fs=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:cs,contentInitial:rs,disable:ds,document:is,flow:os,flowInitial:as,insideSpan:us,string:ss,text:ls},Symbol.toStringTag,{value:"Module"}));function ms(n,t,e){let i={_bufferIndex:-1,_index:0,line:e&&e.line||1,column:e&&e.column||1,offset:e&&e.offset||0};const r={},o=[];let a=[],s=[];const l={attempt:L(P),check:L(S),consume:x,enter:k,exit:E,interrupt:L(S,{interrupt:!0})},u={code:null,containerState:{},defineSkip:y,events:[],now:g,parser:n,previous:null,sliceSerialize:m,sliceStream:f,write:c};let d=t.tokenize.call(u,l);return t.resolveAll&&o.push(t),u;function c(T){return a=sn(a,T),w(),a[a.length-1]!==null?[]:(j(t,0),u.events=$e(o,u.events,u),u.events)}function m(T,R){return hs(f(T),R)}function f(T){return ps(a,T)}function g(){const{_bufferIndex:T,_index:R,line:B,column:U,offset:q}=i;return{_bufferIndex:T,_index:R,line:B,column:U,offset:q}}function y(T){r[T.line]=T.column,$()}function w(){let T;for(;i._index<a.length;){const R=a[i._index];if(typeof R=="string")for(T=i._index,i._bufferIndex<0&&(i._bufferIndex=0);i._index===T&&i._bufferIndex<R.length;)b(R.charCodeAt(i._bufferIndex));else b(R)}}function b(T){d=d(T)}function x(T){A(T)?(i.line++,i.column=1,i.offset+=T===-3?2:1,$()):T!==-1&&(i.column++,i.offset++),i._bufferIndex<0?i._index++:(i._bufferIndex++,i._bufferIndex===a[i._index].length&&(i._bufferIndex=-1,i._index++)),u.previous=T}function k(T,R){const B=R||{};return B.type=T,B.start=g(),u.events.push(["enter",B,u]),s.push(B),B}function E(T){const R=s.pop();return R.end=g(),u.events.push(["exit",R,u]),R}function P(T,R){j(T,R.from)}function S(T,R){R.restore()}function L(T,R){return B;function B(U,q,K){let Q,ln,hn,p;return Array.isArray(U)?gn(U):"tokenize"in U?gn([U]):un(U);function un(Y){return qn;function qn(xn){const Mn=xn!==null&&Y[xn],En=xn!==null&&Y.null,te=[...Array.isArray(Mn)?Mn:Mn?[Mn]:[],...Array.isArray(En)?En:En?[En]:[]];return gn(te)(xn)}}function gn(Y){return Q=Y,ln=0,Y.length===0?K:h(Y[ln])}function h(Y){return qn;function qn(xn){return p=I(),hn=Y,Y.partial||(u.currentConstruct=Y),Y.name&&u.parser.constructs.disable.null.includes(Y.name)?vn():Y.tokenize.call(R?Object.assign(Object.create(u),R):u,l,cn,vn)(xn)}}function cn(Y){return T(hn,p),q}function vn(Y){return p.restore(),++ln<Q.length?h(Q[ln]):K}}}function j(T,R){T.resolveAll&&!o.includes(T)&&o.push(T),T.resolve&&on(u.events,R,u.events.length-R,T.resolve(u.events.slice(R),u)),T.resolveTo&&(u.events=T.resolveTo(u.events,u))}function I(){const T=g(),R=u.previous,B=u.currentConstruct,U=u.events.length,q=Array.from(s);return{from:U,restore:K};function K(){i=T,u.previous=R,u.currentConstruct=B,u.events.length=U,s=q,$()}}function $(){i.line in r&&i.column<2&&(i.column=r[i.line],i.offset+=r[i.line]-1)}}function ps(n,t){const e=t.start._index,i=t.start._bufferIndex,r=t.end._index,o=t.end._bufferIndex;let a;if(e===r)a=[n[e].slice(i,o)];else{if(a=n.slice(e,r),i>-1){const s=a[0];typeof s=="string"?a[0]=s.slice(i):a.shift()}o>0&&a.push(n[r].slice(0,o))}return a}function hs(n,t){let e=-1;const i=[];let r;for(;++e<n.length;){const o=n[e];let a;if(typeof o=="string")a=o;else switch(o){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=t?" ":"	";break}case-1:{if(!t&&r)continue;a=" ";break}default:a=String.fromCharCode(o)}r=o===-2,i.push(a)}return i.join("")}function gs(n){const i={constructs:Ci([fs,...(n||{}).extensions||[]]),content:r(Ma),defined:[],document:r(Pa),flow:r(Ko),lazy:{},string:r(ns),text:r(es)};return i;function r(o){return a;function a(s){return ms(i,o,s)}}}function bs(n){for(;!Ei(n););return n}const Wt=/[\0\t\n\r]/g;function $s(){let n=1,t="",e=!0,i;return r;function r(o,a,s){const l=[];let u,d,c,m,f;for(o=t+(typeof o=="string"?o.toString():new TextDecoder(a||void 0).decode(o)),c=0,t="",e&&(o.charCodeAt(0)===65279&&c++,e=void 0);c<o.length;){if(Wt.lastIndex=c,u=Wt.exec(o),m=u&&u.index!==void 0?u.index:o.length,f=o.charCodeAt(m),!u){t=o.slice(c);break}if(f===10&&c===m&&i)l.push(-3),i=void 0;else switch(i&&(l.push(-5),i=void 0),c<m&&(l.push(o.slice(c,m)),n+=m-c),f){case 0:{l.push(65533),n++;break}case 9:{for(d=Math.ceil(n/4)*4,l.push(-2);n++<d;)l.push(-1);break}case 10:{l.push(-4),n=1;break}default:i=!0,n=1}c=m+1}return s&&(i&&l.push(-5),t&&l.push(t),l.push(null)),l}}const ys=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function xs(n){return n.replace(ys,_s)}function _s(n,t,e){if(t)return t;if(e.charCodeAt(0)===35){const r=e.charCodeAt(1),o=r===120||r===88;return Ai(e.slice(o?2:1),o?16:10)}return tt(e)||n}const Ni={}.hasOwnProperty;function Ss(n,t,e){return typeof t!="string"&&(e=t,t=void 0),vs(e)(bs(gs(e).document().write($s()(n,t,!0))))}function vs(n){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:o(xt),autolinkProtocol:I,autolinkEmail:I,atxHeading:o(bt),blockQuote:o(En),characterEscape:I,characterReference:I,codeFenced:o(te),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:o(te,a),codeText:o(jr,a),codeTextData:I,data:I,codeFlowValue:I,definition:o(Br),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:o(Vr),hardBreakEscape:o($t),hardBreakTrailing:o($t),htmlFlow:o(yt,a),htmlFlowData:I,htmlText:o(yt,a),htmlTextData:I,image:o(Wr),label:a,link:o(xt),listItem:o(Hr),listItemValue:m,listOrdered:o(_t,c),listUnordered:o(_t),paragraph:o(Ur),reference:h,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:o(bt),strong:o(Xr),thematicBreak:o(Qr)},exit:{atxHeading:l(),atxHeadingSequence:P,autolink:l(),autolinkEmail:Mn,autolinkProtocol:xn,blockQuote:l(),characterEscapeValue:$,characterReferenceMarkerHexadecimal:vn,characterReferenceMarkerNumeric:vn,characterReferenceValue:Y,characterReference:qn,codeFenced:l(w),codeFencedFence:y,codeFencedFenceInfo:f,codeFencedFenceMeta:g,codeFlowValue:$,codeIndented:l(b),codeText:l(q),codeTextData:$,data:$,definition:l(),definitionDestinationString:E,definitionLabelString:x,definitionTitleString:k,emphasis:l(),hardBreakEscape:l(R),hardBreakTrailing:l(R),htmlFlow:l(B),htmlFlowData:$,htmlText:l(U),htmlTextData:$,image:l(Q),label:hn,labelText:ln,lineEnding:T,link:l(K),listItem:l(),listOrdered:l(),listUnordered:l(),paragraph:l(),referenceString:cn,resourceDestinationString:p,resourceTitleString:un,resource:gn,setextHeading:l(j),setextHeadingLineSequence:L,setextHeadingText:S,strong:l(),thematicBreak:l()}};Li(t,(n||{}).mdastExtensions||[]);const e={};return i;function i(_){let C={type:"root",children:[]};const M={stack:[C],tokenStack:[],config:t,enter:s,exit:u,buffer:a,resume:d,data:e},O=[];let V=-1;for(;++V<_.length;)if(_[V][1].type==="listOrdered"||_[V][1].type==="listUnordered")if(_[V][0]==="enter")O.push(V);else{const dn=O.pop();V=r(_,dn,V)}for(V=-1;++V<_.length;){const dn=t[_[V][0]];Ni.call(dn,_[V][1].type)&&dn[_[V][1].type].call(Object.assign({sliceSerialize:_[V][2].sliceSerialize},M),_[V][1])}if(M.tokenStack.length>0){const dn=M.tokenStack[M.tokenStack.length-1];(dn[1]||Ht).call(M,void 0,dn[0])}for(C.position={start:_n(_.length>0?_[0][1].start:{line:1,column:1,offset:0}),end:_n(_.length>0?_[_.length-2][1].end:{line:1,column:1,offset:0})},V=-1;++V<t.transforms.length;)C=t.transforms[V](C)||C;return C}function r(_,C,M){let O=C-1,V=-1,dn=!1,kn,bn,jn,Bn;for(;++O<=M;){const rn=_[O];switch(rn[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{rn[0]==="enter"?V++:V--,Bn=void 0;break}case"lineEndingBlank":{rn[0]==="enter"&&(kn&&!Bn&&!V&&!jn&&(jn=O),Bn=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Bn=void 0}if(!V&&rn[0]==="enter"&&rn[1].type==="listItemPrefix"||V===-1&&rn[0]==="exit"&&(rn[1].type==="listUnordered"||rn[1].type==="listOrdered")){if(kn){let Pn=O;for(bn=void 0;Pn--;){const $n=_[Pn];if($n[1].type==="lineEnding"||$n[1].type==="lineEndingBlank"){if($n[0]==="exit")continue;bn&&(_[bn][1].type="lineEndingBlank",dn=!0),$n[1].type="lineEnding",bn=Pn}else if(!($n[1].type==="linePrefix"||$n[1].type==="blockQuotePrefix"||$n[1].type==="blockQuotePrefixWhitespace"||$n[1].type==="blockQuoteMarker"||$n[1].type==="listItemIndent"))break}jn&&(!bn||jn<bn)&&(kn._spread=!0),kn.end=Object.assign({},bn?_[bn][1].start:rn[1].end),_.splice(bn||O,0,["exit",kn,rn[2]]),O++,M++}if(rn[1].type==="listItemPrefix"){const Pn={type:"listItem",_spread:!1,start:Object.assign({},rn[1].start),end:void 0};kn=Pn,_.splice(O,0,["enter",Pn,rn[2]]),O++,M++,jn=void 0,Bn=!0}}}return _[C][1]._spread=dn,M}function o(_,C){return M;function M(O){s.call(this,_(O),O),C&&C.call(this,O)}}function a(){this.stack.push({type:"fragment",children:[]})}function s(_,C,M){this.stack[this.stack.length-1].children.push(_),this.stack.push(_),this.tokenStack.push([C,M||void 0]),_.position={start:_n(C.start),end:void 0}}function l(_){return C;function C(M){_&&_.call(this,M),u.call(this,M)}}function u(_,C){const M=this.stack.pop(),O=this.tokenStack.pop();if(O)O[0].type!==_.type&&(C?C.call(this,_,O[0]):(O[1]||Ht).call(this,_,O[0]));else throw new Error("Cannot close `"+_.type+"` ("+Xn({start:_.start,end:_.end})+"): it’s not open");M.position.end=_n(_.end)}function d(){return et(this.stack.pop())}function c(){this.data.expectingFirstListItemValue=!0}function m(_){if(this.data.expectingFirstListItemValue){const C=this.stack[this.stack.length-2];C.start=Number.parseInt(this.sliceSerialize(_),10),this.data.expectingFirstListItemValue=void 0}}function f(){const _=this.resume(),C=this.stack[this.stack.length-1];C.lang=_}function g(){const _=this.resume(),C=this.stack[this.stack.length-1];C.meta=_}function y(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function w(){const _=this.resume(),C=this.stack[this.stack.length-1];C.value=_.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function b(){const _=this.resume(),C=this.stack[this.stack.length-1];C.value=_.replace(/(\r?\n|\r)$/g,"")}function x(_){const C=this.resume(),M=this.stack[this.stack.length-1];M.label=C,M.identifier=fn(this.sliceSerialize(_)).toLowerCase()}function k(){const _=this.resume(),C=this.stack[this.stack.length-1];C.title=_}function E(){const _=this.resume(),C=this.stack[this.stack.length-1];C.url=_}function P(_){const C=this.stack[this.stack.length-1];if(!C.depth){const M=this.sliceSerialize(_).length;C.depth=M}}function S(){this.data.setextHeadingSlurpLineEnding=!0}function L(_){const C=this.stack[this.stack.length-1];C.depth=this.sliceSerialize(_).codePointAt(0)===61?1:2}function j(){this.data.setextHeadingSlurpLineEnding=void 0}function I(_){const M=this.stack[this.stack.length-1].children;let O=M[M.length-1];(!O||O.type!=="text")&&(O=Gr(),O.position={start:_n(_.start),end:void 0},M.push(O)),this.stack.push(O)}function $(_){const C=this.stack.pop();C.value+=this.sliceSerialize(_),C.position.end=_n(_.end)}function T(_){const C=this.stack[this.stack.length-1];if(this.data.atHardBreak){const M=C.children[C.children.length-1];M.position.end=_n(_.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(C.type)&&(I.call(this,_),$.call(this,_))}function R(){this.data.atHardBreak=!0}function B(){const _=this.resume(),C=this.stack[this.stack.length-1];C.value=_}function U(){const _=this.resume(),C=this.stack[this.stack.length-1];C.value=_}function q(){const _=this.resume(),C=this.stack[this.stack.length-1];C.value=_}function K(){const _=this.stack[this.stack.length-1];if(this.data.inReference){const C=this.data.referenceType||"shortcut";_.type+="Reference",_.referenceType=C,delete _.url,delete _.title}else delete _.identifier,delete _.label;this.data.referenceType=void 0}function Q(){const _=this.stack[this.stack.length-1];if(this.data.inReference){const C=this.data.referenceType||"shortcut";_.type+="Reference",_.referenceType=C,delete _.url,delete _.title}else delete _.identifier,delete _.label;this.data.referenceType=void 0}function ln(_){const C=this.sliceSerialize(_),M=this.stack[this.stack.length-2];M.label=xs(C),M.identifier=fn(C).toLowerCase()}function hn(){const _=this.stack[this.stack.length-1],C=this.resume(),M=this.stack[this.stack.length-1];if(this.data.inReference=!0,M.type==="link"){const O=_.children;M.children=O}else M.alt=C}function p(){const _=this.resume(),C=this.stack[this.stack.length-1];C.url=_}function un(){const _=this.resume(),C=this.stack[this.stack.length-1];C.title=_}function gn(){this.data.inReference=void 0}function h(){this.data.referenceType="collapsed"}function cn(_){const C=this.resume(),M=this.stack[this.stack.length-1];M.label=C,M.identifier=fn(this.sliceSerialize(_)).toLowerCase(),this.data.referenceType="full"}function vn(_){this.data.characterReferenceType=_.type}function Y(_){const C=this.sliceSerialize(_),M=this.data.characterReferenceType;let O;M?(O=Ai(C,M==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):O=tt(C);const V=this.stack[this.stack.length-1];V.value+=O}function qn(_){const C=this.stack.pop();C.position.end=_n(_.end)}function xn(_){$.call(this,_);const C=this.stack[this.stack.length-1];C.url=this.sliceSerialize(_)}function Mn(_){$.call(this,_);const C=this.stack[this.stack.length-1];C.url="mailto:"+this.sliceSerialize(_)}function En(){return{type:"blockquote",children:[]}}function te(){return{type:"code",lang:null,meta:null,value:""}}function jr(){return{type:"inlineCode",value:""}}function Br(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Vr(){return{type:"emphasis",children:[]}}function bt(){return{type:"heading",depth:0,children:[]}}function $t(){return{type:"break"}}function yt(){return{type:"html",value:""}}function Wr(){return{type:"image",title:null,url:"",alt:null}}function xt(){return{type:"link",title:null,url:"",children:[]}}function _t(_){return{type:"list",ordered:_.type==="listOrdered",start:null,spread:_._spread,children:[]}}function Hr(_){return{type:"listItem",spread:_._spread,checked:null,children:[]}}function Ur(){return{type:"paragraph",children:[]}}function Xr(){return{type:"strong",children:[]}}function Gr(){return{type:"text",value:""}}function Qr(){return{type:"thematicBreak"}}}function _n(n){return{line:n.line,column:n.column,offset:n.offset}}function Li(n,t){let e=-1;for(;++e<t.length;){const i=t[e];Array.isArray(i)?Li(n,i):ks(n,i)}}function ks(n,t){let e;for(e in t)if(Ni.call(t,e))switch(e){case"canContainEols":{const i=t[e];i&&n[e].push(...i);break}case"transforms":{const i=t[e];i&&n[e].push(...i);break}case"enter":case"exit":{const i=t[e];i&&Object.assign(n[e],i);break}}}function Ht(n,t){throw n?new Error("Cannot close `"+n.type+"` ("+Xn({start:n.start,end:n.end})+"): a different token (`"+t.type+"`, "+Xn({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Xn({start:t.start,end:t.end})+") is still open")}function ws(n){const t=this;t.parser=e;function e(i){return Ss(i,{...t.data("settings"),...n,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function me(n,t){const e=String(n);if(typeof t!="string")throw new TypeError("Expected character");let i=0,r=e.indexOf(t);for(;r!==-1;)i++,r=e.indexOf(t,r+t.length);return i}function Cs(n){if(typeof n!="string")throw new TypeError("Expected a string");return n.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}const ne=function(n){if(n==null)return Ms;if(typeof n=="function")return ye(n);if(typeof n=="object")return Array.isArray(n)?As(n):Ts(n);if(typeof n=="string")return Rs(n);throw new Error("Expected function, string, or object as test")};function As(n){const t=[];let e=-1;for(;++e<n.length;)t[e]=ne(n[e]);return ye(i);function i(...r){let o=-1;for(;++o<t.length;)if(t[o].apply(this,r))return!0;return!1}}function Ts(n){const t=n;return ye(e);function e(i){const r=i;let o;for(o in n)if(r[o]!==t[o])return!1;return!0}}function Rs(n){return ye(t);function t(e){return e&&e.type===n}}function ye(n){return t;function t(e,i,r){return!!(Es(e)&&n.call(this,e,typeof i=="number"?i:void 0,r||void 0))}}function Ms(){return!0}function Es(n){return n!==null&&typeof n=="object"&&"type"in n}const Oi=[],Ps=!0,Ue=!1,qi="skip";function rt(n,t,e,i){let r;typeof t=="function"&&typeof e!="function"?(i=e,e=t):r=t;const o=ne(r),a=i?-1:1;s(n,void 0,[])();function s(l,u,d){const c=l&&typeof l=="object"?l:{};if(typeof c.type=="string"){const f=typeof c.tagName=="string"?c.tagName:typeof c.name=="string"?c.name:void 0;Object.defineProperty(m,"name",{value:"node ("+(l.type+(f?"<"+f+">":""))+")"})}return m;function m(){let f=Oi,g,y,w;if((!t||o(l,u,d[d.length-1]||void 0))&&(f=Is(e(l,d)),f[0]===Ue))return f;if("children"in l&&l.children){const b=l;if(b.children&&f[0]!==qi)for(y=(i?b.children.length:-1)+a,w=d.concat(b);y>-1&&y<b.children.length;){const x=b.children[y];if(g=s(x,y,w)(),g[0]===Ue)return g;y=typeof g[1]=="number"?g[1]:y+a}}return f}}}function Is(n){return Array.isArray(n)?n:typeof n=="number"?[Ps,n]:n==null?Oi:[n]}function zs(n,t,e){const r=ne((e||{}).ignore||[]),o=Fs(t);let a=-1;for(;++a<o.length;)rt(n,"text",s);function s(u,d){let c=-1,m;for(;++c<d.length;){const f=d[c],g=m?m.children:void 0;if(r(f,g?g.indexOf(f):void 0,m))return;m=f}if(m)return l(u,d)}function l(u,d){const c=d[d.length-1],m=o[a][0],f=o[a][1];let g=0;const w=c.children.indexOf(u);let b=!1,x=[];m.lastIndex=0;let k=m.exec(u.value);for(;k;){const E=k.index,P={index:k.index,input:k.input,stack:[...d,u]};let S=f(...k,P);if(typeof S=="string"&&(S=S.length>0?{type:"text",value:S}:void 0),S===!1?m.lastIndex=E+1:(g!==E&&x.push({type:"text",value:u.value.slice(g,E)}),Array.isArray(S)?x.push(...S):S&&x.push(S),g=E+k[0].length,b=!0),!m.global)break;k=m.exec(u.value)}return b?(g<u.value.length&&x.push({type:"text",value:u.value.slice(g)}),c.children.splice(w,1,...x)):x=[u],w+x.length}}function Fs(n){const t=[];if(!Array.isArray(n))throw new TypeError("Expected find and replace tuple or list of tuples");const e=!n[0]||Array.isArray(n[0])?n:[n];let i=-1;for(;++i<e.length;){const r=e[i];t.push([Ds(r[0]),Ns(r[1])])}return t}function Ds(n){return typeof n=="string"?new RegExp(Cs(n),"g"):n}function Ns(n){return typeof n=="function"?n:function(){return n}}const Ie="phrasing",ze=["autolink","link","image","label"];function Ls(){return{transforms:[Hs],enter:{literalAutolink:qs,literalAutolinkEmail:Fe,literalAutolinkHttp:Fe,literalAutolinkWww:Fe},exit:{literalAutolink:Ws,literalAutolinkEmail:Vs,literalAutolinkHttp:js,literalAutolinkWww:Bs}}}function Os(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:Ie,notInConstruct:ze},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:Ie,notInConstruct:ze},{character:":",before:"[ps]",after:"\\/",inConstruct:Ie,notInConstruct:ze}]}}function qs(n){this.enter({type:"link",title:null,url:"",children:[]},n)}function Fe(n){this.config.enter.autolinkProtocol.call(this,n)}function js(n){this.config.exit.autolinkProtocol.call(this,n)}function Bs(n){this.config.exit.data.call(this,n);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(n)}function Vs(n){this.config.exit.autolinkEmail.call(this,n)}function Ws(n){this.exit(n)}function Hs(n){zs(n,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,Us],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),Xs]],{ignore:["link","linkReference"]})}function Us(n,t,e,i,r){let o="";if(!ji(r)||(/^w/i.test(t)&&(e=t+e,t="",o="http://"),!Gs(e)))return!1;const a=Qs(e+i);if(!a[0])return!1;const s={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[s,{type:"text",value:a[1]}]:s}function Xs(n,t,e,i){return!ji(i,!0)||/[-\d_]$/.test(e)?!1:{type:"link",title:null,url:"mailto:"+t+"@"+e,children:[{type:"text",value:t+"@"+e}]}}function Gs(n){const t=n.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}function Qs(n){const t=/[!"&'),.:;<>?\]}]+$/.exec(n);if(!t)return[n,void 0];n=n.slice(0,t.index);let e=t[0],i=e.indexOf(")");const r=me(n,"(");let o=me(n,")");for(;i!==-1&&r>o;)n+=e.slice(0,i+1),e=e.slice(i+1),i=e.indexOf(")"),o++;return[n,e]}function ji(n,t){const e=n.input.charCodeAt(n.index-1);return(n.index===0||An(e)||be(e))&&(!t||e!==47)}Bi.peek=rl;function Ys(){this.buffer()}function Ks(n){this.enter({type:"footnoteReference",identifier:"",label:""},n)}function Js(){this.buffer()}function Zs(n){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},n)}function nl(n){const t=this.resume(),e=this.stack[this.stack.length-1];e.type,e.identifier=fn(this.sliceSerialize(n)).toLowerCase(),e.label=t}function el(n){this.exit(n)}function tl(n){const t=this.resume(),e=this.stack[this.stack.length-1];e.type,e.identifier=fn(this.sliceSerialize(n)).toLowerCase(),e.label=t}function il(n){this.exit(n)}function rl(){return"["}function Bi(n,t,e,i){const r=e.createTracker(i);let o=r.move("[^");const a=e.enter("footnoteReference"),s=e.enter("reference");return o+=r.move(e.safe(e.associationId(n),{after:"]",before:o})),s(),a(),o+=r.move("]"),o}function al(){return{enter:{gfmFootnoteCallString:Ys,gfmFootnoteCall:Ks,gfmFootnoteDefinitionLabelString:Js,gfmFootnoteDefinition:Zs},exit:{gfmFootnoteCallString:nl,gfmFootnoteCall:el,gfmFootnoteDefinitionLabelString:tl,gfmFootnoteDefinition:il}}}function ol(n){let t=!1;return n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:e,footnoteReference:Bi},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function e(i,r,o,a){const s=o.createTracker(a);let l=s.move("[^");const u=o.enter("footnoteDefinition"),d=o.enter("label");return l+=s.move(o.safe(o.associationId(i),{before:l,after:"]"})),d(),l+=s.move("]:"),i.children&&i.children.length>0&&(s.shift(4),l+=s.move((t?`
`:" ")+o.indentLines(o.containerFlow(i,s.current()),t?Vi:sl))),u(),l}}function sl(n,t,e){return t===0?n:Vi(n,t,e)}function Vi(n,t,e){return(e?"":"    ")+n}const ll=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];Wi.peek=ml;function ul(){return{canContainEols:["delete"],enter:{strikethrough:dl},exit:{strikethrough:fl}}}function cl(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:ll}],handlers:{delete:Wi}}}function dl(n){this.enter({type:"delete",children:[]},n)}function fl(n){this.exit(n)}function Wi(n,t,e,i){const r=e.createTracker(i),o=e.enter("strikethrough");let a=r.move("~~");return a+=e.containerPhrasing(n,{...r.current(),before:a,after:"~"}),a+=r.move("~~"),o(),a}function ml(){return"~"}function pl(n){return n.length}function hl(n,t){const e=t||{},i=(e.align||[]).concat(),r=e.stringLength||pl,o=[],a=[],s=[],l=[];let u=0,d=-1;for(;++d<n.length;){const y=[],w=[];let b=-1;for(n[d].length>u&&(u=n[d].length);++b<n[d].length;){const x=gl(n[d][b]);if(e.alignDelimiters!==!1){const k=r(x);w[b]=k,(l[b]===void 0||k>l[b])&&(l[b]=k)}y.push(x)}a[d]=y,s[d]=w}let c=-1;if(typeof i=="object"&&"length"in i)for(;++c<u;)o[c]=Ut(i[c]);else{const y=Ut(i);for(;++c<u;)o[c]=y}c=-1;const m=[],f=[];for(;++c<u;){const y=o[c];let w="",b="";y===99?(w=":",b=":"):y===108?w=":":y===114&&(b=":");let x=e.alignDelimiters===!1?1:Math.max(1,l[c]-w.length-b.length);const k=w+"-".repeat(x)+b;e.alignDelimiters!==!1&&(x=w.length+x+b.length,x>l[c]&&(l[c]=x),f[c]=x),m[c]=k}a.splice(1,0,m),s.splice(1,0,f),d=-1;const g=[];for(;++d<a.length;){const y=a[d],w=s[d];c=-1;const b=[];for(;++c<u;){const x=y[c]||"";let k="",E="";if(e.alignDelimiters!==!1){const P=l[c]-(w[c]||0),S=o[c];S===114?k=" ".repeat(P):S===99?P%2?(k=" ".repeat(P/2+.5),E=" ".repeat(P/2-.5)):(k=" ".repeat(P/2),E=k):E=" ".repeat(P)}e.delimiterStart!==!1&&!c&&b.push("|"),e.padding!==!1&&!(e.alignDelimiters===!1&&x==="")&&(e.delimiterStart!==!1||c)&&b.push(" "),e.alignDelimiters!==!1&&b.push(k),b.push(x),e.alignDelimiters!==!1&&b.push(E),e.padding!==!1&&b.push(" "),(e.delimiterEnd!==!1||c!==u-1)&&b.push("|")}g.push(e.delimiterEnd===!1?b.join("").replace(/ +$/,""):b.join(""))}return g.join(`
`)}function gl(n){return n==null?"":String(n)}function Ut(n){const t=typeof n=="string"?n.codePointAt(0):0;return t===67||t===99?99:t===76||t===108?108:t===82||t===114?114:0}const Xt={}.hasOwnProperty;function bl(n,t){const e=t||{};function i(r,...o){let a=i.invalid;const s=i.handlers;if(r&&Xt.call(r,n)){const l=String(r[n]);a=Xt.call(s,l)?s[l]:i.unknown}if(a)return a.call(this,r,...o)}return i.handlers=e.handlers||{},i.invalid=e.invalid,i.unknown=e.unknown,i}function $l(n,t,e,i){const r=e.enter("blockquote"),o=e.createTracker(i);o.move("> "),o.shift(2);const a=e.indentLines(e.containerFlow(n,o.current()),yl);return r(),a}function yl(n,t,e){return">"+(e?"":" ")+n}function xl(n,t){return Gt(n,t.inConstruct,!0)&&!Gt(n,t.notInConstruct,!1)}function Gt(n,t,e){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return e;let i=-1;for(;++i<t.length;)if(n.includes(t[i]))return!0;return!1}function Qt(n,t,e,i){let r=-1;for(;++r<e.unsafe.length;)if(e.unsafe[r].character===`
`&&xl(e.stack,e.unsafe[r]))return/[ \t]/.test(i.before)?"":" ";return`\\
`}function Hi(n,t){const e=String(n);let i=e.indexOf(t),r=i,o=0,a=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;i!==-1;)i===r?++o>a&&(a=o):o=1,r=i+t.length,i=e.indexOf(t,r);return a}function _l(n,t){return!!(t.options.fences===!1&&n.value&&!n.lang&&/[^ \r\n]/.test(n.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(n.value))}function Sl(n){const t=n.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}function vl(n,t,e,i){const r=Sl(e),o=n.value||"",a=r==="`"?"GraveAccent":"Tilde";if(_l(n,e)){const c=e.enter("codeIndented"),m=e.indentLines(o,kl);return c(),m}const s=e.createTracker(i),l=r.repeat(Math.max(Hi(o,r)+1,3)),u=e.enter("codeFenced");let d=s.move(l);if(n.lang){const c=e.enter(`codeFencedLang${a}`);d+=s.move(e.safe(n.lang,{before:d,after:" ",encode:["`"],...s.current()})),c()}if(n.lang&&n.meta){const c=e.enter(`codeFencedMeta${a}`);d+=s.move(" "),d+=s.move(e.safe(n.meta,{before:d,after:`
`,encode:["`"],...s.current()})),c()}return d+=s.move(`
`),o&&(d+=s.move(o+`
`)),d+=s.move(l),u(),d}function kl(n,t,e){return(e?"":"    ")+n}function at(n){const t=n.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function wl(n,t,e,i){const r=at(e),o=r==='"'?"Quote":"Apostrophe",a=e.enter("definition");let s=e.enter("label");const l=e.createTracker(i);let u=l.move("[");return u+=l.move(e.safe(e.associationId(n),{before:u,after:"]",...l.current()})),u+=l.move("]: "),s(),!n.url||/[\0- \u007F]/.test(n.url)?(s=e.enter("destinationLiteral"),u+=l.move("<"),u+=l.move(e.safe(n.url,{before:u,after:">",...l.current()})),u+=l.move(">")):(s=e.enter("destinationRaw"),u+=l.move(e.safe(n.url,{before:u,after:n.title?" ":`
`,...l.current()}))),s(),n.title&&(s=e.enter(`title${o}`),u+=l.move(" "+r),u+=l.move(e.safe(n.title,{before:u,after:r,...l.current()})),u+=l.move(r),s()),a(),u}function Cl(n){const t=n.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}function Yn(n){return"&#x"+n.toString(16).toUpperCase()+";"}function pe(n,t,e){const i=Nn(n),r=Nn(t);return i===void 0?r===void 0?e==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:r===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:i===1?r===void 0?{inside:!1,outside:!1}:r===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:r===void 0?{inside:!1,outside:!1}:r===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}Ui.peek=Al;function Ui(n,t,e,i){const r=Cl(e),o=e.enter("emphasis"),a=e.createTracker(i),s=a.move(r);let l=a.move(e.containerPhrasing(n,{after:r,before:s,...a.current()}));const u=l.charCodeAt(0),d=pe(i.before.charCodeAt(i.before.length-1),u,r);d.inside&&(l=Yn(u)+l.slice(1));const c=l.charCodeAt(l.length-1),m=pe(i.after.charCodeAt(0),c,r);m.inside&&(l=l.slice(0,-1)+Yn(c));const f=a.move(r);return o(),e.attentionEncodeSurroundingInfo={after:m.outside,before:d.outside},s+l+f}function Al(n,t,e){return e.options.emphasis||"*"}function Xi(n,t,e,i){let r,o,a;typeof t=="function"?(o=void 0,a=t,r=e):(o=t,a=e,r=i),rt(n,o,s,r);function s(l,u){const d=u[u.length-1],c=d?d.children.indexOf(l):void 0;return a(l,c,d)}}function Tl(n,t){let e=!1;return Xi(n,function(i){if("value"in i&&/\r?\n|\r/.test(i.value)||i.type==="break")return e=!0,Ue}),!!((!n.depth||n.depth<3)&&et(n)&&(t.options.setext||e))}function Rl(n,t,e,i){const r=Math.max(Math.min(6,n.depth||1),1),o=e.createTracker(i);if(Tl(n,e)){const d=e.enter("headingSetext"),c=e.enter("phrasing"),m=e.containerPhrasing(n,{...o.current(),before:`
`,after:`
`});return c(),d(),m+`
`+(r===1?"=":"-").repeat(m.length-(Math.max(m.lastIndexOf("\r"),m.lastIndexOf(`
`))+1))}const a="#".repeat(r),s=e.enter("headingAtx"),l=e.enter("phrasing");o.move(a+" ");let u=e.containerPhrasing(n,{before:"# ",after:`
`,...o.current()});return/^[\t ]/.test(u)&&(u=Yn(u.charCodeAt(0))+u.slice(1)),u=u?a+" "+u:a,e.options.closeAtx&&(u+=" "+a),l(),s(),u}Gi.peek=Ml;function Gi(n){return n.value||""}function Ml(){return"<"}Qi.peek=El;function Qi(n,t,e,i){const r=at(e),o=r==='"'?"Quote":"Apostrophe",a=e.enter("image");let s=e.enter("label");const l=e.createTracker(i);let u=l.move("![");return u+=l.move(e.safe(n.alt,{before:u,after:"]",...l.current()})),u+=l.move("]("),s(),!n.url&&n.title||/[\0- \u007F]/.test(n.url)?(s=e.enter("destinationLiteral"),u+=l.move("<"),u+=l.move(e.safe(n.url,{before:u,after:">",...l.current()})),u+=l.move(">")):(s=e.enter("destinationRaw"),u+=l.move(e.safe(n.url,{before:u,after:n.title?" ":")",...l.current()}))),s(),n.title&&(s=e.enter(`title${o}`),u+=l.move(" "+r),u+=l.move(e.safe(n.title,{before:u,after:r,...l.current()})),u+=l.move(r),s()),u+=l.move(")"),a(),u}function El(){return"!"}Yi.peek=Pl;function Yi(n,t,e,i){const r=n.referenceType,o=e.enter("imageReference");let a=e.enter("label");const s=e.createTracker(i);let l=s.move("![");const u=e.safe(n.alt,{before:l,after:"]",...s.current()});l+=s.move(u+"]["),a();const d=e.stack;e.stack=[],a=e.enter("reference");const c=e.safe(e.associationId(n),{before:l,after:"]",...s.current()});return a(),e.stack=d,o(),r==="full"||!u||u!==c?l+=s.move(c+"]"):r==="shortcut"?l=l.slice(0,-1):l+=s.move("]"),l}function Pl(){return"!"}Ki.peek=Il;function Ki(n,t,e){let i=n.value||"",r="`",o=-1;for(;new RegExp("(^|[^`])"+r+"([^`]|$)").test(i);)r+="`";for(/[^ \r\n]/.test(i)&&(/^[ \r\n]/.test(i)&&/[ \r\n]$/.test(i)||/^`|`$/.test(i))&&(i=" "+i+" ");++o<e.unsafe.length;){const a=e.unsafe[o],s=e.compilePattern(a);let l;if(a.atBreak)for(;l=s.exec(i);){let u=l.index;i.charCodeAt(u)===10&&i.charCodeAt(u-1)===13&&u--,i=i.slice(0,u)+" "+i.slice(l.index+1)}}return r+i+r}function Il(){return"`"}function Ji(n,t){const e=et(n);return!!(!t.options.resourceLink&&n.url&&!n.title&&n.children&&n.children.length===1&&n.children[0].type==="text"&&(e===n.url||"mailto:"+e===n.url)&&/^[a-z][a-z+.-]+:/i.test(n.url)&&!/[\0- <>\u007F]/.test(n.url))}Zi.peek=zl;function Zi(n,t,e,i){const r=at(e),o=r==='"'?"Quote":"Apostrophe",a=e.createTracker(i);let s,l;if(Ji(n,e)){const d=e.stack;e.stack=[],s=e.enter("autolink");let c=a.move("<");return c+=a.move(e.containerPhrasing(n,{before:c,after:">",...a.current()})),c+=a.move(">"),s(),e.stack=d,c}s=e.enter("link"),l=e.enter("label");let u=a.move("[");return u+=a.move(e.containerPhrasing(n,{before:u,after:"](",...a.current()})),u+=a.move("]("),l(),!n.url&&n.title||/[\0- \u007F]/.test(n.url)?(l=e.enter("destinationLiteral"),u+=a.move("<"),u+=a.move(e.safe(n.url,{before:u,after:">",...a.current()})),u+=a.move(">")):(l=e.enter("destinationRaw"),u+=a.move(e.safe(n.url,{before:u,after:n.title?" ":")",...a.current()}))),l(),n.title&&(l=e.enter(`title${o}`),u+=a.move(" "+r),u+=a.move(e.safe(n.title,{before:u,after:r,...a.current()})),u+=a.move(r),l()),u+=a.move(")"),s(),u}function zl(n,t,e){return Ji(n,e)?"<":"["}nr.peek=Fl;function nr(n,t,e,i){const r=n.referenceType,o=e.enter("linkReference");let a=e.enter("label");const s=e.createTracker(i);let l=s.move("[");const u=e.containerPhrasing(n,{before:l,after:"]",...s.current()});l+=s.move(u+"]["),a();const d=e.stack;e.stack=[],a=e.enter("reference");const c=e.safe(e.associationId(n),{before:l,after:"]",...s.current()});return a(),e.stack=d,o(),r==="full"||!u||u!==c?l+=s.move(c+"]"):r==="shortcut"?l=l.slice(0,-1):l+=s.move("]"),l}function Fl(){return"["}function ot(n){const t=n.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function Dl(n){const t=ot(n),e=n.options.bulletOther;if(!e)return t==="*"?"-":"*";if(e!=="*"&&e!=="+"&&e!=="-")throw new Error("Cannot serialize items with `"+e+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(e===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+e+"`) to be different");return e}function Nl(n){const t=n.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}function er(n){const t=n.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}function Ll(n,t,e,i){const r=e.enter("list"),o=e.bulletCurrent;let a=n.ordered?Nl(e):ot(e);const s=n.ordered?a==="."?")":".":Dl(e);let l=t&&e.bulletLastUsed?a===e.bulletLastUsed:!1;if(!n.ordered){const d=n.children?n.children[0]:void 0;if((a==="*"||a==="-")&&d&&(!d.children||!d.children[0])&&e.stack[e.stack.length-1]==="list"&&e.stack[e.stack.length-2]==="listItem"&&e.stack[e.stack.length-3]==="list"&&e.stack[e.stack.length-4]==="listItem"&&e.indexStack[e.indexStack.length-1]===0&&e.indexStack[e.indexStack.length-2]===0&&e.indexStack[e.indexStack.length-3]===0&&(l=!0),er(e)===a&&d){let c=-1;for(;++c<n.children.length;){const m=n.children[c];if(m&&m.type==="listItem"&&m.children&&m.children[0]&&m.children[0].type==="thematicBreak"){l=!0;break}}}}l&&(a=s),e.bulletCurrent=a;const u=e.containerFlow(n,i);return e.bulletLastUsed=a,e.bulletCurrent=o,r(),u}function Ol(n){const t=n.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}function ql(n,t,e,i){const r=Ol(e);let o=e.bulletCurrent||ot(e);t&&t.type==="list"&&t.ordered&&(o=(typeof t.start=="number"&&t.start>-1?t.start:1)+(e.options.incrementListMarker===!1?0:t.children.indexOf(n))+o);let a=o.length+1;(r==="tab"||r==="mixed"&&(t&&t.type==="list"&&t.spread||n.spread))&&(a=Math.ceil(a/4)*4);const s=e.createTracker(i);s.move(o+" ".repeat(a-o.length)),s.shift(a);const l=e.enter("listItem"),u=e.indentLines(e.containerFlow(n,s.current()),d);return l(),u;function d(c,m,f){return m?(f?"":" ".repeat(a))+c:(f?o:o+" ".repeat(a-o.length))+c}}function jl(n,t,e,i){const r=e.enter("paragraph"),o=e.enter("phrasing"),a=e.containerPhrasing(n,i);return o(),r(),a}const Bl=ne(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function Vl(n,t,e,i){return(n.children.some(function(a){return Bl(a)})?e.containerPhrasing:e.containerFlow).call(e,n,i)}function Wl(n){const t=n.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}tr.peek=Hl;function tr(n,t,e,i){const r=Wl(e),o=e.enter("strong"),a=e.createTracker(i),s=a.move(r+r);let l=a.move(e.containerPhrasing(n,{after:r,before:s,...a.current()}));const u=l.charCodeAt(0),d=pe(i.before.charCodeAt(i.before.length-1),u,r);d.inside&&(l=Yn(u)+l.slice(1));const c=l.charCodeAt(l.length-1),m=pe(i.after.charCodeAt(0),c,r);m.inside&&(l=l.slice(0,-1)+Yn(c));const f=a.move(r+r);return o(),e.attentionEncodeSurroundingInfo={after:m.outside,before:d.outside},s+l+f}function Hl(n,t,e){return e.options.strong||"*"}function Ul(n,t,e,i){return e.safe(n.value,i)}function Xl(n){const t=n.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}function Gl(n,t,e){const i=(er(e)+(e.options.ruleSpaces?" ":"")).repeat(Xl(e));return e.options.ruleSpaces?i.slice(0,-1):i}const ir={blockquote:$l,break:Qt,code:vl,definition:wl,emphasis:Ui,hardBreak:Qt,heading:Rl,html:Gi,image:Qi,imageReference:Yi,inlineCode:Ki,link:Zi,linkReference:nr,list:Ll,listItem:ql,paragraph:jl,root:Vl,strong:tr,text:Ul,thematicBreak:Gl};function Ql(){return{enter:{table:Yl,tableData:Yt,tableHeader:Yt,tableRow:Jl},exit:{codeText:Zl,table:Kl,tableData:De,tableHeader:De,tableRow:De}}}function Yl(n){const t=n._align;this.enter({type:"table",align:t.map(function(e){return e==="none"?null:e}),children:[]},n),this.data.inTable=!0}function Kl(n){this.exit(n),this.data.inTable=void 0}function Jl(n){this.enter({type:"tableRow",children:[]},n)}function De(n){this.exit(n)}function Yt(n){this.enter({type:"tableCell",children:[]},n)}function Zl(n){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,nu));const e=this.stack[this.stack.length-1];e.type,e.value=t,this.exit(n)}function nu(n,t){return t==="|"?t:n}function eu(n){const t=n||{},e=t.tableCellPadding,i=t.tablePipeAlign,r=t.stringLength,o=e?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:m,table:a,tableCell:l,tableRow:s}};function a(f,g,y,w){return u(d(f,y,w),f.align)}function s(f,g,y,w){const b=c(f,y,w),x=u([b]);return x.slice(0,x.indexOf(`
`))}function l(f,g,y,w){const b=y.enter("tableCell"),x=y.enter("phrasing"),k=y.containerPhrasing(f,{...w,before:o,after:o});return x(),b(),k}function u(f,g){return hl(f,{align:g,alignDelimiters:i,padding:e,stringLength:r})}function d(f,g,y){const w=f.children;let b=-1;const x=[],k=g.enter("table");for(;++b<w.length;)x[b]=c(w[b],g,y);return k(),x}function c(f,g,y){const w=f.children;let b=-1;const x=[],k=g.enter("tableRow");for(;++b<w.length;)x[b]=l(w[b],f,g,y);return k(),x}function m(f,g,y){let w=ir.inlineCode(f,g,y);return y.stack.includes("tableCell")&&(w=w.replace(/\|/g,"\\$&")),w}}function tu(){return{exit:{taskListCheckValueChecked:Kt,taskListCheckValueUnchecked:Kt,paragraph:ru}}}function iu(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:au}}}function Kt(n){const t=this.stack[this.stack.length-2];t.type,t.checked=n.type==="taskListCheckValueChecked"}function ru(n){const t=this.stack[this.stack.length-2];if(t&&t.type==="listItem"&&typeof t.checked=="boolean"){const e=this.stack[this.stack.length-1];e.type;const i=e.children[0];if(i&&i.type==="text"){const r=t.children;let o=-1,a;for(;++o<r.length;){const s=r[o];if(s.type==="paragraph"){a=s;break}}a===e&&(i.value=i.value.slice(1),i.value.length===0?e.children.shift():e.position&&i.position&&typeof i.position.start.offset=="number"&&(i.position.start.column++,i.position.start.offset++,e.position.start=Object.assign({},i.position.start)))}}this.exit(n)}function au(n,t,e,i){const r=n.children[0],o=typeof n.checked=="boolean"&&r&&r.type==="paragraph",a="["+(n.checked?"x":" ")+"] ",s=e.createTracker(i);o&&s.move(a);let l=ir.listItem(n,t,e,{...i,...s.current()});return o&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,u)),l;function u(d){return d+a}}function ou(){return[Ls(),al(),ul(),Ql(),tu()]}function su(n){return{extensions:[Os(),ol(n),cl(),eu(n),iu()]}}const lu={tokenize:pu,partial:!0},rr={tokenize:hu,partial:!0},ar={tokenize:gu,partial:!0},or={tokenize:bu,partial:!0},uu={tokenize:$u,partial:!0},sr={name:"wwwAutolink",tokenize:fu,previous:ur},lr={name:"protocolAutolink",tokenize:mu,previous:cr},yn={name:"emailAutolink",tokenize:du,previous:dr},pn={};function cu(){return{text:pn}}let Cn=48;for(;Cn<123;)pn[Cn]=yn,Cn++,Cn===58?Cn=65:Cn===91&&(Cn=97);pn[43]=yn;pn[45]=yn;pn[46]=yn;pn[95]=yn;pn[72]=[yn,lr];pn[104]=[yn,lr];pn[87]=[yn,sr];pn[119]=[yn,sr];function du(n,t,e){const i=this;let r,o;return a;function a(c){return!Xe(c)||!dr.call(i,i.previous)||st(i.events)?e(c):(n.enter("literalAutolink"),n.enter("literalAutolinkEmail"),s(c))}function s(c){return Xe(c)?(n.consume(c),s):c===64?(n.consume(c),l):e(c)}function l(c){return c===46?n.check(uu,d,u)(c):c===45||c===95||J(c)?(o=!0,n.consume(c),l):d(c)}function u(c){return n.consume(c),r=!0,l}function d(c){return o&&r&&Z(i.previous)?(n.exit("literalAutolinkEmail"),n.exit("literalAutolink"),t(c)):e(c)}}function fu(n,t,e){const i=this;return r;function r(a){return a!==87&&a!==119||!ur.call(i,i.previous)||st(i.events)?e(a):(n.enter("literalAutolink"),n.enter("literalAutolinkWww"),n.check(lu,n.attempt(rr,n.attempt(ar,o),e),e)(a))}function o(a){return n.exit("literalAutolinkWww"),n.exit("literalAutolink"),t(a)}}function mu(n,t,e){const i=this;let r="",o=!1;return a;function a(c){return(c===72||c===104)&&cr.call(i,i.previous)&&!st(i.events)?(n.enter("literalAutolink"),n.enter("literalAutolinkHttp"),r+=String.fromCodePoint(c),n.consume(c),s):e(c)}function s(c){if(Z(c)&&r.length<5)return r+=String.fromCodePoint(c),n.consume(c),s;if(c===58){const m=r.toLowerCase();if(m==="http"||m==="https")return n.consume(c),l}return e(c)}function l(c){return c===47?(n.consume(c),o?u:(o=!0,l)):e(c)}function u(c){return c===null||fe(c)||W(c)||An(c)||be(c)?e(c):n.attempt(rr,n.attempt(ar,d),e)(c)}function d(c){return n.exit("literalAutolinkHttp"),n.exit("literalAutolink"),t(c)}}function pu(n,t,e){let i=0;return r;function r(a){return(a===87||a===119)&&i<3?(i++,n.consume(a),r):a===46&&i===3?(n.consume(a),o):e(a)}function o(a){return a===null?e(a):t(a)}}function hu(n,t,e){let i,r,o;return a;function a(u){return u===46||u===95?n.check(or,l,s)(u):u===null||W(u)||An(u)||u!==45&&be(u)?l(u):(o=!0,n.consume(u),a)}function s(u){return u===95?i=!0:(r=i,i=void 0),n.consume(u),a}function l(u){return r||i||!o?e(u):t(u)}}function gu(n,t){let e=0,i=0;return r;function r(a){return a===40?(e++,n.consume(a),r):a===41&&i<e?o(a):a===33||a===34||a===38||a===39||a===41||a===42||a===44||a===46||a===58||a===59||a===60||a===63||a===93||a===95||a===126?n.check(or,t,o)(a):a===null||W(a)||An(a)?t(a):(n.consume(a),r)}function o(a){return a===41&&i++,n.consume(a),r}}function bu(n,t,e){return i;function i(s){return s===33||s===34||s===39||s===41||s===42||s===44||s===46||s===58||s===59||s===63||s===95||s===126?(n.consume(s),i):s===38?(n.consume(s),o):s===93?(n.consume(s),r):s===60||s===null||W(s)||An(s)?t(s):e(s)}function r(s){return s===null||s===40||s===91||W(s)||An(s)?t(s):i(s)}function o(s){return Z(s)?a(s):e(s)}function a(s){return s===59?(n.consume(s),i):Z(s)?(n.consume(s),a):e(s)}}function $u(n,t,e){return i;function i(o){return n.consume(o),r}function r(o){return J(o)?e(o):t(o)}}function ur(n){return n===null||n===40||n===42||n===95||n===91||n===93||n===126||W(n)}function cr(n){return!Z(n)}function dr(n){return!(n===47||Xe(n))}function Xe(n){return n===43||n===45||n===46||n===95||J(n)}function st(n){let t=n.length,e=!1;for(;t--;){const i=n[t][1];if((i.type==="labelLink"||i.type==="labelImage")&&!i._balanced){e=!0;break}if(i._gfmAutolinkLiteralWalkedInto){e=!1;break}}return n.length>0&&!e&&(n[n.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),e}const yu={tokenize:Au,partial:!0};function xu(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:ku,continuation:{tokenize:wu},exit:Cu}},text:{91:{name:"gfmFootnoteCall",tokenize:vu},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:_u,resolveTo:Su}}}}function _u(n,t,e){const i=this;let r=i.events.length;const o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);let a;for(;r--;){const l=i.events[r][1];if(l.type==="labelImage"){a=l;break}if(l.type==="gfmFootnoteCall"||l.type==="labelLink"||l.type==="label"||l.type==="image"||l.type==="link")break}return s;function s(l){if(!a||!a._balanced)return e(l);const u=fn(i.sliceSerialize({start:a.end,end:i.now()}));return u.codePointAt(0)!==94||!o.includes(u.slice(1))?e(l):(n.enter("gfmFootnoteCallLabelMarker"),n.consume(l),n.exit("gfmFootnoteCallLabelMarker"),t(l))}}function Su(n,t){let e=n.length;for(;e--;)if(n[e][1].type==="labelImage"&&n[e][0]==="enter"){n[e][1];break}n[e+1][1].type="data",n[e+3][1].type="gfmFootnoteCallLabelMarker";const i={type:"gfmFootnoteCall",start:Object.assign({},n[e+3][1].start),end:Object.assign({},n[n.length-1][1].end)},r={type:"gfmFootnoteCallMarker",start:Object.assign({},n[e+3][1].end),end:Object.assign({},n[e+3][1].end)};r.end.column++,r.end.offset++,r.end._bufferIndex++;const o={type:"gfmFootnoteCallString",start:Object.assign({},r.end),end:Object.assign({},n[n.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},s=[n[e+1],n[e+2],["enter",i,t],n[e+3],n[e+4],["enter",r,t],["exit",r,t],["enter",o,t],["enter",a,t],["exit",a,t],["exit",o,t],n[n.length-2],n[n.length-1],["exit",i,t]];return n.splice(e,n.length-e+1,...s),n}function vu(n,t,e){const i=this,r=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);let o=0,a;return s;function s(c){return n.enter("gfmFootnoteCall"),n.enter("gfmFootnoteCallLabelMarker"),n.consume(c),n.exit("gfmFootnoteCallLabelMarker"),l}function l(c){return c!==94?e(c):(n.enter("gfmFootnoteCallMarker"),n.consume(c),n.exit("gfmFootnoteCallMarker"),n.enter("gfmFootnoteCallString"),n.enter("chunkString").contentType="string",u)}function u(c){if(o>999||c===93&&!a||c===null||c===91||W(c))return e(c);if(c===93){n.exit("chunkString");const m=n.exit("gfmFootnoteCallString");return r.includes(fn(i.sliceSerialize(m)))?(n.enter("gfmFootnoteCallLabelMarker"),n.consume(c),n.exit("gfmFootnoteCallLabelMarker"),n.exit("gfmFootnoteCall"),t):e(c)}return W(c)||(a=!0),o++,n.consume(c),c===92?d:u}function d(c){return c===91||c===92||c===93?(n.consume(c),o++,u):u(c)}}function ku(n,t,e){const i=this,r=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);let o,a=0,s;return l;function l(g){return n.enter("gfmFootnoteDefinition")._container=!0,n.enter("gfmFootnoteDefinitionLabel"),n.enter("gfmFootnoteDefinitionLabelMarker"),n.consume(g),n.exit("gfmFootnoteDefinitionLabelMarker"),u}function u(g){return g===94?(n.enter("gfmFootnoteDefinitionMarker"),n.consume(g),n.exit("gfmFootnoteDefinitionMarker"),n.enter("gfmFootnoteDefinitionLabelString"),n.enter("chunkString").contentType="string",d):e(g)}function d(g){if(a>999||g===93&&!s||g===null||g===91||W(g))return e(g);if(g===93){n.exit("chunkString");const y=n.exit("gfmFootnoteDefinitionLabelString");return o=fn(i.sliceSerialize(y)),n.enter("gfmFootnoteDefinitionLabelMarker"),n.consume(g),n.exit("gfmFootnoteDefinitionLabelMarker"),n.exit("gfmFootnoteDefinitionLabel"),m}return W(g)||(s=!0),a++,n.consume(g),g===92?c:d}function c(g){return g===91||g===92||g===93?(n.consume(g),a++,d):d(g)}function m(g){return g===58?(n.enter("definitionMarker"),n.consume(g),n.exit("definitionMarker"),r.includes(o)||r.push(o),D(n,f,"gfmFootnoteDefinitionWhitespace")):e(g)}function f(g){return t(g)}}function wu(n,t,e){return n.check(Zn,t,n.attempt(yu,t,e))}function Cu(n){n.exit("gfmFootnoteDefinition")}function Au(n,t,e){const i=this;return D(n,r,"gfmFootnoteDefinitionIndent",5);function r(o){const a=i.events[i.events.length-1];return a&&a[1].type==="gfmFootnoteDefinitionIndent"&&a[2].sliceSerialize(a[1],!0).length===4?t(o):e(o)}}function Tu(n){let e=(n||{}).singleTilde;const i={name:"strikethrough",tokenize:o,resolveAll:r};return e==null&&(e=!0),{text:{126:i},insideSpan:{null:[i]},attentionMarkers:{null:[126]}};function r(a,s){let l=-1;for(;++l<a.length;)if(a[l][0]==="enter"&&a[l][1].type==="strikethroughSequenceTemporary"&&a[l][1]._close){let u=l;for(;u--;)if(a[u][0]==="exit"&&a[u][1].type==="strikethroughSequenceTemporary"&&a[u][1]._open&&a[l][1].end.offset-a[l][1].start.offset===a[u][1].end.offset-a[u][1].start.offset){a[l][1].type="strikethroughSequence",a[u][1].type="strikethroughSequence";const d={type:"strikethrough",start:Object.assign({},a[u][1].start),end:Object.assign({},a[l][1].end)},c={type:"strikethroughText",start:Object.assign({},a[u][1].end),end:Object.assign({},a[l][1].start)},m=[["enter",d,s],["enter",a[u][1],s],["exit",a[u][1],s],["enter",c,s]],f=s.parser.constructs.insideSpan.null;f&&on(m,m.length,0,$e(f,a.slice(u+1,l),s)),on(m,m.length,0,[["exit",c,s],["enter",a[l][1],s],["exit",a[l][1],s],["exit",d,s]]),on(a,u-1,l-u+3,m),l=u+m.length-2;break}}for(l=-1;++l<a.length;)a[l][1].type==="strikethroughSequenceTemporary"&&(a[l][1].type="data");return a}function o(a,s,l){const u=this.previous,d=this.events;let c=0;return m;function m(g){return u===126&&d[d.length-1][1].type!=="characterEscape"?l(g):(a.enter("strikethroughSequenceTemporary"),f(g))}function f(g){const y=Nn(u);if(g===126)return c>1?l(g):(a.consume(g),c++,f);if(c<2&&!e)return l(g);const w=a.exit("strikethroughSequenceTemporary"),b=Nn(g);return w._open=!b||b===2&&!!y,w._close=!y||y===2&&!!b,s(g)}}}class Ru{constructor(){this.map=[]}add(t,e,i){Mu(this,t,e,i)}consume(t){if(this.map.sort(function(o,a){return o[0]-a[0]}),this.map.length===0)return;let e=this.map.length;const i=[];for(;e>0;)e-=1,i.push(t.slice(this.map[e][0]+this.map[e][1]),this.map[e][2]),t.length=this.map[e][0];i.push(t.slice()),t.length=0;let r=i.pop();for(;r;){for(const o of r)t.push(o);r=i.pop()}this.map.length=0}}function Mu(n,t,e,i){let r=0;if(!(e===0&&i.length===0)){for(;r<n.map.length;){if(n.map[r][0]===t){n.map[r][1]+=e,n.map[r][2].push(...i);return}r+=1}n.map.push([t,e,i])}}function Eu(n,t){let e=!1;const i=[];for(;t<n.length;){const r=n[t];if(e){if(r[0]==="enter")r[1].type==="tableContent"&&i.push(n[t+1][1].type==="tableDelimiterMarker"?"left":"none");else if(r[1].type==="tableContent"){if(n[t-1][1].type==="tableDelimiterMarker"){const o=i.length-1;i[o]=i[o]==="left"?"center":"right"}}else if(r[1].type==="tableDelimiterRow")break}else r[0]==="enter"&&r[1].type==="tableDelimiterRow"&&(e=!0);t+=1}return i}function Pu(){return{flow:{null:{name:"table",tokenize:Iu,resolveAll:zu}}}}function Iu(n,t,e){const i=this;let r=0,o=0,a;return s;function s($){let T=i.events.length-1;for(;T>-1;){const U=i.events[T][1].type;if(U==="lineEnding"||U==="linePrefix")T--;else break}const R=T>-1?i.events[T][1].type:null,B=R==="tableHead"||R==="tableRow"?S:l;return B===S&&i.parser.lazy[i.now().line]?e($):B($)}function l($){return n.enter("tableHead"),n.enter("tableRow"),u($)}function u($){return $===124||(a=!0,o+=1),d($)}function d($){return $===null?e($):A($)?o>1?(o=0,i.interrupt=!0,n.exit("tableRow"),n.enter("lineEnding"),n.consume($),n.exit("lineEnding"),f):e($):N($)?D(n,d,"whitespace")($):(o+=1,a&&(a=!1,r+=1),$===124?(n.enter("tableCellDivider"),n.consume($),n.exit("tableCellDivider"),a=!0,d):(n.enter("data"),c($)))}function c($){return $===null||$===124||W($)?(n.exit("data"),d($)):(n.consume($),$===92?m:c)}function m($){return $===92||$===124?(n.consume($),c):c($)}function f($){return i.interrupt=!1,i.parser.lazy[i.now().line]?e($):(n.enter("tableDelimiterRow"),a=!1,N($)?D(n,g,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)($):g($))}function g($){return $===45||$===58?w($):$===124?(a=!0,n.enter("tableCellDivider"),n.consume($),n.exit("tableCellDivider"),y):P($)}function y($){return N($)?D(n,w,"whitespace")($):w($)}function w($){return $===58?(o+=1,a=!0,n.enter("tableDelimiterMarker"),n.consume($),n.exit("tableDelimiterMarker"),b):$===45?(o+=1,b($)):$===null||A($)?E($):P($)}function b($){return $===45?(n.enter("tableDelimiterFiller"),x($)):P($)}function x($){return $===45?(n.consume($),x):$===58?(a=!0,n.exit("tableDelimiterFiller"),n.enter("tableDelimiterMarker"),n.consume($),n.exit("tableDelimiterMarker"),k):(n.exit("tableDelimiterFiller"),k($))}function k($){return N($)?D(n,E,"whitespace")($):E($)}function E($){return $===124?g($):$===null||A($)?!a||r!==o?P($):(n.exit("tableDelimiterRow"),n.exit("tableHead"),t($)):P($)}function P($){return e($)}function S($){return n.enter("tableRow"),L($)}function L($){return $===124?(n.enter("tableCellDivider"),n.consume($),n.exit("tableCellDivider"),L):$===null||A($)?(n.exit("tableRow"),t($)):N($)?D(n,L,"whitespace")($):(n.enter("data"),j($))}function j($){return $===null||$===124||W($)?(n.exit("data"),L($)):(n.consume($),$===92?I:j)}function I($){return $===92||$===124?(n.consume($),j):j($)}}function zu(n,t){let e=-1,i=!0,r=0,o=[0,0,0,0],a=[0,0,0,0],s=!1,l=0,u,d,c;const m=new Ru;for(;++e<n.length;){const f=n[e],g=f[1];f[0]==="enter"?g.type==="tableHead"?(s=!1,l!==0&&(Jt(m,t,l,u,d),d=void 0,l=0),u={type:"table",start:Object.assign({},g.start),end:Object.assign({},g.end)},m.add(e,0,[["enter",u,t]])):g.type==="tableRow"||g.type==="tableDelimiterRow"?(i=!0,c=void 0,o=[0,0,0,0],a=[0,e+1,0,0],s&&(s=!1,d={type:"tableBody",start:Object.assign({},g.start),end:Object.assign({},g.end)},m.add(e,0,[["enter",d,t]])),r=g.type==="tableDelimiterRow"?2:d?3:1):r&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")?(i=!1,a[2]===0&&(o[1]!==0&&(a[0]=a[1],c=ae(m,t,o,r,void 0,c),o=[0,0,0,0]),a[2]=e)):g.type==="tableCellDivider"&&(i?i=!1:(o[1]!==0&&(a[0]=a[1],c=ae(m,t,o,r,void 0,c)),o=a,a=[o[1],e,0,0])):g.type==="tableHead"?(s=!0,l=e):g.type==="tableRow"||g.type==="tableDelimiterRow"?(l=e,o[1]!==0?(a[0]=a[1],c=ae(m,t,o,r,e,c)):a[1]!==0&&(c=ae(m,t,a,r,e,c)),r=0):r&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")&&(a[3]=e)}for(l!==0&&Jt(m,t,l,u,d),m.consume(t.events),e=-1;++e<t.events.length;){const f=t.events[e];f[0]==="enter"&&f[1].type==="table"&&(f[1]._align=Eu(t.events,e))}return n}function ae(n,t,e,i,r,o){const a=i===1?"tableHeader":i===2?"tableDelimiter":"tableData",s="tableContent";e[0]!==0&&(o.end=Object.assign({},zn(t.events,e[0])),n.add(e[0],0,[["exit",o,t]]));const l=zn(t.events,e[1]);if(o={type:a,start:Object.assign({},l),end:Object.assign({},l)},n.add(e[1],0,[["enter",o,t]]),e[2]!==0){const u=zn(t.events,e[2]),d=zn(t.events,e[3]),c={type:s,start:Object.assign({},u),end:Object.assign({},d)};if(n.add(e[2],0,[["enter",c,t]]),i!==2){const m=t.events[e[2]],f=t.events[e[3]];if(m[1].end=Object.assign({},f[1].end),m[1].type="chunkText",m[1].contentType="text",e[3]>e[2]+1){const g=e[2]+1,y=e[3]-e[2]-1;n.add(g,y,[])}}n.add(e[3]+1,0,[["exit",c,t]])}return r!==void 0&&(o.end=Object.assign({},zn(t.events,r)),n.add(r,0,[["exit",o,t]]),o=void 0),o}function Jt(n,t,e,i,r){const o=[],a=zn(t.events,e);r&&(r.end=Object.assign({},a),o.push(["exit",r,t])),i.end=Object.assign({},a),o.push(["exit",i,t]),n.add(e+1,0,o)}function zn(n,t){const e=n[t],i=e[0]==="enter"?"start":"end";return e[1][i]}const Fu={name:"tasklistCheck",tokenize:Nu};function Du(){return{text:{91:Fu}}}function Nu(n,t,e){const i=this;return r;function r(l){return i.previous!==null||!i._gfmTasklistFirstContentOfListItem?e(l):(n.enter("taskListCheck"),n.enter("taskListCheckMarker"),n.consume(l),n.exit("taskListCheckMarker"),o)}function o(l){return W(l)?(n.enter("taskListCheckValueUnchecked"),n.consume(l),n.exit("taskListCheckValueUnchecked"),a):l===88||l===120?(n.enter("taskListCheckValueChecked"),n.consume(l),n.exit("taskListCheckValueChecked"),a):e(l)}function a(l){return l===93?(n.enter("taskListCheckMarker"),n.consume(l),n.exit("taskListCheckMarker"),n.exit("taskListCheck"),s):e(l)}function s(l){return A(l)?t(l):N(l)?n.check({tokenize:Lu},t,e)(l):e(l)}}function Lu(n,t,e){return D(n,i,"whitespace");function i(r){return r===null?e(r):t(r)}}function Ou(n){return Ci([cu(),xu(),Tu(n),Pu(),Du()])}const qu={};function ju(n){const t=this,e=n||qu,i=t.data(),r=i.micromarkExtensions||(i.micromarkExtensions=[]),o=i.fromMarkdownExtensions||(i.fromMarkdownExtensions=[]),a=i.toMarkdownExtensions||(i.toMarkdownExtensions=[]);r.push(Ou(e)),o.push(ou()),a.push(su(e))}function Bu(){return{enter:{mathFlow:n,mathFlowFenceMeta:t,mathText:o},exit:{mathFlow:r,mathFlowFence:i,mathFlowFenceMeta:e,mathFlowValue:s,mathText:a,mathTextData:s}};function n(l){const u={type:"element",tagName:"code",properties:{className:["language-math","math-display"]},children:[]};this.enter({type:"math",meta:null,value:"",data:{hName:"pre",hChildren:[u]}},l)}function t(){this.buffer()}function e(){const l=this.resume(),u=this.stack[this.stack.length-1];u.type,u.meta=l}function i(){this.data.mathFlowInside||(this.buffer(),this.data.mathFlowInside=!0)}function r(l){const u=this.resume().replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),d=this.stack[this.stack.length-1];d.type,this.exit(l),d.value=u;const c=d.data.hChildren[0];c.type,c.tagName,c.children.push({type:"text",value:u}),this.data.mathFlowInside=void 0}function o(l){this.enter({type:"inlineMath",value:"",data:{hName:"code",hProperties:{className:["language-math","math-inline"]},hChildren:[]}},l),this.buffer()}function a(l){const u=this.resume(),d=this.stack[this.stack.length-1];d.type,this.exit(l),d.value=u,d.data.hChildren.push({type:"text",value:u})}function s(l){this.config.enter.data.call(this,l),this.config.exit.data.call(this,l)}}function Vu(n){let t=(n||{}).singleDollarTextMath;return t==null&&(t=!0),i.peek=r,{unsafe:[{character:"\r",inConstruct:"mathFlowMeta"},{character:`
`,inConstruct:"mathFlowMeta"},{character:"$",after:t?void 0:"\\$",inConstruct:"phrasing"},{character:"$",inConstruct:"mathFlowMeta"},{atBreak:!0,character:"$",after:"\\$"}],handlers:{math:e,inlineMath:i}};function e(o,a,s,l){const u=o.value||"",d=s.createTracker(l),c="$".repeat(Math.max(Hi(u,"$")+1,2)),m=s.enter("mathFlow");let f=d.move(c);if(o.meta){const g=s.enter("mathFlowMeta");f+=d.move(s.safe(o.meta,{after:`
`,before:f,encode:["$"],...d.current()})),g()}return f+=d.move(`
`),u&&(f+=d.move(u+`
`)),f+=d.move(c),m(),f}function i(o,a,s){let l=o.value||"",u=1;for(t||u++;new RegExp("(^|[^$])"+"\\$".repeat(u)+"([^$]|$)").test(l);)u++;const d="$".repeat(u);/[^ \r\n]/.test(l)&&(/^[ \r\n]/.test(l)&&/[ \r\n]$/.test(l)||/^\$|\$$/.test(l))&&(l=" "+l+" ");let c=-1;for(;++c<s.unsafe.length;){const m=s.unsafe[c];if(!m.atBreak)continue;const f=s.compilePattern(m);let g;for(;g=f.exec(l);){let y=g.index;l.codePointAt(y)===10&&l.codePointAt(y-1)===13&&y--,l=l.slice(0,y)+" "+l.slice(g.index+1)}}return d+l+d}function r(){return"$"}}const Wu={tokenize:Hu,concrete:!0,name:"mathFlow"},Zt={tokenize:Uu,partial:!0};function Hu(n,t,e){const i=this,r=i.events[i.events.length-1],o=r&&r[1].type==="linePrefix"?r[2].sliceSerialize(r[1],!0).length:0;let a=0;return s;function s(x){return n.enter("mathFlow"),n.enter("mathFlowFence"),n.enter("mathFlowFenceSequence"),l(x)}function l(x){return x===36?(n.consume(x),a++,l):a<2?e(x):(n.exit("mathFlowFenceSequence"),D(n,u,"whitespace")(x))}function u(x){return x===null||A(x)?c(x):(n.enter("mathFlowFenceMeta"),n.enter("chunkString",{contentType:"string"}),d(x))}function d(x){return x===null||A(x)?(n.exit("chunkString"),n.exit("mathFlowFenceMeta"),c(x)):x===36?e(x):(n.consume(x),d)}function c(x){return n.exit("mathFlowFence"),i.interrupt?t(x):n.attempt(Zt,m,w)(x)}function m(x){return n.attempt({tokenize:b,partial:!0},w,f)(x)}function f(x){return(o?D(n,g,"linePrefix",o+1):g)(x)}function g(x){return x===null?w(x):A(x)?n.attempt(Zt,m,w)(x):(n.enter("mathFlowValue"),y(x))}function y(x){return x===null||A(x)?(n.exit("mathFlowValue"),g(x)):(n.consume(x),y)}function w(x){return n.exit("mathFlow"),t(x)}function b(x,k,E){let P=0;return D(x,S,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4);function S(I){return x.enter("mathFlowFence"),x.enter("mathFlowFenceSequence"),L(I)}function L(I){return I===36?(P++,x.consume(I),L):P<a?E(I):(x.exit("mathFlowFenceSequence"),D(x,j,"whitespace")(I))}function j(I){return I===null||A(I)?(x.exit("mathFlowFence"),k(I)):E(I)}}}function Uu(n,t,e){const i=this;return r;function r(a){return a===null?t(a):(n.enter("lineEnding"),n.consume(a),n.exit("lineEnding"),o)}function o(a){return i.parser.lazy[i.now().line]?e(a):t(a)}}function Xu(n){let e=(n||{}).singleDollarTextMath;return e==null&&(e=!0),{tokenize:i,resolve:Gu,previous:Qu,name:"mathText"};function i(r,o,a){let s=0,l,u;return d;function d(y){return r.enter("mathText"),r.enter("mathTextSequence"),c(y)}function c(y){return y===36?(r.consume(y),s++,c):s<2&&!e?a(y):(r.exit("mathTextSequence"),m(y))}function m(y){return y===null?a(y):y===36?(u=r.enter("mathTextSequence"),l=0,g(y)):y===32?(r.enter("space"),r.consume(y),r.exit("space"),m):A(y)?(r.enter("lineEnding"),r.consume(y),r.exit("lineEnding"),m):(r.enter("mathTextData"),f(y))}function f(y){return y===null||y===32||y===36||A(y)?(r.exit("mathTextData"),m(y)):(r.consume(y),f)}function g(y){return y===36?(r.consume(y),l++,g):l===s?(r.exit("mathTextSequence"),r.exit("mathText"),o(y)):(u.type="mathTextData",f(y))}}}function Gu(n){let t=n.length-4,e=3,i,r;if((n[e][1].type==="lineEnding"||n[e][1].type==="space")&&(n[t][1].type==="lineEnding"||n[t][1].type==="space")){for(i=e;++i<t;)if(n[i][1].type==="mathTextData"){n[t][1].type="mathTextPadding",n[e][1].type="mathTextPadding",e+=2,t-=2;break}}for(i=e-1,t++;++i<=t;)r===void 0?i!==t&&n[i][1].type!=="lineEnding"&&(r=i):(i===t||n[i][1].type==="lineEnding")&&(n[r][1].type="mathTextData",i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),t-=i-r-2,i=r+2),r=void 0);return n}function Qu(n){return n!==36||this.events[this.events.length-1][1].type==="characterEscape"}function Yu(n){return{flow:{36:Wu},text:{36:Xu(n)}}}const Ku={};function Ju(n){const t=this,e=n||Ku,i=t.data(),r=i.micromarkExtensions||(i.micromarkExtensions=[]),o=i.fromMarkdownExtensions||(i.fromMarkdownExtensions=[]),a=i.toMarkdownExtensions||(i.toMarkdownExtensions=[]);r.push(Yu(e)),o.push(Bu()),a.push(Vu(e))}const Zu=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class ee{constructor(t,e,i){this.normal=e,this.property=t,i&&(this.space=i)}}ee.prototype.normal={};ee.prototype.property={};ee.prototype.space=void 0;function fr(n,t){const e={},i={};for(const r of n)Object.assign(e,r.property),Object.assign(i,r.normal);return new ee(e,i,t)}function Kn(n){return n.toLowerCase()}class tn{constructor(t,e){this.attribute=e,this.property=t}}tn.prototype.attribute="";tn.prototype.booleanish=!1;tn.prototype.boolean=!1;tn.prototype.commaOrSpaceSeparated=!1;tn.prototype.commaSeparated=!1;tn.prototype.defined=!1;tn.prototype.mustUseProperty=!1;tn.prototype.number=!1;tn.prototype.overloadedBoolean=!1;tn.prototype.property="";tn.prototype.spaceSeparated=!1;tn.prototype.space=void 0;let nc=0;const F=Tn(),X=Tn(),Ge=Tn(),v=Tn(),H=Tn(),Fn=Tn(),an=Tn();function Tn(){return 2**++nc}const Qe=Object.freeze(Object.defineProperty({__proto__:null,boolean:F,booleanish:X,commaOrSpaceSeparated:an,commaSeparated:Fn,number:v,overloadedBoolean:Ge,spaceSeparated:H},Symbol.toStringTag,{value:"Module"})),Ne=Object.keys(Qe);class lt extends tn{constructor(t,e,i,r){let o=-1;if(super(t,e),ni(this,"space",r),typeof i=="number")for(;++o<Ne.length;){const a=Ne[o];ni(this,Ne[o],(i&Qe[a])===Qe[a])}}}lt.prototype.defined=!0;function ni(n,t,e){e&&(n[t]=e)}function On(n){const t={},e={};for(const[i,r]of Object.entries(n.properties)){const o=new lt(i,n.transform(n.attributes||{},i),r,n.space);n.mustUseProperty&&n.mustUseProperty.includes(i)&&(o.mustUseProperty=!0),t[i]=o,e[Kn(i)]=i,e[Kn(o.attribute)]=i}return new ee(t,e,n.space)}const mr=On({properties:{ariaActiveDescendant:null,ariaAtomic:X,ariaAutoComplete:null,ariaBusy:X,ariaChecked:X,ariaColCount:v,ariaColIndex:v,ariaColSpan:v,ariaControls:H,ariaCurrent:null,ariaDescribedBy:H,ariaDetails:null,ariaDisabled:X,ariaDropEffect:H,ariaErrorMessage:null,ariaExpanded:X,ariaFlowTo:H,ariaGrabbed:X,ariaHasPopup:null,ariaHidden:X,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:H,ariaLevel:v,ariaLive:null,ariaModal:X,ariaMultiLine:X,ariaMultiSelectable:X,ariaOrientation:null,ariaOwns:H,ariaPlaceholder:null,ariaPosInSet:v,ariaPressed:X,ariaReadOnly:X,ariaRelevant:null,ariaRequired:X,ariaRoleDescription:H,ariaRowCount:v,ariaRowIndex:v,ariaRowSpan:v,ariaSelected:X,ariaSetSize:v,ariaSort:null,ariaValueMax:v,ariaValueMin:v,ariaValueNow:v,ariaValueText:null,role:null},transform(n,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function pr(n,t){return t in n?n[t]:t}function hr(n,t){return pr(n,t.toLowerCase())}const ec=On({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Fn,acceptCharset:H,accessKey:H,action:null,allow:null,allowFullScreen:F,allowPaymentRequest:F,allowUserMedia:F,alt:null,as:null,async:F,autoCapitalize:null,autoComplete:H,autoFocus:F,autoPlay:F,blocking:H,capture:null,charSet:null,checked:F,cite:null,className:H,cols:v,colSpan:null,content:null,contentEditable:X,controls:F,controlsList:H,coords:v|Fn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:F,defer:F,dir:null,dirName:null,disabled:F,download:Ge,draggable:X,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:F,formTarget:null,headers:H,height:v,hidden:Ge,high:v,href:null,hrefLang:null,htmlFor:H,httpEquiv:H,id:null,imageSizes:null,imageSrcSet:null,inert:F,inputMode:null,integrity:null,is:null,isMap:F,itemId:null,itemProp:H,itemRef:H,itemScope:F,itemType:H,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:F,low:v,manifest:null,max:null,maxLength:v,media:null,method:null,min:null,minLength:v,multiple:F,muted:F,name:null,nonce:null,noModule:F,noValidate:F,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:F,optimum:v,pattern:null,ping:H,placeholder:null,playsInline:F,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:F,referrerPolicy:null,rel:H,required:F,reversed:F,rows:v,rowSpan:v,sandbox:H,scope:null,scoped:F,seamless:F,selected:F,shadowRootClonable:F,shadowRootDelegatesFocus:F,shadowRootMode:null,shape:null,size:v,sizes:null,slot:null,span:v,spellCheck:X,src:null,srcDoc:null,srcLang:null,srcSet:null,start:v,step:null,style:null,tabIndex:v,target:null,title:null,translate:null,type:null,typeMustMatch:F,useMap:null,value:X,width:v,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:H,axis:null,background:null,bgColor:null,border:v,borderColor:null,bottomMargin:v,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:F,declare:F,event:null,face:null,frame:null,frameBorder:null,hSpace:v,leftMargin:v,link:null,longDesc:null,lowSrc:null,marginHeight:v,marginWidth:v,noResize:F,noHref:F,noShade:F,noWrap:F,object:null,profile:null,prompt:null,rev:null,rightMargin:v,rules:null,scheme:null,scrolling:X,standby:null,summary:null,text:null,topMargin:v,valueType:null,version:null,vAlign:null,vLink:null,vSpace:v,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:F,disableRemotePlayback:F,prefix:null,property:null,results:v,security:null,unselectable:null},space:"html",transform:hr}),tc=On({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:an,accentHeight:v,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:v,amplitude:v,arabicForm:null,ascent:v,attributeName:null,attributeType:null,azimuth:v,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:v,by:null,calcMode:null,capHeight:v,className:H,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:v,diffuseConstant:v,direction:null,display:null,dur:null,divisor:v,dominantBaseline:null,download:F,dx:null,dy:null,edgeMode:null,editable:null,elevation:v,enableBackground:null,end:null,event:null,exponent:v,externalResourcesRequired:null,fill:null,fillOpacity:v,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Fn,g2:Fn,glyphName:Fn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:v,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:v,horizOriginX:v,horizOriginY:v,id:null,ideographic:v,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:v,k:v,k1:v,k2:v,k3:v,k4:v,kernelMatrix:an,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:v,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:v,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:v,overlineThickness:v,paintOrder:null,panose1:null,path:null,pathLength:v,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:H,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:v,pointsAtY:v,pointsAtZ:v,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:an,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:an,rev:an,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:an,requiredFeatures:an,requiredFonts:an,requiredFormats:an,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:v,specularExponent:v,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:v,strikethroughThickness:v,string:null,stroke:null,strokeDashArray:an,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:v,strokeOpacity:v,strokeWidth:null,style:null,surfaceScale:v,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:an,tabIndex:v,tableValues:null,target:null,targetX:v,targetY:v,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:an,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:v,underlineThickness:v,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:v,values:null,vAlphabetic:v,vMathematical:v,vectorEffect:null,vHanging:v,vIdeographic:v,version:null,vertAdvY:v,vertOriginX:v,vertOriginY:v,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:v,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:pr}),gr=On({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(n,t){return"xlink:"+t.slice(5).toLowerCase()}}),br=On({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:hr}),$r=On({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(n,t){return"xml:"+t.slice(3).toLowerCase()}}),ic=/[A-Z]/g,ei=/-[a-z]/g,rc=/^data[-\w.:]+$/i;function yr(n,t){const e=Kn(t);let i=t,r=tn;if(e in n.normal)return n.property[n.normal[e]];if(e.length>4&&e.slice(0,4)==="data"&&rc.test(t)){if(t.charAt(4)==="-"){const o=t.slice(5).replace(ei,oc);i="data"+o.charAt(0).toUpperCase()+o.slice(1)}else{const o=t.slice(4);if(!ei.test(o)){let a=o.replace(ic,ac);a.charAt(0)!=="-"&&(a="-"+a),t="data"+a}}r=lt}return new r(i,t)}function ac(n){return"-"+n.toLowerCase()}function oc(n){return n.charAt(1).toUpperCase()}const xr=fr([mr,ec,gr,br,$r],"html"),ut=fr([mr,tc,gr,br,$r],"svg"),sc=/["&'<>`]/g,lc=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,uc=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,cc=/[|\\{}()[\]^$+*?.]/g,ti=new WeakMap;function dc(n,t){if(n=n.replace(t.subset?fc(t.subset):sc,i),t.subset||t.escapeOnly)return n;return n.replace(lc,e).replace(uc,i);function e(r,o,a){return t.format((r.charCodeAt(0)-55296)*1024+r.charCodeAt(1)-56320+65536,a.charCodeAt(o+2),t)}function i(r,o,a){return t.format(r.charCodeAt(0),a.charCodeAt(o+1),t)}}function fc(n){let t=ti.get(n);return t||(t=mc(n),ti.set(n,t)),t}function mc(n){const t=[];let e=-1;for(;++e<n.length;)t.push(n[e].replace(cc,"\\$&"));return new RegExp("(?:"+t.join("|")+")","g")}const pc=/[\dA-Fa-f]/;function hc(n,t,e){const i="&#x"+n.toString(16).toUpperCase();return e&&t&&!pc.test(String.fromCharCode(t))?i:i+";"}const gc=/\d/;function bc(n,t,e){const i="&#"+String(n);return e&&t&&!gc.test(String.fromCharCode(t))?i:i+";"}const $c=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],Le={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},yc=["cent","copy","divide","gt","lt","not","para","times"],_r={}.hasOwnProperty,Ye={};let oe;for(oe in Le)_r.call(Le,oe)&&(Ye[Le[oe]]=oe);const xc=/[^\dA-Za-z]/;function _c(n,t,e,i){const r=String.fromCharCode(n);if(_r.call(Ye,r)){const o=Ye[r],a="&"+o;return e&&$c.includes(o)&&!yc.includes(o)&&(!i||t&&t!==61&&xc.test(String.fromCharCode(t)))?a:a+";"}return""}function Sc(n,t,e){let i=hc(n,t,e.omitOptionalSemicolons),r;if((e.useNamedReferences||e.useShortestReferences)&&(r=_c(n,t,e.omitOptionalSemicolons,e.attribute)),(e.useShortestReferences||!r)&&e.useShortestReferences){const o=bc(n,t,e.omitOptionalSemicolons);o.length<i.length&&(i=o)}return r&&(!e.useShortestReferences||r.length<i.length)?r:i}function Dn(n,t){return dc(n,Object.assign({format:Sc},t))}const vc=/^>|^->|<!--|-->|--!>|<!-$/g,kc=[">"],wc=["<",">"];function Cc(n,t,e,i){return i.settings.bogusComments?"<?"+Dn(n.value,Object.assign({},i.settings.characterReferences,{subset:kc}))+">":"<!--"+n.value.replace(vc,r)+"-->";function r(o){return Dn(o,Object.assign({},i.settings.characterReferences,{subset:wc}))}}function Ac(n,t,e,i){return"<!"+(i.settings.upperDoctype?"DOCTYPE":"doctype")+(i.settings.tightDoctype?"":" ")+"html>"}function ii(n){const t=[],e=String(n||"");let i=e.indexOf(","),r=0,o=!1;for(;!o;){i===-1&&(i=e.length,o=!0);const a=e.slice(r,i).trim();(a||!o)&&t.push(a),r=i+1,i=e.indexOf(",",r)}return t}function Tc(n,t){const e=t||{};return(n[n.length-1]===""?[...n,""]:n).join((e.padRight?" ":"")+","+(e.padLeft===!1?"":" ")).trim()}function ri(n){const t=String(n||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function Rc(n){return n.join(" ").trim()}const Mc=/[ \t\n\f\r]/g;function ct(n){return typeof n=="object"?n.type==="text"?ai(n.value):!1:ai(n)}function ai(n){return n.replace(Mc,"")===""}const G=vr(1),Sr=vr(-1),Ec=[];function vr(n){return t;function t(e,i,r){const o=e?e.children:Ec;let a=(i||0)+n,s=o[a];if(!r)for(;s&&ct(s);)a+=n,s=o[a];return s}}const Pc={}.hasOwnProperty;function kr(n){return t;function t(e,i,r){return Pc.call(n,e.tagName)&&n[e.tagName](e,i,r)}}const dt=kr({body:zc,caption:Oe,colgroup:Oe,dd:Lc,dt:Nc,head:Oe,html:Ic,li:Dc,optgroup:Oc,option:qc,p:Fc,rp:oi,rt:oi,tbody:Bc,td:si,tfoot:Vc,th:si,thead:jc,tr:Wc});function Oe(n,t,e){const i=G(e,t,!0);return!i||i.type!=="comment"&&!(i.type==="text"&&ct(i.value.charAt(0)))}function Ic(n,t,e){const i=G(e,t);return!i||i.type!=="comment"}function zc(n,t,e){const i=G(e,t);return!i||i.type!=="comment"}function Fc(n,t,e){const i=G(e,t);return i?i.type==="element"&&(i.tagName==="address"||i.tagName==="article"||i.tagName==="aside"||i.tagName==="blockquote"||i.tagName==="details"||i.tagName==="div"||i.tagName==="dl"||i.tagName==="fieldset"||i.tagName==="figcaption"||i.tagName==="figure"||i.tagName==="footer"||i.tagName==="form"||i.tagName==="h1"||i.tagName==="h2"||i.tagName==="h3"||i.tagName==="h4"||i.tagName==="h5"||i.tagName==="h6"||i.tagName==="header"||i.tagName==="hgroup"||i.tagName==="hr"||i.tagName==="main"||i.tagName==="menu"||i.tagName==="nav"||i.tagName==="ol"||i.tagName==="p"||i.tagName==="pre"||i.tagName==="section"||i.tagName==="table"||i.tagName==="ul"):!e||!(e.type==="element"&&(e.tagName==="a"||e.tagName==="audio"||e.tagName==="del"||e.tagName==="ins"||e.tagName==="map"||e.tagName==="noscript"||e.tagName==="video"))}function Dc(n,t,e){const i=G(e,t);return!i||i.type==="element"&&i.tagName==="li"}function Nc(n,t,e){const i=G(e,t);return!!(i&&i.type==="element"&&(i.tagName==="dt"||i.tagName==="dd"))}function Lc(n,t,e){const i=G(e,t);return!i||i.type==="element"&&(i.tagName==="dt"||i.tagName==="dd")}function oi(n,t,e){const i=G(e,t);return!i||i.type==="element"&&(i.tagName==="rp"||i.tagName==="rt")}function Oc(n,t,e){const i=G(e,t);return!i||i.type==="element"&&i.tagName==="optgroup"}function qc(n,t,e){const i=G(e,t);return!i||i.type==="element"&&(i.tagName==="option"||i.tagName==="optgroup")}function jc(n,t,e){const i=G(e,t);return!!(i&&i.type==="element"&&(i.tagName==="tbody"||i.tagName==="tfoot"))}function Bc(n,t,e){const i=G(e,t);return!i||i.type==="element"&&(i.tagName==="tbody"||i.tagName==="tfoot")}function Vc(n,t,e){return!G(e,t)}function Wc(n,t,e){const i=G(e,t);return!i||i.type==="element"&&i.tagName==="tr"}function si(n,t,e){const i=G(e,t);return!i||i.type==="element"&&(i.tagName==="td"||i.tagName==="th")}const Hc=kr({body:Gc,colgroup:Qc,head:Xc,html:Uc,tbody:Yc});function Uc(n){const t=G(n,-1);return!t||t.type!=="comment"}function Xc(n){const t=new Set;for(const i of n.children)if(i.type==="element"&&(i.tagName==="base"||i.tagName==="title")){if(t.has(i.tagName))return!1;t.add(i.tagName)}const e=n.children[0];return!e||e.type==="element"}function Gc(n){const t=G(n,-1,!0);return!t||t.type!=="comment"&&!(t.type==="text"&&ct(t.value.charAt(0)))&&!(t.type==="element"&&(t.tagName==="meta"||t.tagName==="link"||t.tagName==="script"||t.tagName==="style"||t.tagName==="template"))}function Qc(n,t,e){const i=Sr(e,t),r=G(n,-1,!0);return e&&i&&i.type==="element"&&i.tagName==="colgroup"&&dt(i,e.children.indexOf(i),e)?!1:!!(r&&r.type==="element"&&r.tagName==="col")}function Yc(n,t,e){const i=Sr(e,t),r=G(n,-1);return e&&i&&i.type==="element"&&(i.tagName==="thead"||i.tagName==="tbody")&&dt(i,e.children.indexOf(i),e)?!1:!!(r&&r.type==="element"&&r.tagName==="tr")}const se={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function Kc(n,t,e,i){const r=i.schema,o=r.space==="svg"?!1:i.settings.omitOptionalTags;let a=r.space==="svg"?i.settings.closeEmptyElements:i.settings.voids.includes(n.tagName.toLowerCase());const s=[];let l;r.space==="html"&&n.tagName==="svg"&&(i.schema=ut);const u=Jc(i,n.properties),d=i.all(r.space==="html"&&n.tagName==="template"?n.content:n);return i.schema=r,d&&(a=!1),(u||!o||!Hc(n,t,e))&&(s.push("<",n.tagName,u?" "+u:""),a&&(r.space==="svg"||i.settings.closeSelfClosing)&&(l=u.charAt(u.length-1),(!i.settings.tightSelfClosing||l==="/"||l&&l!=='"'&&l!=="'")&&s.push(" "),s.push("/")),s.push(">")),s.push(d),!a&&(!o||!dt(n,t,e))&&s.push("</"+n.tagName+">"),s.join("")}function Jc(n,t){const e=[];let i=-1,r;if(t){for(r in t)if(t[r]!==null&&t[r]!==void 0){const o=Zc(n,r,t[r]);o&&e.push(o)}}for(;++i<e.length;){const o=n.settings.tightAttributes?e[i].charAt(e[i].length-1):void 0;i!==e.length-1&&o!=='"'&&o!=="'"&&(e[i]+=" ")}return e.join("")}function Zc(n,t,e){const i=yr(n.schema,t),r=n.settings.allowParseErrors&&n.schema.space==="html"?0:1,o=n.settings.allowDangerousCharacters?0:1;let a=n.quote,s;if(i.overloadedBoolean&&(e===i.attribute||e==="")?e=!0:(i.boolean||i.overloadedBoolean)&&(typeof e!="string"||e===i.attribute||e==="")&&(e=!!e),e==null||e===!1||typeof e=="number"&&Number.isNaN(e))return"";const l=Dn(i.attribute,Object.assign({},n.settings.characterReferences,{subset:se.name[r][o]}));return e===!0||(e=Array.isArray(e)?(i.commaSeparated?Tc:Rc)(e,{padLeft:!n.settings.tightCommaSeparatedLists}):String(e),n.settings.collapseEmptyAttributes&&!e)?l:(n.settings.preferUnquoted&&(s=Dn(e,Object.assign({},n.settings.characterReferences,{attribute:!0,subset:se.unquoted[r][o]}))),s!==e&&(n.settings.quoteSmart&&me(e,a)>me(e,n.alternative)&&(a=n.alternative),s=a+Dn(e,Object.assign({},n.settings.characterReferences,{subset:(a==="'"?se.single:se.double)[r][o],attribute:!0}))+a),l+(s&&"="+s))}const nd=["<","&"];function wr(n,t,e,i){return e&&e.type==="element"&&(e.tagName==="script"||e.tagName==="style")?n.value:Dn(n.value,Object.assign({},i.settings.characterReferences,{subset:nd}))}function ed(n,t,e,i){return i.settings.allowDangerousHtml?n.value:wr(n,t,e,i)}function td(n,t,e,i){return i.all(n)}const id=bl("type",{invalid:rd,unknown:ad,handlers:{comment:Cc,doctype:Ac,element:Kc,raw:ed,root:td,text:wr}});function rd(n){throw new Error("Expected node, not `"+n+"`")}function ad(n){const t=n;throw new Error("Cannot compile unknown node `"+t.type+"`")}const od={},sd={},ld=[];function ud(n,t){const e=t||od,i=e.quote||'"',r=i==='"'?"'":'"';if(i!=='"'&&i!=="'")throw new Error("Invalid quote `"+i+"`, expected `'` or `\"`");return{one:cd,all:dd,settings:{omitOptionalTags:e.omitOptionalTags||!1,allowParseErrors:e.allowParseErrors||!1,allowDangerousCharacters:e.allowDangerousCharacters||!1,quoteSmart:e.quoteSmart||!1,preferUnquoted:e.preferUnquoted||!1,tightAttributes:e.tightAttributes||!1,upperDoctype:e.upperDoctype||!1,tightDoctype:e.tightDoctype||!1,bogusComments:e.bogusComments||!1,tightCommaSeparatedLists:e.tightCommaSeparatedLists||!1,tightSelfClosing:e.tightSelfClosing||!1,collapseEmptyAttributes:e.collapseEmptyAttributes||!1,allowDangerousHtml:e.allowDangerousHtml||!1,voids:e.voids||Zu,characterReferences:e.characterReferences||sd,closeSelfClosing:e.closeSelfClosing||!1,closeEmptyElements:e.closeEmptyElements||!1},schema:e.space==="svg"?ut:xr,quote:i,alternative:r}.one(Array.isArray(n)?{type:"root",children:n}:n,void 0,void 0)}function cd(n,t,e){return id(n,t,e,this)}function dd(n){const t=[],e=n&&n.children||ld;let i=-1;for(;++i<e.length;)t[i]=this.one(e[i],i,n);return t.join("")}function fd(n){const t=this,e={...t.data("settings"),...n};t.compiler=i;function i(r){return ud(r,e)}}const li=/[#.]/g;function md(n,t){const e=n||"",i={};let r=0,o,a;for(;r<e.length;){li.lastIndex=r;const s=li.exec(e),l=e.slice(r,s?s.index:e.length);l&&(o?o==="#"?i.id=l:Array.isArray(i.className)?i.className.push(l):i.className=[l]:a=l,r+=l.length),s&&(o=s[0],r++)}return{type:"element",tagName:a||t||"div",properties:i,children:[]}}function Cr(n,t,e){const i=e?bd(e):void 0;function r(o,a,...s){let l;if(o==null){l={type:"root",children:[]};const u=a;s.unshift(u)}else{l=md(o,t);const u=l.tagName.toLowerCase(),d=i?i.get(u):void 0;if(l.tagName=d||u,pd(a))s.unshift(a);else for(const[c,m]of Object.entries(a))hd(n,l.properties,c,m)}for(const u of s)Ke(l.children,u);return l.type==="element"&&l.tagName==="template"&&(l.content={type:"root",children:l.children},l.children=[]),l}return r}function pd(n){if(n===null||typeof n!="object"||Array.isArray(n))return!0;if(typeof n.type!="string")return!1;const t=n,e=Object.keys(n);for(const i of e){const r=t[i];if(r&&typeof r=="object"){if(!Array.isArray(r))return!0;const o=r;for(const a of o)if(typeof a!="number"&&typeof a!="string")return!0}}return!!("children"in n&&Array.isArray(n.children))}function hd(n,t,e,i){const r=yr(n,e);let o;if(i!=null){if(typeof i=="number"){if(Number.isNaN(i))return;o=i}else typeof i=="boolean"?o=i:typeof i=="string"?r.spaceSeparated?o=ri(i):r.commaSeparated?o=ii(i):r.commaOrSpaceSeparated?o=ri(ii(i).join(" ")):o=ui(r,r.property,i):Array.isArray(i)?o=[...i]:o=r.property==="style"?gd(i):String(i);if(Array.isArray(o)){const a=[];for(const s of o)a.push(ui(r,r.property,s));o=a}r.property==="className"&&Array.isArray(t.className)&&(o=t.className.concat(o)),t[r.property]=o}}function Ke(n,t){if(t!=null)if(typeof t=="number"||typeof t=="string")n.push({type:"text",value:String(t)});else if(Array.isArray(t))for(const e of t)Ke(n,e);else if(typeof t=="object"&&"type"in t)t.type==="root"?Ke(n,t.children):n.push(t);else throw new Error("Expected node, nodes, or string, got `"+t+"`")}function ui(n,t,e){if(typeof e=="string"){if(n.number&&e&&!Number.isNaN(Number(e)))return Number(e);if((n.boolean||n.overloadedBoolean)&&(e===""||Kn(e)===Kn(t)))return!0}return e}function gd(n){const t=[];for(const[e,i]of Object.entries(n))t.push([e,i].join(": "));return t.join("; ")}function bd(n){const t=new Map;for(const e of n)t.set(e.toLowerCase(),e);return t}const $d=["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"],yd=Cr(xr,"div"),xd=Cr(ut,"g",$d),qe={html:"http://www.w3.org/1999/xhtml",svg:"http://www.w3.org/2000/svg"};function _d(n,t){return Ar(n,{})||{type:"root",children:[]}}function Ar(n,t){const e=Sd(n,t);return e&&t.afterTransform&&t.afterTransform(n,e),e}function Sd(n,t){switch(n.nodeType){case 1:return Cd(n,t);case 3:return kd(n);case 8:return wd(n);case 9:return ci(n,t);case 10:return vd();case 11:return ci(n,t);default:return}}function ci(n,t){return{type:"root",children:Tr(n,t)}}function vd(){return{type:"doctype"}}function kd(n){return{type:"text",value:n.nodeValue||""}}function wd(n){return{type:"comment",value:n.nodeValue||""}}function Cd(n,t){const e=n.namespaceURI,i=e===qe.svg?xd:yd,r=e===qe.html?n.tagName.toLowerCase():n.tagName,o=e===qe.html&&r==="template"?n.content:n,a=n.getAttributeNames(),s={};let l=-1;for(;++l<a.length;)s[a[l]]=n.getAttribute(a[l])||"";return i(r,s,Tr(o,t))}function Tr(n,t){const e=n.childNodes,i=[];let r=-1;for(;++r<e.length;){const o=Ar(e[r],t);o!==void 0&&i.push(o)}return i}new DOMParser;function Ad(n,t){const e=Td(n);return _d(e)}function Td(n){const t=document.createElement("template");return t.innerHTML=n,t.content}const di=function(n,t,e){const i=ne(e);if(!n||!n.type||!n.children)throw new Error("Expected parent node");if(typeof t=="number"){if(t<0||t===Number.POSITIVE_INFINITY)throw new Error("Expected positive finite number as index")}else if(t=n.children.indexOf(t),t<0)throw new Error("Expected child node or index");for(;++t<n.children.length;)if(i(n.children[t],t,n))return n.children[t]},Rn=function(n){if(n==null)return Ed;if(typeof n=="string")return Md(n);if(typeof n=="object")return Rd(n);if(typeof n=="function")return ft(n);throw new Error("Expected function, string, or array as `test`")};function Rd(n){const t=[];let e=-1;for(;++e<n.length;)t[e]=Rn(n[e]);return ft(i);function i(...r){let o=-1;for(;++o<t.length;)if(t[o].apply(this,r))return!0;return!1}}function Md(n){return ft(t);function t(e){return e.tagName===n}}function ft(n){return t;function t(e,i,r){return!!(Pd(e)&&n.call(this,e,typeof i=="number"?i:void 0,r||void 0))}}function Ed(n){return!!(n&&typeof n=="object"&&"type"in n&&n.type==="element"&&"tagName"in n&&typeof n.tagName=="string")}function Pd(n){return n!==null&&typeof n=="object"&&"type"in n&&"tagName"in n}const fi=/\n/g,mi=/[\t ]+/g,Je=Rn("br"),pi=Rn(qd),Id=Rn("p"),hi=Rn("tr"),zd=Rn(["datalist","head","noembed","noframes","noscript","rp","script","style","template","title",Od,jd]),Rr=Rn(["address","article","aside","blockquote","body","caption","center","dd","dialog","dir","dl","dt","div","figure","figcaption","footer","form,","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","legend","li","listing","main","menu","nav","ol","p","plaintext","pre","section","ul","xmp"]);function Fd(n,t){const e=t||{},i="children"in n?n.children:[],r=Rr(n),o=Pr(n,{whitespace:e.whitespace||"normal"}),a=[];(n.type==="text"||n.type==="comment")&&a.push(...Er(n,{breakBefore:!0,breakAfter:!0}));let s=-1;for(;++s<i.length;)a.push(...Mr(i[s],n,{whitespace:o,breakBefore:s?void 0:r,breakAfter:s<i.length-1?Je(i[s+1]):r}));const l=[];let u;for(s=-1;++s<a.length;){const d=a[s];typeof d=="number"?u!==void 0&&d>u&&(u=d):d&&(u!==void 0&&u>-1&&l.push(`
`.repeat(u)||" "),u=-1,l.push(d))}return l.join("")}function Mr(n,t,e){return n.type==="element"?Dd(n,t,e):n.type==="text"?e.whitespace==="normal"?Er(n,e):Nd(n):[]}function Dd(n,t,e){const i=Pr(n,e),r=n.children||[];let o=-1,a=[];if(zd(n))return a;let s,l;for(Je(n)||hi(n)&&di(t,n,hi)?l=`
`:Id(n)?(s=2,l=2):Rr(n)&&(s=1,l=1);++o<r.length;)a=a.concat(Mr(r[o],n,{whitespace:i,breakBefore:o?void 0:s,breakAfter:o<r.length-1?Je(r[o+1]):l}));return pi(n)&&di(t,n,pi)&&a.push("	"),s&&a.unshift(s),l&&a.push(l),a}function Er(n,t){const e=String(n.value),i=[],r=[];let o=0;for(;o<=e.length;){fi.lastIndex=o;const l=fi.exec(e),u=l&&"index"in l?l.index:e.length;i.push(Ld(e.slice(o,u).replace(/[\u061C\u200E\u200F\u202A-\u202E\u2066-\u2069]/g,""),o===0?t.breakBefore:!0,u===e.length?t.breakAfter:!0)),o=u+1}let a=-1,s;for(;++a<i.length;)i[a].charCodeAt(i[a].length-1)===8203||a<i.length-1&&i[a+1].charCodeAt(0)===8203?(r.push(i[a]),s=void 0):i[a]?(typeof s=="number"&&r.push(s),r.push(i[a]),s=0):(a===0||a===i.length-1)&&r.push(0);return r}function Nd(n){return[String(n.value)]}function Ld(n,t,e){const i=[];let r=0,o;for(;r<n.length;){mi.lastIndex=r;const a=mi.exec(n);o=a?a.index:n.length,!r&&!o&&a&&!t&&i.push(""),r!==o&&i.push(n.slice(r,o)),r=a?o+a[0].length:o}return r!==o&&!e&&i.push(""),i.join(" ")}function Pr(n,t){if(n.type==="element"){const e=n.properties||{};switch(n.tagName){case"listing":case"plaintext":case"xmp":return"pre";case"nobr":return"nowrap";case"pre":return e.wrap?"pre-wrap":"pre";case"td":case"th":return e.noWrap?"nowrap":t.whitespace;case"textarea":return"pre-wrap"}}return t.whitespace}function Od(n){return!!(n.properties||{}).hidden}function qd(n){return n.tagName==="td"||n.tagName==="th"}function jd(n){return n.tagName==="dialog"&&!(n.properties||{}).open}const Bd={},Vd=[];function Wd(n){const t=n||Bd;return function(e,i){rt(e,"element",function(r,o){const a=Array.isArray(r.properties.className)?r.properties.className:Vd,s=a.includes("language-math"),l=a.includes("math-display"),u=a.includes("math-inline");let d=l;if(!s&&!l&&!u)return;let c=o[o.length-1],m=r;if(r.tagName==="code"&&s&&c&&c.type==="element"&&c.tagName==="pre"&&(m=c,c=o[o.length-2],d=!0),!c)return;const f=Fd(m,{whitespace:"pre"});let g;try{g=St.renderToString(f,{...t,displayMode:d,throwOnError:!0})}catch(w){const b=w,x=b.name.toLowerCase();i.message("Could not render math with KaTeX",{ancestors:[...o,r],cause:b,place:r.position,ruleId:x,source:"rehype-katex"});try{g=St.renderToString(f,{...t,displayMode:d,strict:"ignore",throwOnError:!1})}catch{g=[{type:"element",tagName:"span",properties:{className:["katex-error"],style:"color:"+(t.errorColor||"#cc0000"),title:String(w)},children:[{type:"text",value:f}]}]}}typeof g=="string"&&(g=Ad(g).children);const y=c.children.indexOf(m);return c.children.splice(y,1,...g),qi})}}function Hd(n,t){const e={type:"element",tagName:"blockquote",properties:{},children:n.wrap(n.all(t),!0)};return n.patch(t,e),n.applyData(t,e)}function Ud(n,t){const e={type:"element",tagName:"br",properties:{},children:[]};return n.patch(t,e),[n.applyData(t,e),{type:"text",value:`
`}]}function Xd(n,t){const e=t.value?t.value+`
`:"",i={};t.lang&&(i.className=["language-"+t.lang]);let r={type:"element",tagName:"code",properties:i,children:[{type:"text",value:e}]};return t.meta&&(r.data={meta:t.meta}),n.patch(t,r),r=n.applyData(t,r),r={type:"element",tagName:"pre",properties:{},children:[r]},n.patch(t,r),r}function Gd(n,t){const e={type:"element",tagName:"del",properties:{},children:n.all(t)};return n.patch(t,e),n.applyData(t,e)}function Qd(n,t){const e={type:"element",tagName:"em",properties:{},children:n.all(t)};return n.patch(t,e),n.applyData(t,e)}function Yd(n,t){const e=typeof n.options.clobberPrefix=="string"?n.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),r=Ln(i.toLowerCase()),o=n.footnoteOrder.indexOf(i);let a,s=n.footnoteCounts.get(i);s===void 0?(s=0,n.footnoteOrder.push(i),a=n.footnoteOrder.length):a=o+1,s+=1,n.footnoteCounts.set(i,s);const l={type:"element",tagName:"a",properties:{href:"#"+e+"fn-"+r,id:e+"fnref-"+r+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};n.patch(t,l);const u={type:"element",tagName:"sup",properties:{},children:[l]};return n.patch(t,u),n.applyData(t,u)}function Kd(n,t){const e={type:"element",tagName:"h"+t.depth,properties:{},children:n.all(t)};return n.patch(t,e),n.applyData(t,e)}function Jd(n,t){if(n.options.allowDangerousHtml){const e={type:"raw",value:t.value};return n.patch(t,e),n.applyData(t,e)}}function Ir(n,t){const e=t.referenceType;let i="]";if(e==="collapsed"?i+="[]":e==="full"&&(i+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+i}];const r=n.all(t),o=r[0];o&&o.type==="text"?o.value="["+o.value:r.unshift({type:"text",value:"["});const a=r[r.length-1];return a&&a.type==="text"?a.value+=i:r.push({type:"text",value:i}),r}function Zd(n,t){const e=String(t.identifier).toUpperCase(),i=n.definitionById.get(e);if(!i)return Ir(n,t);const r={src:Ln(i.url||""),alt:t.alt};i.title!==null&&i.title!==void 0&&(r.title=i.title);const o={type:"element",tagName:"img",properties:r,children:[]};return n.patch(t,o),n.applyData(t,o)}function nf(n,t){const e={src:Ln(t.url)};t.alt!==null&&t.alt!==void 0&&(e.alt=t.alt),t.title!==null&&t.title!==void 0&&(e.title=t.title);const i={type:"element",tagName:"img",properties:e,children:[]};return n.patch(t,i),n.applyData(t,i)}function ef(n,t){const e={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};n.patch(t,e);const i={type:"element",tagName:"code",properties:{},children:[e]};return n.patch(t,i),n.applyData(t,i)}function tf(n,t){const e=String(t.identifier).toUpperCase(),i=n.definitionById.get(e);if(!i)return Ir(n,t);const r={href:Ln(i.url||"")};i.title!==null&&i.title!==void 0&&(r.title=i.title);const o={type:"element",tagName:"a",properties:r,children:n.all(t)};return n.patch(t,o),n.applyData(t,o)}function rf(n,t){const e={href:Ln(t.url)};t.title!==null&&t.title!==void 0&&(e.title=t.title);const i={type:"element",tagName:"a",properties:e,children:n.all(t)};return n.patch(t,i),n.applyData(t,i)}function af(n,t,e){const i=n.all(t),r=e?of(e):zr(t),o={},a=[];if(typeof t.checked=="boolean"){const d=i[0];let c;d&&d.type==="element"&&d.tagName==="p"?c=d:(c={type:"element",tagName:"p",properties:{},children:[]},i.unshift(c)),c.children.length>0&&c.children.unshift({type:"text",value:" "}),c.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let s=-1;for(;++s<i.length;){const d=i[s];(r||s!==0||d.type!=="element"||d.tagName!=="p")&&a.push({type:"text",value:`
`}),d.type==="element"&&d.tagName==="p"&&!r?a.push(...d.children):a.push(d)}const l=i[i.length-1];l&&(r||l.type!=="element"||l.tagName!=="p")&&a.push({type:"text",value:`
`});const u={type:"element",tagName:"li",properties:o,children:a};return n.patch(t,u),n.applyData(t,u)}function of(n){let t=!1;if(n.type==="list"){t=n.spread||!1;const e=n.children;let i=-1;for(;!t&&++i<e.length;)t=zr(e[i])}return t}function zr(n){const t=n.spread;return t??n.children.length>1}function sf(n,t){const e={},i=n.all(t);let r=-1;for(typeof t.start=="number"&&t.start!==1&&(e.start=t.start);++r<i.length;){const a=i[r];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){e.className=["contains-task-list"];break}}const o={type:"element",tagName:t.ordered?"ol":"ul",properties:e,children:n.wrap(i,!0)};return n.patch(t,o),n.applyData(t,o)}function lf(n,t){const e={type:"element",tagName:"p",properties:{},children:n.all(t)};return n.patch(t,e),n.applyData(t,e)}function uf(n,t){const e={type:"root",children:n.wrap(n.all(t))};return n.patch(t,e),n.applyData(t,e)}function cf(n,t){const e={type:"element",tagName:"strong",properties:{},children:n.all(t)};return n.patch(t,e),n.applyData(t,e)}const Fr=Nr("end"),Dr=Nr("start");function Nr(n){return t;function t(e){const i=e&&e.position&&e.position[n]||{};if(typeof i.line=="number"&&i.line>0&&typeof i.column=="number"&&i.column>0)return{line:i.line,column:i.column,offset:typeof i.offset=="number"&&i.offset>-1?i.offset:void 0}}}function df(n){const t=Dr(n),e=Fr(n);if(t&&e)return{start:t,end:e}}function ff(n,t){const e=n.all(t),i=e.shift(),r=[];if(i){const a={type:"element",tagName:"thead",properties:{},children:n.wrap([i],!0)};n.patch(t.children[0],a),r.push(a)}if(e.length>0){const a={type:"element",tagName:"tbody",properties:{},children:n.wrap(e,!0)},s=Dr(t.children[1]),l=Fr(t.children[t.children.length-1]);s&&l&&(a.position={start:s,end:l}),r.push(a)}const o={type:"element",tagName:"table",properties:{},children:n.wrap(r,!0)};return n.patch(t,o),n.applyData(t,o)}function mf(n,t,e){const i=e?e.children:void 0,o=(i?i.indexOf(t):1)===0?"th":"td",a=e&&e.type==="table"?e.align:void 0,s=a?a.length:t.children.length;let l=-1;const u=[];for(;++l<s;){const c=t.children[l],m={},f=a?a[l]:void 0;f&&(m.align=f);let g={type:"element",tagName:o,properties:m,children:[]};c&&(g.children=n.all(c),n.patch(c,g),g=n.applyData(c,g)),u.push(g)}const d={type:"element",tagName:"tr",properties:{},children:n.wrap(u,!0)};return n.patch(t,d),n.applyData(t,d)}function pf(n,t){const e={type:"element",tagName:"td",properties:{},children:n.all(t)};return n.patch(t,e),n.applyData(t,e)}const gi=9,bi=32;function hf(n){const t=String(n),e=/\r?\n|\r/g;let i=e.exec(t),r=0;const o=[];for(;i;)o.push($i(t.slice(r,i.index),r>0,!0),i[0]),r=i.index+i[0].length,i=e.exec(t);return o.push($i(t.slice(r),r>0,!1)),o.join("")}function $i(n,t,e){let i=0,r=n.length;if(t){let o=n.codePointAt(i);for(;o===gi||o===bi;)i++,o=n.codePointAt(i)}if(e){let o=n.codePointAt(r-1);for(;o===gi||o===bi;)r--,o=n.codePointAt(r-1)}return r>i?n.slice(i,r):""}function gf(n,t){const e={type:"text",value:hf(String(t.value))};return n.patch(t,e),n.applyData(t,e)}function bf(n,t){const e={type:"element",tagName:"hr",properties:{},children:[]};return n.patch(t,e),n.applyData(t,e)}const $f={blockquote:Hd,break:Ud,code:Xd,delete:Gd,emphasis:Qd,footnoteReference:Yd,heading:Kd,html:Jd,imageReference:Zd,image:nf,inlineCode:ef,linkReference:tf,link:rf,listItem:af,list:sf,paragraph:lf,root:uf,strong:cf,table:ff,tableCell:pf,tableRow:mf,text:gf,thematicBreak:bf,toml:le,yaml:le,definition:le,footnoteDefinition:le};function le(){}const Lr=-1,xe=0,Qn=1,he=2,mt=3,pt=4,ht=5,gt=6,Or=7,qr=8,yi=typeof self=="object"?self:globalThis,yf=(n,t)=>{const e=(r,o)=>(n.set(o,r),r),i=r=>{if(n.has(r))return n.get(r);const[o,a]=t[r];switch(o){case xe:case Lr:return e(a,r);case Qn:{const s=e([],r);for(const l of a)s.push(i(l));return s}case he:{const s=e({},r);for(const[l,u]of a)s[i(l)]=i(u);return s}case mt:return e(new Date(a),r);case pt:{const{source:s,flags:l}=a;return e(new RegExp(s,l),r)}case ht:{const s=e(new Map,r);for(const[l,u]of a)s.set(i(l),i(u));return s}case gt:{const s=e(new Set,r);for(const l of a)s.add(i(l));return s}case Or:{const{name:s,message:l}=a;return e(new yi[s](l),r)}case qr:return e(BigInt(a),r);case"BigInt":return e(Object(BigInt(a)),r);case"ArrayBuffer":return e(new Uint8Array(a).buffer,a);case"DataView":{const{buffer:s}=new Uint8Array(a);return e(new DataView(s),a)}}return e(new yi[o](a),r)};return i},xi=n=>yf(new Map,n)(0),In="",{toString:xf}={},{keys:_f}=Object,Un=n=>{const t=typeof n;if(t!=="object"||!n)return[xe,t];const e=xf.call(n).slice(8,-1);switch(e){case"Array":return[Qn,In];case"Object":return[he,In];case"Date":return[mt,In];case"RegExp":return[pt,In];case"Map":return[ht,In];case"Set":return[gt,In];case"DataView":return[Qn,e]}return e.includes("Array")?[Qn,e]:e.includes("Error")?[Or,e]:[he,e]},ue=([n,t])=>n===xe&&(t==="function"||t==="symbol"),Sf=(n,t,e,i)=>{const r=(a,s)=>{const l=i.push(a)-1;return e.set(s,l),l},o=a=>{if(e.has(a))return e.get(a);let[s,l]=Un(a);switch(s){case xe:{let d=a;switch(l){case"bigint":s=qr,d=a.toString();break;case"function":case"symbol":if(n)throw new TypeError("unable to serialize "+l);d=null;break;case"undefined":return r([Lr],a)}return r([s,d],a)}case Qn:{if(l){let m=a;return l==="DataView"?m=new Uint8Array(a.buffer):l==="ArrayBuffer"&&(m=new Uint8Array(a)),r([l,[...m]],a)}const d=[],c=r([s,d],a);for(const m of a)d.push(o(m));return c}case he:{if(l)switch(l){case"BigInt":return r([l,a.toString()],a);case"Boolean":case"Number":case"String":return r([l,a.valueOf()],a)}if(t&&"toJSON"in a)return o(a.toJSON());const d=[],c=r([s,d],a);for(const m of _f(a))(n||!ue(Un(a[m])))&&d.push([o(m),o(a[m])]);return c}case mt:return r([s,a.toISOString()],a);case pt:{const{source:d,flags:c}=a;return r([s,{source:d,flags:c}],a)}case ht:{const d=[],c=r([s,d],a);for(const[m,f]of a)(n||!(ue(Un(m))||ue(Un(f))))&&d.push([o(m),o(f)]);return c}case gt:{const d=[],c=r([s,d],a);for(const m of a)(n||!ue(Un(m)))&&d.push(o(m));return c}}const{message:u}=a;return r([s,{name:l,message:u}],a)};return o},_i=(n,{json:t,lossy:e}={})=>{const i=[];return Sf(!(t||e),!!t,new Map,i)(n),i},ge=typeof structuredClone=="function"?(n,t)=>t&&("json"in t||"lossy"in t)?xi(_i(n,t)):structuredClone(n):(n,t)=>xi(_i(n,t));function vf(n,t){const e=[{type:"text",value:"↩"}];return t>1&&e.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),e}function kf(n,t){return"Back to reference "+(n+1)+(t>1?"-"+t:"")}function wf(n){const t=typeof n.options.clobberPrefix=="string"?n.options.clobberPrefix:"user-content-",e=n.options.footnoteBackContent||vf,i=n.options.footnoteBackLabel||kf,r=n.options.footnoteLabel||"Footnotes",o=n.options.footnoteLabelTagName||"h2",a=n.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let l=-1;for(;++l<n.footnoteOrder.length;){const u=n.footnoteById.get(n.footnoteOrder[l]);if(!u)continue;const d=n.all(u),c=String(u.identifier).toUpperCase(),m=Ln(c.toLowerCase());let f=0;const g=[],y=n.footnoteCounts.get(c);for(;y!==void 0&&++f<=y;){g.length>0&&g.push({type:"text",value:" "});let x=typeof e=="string"?e:e(l,f);typeof x=="string"&&(x={type:"text",value:x}),g.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+m+(f>1?"-"+f:""),dataFootnoteBackref:"",ariaLabel:typeof i=="string"?i:i(l,f),className:["data-footnote-backref"]},children:Array.isArray(x)?x:[x]})}const w=d[d.length-1];if(w&&w.type==="element"&&w.tagName==="p"){const x=w.children[w.children.length-1];x&&x.type==="text"?x.value+=" ":w.children.push({type:"text",value:" "}),w.children.push(...g)}else d.push(...g);const b={type:"element",tagName:"li",properties:{id:t+"fn-"+m},children:n.wrap(d,!0)};n.patch(u,b),s.push(b)}if(s.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...ge(a),id:"footnote-label"},children:[{type:"text",value:r}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:n.wrap(s,!0)},{type:"text",value:`
`}]}}const Ze={}.hasOwnProperty,Cf={};function Af(n,t){const e=t||Cf,i=new Map,r=new Map,o=new Map,a={...$f,...e.handlers},s={all:u,applyData:Rf,definitionById:i,footnoteById:r,footnoteCounts:o,footnoteOrder:[],handlers:a,one:l,options:e,patch:Tf,wrap:Ef};return Xi(n,function(d){if(d.type==="definition"||d.type==="footnoteDefinition"){const c=d.type==="definition"?i:r,m=String(d.identifier).toUpperCase();c.has(m)||c.set(m,d)}}),s;function l(d,c){const m=d.type,f=s.handlers[m];if(Ze.call(s.handlers,m)&&f)return f(s,d,c);if(s.options.passThrough&&s.options.passThrough.includes(m)){if("children"in d){const{children:y,...w}=d,b=ge(w);return b.children=s.all(d),b}return ge(d)}return(s.options.unknownHandler||Mf)(s,d,c)}function u(d){const c=[];if("children"in d){const m=d.children;let f=-1;for(;++f<m.length;){const g=s.one(m[f],d);if(g){if(f&&m[f-1].type==="break"&&(!Array.isArray(g)&&g.type==="text"&&(g.value=Si(g.value)),!Array.isArray(g)&&g.type==="element")){const y=g.children[0];y&&y.type==="text"&&(y.value=Si(y.value))}Array.isArray(g)?c.push(...g):c.push(g)}}}return c}}function Tf(n,t){n.position&&(t.position=df(n))}function Rf(n,t){let e=t;if(n&&n.data){const i=n.data.hName,r=n.data.hChildren,o=n.data.hProperties;if(typeof i=="string")if(e.type==="element")e.tagName=i;else{const a="children"in e?e.children:[e];e={type:"element",tagName:i,properties:{},children:a}}e.type==="element"&&o&&Object.assign(e.properties,ge(o)),"children"in e&&e.children&&r!==null&&r!==void 0&&(e.children=r)}return e}function Mf(n,t){const e=t.data||{},i="value"in t&&!(Ze.call(e,"hProperties")||Ze.call(e,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:n.all(t)};return n.patch(t,i),n.applyData(t,i)}function Ef(n,t){const e=[];let i=-1;for(t&&e.push({type:"text",value:`
`});++i<n.length;)i&&e.push({type:"text",value:`
`}),e.push(n[i]);return t&&n.length>0&&e.push({type:"text",value:`
`}),e}function Si(n){let t=0,e=n.charCodeAt(t);for(;e===9||e===32;)t++,e=n.charCodeAt(t);return n.slice(t)}function vi(n,t){const e=Af(n,t),i=e.one(n,void 0),r=wf(e),o=Array.isArray(i)?{type:"root",children:i}:i||{type:"root",children:[]};return r&&o.children.push({type:"text",value:`
`},r),o}function Pf(n,t){return n&&"run"in n?async function(e,i){const r=vi(e,{file:i,...t});await n.run(r,i)}:function(e,i){return vi(e,{file:i,...n||t})}}const If=`# Logistic Regression Tutorial\r
\r
This tutorial provides a guide to understanding and using the Logistic Regression analysis feature in the application.\r
\r
## Introduction to Logistic Regression\r
\r
Logistic Regression is a statistical method used for predicting the probability of a binary outcome (an outcome that can have only two possible values, e.g., Yes/No, 0/1, True/False). Unlike linear regression, which predicts a continuous outcome, logistic regression uses a logistic function to model the relationship between the independent variables (predictors) and the probability of the dependent variable belonging to a particular class.\r
\r
It is widely used in various fields, including:\r
*   **Medicine:** Predicting the likelihood of a disease based on patient characteristics.\r
*   **Marketing:** Predicting whether a customer will purchase a product.\r
*   **Finance:** Predicting the probability of loan default.\r
\r
## Using the Logistic Regression Component\r
\r
The Logistic Regression component in this application allows you to perform logistic regression analysis on your datasets.\r
\r
1.  **Select Dataset:** Choose the dataset you want to analyze from the "Dataset" dropdown.\r
2.  **Select Independent Variables (X):** Select one or more predictor variables from the "Independent Variables (X)" dropdown. These can be Numeric or Categorical.\r
3.  **Select Dependent Variable (Y - Binary):** Select a binary outcome variable from the "Dependent Variable (Y - Binary)" dropdown. This variable must be binary (e.g., 0/1) or a categorical variable with exactly two distinct values. If you select a categorical variable with two values, you will be prompted to map which value corresponds to '1' and which to '0'.\r
4.  **Select Confidence Level:** Choose the desired confidence level for confidence intervals (e.g., 95%).\r
5.  **Display Options:** Select which visualizations and information you want to display (e.g., regression curve, ROC curve, equation).\r
6.  **Run Logistic Regression:** Click the "Run Logistic Regression" button to perform the analysis.\r
\r
## Computational and Formula Details\r
\r
The component implements multiple logistic regression, handling both numeric and categorical independent variables.\r
\r
### The Logistic Function and Logit Transformation\r
\r
Logistic regression models the relationship between the predictors and the log odds of the outcome. The log odds (or logit) is a linear combination of the independent variables:\r
\r
$logit(p) = \\ln\\left(\\frac{p}{1-p}\\right) = \\beta_0 + \\beta_1 X_1 + \\beta_2 X_2 + \\dots + \\beta_n X_n$\r
\r
Where:\r
*   $p$ is the probability of the dependent variable being 1 (the event of interest).\r
*   $\\beta_0$ is the intercept.\r
*   $\\beta_i$ are the coefficients for the independent variables $X_i$.\r
\r
To get the predicted probability $p$ from the logit, the inverse of the logit transformation (the logistic function) is used:\r
\r
$p = \\frac{1}{1 + e^{-(\\beta_0 + \\beta_1 X_1 + \\dots + \\beta_n X_n)}}$\r
\r
The \`multipleLogisticRegression\` function in src/utils/stats likely implements an iterative algorithm (like Iteratively Reweighted Least Squares - IRLS) to find the coefficients ($\\beta_i$) that maximize the likelihood of observing the given data.\r
\r
### Handling Categorical Independent Variables\r
\r
Categorical independent variables with more than two categories are handled using **dummy coding**. For a categorical variable with $k$ categories, $k-1$ dummy variables are created. One category is chosen as the **base category**, and no dummy variable is created for it. Each dummy variable represents the difference between a specific category and the base category.\r
\r
For example, if a categorical variable "Color" has categories "Red", "Green", and "Blue", and "Blue" is chosen as the base category, two dummy variables might be created:\r
*   Color_Red: 1 if Color is "Red", 0 otherwise.\r
*   Color_Green: 1 if Color is "Green", 0 otherwise.\r
\r
The coefficient for Color_Red would represent the change in log odds when the color is "Red" compared to the base category "Blue", holding other variables constant.\r
\r
The component allows you to select the base category for each categorical independent variable.\r
\r
### Interpretation of Coefficients and Odds Ratios\r
\r
*   **Coefficients ($\\beta_i$):** The coefficients represent the change in the log odds of the outcome for a one-unit increase in the corresponding independent variable, holding other variables constant.\r
*   **Odds Ratio ($e^{\\beta_i}$):** The exponential of a coefficient ($e^{\\beta_i}$) is the odds ratio. It represents the factor by which the odds of the outcome change for a one-unit increase in the independent variable, holding other variables constant.\r
    *   If $e^{\\beta_i} > 1$, the odds of the outcome increase as $X_i$ increases.\r
    *   If $e^{\\beta_i} < 1$, the odds of the outcome decrease as $X_i$ increases.\r
    *   If $e^{\\beta_i} = 1$, the odds of the outcome do not change as $X_i$ increases (the variable has no effect).\r
\r
### Statistical Significance\r
\r
The component calculates standard errors, z-values, and p-values for each coefficient and the intercept.\r
*   **Standard Error:** A measure of the variability of the coefficient estimate.\r
*   **z-value:** The ratio of the coefficient to its standard error ($\\beta_i / SE(\\beta_i)$). It is used to test the null hypothesis that the coefficient is zero.\r
*   **p-value:** The probability of observing a z-value as extreme as, or more extreme than, the calculated value, assuming the null hypothesis is true. A small p-value (typically < 0.05) indicates that the coefficient is statistically significant, meaning there is sufficient evidence to conclude that the independent variable is associated with the outcome.\r
\r
### Model Fit Statistics\r
\r
*   **Log Likelihood:** A measure of how well the model fits the data. Higher values indicate a better fit.\r
*   **AIC (Akaike Information Criterion):** A measure that balances model fit and complexity. Lower values indicate a better model.\r
*   **Pseudo R² (McFadden's):** A measure analogous to R² in linear regression, indicating the proportion of variance in the dependent variable explained by the model. Values range from 0 to 1, with higher values indicating a better fit. However, interpretation differs from linear regression R².\r
\r
### Classification Metrics and Confusion Matrix\r
\r
At a given probability threshold (defaulting to 0.5), the model classifies each observation into one of the two outcome classes. The **Confusion Matrix** summarizes the results:\r
\r
|             | Predicted 0 | Predicted 1 |\r
| :---------- | :---------- | :---------- |\r
| **Actual 0** | True Negatives (TN) | False Positives (FP) |\r
| **Actual 1** | False Negatives (FN) | True Positives (TP) |\r
\r
Based on the confusion matrix, several classification metrics are calculated:\r
*   **Accuracy:** $(TN + TP) / (TN + FP + FN + TP)$ - Overall proportion of correctly classified instances.\r
*   **Precision:** $TP / (TP + FP)$ - Proportion of positive predictions that were actually positive.\r
*   **Recall (Sensitivity):** $TP / (TP + FN)$ - Proportion of actual positives that were correctly identified.\r
*   **Specificity:** $TN / (TN + FP)$ - Proportion of actual negatives that were correctly identified.\r
*   **F1 Score:** $2 \\times \\frac{Precision \\times Recall}{Precision + Recall}$ - Harmonic mean of precision and recall.\r
\r
### ROC Curve and AUC\r
\r
The **ROC (Receiver Operating Characteristic) curve** is a graphical plot that illustrates the diagnostic ability of a binary classifier system as its discrimination threshold is varied. It plots the True Positive Rate (Sensitivity) against the False Positive Rate (1 - Specificity) at various threshold settings.\r
\r
The **AUC (Area Under the ROC Curve)** is a single scalar value that summarizes the overall performance of the classifier across all possible thresholds.\r
*   AUC = 0.5 indicates no discrimination (equivalent to random chance).\r
*   AUC = 1.0 indicates perfect discrimination.\r
*   AUC values between 0.5 and 1.0 indicate varying degrees of discrimination ability.\r
\r
### Prediction Tool\r
\r
The prediction tool allows you to input values for the independent variables and get a predicted probability of the outcome being 1.\r
\r
The prediction is calculated using the logistic function with the estimated coefficients and the input values:\r
\r
$p_{predicted} = \\frac{1}{1 + e^{-(\\beta_0 + \\sum_{i=1}^n \\beta_i X_{i, input})}}$\r
\r
The component also attempts to calculate a confidence interval for the predicted probability, although this is currently limited to models with a single numeric predictor due to computational complexity.\r
\r
## Examples\r
\r
*(Note: Specific examples would require sample data. Below is a conceptual example.)*\r
\r
Suppose you are predicting the probability of a customer clicking on an ad (1 = clicked, 0 = not clicked) based on their age (numeric) and location (categorical: Urban, Suburban, Rural).\r
\r
After running the regression, you might get the following results:\r
\r
| Parameter         | Estimate | p-value | Odds Ratio |\r
| :---------------- | :------- | :------ | :--------- |\r
| Intercept         | -3.5000  | < 0.001 | 0.0302     |\r
| Age               | 0.0500   | < 0.001 | 1.0513     |\r
| Location (Urban)  | 1.2000   | 0.015   | 3.3201     |\r
| Location (Suburban)| 0.8000   | 0.080   | 2.2255     |\r
\r
*(Assuming Rural is the base category)*\r
\r
**Interpretation:**\r
\r
*   **Intercept:** When Age is 0 and Location is Rural (the base category), the log odds of clicking are -3.5000, corresponding to a probability of $1 / (1 + e^{3.5}) \\approx 0.03$.\r
*   **Age:** For each one-year increase in age, the log odds of clicking increase by 0.0500. The odds ratio is 1.0513, meaning the odds of clicking increase by about 5.13% for each additional year of age, holding location constant. This effect is statistically significant (p < 0.001).\r
*   **Location (Urban):** Compared to the Rural base category, being in an Urban location increases the log odds of clicking by 1.2000. The odds ratio is 3.3201, meaning the odds of clicking are about 3.32 times higher in Urban areas compared to Rural areas, holding age constant. This effect is statistically significant (p = 0.015).\r
*   **Location (Suburban):** Compared to the Rural base category, being in a Suburban location increases the log odds of clicking by 0.8000. The odds ratio is 2.2255, meaning the odds of clicking are about 2.23 times higher in Suburban areas compared to Rural areas, holding age constant. This effect is not statistically significant (p = 0.080).\r
\r
**Prediction Example:**\r
\r
To predict the probability of clicking for a 40-year-old in a Suburban location:\r
*   Age = 40\r
*   Location = Suburban (Dummy for Suburban = 1, Dummy for Urban = 0)\r
\r
$logit(p) = -3.5000 + (0.0500 \\times 40) + (1.2000 \\times 0) + (0.8000 \\times 1)$  \r
$logit(p) = -3.5000 + 2.0000 + 0 + 0.8000$\r
$logit(p) = -0.7000$\r
\r
$p = \\frac{1}{1 + e^{-(-0.7000)}} = \\frac{1}{1 + e^{0.7000}} \\approx \\frac{1}{1 + 2.0138} \\approx \\frac{1}{3.0138} \\approx 0.3318$\r
\r
The predicted probability of a 40-year-old in a Suburban location clicking the ad is approximately 0.3318 (or 33.18%). Since this is below 0.5, the model would classify this individual as "not clicked".\r
\r
This tutorial provides a foundation for understanding the Logistic Regression component and its output. For more advanced statistical concepts or troubleshooting, refer to standard statistical resources.\r
`,zf=`# T-Tests and Alternatives: Comprehensive Reference Guide

This comprehensive guide covers t-tests and their non-parametric alternatives, providing detailed explanations, formulas, assumptions, and interpretation guidelines for statistical hypothesis testing.

## Overview

T-tests are parametric statistical tests used to compare means and determine if observed differences are statistically significant. When t-test assumptions are violated, non-parametric alternatives provide robust solutions for hypothesis testing.

## Types of T-Tests

### 1. One-Sample T-Test

**Purpose:** Tests whether a sample mean differs significantly from a known population mean (μ₀).

**Null Hypothesis:** H₀: μ = μ₀  
**Alternative Hypothesis:** H₁: μ ≠ μ₀ (two-tailed) or H₁: μ > μ₀ or H₁: μ < μ₀ (one-tailed)

**Formula:**
$$t = \\frac{\\bar{x} - \\mu_0}{s / \\sqrt{n}}$$

Where:
- $\\bar{x}$ = sample mean
- $\\mu_0$ = hypothesized population mean
- $s$ = sample standard deviation
- $n$ = sample size

**Degrees of Freedom:** df = n - 1

**Effect Size (Cohen's d):**
$$d = \\frac{\\bar{x} - \\mu_0}{s}$$

**Interpretation:**
- Small effect: |d| = 0.2
- Medium effect: |d| = 0.5
- Large effect: |d| = 0.8

### 2. Independent Samples T-Test (Two-Sample)

**Purpose:** Compares means between two independent groups.

**Null Hypothesis:** H₀: μ₁ = μ₂  
**Alternative Hypothesis:** H₁: μ₁ ≠ μ₂ (two-tailed) or H₁: μ₁ > μ₂ or H₁: μ₁ < μ₂ (one-tailed)

#### Equal Variances Assumed (Pooled t-test):
$$t = \\frac{\\bar{x_1} - \\bar{x_2}}{s_p \\sqrt{\\frac{1}{n_1} + \\frac{1}{n_2}}}$$

**Pooled Standard Deviation:**
$$s_p = \\sqrt{\\frac{(n_1-1)s_1^2 + (n_2-1)s_2^2}{n_1 + n_2 - 2}}$$

**Degrees of Freedom:** df = n₁ + n₂ - 2

#### Unequal Variances (Welch's t-test):
$$t = \\frac{\\bar{x_1} - \\bar{x_2}}{\\sqrt{\\frac{s_1^2}{n_1} + \\frac{s_2^2}{n_2}}}$$

**Degrees of Freedom (Welch-Satterthwaite):**
$$df = \\frac{(\\frac{s_1^2}{n_1} + \\frac{s_2^2}{n_2})^2}{\\frac{(s_1^2/n_1)^2}{n_1-1} + \\frac{(s_2^2/n_2)^2}{n_2-1}}$$

**Effect Size (Cohen's d):**
$$d = \\frac{\\bar{x_1} - \\bar{x_2}}{s_p}$$ (for equal variances)

### 3. Paired Samples T-Test (Dependent Samples)

**Purpose:** Compares means of the same subjects measured at two different times or under two different conditions.

**Null Hypothesis:** H₀: μd = 0  
**Alternative Hypothesis:** H₁: μd ≠ 0 (two-tailed) or H₁: μd > 0 or H₁: μd < 0 (one-tailed)

**Formula:**
$$t = \\frac{\\bar{d}}{s_d / \\sqrt{n}}$$

Where:
- $\\bar{d}$ = mean of differences (d = x₁ - x₂)
- $s_d$ = standard deviation of differences
- $n$ = number of pairs

**Degrees of Freedom:** df = n - 1

**Effect Size (Cohen's d):**
$$d = \\frac{\\bar{d}}{s_d}$$

## T-Test Assumptions

### 1. Normality
- **One-sample & Paired:** Sample differences should be approximately normally distributed
- **Independent samples:** Each group should be approximately normally distributed
- **Assessment:** Shapiro-Wilk test, Q-Q plots, histograms
- **Robustness:** T-tests are relatively robust to normality violations with larger samples (n > 30)

### 2. Independence
- **Critical assumption:** Observations must be independent of each other
- **Violations:** Clustered data, repeated measures, time series data
- **Solutions:** Use appropriate statistical models (mixed-effects, repeated measures ANOVA)

### 3. Equal Variances (Homoscedasticity)
- **Applies to:** Independent samples t-test only
- **Assessment:** Levene's test, F-test for equality of variances
- **Solution:** Use Welch's t-test when variances are unequal

### 4. Continuous Data
- **Requirement:** Dependent variable should be measured at interval or ratio level
- **Alternatives:** Non-parametric tests for ordinal data

## Non-Parametric Alternatives

When t-test assumptions are violated, non-parametric tests provide robust alternatives that don't assume normal distributions.

### 1. Sign Test (Alternative to One-Sample T-Test)

**Purpose:** Tests whether the median of a population differs from a hypothesized value.

**Assumptions:**
- Data are paired or single sample
- Measurement scale is at least ordinal
- No assumption of normality

**Procedure:**
1. Calculate differences from hypothesized median
2. Count positive and negative differences (ignore zeros)
3. Use binomial distribution with p = 0.5

**Test Statistic:** Number of positive (or negative) differences
**Distribution:** Binomial(n, 0.5) where n = number of non-zero differences

**When to Use:**
- Small sample sizes
- Severely non-normal data
- Ordinal data
- Presence of outliers

### 2. Mann-Whitney U Test (Alternative to Independent Samples T-Test)

**Purpose:** Tests whether two independent samples come from populations with the same distribution.

**Null Hypothesis:** The distributions of both groups are equal
**Alternative Hypothesis:** The distributions differ (often interpreted as difference in medians)

**Assumptions:**
- Two independent samples
- Ordinal or continuous data
- Similar distribution shapes (for median comparison)

**Test Statistic:**
$$U_1 = n_1 n_2 + \\frac{n_1(n_1+1)}{2} - R_1$$
$$U_2 = n_1 n_2 + \\frac{n_2(n_2+1)}{2} - R_2$$

Where:
- $R_1$, $R_2$ = sum of ranks for groups 1 and 2
- $U = \\min(U_1, U_2)$

**Effect Size (r):**
$$r = \\frac{Z}{\\sqrt{N}}$$
Where Z is the standardized test statistic and N is total sample size.

**Interpretation:**
- Small effect: r = 0.1
- Medium effect: r = 0.3
- Large effect: r = 0.5

### 3. Wilcoxon Signed-Rank Test (Alternative to Paired Samples T-Test)

**Purpose:** Tests whether the median difference between paired observations is zero.

**Assumptions:**
- Paired data
- Differences are continuous
- Differences are symmetrically distributed around the median
- At least ordinal scale

**Procedure:**
1. Calculate differences for each pair
2. Rank absolute differences (exclude zeros)
3. Sum ranks for positive and negative differences
4. Test statistic is the smaller sum

**Test Statistic:** W = smaller of W⁺ (sum of positive ranks) or W⁻ (sum of negative ranks)

**Effect Size (r):**
$$r = \\frac{Z}{\\sqrt{N}}$$

## Choosing the Right Test

### Decision Tree

1. **One sample or two samples?**
   - One sample → One-sample t-test vs. Sign test
   - Two samples → Continue to step 2

2. **Independent or paired samples?**
   - Independent → Independent samples t-test vs. Mann-Whitney U
   - Paired → Paired samples t-test vs. Wilcoxon signed-rank

3. **Check assumptions:**
   - **Normality:** Shapiro-Wilk test, visual inspection
   - **Equal variances:** Levene's test (for independent samples)
   - **Sample size:** Larger samples (n > 30) are more robust

4. **Select test:**
   - All assumptions met → Use t-test
   - Normality violated → Use non-parametric alternative
   - Unequal variances (independent samples) → Use Welch's t-test

### Sample Size Considerations

**T-tests:**
- Minimum: n ≥ 30 per group for robustness
- Power analysis recommended for optimal sample size

**Non-parametric tests:**
- Generally require larger samples for equivalent power
- More robust with small samples when assumptions are violated

## Interpretation Guidelines

### Statistical Significance
- **p-value < 0.05:** Statistically significant (reject H₀)
- **p-value ≥ 0.05:** Not statistically significant (fail to reject H₀)
- **Confidence intervals:** If CI doesn't include null value, result is significant

### Practical Significance
- **Effect size:** Magnitude of difference (Cohen's d, r)
- **Clinical/practical importance:** Consider real-world relevance
- **Confidence intervals:** Provide range of plausible values

### Reporting Results

**T-test example:**
"An independent samples t-test revealed a statistically significant difference between groups, t(48) = 3.21, p = 0.002, Cohen's d = 0.92, indicating a large effect size."

**Non-parametric example:**
"A Mann-Whitney U test indicated that Group 1 scores (Mdn = 75) were significantly higher than Group 2 scores (Mdn = 68), U = 234, p = 0.031, r = 0.31."

## Common Pitfalls and Solutions

### 1. Multiple Comparisons
- **Problem:** Increased Type I error rate with multiple tests
- **Solutions:** Bonferroni correction, FDR control, planned comparisons

### 2. Assumption Violations
- **Problem:** Invalid results when assumptions aren't met
- **Solutions:** Check assumptions, use appropriate alternatives, transform data

### 3. Effect Size Neglect
- **Problem:** Focusing only on p-values
- **Solutions:** Always report effect sizes and confidence intervals

### 4. Sample Size Issues
- **Problem:** Underpowered studies or overpowered trivial effects
- **Solutions:** Conduct power analysis, consider practical significance

## Advanced Considerations

### Robust Statistics
- **Trimmed means:** Remove extreme values
- **Bootstrap methods:** Resampling techniques
- **Permutation tests:** Distribution-free alternatives

### Bayesian Approaches
- **Bayesian t-tests:** Incorporate prior information
- **Bayes factors:** Evidence for H₀ vs. H₁
- **Credible intervals:** Bayesian confidence intervals

This comprehensive guide provides the foundation for understanding and applying t-tests and their alternatives. For specific implementation in statistical software, consult the relevant documentation and consider the context of your research question.
`,Ff=`# ANOVA Tests and Alternatives: Comprehensive Reference Guide

This comprehensive guide covers Analysis of Variance (ANOVA) tests and their non-parametric alternatives, providing detailed explanations, formulas, assumptions, and interpretation guidelines for comparing means across multiple groups.

## Overview

ANOVA is a statistical technique used to test for differences between the means of three or more groups. When ANOVA assumptions are violated, non-parametric alternatives provide robust solutions for multi-group comparisons.

## Types of ANOVA Tests

### 1. One-Way ANOVA (Between-Subjects)

**Purpose:** Tests whether there are statistically significant differences between the means of three or more independent groups.

**Null Hypothesis:** H₀: μ₁ = μ₂ = μ₃ = ... = μₖ (all group means are equal)  
**Alternative Hypothesis:** H₁: At least one group mean differs from the others

**F-Statistic Formula:**
$$F = \\frac{MSB}{MSW} = \\frac{SSB/(k-1)}{SSW/(N-k)}$$

**Sum of Squares Calculations:**
- **Total Sum of Squares:** $SST = \\sum_{i=1}^{N}(X_i - \\bar{X}_{grand})^2$
- **Between-Groups Sum of Squares:** $SSB = \\sum_{j=1}^{k}n_j(\\bar{X}_j - \\bar{X}_{grand})^2$
- **Within-Groups Sum of Squares:** $SSW = SST - SSB = \\sum_{j=1}^{k}\\sum_{i=1}^{n_j}(X_{ij} - \\bar{X}_j)^2$

**Degrees of Freedom:**
- Between-groups: $df_B = k - 1$
- Within-groups: $df_W = N - k$
- Total: $df_T = N - 1$

**Effect Size (Eta-squared):**
$$\\eta^2 = \\frac{SSB}{SST}$$

**Interpretation:**
- Small effect: η² = 0.01
- Medium effect: η² = 0.06
- Large effect: η² = 0.14

### 2. Two-Way ANOVA (Factorial Design)

**Purpose:** Examines the effects of two independent variables (factors) and their interaction on a dependent variable.

**Main Effects and Interaction:**
- **Factor A Main Effect:** $F_A = \\frac{MSA}{MSE}$
- **Factor B Main Effect:** $F_B = \\frac{MSB}{MSE}$
- **Interaction Effect:** $F_{A×B} = \\frac{MS_{A×B}}{MSE}$

**Sum of Squares Decomposition:**
$$SST = SSA + SSB + SS_{A×B} + SSE$$

Where:
- $SSA$ = Sum of squares for Factor A
- $SSB$ = Sum of squares for Factor B
- $SS_{A×B}$ = Sum of squares for interaction
- $SSE$ = Error sum of squares

**Degrees of Freedom:**
- Factor A: $df_A = a - 1$
- Factor B: $df_B = b - 1$
- Interaction: $df_{A×B} = (a-1)(b-1)$
- Error: $df_E = ab(n-1)$

**Partial Eta-squared:**
$$\\eta_p^2 = \\frac{SS_{effect}}{SS_{effect} + SS_{error}}$$

### 3. Repeated Measures ANOVA (Within-Subjects)

**Purpose:** Tests for differences between means when the same subjects are measured under different conditions.

**F-Statistic:**
$$F = \\frac{MS_{between}}{MS_{error}}$$

**Sphericity Assumption:** Variances of differences between all pairs of conditions should be equal.

**Greenhouse-Geisser Correction:** Applied when sphericity is violated:
$$\\epsilon = \\frac{[tr(S)]^2}{(k-1)tr(S^2)}$$

**Effect Size (Partial Eta-squared):**
$$\\eta_p^2 = \\frac{SS_{treatment}}{SS_{treatment} + SS_{error}}$$

### 4. Mixed-Design ANOVA

**Purpose:** Combines between-subjects and within-subjects factors in a single analysis.

**Model Components:**
- Between-subjects factor(s)
- Within-subjects factor(s)
- Interaction between factors
- Subject effects (random factor)

**Assumptions:** Combines requirements of both between and within-subjects designs.

## ANOVA Assumptions

### 1. Normality
- **Requirement:** Dependent variable should be approximately normally distributed within each group
- **Assessment:** Shapiro-Wilk test, Q-Q plots, histograms for each group
- **Robustness:** ANOVA is relatively robust to normality violations with larger samples

### 2. Homogeneity of Variance (Homoscedasticity)
- **Requirement:** Variances should be approximately equal across groups
- **Assessment:** Levene's test, Bartlett's test
- **Violation consequences:** Increased Type I error rate, especially with unequal sample sizes

### 3. Independence of Observations
- **Critical assumption:** Observations must be independent within and between groups
- **Violations:** Clustered data, repeated measures without proper modeling
- **Solutions:** Use appropriate mixed-effects models or repeated measures designs

### 4. Sphericity (Repeated Measures Only)
- **Requirement:** Variances of differences between all pairs of conditions should be equal
- **Assessment:** Mauchly's test of sphericity
- **Corrections:** Greenhouse-Geisser or Huynh-Feldt corrections when violated

## Non-Parametric Alternatives

### 1. Kruskal-Wallis Test (Alternative to One-Way ANOVA)

**Purpose:** Tests whether samples originate from the same distribution when comparing three or more independent groups.

**Test Statistic:**
$$H = \\frac{12}{N(N+1)}\\sum_{i=1}^{k}\\frac{R_i^2}{n_i} - 3(N+1)$$

Where:
- $R_i$ = sum of ranks for group i
- $n_i$ = sample size for group i
- $N$ = total sample size

**Assumptions:**
- Independent observations
- Ordinal or continuous data
- Similar distribution shapes (for median comparison)

**Effect Size (Epsilon-squared):**
$$\\epsilon^2 = \\frac{H - k + 1}{N - k}$$

### 2. Friedman Test (Alternative to Repeated Measures ANOVA)

**Purpose:** Tests for differences between repeated measurements when data violate ANOVA assumptions.

**Test Statistic:**
$$\\chi_r^2 = \\frac{12}{nk(k+1)}\\sum_{j=1}^{k}R_j^2 - 3n(k+1)$$

Where:
- $R_j$ = sum of ranks for condition j
- $n$ = number of subjects
- $k$ = number of conditions

**Assumptions:**
- Related samples (same subjects)
- Ordinal or continuous data
- No specific distribution requirements

**Effect Size (Kendall's W):**
$$W = \\frac{\\chi_r^2}{n(k-1)}$$

### 3. Aligned Rank Transform ANOVA (For Factorial Designs)

**Purpose:** Non-parametric alternative for factorial ANOVA designs.

**Procedure:**
1. Align data by removing estimated effects
2. Rank the aligned data
3. Apply standard ANOVA to ranked data

**Advantages:**
- Handles interactions in factorial designs
- Robust to assumption violations
- Maintains Type I error control

## Post-Hoc Testing

### When to Use Post-Hoc Tests
- **Significant omnibus test:** Only conduct post-hoc tests after significant ANOVA
- **Multiple groups:** When comparing three or more groups
- **Exploratory analysis:** When specific comparisons weren't planned

### Types of Post-Hoc Tests

#### 1. Tukey's Honestly Significant Difference (HSD)
- **Use:** Equal sample sizes, controls familywise error rate
- **Formula:** $HSD = q_{\\alpha,k,df_E}\\sqrt{\\frac{MSE}{n}}$

#### 2. Bonferroni Correction
- **Use:** Unequal sample sizes, very conservative
- **Adjustment:** $\\alpha_{adjusted} = \\frac{\\alpha}{c}$ where c = number of comparisons

#### 3. Scheffé Test
- **Use:** Most conservative, allows any contrast
- **Critical value:** $\\sqrt{(k-1)F_{\\alpha,k-1,df_E}}$

#### 4. Games-Howell Test
- **Use:** Unequal variances and sample sizes
- **Robust:** Doesn't assume homogeneity of variance

### Multiple Comparisons Problem
- **Issue:** Increased Type I error rate with multiple tests
- **Solution:** Use appropriate correction methods
- **Family-wise error rate:** Probability of making at least one Type I error

## Effect Size Measures

### 1. Eta-squared (η²)
- **Formula:** $\\eta^2 = \\frac{SS_{effect}}{SS_{total}}$
- **Interpretation:** Proportion of total variance explained

### 2. Partial Eta-squared (η²ₚ)
- **Formula:** $\\eta_p^2 = \\frac{SS_{effect}}{SS_{effect} + SS_{error}}$
- **Use:** Preferred for factorial designs

### 3. Omega-squared (ω²)
- **Formula:** $\\omega^2 = \\frac{SS_{effect} - (df_{effect})(MS_{error})}{SS_{total} + MS_{error}}$
- **Advantage:** Less biased estimate than eta-squared

## Choosing the Right Test

### Decision Tree

1. **How many groups?**
   - Two groups → Use t-test
   - Three or more → Continue to step 2

2. **Independent or related groups?**
   - Independent → One-way ANOVA vs. Kruskal-Wallis
   - Related → Repeated measures ANOVA vs. Friedman test

3. **How many factors?**
   - One factor → One-way ANOVA
   - Two factors → Two-way ANOVA
   - Mixed design → Mixed ANOVA

4. **Check assumptions:**
   - **Normality:** Test each group separately
   - **Homogeneity:** Levene's test
   - **Sphericity:** Mauchly's test (repeated measures)

5. **Select appropriate test:**
   - All assumptions met → Use parametric ANOVA
   - Assumptions violated → Use non-parametric alternative

## Interpretation Guidelines

### Statistical Significance
- **F-statistic:** Ratio of between-group to within-group variance
- **p-value < 0.05:** Reject null hypothesis (significant difference exists)
- **Effect size:** Always report alongside significance tests

### Practical Significance
- **Consider context:** Statistical significance ≠ practical importance
- **Effect size guidelines:** Use Cohen's conventions as starting point
- **Confidence intervals:** Provide range of plausible effect sizes

### Reporting Results

**One-way ANOVA example:**
"A one-way ANOVA revealed a statistically significant difference between groups, F(2, 87) = 12.34, p < 0.001, η² = 0.22, indicating a large effect size."

**Post-hoc example:**
"Tukey's HSD post-hoc tests indicated that Group A (M = 85.2, SD = 12.1) scored significantly higher than both Group B (M = 78.4, SD = 10.8, p = 0.032) and Group C (M = 72.1, SD = 11.5, p < 0.001)."

## Common Pitfalls and Solutions

### 1. Assumption Violations
- **Problem:** Invalid results when assumptions aren't met
- **Solutions:** Check assumptions, use robust alternatives, transform data

### 2. Multiple Testing Without Correction
- **Problem:** Inflated Type I error rate
- **Solutions:** Use appropriate post-hoc tests, plan comparisons a priori

### 3. Ignoring Effect Sizes
- **Problem:** Overemphasis on statistical significance
- **Solutions:** Always report effect sizes and confidence intervals

### 4. Inappropriate Post-Hoc Testing
- **Problem:** Conducting post-hoc tests after non-significant omnibus test
- **Solutions:** Only use post-hoc tests following significant ANOVA

This comprehensive guide provides the foundation for understanding and applying ANOVA tests and their alternatives. For specific implementation in statistical software, consult the relevant documentation and consider the context of your research question.
`,Df=`# Numerical Descriptives and Distributions: Comprehensive Reference Guide

This comprehensive guide covers descriptive statistics for numerical data, including measures of central tendency, variability, distribution shape, normality testing, and data transformation techniques with detailed mathematical formulations and interpretation guidelines.

## Overview

Descriptive statistics summarize and describe the main features of numerical datasets. Understanding these measures is fundamental for data analysis, hypothesis testing, and making informed decisions based on empirical evidence.

## Measures of Central Tendency

### 1. Arithmetic Mean

**Purpose:** The average value of a dataset, representing the central point around which data values cluster.

**Formula:**
$$\\bar{x} = \\frac{1}{n}\\sum_{i=1}^{n}x_i = \\frac{x_1 + x_2 + ... + x_n}{n}$$

**Properties:**
- Sensitive to outliers
- Minimizes sum of squared deviations
- Used in parametric statistical tests

**Population Mean:**
$$\\mu = \\frac{1}{N}\\sum_{i=1}^{N}x_i$$

### 2. Median

**Purpose:** The middle value when data is arranged in ascending order, representing the 50th percentile.

**Calculation:**
- **Odd n:** $Median = x_{(n+1)/2}$
- **Even n:** $Median = \\frac{x_{n/2} + x_{(n/2)+1}}{2}$

**Properties:**
- Robust to outliers
- Appropriate for skewed distributions
- Divides dataset into two equal halves

### 3. Mode

**Purpose:** The most frequently occurring value(s) in a dataset.

**Types:**
- **Unimodal:** One mode
- **Bimodal:** Two modes
- **Multimodal:** Multiple modes
- **No mode:** All values occur with equal frequency

**Properties:**
- Can be used with categorical data
- May not exist or may not be unique
- Useful for identifying typical values

## Measures of Variability

### 1. Variance

**Sample Variance:**
$$s^2 = \\frac{1}{n-1}\\sum_{i=1}^{n}(x_i - \\bar{x})^2$$

**Population Variance:**
$$\\sigma^2 = \\frac{1}{N}\\sum_{i=1}^{N}(x_i - \\mu)^2$$

**Properties:**
- Measures average squared deviation from mean
- Units are squared original units
- Always non-negative

### 2. Standard Deviation

**Sample Standard Deviation:**
$$s = \\sqrt{\\frac{1}{n-1}\\sum_{i=1}^{n}(x_i - \\bar{x})^2}$$

**Population Standard Deviation:**
$$\\sigma = \\sqrt{\\frac{1}{N}\\sum_{i=1}^{N}(x_i - \\mu)^2}$$

**Properties:**
- Same units as original data
- Approximately 68% of data within 1 SD of mean (normal distribution)
- Approximately 95% of data within 2 SD of mean (normal distribution)

### 3. Range

**Formula:**
$$Range = x_{max} - x_{min}$$

**Properties:**
- Simple measure of spread
- Highly sensitive to outliers
- Easy to calculate and interpret

### 4. Interquartile Range (IQR)

**Formula:**
$$IQR = Q_3 - Q_1$$

Where:
- $Q_1$ = 25th percentile (first quartile)
- $Q_3$ = 75th percentile (third quartile)

**Properties:**
- Robust to outliers
- Contains middle 50% of data
- Used in box plot construction

### 5. Coefficient of Variation

**Formula:**
$$CV = \\frac{s}{\\bar{x}} \\times 100\\%$$

**Properties:**
- Relative measure of variability
- Unitless (allows comparison across different scales)
- Useful when comparing variability of different datasets

## Distribution Shape Measures

### 1. Skewness

**Sample Skewness:**
$$Skewness = \\frac{n}{(n-1)(n-2)}\\sum_{i=1}^{n}\\left(\\frac{x_i - \\bar{x}}{s}\\right)^3$$

**Interpretation:**
- **Skewness = 0:** Symmetric distribution
- **Skewness > 0:** Right-skewed (positive skew)
- **Skewness < 0:** Left-skewed (negative skew)
- **|Skewness| > 2:** Highly skewed

### 2. Kurtosis

**Sample Kurtosis:**
$$Kurtosis = \\frac{n(n+1)}{(n-1)(n-2)(n-3)}\\sum_{i=1}^{n}\\left(\\frac{x_i - \\bar{x}}{s}\\right)^4 - \\frac{3(n-1)^2}{(n-2)(n-3)}$$

**Interpretation:**
- **Kurtosis = 0:** Normal distribution (mesokurtic)
- **Kurtosis > 0:** Heavy-tailed distribution (leptokurtic)
- **Kurtosis < 0:** Light-tailed distribution (platykurtic)

## Percentiles and Quartiles

### Percentile Calculation

**Formula for kth percentile:**
$$P_k = \\text{value below which } k\\% \\text{ of data falls}$$

**Position calculation:**
$$Position = \\frac{k}{100} \\times (n + 1)$$

### Quartiles

- **Q₁ (25th percentile):** $P_{25}$
- **Q₂ (50th percentile):** $P_{50}$ = Median
- **Q₃ (75th percentile):** $P_{75}$

### Five-Number Summary

1. Minimum value
2. First quartile (Q₁)
3. Median (Q₂)
4. Third quartile (Q₃)
5. Maximum value

## Confidence Intervals for Means

### Confidence Interval for Population Mean (σ known)

$$CI = \\bar{x} \\pm z_{\\alpha/2} \\times \\frac{\\sigma}{\\sqrt{n}}$$

### Confidence Interval for Population Mean (σ unknown)

$$CI = \\bar{x} \\pm t_{\\alpha/2,df} \\times \\frac{s}{\\sqrt{n}}$$

Where:
- $df = n - 1$ (degrees of freedom)
- $t_{\\alpha/2,df}$ = critical t-value

**Interpretation:**
- 95% CI: We are 95% confident the true population mean lies within this interval
- Narrower intervals indicate more precise estimates
- Larger samples generally produce narrower intervals

## Normality Testing

### 1. Shapiro-Wilk Test

**Purpose:** Tests whether a sample comes from a normally distributed population.

**Test Statistic:**
$$W = \\frac{\\left(\\sum_{i=1}^{n}a_i x_{(i)}\\right)^2}{\\sum_{i=1}^{n}(x_i - \\bar{x})^2}$$

**Properties:**
- Most powerful test for normality
- Recommended for sample sizes n ≤ 50
- Sensitive to outliers

### 2. Kolmogorov-Smirnov Test

**Purpose:** Tests whether a sample follows a specified distribution.

**Test Statistic:**
$$D = \\max_i |F_n(x_i) - F_0(x_i)|$$

Where:
- $F_n(x)$ = empirical distribution function
- $F_0(x)$ = theoretical distribution function

**Properties:**
- Distribution-free test
- Can test against any continuous distribution
- Less powerful than Shapiro-Wilk for normality

### 3. Anderson-Darling Test

**Purpose:** Tests goodness of fit to a specified distribution with emphasis on tail behavior.

**Test Statistic:**
$$A^2 = -n - \\frac{1}{n}\\sum_{i=1}^{n}(2i-1)[\\ln F(x_i) + \\ln(1-F(x_{n+1-i}))]$$

**Properties:**
- More sensitive to deviations in tails
- Good for detecting departures from normality
- Provides better power than Kolmogorov-Smirnov

## Data Transformation Techniques

### 1. Log Transformation

**Formula:**
$$y = \\ln(x) \\text{ or } y = \\log_{10}(x)$$

**Use Cases:**
- Right-skewed data
- Multiplicative relationships
- Stabilizing variance

### 2. Square Root Transformation

**Formula:**
$$y = \\sqrt{x}$$

**Use Cases:**
- Poisson-distributed data
- Count data with small values
- Moderate right skew

### 3. Box-Cox Transformation

**Formula:**

For $\\lambda \\neq 0$: $$y(\\lambda) = \\frac{x^\\lambda - 1}{\\lambda}$$

For $\\lambda = 0$: $$y(\\lambda) = \\ln(x)$$

**Properties:**
- Optimal λ chosen to maximize normality
- Includes log transformation as special case
- Requires positive values

### 4. Reciprocal Transformation

**Formula:**
$$y = \\frac{1}{x}$$

**Use Cases:**
- Severe right skew
- Rate or time data
- When larger values need compression

## Outlier Detection Methods

### 1. Z-Score Method

**Formula:**
$$z_i = \\frac{x_i - \\bar{x}}{s}$$

**Criterion:** $|z_i| > 2$ or $|z_i| > 3$ (depending on stringency)

### 2. Interquartile Range (IQR) Method

**Outlier boundaries:**
- Lower fence: $Q_1 - 1.5 \\times IQR$
- Upper fence: $Q_3 + 1.5 \\times IQR$

**Extreme outliers:**
- Lower extreme: $Q_1 - 3 \\times IQR$
- Upper extreme: $Q_3 + 3 \\times IQR$

### 3. Modified Z-Score

**Formula:**
$$M_i = \\frac{0.6745(x_i - \\tilde{x})}{MAD}$$

Where:
- $\\tilde{x}$ = median
- $MAD$ = median absolute deviation

**Criterion:** $|M_i| > 3.5$

## Practical Guidelines

### Choosing Appropriate Measures

**For Symmetric Distributions:**
- Central tendency: Mean
- Variability: Standard deviation
- Use parametric methods

**For Skewed Distributions:**
- Central tendency: Median
- Variability: IQR or MAD
- Consider transformations or non-parametric methods

**For Distributions with Outliers:**
- Use robust measures (median, IQR)
- Investigate outliers before removal
- Consider outlier-resistant methods

### Sample Size Considerations

**Small Samples (n < 30):**
- Use t-distribution for confidence intervals
- Be cautious with normality assumptions
- Consider non-parametric alternatives

**Large Samples (n ≥ 30):**
- Central Limit Theorem applies
- Normal approximation often valid
- More robust to assumption violations

### Reporting Guidelines

**Essential Elements:**
- Sample size (n)
- Measures of central tendency and variability
- Confidence intervals when appropriate
- Assessment of distributional assumptions
- Treatment of outliers and missing data

**Example:**
"The sample (n = 120) had a mean age of 45.2 years (SD = 12.8, 95% CI [42.9, 47.5]). The distribution was approximately normal (Shapiro-Wilk p = 0.18) with no extreme outliers identified."

This comprehensive guide provides the foundation for understanding and applying descriptive statistics for numerical data. Proper application of these concepts is essential for accurate data analysis and valid statistical inference.
`,Nf=`# Categorical Descriptives and Association: Comprehensive Reference Guide

This comprehensive guide covers descriptive statistics and association measures for categorical data, including frequency analysis, chi-square tests, measures of association, odds ratios, and specialized tests for categorical data analysis.

## Overview

Categorical data analysis involves examining the distribution and relationships between variables measured on nominal or ordinal scales. These methods are fundamental for understanding patterns, associations, and dependencies in categorical datasets.

## Frequency Tables and Cross-Tabulations

### 1. Frequency Tables

**Purpose:** Summarize the distribution of a single categorical variable.

**Components:**
- **Frequency (f):** Count of observations in each category
- **Relative Frequency:** $p_i = \\frac{f_i}{n}$
- **Percentage:** $\\text{Percentage}_i = p_i \\times 100\\%$
- **Cumulative Frequency:** Running total of frequencies

**Example Structure:**
| Category | Frequency | Relative Frequency | Percentage | Cumulative % |
|----------|-----------|-------------------|------------|--------------|
| A        | 25        | 0.25              | 25%        | 25%          |
| B        | 40        | 0.40              | 40%        | 65%          |
| C        | 35        | 0.35              | 35%        | 100%         |

### 2. Cross-Tabulation (Contingency Tables)

**Purpose:** Examine the relationship between two categorical variables.

**2×2 Contingency Table:**
|           | Variable B |           |         |
|-----------|------------|-----------|---------|
| Variable A| B₁         | B₂        | Total   |
| A₁        | a          | b         | a + b   |
| A₂        | c          | d         | c + d   |
| Total     | a + c      | b + d     | n       |

**Expected Frequencies:**
$$E_{ij} = \\frac{(\\text{Row Total}_i) \\times (\\text{Column Total}_j)}{n}$$

## Proportions and Percentages

### 1. Sample Proportion

**Formula:**
$$\\hat{p} = \\frac{x}{n}$$

Where:
- x = number of successes
- n = sample size

### 2. Confidence Interval for Proportion

**Normal Approximation (large samples):**
$$CI = \\hat{p} \\pm z_{\\alpha/2}\\sqrt{\\frac{\\hat{p}(1-\\hat{p})}{n}}$$

**Wilson Score Interval (better for small samples):**
$$CI = \\frac{\\hat{p} + \\frac{z^2}{2n} \\pm z\\sqrt{\\frac{\\hat{p}(1-\\hat{p})}{n} + \\frac{z^2}{4n^2}}}{1 + \\frac{z^2}{n}}$$

### 3. Difference in Proportions

**Formula:**
$$\\hat{p}_1 - \\hat{p}_2$$

**Confidence Interval:**
$$CI = (\\hat{p}_1 - \\hat{p}_2) \\pm z_{\\alpha/2}\\sqrt{\\frac{\\hat{p}_1(1-\\hat{p}_1)}{n_1} + \\frac{\\hat{p}_2(1-\\hat{p}_2)}{n_2}}$$

## Chi-Square Tests

### 1. Chi-Square Test of Independence

**Purpose:** Tests whether two categorical variables are independent.

**Null Hypothesis:** H₀: Variables are independent  
**Alternative Hypothesis:** H₁: Variables are associated

**Test Statistic:**
$$\\chi^2 = \\sum_{i=1}^{r}\\sum_{j=1}^{c}\\frac{(O_{ij} - E_{ij})^2}{E_{ij}}$$

Where:
- $O_{ij}$ = observed frequency in cell (i,j)
- $E_{ij}$ = expected frequency in cell (i,j)

**Degrees of Freedom:**
$$df = (r-1)(c-1)$$

**Assumptions:**
- Independent observations
- Expected frequencies ≥ 5 in at least 80% of cells
- No expected frequencies < 1

### 2. Chi-Square Goodness of Fit Test

**Purpose:** Tests whether observed frequencies match expected frequencies from a theoretical distribution.

**Test Statistic:**
$$\\chi^2 = \\sum_{i=1}^{k}\\frac{(O_i - E_i)^2}{E_i}$$

**Degrees of Freedom:**
$$df = k - 1 - \\text{number of estimated parameters}$$

### 3. Yates' Continuity Correction

**For 2×2 tables:**
$$\\chi^2 = \\sum_{i,j}\\frac{(|O_{ij} - E_{ij}| - 0.5)^2}{E_{ij}}$$

**Use when:** Any expected frequency is between 5 and 10

## Measures of Association

### 1. Cramér's V

**Purpose:** Measures strength of association between two categorical variables.

**Formula:**
$$V = \\sqrt{\\frac{\\chi^2}{n \\times \\min(r-1, c-1)}}$$

**Interpretation:**
- 0 ≤ V ≤ 1
- V = 0: No association
- V = 1: Perfect association
- Small: V < 0.1, Medium: 0.1 ≤ V < 0.3, Large: V ≥ 0.3

### 2. Phi Coefficient (φ)

**Purpose:** Measures association for 2×2 tables.

**Formula:**
$$\\phi = \\sqrt{\\frac{\\chi^2}{n}} = \\frac{ad - bc}{\\sqrt{(a+b)(c+d)(a+c)(b+d)}}$$

**Properties:**
- -1 ≤ φ ≤ 1
- φ = 0: No association
- |φ| = 1: Perfect association

### 3. Lambda (λ)

**Purpose:** Proportional reduction in error measure based on modal categories.

**Symmetric Lambda:**
$$\\lambda = \\frac{(\\sum f_{r,max} + \\sum f_{c,max}) - (f_{max} + f_{max})}{2n - (f_{max} + f_{max})}$$

**Asymmetric Lambda (Y dependent on X):**
$$\\lambda_{Y|X} = \\frac{\\sum f_{r,max} - f_{c,max}}{n - f_{c,max}}$$

**Interpretation:**
- 0 ≤ λ ≤ 1
- λ = 0: No reduction in error
- λ = 1: Perfect prediction

### 4. Contingency Coefficient

**Formula:**
$$C = \\sqrt{\\frac{\\chi^2}{\\chi^2 + n}}$$

**Properties:**
- 0 ≤ C < 1
- Maximum value depends on table size
- Less interpretable than Cramér's V

## Odds Ratios and Relative Risk

### 1. Odds Ratio (OR)

**For 2×2 table:**
$$OR = \\frac{ad}{bc} = \\frac{\\text{odds in group 1}}{\\text{odds in group 2}}$$

**Log Odds Ratio:**
$$\\ln(OR) = \\ln(a) + \\ln(d) - \\ln(b) - \\ln(c)$$

**Confidence Interval for ln(OR):**
$$CI = \\ln(OR) \\pm z_{\\alpha/2}\\sqrt{\\frac{1}{a} + \\frac{1}{b} + \\frac{1}{c} + \\frac{1}{d}}$$

**Interpretation:**
- OR = 1: No association
- OR > 1: Positive association
- OR < 1: Negative association

### 2. Relative Risk (RR)

**Formula:**
$$RR = \\frac{a/(a+b)}{c/(c+d)} = \\frac{\\text{risk in exposed group}}{\\text{risk in unexposed group}}$$

**Confidence Interval for ln(RR):**
$$CI = \\ln(RR) \\pm z_{\\alpha/2}\\sqrt{\\frac{1}{a} - \\frac{1}{a+b} + \\frac{1}{c} - \\frac{1}{c+d}}$$

**Interpretation:**
- RR = 1: No difference in risk
- RR > 1: Increased risk in exposed group
- RR < 1: Decreased risk in exposed group

### 3. Number Needed to Treat (NNT)

**Formula:**
$$NNT = \\frac{1}{|p_1 - p_2|} = \\frac{1}{\\text{Absolute Risk Reduction}}$$

**Interpretation:** Number of patients that need to be treated to prevent one additional adverse outcome.

## Specialized Tests for Categorical Data

### 1. Fisher's Exact Test

**Purpose:** Exact test for 2×2 tables when chi-square assumptions are violated.

**Test Statistic:** Uses hypergeometric distribution

**Probability:**
$$P = \\frac{(a+b)!(c+d)!(a+c)!(b+d)!}{a!b!c!d!n!}$$

**Use When:**
- Small sample sizes
- Expected frequencies < 5
- Need exact p-values

### 2. McNemar's Test

**Purpose:** Tests for changes in paired categorical data (before/after designs).

**Test Statistic:**
$$\\chi^2 = \\frac{(b-c)^2}{b+c}$$

**With Continuity Correction:**
$$\\chi^2 = \\frac{(|b-c|-1)^2}{b+c}$$

**Table Structure:**
|        | After + | After - | Total |
|--------|---------|---------|-------|
| Before +| a       | b       | a+b   |
| Before -| c       | d       | c+d   |
| Total  | a+c     | b+d     | n     |

**Assumptions:**
- Paired observations
- Dichotomous variables
- Large sample (b + c ≥ 25)

### 3. Cochran's Q Test

**Purpose:** Extension of McNemar's test for more than two time points.

**Test Statistic:**
$$Q = \\frac{k(k-1)\\sum_{j=1}^{k}(C_j - \\bar{C})^2}{\\sum_{i=1}^{n}R_i - \\sum_{i=1}^{n}R_i^2}$$

Where:
- k = number of time points
- $C_j$ = column totals
- $R_i$ = row totals

## Effect Size Measures

### 1. Cohen's w

**For goodness of fit:**
$$w = \\sqrt{\\sum_{i=1}^{k}\\frac{(p_{0i} - p_{1i})^2}{p_{0i}}}$$

**For independence:**
$$w = \\sqrt{\\frac{\\chi^2}{n}}$$

**Interpretation:**
- Small: w = 0.1
- Medium: w = 0.3
- Large: w = 0.5

### 2. Cohen's h

**For difference in proportions:**
$$h = 2(\\arcsin\\sqrt{p_1} - \\arcsin\\sqrt{p_2})$$

**Interpretation:**
- Small: h = 0.2
- Medium: h = 0.5
- Large: h = 0.8

## Sample Size and Power Considerations

### 1. Sample Size for Chi-Square Test

**Formula:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{w^2}$$

Where:
- w = effect size (Cohen's w)
- $z_\\beta$ = z-value for desired power

### 2. Sample Size for Proportion

**Single proportion:**
$$n = \\frac{z_{\\alpha/2}^2 \\times p(1-p)}{E^2}$$

**Two proportions:**
$$n = \\frac{2\\bar{p}(1-\\bar{p})(z_{\\alpha/2} + z_\\beta)^2}{(p_1 - p_2)^2}$$

## Practical Guidelines

### Choosing Appropriate Tests

**For Independence:**
- Large samples: Chi-square test
- Small samples: Fisher's exact test
- Ordered categories: Mantel-Haenszel test

**For Paired Data:**
- Two time points: McNemar's test
- Multiple time points: Cochran's Q test

**For Association Strength:**
- 2×2 tables: Phi coefficient, Odds ratio
- Larger tables: Cramér's V
- Ordinal data: Spearman's rank correlation

### Assumption Checking

**Chi-Square Test:**
- Check expected frequencies
- Ensure independence of observations
- Consider continuity correction for 2×2 tables

**Fisher's Exact Test:**
- Use when chi-square assumptions violated
- Computationally intensive for large tables
- Provides exact p-values

### Reporting Guidelines

**Essential Elements:**
- Sample sizes and frequencies
- Test statistics and p-values
- Effect sizes and confidence intervals
- Description of categories and coding

**Example:**
"A chi-square test of independence revealed a significant association between treatment group and outcome, χ²(1, N = 200) = 8.45, p = 0.004, Cramér's V = 0.21, indicating a medium effect size. The odds ratio was 2.34 (95% CI [1.32, 4.15]), suggesting patients in the treatment group had 2.34 times higher odds of positive outcomes."

This comprehensive guide provides the foundation for understanding and applying descriptive statistics and association measures for categorical data analysis.
`,Lf=`# Epidemiological Calculators and Study Design: Comprehensive Reference Guide

This comprehensive guide covers epidemiological study designs, measures of association, diagnostic test evaluation, and sample size calculations for epidemiological research with detailed mathematical formulations and interpretation guidelines.

## Overview

Epidemiology is the study of the distribution and determinants of health-related states in populations. Understanding epidemiological measures and study designs is essential for public health research, clinical decision-making, and evidence-based practice.

## Study Design Types

### 1. Case-Control Studies

**Purpose:** Investigates the association between exposure and disease by comparing cases (with disease) to controls (without disease).

**Design Characteristics:**
- Retrospective approach
- Starts with outcome (disease status)
- Looks backward to exposure
- Efficient for rare diseases
- Cannot calculate incidence directly

**2×2 Table for Case-Control Study:**
|           | Cases | Controls | Total |
|-----------|-------|----------|-------|
| Exposed   | a     | b        | a + b |
| Unexposed | c     | d        | c + d |
| Total     | a + c | b + d    | n     |

### 2. Cohort Studies

**Purpose:** Follows exposed and unexposed groups over time to determine disease incidence.

**Design Characteristics:**
- Prospective or retrospective approach
- Starts with exposure status
- Follows forward to outcome
- Can calculate incidence and relative risk
- Good for common exposures

**Types:**
- **Prospective cohort:** Follow subjects forward in time
- **Retrospective cohort:** Use historical records
- **Ambidirectional cohort:** Combination of both approaches

### 3. Cross-Sectional Studies

**Purpose:** Examines exposure and outcome simultaneously at one point in time.

**Design Characteristics:**
- Snapshot of population
- Prevalence study
- Cannot establish temporal sequence
- Good for descriptive purposes
- Relatively quick and inexpensive

## Measures of Association

### 1. Odds Ratio (OR)

**Formula:**
$$OR = \\frac{a \\times d}{b \\times c} = \\frac{\\text{odds of exposure in cases}}{\\text{odds of exposure in controls}}$$

**Confidence Interval:**
$$CI = \\exp\\left[\\ln(OR) \\pm z_{\\alpha/2}\\sqrt{\\frac{1}{a} + \\frac{1}{b} + \\frac{1}{c} + \\frac{1}{d}}\\right]$$

**Interpretation:**
- OR = 1: No association
- OR > 1: Positive association (exposure increases odds of disease)
- OR < 1: Negative association (exposure decreases odds of disease)

### 2. Relative Risk (RR)

**Formula:**
$$RR = \\frac{a/(a+b)}{c/(c+d)} = \\frac{\\text{incidence in exposed}}{\\text{incidence in unexposed}}$$

**Confidence Interval:**
$$CI = \\exp\\left[\\ln(RR) \\pm z_{\\alpha/2}\\sqrt{\\frac{1}{a} - \\frac{1}{a+b} + \\frac{1}{c} - \\frac{1}{c+d}}\\right]$$

**Interpretation:**
- RR = 1: No difference in risk
- RR > 1: Increased risk in exposed group
- RR < 1: Decreased risk in exposed group

### 3. Risk Difference (RD)

**Formula:**
$$RD = \\frac{a}{a+b} - \\frac{c}{c+d} = I_e - I_u$$

**Confidence Interval:**
$$CI = RD \\pm z_{\\alpha/2}\\sqrt{\\frac{a \\times b}{(a+b)^3} + \\frac{c \\times d}{(c+d)^3}}$$

**Interpretation:**
- RD = 0: No difference in risk
- RD > 0: Excess risk in exposed group
- RD < 0: Protective effect of exposure

## Attributable Risk Measures

### 1. Attributable Risk (AR)

**Formula:**
$$AR = I_e - I_u = RD$$

**Attributable Risk Percent (AR%):**
$$AR\\% = \\frac{I_e - I_u}{I_e} \\times 100\\% = \\frac{RR - 1}{RR} \\times 100\\%$$

### 2. Population Attributable Risk (PAR)

**Formula:**
$$PAR = I_t - I_u$$

Where $I_t$ = incidence in total population

**Population Attributable Risk Percent (PAR%):**
$$PAR\\% = \\frac{I_t - I_u}{I_t} \\times 100\\% = \\frac{P_e(RR - 1)}{1 + P_e(RR - 1)} \\times 100\\%$$

Where $P_e$ = proportion of population exposed

### 3. Prevented Fraction

**For protective exposures (RR < 1):**
$$PF = \\frac{I_u - I_e}{I_u} = 1 - RR$$

## Clinical Decision Measures

### 1. Number Needed to Treat (NNT)

**Formula:**
$$NNT = \\frac{1}{|ARR|} = \\frac{1}{|CER - EER|}$$

Where:
- ARR = Absolute Risk Reduction
- CER = Control Event Rate
- EER = Experimental Event Rate

**Interpretation:** Number of patients that need to be treated to prevent one additional adverse outcome.

### 2. Number Needed to Harm (NNH)

**Formula:**
$$NNH = \\frac{1}{ARI} = \\frac{1}{EER - CER}$$

Where ARI = Absolute Risk Increase

**Interpretation:** Number of patients that need to be treated to cause one additional adverse outcome.

## Diagnostic Test Evaluation

### 1. Basic Diagnostic Measures

**2×2 Table for Diagnostic Tests:**
|           | Disease + | Disease - | Total |
|-----------|-----------|-----------|-------|
| Test +    | TP        | FP        | TP+FP |
| Test -    | FN        | TN        | FN+TN |
| Total     | TP+FN     | FP+TN     | n     |

**Sensitivity (True Positive Rate):**
$$Sensitivity = \\frac{TP}{TP + FN}$$

**Specificity (True Negative Rate):**
$$Specificity = \\frac{TN}{TN + FP}$$

**Positive Predictive Value (PPV):**
$$PPV = \\frac{TP}{TP + FP}$$

**Negative Predictive Value (NPV):**
$$NPV = \\frac{TN}{TN + FN}$$

### 2. Likelihood Ratios

**Positive Likelihood Ratio (LR+):**
$$LR+ = \\frac{Sensitivity}{1 - Specificity} = \\frac{TP/(TP+FN)}{FP/(FP+TN)}$$

**Negative Likelihood Ratio (LR-):**
$$LR- = \\frac{1 - Sensitivity}{Specificity} = \\frac{FN/(TP+FN)}{TN/(FP+TN)}$$

**Interpretation:**
- LR+ > 10: Strong evidence for disease
- LR+ 5-10: Moderate evidence for disease
- LR+ 2-5: Weak evidence for disease
- LR+ 1: No diagnostic value
- LR- < 0.1: Strong evidence against disease

### 3. ROC Curve Analysis

**Area Under the Curve (AUC):**
- AUC = 0.5: No discriminatory ability
- AUC = 0.7-0.8: Acceptable discrimination
- AUC = 0.8-0.9: Excellent discrimination
- AUC > 0.9: Outstanding discrimination

**Youden's Index:**
$$J = Sensitivity + Specificity - 1$$

**Optimal cutoff:** Maximizes Youden's Index

### 4. Predictive Values and Prevalence

**Relationship with prevalence:**
$$PPV = \\frac{Sensitivity \\times Prevalence}{Sensitivity \\times Prevalence + (1-Specificity) \\times (1-Prevalence)}$$

$$NPV = \\frac{Specificity \\times (1-Prevalence)}{(1-Sensitivity) \\times Prevalence + Specificity \\times (1-Prevalence)}$$

## Sample Size Calculations for Epidemiological Studies

### 1. Case-Control Studies

**Formula for unmatched case-control:**
$$n = \\frac{(z_{\\alpha/2}\\sqrt{2\\bar{p}(1-\\bar{p})} + z_\\beta\\sqrt{p_1(1-p_1) + p_0(1-p_0)})^2}{(p_1 - p_0)^2}$$

Where:
- $p_1$ = proportion exposed among cases
- $p_0$ = proportion exposed among controls
- $\\bar{p} = (p_1 + p_0)/2$

**For matched case-control (McNemar's test):**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2(\\psi + 1)^2}{(\\psi - 1)^2 \\times p_{10}}$$

Where:
- $\\psi$ = odds ratio
- $p_{10}$ = probability of discordant pair (case exposed, control unexposed)

### 2. Cohort Studies

**Formula for cohort studies:**
$$n = \\frac{(z_{\\alpha/2}\\sqrt{2\\bar{p}(1-\\bar{p})} + z_\\beta\\sqrt{p_1(1-p_1) + p_0(1-p_0)})^2}{(p_1 - p_0)^2}$$

**With unequal group sizes:**
$$n_1 = \\frac{(z_{\\alpha/2}\\sqrt{(1+1/k)\\bar{p}(1-\\bar{p})} + z_\\beta\\sqrt{p_1(1-p_1) + p_0(1-p_0)/k})^2}{(p_1 - p_0)^2}$$

Where k = $n_0/n_1$ (ratio of unexposed to exposed)

### 3. Cross-Sectional Studies

**For single proportion:**
$$n = \\frac{z_{\\alpha/2}^2 \\times p(1-p)}{d^2}$$

Where:
- p = expected proportion
- d = desired precision (margin of error)

**For comparing two proportions:**
$$n = \\frac{2(z_{\\alpha/2} + z_\\beta)^2 \\times \\bar{p}(1-\\bar{p})}{(p_1 - p_2)^2}$$

## Bias and Confounding

### 1. Types of Bias

**Selection Bias:**
- Berkson's bias (hospital-based studies)
- Healthy worker effect
- Loss to follow-up bias

**Information Bias:**
- Recall bias
- Interviewer bias
- Misclassification bias

**Confounding:**
- Variable associated with both exposure and outcome
- Not in causal pathway
- Can be controlled through design or analysis

### 2. Controlling for Confounding

**Stratified Analysis:**
$$OR_{MH} = \\frac{\\sum_i \\frac{a_i d_i}{n_i}}{\\sum_i \\frac{b_i c_i}{n_i}}$$

**Mantel-Haenszel Test:**
$$\\chi^2_{MH} = \\frac{(\\sum_i a_i - \\sum_i E(a_i))^2}{\\sum_i Var(a_i)}$$

## Survival Analysis in Epidemiology

### 1. Kaplan-Meier Estimator

**Survival Function:**
$$\\hat{S}(t) = \\prod_{t_i \\leq t}\\left(1 - \\frac{d_i}{n_i}\\right)$$

Where:
- $d_i$ = number of events at time $t_i$
- $n_i$ = number at risk at time $t_i$

### 2. Hazard Ratio

**From Cox Proportional Hazards Model:**
$$HR = \\frac{h_1(t)}{h_0(t)} = e^{\\beta}$$

**Interpretation:**
- HR = 1: No difference in hazard
- HR > 1: Increased hazard in exposed group
- HR < 1: Decreased hazard in exposed group

## Practical Guidelines

### Study Design Selection

**Case-Control Studies:**
- Rare diseases
- Long latency periods
- Multiple exposures
- Limited resources

**Cohort Studies:**
- Common diseases
- Rare exposures
- Multiple outcomes
- Temporal sequence important

**Cross-Sectional Studies:**
- Prevalence estimation
- Hypothesis generation
- Chronic conditions
- Quick assessment

### Sample Size Considerations

**Factors Affecting Sample Size:**
- Effect size (larger effects need smaller samples)
- Significance level (α)
- Power (1-β)
- Baseline risk/prevalence
- Ratio of exposed to unexposed

### Reporting Guidelines

**Essential Elements:**
- Study design and setting
- Participant selection criteria
- Exposure and outcome definitions
- Statistical methods used
- Confidence intervals for all estimates
- Potential sources of bias

**Example:**
"In this case-control study (n = 500 cases, 500 controls), smoking was associated with lung cancer (OR = 3.2, 95% CI [2.1, 4.9], p < 0.001). The population attributable risk percent was 45%, suggesting that 45% of lung cancer cases in this population could be attributed to smoking."

This comprehensive guide provides the foundation for understanding and applying epidemiological methods and calculations in public health research and clinical practice.
`,Of=`# Sample Size and Power Analysis: Comprehensive Reference Guide

This comprehensive guide covers power analysis fundamentals, sample size calculations for various study designs, effect size determination, and practical considerations for planning statistical studies with detailed mathematical formulations and interpretation guidelines.

## Overview

Sample size and power analysis are crucial components of study design that determine the ability to detect meaningful effects and ensure adequate statistical power. Proper planning prevents underpowered studies and resource waste while maintaining scientific rigor.

## Power Analysis Fundamentals

### 1. Statistical Errors

**Type I Error (α):**
- Probability of rejecting true null hypothesis
- False positive rate
- Typically set at 0.05 (5%)

**Type II Error (β):**
- Probability of failing to reject false null hypothesis
- False negative rate
- Typically set at 0.10 or 0.20

**Statistical Power (1-β):**
- Probability of correctly rejecting false null hypothesis
- Ability to detect true effect
- Typically desired at 0.80 (80%) or 0.90 (90%)

### 2. Effect Size

**Definition:** Standardized measure of the magnitude of difference or association.

**Cohen's Conventions:**
- **Small effect:** d = 0.2, r = 0.1, f = 0.1
- **Medium effect:** d = 0.5, r = 0.3, f = 0.25
- **Large effect:** d = 0.8, r = 0.5, f = 0.4

**Cohen's d (standardized mean difference):**
$$d = \\frac{\\mu_1 - \\mu_2}{\\sigma}$$

**Correlation coefficient (r):**
$$r = \\frac{\\text{covariance}}{\\sigma_x \\sigma_y}$$

**Cohen's f (ANOVA effect size):**
$$f = \\sqrt{\\frac{\\eta^2}{1-\\eta^2}}$$

### 3. Factors Affecting Power

1. **Effect size:** Larger effects easier to detect
2. **Sample size:** Larger samples increase power
3. **Significance level (α):** Lower α decreases power
4. **Variability:** Lower variability increases power
5. **Study design:** More efficient designs increase power

## Sample Size Calculations for Different Study Designs

### 1. One-Sample Tests

**One-Sample t-test (mean):**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2 \\sigma^2}{(\\mu_1 - \\mu_0)^2}$$

**One-Sample z-test (proportion):**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2 p_0(1-p_0)}{(p_1 - p_0)^2}$$

Where:
- $\\mu_0$ = null hypothesis mean
- $\\mu_1$ = alternative hypothesis mean
- $p_0$ = null hypothesis proportion
- $p_1$ = alternative hypothesis proportion

### 2. Two-Sample Tests

**Independent samples t-test (equal variances):**
$$n = \\frac{2(z_{\\alpha/2} + z_\\beta)^2 \\sigma^2}{(\\mu_1 - \\mu_2)^2}$$

**Independent samples t-test (unequal variances):**
$$n_1 = \\frac{(z_{\\alpha/2} + z_\\beta)^2(\\sigma_1^2 + \\sigma_2^2/k)}{(\\mu_1 - \\mu_2)^2}$$

Where k = $n_2/n_1$ (allocation ratio)

**Two-sample z-test (proportions):**
$$n = \\frac{(z_{\\alpha/2}\\sqrt{2\\bar{p}(1-\\bar{p})} + z_\\beta\\sqrt{p_1(1-p_1) + p_2(1-p_2)})^2}{(p_1 - p_2)^2}$$

Where $\\bar{p} = (p_1 + p_2)/2$

**Paired t-test:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2 \\sigma_d^2}{\\mu_d^2}$$

Where:
- $\\sigma_d$ = standard deviation of differences
- $\\mu_d$ = mean difference

### 3. ANOVA Designs

**One-way ANOVA:**
$$n = \\frac{(F_{\\alpha,k-1,\\infty} + F_{\\beta,k-1,\\infty})^2}{f^2}$$

**Simplified formula:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{f^2} + 1$$

**Two-way ANOVA:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{f^2 \\times df_{effect}} + c$$

Where:
- f = Cohen's f effect size
- c = correction factor based on design
- $df_{effect}$ = degrees of freedom for effect

**Repeated Measures ANOVA:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2(1 + (k-1)\\rho)}{k \\times f^2}$$

Where:
- k = number of repeated measures
- ρ = correlation between repeated measures

### 4. Factorial Designs

**2×2 Factorial Design:**
$$n = \\frac{4(z_{\\alpha/2} + z_\\beta)^2 \\sigma^2}{(\\text{main effect})^2}$$

**For interaction effect:**
$$n = \\frac{4(z_{\\alpha/2} + z_\\beta)^2 \\sigma^2}{(\\text{interaction effect})^2}$$

**General factorial design:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2 \\sigma^2 \\times \\text{design factor}}{(\\text{effect size})^2}$$

## Correlation and Regression Studies

### 1. Correlation Analysis

**Sample size for correlation:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{(\\frac{1}{2}\\ln(\\frac{1+r}{1-r}))^2} + 3$$

**Fisher's z-transformation:**
$$z_r = \\frac{1}{2}\\ln\\left(\\frac{1+r}{1-r}\\right)$$

**Power for given sample size:**
$$Power = \\Phi\\left(\\frac{|z_r|\\sqrt{n-3} - z_{\\alpha/2}}{1}\\right)$$

### 2. Linear Regression

**Simple linear regression:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{f^2} + u + 1$$

Where:
- u = number of predictors
- $f^2 = \\frac{R^2}{1-R^2}$ (effect size)

**Multiple regression:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2(1-R^2)}{R^2} + u + 1$$

**Logistic regression:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{p(1-p)(\\ln(OR))^2}$$

Where:
- p = proportion of events
- OR = odds ratio

## Non-Parametric Test Sample Sizes

### 1. Mann-Whitney U Test

**Asymptotic relative efficiency (ARE) = 0.955:**
$$n_{nonparametric} = \\frac{n_{parametric}}{0.955}$$

**Direct formula:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{12(\\Phi^{-1}(P(X > Y)) - 0.5)^2}$$

### 2. Wilcoxon Signed-Rank Test

**ARE = 0.955 relative to paired t-test:**
$$n_{Wilcoxon} = \\frac{n_{t-test}}{0.955}$$

### 3. Kruskal-Wallis Test

**ARE = 0.864 relative to one-way ANOVA:**
$$n_{KW} = \\frac{n_{ANOVA}}{0.864}$$

## Survival Analysis Sample Size Calculations

### 1. Log-Rank Test

**Formula:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{p_1 p_2 (\\ln(HR))^2}$$

Where:
- $p_1, p_2$ = proportions in each group
- HR = hazard ratio

**With censoring:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{p_1 p_2 (\\ln(HR))^2 \\times P_{event}}$$

Where $P_{event}$ = probability of observing event

### 2. Cox Proportional Hazards

**Number of events needed:**
$$E = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{(\\ln(HR))^2}$$

**Total sample size:**
$$n = \\frac{E}{P_{event}}$$

### 3. Exponential Survival

**Equal allocation:**
$$n = \\frac{2(z_{\\alpha/2} + z_\\beta)^2(\\lambda_1 + \\lambda_2)^2}{(\\lambda_1 - \\lambda_2)^2 T}$$

Where:
- λ = hazard rates
- T = study duration

## Cluster Randomized Trials and Multilevel Studies

### 1. Cluster Randomized Trials

**Design effect:**
$$DE = 1 + (m-1)\\rho$$

Where:
- m = average cluster size
- ρ = intracluster correlation coefficient

**Adjusted sample size:**
$$n_{cluster} = n_{individual} \\times DE$$

**Number of clusters:**
$$c = \\frac{n_{cluster}}{m}$$

### 2. Multilevel Models

**Two-level design:**
$$n_{level2} = \\frac{(z_{\\alpha/2} + z_\\beta)^2 \\sigma^2_{total}}{(\\text{effect size})^2 \\times (1-\\rho)}$$

**Three-level design:**
$$n = \\frac{(z_{\\alpha/2} + z_\\beta)^2}{(\\text{effect size})^2} \\times \\text{variance inflation factor}$$

### 3. Stepped Wedge Designs

**Sample size adjustment:**
$$n_{SW} = n_{parallel} \\times \\frac{3(1-\\rho)}{2T\\rho}$$

Where:
- T = number of time periods
- ρ = intracluster correlation

## Post-Hoc Power Analysis Considerations

### 1. Observed Power

**Problems with observed power:**
- Circular reasoning when non-significant
- Misleading interpretation
- Not useful for study interpretation

**Formula:**
$$\\text{Observed Power} = \\Phi\\left(\\frac{|t_{observed}| - t_{\\alpha/2}}{\\sqrt{1}}\\right)$$

### 2. Confidence Intervals

**Preferred approach:**
- Report confidence intervals instead of post-hoc power
- Provides information about precision
- Indicates practical significance

**Relationship to power:**
$$CI = \\text{estimate} \\pm t_{\\alpha/2} \\times SE$$

### 3. Effect Size Estimation

**Retrospective effect size:**
$$d = \\frac{\\bar{x}_1 - \\bar{x}_2}{s_{pooled}}$$

**Confidence interval for effect size:**
$$CI_d = d \\pm t_{\\alpha/2} \\times SE_d$$

## Software Recommendations and Practical Guidelines

### 1. Software Options

**Specialized Software:**
- G*Power (free, comprehensive)
- PASS (commercial, extensive)
- nQuery (commercial, clinical trials)
- SAS/PROC POWER
- R packages (pwr, PowerTOST)

**General Statistical Software:**
- SPSS (limited power analysis)
- Stata (sampsi, power commands)
- SAS (PROC POWER)
- R (multiple packages)

### 2. Practical Considerations

**Planning Phase:**
1. Define primary endpoint clearly
2. Specify effect size of interest
3. Consider feasibility constraints
4. Plan for attrition/dropout
5. Consider multiple comparisons

**Effect Size Determination:**
- Literature review
- Pilot studies
- Clinical significance
- Regulatory guidelines
- Expert opinion

**Sample Size Inflation:**
- Dropout rate: multiply by 1/(1-dropout rate)
- Non-compliance: adjust for dilution effect
- Multiple comparisons: Bonferroni or other corrections

### 3. Reporting Guidelines

**Essential Elements:**
- Primary hypothesis and endpoint
- Effect size and justification
- Power and significance level
- Sample size calculation method
- Assumptions made
- Software used

**Example:**
"Sample size was calculated for a two-sided t-test comparing mean scores between groups. Assuming a medium effect size (Cohen's d = 0.5), α = 0.05, and power = 0.80, a total sample size of 128 participants (64 per group) was required. Accounting for 20% attrition, we aimed to recruit 160 participants."

### 4. Common Mistakes

**Avoid These Errors:**
- Using post-hoc power analysis for interpretation
- Ignoring multiple comparisons
- Unrealistic effect size assumptions
- Inadequate consideration of dropout
- Confusing statistical and clinical significance

This comprehensive guide provides the foundation for understanding and conducting proper sample size and power analysis for various study designs and statistical tests.
`,qf=`# Correlation and Linear Regression Analysis: Comprehensive Reference Guide

This comprehensive guide covers correlation analysis and linear regression modeling, including assumptions, diagnostics, model selection, and interpretation guidelines with detailed mathematical formulations and practical examples.

## Overview

Correlation and regression analysis are fundamental statistical techniques for examining relationships between variables. Correlation measures the strength and direction of linear relationships, while regression models these relationships to make predictions and understand variable dependencies.

## Correlation Analysis

### 1. Pearson Product-Moment Correlation

**Purpose:** Measures the linear relationship between two continuous variables.

**Formula:**
$$r = \\frac{\\sum_{i=1}^{n}(x_i - \\bar{x})(y_i - \\bar{y})}{\\sqrt{\\sum_{i=1}^{n}(x_i - \\bar{x})^2}\\sqrt{\\sum_{i=1}^{n}(y_i - \\bar{y})^2}}$$

**Alternative formula:**
$$r = \\frac{n\\sum xy - \\sum x \\sum y}{\\sqrt{[n\\sum x^2 - (\\sum x)^2][n\\sum y^2 - (\\sum y)^2]}}$$

**Properties:**
- Range: -1 ≤ r ≤ 1
- r = 1: Perfect positive linear relationship
- r = -1: Perfect negative linear relationship
- r = 0: No linear relationship

**Interpretation Guidelines:**
- |r| < 0.3: Weak correlation
- 0.3 ≤ |r| < 0.7: Moderate correlation
- |r| ≥ 0.7: Strong correlation

### 2. Spearman Rank Correlation

**Purpose:** Measures monotonic relationships between variables, robust to outliers.

**Formula:**
$$r_s = 1 - \\frac{6\\sum d_i^2}{n(n^2-1)}$$

Where $d_i$ = difference between ranks of corresponding values.

**When tied ranks exist:**
$$r_s = \\frac{\\sum_{i=1}^{n}(R_x - \\bar{R_x})(R_y - \\bar{R_y})}{\\sqrt{\\sum_{i=1}^{n}(R_x - \\bar{R_x})^2}\\sqrt{\\sum_{i=1}^{n}(R_y - \\bar{R_y})^2}}$$

**Use Cases:**
- Ordinal data
- Non-linear monotonic relationships
- Presence of outliers
- Non-normal distributions

### 3. Correlation Assumptions and Testing

**Pearson Correlation Assumptions:**
- Linear relationship
- Continuous variables
- Bivariate normality
- Homoscedasticity

**Significance Test:**
$$t = \\frac{r\\sqrt{n-2}}{\\sqrt{1-r^2}}$$

With df = n - 2

**Confidence Interval for r:**
$$CI = \\tanh\\left(z_r \\pm \\frac{z_{\\alpha/2}}{\\sqrt{n-3}}\\right)$$

Where $z_r = \\frac{1}{2}\\ln\\left(\\frac{1+r}{1-r}\\right)$ (Fisher's z-transformation)

## Simple Linear Regression

### 1. Linear Regression Model

**Population Model:**
$$Y_i = \\beta_0 + \\beta_1 X_i + \\epsilon_i$$

**Sample Model:**
$$\\hat{Y}_i = b_0 + b_1 X_i$$

Where:
- $\\beta_0$ = population intercept
- $\\beta_1$ = population slope
- $\\epsilon_i$ = error term
- $b_0, b_1$ = sample estimates

### 2. Least Squares Estimation

**Slope:**
$$b_1 = \\frac{\\sum_{i=1}^{n}(x_i - \\bar{x})(y_i - \\bar{y})}{\\sum_{i=1}^{n}(x_i - \\bar{x})^2} = \\frac{SS_{xy}}{SS_{xx}}$$

**Intercept:**
$$b_0 = \\bar{y} - b_1\\bar{x}$$

**Alternative formulas:**
$$b_1 = \\frac{n\\sum xy - \\sum x \\sum y}{n\\sum x^2 - (\\sum x)^2}$$

$$b_0 = \\frac{\\sum y - b_1 \\sum x}{n}$$

### 3. Regression Assumptions

**LINEAR:** Linear relationship between X and Y
**INDEPENDENCE:** Observations are independent
**NORMALITY:** Residuals are normally distributed
**EQUAL VARIANCE:** Homoscedasticity of residuals

**Residual:**
$$e_i = y_i - \\hat{y}_i$$

### 4. Standard Errors and Confidence Intervals

**Standard Error of Slope:**
$$SE(b_1) = \\sqrt{\\frac{MSE}{SS_{xx}}} = \\sqrt{\\frac{MSE}{\\sum(x_i - \\bar{x})^2}}$$

**Standard Error of Intercept:**
$$SE(b_0) = \\sqrt{MSE\\left(\\frac{1}{n} + \\frac{\\bar{x}^2}{SS_{xx}}\\right)}$$

**Mean Square Error:**
$$MSE = \\frac{SSE}{n-2} = \\frac{\\sum(y_i - \\hat{y}_i)^2}{n-2}$$

**Confidence Intervals:**
$$b_1 \\pm t_{\\alpha/2,n-2} \\times SE(b_1)$$
$$b_0 \\pm t_{\\alpha/2,n-2} \\times SE(b_0)$$

## Multiple Linear Regression

### 1. Multiple Regression Model

**Population Model:**
$$Y_i = \\beta_0 + \\beta_1 X_{1i} + \\beta_2 X_{2i} + ... + \\beta_k X_{ki} + \\epsilon_i$$

**Matrix Form:**
$$\\mathbf{Y} = \\mathbf{X}\\boldsymbol{\\beta} + \\boldsymbol{\\epsilon}$$

**Least Squares Solution:**
$$\\hat{\\boldsymbol{\\beta}} = (\\mathbf{X}^T\\mathbf{X})^{-1}\\mathbf{X}^T\\mathbf{Y}$$

### 2. Coefficient Interpretation

**Partial Regression Coefficient:**
- $\\beta_j$ = change in Y for one-unit increase in $X_j$, holding all other variables constant

**Standardized Coefficients:**
$$\\beta_j^* = \\beta_j \\times \\frac{s_{x_j}}{s_y}$$

### 3. Model Selection Techniques

**Forward Selection:**
1. Start with no variables
2. Add variables that significantly improve model
3. Stop when no improvement

**Backward Elimination:**
1. Start with all variables
2. Remove non-significant variables
3. Stop when all remaining variables are significant

**Stepwise Selection:**
- Combination of forward and backward
- Variables can be added or removed at each step

**Selection Criteria:**
- **AIC:** $AIC = n \\ln(SSE/n) + 2k$
- **BIC:** $BIC = n \\ln(SSE/n) + k \\ln(n)$
- **Adjusted R²:** $R_{adj}^2 = 1 - \\frac{(1-R^2)(n-1)}{n-k-1}$

## Model Evaluation and Diagnostics

### 1. Coefficient of Determination

**R-squared:**
$$R^2 = \\frac{SSR}{SST} = 1 - \\frac{SSE}{SST}$$

Where:
- SSR = Sum of Squares Regression
- SSE = Sum of Squares Error
- SST = Total Sum of Squares

**Adjusted R-squared:**
$$R_{adj}^2 = 1 - \\frac{SSE/(n-k-1)}{SST/(n-1)}$$

**Interpretation:**
- R² = proportion of variance in Y explained by X
- Adjusted R² penalizes for additional predictors

### 2. ANOVA for Regression

**F-test for Overall Significance:**
$$F = \\frac{MSR}{MSE} = \\frac{SSR/k}{SSE/(n-k-1)}$$

**ANOVA Table:**
| Source     | df    | SS  | MS      | F       |
|------------|-------|-----|---------|---------|
| Regression | k     | SSR | MSR     | MSR/MSE |
| Error      | n-k-1 | SSE | MSE     |         |
| Total      | n-1   | SST |         |         |

### 3. Residual Analysis

**Standardized Residuals:**
$$r_i = \\frac{e_i}{\\sqrt{MSE}}$$

**Studentized Residuals:**
$$t_i = \\frac{e_i}{\\sqrt{MSE(1-h_{ii})}}$$

Where $h_{ii}$ = leverage value

**Diagnostic Plots:**
- Residuals vs. Fitted Values (linearity, homoscedasticity)
- Normal Q-Q Plot (normality)
- Residuals vs. Leverage (influential points)
- Cook's Distance (influential observations)

### 4. Outliers and Influential Points

**Leverage:**
$$h_{ii} = \\mathbf{x}_i^T(\\mathbf{X}^T\\mathbf{X})^{-1}\\mathbf{x}_i$$

**Cook's Distance:**
$$D_i = \\frac{r_i^2}{k+1} \\times \\frac{h_{ii}}{1-h_{ii}}$$

**Criteria:**
- High leverage: $h_{ii} > 2(k+1)/n$
- Outlier: $|t_i| > 2$ or $|t_i| > 3$
- Influential: $D_i > 4/n$ or $D_i > 1$

## Prediction and Inference

### 1. Prediction Intervals vs. Confidence Intervals

**Confidence Interval for Mean Response:**
$$\\hat{Y}_0 \\pm t_{\\alpha/2,n-2} \\times SE(\\hat{Y}_0)$$

**Prediction Interval for Individual Response:**
$$\\hat{Y}_0 \\pm t_{\\alpha/2,n-2} \\times SE(pred)$$

Where:
$$SE(\\hat{Y}_0) = \\sqrt{MSE \\times \\mathbf{x}_0^T(\\mathbf{X}^T\\mathbf{X})^{-1}\\mathbf{x}_0}$$

$$SE(pred) = \\sqrt{MSE \\times (1 + \\mathbf{x}_0^T(\\mathbf{X}^T\\mathbf{X})^{-1}\\mathbf{x}_0)}$$

### 2. Hypothesis Testing

**Test for Individual Coefficients:**
$$H_0: \\beta_j = 0 \\text{ vs. } H_1: \\beta_j \\neq 0$$

$$t = \\frac{b_j}{SE(b_j)}$$

**Test for Multiple Coefficients:**
$$F = \\frac{(SSE_R - SSE_F)/(df_R - df_F)}{SSE_F/df_F}$$

Where R = reduced model, F = full model

## Practical Guidelines

### 1. Model Building Process

**Steps:**
1. Exploratory data analysis
2. Check assumptions
3. Fit initial model
4. Residual analysis
5. Model refinement
6. Validation

### 2. Assumption Checking

**Linearity:**
- Scatterplots of Y vs. each X
- Residuals vs. fitted values plot

**Independence:**
- Durbin-Watson test for autocorrelation
- Plot residuals vs. time (if applicable)

**Normality:**
- Q-Q plot of residuals
- Shapiro-Wilk test
- Histogram of residuals

**Homoscedasticity:**
- Residuals vs. fitted values
- Breusch-Pagan test
- White test

### 3. Common Issues and Solutions

**Multicollinearity:**
- **Detection:** VIF > 10, condition index > 30
- **Solutions:** Remove variables, ridge regression, PCA

**Non-linearity:**
- **Solutions:** Polynomial terms, transformations, splines

**Heteroscedasticity:**
- **Solutions:** Weighted least squares, robust standard errors

**Non-normality:**
- **Solutions:** Transformations, robust regression

### 4. Reporting Guidelines

**Essential Elements:**
- Model equation with coefficients
- R² and adjusted R²
- F-statistic and p-value
- Individual coefficient tests
- Confidence intervals
- Assumption checking results
- Sample size and missing data

**Example:**
"A simple linear regression revealed that study hours significantly predicted exam scores, F(1, 98) = 45.2, p < 0.001, R² = 0.32. The regression equation was: Exam Score = 65.4 + 2.8(Study Hours). For each additional hour of study, exam scores increased by 2.8 points (95% CI [2.0, 3.6])."

This comprehensive guide provides the foundation for understanding and applying correlation and regression analysis in statistical research and data analysis.
`,jf=`# Exploratory Factor Analysis (EFA): Comprehensive Reference Guide

This comprehensive guide covers Exploratory Factor Analysis (EFA), a statistical technique used to identify underlying factors that explain patterns of correlations among observed variables. EFA is essential for data reduction, construct validation, and understanding latent structures in multivariate data.

## Overview

Exploratory Factor Analysis is a multivariate statistical technique that explores the underlying structure of a set of observed variables by identifying common factors that explain the correlations among variables. Unlike Confirmatory Factor Analysis (CFA), EFA does not require prior hypotheses about the factor structure and is used for exploratory purposes to discover the latent dimensions in data.

## Theoretical Foundation

### 1. Factor Model

**Basic Factor Model:**
$$X_i = \\lambda_{i1}F_1 + \\lambda_{i2}F_2 + ... + \\lambda_{im}F_m + \\epsilon_i$$

Where:
- $X_i$ = observed variable i
- $F_j$ = common factor j
- $\\lambda_{ij}$ = factor loading of variable i on factor j
- $\\epsilon_i$ = unique factor (error) for variable i
- m = number of common factors

**Matrix Form:**
$$\\mathbf{X} = \\mathbf{\\Lambda F} + \\mathbf{\\epsilon}$$

Where:
- $\\mathbf{X}$ = vector of observed variables
- $\\mathbf{\\Lambda}$ = matrix of factor loadings
- $\\mathbf{F}$ = vector of common factors
- $\\mathbf{\\epsilon}$ = vector of unique factors

### 2. Correlation Structure

**Reproduced Correlation:**
$$r_{ij} = \\sum_{k=1}^{m} \\lambda_{ik}\\lambda_{jk}$$

**Communality:**
$$h_i^2 = \\sum_{j=1}^{m} \\lambda_{ij}^2$$

**Uniqueness:**
$$u_i^2 = 1 - h_i^2$$

## Factor Extraction Methods

### 1. Principal Component Analysis (PCA)

**Eigenvalue Decomposition:**
$$\\mathbf{R} = \\mathbf{V\\Lambda V'}$$

Where:
- $\\mathbf{R}$ = correlation matrix
- $\\mathbf{V}$ = matrix of eigenvectors
- $\\mathbf{\\Lambda}$ = diagonal matrix of eigenvalues

**Component Scores:**
$$F_j = \\sum_{i=1}^{p} w_{ij}X_i$$

Where $w_{ij}$ are component score coefficients.

### 2. Principal Axis Factoring (PAF)

**Iterative Process:**
1. Initial communality estimates in diagonal of R
2. Extract factors from reduced correlation matrix
3. Compute new communality estimates
4. Repeat until convergence

**Convergence Criterion:**
$$\\sum_{i=1}^{p}(h_i^{2(k+1)} - h_i^{2(k)})^2 < \\epsilon$$

### 3. Maximum Likelihood Estimation

**Likelihood Function:**
$$L = \\frac{1}{(2\\pi)^{np/2}|\\mathbf{\\Sigma}|^{n/2}} \\exp\\left(-\\frac{1}{2}\\text{tr}(\\mathbf{S\\Sigma}^{-1})\\right)$$

Where:
- $\\mathbf{S}$ = sample covariance matrix
- $\\mathbf{\\Sigma}$ = model-implied covariance matrix
- n = sample size

## Determining Number of Factors

### 1. Kaiser Criterion (Eigenvalue > 1)

**Rule:** Retain factors with eigenvalues > 1.0

**Rationale:** Factors should explain more variance than a single standardized variable.

### 2. Scree Plot

**Visual Method:** Plot eigenvalues and look for "elbow" where slope changes dramatically.

**Cattell's Rule:** Retain factors before the point where eigenvalues level off.

### 3. Parallel Analysis

**Procedure:**
1. Generate random correlation matrices
2. Compute eigenvalues for random data
3. Compare actual eigenvalues to random eigenvalues
4. Retain factors where actual > random

**Decision Rule:**
$$\\lambda_{actual,i} > \\lambda_{random,i}$$

### 4. Variance Explained Criteria

**Total Variance Explained:**
$$\\text{Proportion} = \\frac{\\sum_{j=1}^{m}\\lambda_j}{\\sum_{j=1}^{p}\\lambda_j}$$

**Common Thresholds:**
- Social Sciences: 60-70%
- Natural Sciences: 80-90%

## Factor Rotation Methods

### 1. Orthogonal Rotations

**Varimax Rotation:**
- Maximizes variance of squared loadings within factors
- Produces simple structure with high and low loadings
- Most commonly used orthogonal rotation

**Quartimax Rotation:**
- Maximizes variance of squared loadings within variables
- Tends to produce general factor

**Equamax Rotation:**
- Combination of Varimax and Quartimax
- Balances simplicity of factors and variables

### 2. Oblique Rotations

**Direct Oblimin:**
- Allows factors to correlate
- Parameter δ controls degree of obliqueness
- δ = 0: Most oblique solution

**Promax Rotation:**
- Two-step process: Varimax followed by oblique transformation
- Computationally efficient
- Allows moderate factor correlations

**Factor Correlation Matrix:**
$$\\mathbf{\\Phi} = \\mathbf{T}^{-1}\\mathbf{T}^{-1'}$$

Where $\\mathbf{T}$ is the transformation matrix.

## Factor Interpretation and Scoring

### 1. Factor Loading Interpretation

**Loading Magnitude Guidelines:**
- |λ| ≥ 0.70: Excellent
- |λ| ≥ 0.63: Very good
- |λ| ≥ 0.55: Good
- |λ| ≥ 0.45: Fair
- |λ| ≥ 0.32: Poor

**Cross-loadings:** Variables loading > 0.32 on multiple factors

### 2. Factor Score Computation

**Regression Method:**
$$\\mathbf{F} = \\mathbf{R}^{-1}\\mathbf{\\Lambda}(\\mathbf{\\Lambda}'\\mathbf{R}^{-1}\\mathbf{\\Lambda})^{-1}\\mathbf{X}$$

**Bartlett Method:**
$$\\mathbf{F} = (\\mathbf{\\Lambda}'\\mathbf{\\Psi}^{-1}\\mathbf{\\Lambda})^{-1}\\mathbf{\\Lambda}'\\mathbf{\\Psi}^{-1}\\mathbf{X}$$

Where $\\mathbf{\\Psi}$ is the uniqueness matrix.

**Anderson-Rubin Method:**
- Produces uncorrelated factor scores
- Standardized with mean 0 and variance 1

## Assumptions and Prerequisites

### 1. Sample Size Requirements

**Minimum Requirements:**
- Absolute minimum: 100 cases
- Preferred: 200+ cases
- Rule of thumb: 5-10 cases per variable
- Complex models: 20+ cases per variable

**Kaiser-Meyer-Olkin (KMO) Test:**
$$KMO = \\frac{\\sum_{i \\neq j}r_{ij}^2}{\\sum_{i \\neq j}r_{ij}^2 + \\sum_{i \\neq j}a_{ij}^2}$$

**KMO Interpretation:**
- 0.90+: Marvelous
- 0.80-0.89: Meritorious
- 0.70-0.79: Middling
- 0.60-0.69: Mediocre
- 0.50-0.59: Miserable
- < 0.50: Unacceptable

### 2. Bartlett's Test of Sphericity

**Null Hypothesis:** Correlation matrix is identity matrix

**Test Statistic:**
$$\\chi^2 = -\\left(n - 1 - \\frac{2p + 5}{6}\\right)\\ln|\\mathbf{R}|$$

**Degrees of Freedom:**
$$df = \\frac{p(p-1)}{2}$$

### 3. Data Assumptions

**Linearity:** Relationships between variables should be linear

**Normality:** Multivariate normality preferred for ML estimation

**Homoscedasticity:** Equal variances across groups

**No Extreme Outliers:** Can distort factor structure

## Model Evaluation

### 1. Goodness of Fit Indices

**Chi-Square Test:**
$$\\chi^2 = (n-1)F_{ML}$$

Where $F_{ML}$ is the maximum likelihood fit function.

**Root Mean Square Error of Approximation (RMSEA):**
$$RMSEA = \\sqrt{\\frac{\\chi^2 - df}{df(n-1)}}$$

**Comparative Fit Index (CFI):**
$$CFI = 1 - \\frac{\\max(\\chi^2_{model} - df_{model}, 0)}{\\max(\\chi^2_{baseline} - df_{baseline}, 0)}$$

### 2. Residual Analysis

**Standardized Residuals:**
$$z_{ij} = \\frac{r_{ij} - \\hat{r}_{ij}}{\\sqrt{\\text{Var}(r_{ij} - \\hat{r}_{ij})}}$$

**Acceptable Range:** |z| < 2.58 (p < 0.01)

## Advanced Topics

### 1. Higher-Order Factor Analysis

**Second-Order Model:**
$$\\mathbf{F}_1 = \\mathbf{\\Lambda}_2\\mathbf{F}_2 + \\mathbf{\\epsilon}_1$$

Where $\\mathbf{F}_1$ are first-order factors and $\\mathbf{F}_2$ are second-order factors.

### 2. Exploratory Structural Equation Modeling (ESEM)

**Integration of EFA and SEM:**
- Allows cross-loadings in confirmatory framework
- More flexible than traditional CFA
- Better fit for complex psychological constructs

### 3. Robust Factor Analysis

**Handling Non-normality:**
- Weighted Least Squares (WLS)
- Diagonally Weighted Least Squares (DWLS)
- Robust Maximum Likelihood

## Practical Guidelines

### 1. Planning the Analysis

**Variable Selection:**
- Include 3-5 variables per expected factor
- Avoid highly correlated variables (r > 0.90)
- Consider theoretical relevance

**Sample Size Planning:**
- Minimum 5:1 variable-to-case ratio
- Larger samples for complex structures
- Consider missing data patterns

### 2. Interpretation Guidelines

**Factor Naming:**
- Examine highest loading variables
- Consider theoretical meaning
- Use substantive knowledge
- Avoid over-interpretation

**Reporting Standards:**
- Factor extraction method
- Rotation method
- Number of factors retained
- Variance explained
- Factor loadings matrix
- Factor correlations (if oblique)

### 3. Common Issues and Solutions

**Heywood Cases:**
- Communalities > 1.0
- Solutions: Reduce factors, change extraction method

**Factor Indeterminacy:**
- Multiple solutions possible
- Focus on interpretability and replicability

**Overfactoring/Underfactoring:**
- Use multiple criteria for factor retention
- Consider theoretical expectations

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Sample size and characteristics
- Variable selection rationale
- Extraction method and justification
- Rotation method and rationale
- Factor retention criteria

### 2. Results Section

**Required Information:**
- KMO and Bartlett's test results
- Total variance explained
- Factor loadings table
- Factor correlations (if oblique)
- Factor score reliability

### 3. Example Reporting

"Exploratory factor analysis was conducted using principal axis factoring with Promax rotation on 15 items (N = 300). The Kaiser-Meyer-Olkin measure verified sampling adequacy (KMO = 0.85), and Bartlett's test of sphericity was significant (χ² = 1847.3, df = 105, p < 0.001). Three factors with eigenvalues > 1.0 were retained, explaining 68.4% of the total variance. Factor loadings ranged from 0.45 to 0.89, with no cross-loadings > 0.32."

This comprehensive guide provides the foundation for conducting and interpreting Exploratory Factor Analysis in statistical research and psychometric applications.
`,Bf=`# Confirmatory Factor Analysis (CFA): Comprehensive Reference Guide

This comprehensive guide covers Confirmatory Factor Analysis (CFA), a structural equation modeling technique used to test hypothesized factor structures and validate measurement models. CFA is essential for construct validation, psychometric evaluation, and theory testing in multivariate research.

## Overview

Confirmatory Factor Analysis is a theory-driven statistical technique that tests whether a hypothesized factor structure fits the observed data. Unlike Exploratory Factor Analysis (EFA), CFA requires researchers to specify the number of factors, which variables load on which factors, and the relationships between factors before analysis.

## Theoretical Foundation

### 1. Measurement Model

**Basic CFA Model:**
$$\\mathbf{x} = \\mathbf{\\Lambda_x \\xi} + \\mathbf{\\delta}$$

Where:
- $\\mathbf{x}$ = vector of observed variables
- $\\mathbf{\\Lambda_x}$ = matrix of factor loadings
- $\\mathbf{\\xi}$ = vector of latent factors
- $\\mathbf{\\delta}$ = vector of measurement errors

**Model-Implied Covariance Matrix:**
$$\\mathbf{\\Sigma(\\theta)} = \\mathbf{\\Lambda_x \\Phi \\Lambda_x'} + \\mathbf{\\Theta_\\delta}$$

Where:
- $\\mathbf{\\Phi}$ = factor covariance matrix
- $\\mathbf{\\Theta_\\delta}$ = error covariance matrix
- $\\mathbf{\\theta}$ = vector of model parameters

### 2. Identification Requirements

**Order Condition:**
$$t \\leq \\frac{p(p+1)}{2}$$

Where:
- t = number of free parameters
- p = number of observed variables

**Rank Condition:**
- Information matrix must be positive definite
- No linear dependencies among parameters

**Scale Setting:**
- Fix one loading per factor to 1.0 (reference indicator)
- Or fix factor variance to 1.0

## Estimation Methods

### 1. Maximum Likelihood (ML)

**Fit Function:**
$$F_{ML} = \\ln|\\mathbf{\\Sigma(\\theta)}| + \\text{tr}(\\mathbf{S\\Sigma(\\theta)}^{-1}) - \\ln|\\mathbf{S}| - p$$

**Assumptions:**
- Multivariate normality
- Continuous variables
- Large sample size (N > 200)

**Test Statistic:**
$$\\chi^2 = (N-1)F_{ML}$$

### 2. Weighted Least Squares (WLS)

**Fit Function:**
$$F_{WLS} = (\\mathbf{s} - \\mathbf{\\sigma(\\theta)})'\\mathbf{W}^{-1}(\\mathbf{s} - \\mathbf{\\sigma(\\theta)})$$

Where:
- $\\mathbf{s}$ = vector of sample moments
- $\\mathbf{\\sigma(\\theta)}$ = vector of model-implied moments
- $\\mathbf{W}$ = weight matrix

**Advantages:**
- Robust to non-normality
- Handles ordinal variables
- Asymptotically distribution-free

### 3. Robust Maximum Likelihood

**Satorra-Bentler Correction:**
$$\\chi^2_{SB} = \\frac{\\chi^2_{ML}}{c}$$

Where c is a scaling correction factor.

**Yuan-Bentler Correction:**
- Adjusts both test statistic and standard errors
- Better performance with small samples

## Model Fit Assessment

### 1. Absolute Fit Indices

**Chi-Square Test:**
- $H_0$: Model fits perfectly
- Sensitive to sample size
- Significant result indicates poor fit

**Root Mean Square Error of Approximation (RMSEA):**
$$RMSEA = \\sqrt{\\frac{\\max(\\chi^2 - df, 0)}{df(N-1)}}$$

**Interpretation:**
- < 0.05: Close fit
- 0.05-0.08: Fair fit
- 0.08-0.10: Mediocre fit
- > 0.10: Poor fit

**Standardized Root Mean Square Residual (SRMR):**
$$SRMR = \\sqrt{\\frac{2\\sum_{i \\leq j}(r_{ij} - \\hat{r}_{ij})^2}{p(p+1)}}$$

**Interpretation:**
- < 0.05: Good fit
- < 0.08: Acceptable fit

### 2. Incremental Fit Indices

**Comparative Fit Index (CFI):**
$$CFI = 1 - \\frac{\\max(\\chi^2_t - df_t, 0)}{\\max(\\chi^2_b - df_b, 0)}$$

Where subscripts t and b refer to target and baseline models.

**Tucker-Lewis Index (TLI):**
$$TLI = \\frac{(\\chi^2_b/df_b) - (\\chi^2_t/df_t)}{(\\chi^2_b/df_b) - 1}$$

**Interpretation (CFI & TLI):**
- > 0.95: Excellent fit
- 0.90-0.95: Acceptable fit
- < 0.90: Poor fit

### 3. Information Criteria

**Akaike Information Criterion (AIC):**
$$AIC = \\chi^2 + 2t$$

**Bayesian Information Criterion (BIC):**
$$BIC = \\chi^2 + t \\ln(N)$$

**Model Comparison:**
- Lower values indicate better fit
- Useful for non-nested model comparison

## Model Specification

### 1. Single-Factor Model

**Specification:**
- All indicators load on one factor
- Error terms uncorrelated
- Factor variance fixed or loading fixed

**Identification:**
- Minimum 3 indicators required
- df = p(p-1)/2 - p = p(p-3)/2

### 2. Multi-Factor Model

**Correlated Factors:**

Factor correlation matrix with correlations between factors:
$$\\phi_{12}, \\phi_{13}, \\phi_{23}$$

Where $\\phi_{ij}$ represents the correlation between factors i and j.

**Orthogonal Factors:**
$$\\mathbf{\\Phi} = \\mathbf{I}$$

### 3. Higher-Order Models

**Second-Order Factor:**
$$\\mathbf{\\xi} = \\mathbf{\\Gamma \\eta} + \\mathbf{\\zeta}$$

Where:
- $\\mathbf{\\eta}$ = second-order factors
- $\\mathbf{\\Gamma}$ = second-order loadings
- $\\mathbf{\\zeta}$ = disturbances

## Reliability and Validity

### 1. Reliability Measures

**Composite Reliability (CR):**
$$CR = \\frac{(\\sum \\lambda_i)^2}{(\\sum \\lambda_i)^2 + \\sum \\text{Var}(\\epsilon_i)}$$

**Interpretation:**
- > 0.70: Acceptable
- > 0.80: Good
- > 0.90: Excellent

**Coefficient Omega:**
$$\\omega = \\frac{(\\sum \\lambda_i)^2}{(\\sum \\lambda_i)^2 + \\sum \\psi_{ii} + 2\\sum_{i<j}\\psi_{ij}}$$

### 2. Validity Assessment

**Convergent Validity:**
- Factor loadings > 0.50
- Average Variance Extracted (AVE) > 0.50

**Average Variance Extracted (AVE):**
$$AVE = \\frac{\\sum \\lambda_i^2}{\\sum \\lambda_i^2 + \\sum \\text{Var}(\\epsilon_i)}$$

**Discriminant Validity:**
- $\\sqrt{AVE} >$ factor correlations
- Confidence interval of factor correlation excludes 1.0

### 3. Measurement Invariance

**Configural Invariance:**
- Same factor structure across groups
- No equality constraints

**Metric Invariance:**
- Equal factor loadings across groups
- $\\mathbf{\\Lambda}_1 = \\mathbf{\\Lambda}_2$

**Scalar Invariance:**
- Equal intercepts across groups
- $\\mathbf{\\tau}_1 = \\mathbf{\\tau}_2$

**Strict Invariance:**
- Equal error variances across groups
- $\\mathbf{\\Theta}_1 = \\mathbf{\\Theta}_2$

## Model Modification

### 1. Modification Indices

**Lagrange Multiplier Test:**
$$LM = \\frac{(\\partial F/\\partial \\theta_j)^2}{\\partial^2 F/\\partial \\theta_j^2}$$

**Interpretation:**
- Expected decrease in χ² if parameter is freed
- Values > 3.84 suggest significant improvement

**Expected Parameter Change (EPC):**
- Estimated value of freed parameter
- Helps assess practical significance

### 2. Residual Analysis

**Standardized Residuals:**
$$z_{ij} = \\frac{s_{ij} - \\hat{\\sigma}_{ij}}{\\sqrt{\\text{Var}(s_{ij} - \\hat{\\sigma}_{ij})}}$$

**Acceptable Range:** |z| < 2.58

**Normalized Residuals:**
- Adjusted for sampling variability
- Better for model diagnosis

### 3. Modification Guidelines

**Theoretical Justification:**
- Modifications must make theoretical sense
- Avoid purely statistical modifications

**Cross-Validation:**
- Test modified model on independent sample
- Prevents capitalization on chance

## Advanced Applications

### 1. Multi-Trait Multi-Method (MTMM)

**Correlated Traits-Correlated Methods:**
- Separate trait and method factors
- Assess convergent and discriminant validity

**Model Specification:**
$$x_{ij} = \\lambda_{Ti}\\xi_{Ti} + \\lambda_{Mj}\\xi_{Mj} + \\epsilon_{ij}$$

Where T = trait, M = method.

### 2. Bifactor Models

**General Factor Plus Specific Factors:**
$$x_i = \\lambda_{Gi}\\xi_G + \\lambda_{Si}\\xi_{Si} + \\epsilon_i$$

**Advantages:**
- Models general and specific variance
- Better fit than higher-order models

### 3. Longitudinal CFA

**Measurement Invariance Over Time:**
- Test stability of factor structure
- Assess true change vs. measurement error

**Autoregressive Models:**
$$\\xi_{t+1} = \\beta\\xi_t + \\zeta_{t+1}$$

## Assumptions and Diagnostics

### 1. Sample Size Requirements

**Minimum Requirements:**
- 5-10 observations per parameter
- Absolute minimum: 100-150 cases
- Complex models: 200+ cases

**Power Analysis:**
- Consider effect size (RMSEA)
- Desired power (typically 0.80)
- Significance level (α = 0.05)

### 2. Distributional Assumptions

**Multivariate Normality:**
- Mardia's coefficient < 3.0 (skewness)
- Mardia's coefficient < 10.0 (kurtosis)

**Outlier Detection:**
- Mahalanobis distance
- Leverage values
- Standardized residuals

### 3. Missing Data

**Missing Completely at Random (MCAR):**
- Little's MCAR test
- Missing data patterns

**Handling Methods:**
- Full Information Maximum Likelihood (FIML)
- Multiple imputation
- Avoid listwise deletion

## Practical Guidelines

### 1. Model Building Strategy

**Step 1:** Specify measurement model
**Step 2:** Assess model identification
**Step 3:** Estimate model parameters
**Step 4:** Evaluate model fit
**Step 5:** Modify model if necessary
**Step 6:** Cross-validate results

### 2. Reporting Standards

**Model Specification:**
- Number of factors and indicators
- Fixed and free parameters
- Identification constraints

**Estimation Details:**
- Estimation method
- Software used
- Convergence information

**Fit Assessment:**
- Multiple fit indices
- Confidence intervals
- Model comparison results

### 3. Common Pitfalls

**Specification Errors:**
- Under-identification
- Improper constraints
- Omitted parameters

**Estimation Problems:**
- Non-convergence
- Improper solutions
- Boundary estimates

**Interpretation Issues:**
- Over-reliance on fit indices
- Ignoring theoretical considerations
- Excessive model modification

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Sample characteristics
- Measurement instruments
- Estimation method
- Software and version

### 2. Results Section

**Required Information:**
- Model fit indices with confidence intervals
- Parameter estimates with standard errors
- Reliability and validity evidence
- Model modification details

### 3. Example Reporting

"A confirmatory factor analysis was conducted using maximum likelihood estimation (Mplus 8.0) to test a three-factor measurement model (N = 425). The model demonstrated acceptable fit: χ²(87) = 156.3, p < 0.001; RMSEA = 0.043 (90% CI: 0.032-0.054); CFI = 0.96; SRMR = 0.048. All factor loadings were significant (p < 0.001) and ranged from 0.58 to 0.89. Composite reliability exceeded 0.80 for all factors, and discriminant validity was supported."

This comprehensive guide provides the foundation for conducting and interpreting Confirmatory Factor Analysis in measurement validation and structural equation modeling applications.
`,Vf=`# Survival Analysis: Comprehensive Reference Guide

This comprehensive guide covers survival analysis methods for analyzing time-to-event data, including Kaplan-Meier estimation, log-rank tests, and Cox proportional hazards regression. These techniques are essential for medical research, reliability engineering, and any field studying duration until an event occurs.

## Overview

Survival analysis is a collection of statistical methods for analyzing data where the outcome variable is the time until an event occurs. The event can be death, disease recurrence, equipment failure, or any other occurrence of interest. Survival analysis handles censored data, where the event time is not observed for all subjects.

## Fundamental Concepts

### 1. Survival Function

**Definition:**
$$S(t) = P(T > t)$$

Where T is the random variable representing survival time.

**Properties:**
- $S(0) = 1$ (all subjects alive at start)
- $S(\\infty) = 0$ (eventually all experience event)
- S(t) is non-increasing function

**Relationship to Distribution Function:**
$$S(t) = 1 - F(t)$$

### 2. Hazard Function

**Instantaneous Risk:**
$$h(t) = \\lim_{\\Delta t \\to 0} \\frac{P(t \\leq T < t + \\Delta t | T \\geq t)}{\\Delta t}$$

**Alternative Form:**
$$h(t) = \\frac{f(t)}{S(t)} = -\\frac{d}{dt}\\ln S(t)$$

Where f(t) is the probability density function.

**Cumulative Hazard:**
$$H(t) = \\int_0^t h(u) du = -\\ln S(t)$$

### 3. Types of Censoring

**Right Censoring:**
- Most common type
- Event time is greater than observed time
- Occurs due to study end or loss to follow-up

**Left Censoring:**
- Event occurred before observation began
- Less common in practice

**Interval Censoring:**
- Event occurred within a time interval
- Exact time unknown

## Kaplan-Meier Estimation

### 1. Non-Parametric Estimator

**Kaplan-Meier Formula:**
$$\\hat{S}(t) = \\prod_{t_i \\leq t} \\left(1 - \\frac{d_i}{n_i}\\right)$$

Where:
- $t_i$ = distinct event times
- $d_i$ = number of events at time $t_i$
- $n_i$ = number at risk at time $t_i$

**Step Function:**
- Decreases only at event times
- Constant between events
- Jumps proportional to number of events

### 2. Variance Estimation

**Greenwood's Formula:**
$$\\text{Var}[\\hat{S}(t)] = [\\hat{S}(t)]^2 \\sum_{t_i \\leq t} \\frac{d_i}{n_i(n_i - d_i)}$$

**Standard Error:**
$$SE[\\hat{S}(t)] = \\sqrt{\\text{Var}[\\hat{S}(t)]}$$

**Confidence Intervals:**
$$\\hat{S}(t) \\pm z_{\\alpha/2} \\cdot SE[\\hat{S}(t)]$$

### 3. Median Survival Time

**Definition:**
$$\\hat{t}_{50} = \\inf\\{t: \\hat{S}(t) \\leq 0.5\\}$$

**Confidence Interval:**
- Based on confidence bands for S(t)
- May be wide or undefined if few events

## Log-Rank Test

### 1. Two-Group Comparison

**Null Hypothesis:**
$$H_0: S_1(t) = S_2(t) \\text{ for all } t$$

**Test Statistic:**
$$Z = \\frac{\\sum_{i=1}^k (O_{1i} - E_{1i})}{\\sqrt{\\sum_{i=1}^k V_i}}$$

Where:
- $O_{1i}$ = observed events in group 1 at time $t_i$
- $E_{1i}$ = expected events in group 1 at time $t_i$
- $V_i$ = variance at time $t_i$

**Expected Events:**
$$E_{1i} = \\frac{n_{1i} \\cdot d_i}{n_i}$$

**Variance:**
$$V_i = \\frac{n_{1i} \\cdot n_{2i} \\cdot d_i \\cdot (n_i - d_i)}{n_i^2 \\cdot (n_i - 1)}$$

### 2. Multi-Group Extension

**Chi-Square Statistic:**
$$\\chi^2 = \\mathbf{(O - E)' V^{-1} (O - E)}$$

Where:
- $\\mathbf{O}$ = vector of observed events
- $\\mathbf{E}$ = vector of expected events
- $\\mathbf{V}$ = covariance matrix

**Degrees of Freedom:** g - 1 (where g = number of groups)

### 3. Weighted Log-Rank Tests

**Wilcoxon Test:**
- Weights early failures more heavily
- Weight = $n_i$

**Tarone-Ware Test:**
- Intermediate weighting
- Weight = $\\sqrt{n_i}$

**Fleming-Harrington Test:**
- General weight function: $w(t) = S(t)^p[1-S(t)]^q$

## Cox Proportional Hazards Model

### 1. Model Specification

**Hazard Function:**
$$h(t|x) = h_0(t) \\exp(\\beta_1 x_1 + \\beta_2 x_2 + ... + \\beta_p x_p)$$

Where:
- $h_0(t)$ = baseline hazard function
- $\\mathbf{x}$ = vector of covariates
- $\\boldsymbol{\\beta}$ = vector of regression coefficients

**Hazard Ratio:**
$$HR = \\frac{h(t|x_1)}{h(t|x_0)} = \\exp(\\boldsymbol{\\beta}'\\mathbf{(x_1 - x_0)})$$

### 2. Partial Likelihood

**Likelihood Function:**
$$L(\\boldsymbol{\\beta}) = \\prod_{i=1}^D \\frac{\\exp(\\boldsymbol{\\beta}'\\mathbf{x}_i)}{\\sum_{j \\in R(t_i)} \\exp(\\boldsymbol{\\beta}'\\mathbf{x}_j)}$$

Where:
- D = total number of events
- $R(t_i)$ = risk set at time $t_i$

**Log-Likelihood:**
$$\\ell(\\boldsymbol{\\beta}) = \\sum_{i=1}^D \\left[\\boldsymbol{\\beta}'\\mathbf{x}_i - \\ln\\left(\\sum_{j \\in R(t_i)} \\exp(\\boldsymbol{\\beta}'\\mathbf{x}_j)\\right)\\right]$$

### 3. Parameter Estimation

**Score Function:**
$$U(\\boldsymbol{\\beta}) = \\frac{\\partial \\ell(\\boldsymbol{\\beta})}{\\partial \\boldsymbol{\\beta}} = \\sum_{i=1}^D \\left[\\mathbf{x}_i - \\frac{\\sum_{j \\in R(t_i)} \\mathbf{x}_j \\exp(\\boldsymbol{\\beta}'\\mathbf{x}_j)}{\\sum_{j \\in R(t_i)} \\exp(\\boldsymbol{\\beta}'\\mathbf{x}_j)}\\right]$$

**Information Matrix:**
$$I(\\boldsymbol{\\beta}) = -\\frac{\\partial^2 \\ell(\\boldsymbol{\\beta})}{\\partial \\boldsymbol{\\beta} \\partial \\boldsymbol{\\beta}'}$$

**Newton-Raphson Algorithm:**
$$\\boldsymbol{\\beta}^{(k+1)} = \\boldsymbol{\\beta}^{(k)} + I(\\boldsymbol{\\beta}^{(k)})^{-1} U(\\boldsymbol{\\beta}^{(k)})$$

### 4. Inference

**Wald Test:**
$$Z = \\frac{\\hat{\\beta}_j}{SE(\\hat{\\beta}_j)} \\sim N(0,1)$$

**Likelihood Ratio Test:**
$$LR = 2[\\ell(\\hat{\\boldsymbol{\\beta}}) - \\ell(\\boldsymbol{\\beta}_0)] \\sim \\chi^2_p$$

**Confidence Intervals:**
$$\\hat{\\beta}_j \\pm z_{\\alpha/2} \\cdot SE(\\hat{\\beta}_j)$$

**Hazard Ratio CI:**
$$\\exp(\\hat{\\beta}_j \\pm z_{\\alpha/2} \\cdot SE(\\hat{\\beta}_j))$$

## Model Assessment and Diagnostics

### 1. Proportional Hazards Assumption

**Schoenfeld Residuals:**
$$r_{ij} = \\delta_i \\left[x_{ij} - \\frac{\\sum_{k \\in R(t_i)} x_{kj} \\exp(\\hat{\\boldsymbol{\\beta}}'\\mathbf{x}_k)}{\\sum_{k \\in R(t_i)} \\exp(\\hat{\\boldsymbol{\\beta}}'\\mathbf{x}_k)}\\right]$$

**Test for Proportionality:**
- Plot scaled Schoenfeld residuals vs. time
- Test correlation with time
- Global test: $\\chi^2$ with p degrees of freedom

**Time-Varying Coefficients:**
$$h(t|x) = h_0(t) \\exp[\\beta(t) x]$$

### 2. Model Fit Assessment

**Martingale Residuals:**
$$M_i = \\delta_i - \\hat{H}_0(t_i) \\exp(\\hat{\\boldsymbol{\\beta}}'\\mathbf{x}_i)$$

**Deviance Residuals:**
$$D_i = \\text{sign}(M_i) \\sqrt{-2[M_i + \\delta_i \\ln(\\delta_i - M_i)]}$$

**Cox-Snell Residuals:**
$$r_{CS,i} = \\hat{H}_0(t_i) \\exp(\\hat{\\boldsymbol{\\beta}}'\\mathbf{x}_i)$$

### 3. Influential Observations

**DFBETA:**
- Change in coefficient when observation i is deleted
- Standardized version: $|DFBETA| > 2/\\sqrt{n}$

**Likelihood Displacement:**
$$LD_i = 2[\\ell(\\hat{\\boldsymbol{\\beta}}) - \\ell(\\hat{\\boldsymbol{\\beta}}_{(i)})]$$

## Advanced Topics

### 1. Stratified Cox Model

**Model:**
$$h_g(t|x) = h_{0g}(t) \\exp(\\boldsymbol{\\beta}'\\mathbf{x})$$

Where g indexes strata.

**Use Cases:**
- Non-proportional hazards for stratification variable
- Different baseline hazards across groups

### 2. Frailty Models

**Shared Frailty:**
$$h_{ij}(t|x_{ij}, w_i) = w_i h_0(t) \\exp(\\boldsymbol{\\beta}'\\mathbf{x}_{ij})$$

Where $w_i$ is the frailty term for cluster i.

**Gamma Frailty:**
- $w_i \\sim \\text{Gamma}(\\theta, \\theta)$
- Accounts for unobserved heterogeneity

### 3. Competing Risks

**Cause-Specific Hazard:**
$$h_k(t) = \\lim_{\\Delta t \\to 0} \\frac{P(t \\leq T < t + \\Delta t, \\delta = k | T \\geq t)}{\\Delta t}$$

**Cumulative Incidence Function:**
$$F_k(t) = \\int_0^t S(u) h_k(u) du$$

**Fine-Gray Model:**
- Models subdistribution hazard
- Treats competing events as censored

## Parametric Survival Models

### 1. Exponential Model

**Hazard Function:**
$$h(t) = \\lambda$$

**Survival Function:**
$$S(t) = \\exp(-\\lambda t)$$

**Mean Survival Time:**
$$E[T] = \\frac{1}{\\lambda}$$

### 2. Weibull Model

**Hazard Function:**
$$h(t) = \\frac{\\gamma}{\\lambda}\\left(\\frac{t}{\\lambda}\\right)^{\\gamma-1}$$

**Survival Function:**
$$S(t) = \\exp\\left[-\\left(\\frac{t}{\\lambda}\\right)^\\gamma\\right]$$

**Shape Parameter Interpretation:**
- γ < 1: Decreasing hazard
- γ = 1: Constant hazard (exponential)
- γ > 1: Increasing hazard

### 3. Log-Normal Model

**Hazard Function:**
$$h(t) = \\frac{\\phi\\left(\\frac{\\ln t - \\mu}{\\sigma}\\right)}{\\sigma t \\left[1 - \\Phi\\left(\\frac{\\ln t - \\mu}{\\sigma}\\right)\\right]}$$

Where φ and Φ are standard normal PDF and CDF.

## Practical Guidelines

### 1. Study Design Considerations

**Sample Size Calculation:**
- Based on expected hazard ratio
- Number of events, not sample size
- Account for censoring rate

**Follow-up Duration:**
- Sufficient events for stable estimates
- Balance between information and cost

### 2. Data Preparation

**Time Scale Selection:**
- Calendar time vs. time since entry
- Age as time scale for age-related events

**Covariate Coding:**
- Reference categories for categorical variables
- Centering continuous variables
- Interaction terms

### 3. Model Building Strategy

**Variable Selection:**
- Clinical/theoretical importance
- Statistical significance
- Avoid overfitting

**Model Comparison:**
- Likelihood ratio tests for nested models
- AIC/BIC for non-nested models
- Cross-validation

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Study design and follow-up
- Event definition and ascertainment
- Censoring mechanism
- Statistical methods used

### 2. Results Section

**Kaplan-Meier Analysis:**
- Median survival times with CI
- Survival probabilities at key time points
- Log-rank test results

**Cox Regression:**
- Hazard ratios with 95% CI
- P-values for individual coefficients
- Overall model significance
- Assumption checking results

### 3. Example Reporting

"Median overall survival was 24.3 months (95% CI: 18.7-31.2) in the treatment group versus 18.1 months (95% CI: 14.2-22.8) in the control group (log-rank p = 0.032). In multivariable Cox regression, treatment was associated with reduced mortality risk (HR = 0.73, 95% CI: 0.55-0.97, p = 0.031) after adjusting for age, stage, and performance status. The proportional hazards assumption was satisfied (global test p = 0.18)."

This comprehensive guide provides the foundation for conducting and interpreting survival analysis in medical research, reliability studies, and other time-to-event applications.
`,Wf=`# Reliability Analysis: Comprehensive Reference Guide

This comprehensive guide covers reliability analysis methods for assessing the consistency and dependability of measurements, scales, and instruments. Reliability analysis is essential for psychometrics, survey research, educational testing, and any field requiring consistent measurement tools.

## Overview

Reliability analysis evaluates the consistency of measurements across time, items, or raters. It addresses the fundamental question: "If we measured the same thing again under the same conditions, would we get the same result?" High reliability is prerequisite for valid measurement and meaningful research conclusions.

## Theoretical Foundation

### 1. Classical Test Theory

**Basic Equation:**
$$X = T + E$$

Where:
- X = observed score
- T = true score
- E = measurement error

**Reliability Definition:**
$$\\rho_{XX} = \\frac{\\sigma_T^2}{\\sigma_X^2} = \\frac{\\sigma_T^2}{\\sigma_T^2 + \\sigma_E^2}$$

Where:
- $\\sigma_T^2$ = true score variance
- $\\sigma_E^2$ = error variance
- $\\sigma_X^2$ = observed score variance

**Properties:**
- $0 \\leq \\rho_{XX} \\leq 1$
- Higher values indicate greater reliability
- Perfect reliability: $\\rho_{XX} = 1$ (no measurement error)

### 2. Standard Error of Measurement

**Formula:**
$$SEM = \\sigma_X \\sqrt{1 - \\rho_{XX}}$$

**Confidence Intervals:**
$$CI = X \\pm z_{\\alpha/2} \\times SEM$$

**Interpretation:**
- Smaller SEM indicates more precise measurement
- Used for individual score interpretation

## Types of Reliability

### 1. Internal Consistency

**Cronbach's Alpha:**
$$\\alpha = \\frac{k}{k-1}\\left(1 - \\frac{\\sum_{i=1}^k \\sigma_{Y_i}^2}{\\sigma_X^2}\\right)$$

Where:
- k = number of items
- $\\sigma_{Y_i}^2$ = variance of item i
- $\\sigma_X^2$ = variance of total score

**Interpretation Guidelines:**
- α ≥ 0.90: Excellent
- α ≥ 0.80: Good
- α ≥ 0.70: Acceptable
- α ≥ 0.60: Questionable
- α < 0.60: Poor

**Standardized Alpha:**
$$\\alpha_{std} = \\frac{k \\bar{r}}{1 + (k-1)\\bar{r}}$$

Where $\\bar{r}$ is the average inter-item correlation.

### 2. Split-Half Reliability

**Spearman-Brown Formula:**
$$\\rho_{XX} = \\frac{2\\rho_{12}}{1 + \\rho_{12}}$$

Where $\\rho_{12}$ is the correlation between half-tests.

**Equal-Length Assumption:**
- Assumes both halves have equal reliability
- Corrects for test length reduction

**Guttman Split-Half:**
$$\\rho_{XX} = 2\\left(1 - \\frac{\\sigma_1^2 + \\sigma_2^2}{\\sigma_X^2}\\right)$$

### 3. Test-Retest Reliability

**Stability Coefficient:**
$$\\rho_{XX} = \\text{Corr}(X_1, X_2)$$

Where $X_1$ and $X_2$ are scores at two time points.

**Considerations:**
- Time interval selection
- Practice effects
- Memory effects
- True change vs. measurement error

### 4. Parallel Forms Reliability

**Equivalence Coefficient:**
$$\\rho_{XX} = \\text{Corr}(X_A, X_B)$$

Where A and B are parallel forms.

**Requirements:**
- Equal means: $\\mu_A = \\mu_B$
- Equal variances: $\\sigma_A^2 = \\sigma_B^2$
- Equal correlations with external variables

## Advanced Reliability Measures

### 1. Coefficient Omega

**Total Omega:**
$$\\omega_t = \\frac{(\\sum \\lambda_i)^2}{(\\sum \\lambda_i)^2 + \\sum \\psi_{ii} + 2\\sum_{i<j}\\psi_{ij}}$$

Where:
- $\\lambda_i$ = factor loading for item i
- $\\psi_{ii}$ = unique variance for item i
- $\\psi_{ij}$ = covariance between unique factors

**Hierarchical Omega:**
$$\\omega_h = \\frac{(\\sum \\lambda_{gi})^2}{(\\sum \\lambda_{gi})^2 + (\\sum \\lambda_{si})^2 + \\sum \\psi_{ii}}$$

Where subscripts g and s refer to general and specific factors.

### 2. Greatest Lower Bound (GLB)

**Formula:**
$$GLB = 1 - \\frac{\\text{tr}(\\mathbf{R}^{-1}\\mathbf{D})}{\\mathbf{1}'\\mathbf{R}^{-1}\\mathbf{1}}$$

Where:
- $\\mathbf{R}$ = correlation matrix
- $\\mathbf{D}$ = diagonal matrix of $\\mathbf{R}$
- $\\mathbf{1}$ = vector of ones

**Properties:**
- Upper bound for reliability
- More accurate than alpha for multidimensional scales

### 3. Composite Reliability

**Factor Analysis Based:**
$$CR = \\frac{(\\sum \\lambda_i)^2}{(\\sum \\lambda_i)^2 + \\sum \\text{Var}(\\epsilon_i)}$$

**Advantages:**
- Accounts for different factor loadings
- More appropriate for CFA models
- Not affected by number of items

## Item Analysis

### 1. Item-Total Correlations

**Corrected Item-Total Correlation:**
$$r_{it} = \\frac{r_{ix} \\sigma_x - \\sigma_i}{\\sqrt{\\sigma_x^2 + \\sigma_i^2 - 2r_{ix}\\sigma_x\\sigma_i}}$$

Where:
- $r_{ix}$ = correlation between item and total
- $\\sigma_x$ = standard deviation of total score
- $\\sigma_i$ = standard deviation of item i

**Interpretation:**
- $r_{it} \\geq 0.30$: Acceptable
- $r_{it} < 0.30$: Consider removal

### 2. Alpha if Item Deleted

**Formula:**
$$\\alpha_{-i} = \\frac{k-1}{k-2}\\left(1 - \\frac{\\sum_{j \\neq i} \\sigma_{Y_j}^2 + \\sum_{j \\neq i}\\sum_{l \\neq i, l \\neq j} \\sigma_{jl}}{\\sigma_{X_{-i}}^2}\\right)$$

**Decision Rule:**
- If $\\alpha_{-i} > \\alpha$: Consider removing item i
- Balance between reliability and content validity

### 3. Item Discrimination

**Ferguson's Delta:**
$$\\delta = \\frac{k^2 - \\sum f_i^2}{k^2 - k}$$

Where $f_i$ is the frequency of score i.

**Interpretation:**
- δ > 0.90: Excellent discrimination
- δ = 0: No discrimination

## Generalizability Theory

### 1. G-Study (Generalizability Study)

**Variance Components:**
$$\\sigma^2(X) = \\sigma^2(p) + \\sigma^2(i) + \\sigma^2(pi) + \\sigma^2(e)$$

Where:
- p = person effect
- i = item effect
- pi = person × item interaction
- e = residual error

**G-Coefficient:**
$$E\\rho^2 = \\frac{\\sigma^2(p)}{\\sigma^2(p) + \\frac{\\sigma^2(pi)}{n_i} + \\frac{\\sigma^2(e)}{n_i}}$$

### 2. D-Study (Decision Study)

**Dependability Coefficient:**
$$\\Phi = \\frac{\\sigma^2(p)}{\\sigma^2(p) + \\sigma^2(\\delta)}$$

Where $\\sigma^2(\\delta)$ is the error variance for absolute decisions.

**Optimization:**
- Determine optimal number of items/raters
- Cost-benefit analysis
- Acceptable reliability threshold

## Reliability for Different Data Types

### 1. Ordinal Data

**Ordinal Alpha:**
- Based on polychoric correlations
- More appropriate than Pearson correlations
- Accounts for ordinal nature of responses

**Categorical Omega:**
$$\\omega_{cat} = \\frac{(\\sum \\lambda_i^*)^2}{(\\sum \\lambda_i^*)^2 + \\sum \\theta_{ii}^*}$$

Where * denotes parameters from categorical factor analysis.

### 2. Binary Data

**KR-20 (Kuder-Richardson 20):**
$$KR_{20} = \\frac{k}{k-1}\\left(1 - \\frac{\\sum p_i q_i}{\\sigma_X^2}\\right)$$

Where:
- $p_i$ = proportion correct on item i
- $q_i = 1 - p_i$

**KR-21 (Simplified Version):**
$$KR_{21} = \\frac{k}{k-1}\\left(1 - \\frac{k\\bar{p}\\bar{q}}{\\sigma_X^2}\\right)$$

### 3. Multilevel Data

**Intraclass Correlation (ICC):**

**ICC(1,1) - One-way random:**
$$ICC(1,1) = \\frac{MS_B - MS_W}{MS_B + (k-1)MS_W}$$

**ICC(2,1) - Two-way random:**
$$ICC(2,1) = \\frac{MS_B - MS_E}{MS_B + (k-1)MS_E + k(MS_J - MS_E)/n}$$

**ICC(3,1) - Two-way mixed:**
$$ICC(3,1) = \\frac{MS_B - MS_E}{MS_B + (k-1)MS_E}$$

## Factors Affecting Reliability

### 1. Test Length

**Spearman-Brown Prophecy:**
$$\\rho_{kk} = \\frac{k\\rho_{XX}}{1 + (k-1)\\rho_{XX}}$$

Where k is the factor by which test length is changed.

**Optimal Test Length:**
- Longer tests generally more reliable
- Diminishing returns with excessive length
- Balance reliability with practicality

### 2. Sample Heterogeneity

**Range Restriction Effect:**
- Homogeneous samples reduce reliability
- Heterogeneous samples increase reliability
- Consider target population characteristics

**Correction for Range Restriction:**
$$\\rho_{XX,u} = \\frac{\\rho_{XX,r}}{1 - \\rho_{XX,r}(1 - u^2)}$$

Where u is the ratio of restricted to unrestricted standard deviations.

### 3. Item Quality

**Item Characteristics:**
- Clear, unambiguous wording
- Appropriate difficulty level
- Good discrimination
- Minimal guessing effects

## Reliability Standards by Field

### 1. Psychological Testing

**Cognitive Abilities:**
- Individual decisions: ≥ 0.90
- Group comparisons: ≥ 0.80
- Research purposes: ≥ 0.70

**Personality Measures:**
- Clinical use: ≥ 0.85
- Research use: ≥ 0.70
- Screening: ≥ 0.60

### 2. Educational Assessment

**High-Stakes Testing:**
- Individual decisions: ≥ 0.95
- Certification: ≥ 0.90
- Placement: ≥ 0.85

**Classroom Assessment:**
- Summative: ≥ 0.80
- Formative: ≥ 0.70

### 3. Medical Instruments

**Diagnostic Tools:**
- Clinical diagnosis: ≥ 0.90
- Screening: ≥ 0.80
- Research: ≥ 0.70

## Improving Reliability

### 1. Item-Level Strategies

**Item Writing:**
- Clear, specific language
- Avoid double-barreled questions
- Appropriate reading level
- Consistent response format

**Item Selection:**
- Remove poorly performing items
- Optimize item difficulty
- Ensure content representativeness

### 2. Scale-Level Strategies

**Increase Test Length:**
- Add high-quality items
- Maintain content balance
- Consider respondent burden

**Improve Instructions:**
- Clear administration procedures
- Standardized conditions
- Adequate time limits

### 3. Statistical Approaches

**Composite Scoring:**
- Weight items by quality
- Use factor scores
- Apply item response theory

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Reliability type assessed
- Sample characteristics
- Administration conditions
- Analysis software used

### 2. Results Section

**Required Information:**
- Reliability coefficients with confidence intervals
- Item-level statistics
- Factor structure (if relevant)
- Comparison to previous studies

### 3. Example Reporting

"Internal consistency was assessed using Cronbach's alpha and McDonald's omega. The total scale demonstrated excellent reliability (α = 0.92, 95% CI: 0.89-0.94; ω = 0.93). Subscale reliabilities ranged from acceptable to good (α = 0.71-0.85). Item-total correlations ranged from 0.34 to 0.78, with no items flagged for removal. Test-retest reliability over 2 weeks was good (r = 0.84, 95% CI: 0.78-0.89, n = 150)."

This comprehensive guide provides the foundation for conducting and interpreting reliability analysis across various measurement contexts and research applications.
`,Hf=`# Mediation and Moderation Analysis: Comprehensive Reference Guide

This comprehensive guide covers mediation and moderation analysis techniques for understanding complex relationships between variables. These methods are essential for testing theoretical mechanisms, identifying boundary conditions, and advancing causal understanding in behavioral and social sciences.

## Overview

Mediation and moderation analyses examine how and when variables are related. Mediation addresses the mechanism or process through which one variable affects another (the "how" question), while moderation examines the conditions under which relationships vary (the "when" or "for whom" question).

## Mediation Analysis

### 1. Simple Mediation Model

**Path Model:**
- X → M → Y (indirect path)
- X → Y (direct path)

**Regression Equations:**
$$M = i_1 + aX + e_1$$
$$Y = i_2 + c'X + bM + e_2$$

Where:
- a = effect of X on M
- b = effect of M on Y (controlling for X)
- c' = direct effect of X on Y
- c = total effect of X on Y

**Total Effect:**
$$c = c' + ab$$

### 2. Indirect Effect Estimation

**Product of Coefficients:**
$$ab = a \\times b$$

**Standard Error (Sobel Test):**
$$SE_{ab} = \\sqrt{a^2 s_b^2 + b^2 s_a^2}$$

**Sobel Test Statistic:**
$$z = \\frac{ab}{SE_{ab}}$$

**Limitations:**
- Assumes normal distribution of ab
- Low power for small effects
- Not recommended for inference

### 3. Bootstrap Confidence Intervals

**Procedure:**
1. Resample data with replacement (B times)
2. Estimate ab for each bootstrap sample
3. Create empirical distribution of ab
4. Determine percentile-based confidence intervals

**Bias-Corrected and Accelerated (BCa):**
$$CI_{BCa} = [\\Phi^{-1}(\\alpha_1), \\Phi^{-1}(\\alpha_2)]$$

Where:
$$\\alpha_1 = \\Phi\\left(z_0 + \\frac{z_0 + z_{\\alpha/2}}{1 - a(z_0 + z_{\\alpha/2})}\\right)$$

**Advantages:**
- No normality assumption
- Higher power than Sobel test
- Asymmetric confidence intervals

### 4. Multiple Mediation

**Parallel Mediators:**
$$Y = i + c'X + b_1M_1 + b_2M_2 + ... + b_kM_k + e$$

**Specific Indirect Effects:**
$$a_i b_i$$ for mediator i

**Total Indirect Effect:**
$$\\sum_{i=1}^k a_i b_i$$

**Serial Mediation:**
$$X \\rightarrow M_1 \\rightarrow M_2 \\rightarrow Y$$

**Indirect Effects:**
- $a_1 b_1$: Through $M_1$ only
- $a_2 b_2$: Through $M_2$ only  
- $a_1 d_{21} b_2$: Through $M_1$ then $M_2$

## Moderation Analysis

### 1. Simple Moderation Model

**Regression Equation:**
$$Y = i + b_1X + b_2W + b_3XW + e$$

Where:
- $b_1$ = effect of X when W = 0
- $b_2$ = effect of W when X = 0
- $b_3$ = interaction effect (moderation)

**Conditional Effects:**
$$\\theta_{X \\rightarrow Y} = b_1 + b_3W$$

**Simple Slopes:**
- At W = -1 SD: $b_1 - b_3 \\sigma_W$
- At W = Mean: $b_1$
- At W = +1 SD: $b_1 + b_3 \\sigma_W$

### 2. Centering Variables

**Mean Centering:**
$$X_c = X - \\bar{X}$$
$$W_c = W - \\bar{W}$$

**Benefits:**
- Reduces multicollinearity
- Makes lower-order terms interpretable
- Doesn't affect interaction term

**Grand Mean Centering vs. Group Mean Centering:**
- Grand mean: For between-subjects designs
- Group mean: For within-subjects/multilevel designs

### 3. Probing Interactions

**Johnson-Neyman Technique:**
Find values of W where conditional effect is significant:

$$W_{JN} = \\frac{-b_1 \\pm t_{\\alpha/2,df} \\sqrt{s_{b_1}^2 + 2b_1 s_{b_1,b_3} + b_1^2 s_{b_3}^2}}{b_3}$$

**Regions of Significance:**
- Values of W where effect is significant
- More informative than simple slopes

**Spotlight Analysis:**
- Test conditional effects at specific W values
- Theoretically meaningful values
- Percentiles of W distribution

### 4. Categorical Moderators

**Dummy Coding:**
$$Y = i + b_1X + b_2D_1 + b_3D_2 + b_4XD_1 + b_5XD_2 + e$$

For 3-group moderator with reference group.

**Effect Coding:**
- Sum of codes equals zero
- Coefficients represent deviations from grand mean

**Omnibus Test:**
$$F = \\frac{(SSE_R - SSE_F)/(df_R - df_F)}{SSE_F/df_F}$$

## Moderated Mediation

### 1. Conceptual Framework

**First Stage Moderation:**
$$M = i_1 + a_1X + a_2W + a_3XW + e_1$$
$$Y = i_2 + c'X + bM + e_2$$

**Conditional Indirect Effect:**
$$\\theta_{X \\rightarrow M \\rightarrow Y} = (a_1 + a_3W)b$$

**Second Stage Moderation:**
$$M = i_1 + aX + e_1$$
$$Y = i_2 + c'X + b_1M + b_2W + b_3MW + e_2$$

**Conditional Indirect Effect:**
$$\\theta_{X \\rightarrow M \\rightarrow Y} = a(b_1 + b_3W)$$

### 2. Index of Moderated Mediation

**Definition:**
$$\\omega = a_3b$$ (for first stage moderation)
$$\\omega = ab_3$$ (for second stage moderation)

**Interpretation:**
- Quantifies how much mediation changes across moderator values
- Bootstrap confidence intervals for inference

### 3. Conditional Process Analysis

**Integrated Model:**
$$M = i_1 + a_1X + a_2W + a_3XW + e_1$$
$$Y = i_2 + c'X + b_1M + b_2W + b_3MW + b_4XW + e_2$$

**Multiple Conditional Effects:**
- Direct effect moderation: $b_4$
- Indirect effect moderation: $a_3b_1 + ab_3$

## Mediated Moderation

### 1. Conceptual Model

**Process:**
1. X and W interact to affect M
2. M mediates the X×W effect on Y

**Equations:**
$$M = i_1 + a_1X + a_2W + a_3XW + e_1$$
$$Y = i_2 + c'X + c_2W + bM + e_2$$

**Mediated Moderation Effect:**
$$a_3b$$

### 2. Interpretation

**Meaning:**
- How much of the X×W interaction on Y is mediated by M
- Explains mechanism of moderation effect

**Testing:**
- Bootstrap confidence intervals for $a_3b$
- Compare total interaction effect to mediated portion

## Advanced Topics

### 1. Multilevel Mediation

**2-1-1 Model:**
- Level 2 X → Level 1 M → Level 1 Y

**1-1-1 Model:**
- All variables at Level 1
- Random slopes for mediation paths

**Cross-Level Interactions:**
$$M_{ij} = \\beta_{0j} + \\beta_{1j}X_{ij} + r_{ij}$$
$$\\beta_{1j} = \\gamma_{10} + \\gamma_{11}W_j + u_{1j}$$

### 2. Longitudinal Mediation

**Autoregressive Models:**
$$M_t = \\alpha_1 M_{t-1} + a X_{t-1} + e_{1t}$$
$$Y_t = \\alpha_2 Y_{t-1} + \\beta_1 M_{t-1} + c' X_{t-1} + e_{2t}$$

**Cross-Lagged Panel Models:**
- Bidirectional relationships
- Temporal precedence
- Stability coefficients

### 3. Causal Mediation Analysis

**Potential Outcomes Framework:**
$$Y_i(t,m) = \\text{potential outcome for unit i under treatment t and mediator value m}$$

**Natural Direct Effect (NDE):**
$$\\delta(t) = E[Y_i(t,M_i(0)) - Y_i(0,M_i(0))]$$

**Natural Indirect Effect (NIE):**
$$\\zeta(t) = E[Y_i(t,M_i(t)) - Y_i(t,M_i(0))]$$

**Assumptions:**
- No unmeasured confounding
- No mediator-outcome confounders affected by treatment

## Effect Sizes

### 1. Mediation Effect Sizes

**Proportion Mediated:**
$$P_M = \\frac{ab}{c} = \\frac{ab}{c' + ab}$$

**Ratio of Indirect to Direct Effect:**
$$\\frac{ab}{c'}$$

**Completely Standardized Indirect Effect:**
$$ab_{cs} = a_{cs} \\times b_{cs}$$

### 2. Moderation Effect Sizes

**Proportion of Variance Explained by Interaction:**
$$f^2 = \\frac{R^2_{full} - R^2_{main}}{1 - R^2_{full}}$$

**Cohen's Guidelines:**
- Small: $f^2 = 0.02$
- Medium: $f^2 = 0.15$
- Large: $f^2 = 0.35$

## Assumptions and Diagnostics

### 1. Mediation Assumptions

**Temporal Precedence:**
- X precedes M precedes Y
- Longitudinal or experimental design preferred

**No Omitted Variables:**
- No unmeasured confounders of X-M, M-Y, or X-Y relationships
- Sensitivity analyses recommended

**Measurement Reliability:**
- Unreliable measures attenuate effects
- Correct for measurement error when possible

### 2. Moderation Assumptions

**Linearity:**
- Linear relationships between variables
- Check with scatterplots and residual plots

**Homoscedasticity:**
- Constant error variance across moderator values
- Breusch-Pagan test

**Normality:**
- Normal distribution of residuals
- Robust methods if violated

## Practical Guidelines

### 1. Sample Size Planning

**Mediation Analysis:**
- Minimum 200 for simple mediation
- Larger samples for multiple mediators
- Power depends on effect sizes of a and b paths

**Moderation Analysis:**
- Minimum 100 for detecting large interactions
- 200+ for medium interactions
- 400+ for small interactions

### 2. Variable Preparation

**Scaling:**
- Standardize continuous variables if desired
- Consider meaningful zero points

**Missing Data:**
- Multiple imputation preferred
- Avoid listwise deletion

**Outliers:**
- Examine influence on interaction terms
- Consider robust methods

### 3. Model Building Strategy

**Theory-Driven Approach:**
- Specify models based on theory
- Avoid exploratory fishing

**Model Comparison:**
- Test nested models
- Use information criteria for non-nested models

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Theoretical rationale for mediation/moderation
- Variable measurement and coding
- Analysis software and procedures
- Bootstrap specifications

### 2. Results Section

**Mediation Results:**
- Path coefficients with confidence intervals
- Indirect effects with bootstrap CIs
- Total and direct effects
- Effect sizes

**Moderation Results:**
- Interaction term significance
- Simple slopes analysis
- Regions of significance (if applicable)
- Interaction plots

### 3. Example Reporting

**Mediation:** "Bootstrap analysis (5,000 samples) revealed a significant indirect effect of training on performance through self-efficacy (ab = 0.23, 95% CI: 0.12-0.36). The direct effect remained significant (c' = 0.31, p < 0.01), indicating partial mediation. The indirect effect accounted for 43% of the total effect."

**Moderation:** "The interaction between stress and social support significantly predicted well-being (b = 0.18, p < 0.05). Simple slopes analysis revealed that stress was negatively related to well-being at low support (b = -0.42, p < 0.001) but not at high support (b = -0.06, p = 0.52). The Johnson-Neyman technique identified the region of significance for support scores below 4.2."

This comprehensive guide provides the foundation for conducting and interpreting mediation and moderation analyses in psychological, social, and behavioral research.
`,Uf=`# Cluster Analysis: Comprehensive Reference Guide

This comprehensive guide covers cluster analysis methods for identifying natural groupings in data. Cluster analysis is essential for market segmentation, pattern recognition, data mining, and exploratory data analysis across diverse fields including psychology, marketing, biology, and social sciences.

## Overview

Cluster analysis is a multivariate statistical technique that groups objects or observations into clusters such that objects within the same cluster are more similar to each other than to objects in other clusters. Unlike classification, cluster analysis is an unsupervised learning technique that discovers hidden patterns without prior knowledge of group membership.

## Theoretical Foundation

### 1. Similarity and Distance Measures

**Euclidean Distance:**
$$d_{ij} = \\sqrt{\\sum_{k=1}^p (x_{ik} - x_{jk})^2}$$

**Manhattan Distance:**
$$d_{ij} = \\sum_{k=1}^p |x_{ik} - x_{jk}|$$

**Minkowski Distance:**
$$d_{ij} = \\left(\\sum_{k=1}^p |x_{ik} - x_{jk}|^r\\right)^{1/r}$$

**Mahalanobis Distance:**
$$d_{ij} = \\sqrt{(\\mathbf{x}_i - \\mathbf{x}_j)'\\mathbf{S}^{-1}(\\mathbf{x}_i - \\mathbf{x}_j)}$$

Where $\\mathbf{S}$ is the covariance matrix.

### 2. Similarity Measures

**Pearson Correlation:**
$$r_{ij} = \\frac{\\sum_{k=1}^p (x_{ik} - \\bar{x}_i)(x_{jk} - \\bar{x}_j)}{\\sqrt{\\sum_{k=1}^p (x_{ik} - \\bar{x}_i)^2 \\sum_{k=1}^p (x_{jk} - \\bar{x}_j)^2}}$$

**Cosine Similarity:**
$$\\cos(\\theta_{ij}) = \\frac{\\mathbf{x}_i \\cdot \\mathbf{x}_j}{||\\mathbf{x}_i|| \\times ||\\mathbf{x}_j||}$$

**Jaccard Coefficient (Binary Data):**
$$J_{ij} = \\frac{a}{a + b + c}$$

Where a = both present, b = i present/j absent, c = i absent/j present.

## Hierarchical Clustering

### 1. Agglomerative Methods

**Single Linkage (Nearest Neighbor):**
$$d(C_i, C_j) = \\min_{x \\in C_i, y \\in C_j} d(x,y)$$

**Complete Linkage (Farthest Neighbor):**
$$d(C_i, C_j) = \\max_{x \\in C_i, y \\in C_j} d(x,y)$$

**Average Linkage:**
$$d(C_i, C_j) = \\frac{1}{|C_i||C_j|} \\sum_{x \\in C_i} \\sum_{y \\in C_j} d(x,y)$$

**Ward's Method:**
$$d(C_i, C_j) = \\frac{|C_i||C_j|}{|C_i| + |C_j|} ||\\mu_i - \\mu_j||^2$$

Where $\\mu_i$ and $\\mu_j$ are cluster centroids.

### 2. Divisive Methods

**Algorithm:**
1. Start with all objects in one cluster
2. Recursively split clusters
3. Continue until each object is its own cluster

**Splitting Criteria:**
- Maximum within-cluster distance
- Minimum between-cluster distance
- Largest cluster first

### 3. Dendrogram Interpretation

**Height:** Distance at which clusters merge
**Cophenetic Correlation:** Agreement between dendrogram and original distances

$$r_c = \\frac{\\sum_{i<j}(d_{ij} - \\bar{d})(c_{ij} - \\bar{c})}{\\sqrt{\\sum_{i<j}(d_{ij} - \\bar{d})^2 \\sum_{i<j}(c_{ij} - \\bar{c})^2}}$$

Where $c_{ij}$ is the cophenetic distance.

## Partitioning Methods

### 1. K-Means Clustering

**Objective Function:**
$$J = \\sum_{i=1}^k \\sum_{x \\in C_i} ||x - \\mu_i||^2$$

**Algorithm:**
1. Initialize k cluster centers
2. Assign each point to nearest center
3. Update centers to cluster means
4. Repeat until convergence

**Convergence Criterion:**
$$\\sum_{i=1}^k ||\\mu_i^{(t+1)} - \\mu_i^{(t)}||^2 < \\epsilon$$

### 2. K-Medoids (PAM)

**Objective Function:**
$$J = \\sum_{i=1}^k \\sum_{x \\in C_i} d(x, m_i)$$

Where $m_i$ is the medoid of cluster i.

**Algorithm:**
1. Select k initial medoids
2. Assign objects to nearest medoid
3. For each medoid, try swapping with non-medoid
4. Keep swap if it reduces total cost

**Advantages:**
- Robust to outliers
- Works with any distance measure
- Medoids are actual data points

### 3. Fuzzy C-Means

**Membership Function:**
$$u_{ij} = \\frac{1}{\\sum_{k=1}^c \\left(\\frac{d_{ij}}{d_{kj}}\\right)^{2/(m-1)}}$$

**Objective Function:**
$$J_m = \\sum_{i=1}^c \\sum_{j=1}^n u_{ij}^m d_{ij}^2$$

Where m is the fuzziness parameter (m > 1).

**Update Rules:**
$$\\mu_i = \\frac{\\sum_{j=1}^n u_{ij}^m x_j}{\\sum_{j=1}^n u_{ij}^m}$$

## Density-Based Clustering

### 1. DBSCAN Algorithm

**Core Point:** Point with at least MinPts neighbors within distance ε

**Density-Reachable:** Point reachable from core point through chain of core points

**Algorithm:**
1. For each unvisited point p:
2. If p has ≥ MinPts neighbors within ε, start new cluster
3. Add all density-reachable points to cluster
4. Points not reachable from any core point are noise

**Parameters:**
- ε (epsilon): Neighborhood radius
- MinPts: Minimum points to form cluster

### 2. OPTICS Algorithm

**Reachability Distance:**
$$r(p,q) = \\max(\\text{core-distance}(p), d(p,q))$$

**Core Distance:**
$$\\text{core-distance}(p) = \\text{distance to MinPts-th nearest neighbor}$$

**Advantages:**
- Produces cluster hierarchy
- Less sensitive to parameter choice
- Handles varying densities

## Model-Based Clustering

### 1. Gaussian Mixture Models

**Probability Density:**
$$f(x) = \\sum_{k=1}^K \\pi_k \\phi(x|\\mu_k, \\Sigma_k)$$

Where:
- $\\pi_k$ = mixing proportion for component k
- $\\phi(x|\\mu_k, \\Sigma_k)$ = multivariate normal density

**EM Algorithm:**

**E-Step:**
$$\\gamma_{ik} = \\frac{\\pi_k \\phi(x_i|\\mu_k, \\Sigma_k)}{\\sum_{j=1}^K \\pi_j \\phi(x_i|\\mu_j, \\Sigma_j)}$$

**M-Step:**
$$\\pi_k = \\frac{1}{n}\\sum_{i=1}^n \\gamma_{ik}$$
$$\\mu_k = \\frac{\\sum_{i=1}^n \\gamma_{ik} x_i}{\\sum_{i=1}^n \\gamma_{ik}}$$
$$\\Sigma_k = \\frac{\\sum_{i=1}^n \\gamma_{ik} (x_i - \\mu_k)(x_i - \\mu_k)'}{\\sum_{i=1}^n \\gamma_{ik}}$$

### 2. Model Selection

**Bayesian Information Criterion:**
$$BIC = -2\\ln L + p \\ln n$$

**Akaike Information Criterion:**
$$AIC = -2\\ln L + 2p$$

**Integrated Completed Likelihood:**
$$ICL = BIC - 2\\sum_{i=1}^n \\sum_{k=1}^K \\gamma_{ik} \\ln \\gamma_{ik}$$

## Determining Number of Clusters

### 1. Elbow Method

**Within-Cluster Sum of Squares:**
$$WCSS_k = \\sum_{i=1}^k \\sum_{x \\in C_i} ||x - \\mu_i||^2$$

**Procedure:**
1. Plot WCSS vs. number of clusters
2. Look for "elbow" where improvement diminishes

### 2. Silhouette Analysis

**Silhouette Width:**
$$s(i) = \\frac{b(i) - a(i)}{\\max(a(i), b(i))}$$

Where:
- $a(i)$ = average distance to points in same cluster
- $b(i)$ = average distance to points in nearest cluster

**Interpretation:**
- s(i) ≈ 1: Well clustered
- s(i) ≈ 0: On border between clusters
- s(i) ≈ -1: Misclassified

**Average Silhouette Width:**
$$\\bar{s} = \\frac{1}{n}\\sum_{i=1}^n s(i)$$

### 3. Gap Statistic

**Definition:**
$$\\text{Gap}(k) = E[\\ln(W_k)] - \\ln(W_k)$$

Where $E[\\ln(W_k)]$ is expected value under null distribution.

**Procedure:**
1. Generate reference datasets
2. Cluster reference data
3. Compare observed vs. expected WCSS
4. Choose k where gap is maximized

### 4. Information Criteria

**Calinski-Harabasz Index:**
$$CH(k) = \\frac{BCSS/(k-1)}{WCSS/(n-k)}$$

**Davies-Bouldin Index:**
$$DB(k) = \\frac{1}{k}\\sum_{i=1}^k \\max_{j \\neq i} \\frac{\\sigma_i + \\sigma_j}{d_{ij}}$$

Where $\\sigma_i$ is within-cluster scatter and $d_{ij}$ is between-cluster distance.

## Cluster Validation

### 1. Internal Validation

**Dunn Index:**
$$D = \\frac{\\min_{1 \\leq i < j \\leq k} d(C_i, C_j)}{\\max_{1 \\leq l \\leq k} \\text{diam}(C_l)}$$

**Connectivity:**
$$\\text{Conn} = \\sum_{i=1}^n \\sum_{j=1}^L x_{i,nn_j(i)}$$

Where $nn_j(i)$ is the j-th nearest neighbor of point i.

### 2. External Validation

**Adjusted Rand Index:**
$$ARI = \\frac{\\sum_{ij}\\binom{n_{ij}}{2} - [\\sum_i\\binom{a_i}{2}\\sum_j\\binom{b_j}{2}]/\\binom{n}{2}}{\\frac{1}{2}[\\sum_i\\binom{a_i}{2} + \\sum_j\\binom{b_j}{2}] - [\\sum_i\\binom{a_i}{2}\\sum_j\\binom{b_j}{2}]/\\binom{n}{2}}$$

**Normalized Mutual Information:**
$$NMI = \\frac{2 \\times MI(C,T)}{H(C) + H(T)}$$

Where MI is mutual information and H is entropy.

### 3. Stability Analysis

**Bootstrap Resampling:**
1. Generate bootstrap samples
2. Cluster each sample
3. Measure agreement between clusterings
4. High agreement indicates stable solution

**Jaccard Coefficient for Clusters:**
$$J = \\frac{|C_1 \\cap C_2|}{|C_1 \\cup C_2|}$$

## Advanced Clustering Methods

### 1. Spectral Clustering

**Similarity Matrix:** $S_{ij} = \\exp(-||x_i - x_j||^2/2\\sigma^2)$

**Laplacian Matrix:** $L = D - S$ (where D is degree matrix)

**Algorithm:**
1. Compute normalized Laplacian
2. Find k smallest eigenvalues
3. Use eigenvectors as features
4. Apply k-means to eigenvectors

### 2. Consensus Clustering

**Procedure:**
1. Generate multiple clusterings
2. Create consensus matrix
3. Cluster consensus matrix
4. Evaluate stability

**Consensus Matrix:**
$$M_{ij} = \\frac{\\text{number of times i and j clustered together}}{\\text{number of times both i and j were selected}}$$

### 3. Biclustering

**Objective:** Find subsets of objects and variables that cluster together

**Cheng-Church Algorithm:**
1. Remove rows/columns with high mean squared residue
2. Add rows/columns that reduce residue
3. Repeat until convergence

**Mean Squared Residue:**
$$H(I,J) = \\frac{1}{|I||J|}\\sum_{i \\in I, j \\in J}(a_{ij} - a_{iJ} - a_{Ij} + a_{IJ})^2$$

## Data Preprocessing

### 1. Standardization

**Z-Score Standardization:**
$$z_{ij} = \\frac{x_{ij} - \\bar{x}_j}{s_j}$$

**Min-Max Scaling:**
$$x'_{ij} = \\frac{x_{ij} - \\min_j}{\\max_j - \\min_j}$$

**Robust Scaling:**
$$x'_{ij} = \\frac{x_{ij} - \\text{median}_j}{IQR_j}$$

### 2. Dimensionality Reduction

**Principal Component Analysis:**
- Reduce dimensions before clustering
- Retain components explaining 80-90% variance

**Factor Analysis:**
- Use factor scores as clustering variables
- Addresses measurement error

### 3. Outlier Treatment

**Detection Methods:**
- Mahalanobis distance
- Local outlier factor
- Isolation forest

**Treatment Options:**
- Remove outliers
- Robust clustering methods
- Separate outlier cluster

## Practical Guidelines

### 1. Algorithm Selection

**Data Characteristics:**
- Spherical clusters: K-means
- Arbitrary shapes: DBSCAN
- Overlapping clusters: Fuzzy C-means
- Hierarchical structure: Hierarchical clustering

**Sample Size:**
- Small samples: Hierarchical clustering
- Large samples: K-means, DBSCAN
- Very large samples: Mini-batch K-means

### 2. Parameter Tuning

**K-means:**
- Try multiple initializations
- Use k-means++ initialization
- Consider k-medoids for robustness

**DBSCAN:**
- Use k-distance plot for ε selection
- MinPts = 2×dimensions (rule of thumb)

**Hierarchical:**
- Compare different linkage methods
- Use cophenetic correlation for evaluation

### 3. Interpretation Guidelines

**Cluster Profiling:**
- Compare cluster means/medians
- Statistical significance tests
- Effect sizes for differences

**Visualization:**
- Scatter plots (2D/3D)
- Parallel coordinate plots
- Heatmaps for high dimensions

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Clustering algorithm and parameters
- Distance measure used
- Data preprocessing steps
- Number of clusters determination method

### 2. Results Section

**Required Information:**
- Final number of clusters
- Cluster sizes and characteristics
- Validation indices
- Stability analysis results

### 3. Example Reporting

"K-means clustering was performed on standardized variables using Euclidean distance. The optimal number of clusters (k=4) was determined using the elbow method and silhouette analysis (average silhouette width = 0.67). The solution explained 73% of the total variance. Cluster stability was assessed through bootstrap resampling (1000 iterations), showing high stability (Jaccard coefficient > 0.80) for all clusters. Cluster 1 (n=45) was characterized by high extraversion and low neuroticism, while Cluster 2 (n=38) showed the opposite pattern."

This comprehensive guide provides the foundation for conducting and interpreting cluster analysis across various research domains and data types.
`,Xf=`# Meta-Analysis: Comprehensive Reference Guide

This comprehensive guide covers meta-analysis methods for systematically combining results from multiple independent studies. Meta-analysis is essential for evidence-based practice, systematic reviews, and synthesizing research findings across diverse fields including medicine, psychology, education, and social sciences.

## Overview

Meta-analysis is a statistical technique that combines results from multiple independent studies addressing the same research question to produce a single, more precise estimate of the effect. It provides greater statistical power than individual studies and helps resolve conflicting findings in the literature.

## Theoretical Foundation

### 1. Effect Size Measures

**Standardized Mean Difference (Cohen's d):**
$$d = \\frac{\\bar{X}_1 - \\bar{X}_2}{s_{pooled}}$$

Where:
$$s_{pooled} = \\sqrt{\\frac{(n_1-1)s_1^2 + (n_2-1)s_2^2}{n_1 + n_2 - 2}}$$

**Hedges' g (Bias-Corrected):**
$$g = d \\times \\left(1 - \\frac{3}{4(n_1 + n_2) - 9}\\right)$$

**Glass's Δ:**
$$\\Delta = \\frac{\\bar{X}_1 - \\bar{X}_2}{s_{control}}$$

### 2. Correlation Effect Sizes

**Pearson Correlation Coefficient:**
$$r = \\frac{\\sum(X_i - \\bar{X})(Y_i - \\bar{Y})}{\\sqrt{\\sum(X_i - \\bar{X})^2 \\sum(Y_i - \\bar{Y})^2}}$$

**Fisher's z-Transformation:**
$$z_r = \\frac{1}{2}\\ln\\left(\\frac{1+r}{1-r}\\right)$$

**Variance of z:**
$$\\text{Var}(z_r) = \\frac{1}{n-3}$$

### 3. Odds Ratio and Risk Ratio

**Odds Ratio:**
$$OR = \\frac{a \\times d}{b \\times c}$$

**Log Odds Ratio:**
$$\\ln(OR) = \\ln(a) + \\ln(d) - \\ln(b) - \\ln(c)$$

**Variance of Log OR:**
$$\\text{Var}[\\ln(OR)] = \\frac{1}{a} + \\frac{1}{b} + \\frac{1}{c} + \\frac{1}{d}$$

**Risk Ratio:**
$$RR = \\frac{a/(a+b)}{c/(c+d)}$$

## Fixed-Effects Model

### 1. Model Specification

**Assumption:** All studies estimate the same true effect size θ

**Model:**
$$T_i = \\theta + \\epsilon_i$$

Where $\\epsilon_i \\sim N(0, \\sigma_i^2)$

### 2. Weighted Average

**Pooled Effect Size:**
$$\\hat{\\theta}_{FE} = \\frac{\\sum_{i=1}^k w_i T_i}{\\sum_{i=1}^k w_i}$$

**Weights:**
$$w_i = \\frac{1}{\\sigma_i^2}$$

**Variance of Pooled Estimate:**
$$\\text{Var}(\\hat{\\theta}_{FE}) = \\frac{1}{\\sum_{i=1}^k w_i}$$

### 3. Confidence Intervals

**95% Confidence Interval:**
$$\\hat{\\theta}_{FE} \\pm 1.96 \\sqrt{\\text{Var}(\\hat{\\theta}_{FE})}$$

**Z-Test:**
$$Z = \\frac{\\hat{\\theta}_{FE}}{\\sqrt{\\text{Var}(\\hat{\\theta}_{FE})}}$$

## Random-Effects Model

### 1. Model Specification

**Assumption:** Studies estimate different but related effect sizes

**Model:**
$$T_i = \\theta_i + \\epsilon_i$$
$$\\theta_i = \\mu + \\delta_i$$

Where $\\delta_i \\sim N(0, \\tau^2)$

### 2. Between-Study Variance

**Method of Moments (DerSimonian-Laird):**
$$\\hat{\\tau}^2 = \\max\\left(0, \\frac{Q - (k-1)}{\\sum w_i - \\frac{\\sum w_i^2}{\\sum w_i}}\\right)$$

**Restricted Maximum Likelihood (REML):**
- Iterative estimation
- Generally preferred over method of moments
- Less biased for small samples

**Paule-Mandel Method:**
$$\\sum_{i=1}^k \\frac{(T_i - \\hat{\\mu})^2}{\\sigma_i^2 + \\hat{\\tau}^2} = k - 1$$

### 3. Pooled Estimate

**Random-Effects Weights:**
$$w_i^* = \\frac{1}{\\sigma_i^2 + \\hat{\\tau}^2}$$

**Pooled Effect Size:**
$$\\hat{\\mu}_{RE} = \\frac{\\sum_{i=1}^k w_i^* T_i}{\\sum_{i=1}^k w_i^*}$$

**Variance:**
$$\\text{Var}(\\hat{\\mu}_{RE}) = \\frac{1}{\\sum_{i=1}^k w_i^*}$$

## Heterogeneity Assessment

### 1. Cochran's Q Test

**Test Statistic:**
$$Q = \\sum_{i=1}^k w_i (T_i - \\hat{\\theta}_{FE})^2$$

**Distribution:** $Q \\sim \\chi^2_{k-1}$ under homogeneity

**Interpretation:**
- Significant Q indicates heterogeneity
- Low power for detecting heterogeneity

### 2. I² Statistic

**Formula:**
$$I^2 = \\max\\left(0, \\frac{Q - (k-1)}{Q} \\times 100\\%\\right)$$

**Interpretation:**
- 0-25%: Low heterogeneity
- 25-50%: Moderate heterogeneity
- 50-75%: Substantial heterogeneity
- 75-100%: Considerable heterogeneity

### 3. H² Statistic

**Formula:**
$$H^2 = \\frac{Q}{k-1}$$

**Interpretation:**
- H² = 1: No heterogeneity
- H² > 1: Presence of heterogeneity

**Confidence Interval:**
$$CI(H^2) = \\frac{Q}{\\chi^2_{\\alpha/2,k-1}}, \\frac{Q}{\\chi^2_{1-\\alpha/2,k-1}}$$

## Publication Bias

### 1. Funnel Plot

**Construction:**
- X-axis: Effect size
- Y-axis: Standard error (inverted)
- Symmetric distribution expected under no bias

**Asymmetry Tests:**
- Visual inspection
- Statistical tests for asymmetry

### 2. Egger's Test

**Regression Model:**
$$\\frac{T_i}{\\text{SE}_i} = \\beta_0 + \\beta_1 \\frac{1}{\\text{SE}_i} + \\epsilon_i$$

**Test Statistic:**
$$t = \\frac{\\beta_0}{\\text{SE}(\\beta_0)}$$

**Interpretation:**
- Significant intercept suggests bias
- Limited power with few studies

### 3. Trim-and-Fill Method

**Procedure:**
1. Estimate number of missing studies
2. Impute missing studies
3. Recalculate pooled effect
4. Compare original vs. adjusted estimate

**L₀ Estimator:**
$$\\hat{L}_0 = \\max(0, \\gamma_0 - 1)$$

Where $\\gamma_0$ is the number of studies to the right of center.

### 4. Selection Models

**Rosenthal's Fail-Safe N:**
$$N_{fs} = \\frac{k(\\bar{Z}^2 - 2.706)}{2.706}$$

**Orwin's Fail-Safe N:**
$$N_{fs} = k\\left(\\frac{\\bar{d} - d_{crit}}{d_{crit}}\\right)$$

Where $d_{crit}$ is the critical effect size.

## Moderator Analysis

### 1. Subgroup Analysis

**Between-Groups Heterogeneity:**
$$Q_B = \\sum_{j=1}^m k_j(\\hat{\\theta}_j - \\hat{\\theta}_{overall})^2$$

**Within-Groups Heterogeneity:**
$$Q_W = \\sum_{j=1}^m Q_j$$

**Test:** $Q_B \\sim \\chi^2_{m-1}$

### 2. Meta-Regression

**Mixed-Effects Model:**
$$T_i = \\beta_0 + \\beta_1 X_{i1} + ... + \\beta_p X_{ip} + u_i + \\epsilon_i$$

Where $u_i \\sim N(0, \\tau^2)$

**Weighted Least Squares:**
$$\\hat{\\boldsymbol{\\beta}} = (\\mathbf{X}'\\mathbf{W}\\mathbf{X})^{-1}\\mathbf{X}'\\mathbf{W}\\mathbf{T}$$

**Model Fit:**
$$R^2 = \\frac{\\hat{\\tau}_{unconditional}^2 - \\hat{\\tau}_{conditional}^2}{\\hat{\\tau}_{unconditional}^2}$$

### 3. Permutation Tests

**Procedure:**
1. Randomly reassign moderator values
2. Calculate test statistic
3. Repeat many times
4. Compare observed to permutation distribution

**Advantages:**
- No distributional assumptions
- Valid for small samples
- Accounts for multiple testing

## Advanced Methods

### 1. Multivariate Meta-Analysis

**Model:**
$$\\mathbf{T}_i = \\boldsymbol{\\theta}_i + \\boldsymbol{\\epsilon}_i$$
$$\\boldsymbol{\\theta}_i = \\boldsymbol{\\mu} + \\boldsymbol{\\delta}_i$$

**Covariance Structures:**
- Within-study correlations
- Between-study correlations
- Complex dependency patterns

### 2. Network Meta-Analysis

**Consistency Model:**
$$T_{ik} = \\mu_i + \\delta_{ik}$$

Where $\\delta_{ik}$ is treatment effect relative to baseline.

**Inconsistency Assessment:**
- Node-splitting
- Design-by-treatment interaction
- Loop-specific approach

### 3. Individual Patient Data (IPD)

**Two-Stage Approach:**
1. Analyze each study separately
2. Meta-analyze study-level results

**One-Stage Approach:**
$$Y_{ij} = \\beta_0 + \\beta_1 X_{ij} + \\beta_2 S_j + \\beta_3 X_{ij}S_j + u_j + \\epsilon_{ij}$$

Where S_j is study indicator.

## Bayesian Meta-Analysis

### 1. Model Specification

**Likelihood:**
$$T_i | \\theta_i, \\sigma_i^2 \\sim N(\\theta_i, \\sigma_i^2)$$

**Prior for Study Effects:**
$$\\theta_i | \\mu, \\tau^2 \\sim N(\\mu, \\tau^2)$$

**Hyperpriors:**
$$\\mu \\sim N(0, 100^2)$$
$$\\tau \\sim \\text{Half-Cauchy}(0, 1)$$

### 2. MCMC Estimation

**Gibbs Sampling:**
- Sample from full conditional distributions
- Monitor convergence diagnostics
- Assess chain mixing

**Posterior Summaries:**
- Posterior mean and credible intervals
- Probability statements
- Predictive distributions

### 3. Model Comparison

**Deviance Information Criterion (DIC):**
$$DIC = \\bar{D} + p_D$$

Where $\\bar{D}$ is posterior mean deviance and $p_D$ is effective number of parameters.

**Watanabe-Akaike Information Criterion (WAIC):**
$$WAIC = -2(\\text{lppd} - p_{WAIC})$$

## Quality Assessment

### 1. Study Quality Tools

**Risk of Bias 2 (RoB 2):**
- Randomization process
- Deviations from intended interventions
- Missing outcome data
- Measurement of outcome
- Selection of reported result

**Newcastle-Ottawa Scale:**
- Selection of study groups
- Comparability of groups
- Ascertainment of exposure/outcome

### 2. GRADE Approach

**Quality Domains:**
- Risk of bias
- Inconsistency
- Indirectness
- Imprecision
- Publication bias

**Quality Levels:**
- High: Very confident in effect estimate
- Moderate: Moderately confident
- Low: Limited confidence
- Very low: Very little confidence

## Sensitivity Analysis

### 1. Leave-One-Out Analysis

**Procedure:**
1. Remove one study at a time
2. Recalculate pooled effect
3. Assess influence of each study

**Influence Diagnostics:**
- Change in pooled estimate
- Change in confidence interval
- Change in heterogeneity

### 2. Cumulative Meta-Analysis

**Time-Ordered:**
- Add studies chronologically
- Show evolution of evidence

**Quality-Ordered:**
- Add studies by quality ranking
- Assess impact of study quality

### 3. Alternative Models

**Compare:**
- Fixed vs. random effects
- Different τ² estimators
- Robust methods
- Bayesian vs. frequentist

## Practical Guidelines

### 1. Planning Phase

**Protocol Development:**
- Research question (PICO format)
- Inclusion/exclusion criteria
- Search strategy
- Data extraction plan
- Statistical analysis plan

**Sample Size:**
- Minimum 2 studies required
- More studies increase precision
- Consider heterogeneity sources

### 2. Data Extraction

**Essential Information:**
- Study characteristics
- Participant characteristics
- Effect sizes and precision
- Potential moderators
- Quality indicators

**Missing Data:**
- Contact authors
- Estimate from available information
- Sensitivity analysis

### 3. Analysis Strategy

**Model Selection:**
- Fixed effects: Homogeneous studies
- Random effects: Heterogeneous studies
- Consider prediction intervals

**Heterogeneity Investigation:**
- Subgroup analysis
- Meta-regression
- Sensitivity analysis

## Reporting Guidelines

### 1. PRISMA Statement

**Required Elements:**
- Systematic search strategy
- Study selection process
- Data extraction procedures
- Risk of bias assessment
- Statistical methods
- Results presentation

### 2. Forest Plots

**Components:**
- Individual study effects
- Confidence intervals
- Study weights
- Pooled estimate
- Heterogeneity statistics

### 3. Example Reporting

"A random-effects meta-analysis of 15 studies (N = 3,247) revealed a moderate positive effect of the intervention (g = 0.52, 95% CI: 0.31-0.73, p < 0.001). Substantial heterogeneity was observed (I² = 68%, Q = 43.7, p < 0.001). Meta-regression indicated that effect size was moderated by study quality (β = 0.23, p = 0.04), with higher quality studies showing larger effects. Egger's test suggested minimal publication bias (p = 0.18), and trim-and-fill analysis estimated 2 missing studies with an adjusted effect of g = 0.48."

This comprehensive guide provides the foundation for conducting and interpreting meta-analyses across various research domains and study designs.
`,Gf=`# Variable Tree Analysis: Comprehensive Reference Guide

This comprehensive guide covers Variable Tree Analysis, an innovative visualization and analytical technique for exploring hierarchical relationships between variables. This method is essential for understanding complex data structures, identifying patterns in multivariate data, and creating intuitive representations of variable interactions across diverse research domains.

## Overview

Variable Tree Analysis is a hierarchical data exploration technique that creates tree-like structures to visualize how 2-4 variables relate to each other at different levels of granularity. Unlike traditional clustering or factor analysis, Variable Tree Analysis focuses on creating meaningful hierarchical partitions of data based on variable combinations, providing both statistical summaries and intuitive visualizations.

## Theoretical Foundation

### 1. Hierarchical Data Partitioning

**Tree Structure:**
$$T = \\{N, E, r\\}$$

Where:
- N = set of nodes
- E = set of edges
- r = root node

**Node Definition:**
$$N_i = \\{D_i, S_i, C_i\\}$$

Where:
- $D_i$ = data subset at node i
- $S_i$ = statistical summary at node i
- $C_i$ = children of node i

**Partitioning Function:**
$$P(D, V) = \\{D_1, D_2, ..., D_k\\}$$

Where D is data and V is partitioning variable.

### 2. Information-Theoretic Measures

**Entropy:**
$$H(X) = -\\sum_{i=1}^n p(x_i) \\log_2 p(x_i)$$

**Conditional Entropy:**
$$H(Y|X) = \\sum_{x} p(x) H(Y|X=x)$$

**Information Gain:**
$$IG(Y,X) = H(Y) - H(Y|X)$$

**Gain Ratio:**
$$GR(Y,X) = \\frac{IG(Y,X)}{H(X)}$$

### 3. Statistical Measures at Nodes

**Mean and Variance:**
$$\\mu_i = \\frac{1}{n_i} \\sum_{j=1}^{n_i} x_{ij}$$
$$\\sigma_i^2 = \\frac{1}{n_i-1} \\sum_{j=1}^{n_i} (x_{ij} - \\mu_i)^2$$

**Confidence Intervals:**
$$CI = \\mu_i \\pm t_{\\alpha/2,n_i-1} \\frac{\\sigma_i}{\\sqrt{n_i}}$$

**Effect Sizes (Cohen's d):**
$$d = \\frac{\\mu_1 - \\mu_2}{\\sqrt{\\frac{(n_1-1)\\sigma_1^2 + (n_2-1)\\sigma_2^2}{n_1+n_2-2}}}$$

## Tree Construction Algorithms

### 1. Recursive Partitioning

**CART-Based Approach:**
1. Select best splitting variable and value
2. Partition data into subsets
3. Recursively apply to each subset
4. Stop when criteria met

**Splitting Criterion (Continuous):**
$$\\text{Impurity} = \\sum_{i=1}^k \\frac{n_i}{n} \\text{Var}(Y_i)$$

**Splitting Criterion (Categorical):**
$$\\text{Impurity} = \\sum_{i=1}^k \\frac{n_i}{n} H(Y_i)$$

### 2. Information-Based Splitting

**Best Split Selection:**
$$\\text{Split}^* = \\arg\\max_{v,t} IG(Y, X_v \\leq t)$$

**Multi-way Splits:**
$$IG_{multi} = H(Y) - \\sum_{i=1}^k \\frac{n_i}{n} H(Y_i)$$

**Pruning Criteria:**
- Minimum samples per leaf
- Maximum tree depth
- Minimum information gain
- Statistical significance tests

### 3. Ensemble Methods

**Random Forest Approach:**
1. Bootstrap sampling
2. Random variable selection
3. Build multiple trees
4. Aggregate results

**Variable Importance:**
$$VI_j = \\frac{1}{B} \\sum_{b=1}^B \\sum_{t \\in T_b} p(t) \\Delta_j(t)$$

Where $\\Delta_j(t)$ is impurity decrease from variable j at node t.

## Multi-Variable Tree Construction

### 1. Two-Variable Trees

**Bivariate Partitioning:**
$$P(D, X_1, X_2) = \\{D_{ij}: X_1 \\in C_i, X_2 \\in C_j\\}$$

**Interaction Effects:**
$$\\text{Interaction} = \\mu_{11} + \\mu_{22} - \\mu_{12} - \\mu_{21}$$

**Visualization:**
- 2D grid representation
- Heatmap overlays
- Contour plots

### 2. Three-Variable Trees

**Trivariate Structure:**
$$T_{3D} = \\{(X_1, X_2, X_3) \\rightarrow Y\\}$$

**Hierarchical Levels:**
1. Primary split on X₁
2. Secondary split on X₂
3. Tertiary split on X₃

**3D Visualization:**
- Cube partitioning
- Interactive 3D plots
- Slice-based views

### 3. Four-Variable Trees

**Quaternary Structure:**
$$T_{4D} = \\{(X_1, X_2, X_3, X_4) \\rightarrow Y\\}$$

**Complexity Management:**
- Hierarchical importance ordering
- Dimension reduction techniques
- Interactive filtering

**Visualization Strategies:**
- Parallel coordinates
- Multiple linked views
- Hierarchical displays

## Statistical Analysis at Nodes

### 1. Descriptive Statistics

**Central Tendency:**
- Mean, median, mode
- Trimmed means
- Robust estimators

**Variability:**
- Standard deviation
- Interquartile range
- Coefficient of variation

**Distribution Shape:**
- Skewness: $\\gamma_1 = \\frac{E[(X-\\mu)^3]}{\\sigma^3}$
- Kurtosis: $\\gamma_2 = \\frac{E[(X-\\mu)^4]}{\\sigma^4} - 3$

### 2. Inferential Statistics

**One-Sample Tests:**
$$t = \\frac{\\bar{x} - \\mu_0}{s/\\sqrt{n}}$$

**Two-Sample Tests:**
$$t = \\frac{\\bar{x}_1 - \\bar{x}_2}{\\sqrt{\\frac{s_1^2}{n_1} + \\frac{s_2^2}{n_2}}}$$

**ANOVA for Multiple Groups:**
$$F = \\frac{MSB}{MSW} = \\frac{\\sum_{i=1}^k n_i(\\bar{x}_i - \\bar{x})^2/(k-1)}{\\sum_{i=1}^k \\sum_{j=1}^{n_i} (x_{ij} - \\bar{x}_i)^2/(N-k)}$$

### 3. Effect Size Calculations

**Cohen's d Family:**
- Small: d = 0.2
- Medium: d = 0.5
- Large: d = 0.8

**Eta-squared:**
$$\\eta^2 = \\frac{SSB}{SST}$$

**Omega-squared:**
$$\\omega^2 = \\frac{SSB - (k-1)MSW}{SST + MSW}$$

## Visualization Techniques

### 1. Tree Diagrams

**Node Representation:**
- Size proportional to sample size
- Color coding for statistical significance
- Shape coding for variable types

**Edge Properties:**
- Thickness for effect size
- Style for relationship type
- Labels for split criteria

**Layout Algorithms:**
- Force-directed layouts
- Hierarchical positioning
- Circular arrangements

### 2. Interactive Features

**Drill-Down Capability:**
- Click to expand/collapse nodes
- Zoom to specific branches
- Filter by criteria

**Dynamic Updates:**
- Real-time recalculation
- Parameter adjustment
- Variable selection

**Linked Views:**
- Synchronized highlighting
- Coordinated filtering
- Multiple perspectives

### 3. Statistical Overlays

**Confidence Intervals:**
- Error bars on nodes
- Shaded regions
- Uncertainty visualization

**Significance Indicators:**
- Color coding (p-values)
- Symbol overlays
- Text annotations

**Distribution Displays:**
- Box plots at nodes
- Histograms
- Density curves

## Model Validation and Assessment

### 1. Cross-Validation

**K-Fold Cross-Validation:**
1. Divide data into k folds
2. Train on k-1 folds
3. Test on remaining fold
4. Repeat k times

**Performance Metrics:**
$$RMSE = \\sqrt{\\frac{1}{n}\\sum_{i=1}^n (y_i - \\hat{y}_i)^2}$$

$$MAE = \\frac{1}{n}\\sum_{i=1}^n |y_i - \\hat{y}_i|$$

### 2. Stability Analysis

**Bootstrap Resampling:**
1. Generate bootstrap samples
2. Build trees for each sample
3. Assess structural consistency
4. Calculate stability indices

**Stability Measures:**
$$\\text{Stability} = \\frac{\\text{Number of consistent splits}}{\\text{Total number of splits}}$$

### 3. Sensitivity Analysis

**Parameter Sensitivity:**
- Vary minimum node size
- Change splitting criteria
- Adjust pruning parameters

**Variable Importance:**
- Permutation importance
- Drop-column importance
- Shapley values

## Advanced Applications

### 1. Longitudinal Tree Analysis

**Time-Series Trees:**
$$T_t = f(X_1(t), X_2(t), ..., X_k(t))$$

**Change Detection:**
- Structural breaks
- Trend analysis
- Seasonal patterns

**Dynamic Visualization:**
- Animated transitions
- Time sliders
- Temporal overlays

### 2. Multilevel Tree Analysis

**Hierarchical Data:**
$$Y_{ij} = f(\\text{Level-1 variables}, \\text{Level-2 variables})$$

**Random Effects Trees:**
- Group-specific splits
- Random intercepts/slopes
- Variance component estimation

### 3. Survival Tree Analysis

**Time-to-Event Outcomes:**
$$h(t|x) = h_0(t) \\exp(\\beta' x)$$

**Splitting Criteria:**
- Log-rank test
- Likelihood ratio
- Concordance index

**Visualization:**
- Kaplan-Meier curves at nodes
- Hazard ratio displays
- Risk group identification

## Practical Implementation

### 1. Data Preparation

**Variable Selection:**
- Theoretical relevance
- Statistical significance
- Practical importance
- Multicollinearity assessment

**Data Cleaning:**
- Missing value treatment
- Outlier detection
- Transformation needs
- Scaling considerations

### 2. Parameter Tuning

**Tree Complexity:**
- Maximum depth
- Minimum samples per leaf
- Minimum samples per split
- Maximum features

**Optimization:**
- Grid search
- Random search
- Bayesian optimization
- Cross-validation

### 3. Interpretation Guidelines

**Node Analysis:**
- Statistical significance
- Practical significance
- Sample size adequacy
- Confidence intervals

**Path Analysis:**
- Decision rules
- Variable interactions
- Hierarchical effects
- Predictive accuracy

## Software Implementation

### 1. Algorithm Pseudocode

\`\`\`
FUNCTION BuildVariableTree(data, variables, target):
    IF stopping_criteria_met(data):
        RETURN create_leaf_node(data)
    
    best_split = find_best_split(data, variables)
    node = create_internal_node(best_split)
    
    FOR each subset in partition(data, best_split):
        child = BuildVariableTree(subset, variables, target)
        add_child(node, child)
    
    RETURN node
\`\`\`

### 2. Performance Optimization

**Memory Management:**
- Efficient data structures
- Lazy evaluation
- Garbage collection

**Computational Efficiency:**
- Parallel processing
- Vectorized operations
- Caching strategies

### 3. User Interface Design

**Interactive Controls:**
- Variable selection panels
- Parameter adjustment sliders
- Export/import functionality

**Visualization Options:**
- Multiple layout choices
- Customizable styling
- Print-ready outputs

## Quality Assurance

### 1. Validation Procedures

**Statistical Validation:**
- Significance testing
- Effect size reporting
- Confidence intervals
- Multiple comparison corrections

**Practical Validation:**
- Domain expert review
- Face validity assessment
- Predictive validity
- Construct validity

### 2. Reproducibility

**Documentation:**
- Parameter settings
- Data preprocessing steps
- Random seed values
- Software versions

**Code Sharing:**
- Version control
- Documented functions
- Example datasets
- Tutorial materials

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Variable selection rationale
- Tree construction algorithm
- Parameter settings
- Validation procedures

### 2. Results Section

**Required Information:**
- Tree structure description
- Node-level statistics
- Statistical significance tests
- Effect sizes and confidence intervals

### 3. Example Reporting

"Variable Tree Analysis was conducted using recursive partitioning with information gain as the splitting criterion. The final tree included 3 variables (age, education, income) with 12 terminal nodes. Cross-validation (10-fold) yielded an RMSE of 2.34 (95% CI: 2.18-2.51). The primary split on education (≤12 years vs. >12 years) explained 34% of outcome variance (F = 156.7, p < 0.001, η² = 0.34). Secondary splits on age and income further refined predictions, with all terminal nodes containing ≥30 observations and showing significant differences from the overall mean (all p < 0.05)."

This comprehensive guide provides the foundation for conducting and interpreting Variable Tree Analysis across various research applications and data exploration contexts.
`,Qf=`# Data Visualization: Comprehensive Reference Guide

This comprehensive guide covers data visualization principles, techniques, and best practices for creating effective statistical graphics and interactive visualizations. Data visualization is essential for exploratory data analysis, communicating findings, and revealing patterns in complex datasets across all research domains.

## Overview

Data visualization is the graphical representation of information and data using visual elements like charts, graphs, and maps. Effective visualization transforms abstract numerical data into accessible visual formats that facilitate understanding, pattern recognition, and decision-making. It serves both analytical and communicative purposes in statistical analysis.

## Principles of Effective Data Visualization

### 1. Fundamental Design Principles

**Clarity and Simplicity:**
- Minimize cognitive load
- Remove unnecessary elements (chartjunk)
- Focus attention on data patterns
- Use clear, descriptive labels

**Accuracy and Honesty:**
- Preserve data integrity
- Avoid misleading representations
- Use appropriate scales and axes
- Maintain proportional relationships

**Accessibility and Inclusivity:**
- Consider colorblind-friendly palettes
- Provide alternative text descriptions
- Ensure sufficient contrast ratios
- Support screen reader compatibility

### 2. Visual Encoding Principles

**Perceptual Hierarchy:**
1. Position (most accurate)
2. Length
3. Angle/Slope
4. Area
5. Volume
6. Color intensity
7. Color hue (least accurate)

**Gestalt Principles:**
- **Proximity:** Related elements appear close together
- **Similarity:** Similar elements are perceived as grouped
- **Continuity:** Elements following smooth paths are grouped
- **Closure:** Incomplete shapes are perceived as complete
- **Figure-Ground:** Distinguish foreground from background

### 3. Color Theory in Data Visualization

**Color Spaces:**
- **RGB:** Red, Green, Blue (additive)
- **HSV:** Hue, Saturation, Value
- **LAB:** Lightness, A (green-red), B (blue-yellow)

**Color Palette Types:**
- **Sequential:** Ordered data (light to dark)
- **Diverging:** Data with meaningful midpoint
- **Categorical:** Distinct categories
- **Highlighting:** Emphasis on specific values

**Accessibility Considerations:**
- Deuteranopia (red-green colorblindness): 6% of males
- Protanopia (red colorblindness): 2% of males
- Tritanopia (blue-yellow colorblindness): <1% of population

## Chart Type Selection Guidelines

### 1. Distribution Visualization

**Single Variable Distributions:**

**Histogram:**
- Continuous numerical data
- Shows frequency distribution
- Bin width affects interpretation
- Optimal bins: $k = \\sqrt{n}$ or Sturges' rule: $k = \\log_2(n) + 1$

**Density Plot:**
- Smooth estimate of distribution
- Kernel density estimation: $\\hat{f}(x) = \\frac{1}{nh}\\sum_{i=1}^n K\\left(\\frac{x-x_i}{h}\\right)$
- Bandwidth selection critical

**Box Plot:**
- Five-number summary visualization
- Median, quartiles, and outliers
- Effective for comparing distributions
- Whiskers extend to: $Q_1 - 1.5 \\times IQR$ and $Q_3 + 1.5 \\times IQR$

**Violin Plot:**
- Combines box plot with density estimation
- Shows distribution shape and summary statistics
- Better for multimodal distributions

### 2. Relationship Visualization

**Scatter Plot:**
- Two continuous variables
- Reveals correlation patterns
- Effective sample size: n < 10,000
- Overplotting solutions: transparency, jittering, binning

**Correlation Matrix Heatmap:**
- Multiple variable relationships
- Color intensity represents correlation strength
- Hierarchical clustering for variable ordering

**Regression Plots:**
- Linear relationships with confidence intervals
- Residual plots for assumption checking
- Leverage and influence diagnostics

### 3. Categorical Data Visualization

**Bar Chart:**
- Categorical frequencies or means
- Horizontal vs. vertical orientation
- Grouped and stacked variations
- Error bars for uncertainty

**Pie Chart:**
- Part-to-whole relationships
- Limited to ≤7 categories
- Start at 12 o'clock position
- Order by size (largest first)

**Stacked Bar Chart:**
- Multiple categorical variables
- Proportional relationships
- 100% stacked for percentages

### 4. Time Series Visualization

**Line Plot:**
- Temporal trends and patterns
- Multiple series comparison
- Seasonal decomposition
- Trend analysis

**Area Chart:**
- Cumulative values over time
- Stacked for multiple series
- Emphasizes magnitude

**Heatmap Calendar:**
- Daily patterns over years
- Seasonal trend identification
- Missing data visualization

## Statistical Graphics

### 1. Exploratory Data Analysis Plots

**Q-Q Plot (Quantile-Quantile):**
$$Q_{theoretical}(p) = F^{-1}(p)$$
$$Q_{sample}(p) = x_{(k)}, \\text{ where } k = \\lfloor np \\rfloor$$

**Purpose:** Assess distributional assumptions
**Interpretation:** Points on diagonal indicate good fit

**P-P Plot (Probability-Probability):**
$$P_{theoretical}(x) = F(x)$$
$$P_{sample}(x) = \\frac{\\text{rank}(x)}{n+1}$$

**Residual Plots:**
- Fitted vs. residuals
- Normal Q-Q of residuals
- Scale-location plots
- Leverage plots

### 2. Uncertainty Visualization

**Error Bars:**
- Standard error: $SE = \\frac{s}{\\sqrt{n}}$
- Confidence intervals: $\\bar{x} \\pm t_{\\alpha/2,df} \\times SE$
- Standard deviation: $s = \\sqrt{\\frac{\\sum(x_i - \\bar{x})^2}{n-1}}$

**Confidence Bands:**
- Regression confidence intervals
- Prediction intervals
- Bootstrap confidence regions

**Violin Plots with Quantiles:**
- Distribution shape with uncertainty
- Median and quartile overlays
- Sample size indicators

### 3. Multivariate Visualization

**Parallel Coordinates:**
- High-dimensional data exploration
- Pattern identification across variables
- Clustering visualization

**Radar/Spider Charts:**
- Multivariate profiles
- Performance comparisons
- Standardized variables recommended

**Principal Component Biplots:**
- Dimensionality reduction visualization
- Variable loadings and observations
- Explained variance indication

## Advanced Visualization Techniques

### 1. Interactive Visualization

**Brushing and Linking:**
- Selection propagation across plots
- Coordinated multiple views
- Real-time filtering

**Zooming and Panning:**
- Detail-on-demand exploration
- Multi-scale data investigation
- Overview + detail interfaces

**Animation:**
- Temporal data exploration
- Parameter space investigation
- Transition smoothing

### 2. Faceting and Small Multiples

**Facet Grids:**
- Conditional plots by categories
- Consistent scales for comparison
- Trellis displays

**Small Multiples Principle:**
- Edward Tufte's concept
- Repeated chart structure
- Different data subsets

### 3. Layered Graphics

**Grammar of Graphics:**
- Data layer
- Aesthetic mappings
- Geometric objects
- Statistical transformations
- Coordinate systems
- Faceting specifications

**ggplot2 Structure:**
\`\`\`
ggplot(data) + 
  aes(x, y, color) + 
  geom_point() + 
  stat_smooth() + 
  facet_wrap(~category)
\`\`\`

## Specialized Visualization Types

### 1. Network Visualization

**Node-Link Diagrams:**
- Vertices and edges representation
- Force-directed layouts
- Hierarchical arrangements

**Adjacency Matrices:**
- Matrix representation of connections
- Effective for dense networks
- Pattern identification

**Arc Diagrams:**
- Linear node arrangement
- Arc connections
- Temporal networks

### 2. Geospatial Visualization

**Choropleth Maps:**
- Regional data representation
- Color-coded values
- Normalization considerations

**Point Maps:**
- Location-specific data
- Size and color encoding
- Clustering for dense data

**Flow Maps:**
- Movement and migration patterns
- Origin-destination relationships
- Sankey diagram variations

### 3. Hierarchical Data

**Tree Diagrams:**
- Hierarchical structures
- Parent-child relationships
- Collapsible nodes

**Treemaps:**
- Space-filling visualization
- Nested rectangles
- Size and color encoding

**Sunburst Charts:**
- Radial tree representation
- Multi-level hierarchies
- Interactive exploration

## Color and Accessibility

### 1. Colorblind-Friendly Palettes

**Viridis Color Scale:**
- Perceptually uniform
- Colorblind accessible
- Monotonic luminance

**ColorBrewer Palettes:**
- Cartographic color schemes
- Tested for accessibility
- Print and web optimized

**Simulation Tools:**
- Coblis colorblind simulator
- Stark accessibility checker
- Color Oracle testing

### 2. Contrast and Readability

**WCAG Guidelines:**
- AA standard: 4.5:1 contrast ratio
- AAA standard: 7:1 contrast ratio
- Large text: 3:1 minimum

**Luminance Calculation:**
$$L = 0.2126 \\times R + 0.7152 \\times G + 0.0722 \\times B$$

### 3. Cultural Color Considerations

**Western Associations:**
- Red: danger, negative, stop
- Green: safe, positive, go
- Blue: trust, calm, professional

**Cross-Cultural Variations:**
- Red: luck in China, mourning in South Africa
- White: purity in West, mourning in East Asia
- Yellow: caution in West, imperial in China

## Common Visualization Mistakes

### 1. Misleading Representations

**Truncated Y-Axis:**
- Exaggerates differences
- Misleads interpretation
- Solution: Start at zero or clearly indicate break

**3D Effects:**
- Distorts data perception
- Adds unnecessary complexity
- Solution: Use 2D representations

**Inappropriate Chart Types:**
- Pie charts for many categories
- Line charts for categorical data
- Solution: Match chart type to data type

### 2. Cognitive Overload

**Too Much Information:**
- Cluttered displays
- Multiple competing elements
- Solution: Progressive disclosure, focus

**Poor Color Choices:**
- Rainbow color maps
- Insufficient contrast
- Solution: Perceptually uniform palettes

**Inconsistent Scales:**
- Different axes across subplots
- Misleading comparisons
- Solution: Consistent scaling, clear labeling

### 3. Technical Issues

**Overplotting:**
- Points obscure each other
- Pattern masking
- Solutions: Transparency, jittering, binning, sampling

**Aspect Ratio Problems:**
- Distorted trend perception
- Banking to 45° principle
- Solution: Optimize slope perception

**Missing Data Handling:**
- Invisible gaps in time series
- Misleading interpolation
- Solution: Explicit missing data indicators

## Software and Tools

### 1. Statistical Software

**R Ecosystem:**
- ggplot2: Grammar of graphics
- plotly: Interactive visualizations
- shiny: Web applications
- leaflet: Interactive maps

**Python Libraries:**
- matplotlib: Basic plotting
- seaborn: Statistical visualization
- plotly: Interactive graphics
- bokeh: Web-based visualization

### 2. Specialized Tools

**Tableau:**
- Business intelligence
- Drag-and-drop interface
- Dashboard creation

**D3.js:**
- Web-based custom visualizations
- Data-driven documents
- High customization

**Observable:**
- Collaborative visualization
- Notebook-style development
- Real-time collaboration

### 3. Web Technologies

**SVG (Scalable Vector Graphics):**
- Resolution-independent
- Interactive elements
- CSS styling

**Canvas API:**
- High-performance rendering
- Pixel-level control
- Animation support

**WebGL:**
- GPU-accelerated graphics
- 3D visualizations
- Large dataset handling

## Best Practices and Guidelines

### 1. Design Process

**Understand Your Audience:**
- Technical expertise level
- Domain knowledge
- Viewing context

**Define Objectives:**
- Exploratory vs. explanatory
- Key messages to convey
- Decision support needs

**Iterate and Test:**
- User feedback collection
- A/B testing
- Accessibility validation

### 2. Data Preparation

**Data Quality:**
- Missing value handling
- Outlier identification
- Transformation needs

**Aggregation Levels:**
- Appropriate granularity
- Summary statistics
- Temporal resolution

**Performance Considerations:**
- Data size limitations
- Rendering speed
- Memory constraints

### 3. Presentation Guidelines

**Titles and Labels:**
- Descriptive, informative titles
- Clear axis labels with units
- Legend placement and clarity

**Annotations:**
- Highlight key findings
- Provide context
- Guide interpretation

**Documentation:**
- Data sources
- Methodology notes
- Interpretation guidance

## Evaluation and Validation

### 1. Effectiveness Metrics

**Accuracy:**
- Correct data reading
- Pattern identification
- Trend recognition

**Efficiency:**
- Time to insight
- Cognitive load
- Task completion rate

**Satisfaction:**
- User preference
- Aesthetic appeal
- Engagement level

### 2. Usability Testing

**Think-Aloud Protocols:**
- Verbal feedback during use
- Cognitive process insight
- Problem identification

**Eye-Tracking Studies:**
- Visual attention patterns
- Scanning behavior
- Fixation analysis

**A/B Testing:**
- Comparative effectiveness
- Statistical significance
- User preference

### 3. Accessibility Auditing

**Screen Reader Testing:**
- Alternative text quality
- Navigation structure
- Content accessibility

**Color Contrast Validation:**
- Automated testing tools
- Manual verification
- Multiple device testing

**Motor Accessibility:**
- Keyboard navigation
- Touch target sizes
- Interaction alternatives

## Future Trends and Technologies

### 1. Emerging Technologies

**Virtual Reality (VR):**
- Immersive data exploration
- 3D data environments
- Spatial data analysis

**Augmented Reality (AR):**
- Contextual data overlay
- Real-world integration
- Mobile applications

**Machine Learning Integration:**
- Automated chart selection
- Pattern detection
- Anomaly highlighting

### 2. Advanced Techniques

**Narrative Visualization:**
- Story-driven presentations
- Guided exploration
- Sequential revelation

**Responsive Design:**
- Multi-device optimization
- Adaptive layouts
- Progressive enhancement

**Real-Time Visualization:**
- Streaming data integration
- Live dashboard updates
- Performance optimization

This comprehensive guide provides the foundation for creating effective, accessible, and impactful data visualizations across various domains and applications, from exploratory analysis to publication-ready graphics.
`,Yf={"logistic-regression":If,"t-tests-and-alternatives":zf,"anova-tests-and-alternatives":Ff,"numerical-descriptives":Df,"categorical-descriptives":Nf,"epidemiological-calculators":Lf,"sample-size-power-analysis":Of,"correlation-linear-regression":qf,"exploratory-factor-analysis":jf,"confirmatory-factor-analysis":Bf,"survival-analysis":Vf,"reliability-analysis":Wf,"mediation-moderation":Hf,"cluster-analysis":Uf,"meta-analysis":Xf,"variable-tree-analysis":Gf,"data-visualization":Qf},Kf=`
  .markdown-content h1 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    margin-top: 1.5rem;
    color: #1976d2;
  }
  .markdown-content h2 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
    color: #0d47a1;
  }
  .markdown-content h3 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    margin-top: 1.25rem;
  }
  .markdown-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }
  .markdown-content ul, .markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
  }
  .markdown-content li {
    margin-bottom: 0.5rem;
  }
  .markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
  }
  .markdown-content th, .markdown-content td {
    border: 1px solid #ddd;
    padding: 0.75rem;
  }
  .markdown-content th {
    background-color: #f8f8f8;
  }
  .markdown-content pre {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 1rem;
  }
  .markdown-content code {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
  }
  .markdown-content blockquote {
    border-left: 4px solid #ddd;
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
  }
`,lm=({onNavigate:n,initialSubPage:t})=>{const e=t,[i,r]=Wn.useState(""),[o,a]=Wn.useState(!0),[s,l]=Wn.useState(null),u=Yr(),d=ea.find(c=>c.id===e);return Wn.useEffect(()=>{(async()=>{if(!d){l("Tutorial not found"),a(!1);return}const m=Yf[d.id];if(m===void 0){l("Tutorial content not found for this ID."),a(!1);return}try{const f=await ya().use(ws).use(ju).use(Ju).use(Pf).use(Wd).use(fd).process(m);r(String(f)),a(!1)}catch(f){console.error("Error processing markdown:",f),l("Failed to process tutorial content."),a(!1)}})()},[e,d]),Wn.useEffect(()=>{if(i){const c=document.querySelector(".markdown-content");c&&c.querySelectorAll(".katex").forEach(f=>{let g=f.nextSibling;for(;g;)g.nodeType===Node.TEXT_NODE&&g.textContent&&g.textContent.includes("== $0")||g.nodeType===Node.ELEMENT_NODE&&g.textContent&&g.textContent.includes("== $0")?(c&&g&&c.removeChild(g),g=f.nextSibling):g=g.nextSibling})}},[i]),o?z.jsx(_e,{maxWidth:"lg",sx:{py:4,display:"flex",justifyContent:"center"},children:z.jsx(Kr,{})}):s||!d?z.jsx(_e,{maxWidth:"lg",sx:{py:4},children:z.jsxs(ie,{sx:{p:4,bgcolor:Se(u.palette.error.main,.1)},children:[z.jsx(wn,{variant:"h5",color:"error",gutterBottom:!0,children:"Error"}),z.jsx(wn,{variant:"body1",children:s||"Tutorial not found. Please return to the Knowledge Base and try again."}),z.jsx(Vn,{sx:{mt:2},children:z.jsx(ve,{component:"button",onClick:()=>n==null?void 0:n("app/knowledge-base"),color:"primary",sx:{background:"none",border:"none",cursor:"pointer",textDecoration:"underline","&:hover":{textDecoration:"none"}},children:"Return to Knowledge Base"})})]})}):z.jsxs(_e,{maxWidth:"lg",sx:{py:4},children:[z.jsxs(na,{children:[z.jsxs("title",{children:[d.name," | DataStatPro Knowledge Base"]}),z.jsx("meta",{name:"description",content:d.detailedDescription}),z.jsx("meta",{name:"keywords",content:`${d.name}, statistics, data analysis, tutorial, ${d.category.toLowerCase()}, DataStatPro`}),z.jsx("meta",{name:"author",content:"DataStatPro"}),z.jsx("link",{rel:"canonical",href:`https://datastatpro.com/app#knowledge-base/${d.id}`}),z.jsx("meta",{property:"og:title",content:`${d.name} | DataStatPro Knowledge Base`}),z.jsx("meta",{property:"og:description",content:d.detailedDescription}),z.jsx("meta",{property:"og:type",content:"article"}),z.jsx("meta",{property:"og:url",content:`https://datastatpro.com/app#knowledge-base/${d.id}`}),z.jsx("meta",{property:"og:site_name",content:"DataStatPro"}),z.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),z.jsx("meta",{name:"twitter:title",content:`${d.name} | DataStatPro Knowledge Base`}),z.jsx("meta",{name:"twitter:description",content:d.detailedDescription}),z.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"EducationalResource",name:d.name,description:d.detailedDescription,educationalLevel:"Advanced",learningResourceType:"Tutorial",about:d.category,provider:{"@type":"Organization",name:"DataStatPro",url:"https://datastatpro.com"},url:`https://datastatpro.com/app#knowledge-base/${d.id}`,dateModified:new Date().toISOString().split("T")[0]})})]}),z.jsx("style",{children:Kf}),z.jsxs(Jr,{sx:{mb:2},children:[z.jsx(ve,{component:"button",onClick:()=>n==null?void 0:n("dashboard"),color:"inherit",sx:{background:"none",border:"none",cursor:"pointer",textDecoration:"underline","&:hover":{textDecoration:"none"}},children:"Home"}),z.jsx(ve,{component:"button",onClick:()=>n==null?void 0:n("app/knowledge-base"),color:"inherit",sx:{background:"none",border:"none",cursor:"pointer",textDecoration:"underline","&:hover":{textDecoration:"none"}},children:"Knowledge Base"}),z.jsx(wn,{color:"text.primary",children:d.name})]}),z.jsxs(ie,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${Se(u.palette.primary.main,.1)} 0%, ${Se(u.palette.info.main,.1)} 100%)`,borderRadius:2,display:"flex",alignItems:"center",gap:2},children:[z.jsx(Vn,{sx:{bgcolor:d.color,width:56,height:56,borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white"},children:d.icon}),z.jsxs(Vn,{children:[z.jsx(wn,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:d.name}),z.jsx(wn,{variant:"body1",color:"text.secondary",children:d.shortDescription})]})]}),z.jsxs(ie,{sx:{p:3,mb:4,borderRadius:2},children:[z.jsx(wn,{variant:"h6",gutterBottom:!0,sx:{mb:2,fontWeight:"bold"},children:"📹 Tutorial Video"}),z.jsx(Vn,{sx:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",borderRadius:1,bgcolor:"grey.100",border:"1px solid",borderColor:"grey.300"},children:z.jsx("iframe",{src:"https://www.youtube.com/embed/rCH-99czNMc",title:`${d.name} - Tutorial Video`,allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",border:"none"}})}),z.jsxs(wn,{variant:"body2",color:"text.secondary",sx:{mt:2},children:["Watch this comprehensive tutorial video to learn about ",d.name.toLowerCase(),". The video covers key concepts, practical examples, and step-by-step guidance."]})]}),z.jsxs(ie,{sx:{p:4,borderRadius:2},children:[z.jsx(Vn,{className:"markdown-content",dangerouslySetInnerHTML:{__html:i}})," "]})]})};export{lm as default};
