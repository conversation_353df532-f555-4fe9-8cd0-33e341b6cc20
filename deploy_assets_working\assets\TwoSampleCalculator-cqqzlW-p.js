import{u as te,j as e,B as a,bL as re,bM as A,G as n,R as W,e as r,bN as N,aE as c,I as d,bs as f,ah as C,f as se,bH as oe,bO as ae,bP as ne,k as ie,i as le,l as pe}from"./mui-libs-CfwFIaTD.js";import{r as i}from"./react-libs-Cr2nE3UY.js";import{j as L}from"./other-utils-CR9xr_gI.js";import{g as $}from"./sampleSize-nX7GCAZC.js";import{R as ce,d as de,C as ue,X as he,Y as me,T as xe,e as je}from"./charts-recharts-d3-BEF1Y_jn.js";const Me=()=>{const u=te(),[h,_]=i.useState("proportion"),[p,B]=i.useState(.95),[g,I]=i.useState(.8),[S,D]=i.useState(.5),[v,R]=i.useState(.7),[b,k]=i.useState(50),[w,E]=i.useState(55),[M,q]=i.useState(5),[z,F]=i.useState(10),[l,H]=i.useState(null),[K,X]=i.useState([]);i.useEffect(()=>{Y(),O()},[p,g,S,v,b,w,M,z,h]);const Y=()=>{const t=$(1-(1-p)/2),s=$(g);let y;if(h==="proportion"){const m=S,o=v,T=(m+o)/2,x=Math.pow(t*Math.sqrt(2*T*(1-T))+s*Math.sqrt(m*(1-m)+o*(1-o)),2),j=Math.pow(m-o,2);y=Math.ceil(x/j)}else{const m=2*Math.pow(t+s,2)*(Math.pow(M,2)+Math.pow(z,2)),o=Math.pow(b-w,2);y=Math.ceil(m/o)}H(y)},O=()=>{const t=[],s=Math.max(10,Math.floor(l?l*.5:20)),y=Math.ceil(l?l*1.5:100),m=Math.max(1,Math.floor((y-s)/20));for(let o=s;o<=y;o+=m){let T;if(h==="proportion"){const x=S,j=v,P=(x+j)/2,G=$(1-(1-p)/2),V=Math.sqrt(2*P*(1-P)/o),ee=(Math.abs(x-j)-G*V)/Math.sqrt((x*(1-x)+j*(1-j))/o);T=L.normal.cdf(ee,0,1)}else{const x=$(1-(1-p)/2),j=Math.abs(b-w),P=Math.sqrt((Math.pow(M,2)+Math.pow(z,2))/o),G=(j-x*P)/P;T=L.normal.cdf(G,0,1)}t.push({sampleSize:o,power:Math.max(0,Math.min(1,T))})}X(t)},Z=(t,s)=>{s!==null&&(_(s),s==="proportion"?(B(.95),I(.8),D(.5),R(.7)):s==="mean"&&(B(.95),I(.8),k(50),E(55),q(5),F(10)))},J=()=>{if(l){let t="";h==="proportion"?t=`Required Sample Size: ${l} per group for comparing two independent proportions (${Math.round(S*100)}% vs ${Math.round(v*100)}%) with ${Math.round(g*100)}% power at a ${Math.round(p*100)}% confidence level.`:t=`Required Sample Size: ${l} per group for comparing two independent means (${b} vs ${w}) with standard deviations of ${M} and ${z}, with ${Math.round(g*100)}% power at a ${Math.round(p*100)}% confidence level.`,navigator.clipboard.writeText(t)}},Q=()=>{console.log("Export PDF functionality would go here")},U=()=>{B(.95),I(.8),D(.5),R(.7),k(50),E(55),q(10),F(15)};return e.jsxs(e.Fragment,{children:[e.jsx(a,{sx:{mb:3},children:e.jsxs(re,{value:h,exclusive:!0,onChange:Z,"aria-label":"two sample calculator type",fullWidth:!0,children:[e.jsx(A,{value:"proportion","aria-label":"two independent proportions",sx:{"&.Mui-selected":{bgcolor:u.palette.primary.main,color:u.palette.primary.contrastText,"&:hover":{bgcolor:u.palette.primary.dark}}},children:"Two Independent Proportions"}),e.jsx(A,{value:"mean","aria-label":"two independent means",sx:{"&.Mui-selected":{bgcolor:u.palette.secondary.main,color:u.palette.secondary.contrastText,"&:hover":{bgcolor:u.palette.secondary.dark}}},children:"Two Independent Means"})]})}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(W,{elevation:1,sx:{p:3},children:[e.jsx(r,{variant:"h6",gutterBottom:!0,children:"Input Parameters"}),e.jsxs(a,{sx:{mb:4},children:[e.jsx(r,{gutterBottom:!0,children:"Confidence Level (α)"}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(N,{value:p*100,onChange:(t,s)=>B(s/100),step:1,min:80,max:99,marks:[{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"},{value:99,label:"99%"}]})})})]}),e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Statistical Power (1-β)",e.jsx(c,{title:"The probability of correctly detecting a true difference between groups.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,children:e.jsx(N,{value:g*100,onChange:(t,s)=>I(s/100),step:5,min:70,max:95,marks:[{value:70,label:"70%"},{value:80,label:"80%"},{value:90,label:"90%"},{value:95,label:"95%"}]})})})]}),h==="proportion"&&e.jsxs(e.Fragment,{children:[e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Expected Proportion - Group 1 (p₁)",e.jsx(c,{title:"The expected proportion in the first group.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsxs(n,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(n,{item:!0,xs:!0,children:e.jsx(C,{value:Math.round(S*100),onChange:t=>D(Number(t.target.value)/100),type:"number",inputProps:{min:1,max:99,step:1},fullWidth:!0})}),e.jsx(n,{item:!0,children:e.jsx(r,{children:"%"})})]})]}),e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Expected Proportion - Group 2 (p₂)",e.jsx(c,{title:"The expected proportion in the second group.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsxs(n,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(n,{item:!0,xs:!0,children:e.jsx(C,{value:Math.round(v*100),onChange:t=>R(Number(t.target.value)/100),type:"number",inputProps:{min:1,max:99,step:1},fullWidth:!0})}),e.jsx(n,{item:!0,children:e.jsx(r,{children:"%"})})]})]})]}),h==="mean"&&e.jsxs(e.Fragment,{children:[e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Expected Mean - Group 1 (μ₁)",e.jsx(c,{title:"The expected mean in the first group.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsx(C,{type:"number",value:b,onChange:t=>k(Number(t.target.value)),inputProps:{step:.1},fullWidth:!0})]}),e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Expected Mean - Group 2 (μ₂)",e.jsx(c,{title:"The expected mean in the second group.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsx(C,{type:"number",value:w,onChange:t=>E(Number(t.target.value)),inputProps:{step:.1},fullWidth:!0})]}),e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Standard Deviation - Group 1 (σ₁)",e.jsx(c,{title:"The expected standard deviation in the first group.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsx(C,{type:"number",value:M,onChange:t=>q(Number(t.target.value)),inputProps:{min:.1,step:.1},fullWidth:!0})]}),e.jsxs(a,{sx:{mb:4},children:[e.jsxs(r,{gutterBottom:!0,children:["Standard Deviation - Group 2 (σ₂)",e.jsx(c,{title:"The expected standard deviation in the second group.",children:e.jsx(d,{size:"small",sx:{ml:1},children:e.jsx(f,{fontSize:"small"})})})]}),e.jsx(C,{type:"number",value:z,onChange:t=>F(Number(t.target.value)),inputProps:{min:.1,step:.1},fullWidth:!0})]})]}),e.jsx(se,{variant:"outlined",startIcon:e.jsx(oe,{}),onClick:U,fullWidth:!0,sx:{mt:2},children:"Reset"})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(W,{elevation:1,sx:{p:3},children:[e.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(r,{variant:"h6",children:"Results"}),e.jsxs(a,{children:[e.jsx(c,{title:"Copy results",children:e.jsx(d,{onClick:J,children:e.jsx(ae,{})})}),e.jsx(c,{title:"Export as PDF",children:e.jsx(d,{onClick:Q,children:e.jsx(ne,{})})})]})]}),e.jsx(ie,{sx:{mb:4,bgcolor:le(u.palette.primary.main,.1)},children:e.jsxs(pe,{sx:{textAlign:"center"},children:[e.jsx(r,{variant:"h6",gutterBottom:!0,children:"Required Sample Size"}),e.jsx(r,{variant:"h2",color:"primary",children:l}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"per group"})]})}),e.jsx(r,{variant:"h6",gutterBottom:!0,children:"Power by Sample Size"}),e.jsx(a,{sx:{height:300,mb:2},children:e.jsx(ce,{width:"100%",height:"100%",children:e.jsxs(de,{data:K,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(ue,{strokeDasharray:"3 3"}),e.jsx(he,{dataKey:"sampleSize",label:{value:"Sample Size (per group)",position:"outerBottom",offset:15,style:{fontSize:"12px"}},height:70}),e.jsx(me,{label:{value:"Statistical Power",angle:-90,position:"outside",offset:-60,style:{fontSize:"12px"}},tickFormatter:t=>`${Math.round(t*100)}%`,domain:[0,1],width:100}),e.jsx(xe,{formatter:t=>[`${(t*100).toFixed(1)}%`,"Power"],labelFormatter:t=>`Sample Size: ${t} per group`}),e.jsx(je,{type:"monotone",dataKey:"power",stroke:u.palette.primary.main,activeDot:{r:8},strokeWidth:2})]})})}),e.jsxs(a,{sx:{mt:3},children:[e.jsx(r,{variant:"h6",gutterBottom:!0,children:"Interpretation"}),h==="proportion"?e.jsxs(r,{variant:"body1",children:["A sample size of ",l," per group is needed to detect a difference between proportions of ",Math.round(S*100),"% and ",Math.round(v*100),"% with ",Math.round(g*100),"% power at a ",Math.round(p*100),"% confidence level."]}):e.jsxs(r,{variant:"body1",children:["A sample size of ",l," per group is needed to detect a difference between means of ",b," and ",w," (with standard deviations of ",M," and ",z,") with ",Math.round(g*100),"% power at a ",Math.round(p*100),"% confidence level."]})]})]})})]})]})};export{Me as default};
