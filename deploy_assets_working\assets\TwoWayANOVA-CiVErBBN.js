import{u as tt,a as it,j as e,B as u,R as z,br as m,X as nt,e as l,g as N,aF as xe,G as c,ai as X,b9 as H,ba as Y,bb as Q,D as De,Z as Ie,f as Ve,ae as st,bt as Me,bu as he,bv as at,h as T,I as Le,bw as rt,a6 as lt,a7 as Z,bx as ot,aX as ct,k as D,l as I,o as V,by as fe,i as y,aC as dt,aD as xt,a3 as ht,ao as _,ap as J,aq as R,ar as E,as as s,at as ee,aE as ft}from"./mui-libs-CfwFIaTD.js";import{r as F}from"./react-libs-Cr2nE3UY.js";import{a as pt,D as te}from"./index-Bpan7Tbe.js";import{p as gt,t as mt}from"./twoWayANOVA-CIsn6bwj.js";import{R as pe,d as jt,C as Be,X as ke,Y as Oe,T as ge,L as ut,e as bt,B as yt,a as vt,b as qe,E as St,P as $t,f as Tt}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./other-utils-CR9xr_gI.js";function ie(U){const{children:p,value:K,index:n,...ne}=U;return e.jsx("div",{role:"tabpanel",hidden:K!==n,id:`results-tabpanel-${n}`,"aria-labelledby":`results-tab-${n}`,...ne,children:K===n&&e.jsx(u,{sx:{pt:3},children:p})})}const Vt=()=>{var Se,$e,Te,We,Ce,Fe;const{datasets:U,currentDataset:p,setCurrentDataset:K}=pt(),n=tt();it(n.breakpoints.down("sm"));const[ne,Pe]=F.useState((p==null?void 0:p.id)||""),[se,ae]=F.useState(""),[M,re]=F.useState(""),[L,le]=F.useState(""),[B,me]=F.useState(!1),[je,k]=F.useState(null),[i,A]=F.useState(null),[ue,we]=F.useState({summary:!0,anova:!0,descriptive:!0,posthoc:!1,assumptions:!1}),[O,be]=F.useState(0),Ue=(p==null?void 0:p.columns.filter(t=>t.type===te.NUMERIC))||[],ye=(p==null?void 0:p.columns.filter(t=>t.type===te.CATEGORICAL||t.type===te.ORDINAL||t.type===te.BOOLEAN))||[],Ke=t=>{const a=t.target.value;Pe(a);const r=U.find(d=>d.id===a);r&&K(r),ae(""),re(""),le(""),A(null),k(null)},Ge=t=>{ae(t.target.value),A(null)},Xe=t=>{re(t.target.value),A(null)},He=t=>{le(t.target.value),A(null)},ve=()=>p&&se&&M&&L&&M!==L,Ye=t=>{we(a=>({...a,[t]:!a[t]}))},Qe=(t,a)=>{be(a)},q=t=>t<.01?{label:"Negligible",color:n.palette.grey[500]}:t<.06?{label:"Small",color:n.palette.info.main}:t<.14?{label:"Medium",color:n.palette.warning.main}:{label:"Large",color:n.palette.success.main},Ze=()=>{if(!ve()){k("Please select a dataset, a dependent variable, and two different factor variables.");return}me(!0),k(null),A(null),setTimeout(()=>{var t,a,r,d,o,b,W,j,P;try{const G=p.columns.find(g=>g.id===se),oe=p.columns.find(g=>g.id===M),Je=p.columns.find(g=>g.id===L),w=G.name,v=oe.name,S=Je.name,ce=gt(p.data,w,v,S),$=mt(ce,v,S),Ne=[...new Set(ce.map(g=>g.factorA))],Ee=[...new Set(ce.map(g=>g.factorB))],x=$.anovaTable.find(g=>g.source===v),h=$.anovaTable.find(g=>g.source===S),f=$.anovaTable.find(g=>g.source===`${v} × ${S}`),C=$.anovaTable.find(g=>g.source==="Error"),Ae=(x==null?void 0:x.p)!==null&&(x==null?void 0:x.p)!==void 0&&x.p<.05,ze=(h==null?void 0:h.p)!==null&&(h==null?void 0:h.p)!==void 0&&h.p<.05,de=(f==null?void 0:f.p)!==null&&(f==null?void 0:f.p)!==void 0&&f.p<.05;let Re={summary:`A ${Ne.length} × ${Ee.length} between-subjects ANOVA was conducted to examine the effects of ${v} and ${S} on ${w}.`,mainEffects:{factor1:{significant:Ae,text:`The main effect of ${v} was ${Ae?"statistically significant":"not statistically significant"}, F(${x==null?void 0:x.df}, ${C==null?void 0:C.df}) = ${(t=x==null?void 0:x.F)==null?void 0:t.toFixed(2)}, p = ${(a=x==null?void 0:x.p)==null?void 0:a.toFixed(3)}, η² = ${(r=x==null?void 0:x.etaSquared)==null?void 0:r.toFixed(3)}.`,effectSize:q((x==null?void 0:x.etaSquared)||0)},factor2:{significant:ze,text:`The main effect of ${S} was ${ze?"statistically significant":"not statistically significant"}, F(${h==null?void 0:h.df}, ${C==null?void 0:C.df}) = ${(d=h==null?void 0:h.F)==null?void 0:d.toFixed(2)}, p = ${(o=h==null?void 0:h.p)==null?void 0:o.toFixed(3)}, η² = ${(b=h==null?void 0:h.etaSquared)==null?void 0:b.toFixed(3)}.`,effectSize:q((h==null?void 0:h.etaSquared)||0)}},interaction:{significant:de,text:`The interaction effect between ${v} and ${S} was ${de?"statistically significant":"not statistically significant"}, F(${f==null?void 0:f.df}, ${C==null?void 0:C.df}) = ${(W=f==null?void 0:f.F)==null?void 0:W.toFixed(2)}, p = ${(j=f==null?void 0:f.p)==null?void 0:j.toFixed(3)}, η² = ${(P=f==null?void 0:f.etaSquared)==null?void 0:P.toFixed(3)}.`,effectSize:q((f==null?void 0:f.etaSquared)||0),interpretation:de?`The significant interaction suggests that the effect of ${v} on ${w} differs depending on the level of ${S}. Simple main effects analysis is recommended to understand the nature of this interaction.`:`The non-significant interaction suggests that the effect of ${v} on ${w} is consistent across levels of ${S}, and vice versa.`}};const et={dependentVariable:w,factor1:v,factor2:S,factor1Levels:Ne,factor2Levels:Ee,anovaTable:$.anovaTable,descriptiveStats:$.descriptiveStats,marginalMeansFactor1:$.marginalMeansFactor1,marginalMeansFactor2:$.marginalMeansFactor2,interpretation:Re,assumptions:$.assumptions,totalN:$.totalN,timestamp:new Date};A(et),be(0)}catch(G){const oe=G instanceof Error?G.message:"An error occurred during Two-Way ANOVA calculation.";k(oe)}finally{me(!1)}},1500)},_e=()=>{if(!i)return;const t=`Two-Way ANOVA Results
=====================================
Analysis Date: ${i.timestamp.toLocaleString()}
Dependent Variable: ${i.dependentVariable}
Factor 1: ${i.factor1}
Factor 2: ${i.factor2}
Total N: ${i.totalN}

ANOVA Summary Table
-------------------
${i.anovaTable.map(o=>{var b,W,j;return`${o.source}: F(${o.df||"-"}) = ${((b=o.F)==null?void 0:b.toFixed(2))||"-"}, p = ${((W=o.p)==null?void 0:W.toFixed(3))||"-"}, η² = ${((j=o.etaSquared)==null?void 0:j.toFixed(3))||"-"}`}).join(`
`)}

Interpretation
--------------
${i.interpretation.summary}
${i.interpretation.mainEffects.factor1.text}
${i.interpretation.mainEffects.factor2.text}
${i.interpretation.interaction.text}
${i.interpretation.interaction.interpretation}

Descriptive Statistics
---------------------
${i.descriptiveStats.map(o=>`${i.factor1}: ${o.factor1Level}, ${i.factor2}: ${o.factor2Level} - Mean: ${o.mean.toFixed(2)}, SD: ${o.sd.toFixed(2)}, N: ${o.N}`).join(`
`)}
`,a=new Blob([t],{type:"text/plain"}),r=URL.createObjectURL(a),d=document.createElement("a");d.href=r,d.download=`two-way-anova-results-${new Date().toISOString().slice(0,10)}.txt`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(r)};return e.jsxs(u,{children:[e.jsxs(z,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:`1px solid ${n.palette.divider}`},children:[e.jsxs(m,{direction:"row",alignItems:"center",spacing:2,mb:3,children:[e.jsx(nt,{sx:{color:n.palette.primary.main,fontSize:28}}),e.jsx(l,{variant:"h6",fontWeight:"bold",children:"Two-Way ANOVA Configuration"})]}),e.jsx(N,{severity:"info",icon:e.jsx(xe,{}),sx:{mb:3},children:e.jsxs(l,{variant:"body2",children:[e.jsx("strong",{children:"Two-Way ANOVA:"})," This component performs a complete two-way analysis of variance on your data, calculating descriptive statistics, F-statistics, p-values, and effect sizes."]})}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,md:4,children:e.jsxs(X,{fullWidth:!0,children:[e.jsx(H,{id:"dataset-select-label-twoway",children:"Dataset"}),e.jsx(Y,{labelId:"dataset-select-label-twoway",value:ne,label:"Dataset",onChange:Ke,children:U.map(t=>e.jsx(Q,{value:t.id,children:t.name},t.id))})]})}),e.jsx(c,{item:!0,xs:12,md:8,children:p&&e.jsxs(N,{severity:"info",sx:{height:"100%",display:"flex",alignItems:"center"},children:["Dataset contains ",p.data.length," observations"]})}),e.jsx(c,{item:!0,xs:12,children:e.jsx(De,{sx:{my:1}})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsxs(X,{fullWidth:!0,children:[e.jsx(H,{id:"dependent-variable-label",children:"Dependent Variable"}),e.jsx(Y,{labelId:"dependent-variable-label",value:se,label:"Dependent Variable",onChange:Ge,disabled:!p,children:Ue.map(t=>e.jsx(Q,{value:t.id,children:e.jsxs(m,{direction:"row",alignItems:"center",spacing:1,children:[e.jsx(Ie,{sx:{fontSize:18,color:n.palette.text.secondary}}),e.jsx("span",{children:t.name})]})},t.id))})]})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsxs(X,{fullWidth:!0,children:[e.jsx(H,{id:"factor-1-label",children:"Factor 1 (Between-Subjects)"}),e.jsx(Y,{labelId:"factor-1-label",value:M,label:"Factor 1 (Between-Subjects)",onChange:Xe,disabled:!p,children:ye.filter(t=>t.id!==L).map(t=>e.jsx(Q,{value:t.id,children:t.name},t.id))})]})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsxs(X,{fullWidth:!0,children:[e.jsx(H,{id:"factor-2-label",children:"Factor 2 (Between-Subjects)"}),e.jsx(Y,{labelId:"factor-2-label",value:L,label:"Factor 2 (Between-Subjects)",onChange:He,disabled:!p,children:ye.filter(t=>t.id!==M).map(t=>e.jsx(Q,{value:t.id,children:t.name},t.id))})]})})]}),e.jsxs(u,{sx:{mt:3,display:"flex",justifyContent:"flex-end",gap:2},children:[e.jsx(Ve,{variant:"outlined",onClick:()=>{ae(""),re(""),le(""),A(null),k(null)},disabled:B,children:"Clear"}),e.jsx(Ve,{variant:"contained",onClick:Ze,disabled:!ve()||B,startIcon:B?e.jsx(st,{size:20}):e.jsx(Me,{}),children:B?"Running Analysis...":"Run Two-Way ANOVA"})]})]}),je&&e.jsx(N,{severity:"error",sx:{mb:3},icon:e.jsx(he,{}),children:je}),B&&e.jsx(u,{sx:{mb:3},children:e.jsx(at,{})}),i&&e.jsxs(z,{elevation:0,sx:{p:3,borderRadius:2,border:`1px solid ${n.palette.divider}`},children:[e.jsxs(m,{direction:"row",alignItems:"center",justifyContent:"space-between",mb:3,children:[e.jsx(l,{variant:"h6",fontWeight:"bold",children:"Analysis Results"}),e.jsxs(m,{direction:"row",spacing:1,children:[e.jsx(T,{label:"Statistical analysis results",size:"small",color:"success",variant:"outlined"}),e.jsx(Le,{onClick:_e,color:"primary",title:"Download Results",children:e.jsx(rt,{})})]})]}),e.jsx(u,{sx:{borderBottom:1,borderColor:"divider"},children:e.jsxs(lt,{value:O,onChange:Qe,"aria-label":"results tabs",children:[e.jsx(Z,{label:"Summary",icon:e.jsx(ot,{}),iconPosition:"start"}),e.jsx(Z,{label:"ANOVA Table",icon:e.jsx(Ie,{}),iconPosition:"start"}),e.jsx(Z,{label:"Descriptive Stats",icon:e.jsx(Me,{}),iconPosition:"start"}),e.jsx(Z,{label:"Visualizations",icon:e.jsx(ct,{}),iconPosition:"start"})]})}),e.jsx(ie,{value:O,index:0,children:e.jsxs(c,{container:!0,spacing:3,children:[e.jsxs(c,{item:!0,xs:12,children:[e.jsxs(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(xe,{fontSize:"small"}),"Key Findings"]}),e.jsxs(c,{container:!0,spacing:2,children:[e.jsx(c,{item:!0,xs:12,md:4,children:e.jsx(D,{variant:"outlined",sx:{height:"100%"},children:e.jsxs(I,{children:[e.jsxs(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:["Main Effect: ",i.factor1]}),e.jsxs(m,{direction:"row",alignItems:"center",spacing:1,children:[i.interpretation.mainEffects.factor1.significant?e.jsx(V,{sx:{color:n.palette.success.main}}):e.jsx(fe,{sx:{color:n.palette.grey[400]}}),e.jsx(l,{variant:"h6",fontWeight:"bold",children:i.interpretation.mainEffects.factor1.significant?"Significant":"Not Significant"})]}),e.jsxs(l,{variant:"body2",sx:{mt:1},children:["F(",i.anovaTable[0].df,", ",i.anovaTable[3].df,") = ",(Se=i.anovaTable[0].F)==null?void 0:Se.toFixed(2)]}),e.jsxs(l,{variant:"body2",children:["p = ",($e=i.anovaTable[0].p)==null?void 0:$e.toFixed(3)]}),e.jsx(T,{label:`Effect Size: ${i.interpretation.mainEffects.factor1.effectSize.label}`,size:"small",sx:{mt:1,bgcolor:y(i.interpretation.mainEffects.factor1.effectSize.color,.1),color:i.interpretation.mainEffects.factor1.effectSize.color}})]})})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsx(D,{variant:"outlined",sx:{height:"100%"},children:e.jsxs(I,{children:[e.jsxs(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:["Main Effect: ",i.factor2]}),e.jsxs(m,{direction:"row",alignItems:"center",spacing:1,children:[i.interpretation.mainEffects.factor2.significant?e.jsx(V,{sx:{color:n.palette.success.main}}):e.jsx(fe,{sx:{color:n.palette.grey[400]}}),e.jsx(l,{variant:"h6",fontWeight:"bold",children:i.interpretation.mainEffects.factor2.significant?"Significant":"Not Significant"})]}),e.jsxs(l,{variant:"body2",sx:{mt:1},children:["F(",i.anovaTable[1].df,", ",i.anovaTable[3].df,") = ",(Te=i.anovaTable[1].F)==null?void 0:Te.toFixed(2)]}),e.jsxs(l,{variant:"body2",children:["p = ",(We=i.anovaTable[1].p)==null?void 0:We.toFixed(3)]}),e.jsx(T,{label:`Effect Size: ${i.interpretation.mainEffects.factor2.effectSize.label}`,size:"small",sx:{mt:1,bgcolor:y(i.interpretation.mainEffects.factor2.effectSize.color,.1),color:i.interpretation.mainEffects.factor2.effectSize.color}})]})})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsx(D,{variant:"outlined",sx:{height:"100%"},children:e.jsxs(I,{children:[e.jsx(l,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Interaction Effect"}),e.jsxs(m,{direction:"row",alignItems:"center",spacing:1,children:[i.interpretation.interaction.significant?e.jsx(V,{sx:{color:n.palette.success.main}}):e.jsx(fe,{sx:{color:n.palette.grey[400]}}),e.jsx(l,{variant:"h6",fontWeight:"bold",children:i.interpretation.interaction.significant?"Significant":"Not Significant"})]}),e.jsxs(l,{variant:"body2",sx:{mt:1},children:["F(",i.anovaTable[2].df,", ",i.anovaTable[3].df,") = ",(Ce=i.anovaTable[2].F)==null?void 0:Ce.toFixed(2)]}),e.jsxs(l,{variant:"body2",children:["p = ",(Fe=i.anovaTable[2].p)==null?void 0:Fe.toFixed(3)]}),e.jsx(T,{label:`Effect Size: ${i.interpretation.interaction.effectSize.label}`,size:"small",sx:{mt:1,bgcolor:y(i.interpretation.interaction.effectSize.color,.1),color:i.interpretation.interaction.effectSize.color}})]})})})]})]}),e.jsxs(c,{item:!0,xs:12,children:[e.jsxs(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(xe,{fontSize:"small"}),"Statistical Interpretation"]}),e.jsx(z,{variant:"outlined",sx:{p:3,bgcolor:y(n.palette.primary.main,.02),borderColor:n.palette.primary.main,borderStyle:"dashed"},children:e.jsxs(m,{spacing:2,children:[e.jsx(l,{variant:"body1",children:i.interpretation.summary}),e.jsx(De,{}),e.jsx(l,{variant:"body2",children:i.interpretation.mainEffects.factor1.text}),e.jsx(l,{variant:"body2",children:i.interpretation.mainEffects.factor2.text}),e.jsx(l,{variant:"body2",children:i.interpretation.interaction.text}),e.jsx(N,{severity:i.interpretation.interaction.significant?"warning":"info",sx:{mt:2},children:i.interpretation.interaction.interpretation})]})})]}),e.jsx(c,{item:!0,xs:12,children:e.jsx(D,{variant:"outlined",children:e.jsxs(I,{children:[e.jsxs(m,{direction:"row",alignItems:"center",justifyContent:"space-between",children:[e.jsx(l,{variant:"subtitle1",fontWeight:"bold",children:"Assumptions Check"}),e.jsx(Le,{onClick:()=>Ye("assumptions"),size:"small",children:ue.assumptions?e.jsx(dt,{}):e.jsx(xt,{})})]}),e.jsx(ht,{in:ue.assumptions,children:e.jsxs(m,{spacing:2,sx:{mt:2},children:[e.jsxs(m,{direction:"row",alignItems:"center",spacing:2,children:[i.assumptions.normality.passed?e.jsx(V,{color:"success"}):e.jsx(he,{color:"error"}),e.jsxs(u,{children:[e.jsxs(l,{variant:"body2",fontWeight:"bold",children:["Normality (",i.assumptions.normality.test,")"]}),e.jsxs(l,{variant:"body2",color:"text.secondary",children:["W = ",i.assumptions.normality.statistic.toFixed(3),", p = ",i.assumptions.normality.p.toFixed(3)]})]})]}),e.jsxs(m,{direction:"row",alignItems:"center",spacing:2,children:[i.assumptions.homogeneity.passed?e.jsx(V,{color:"success"}):e.jsx(he,{color:"error"}),e.jsxs(u,{children:[e.jsxs(l,{variant:"body2",fontWeight:"bold",children:["Homogeneity of Variance (",i.assumptions.homogeneity.test,")"]}),e.jsxs(l,{variant:"body2",color:"text.secondary",children:["F = ",i.assumptions.homogeneity.statistic.toFixed(3),", p = ",i.assumptions.homogeneity.p.toFixed(3)]})]})]}),e.jsxs(m,{direction:"row",alignItems:"center",spacing:2,children:[e.jsx(V,{color:"success"}),e.jsxs(u,{children:[e.jsx(l,{variant:"body2",fontWeight:"bold",children:"Independence of Observations"}),e.jsx(l,{variant:"body2",color:"text.secondary",children:i.assumptions.independence.note})]})]})]})})]})})})]})}),e.jsxs(ie,{value:O,index:1,children:[e.jsx(N,{severity:"info",sx:{mb:2},children:"The ANOVA table shows the complete analysis results including sum of squares (SS), degrees of freedom (df), mean squares (MS), F-statistics, p-values, and effect sizes (η²)."}),e.jsx(_,{component:z,variant:"outlined",children:e.jsxs(J,{children:[e.jsx(R,{children:e.jsxs(E,{sx:{bgcolor:y(n.palette.primary.main,.08)},children:[e.jsx(s,{sx:{fontWeight:"bold"},children:"Source of Variation"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SS"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"df"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"MS"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"F"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"p-value"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"η²"})]})}),e.jsx(ee,{children:i.anovaTable.map(t=>{var a,r,d;return e.jsxs(E,{sx:{"&:hover":{bgcolor:y(n.palette.primary.main,.02)},bgcolor:t.p!==null&&t.p<.05?y(n.palette.success.main,.05):"inherit"},children:[e.jsx(s,{component:"th",scope:"row",sx:{fontWeight:t.source==="Total"?"bold":"normal"},children:t.source}),e.jsx(s,{align:"right",children:(a=t.SS)==null?void 0:a.toFixed(2)}),e.jsx(s,{align:"right",children:t.df}),e.jsx(s,{align:"right",children:((r=t.MS)==null?void 0:r.toFixed(2))??"-"}),e.jsx(s,{align:"right",children:((d=t.F)==null?void 0:d.toFixed(2))??"-"}),e.jsx(s,{align:"right",sx:{fontWeight:t.p!==null&&t.p<.05?"bold":"normal",color:t.p!==null&&t.p<.05?n.palette.success.main:"inherit"},children:t.p===null?"-":t.p<.001?"< 0.001":t.p.toFixed(3)}),e.jsx(s,{align:"right",children:t.etaSquared?e.jsx(ft,{title:`Effect size: ${q(t.etaSquared).label}`,children:e.jsx("span",{children:t.etaSquared.toFixed(3)})}):"-"})]},t.source)})})]})}),e.jsx(N,{severity:"info",sx:{mt:3},children:e.jsxs(l,{variant:"body2",children:[e.jsx("strong",{children:"Note:"})," η² (eta squared) represents the proportion of variance explained by each factor. Effect sizes: Small (0.01), Medium (0.06), Large (0.14)."]})})]}),e.jsxs(ie,{value:O,index:2,children:[e.jsx(N,{severity:"info",sx:{mb:2},children:"This section shows descriptive statistics for all factor level combinations and marginal means."}),e.jsxs(c,{container:!0,spacing:3,children:[e.jsxs(c,{item:!0,xs:12,children:[e.jsx(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:"Cell Means and Standard Deviations"}),e.jsx(_,{component:z,variant:"outlined",children:e.jsxs(J,{size:"small",children:[e.jsx(R,{children:e.jsxs(E,{sx:{bgcolor:y(n.palette.primary.main,.08)},children:[e.jsx(s,{sx:{fontWeight:"bold"},children:i.factor1}),e.jsx(s,{sx:{fontWeight:"bold"},children:i.factor2}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"N"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"Mean"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SD"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SE"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"95% CI"})]})}),e.jsx(ee,{children:i.descriptiveStats.map((t,a)=>e.jsxs(E,{sx:{"&:hover":{bgcolor:y(n.palette.primary.main,.02)}},children:[e.jsx(s,{children:t.factor1Level}),e.jsx(s,{children:t.factor2Level}),e.jsx(s,{align:"right",children:t.N}),e.jsx(s,{align:"right",children:isNaN(t.mean)?"-":t.mean.toFixed(2)}),e.jsx(s,{align:"right",children:isNaN(t.sd)?"-":t.sd.toFixed(2)}),e.jsx(s,{align:"right",children:isNaN(t.se)?"-":t.se.toFixed(2)}),e.jsx(s,{align:"right",children:isNaN(t.ci95Lower)?"-":`[${t.ci95Lower.toFixed(2)}, ${t.ci95Upper.toFixed(2)}]`})]},a))})]})})]}),e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsxs(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:["Marginal Means - ",i.factor1]}),e.jsx(_,{component:z,variant:"outlined",children:e.jsxs(J,{size:"small",children:[e.jsx(R,{children:e.jsxs(E,{sx:{bgcolor:y(n.palette.primary.main,.08)},children:[e.jsx(s,{sx:{fontWeight:"bold"},children:i.factor1}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"N"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"Mean"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SD"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SE"})]})}),e.jsx(ee,{children:Object.entries(i.marginalMeansFactor1).map(([t,a])=>e.jsxs(E,{children:[e.jsx(s,{children:t}),e.jsx(s,{align:"right",children:a.n}),e.jsx(s,{align:"right",children:a.mean.toFixed(2)}),e.jsx(s,{align:"right",children:a.sd.toFixed(2)}),e.jsx(s,{align:"right",children:a.se.toFixed(2)})]},t))})]})})]}),e.jsxs(c,{item:!0,xs:12,md:6,children:[e.jsxs(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:["Marginal Means - ",i.factor2]}),e.jsx(_,{component:z,variant:"outlined",children:e.jsxs(J,{size:"small",children:[e.jsx(R,{children:e.jsxs(E,{sx:{bgcolor:y(n.palette.primary.main,.08)},children:[e.jsx(s,{sx:{fontWeight:"bold"},children:i.factor2}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"N"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"Mean"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SD"}),e.jsx(s,{align:"right",sx:{fontWeight:"bold"},children:"SE"})]})}),e.jsx(ee,{children:Object.entries(i.marginalMeansFactor2).map(([t,a])=>e.jsxs(E,{children:[e.jsx(s,{children:t}),e.jsx(s,{align:"right",children:a.n}),e.jsx(s,{align:"right",children:a.mean.toFixed(2)}),e.jsx(s,{align:"right",children:a.sd.toFixed(2)}),e.jsx(s,{align:"right",children:a.se.toFixed(2)})]},t))})]})})]})]})]}),e.jsx(ie,{value:O,index:3,children:e.jsxs(c,{container:!0,spacing:3,children:[e.jsx(c,{item:!0,xs:12,lg:6,children:e.jsx(D,{variant:"outlined",children:e.jsxs(I,{children:[e.jsx(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:"Interaction Plot"}),e.jsx(u,{sx:{height:400,mt:2},children:(()=>{const t=[...new Set(i.descriptiveStats.map(o=>String(o.factor1Level)))],a=[...new Set(i.descriptiveStats.map(o=>String(o.factor2Level)))],r=t.map(o=>{const b={name:o};return a.forEach(W=>{const j=i.descriptiveStats.find(P=>String(P.factor1Level)===o&&String(P.factor2Level)===W);b[W]=j&&!isNaN(j.mean)?j.mean:void 0,b[`${W}_error`]=j&&!isNaN(j.se)?j.se*1.96:void 0}),b}),d=[n.palette.primary.main,n.palette.secondary.main,n.palette.error.main,n.palette.warning.main];return e.jsx(pe,{width:"100%",height:"100%",children:e.jsxs(jt,{data:r,margin:{top:5,right:30,left:20,bottom:40},children:[e.jsx(Be,{strokeDasharray:"3 3",stroke:n.palette.divider}),e.jsx(ke,{dataKey:"name",label:{value:i.factor1,position:"insideBottom",offset:-10,style:{fontWeight:"bold"}},padding:{left:20,right:20}}),e.jsx(Oe,{label:{value:`Mean ${i.dependentVariable}`,angle:-90,position:"insideLeft",style:{fontWeight:"bold"}}}),e.jsx(ge,{contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:4}}),e.jsx(ut,{wrapperStyle:{paddingTop:"20px"},iconType:"line"}),a.map((o,b)=>e.jsx(bt,{type:"monotone",dataKey:o,stroke:d[b%d.length],strokeWidth:2,dot:{r:4},activeDot:{r:6},name:`${i.factor2}: ${o}`},o))]})})})()}),i.interpretation.interaction.significant&&e.jsx(N,{severity:"warning",sx:{mt:2},children:"The lines are not parallel, indicating a significant interaction effect."})]})})}),e.jsx(c,{item:!0,xs:12,lg:6,children:e.jsx(D,{variant:"outlined",children:e.jsxs(I,{children:[e.jsx(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:"Marginal Means Comparison"}),e.jsx(u,{sx:{height:400,mt:2},children:(()=>{const t=[...Object.entries(i.marginalMeansFactor1).map(([a,r])=>({factor:i.factor1,level:a,mean:r.mean,se:r.se})),...Object.entries(i.marginalMeansFactor2).map(([a,r])=>({factor:i.factor2,level:a,mean:r.mean,se:r.se}))];return e.jsx(pe,{width:"100%",height:"100%",children:e.jsxs(yt,{data:t,margin:{top:20,right:30,left:20,bottom:40},children:[e.jsx(Be,{strokeDasharray:"3 3",stroke:n.palette.divider}),e.jsx(ke,{dataKey:"level",label:{value:"Factor Levels",position:"insideBottom",offset:-10,style:{fontWeight:"bold"}}}),e.jsx(Oe,{label:{value:`Mean ${i.dependentVariable}`,angle:-90,position:"insideLeft",style:{fontWeight:"bold"}}}),e.jsx(ge,{contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:4}}),e.jsxs(vt,{dataKey:"mean",fill:n.palette.primary.main,children:[t.map((a,r)=>e.jsx(qe,{fill:a.factor===i.factor1?n.palette.primary.main:n.palette.secondary.main},`cell-${r}`)),e.jsx(St,{dataKey:"se",width:4,stroke:n.palette.grey[700]})]})]})})})()}),e.jsxs(m,{direction:"row",spacing:2,sx:{mt:2,justifyContent:"center"},children:[e.jsx(T,{label:i.factor1,sx:{bgcolor:n.palette.primary.main,color:"white"}}),e.jsx(T,{label:i.factor2,sx:{bgcolor:n.palette.secondary.main,color:"white"}})]})]})})}),e.jsx(c,{item:!0,xs:12,children:e.jsx(D,{variant:"outlined",children:e.jsxs(I,{children:[e.jsx(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:"Effect Size Visualization (Variance Explained)"}),e.jsx(u,{sx:{height:300,mt:2},children:(()=>{const t=i.anovaTable.filter(r=>r.etaSquared!==null&&r.etaSquared!==void 0).map(r=>({name:r.source,value:r.etaSquared*100,fill:q(r.etaSquared).color})),a=t.reduce((r,d)=>r+d.value,0);return t.push({name:"Unexplained",value:100-a,fill:n.palette.grey[300]}),e.jsx(pe,{width:"100%",height:"100%",children:e.jsxs($t,{children:[e.jsx(Tt,{data:t,cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:({name:r,value:d})=>`${r}: ${d.toFixed(1)}%`,children:t.map((r,d)=>e.jsx(qe,{fill:r.fill},`cell-${d}`))}),e.jsx(ge,{formatter:r=>`${r.toFixed(1)}%`,contentStyle:{backgroundColor:n.palette.background.paper,border:`1px solid ${n.palette.divider}`,borderRadius:4}})]})})})()}),e.jsxs(m,{direction:"row",spacing:2,sx:{mt:2,justifyContent:"center"},children:[e.jsx(T,{label:"Negligible: < 1%",size:"small",sx:{bgcolor:n.palette.grey[500],color:"white"}}),e.jsx(T,{label:"Small: 1-6%",size:"small",sx:{bgcolor:n.palette.info.main,color:"white"}}),e.jsx(T,{label:"Medium: 6-14%",size:"small",sx:{bgcolor:n.palette.warning.main,color:"white"}}),e.jsx(T,{label:"Large: > 14%",size:"small",sx:{bgcolor:n.palette.success.main,color:"white"}})]})]})})})]})}),e.jsx(u,{sx:{mt:3,textAlign:"center"},children:e.jsxs(l,{variant:"caption",color:"text.secondary",children:["Analysis performed on: ",i.timestamp.toLocaleString()]})})]})]})};export{Vt as default};
