import{u as je,a as ae,j as e,B as o,e as n,k as _,bz as ge,N as se,l as H,bL as fe,bM as q,V as ve,bn as ye,bo as Te,bW as Se,bX as Ce,aD as we,bY as Ae,a6 as Ie,a7 as re,aW as Ne,bZ as Pe,G as i,br as R,D as ke,ae as ze,g as Fe,f as y,J as ne,bR as We,ai as Re,b9 as Be,ba as Me,bb as B,b_ as De,ah as N,aK as Ee,aj as M,bc as D,bO as Ve,bw as Le,R as ie,ao as Oe,ap as $e,aq as Ge,ar as le,as as oe,at as Ue}from"./mui-libs-CfwFIaTD.js";import{r as h}from"./react-libs-Cr2nE3UY.js";import{P as _e}from"./PublicationReadyGate-BGFbKbJc.js";import"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";function ce(v){const{children:j,value:C,index:g,...w}=v;return e.jsx("div",{role:"tabpanel",hidden:C!==g,id:`apa-tabpanel-${g}`,"aria-labelledby":`apa-tab-${g}`,...w,children:C===g&&e.jsx(o,{sx:{py:3},children:j})})}const tt=()=>{const v=je(),j=ae(v.breakpoints.down("md")),C=ae(v.breakpoints.down("lg")),[g,w]=h.useState(""),[P,de]=h.useState("auto"),[m,J]=h.useState(null),[s,f]=h.useState({tableNumber:"1",tableTitle:"Results of Statistical Analysis",tableNote:"Note. *p < .05. **p < .01. ***p < .001.",includeStatistics:!0,italicizeStatistics:!0,rightAlignNumbers:!0,includeSignificance:!0,significanceThreshold:.05,decimalPlaces:3,includeConfidenceIntervals:!1}),[E,V]=h.useState("tabs"),[L,K]=h.useState(0),[k,O]=h.useState(!1),[Q,A]=h.useState(null),[xe,I]=h.useState(!1),[ue,z]=h.useState("");h.useEffect(()=>{j?V("tabs"):C&&V("stacked")},[j,C]);const X=`Variable,Mean,SD,t,df,p,95% CI
Condition A,12.45,2.34,3.42,28,.002,"[8.21, 16.69]"
Condition B,8.76,1.98,-2.11,28,.045,"[4.52, 13.00]"
Condition C,15.23,3.12,4.67,28,.001,"[11.99, 18.47]"`,he=(t,a)=>{a!==null&&(V(a),K(0))},me=()=>{w(""),J(null),A(null)},Y=()=>{w(X),Z(X)},Z=(t=g)=>{if(!t.trim()){A("Please enter some table data to parse.");return}O(!0),A(null);try{let a=",";if(P==="auto"){const c=(t.match(/,/g)||[]).length,b=(t.match(/\t/g)||[]).length,u=(t.match(/\s+/g)||[]).length;b>c&&b>u?a="	":u>c&&u>b?a=/\s+/:a=","}else P==="tsv"?a="	":P==="spaces"&&(a=/\s+/);const r=t.trim().split(`
`);if(r.length<2)throw new Error("Table must have at least a header row and one data row.");const l=(c,b)=>{if(typeof b=="string"&&b===","){const u=/,(?=(?:[^"]*"[^"]*")*[^"]*$)/;return c.split(u).map(p=>(p=p.trim(),p.startsWith('"')&&p.endsWith('"')&&(p=p.substring(1,p.length-1).replace(/""/g,'"')),p)).filter(p=>p!=="")}else{const u=c.split(b).map(S=>S.trim().replace(/['"]/g,""));return u.length>0&&u[0]===""?[u[0],...u.slice(1).filter(S=>S!=="")]:u.filter(S=>S!=="")}},d=l(r[0],a),x=[];for(let c=1;c<r.length;c++){const b=l(r[c],a);b.length===d.length&&x.push(b)}if(x.length===0)throw new Error("No valid data rows found.");const T={headers:d,rows:x,tableNumber:s.tableNumber,tableTitle:s.tableTitle,tableNote:s.tableNote};J(T),O(!1),z("Table parsed successfully!"),I(!0)}catch(a){A(`Error parsing table: ${a instanceof Error?a.message:String(a)}`),O(!1)}},F=t=>{const a=t.replace(/[,\[\]()]/g,"");return!isNaN(parseFloat(a))&&isFinite(parseFloat(a))},ee=t=>["M","SD","t","F","r","p","df","CI","β","χ²","d","η²"].some(r=>t.toLowerCase().includes(r.toLowerCase())),te=t=>{if(t.match(/^\[\s*[-+]?\d*\.?\d+(?:e[-+]?\d+)?\s*,\s*[-+]?\d*\.?\d+(?:e[-+]?\d+)?\s*\]$/))return`[${t.substring(1,t.length-1).split(",").map(d=>{const x=parseFloat(d.trim());return isNaN(x)?d.trim():x.toFixed(s.decimalPlaces)}).join(", ")}]`;if(!F(t))return t;const a=t.replace(/[,\[\]()]/g,""),r=parseFloat(a);return t.toLowerCase().includes("p")?r<.001?"< .001":r<1?r.toFixed(3).replace(/^0/,""):r.toFixed(s.decimalPlaces):r.toFixed(s.decimalPlaces)},be=async t=>{try{await navigator.clipboard.writeText(t),z("Table copied to clipboard!"),I(!0)}catch{z("Failed to copy to clipboard"),I(!0)}},$=()=>{if(!m)return"";let t=`Table ${s.tableNumber}

`;t+=`${s.tableTitle}

`;const a=m.headers,r=a.map((l,d)=>{const x=l.length,T=m.rows.map(c=>(c[d]||"").length);return Math.max(x,...T)});return t+=a.map((l,d)=>l.padEnd(r[d])).join("  ")+`
`,t+=a.map((l,d)=>"-".repeat(r[d])).join("  ")+`
`,m.rows.forEach(l=>{const d=l.map((x,T)=>{let c=x;return s.rightAlignNumbers&&F(x)?(c=te(x),c.padStart(r[T])):c.padEnd(r[T])});t+=d.join("  ")+`
`}),s.tableNote&&(t+=`
${s.tableNote}`),t},pe=()=>{const t=$(),a=new Blob([t],{type:"text/plain"}),r=URL.createObjectURL(a),l=document.createElement("a");l.href=r,l.download=`apa_table_${s.tableNumber}.txt`,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(r),z("Table downloaded successfully!"),I(!0)},W=()=>e.jsxs(o,{children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Input Table Data"}),e.jsxs(i,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(i,{item:!0,xs:12,sm:6,children:e.jsxs(Re,{fullWidth:!0,size:"small",children:[e.jsx(Be,{children:"Input Format"}),e.jsxs(Me,{value:P,label:"Input Format",onChange:t=>de(t.target.value),children:[e.jsx(B,{value:"auto",children:"Auto-detect"}),e.jsx(B,{value:"csv",children:"Comma-separated (CSV)"}),e.jsx(B,{value:"tsv",children:"Tab-separated (TSV)"}),e.jsx(B,{value:"spaces",children:"Space-separated"})]})]})}),e.jsx(i,{item:!0,xs:12,sm:6,children:e.jsxs(R,{direction:"row",spacing:1,children:[e.jsx(y,{variant:"outlined",size:"small",onClick:Y,startIcon:e.jsx(ne,{}),sx:{textTransform:"none"},children:"Load Sample"}),e.jsx(y,{variant:"outlined",size:"small",onClick:me,startIcon:e.jsx(De,{}),sx:{textTransform:"none"},children:"Clear All"})]})})]}),e.jsx(N,{fullWidth:!0,multiline:!0,rows:8,placeholder:`Paste your table data here...\r
\r
Example formats:\r
• CSV: Variable,Mean,SD,p\r
• TSV: Variable	Mean	SD	p\r
• Spaces: Variable    Mean    SD    p`,value:g,onChange:t=>w(t.target.value),variant:"outlined",sx:{mb:2,"& .MuiInputBase-root":{fontFamily:"monospace",fontSize:"0.875rem"}}}),e.jsx(y,{variant:"contained",startIcon:e.jsx(Ee,{}),onClick:()=>Z(),disabled:!g.trim()||k,sx:{borderRadius:2,textTransform:"none",fontWeight:600},children:k?"Parsing...":"Parse Table"})]}),G=()=>e.jsxs(o,{children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"APA Formatting Options"}),e.jsxs(i,{container:!0,spacing:3,children:[e.jsx(i,{item:!0,xs:12,md:6,children:e.jsxs(R,{spacing:2,children:[e.jsx(N,{fullWidth:!0,label:"Table Number",value:s.tableNumber,onChange:t=>f(a=>({...a,tableNumber:t.target.value})),size:"small"}),e.jsx(N,{fullWidth:!0,label:"Table Title",value:s.tableTitle,onChange:t=>f(a=>({...a,tableTitle:t.target.value})),size:"small"}),e.jsx(N,{fullWidth:!0,multiline:!0,rows:2,label:"Table Note",value:s.tableNote,onChange:t=>f(a=>({...a,tableNote:t.target.value})),size:"small",placeholder:"Note. *p < .05. **p < .01. ***p < .001."})]})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsxs(i,{container:!0,spacing:1,children:[" ",e.jsx(i,{item:!0,xs:12,children:e.jsx(M,{control:e.jsx(D,{checked:s.includeStatistics,onChange:t=>f(a=>({...a,includeStatistics:t.target.checked}))}),label:"Include Statistical Formatting"})}),e.jsx(i,{item:!0,xs:12,children:e.jsx(M,{control:e.jsx(D,{checked:s.italicizeStatistics,onChange:t=>f(a=>({...a,italicizeStatistics:t.target.checked}))}),label:"Italicize Statistical Terms"})}),e.jsx(i,{item:!0,xs:12,children:e.jsx(M,{control:e.jsx(D,{checked:s.rightAlignNumbers,onChange:t=>f(a=>({...a,rightAlignNumbers:t.target.checked}))}),label:"Right-align Numbers"})}),e.jsx(i,{item:!0,xs:12,children:e.jsx(M,{control:e.jsx(D,{checked:s.includeSignificance,onChange:t=>f(a=>({...a,includeSignificance:t.target.checked}))}),label:"Add Significance Indicators"})}),e.jsx(i,{item:!0,xs:12,children:e.jsx(N,{fullWidth:!0,type:"number",label:"Decimal Places",value:s.decimalPlaces,onChange:t=>f(a=>({...a,decimalPlaces:parseInt(t.target.value)||3})),size:"small",inputProps:{min:0,max:6}})})]})})]})]}),U=()=>m?e.jsxs(o,{children:[e.jsxs(o,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(n,{variant:"subtitle1",sx:{fontWeight:500},children:"APA Formatted Table Preview"}),e.jsxs(R,{direction:"row",spacing:1,children:[e.jsx(y,{variant:"outlined",size:"small",startIcon:e.jsx(Ve,{}),onClick:()=>be($()),sx:{textTransform:"none"},children:"Copy Table"}),e.jsx(y,{variant:"outlined",size:"small",startIcon:e.jsx(Le,{}),onClick:pe,sx:{textTransform:"none"},children:"Download"})]})]}),e.jsxs(ie,{variant:"outlined",sx:{p:3,borderRadius:2},children:[e.jsxs(n,{variant:"h6",sx:{fontWeight:600,mb:1},children:["Table ",s.tableNumber]}),e.jsx(n,{variant:"subtitle1",sx:{fontStyle:"italic",mb:3},children:s.tableTitle}),e.jsx(Oe,{children:e.jsxs($e,{size:"small",sx:{"& .MuiTableCell-root":{border:"none",borderBottom:"1px solid #ddd"}},children:[e.jsx(Ge,{children:e.jsx(le,{sx:{borderTop:"2px solid #000",borderBottom:"1px solid #000"},children:m.headers.map((t,a)=>e.jsx(oe,{sx:{fontWeight:600,fontStyle:s.italicizeStatistics&&ee(t)?"italic":"normal",textAlign:a===0?"left":s.rightAlignNumbers?"right":"left"},children:t},a))})}),e.jsx(Ue,{children:m.rows.map((t,a)=>e.jsx(le,{children:t.map((r,l)=>e.jsx(oe,{sx:{textAlign:l===0?"left":s.rightAlignNumbers&&F(r)?"right":"left",fontStyle:s.italicizeStatistics&&ee(r)?"italic":"normal"},children:s.rightAlignNumbers&&F(r)?te(r):r},l))},a))})]})}),s.tableNote&&e.jsx(n,{variant:"body2",sx:{mt:2,fontStyle:"italic"},children:s.tableNote})]}),e.jsxs(o,{sx:{mt:3},children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Text Format Preview"}),e.jsx(ie,{variant:"outlined",sx:{p:2,backgroundColor:v.palette.grey[50],borderRadius:2},children:e.jsx(n,{component:"pre",variant:"body2",sx:{fontFamily:"monospace",whiteSpace:"pre-wrap",fontSize:"0.875rem"},children:$()})})]})]}):null;return e.jsx(_e,{children:e.jsxs(o,{sx:{p:{xs:2,md:3},maxWidth:1400,mx:"auto"},children:[e.jsxs(o,{sx:{mb:4},children:[e.jsx(n,{variant:"h4",gutterBottom:!0,sx:{fontWeight:600,color:v.palette.primary.main},children:"Convert to APA Table Format"}),e.jsx(n,{variant:"body1",color:"text.secondary",children:"Transform your raw data tables into properly formatted APA-style tables for academic publications"})]}),e.jsxs(_,{elevation:2,sx:{mb:3},children:[e.jsx(ge,{title:"Table Converter",avatar:e.jsx(se,{color:"primary"}),sx:{pb:1}}),e.jsxs(H,{children:[m&&e.jsxs(o,{sx:{mb:3},children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Display Mode"}),e.jsxs(fe,{value:E,exclusive:!0,onChange:he,size:"small",sx:{mb:2},children:[e.jsxs(q,{value:"tabs","aria-label":"tabs view",children:[e.jsx(ve,{sx:{mr:1}}),!j&&"Tabs"]}),e.jsxs(q,{value:"stacked","aria-label":"stacked view",children:[e.jsx(ye,{sx:{mr:1}}),!j&&"Stacked"]}),!j&&e.jsxs(q,{value:"side-by-side","aria-label":"side by side view",children:[e.jsx(Te,{sx:{mr:1}}),"Side by Side"]})]})]}),e.jsxs(Se,{defaultExpanded:!1,children:[e.jsx(Ce,{expandIcon:e.jsx(we,{}),children:e.jsx(n,{variant:"subtitle1",sx:{fontWeight:500},children:"APA Table Guidelines"})}),e.jsxs(Ae,{children:[e.jsx(n,{variant:"body2",color:"text.secondary",paragraph:!0,children:"This tool helps format tables according to APA 7th edition guidelines:"}),e.jsxs(o,{component:"ul",sx:{pl:2},children:[e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Table number and title:"})," Above the table, flush left"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Column headers:"})," Brief, descriptive labels"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Number alignment:"})," Right-aligned for consistency"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Statistical symbols:"})," Italicized (M, SD, p, etc.)"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"P-values:"})," ","Report as p = .xxx or p < .001"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Table notes:"})," Below table for significance levels"]})]})]})]}),m?e.jsx(o,{sx:{mt:3},children:E==="tabs"?e.jsxs(o,{children:[e.jsxs(Ie,{value:L,onChange:(t,a)=>K(a),sx:{borderBottom:1,borderColor:"divider",mb:2},children:[e.jsx(re,{label:e.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ne,{fontSize:"small"}),!j&&"Input & Options"]})}),e.jsx(re,{label:e.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Pe,{fontSize:"small"}),!j&&"Preview"]})})]}),e.jsx(ce,{value:L,index:0,children:e.jsxs(i,{container:!0,spacing:4,children:[e.jsx(i,{item:!0,xs:12,lg:6,children:W()}),e.jsx(i,{item:!0,xs:12,lg:6,children:G()})]})}),e.jsx(ce,{value:L,index:1,children:U()})]}):E==="stacked"?e.jsxs(R,{spacing:4,children:[e.jsxs(i,{container:!0,spacing:4,children:[e.jsx(i,{item:!0,xs:12,lg:6,children:W()}),e.jsx(i,{item:!0,xs:12,lg:6,children:G()})]}),e.jsx(ke,{}),U()]}):e.jsxs(i,{container:!0,spacing:4,children:[e.jsx(i,{item:!0,xs:12,lg:4,children:W()}),e.jsx(i,{item:!0,xs:12,lg:4,children:G()}),e.jsx(i,{item:!0,xs:12,lg:4,children:U()})]})}):e.jsx(o,{sx:{mt:3},children:W()})]})]}),k&&e.jsx(_,{elevation:2,children:e.jsx(H,{children:e.jsxs(o,{display:"flex",flexDirection:"column",alignItems:"center",py:4,children:[e.jsx(ze,{size:60,sx:{mb:2}}),e.jsx(n,{variant:"h6",color:"text.secondary",children:"Processing Table..."}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Parsing and formatting your data"})]})})}),Q&&e.jsx(Fe,{severity:"error",sx:{mb:3,borderRadius:2},action:e.jsx(y,{color:"inherit",size:"small",onClick:()=>A(null),children:"Dismiss"}),children:Q}),!k&&!m&&!g&&e.jsx(_,{elevation:1,children:e.jsx(H,{children:e.jsxs(o,{textAlign:"center",py:6,children:[e.jsx(se,{sx:{fontSize:80,color:v.palette.grey[400],mb:2}}),e.jsx(n,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"Ready to Convert"}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Paste your raw table data above to get started with APA formatting"}),e.jsx(y,{variant:"outlined",onClick:Y,startIcon:e.jsx(ne,{}),sx:{textTransform:"none"},children:"Try with Sample Data"})]})})}),e.jsx(We,{open:xe,autoHideDuration:6e3,onClose:()=>I(!1),message:ue,anchorOrigin:{vertical:"bottom",horizontal:"center"}})]})})};export{tt as default};
