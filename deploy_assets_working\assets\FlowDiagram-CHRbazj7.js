const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./dagre-OKDRZEBW-demB6D4E.js","./graph-CwbYeD9S.js","./_baseUniq-DTckOzFM.js","./layout-BFwSFWu9.js","./_basePickBy-CvIohq0t.js","./clone-B_Po1mvy.js","./mui-libs-CfwFIaTD.js","./react-libs-Cr2nE3UY.js","./supabase-lib-B3goak-P.js","./ml-tensorflow-D19WVUQh.js","./charts-plotly-BhN4fPIu.js","./charts-recharts-d3-BEF1Y_jn.js","./charts-plotly-CuCRB34y.css","./PublicationReadyGate-BGFbKbJc.js","./index-Bpan7Tbe.js","./other-utils-CR9xr_gI.js","./index-DyRZcmzc.css","./c4Diagram-VJAJSXHY-BmFvRjRz.js","./chunk-D6G4REZN-J1hK3QFU.js","./flowDiagram-4HSFHLVR-CJF4d5Iv.js","./chunk-RZ5BOZE2-BWVq1HNy.js","./channel-BPnPIQAa.js","./erDiagram-Q7BY3M3F-BCgjx10N.js","./gitGraphDiagram-7IBYFJ6S-CugateeO.js","./chunk-4BMEZGHF-D0nfVVdS.js","./chunk-XZIHB7SX-vQFpsdml.js","./radar-MK3ICKWK-BCMVXlQA.js","./ganttDiagram-APWFNJXF-CQe5BOum.js","./infoDiagram-PH2N3AL5-799nGRxK.js","./pieDiagram-IB7DONF6-BdfZURv8.js","./quadrantDiagram-7GDLP6J5-DlPiuWL7.js","./xychartDiagram-VJFVF3MP-Ddgq57v-.js","./requirementDiagram-KVF5MWMF-Ge3Zv2Cg.js","./sequenceDiagram-X6HHIX6F-vfl950AQ.js","./classDiagram-GIVACNV2-OWvnKXaf.js","./chunk-A2AXSNBT-CfubMA7k.js","./classDiagram-v2-COTLJTTW-OWvnKXaf.js","./stateDiagram-DGXRK772-JjgLOe5W.js","./chunk-AEK57VVT-BChVRIZA.js","./stateDiagram-v2-YXO3MK2T-q5rp7kkZ.js","./journeyDiagram-U35MCT3I-DPyCxeo8.js","./timeline-definition-BDJGKUSR-b8pClVqI.js","./mindmap-definition-ALO5MXBD-qC5qjWgw.js","./cytoscape.esm-Dyt8tNUa.js","./kanban-definition-NDS4AKOZ-DIMQ9e9X.js","./sankeyDiagram-QLVOVGJD-BswtmS1a.js","./diagram-VNBRO52H-C86j_mW_.js","./diagram-SSKATNLV-CjaIi2bO.js","./blockDiagram-JOT3LUYC-RLlEH1Zn.js","./architectureDiagram-IEHRJDOE-BVFoC4YC.js"])))=>i.map(i=>d[i]);
var Cf=Object.defineProperty;var wf=(e,t,r)=>t in e?Cf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var wt=(e,t,r)=>wf(e,typeof t!="symbol"?t+"":t,r);import{j as H}from"./mui-libs-CfwFIaTD.js";import{g as Dl,e as kf,r as pe}from"./react-libs-Cr2nE3UY.js";import{_ as xt}from"./supabase-lib-B3goak-P.js";import{ag as Ol,ah as $l,ai as Rl,aj as Il,ak as Pl,al as Nl,am as vf,an as Sn,ao as zl,ap as Sf,aq as Tf,ar as Wl,as as _f,at as Bf,au as Lf,av as ql,aw as jl,ax as Af,ay as Mf,x as Gi,g as pt,o as Ef}from"./charts-recharts-d3-BEF1Y_jn.js";import{g as Ii}from"./ml-tensorflow-D19WVUQh.js";import{P as Ff}from"./PublicationReadyGate-BGFbKbJc.js";var Hl={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(kf,function(){var r=1e3,i=6e4,a=36e5,n="millisecond",o="second",s="minute",c="hour",l="day",h="week",u="month",d="quarter",p="year",g="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,b=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,x={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(M){var O=["th","st","nd","rd"],F=M%100;return"["+M+(O[(F-20)%10]||O[F]||O[0])+"]"}},k=function(M,O,F){var I=String(M);return!I||I.length>=O?M:""+Array(O+1-I.length).join(F)+M},L={s:k,z:function(M){var O=-M.utcOffset(),F=Math.abs(O),I=Math.floor(F/60),D=F%60;return(O<=0?"+":"-")+k(I,2,"0")+":"+k(D,2,"0")},m:function M(O,F){if(O.date()<F.date())return-M(F,O);var I=12*(F.year()-O.year())+(F.month()-O.month()),D=O.clone().add(I,u),W=F-D<0,j=O.clone().add(I+(W?-1:1),u);return+(-(I+(F-D)/(W?D-j:j-D))||0)},a:function(M){return M<0?Math.ceil(M)||0:Math.floor(M)},p:function(M){return{M:u,y:p,w:h,d:l,D:g,h:c,m:s,s:o,ms:n,Q:d}[M]||String(M||"").toLowerCase().replace(/s$/,"")},u:function(M){return M===void 0}},B="en",C={};C[B]=x;var w="$isDayjsObject",v=function(M){return M instanceof $||!(!M||!M[w])},E=function M(O,F,I){var D;if(!O)return B;if(typeof O=="string"){var W=O.toLowerCase();C[W]&&(D=W),F&&(C[W]=F,D=W);var j=O.split("-");if(!D&&j.length>1)return M(j[0])}else{var U=O.name;C[U]=O,D=U}return!I&&D&&(B=D),D||!I&&B},_=function(M,O){if(v(M))return M.clone();var F=typeof O=="object"?O:{};return F.date=M,F.args=arguments,new $(F)},S=L;S.l=E,S.i=v,S.w=function(M,O){return _(M,{locale:O.$L,utc:O.$u,x:O.$x,$offset:O.$offset})};var $=function(){function M(F){this.$L=E(F.locale,null,!0),this.parse(F),this.$x=this.$x||F.x||{},this[w]=!0}var O=M.prototype;return O.parse=function(F){this.$d=function(I){var D=I.date,W=I.utc;if(D===null)return new Date(NaN);if(S.u(D))return new Date;if(D instanceof Date)return new Date(D);if(typeof D=="string"&&!/Z$/i.test(D)){var j=D.match(y);if(j){var U=j[2]-1||0,Z=(j[7]||"0").substring(0,3);return W?new Date(Date.UTC(j[1],U,j[3]||1,j[4]||0,j[5]||0,j[6]||0,Z)):new Date(j[1],U,j[3]||1,j[4]||0,j[5]||0,j[6]||0,Z)}}return new Date(D)}(F),this.init()},O.init=function(){var F=this.$d;this.$y=F.getFullYear(),this.$M=F.getMonth(),this.$D=F.getDate(),this.$W=F.getDay(),this.$H=F.getHours(),this.$m=F.getMinutes(),this.$s=F.getSeconds(),this.$ms=F.getMilliseconds()},O.$utils=function(){return S},O.isValid=function(){return this.$d.toString()!==m},O.isSame=function(F,I){var D=_(F);return this.startOf(I)<=D&&D<=this.endOf(I)},O.isAfter=function(F,I){return _(F)<this.startOf(I)},O.isBefore=function(F,I){return this.endOf(I)<_(F)},O.$g=function(F,I,D){return S.u(F)?this[I]:this.set(D,F)},O.unix=function(){return Math.floor(this.valueOf()/1e3)},O.valueOf=function(){return this.$d.getTime()},O.startOf=function(F,I){var D=this,W=!!S.u(I)||I,j=S.p(F),U=function(Ct,gt){var Ht=S.w(D.$u?Date.UTC(D.$y,gt,Ct):new Date(D.$y,gt,Ct),D);return W?Ht:Ht.endOf(l)},Z=function(Ct,gt){return S.w(D.toDate()[Ct].apply(D.toDate("s"),(W?[0,0,0,0]:[23,59,59,999]).slice(gt)),D)},V=this.$W,ht=this.$M,N=this.$D,at="set"+(this.$u?"UTC":"");switch(j){case p:return W?U(1,0):U(31,11);case u:return W?U(1,ht):U(0,ht+1);case h:var ct=this.$locale().weekStart||0,ut=(V<ct?V+7:V)-ct;return U(W?N-ut:N+(6-ut),ht);case l:case g:return Z(at+"Hours",0);case c:return Z(at+"Minutes",1);case s:return Z(at+"Seconds",2);case o:return Z(at+"Milliseconds",3);default:return this.clone()}},O.endOf=function(F){return this.startOf(F,!1)},O.$set=function(F,I){var D,W=S.p(F),j="set"+(this.$u?"UTC":""),U=(D={},D[l]=j+"Date",D[g]=j+"Date",D[u]=j+"Month",D[p]=j+"FullYear",D[c]=j+"Hours",D[s]=j+"Minutes",D[o]=j+"Seconds",D[n]=j+"Milliseconds",D)[W],Z=W===l?this.$D+(I-this.$W):I;if(W===u||W===p){var V=this.clone().set(g,1);V.$d[U](Z),V.init(),this.$d=V.set(g,Math.min(this.$D,V.daysInMonth())).$d}else U&&this.$d[U](Z);return this.init(),this},O.set=function(F,I){return this.clone().$set(F,I)},O.get=function(F){return this[S.p(F)]()},O.add=function(F,I){var D,W=this;F=Number(F);var j=S.p(I),U=function(ht){var N=_(W);return S.w(N.date(N.date()+Math.round(ht*F)),W)};if(j===u)return this.set(u,this.$M+F);if(j===p)return this.set(p,this.$y+F);if(j===l)return U(1);if(j===h)return U(7);var Z=(D={},D[s]=i,D[c]=a,D[o]=r,D)[j]||1,V=this.$d.getTime()+F*Z;return S.w(V,this)},O.subtract=function(F,I){return this.add(-1*F,I)},O.format=function(F){var I=this,D=this.$locale();if(!this.isValid())return D.invalidDate||m;var W=F||"YYYY-MM-DDTHH:mm:ssZ",j=S.z(this),U=this.$H,Z=this.$m,V=this.$M,ht=D.weekdays,N=D.months,at=D.meridiem,ct=function(gt,Ht,It,Bt){return gt&&(gt[Ht]||gt(I,W))||It[Ht].slice(0,Bt)},ut=function(gt){return S.s(U%12||12,gt,"0")},Ct=at||function(gt,Ht,It){var Bt=gt<12?"AM":"PM";return It?Bt.toLowerCase():Bt};return W.replace(b,function(gt,Ht){return Ht||function(It){switch(It){case"YY":return String(I.$y).slice(-2);case"YYYY":return S.s(I.$y,4,"0");case"M":return V+1;case"MM":return S.s(V+1,2,"0");case"MMM":return ct(D.monthsShort,V,N,3);case"MMMM":return ct(N,V);case"D":return I.$D;case"DD":return S.s(I.$D,2,"0");case"d":return String(I.$W);case"dd":return ct(D.weekdaysMin,I.$W,ht,2);case"ddd":return ct(D.weekdaysShort,I.$W,ht,3);case"dddd":return ht[I.$W];case"H":return String(U);case"HH":return S.s(U,2,"0");case"h":return ut(1);case"hh":return ut(2);case"a":return Ct(U,Z,!0);case"A":return Ct(U,Z,!1);case"m":return String(Z);case"mm":return S.s(Z,2,"0");case"s":return String(I.$s);case"ss":return S.s(I.$s,2,"0");case"SSS":return S.s(I.$ms,3,"0");case"Z":return j}return null}(gt)||j.replace(":","")})},O.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},O.diff=function(F,I,D){var W,j=this,U=S.p(I),Z=_(F),V=(Z.utcOffset()-this.utcOffset())*i,ht=this-Z,N=function(){return S.m(j,Z)};switch(U){case p:W=N()/12;break;case u:W=N();break;case d:W=N()/3;break;case h:W=(ht-V)/6048e5;break;case l:W=(ht-V)/864e5;break;case c:W=ht/a;break;case s:W=ht/i;break;case o:W=ht/r;break;default:W=ht}return D?W:S.a(W)},O.daysInMonth=function(){return this.endOf(u).$D},O.$locale=function(){return C[this.$L]},O.locale=function(F,I){if(!F)return this.$L;var D=this.clone(),W=E(F,I,!0);return W&&(D.$L=W),D},O.clone=function(){return S.w(this.$d,this)},O.toDate=function(){return new Date(this.valueOf())},O.toJSON=function(){return this.isValid()?this.toISOString():null},O.toISOString=function(){return this.$d.toISOString()},O.toString=function(){return this.$d.toUTCString()},M}(),R=$.prototype;return _.prototype=R,[["$ms",n],["$s",o],["$m",s],["$H",c],["$W",l],["$M",u],["$y",p],["$D",g]].forEach(function(M){R[M[1]]=function(O){return this.$g(O,M[0],M[1])}}),_.extend=function(M,O){return M.$i||(M(O,$,_),M.$i=!0),_},_.locale=E,_.isDayjs=v,_.unix=function(M){return _(1e3*M)},_.en=C[B],_.Ls=C,_.p={},_})})(Hl);var Df=Hl.exports;const Of=Dl(Df),Yi={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:e=>e>=255?255:e<0?0:e,g:e=>e>=255?255:e<0?0:e,b:e=>e>=255?255:e<0?0:e,h:e=>e%360,s:e=>e>=100?100:e<0?0:e,l:e=>e>=100?100:e<0?0:e,a:e=>e>=1?1:e<0?0:e},toLinear:e=>{const t=e/255;return e>.03928?Math.pow((t+.055)/1.055,2.4):t/12.92},hue2rgb:(e,t,r)=>(r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e),hsl2rgb:({h:e,s:t,l:r},i)=>{if(!t)return r*2.55;e/=360,t/=100,r/=100;const a=r<.5?r*(1+t):r+t-r*t,n=2*r-a;switch(i){case"r":return Yi.hue2rgb(n,a,e+1/3)*255;case"g":return Yi.hue2rgb(n,a,e)*255;case"b":return Yi.hue2rgb(n,a,e-1/3)*255}},rgb2hsl:({r:e,g:t,b:r},i)=>{e/=255,t/=255,r/=255;const a=Math.max(e,t,r),n=Math.min(e,t,r),o=(a+n)/2;if(i==="l")return o*100;if(a===n)return 0;const s=a-n,c=o>.5?s/(2-a-n):s/(a+n);if(i==="s")return c*100;switch(a){case e:return((t-r)/s+(t<r?6:0))*60;case t:return((r-e)/s+2)*60;case r:return((e-t)/s+4)*60;default:return-1}}},$f={clamp:(e,t,r)=>t>r?Math.min(t,Math.max(r,e)):Math.min(r,Math.max(t,e)),round:e=>Math.round(e*1e10)/1e10},Rf={dec2hex:e=>{const t=Math.round(e).toString(16);return t.length>1?t:`0${t}`}},ot={channel:Yi,lang:$f,unit:Rf},Pe={};for(let e=0;e<=255;e++)Pe[e]=ot.unit.dec2hex(e);const zt={ALL:0,RGB:1,HSL:2};class If{constructor(){this.type=zt.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=zt.ALL}is(t){return this.type===t}}class Pf{constructor(t,r){this.color=r,this.changed=!1,this.data=t,this.type=new If}set(t,r){return this.color=r,this.changed=!1,this.data=t,this.type.type=zt.ALL,this}_ensureHSL(){const t=this.data,{h:r,s:i,l:a}=t;r===void 0&&(t.h=ot.channel.rgb2hsl(t,"h")),i===void 0&&(t.s=ot.channel.rgb2hsl(t,"s")),a===void 0&&(t.l=ot.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r,g:i,b:a}=t;r===void 0&&(t.r=ot.channel.hsl2rgb(t,"r")),i===void 0&&(t.g=ot.channel.hsl2rgb(t,"g")),a===void 0&&(t.b=ot.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,r=t.r;return!this.type.is(zt.HSL)&&r!==void 0?r:(this._ensureHSL(),ot.channel.hsl2rgb(t,"r"))}get g(){const t=this.data,r=t.g;return!this.type.is(zt.HSL)&&r!==void 0?r:(this._ensureHSL(),ot.channel.hsl2rgb(t,"g"))}get b(){const t=this.data,r=t.b;return!this.type.is(zt.HSL)&&r!==void 0?r:(this._ensureHSL(),ot.channel.hsl2rgb(t,"b"))}get h(){const t=this.data,r=t.h;return!this.type.is(zt.RGB)&&r!==void 0?r:(this._ensureRGB(),ot.channel.rgb2hsl(t,"h"))}get s(){const t=this.data,r=t.s;return!this.type.is(zt.RGB)&&r!==void 0?r:(this._ensureRGB(),ot.channel.rgb2hsl(t,"s"))}get l(){const t=this.data,r=t.l;return!this.type.is(zt.RGB)&&r!==void 0?r:(this._ensureRGB(),ot.channel.rgb2hsl(t,"l"))}get a(){return this.data.a}set r(t){this.type.set(zt.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set(zt.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set(zt.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set(zt.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set(zt.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set(zt.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}const Aa=new Pf({r:0,g:0,b:0,a:0},"transparent"),Cr={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:e=>{if(e.charCodeAt(0)!==35)return;const t=e.match(Cr.re);if(!t)return;const r=t[1],i=parseInt(r,16),a=r.length,n=a%4===0,o=a>4,s=o?1:17,c=o?8:4,l=n?0:-1,h=o?255:15;return Aa.set({r:(i>>c*(l+3)&h)*s,g:(i>>c*(l+2)&h)*s,b:(i>>c*(l+1)&h)*s,a:n?(i&h)*s/255:1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`#${Pe[Math.round(t)]}${Pe[Math.round(r)]}${Pe[Math.round(i)]}${Pe[Math.round(a*255)]}`:`#${Pe[Math.round(t)]}${Pe[Math.round(r)]}${Pe[Math.round(i)]}`}},Xe={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:e=>{const t=e.match(Xe.hueRe);if(t){const[,r,i]=t;switch(i){case"grad":return ot.channel.clamp.h(parseFloat(r)*.9);case"rad":return ot.channel.clamp.h(parseFloat(r)*180/Math.PI);case"turn":return ot.channel.clamp.h(parseFloat(r)*360)}}return ot.channel.clamp.h(parseFloat(e))},parse:e=>{const t=e.charCodeAt(0);if(t!==104&&t!==72)return;const r=e.match(Xe.re);if(!r)return;const[,i,a,n,o,s]=r;return Aa.set({h:Xe._hue2deg(i),s:ot.channel.clamp.s(parseFloat(a)),l:ot.channel.clamp.l(parseFloat(n)),a:o?ot.channel.clamp.a(s?parseFloat(o)/100:parseFloat(o)):1},e)},stringify:e=>{const{h:t,s:r,l:i,a}=e;return a<1?`hsla(${ot.lang.round(t)}, ${ot.lang.round(r)}%, ${ot.lang.round(i)}%, ${a})`:`hsl(${ot.lang.round(t)}, ${ot.lang.round(r)}%, ${ot.lang.round(i)}%)`}},ci={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:e=>{e=e.toLowerCase();const t=ci.colors[e];if(t)return Cr.parse(t)},stringify:e=>{const t=Cr.stringify(e);for(const r in ci.colors)if(ci.colors[r]===t)return r}},ni={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:e=>{const t=e.charCodeAt(0);if(t!==114&&t!==82)return;const r=e.match(ni.re);if(!r)return;const[,i,a,n,o,s,c,l,h]=r;return Aa.set({r:ot.channel.clamp.r(a?parseFloat(i)*2.55:parseFloat(i)),g:ot.channel.clamp.g(o?parseFloat(n)*2.55:parseFloat(n)),b:ot.channel.clamp.b(c?parseFloat(s)*2.55:parseFloat(s)),a:l?ot.channel.clamp.a(h?parseFloat(l)/100:parseFloat(l)):1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`rgba(${ot.lang.round(t)}, ${ot.lang.round(r)}, ${ot.lang.round(i)}, ${ot.lang.round(a)})`:`rgb(${ot.lang.round(t)}, ${ot.lang.round(r)}, ${ot.lang.round(i)})`}},be={format:{keyword:ci,hex:Cr,rgb:ni,rgba:ni,hsl:Xe,hsla:Xe},parse:e=>{if(typeof e!="string")return e;const t=Cr.parse(e)||ni.parse(e)||Xe.parse(e)||ci.parse(e);if(t)return t;throw new Error(`Unsupported color format: "${e}"`)},stringify:e=>!e.changed&&e.color?e.color:e.type.is(zt.HSL)||e.data.r===void 0?Xe.stringify(e):e.a<1||!Number.isInteger(e.r)||!Number.isInteger(e.g)||!Number.isInteger(e.b)?ni.stringify(e):Cr.stringify(e)},Ul=(e,t)=>{const r=be.parse(e);for(const i in t)r[i]=ot.channel.clamp[i](t[i]);return be.stringify(r)},hi=(e,t,r=0,i=1)=>{if(typeof e!="number")return Ul(e,{a:t});const a=Aa.set({r:ot.channel.clamp.r(e),g:ot.channel.clamp.g(t),b:ot.channel.clamp.b(r),a:ot.channel.clamp.a(i)});return be.stringify(a)},Nf=e=>{const{r:t,g:r,b:i}=be.parse(e),a=.2126*ot.channel.toLinear(t)+.7152*ot.channel.toLinear(r)+.0722*ot.channel.toLinear(i);return ot.lang.round(a)},zf=e=>Nf(e)>=.5,vi=e=>!zf(e),Gl=(e,t,r)=>{const i=be.parse(e),a=i[t],n=ot.channel.clamp[t](a+r);return a!==n&&(i[t]=n),be.stringify(i)},G=(e,t)=>Gl(e,"l",t),it=(e,t)=>Gl(e,"l",-t),A=(e,t)=>{const r=be.parse(e),i={};for(const a in t)t[a]&&(i[a]=r[a]+t[a]);return Ul(e,i)},Wf=(e,t,r=50)=>{const{r:i,g:a,b:n,a:o}=be.parse(e),{r:s,g:c,b:l,a:h}=be.parse(t),u=r/100,d=u*2-1,p=o-h,m=((d*p===-1?d:(d+p)/(1+d*p))+1)/2,y=1-m,b=i*m+s*y,x=a*m+c*y,k=n*m+l*y,L=o*u+h*(1-u);return hi(b,x,k,L)},q=(e,t=100)=>{const r=be.parse(e);return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,Wf(r,e,t)};/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:Yl,setPrototypeOf:ko,isFrozen:qf,getPrototypeOf:jf,getOwnPropertyDescriptor:Hf}=Object;let{freeze:Zt,seal:oe,create:Vl}=Object,{apply:Tn,construct:_n}=typeof Reflect<"u"&&Reflect;Zt||(Zt=function(t){return t});oe||(oe=function(t){return t});Tn||(Tn=function(t,r,i){return t.apply(r,i)});_n||(_n=function(t,r){return new t(...r)});const Pi=Kt(Array.prototype.forEach),Uf=Kt(Array.prototype.lastIndexOf),vo=Kt(Array.prototype.pop),Vr=Kt(Array.prototype.push),Gf=Kt(Array.prototype.splice),Vi=Kt(String.prototype.toLowerCase),sn=Kt(String.prototype.toString),So=Kt(String.prototype.match),Xr=Kt(String.prototype.replace),Yf=Kt(String.prototype.indexOf),Vf=Kt(String.prototype.trim),ce=Kt(Object.prototype.hasOwnProperty),Ut=Kt(RegExp.prototype.test),Zr=Xf(TypeError);function Kt(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return Tn(e,t,i)}}function Xf(e){return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return _n(e,r)}}function dt(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Vi;ko&&ko(e,null);let i=t.length;for(;i--;){let a=t[i];if(typeof a=="string"){const n=r(a);n!==a&&(qf(t)||(t[i]=n),a=n)}e[a]=!0}return e}function Zf(e){for(let t=0;t<e.length;t++)ce(e,t)||(e[t]=null);return e}function Be(e){const t=Vl(null);for(const[r,i]of Yl(e))ce(e,r)&&(Array.isArray(i)?t[r]=Zf(i):i&&typeof i=="object"&&i.constructor===Object?t[r]=Be(i):t[r]=i);return t}function Kr(e,t){for(;e!==null;){const i=Hf(e,t);if(i){if(i.get)return Kt(i.get);if(typeof i.value=="function")return Kt(i.value)}e=jf(e)}function r(){return null}return r}const To=Zt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),on=Zt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),ln=Zt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Kf=Zt(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),cn=Zt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Qf=Zt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),_o=Zt(["#text"]),Bo=Zt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),hn=Zt(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Lo=Zt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Ni=Zt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Jf=oe(/\{\{[\w\W]*|[\w\W]*\}\}/gm),tg=oe(/<%[\w\W]*|[\w\W]*%>/gm),eg=oe(/\$\{[\w\W]*/gm),rg=oe(/^data-[\-\w.\u00B7-\uFFFF]+$/),ig=oe(/^aria-[\-\w]+$/),Xl=oe(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),ag=oe(/^(?:\w+script|data):/i),ng=oe(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Zl=oe(/^html$/i),sg=oe(/^[a-z][.\w]*(-[.\w]+)+$/i);var Ao=Object.freeze({__proto__:null,ARIA_ATTR:ig,ATTR_WHITESPACE:ng,CUSTOM_ELEMENT:sg,DATA_ATTR:rg,DOCTYPE_NAME:Zl,ERB_EXPR:tg,IS_ALLOWED_URI:Xl,IS_SCRIPT_OR_DATA:ag,MUSTACHE_EXPR:Jf,TMPLIT_EXPR:eg});const Qr={element:1,text:3,progressingInstruction:7,comment:8,document:9},og=function(){return typeof window>"u"?null:window},lg=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let i=null;const a="data-tt-policy-suffix";r&&r.hasAttribute(a)&&(i=r.getAttribute(a));const n="dompurify"+(i?"#"+i:"");try{return t.createPolicy(n,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+n+" could not be created."),null}},Mo=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Kl(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:og();const t=rt=>Kl(rt);if(t.version="3.2.6",t.removed=[],!e||!e.document||e.document.nodeType!==Qr.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const i=r,a=i.currentScript,{DocumentFragment:n,HTMLTemplateElement:o,Node:s,Element:c,NodeFilter:l,NamedNodeMap:h=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:d,trustedTypes:p}=e,g=c.prototype,m=Kr(g,"cloneNode"),y=Kr(g,"remove"),b=Kr(g,"nextSibling"),x=Kr(g,"childNodes"),k=Kr(g,"parentNode");if(typeof o=="function"){const rt=r.createElement("template");rt.content&&rt.content.ownerDocument&&(r=rt.content.ownerDocument)}let L,B="";const{implementation:C,createNodeIterator:w,createDocumentFragment:v,getElementsByTagName:E}=r,{importNode:_}=i;let S=Mo();t.isSupported=typeof Yl=="function"&&typeof k=="function"&&C&&C.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:$,ERB_EXPR:R,TMPLIT_EXPR:M,DATA_ATTR:O,ARIA_ATTR:F,IS_SCRIPT_OR_DATA:I,ATTR_WHITESPACE:D,CUSTOM_ELEMENT:W}=Ao;let{IS_ALLOWED_URI:j}=Ao,U=null;const Z=dt({},[...To,...on,...ln,...cn,..._o]);let V=null;const ht=dt({},[...Bo,...hn,...Lo,...Ni]);let N=Object.seal(Vl(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),at=null,ct=null,ut=!0,Ct=!0,gt=!1,Ht=!0,It=!1,Bt=!0,Dt=!1,we=!1,Ka=!1,hr=!1,Ei=!1,Fi=!1,io=!0,ao=!1;const df="user-content-";let Qa=!0,Ur=!1,ur={},dr=null;const no=dt({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let so=null;const oo=dt({},["audio","video","img","source","image","track"]);let Ja=null;const lo=dt({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Di="http://www.w3.org/1998/Math/MathML",Oi="http://www.w3.org/2000/svg",ke="http://www.w3.org/1999/xhtml";let pr=ke,tn=!1,en=null;const pf=dt({},[Di,Oi,ke],sn);let $i=dt({},["mi","mo","mn","ms","mtext"]),Ri=dt({},["annotation-xml"]);const ff=dt({},["title","style","font","a","script"]);let Gr=null;const gf=["application/xhtml+xml","text/html"],mf="text/html";let Et=null,fr=null;const yf=r.createElement("form"),co=function(T){return T instanceof RegExp||T instanceof Function},rn=function(){let T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(fr&&fr===T)){if((!T||typeof T!="object")&&(T={}),T=Be(T),Gr=gf.indexOf(T.PARSER_MEDIA_TYPE)===-1?mf:T.PARSER_MEDIA_TYPE,Et=Gr==="application/xhtml+xml"?sn:Vi,U=ce(T,"ALLOWED_TAGS")?dt({},T.ALLOWED_TAGS,Et):Z,V=ce(T,"ALLOWED_ATTR")?dt({},T.ALLOWED_ATTR,Et):ht,en=ce(T,"ALLOWED_NAMESPACES")?dt({},T.ALLOWED_NAMESPACES,sn):pf,Ja=ce(T,"ADD_URI_SAFE_ATTR")?dt(Be(lo),T.ADD_URI_SAFE_ATTR,Et):lo,so=ce(T,"ADD_DATA_URI_TAGS")?dt(Be(oo),T.ADD_DATA_URI_TAGS,Et):oo,dr=ce(T,"FORBID_CONTENTS")?dt({},T.FORBID_CONTENTS,Et):no,at=ce(T,"FORBID_TAGS")?dt({},T.FORBID_TAGS,Et):Be({}),ct=ce(T,"FORBID_ATTR")?dt({},T.FORBID_ATTR,Et):Be({}),ur=ce(T,"USE_PROFILES")?T.USE_PROFILES:!1,ut=T.ALLOW_ARIA_ATTR!==!1,Ct=T.ALLOW_DATA_ATTR!==!1,gt=T.ALLOW_UNKNOWN_PROTOCOLS||!1,Ht=T.ALLOW_SELF_CLOSE_IN_ATTR!==!1,It=T.SAFE_FOR_TEMPLATES||!1,Bt=T.SAFE_FOR_XML!==!1,Dt=T.WHOLE_DOCUMENT||!1,hr=T.RETURN_DOM||!1,Ei=T.RETURN_DOM_FRAGMENT||!1,Fi=T.RETURN_TRUSTED_TYPE||!1,Ka=T.FORCE_BODY||!1,io=T.SANITIZE_DOM!==!1,ao=T.SANITIZE_NAMED_PROPS||!1,Qa=T.KEEP_CONTENT!==!1,Ur=T.IN_PLACE||!1,j=T.ALLOWED_URI_REGEXP||Xl,pr=T.NAMESPACE||ke,$i=T.MATHML_TEXT_INTEGRATION_POINTS||$i,Ri=T.HTML_INTEGRATION_POINTS||Ri,N=T.CUSTOM_ELEMENT_HANDLING||{},T.CUSTOM_ELEMENT_HANDLING&&co(T.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(N.tagNameCheck=T.CUSTOM_ELEMENT_HANDLING.tagNameCheck),T.CUSTOM_ELEMENT_HANDLING&&co(T.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(N.attributeNameCheck=T.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),T.CUSTOM_ELEMENT_HANDLING&&typeof T.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(N.allowCustomizedBuiltInElements=T.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),It&&(Ct=!1),Ei&&(hr=!0),ur&&(U=dt({},_o),V=[],ur.html===!0&&(dt(U,To),dt(V,Bo)),ur.svg===!0&&(dt(U,on),dt(V,hn),dt(V,Ni)),ur.svgFilters===!0&&(dt(U,ln),dt(V,hn),dt(V,Ni)),ur.mathMl===!0&&(dt(U,cn),dt(V,Lo),dt(V,Ni))),T.ADD_TAGS&&(U===Z&&(U=Be(U)),dt(U,T.ADD_TAGS,Et)),T.ADD_ATTR&&(V===ht&&(V=Be(V)),dt(V,T.ADD_ATTR,Et)),T.ADD_URI_SAFE_ATTR&&dt(Ja,T.ADD_URI_SAFE_ATTR,Et),T.FORBID_CONTENTS&&(dr===no&&(dr=Be(dr)),dt(dr,T.FORBID_CONTENTS,Et)),Qa&&(U["#text"]=!0),Dt&&dt(U,["html","head","body"]),U.table&&(dt(U,["tbody"]),delete at.tbody),T.TRUSTED_TYPES_POLICY){if(typeof T.TRUSTED_TYPES_POLICY.createHTML!="function")throw Zr('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof T.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Zr('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');L=T.TRUSTED_TYPES_POLICY,B=L.createHTML("")}else L===void 0&&(L=lg(p,a)),L!==null&&typeof B=="string"&&(B=L.createHTML(""));Zt&&Zt(T),fr=T}},ho=dt({},[...on,...ln,...Kf]),uo=dt({},[...cn,...Qf]),bf=function(T){let z=k(T);(!z||!z.tagName)&&(z={namespaceURI:pr,tagName:"template"});const K=Vi(T.tagName),vt=Vi(z.tagName);return en[T.namespaceURI]?T.namespaceURI===Oi?z.namespaceURI===ke?K==="svg":z.namespaceURI===Di?K==="svg"&&(vt==="annotation-xml"||$i[vt]):!!ho[K]:T.namespaceURI===Di?z.namespaceURI===ke?K==="math":z.namespaceURI===Oi?K==="math"&&Ri[vt]:!!uo[K]:T.namespaceURI===ke?z.namespaceURI===Oi&&!Ri[vt]||z.namespaceURI===Di&&!$i[vt]?!1:!uo[K]&&(ff[K]||!ho[K]):!!(Gr==="application/xhtml+xml"&&en[T.namespaceURI]):!1},de=function(T){Vr(t.removed,{element:T});try{k(T).removeChild(T)}catch{y(T)}},gr=function(T,z){try{Vr(t.removed,{attribute:z.getAttributeNode(T),from:z})}catch{Vr(t.removed,{attribute:null,from:z})}if(z.removeAttribute(T),T==="is")if(hr||Ei)try{de(z)}catch{}else try{z.setAttribute(T,"")}catch{}},po=function(T){let z=null,K=null;if(Ka)T="<remove></remove>"+T;else{const Lt=So(T,/^[\r\n\t ]+/);K=Lt&&Lt[0]}Gr==="application/xhtml+xml"&&pr===ke&&(T='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+T+"</body></html>");const vt=L?L.createHTML(T):T;if(pr===ke)try{z=new d().parseFromString(vt,Gr)}catch{}if(!z||!z.documentElement){z=C.createDocument(pr,"template",null);try{z.documentElement.innerHTML=tn?B:vt}catch{}}const Pt=z.body||z.documentElement;return T&&K&&Pt.insertBefore(r.createTextNode(K),Pt.childNodes[0]||null),pr===ke?E.call(z,Dt?"html":"body")[0]:Dt?z.documentElement:Pt},fo=function(T){return w.call(T.ownerDocument||T,T,l.SHOW_ELEMENT|l.SHOW_COMMENT|l.SHOW_TEXT|l.SHOW_PROCESSING_INSTRUCTION|l.SHOW_CDATA_SECTION,null)},an=function(T){return T instanceof u&&(typeof T.nodeName!="string"||typeof T.textContent!="string"||typeof T.removeChild!="function"||!(T.attributes instanceof h)||typeof T.removeAttribute!="function"||typeof T.setAttribute!="function"||typeof T.namespaceURI!="string"||typeof T.insertBefore!="function"||typeof T.hasChildNodes!="function")},go=function(T){return typeof s=="function"&&T instanceof s};function ve(rt,T,z){Pi(rt,K=>{K.call(t,T,z,fr)})}const mo=function(T){let z=null;if(ve(S.beforeSanitizeElements,T,null),an(T))return de(T),!0;const K=Et(T.nodeName);if(ve(S.uponSanitizeElement,T,{tagName:K,allowedTags:U}),Bt&&T.hasChildNodes()&&!go(T.firstElementChild)&&Ut(/<[/\w!]/g,T.innerHTML)&&Ut(/<[/\w!]/g,T.textContent)||T.nodeType===Qr.progressingInstruction||Bt&&T.nodeType===Qr.comment&&Ut(/<[/\w]/g,T.data))return de(T),!0;if(!U[K]||at[K]){if(!at[K]&&bo(K)&&(N.tagNameCheck instanceof RegExp&&Ut(N.tagNameCheck,K)||N.tagNameCheck instanceof Function&&N.tagNameCheck(K)))return!1;if(Qa&&!dr[K]){const vt=k(T)||T.parentNode,Pt=x(T)||T.childNodes;if(Pt&&vt){const Lt=Pt.length;for(let Qt=Lt-1;Qt>=0;--Qt){const Se=m(Pt[Qt],!0);Se.__removalCount=(T.__removalCount||0)+1,vt.insertBefore(Se,b(T))}}}return de(T),!0}return T instanceof c&&!bf(T)||(K==="noscript"||K==="noembed"||K==="noframes")&&Ut(/<\/no(script|embed|frames)/i,T.innerHTML)?(de(T),!0):(It&&T.nodeType===Qr.text&&(z=T.textContent,Pi([$,R,M],vt=>{z=Xr(z,vt," ")}),T.textContent!==z&&(Vr(t.removed,{element:T.cloneNode()}),T.textContent=z)),ve(S.afterSanitizeElements,T,null),!1)},yo=function(T,z,K){if(io&&(z==="id"||z==="name")&&(K in r||K in yf))return!1;if(!(Ct&&!ct[z]&&Ut(O,z))){if(!(ut&&Ut(F,z))){if(!V[z]||ct[z]){if(!(bo(T)&&(N.tagNameCheck instanceof RegExp&&Ut(N.tagNameCheck,T)||N.tagNameCheck instanceof Function&&N.tagNameCheck(T))&&(N.attributeNameCheck instanceof RegExp&&Ut(N.attributeNameCheck,z)||N.attributeNameCheck instanceof Function&&N.attributeNameCheck(z))||z==="is"&&N.allowCustomizedBuiltInElements&&(N.tagNameCheck instanceof RegExp&&Ut(N.tagNameCheck,K)||N.tagNameCheck instanceof Function&&N.tagNameCheck(K))))return!1}else if(!Ja[z]){if(!Ut(j,Xr(K,D,""))){if(!((z==="src"||z==="xlink:href"||z==="href")&&T!=="script"&&Yf(K,"data:")===0&&so[T])){if(!(gt&&!Ut(I,Xr(K,D,"")))){if(K)return!1}}}}}}return!0},bo=function(T){return T!=="annotation-xml"&&So(T,W)},xo=function(T){ve(S.beforeSanitizeAttributes,T,null);const{attributes:z}=T;if(!z||an(T))return;const K={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:V,forceKeepAttr:void 0};let vt=z.length;for(;vt--;){const Pt=z[vt],{name:Lt,namespaceURI:Qt,value:Se}=Pt,Yr=Et(Lt),nn=Se;let Nt=Lt==="value"?nn:Vf(nn);if(K.attrName=Yr,K.attrValue=Nt,K.keepAttr=!0,K.forceKeepAttr=void 0,ve(S.uponSanitizeAttribute,T,K),Nt=K.attrValue,ao&&(Yr==="id"||Yr==="name")&&(gr(Lt,T),Nt=df+Nt),Bt&&Ut(/((--!?|])>)|<\/(style|title)/i,Nt)){gr(Lt,T);continue}if(K.forceKeepAttr)continue;if(!K.keepAttr){gr(Lt,T);continue}if(!Ht&&Ut(/\/>/i,Nt)){gr(Lt,T);continue}It&&Pi([$,R,M],wo=>{Nt=Xr(Nt,wo," ")});const Co=Et(T.nodeName);if(!yo(Co,Yr,Nt)){gr(Lt,T);continue}if(L&&typeof p=="object"&&typeof p.getAttributeType=="function"&&!Qt)switch(p.getAttributeType(Co,Yr)){case"TrustedHTML":{Nt=L.createHTML(Nt);break}case"TrustedScriptURL":{Nt=L.createScriptURL(Nt);break}}if(Nt!==nn)try{Qt?T.setAttributeNS(Qt,Lt,Nt):T.setAttribute(Lt,Nt),an(T)?de(T):vo(t.removed)}catch{gr(Lt,T)}}ve(S.afterSanitizeAttributes,T,null)},xf=function rt(T){let z=null;const K=fo(T);for(ve(S.beforeSanitizeShadowDOM,T,null);z=K.nextNode();)ve(S.uponSanitizeShadowNode,z,null),mo(z),xo(z),z.content instanceof n&&rt(z.content);ve(S.afterSanitizeShadowDOM,T,null)};return t.sanitize=function(rt){let T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},z=null,K=null,vt=null,Pt=null;if(tn=!rt,tn&&(rt="<!-->"),typeof rt!="string"&&!go(rt))if(typeof rt.toString=="function"){if(rt=rt.toString(),typeof rt!="string")throw Zr("dirty is not a string, aborting")}else throw Zr("toString is not a function");if(!t.isSupported)return rt;if(we||rn(T),t.removed=[],typeof rt=="string"&&(Ur=!1),Ur){if(rt.nodeName){const Se=Et(rt.nodeName);if(!U[Se]||at[Se])throw Zr("root node is forbidden and cannot be sanitized in-place")}}else if(rt instanceof s)z=po("<!---->"),K=z.ownerDocument.importNode(rt,!0),K.nodeType===Qr.element&&K.nodeName==="BODY"||K.nodeName==="HTML"?z=K:z.appendChild(K);else{if(!hr&&!It&&!Dt&&rt.indexOf("<")===-1)return L&&Fi?L.createHTML(rt):rt;if(z=po(rt),!z)return hr?null:Fi?B:""}z&&Ka&&de(z.firstChild);const Lt=fo(Ur?rt:z);for(;vt=Lt.nextNode();)mo(vt),xo(vt),vt.content instanceof n&&xf(vt.content);if(Ur)return rt;if(hr){if(Ei)for(Pt=v.call(z.ownerDocument);z.firstChild;)Pt.appendChild(z.firstChild);else Pt=z;return(V.shadowroot||V.shadowrootmode)&&(Pt=_.call(i,Pt,!0)),Pt}let Qt=Dt?z.outerHTML:z.innerHTML;return Dt&&U["!doctype"]&&z.ownerDocument&&z.ownerDocument.doctype&&z.ownerDocument.doctype.name&&Ut(Zl,z.ownerDocument.doctype.name)&&(Qt="<!DOCTYPE "+z.ownerDocument.doctype.name+`>
`+Qt),It&&Pi([$,R,M],Se=>{Qt=Xr(Qt,Se," ")}),L&&Fi?L.createHTML(Qt):Qt},t.setConfig=function(){let rt=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};rn(rt),we=!0},t.clearConfig=function(){fr=null,we=!1},t.isValidAttribute=function(rt,T,z){fr||rn({});const K=Et(rt),vt=Et(T);return yo(K,vt,z)},t.addHook=function(rt,T){typeof T=="function"&&Vr(S[rt],T)},t.removeHook=function(rt,T){if(T!==void 0){const z=Uf(S[rt],T);return z===-1?void 0:Gf(S[rt],z,1)[0]}return vo(S[rt])},t.removeHooks=function(rt){S[rt]=[]},t.removeAllHooks=function(){S=Mo()},t}var Mr=Kl(),Ql=Object.defineProperty,f=(e,t)=>Ql(e,"name",{value:t,configurable:!0}),cg=(e,t)=>{for(var r in t)Ql(e,r,{get:t[r],enumerable:!0})},Te={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},P={trace:f((...e)=>{},"trace"),debug:f((...e)=>{},"debug"),info:f((...e)=>{},"info"),warn:f((...e)=>{},"warn"),error:f((...e)=>{},"error"),fatal:f((...e)=>{},"fatal")},ds=f(function(e="fatal"){let t=Te.fatal;typeof e=="string"?e.toLowerCase()in Te&&(t=Te[e]):typeof e=="number"&&(t=e),P.trace=()=>{},P.debug=()=>{},P.info=()=>{},P.warn=()=>{},P.error=()=>{},P.fatal=()=>{},t<=Te.fatal&&(P.fatal=console.error?console.error.bind(console,ne("FATAL"),"color: orange"):console.log.bind(console,"\x1B[35m",ne("FATAL"))),t<=Te.error&&(P.error=console.error?console.error.bind(console,ne("ERROR"),"color: orange"):console.log.bind(console,"\x1B[31m",ne("ERROR"))),t<=Te.warn&&(P.warn=console.warn?console.warn.bind(console,ne("WARN"),"color: orange"):console.log.bind(console,"\x1B[33m",ne("WARN"))),t<=Te.info&&(P.info=console.info?console.info.bind(console,ne("INFO"),"color: lightblue"):console.log.bind(console,"\x1B[34m",ne("INFO"))),t<=Te.debug&&(P.debug=console.debug?console.debug.bind(console,ne("DEBUG"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",ne("DEBUG"))),t<=Te.trace&&(P.trace=console.debug?console.debug.bind(console,ne("TRACE"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",ne("TRACE")))},"setLogLevel"),ne=f(e=>`%c${Of().format("ss.SSS")} : ${e} : `,"format"),Jl=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,ui=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,hg=/\s*%%.*\n/gm,kr,tc=(kr=class extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}},f(kr,"UnknownDiagramError"),kr),Er={},ps=f(function(e,t){e=e.replace(Jl,"").replace(ui,"").replace(hg,`
`);for(const[r,{detector:i}]of Object.entries(Er))if(i(e,t))return r;throw new tc(`No diagram type detected matching given configuration for text: ${e}`)},"detectType"),ec=f((...e)=>{for(const{id:t,detector:r,loader:i}of e)rc(t,r,i)},"registerLazyLoadedDiagrams"),rc=f((e,t,r)=>{Er[e]&&P.warn(`Detector with key ${e} already exists. Overwriting.`),Er[e]={detector:t,loader:r},P.debug(`Detector with key ${e} added${r?" with loader":""}`)},"addDetector"),ug=f(e=>Er[e].loader,"getDiagramLoader"),Bn=f((e,t,{depth:r=2,clobber:i=!1}={})=>{const a={depth:r,clobber:i};return Array.isArray(t)&&!Array.isArray(e)?(t.forEach(n=>Bn(e,n,a)),e):Array.isArray(t)&&Array.isArray(e)?(t.forEach(n=>{e.includes(n)||e.push(n)}),e):e===void 0||r<=0?e!=null&&typeof e=="object"&&typeof t=="object"?Object.assign(e,t):t:(t!==void 0&&typeof e=="object"&&typeof t=="object"&&Object.keys(t).forEach(n=>{typeof t[n]=="object"&&(e[n]===void 0||typeof e[n]=="object")?(e[n]===void 0&&(e[n]=Array.isArray(t[n])?[]:{}),e[n]=Bn(e[n],t[n],{depth:r-1,clobber:i})):(i||typeof e[n]!="object"&&typeof t[n]!="object")&&(e[n]=t[n])}),e)},"assignWithDepth"),Rt=Bn,Ma="#ffffff",Ea="#f2f2f2",Gt=f((e,t)=>t?A(e,{s:-40,l:10}):A(e,{s:-40,l:-10}),"mkBorder"),vr,dg=(vr=class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){var r,i,a,n,o,s,c,l,h,u,d,p,g,m,y,b,x,k,L,B,C;if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||A(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||A(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||Gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||Gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||Gt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||Gt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||q(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||q(this.tertiaryColor),this.lineColor=this.lineColor||q(this.background),this.arrowheadColor=this.arrowheadColor||q(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?it(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||this.actorBorder,this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||it(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||q(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||G(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.darkMode?(this.rowOdd=this.rowOdd||it(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||it(this.mainBkg,10)):(this.rowOdd=this.rowOdd||G(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||G(this.mainBkg,5)),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||A(this.primaryColor,{h:30}),this.cScale4=this.cScale4||A(this.primaryColor,{h:60}),this.cScale5=this.cScale5||A(this.primaryColor,{h:90}),this.cScale6=this.cScale6||A(this.primaryColor,{h:120}),this.cScale7=this.cScale7||A(this.primaryColor,{h:150}),this.cScale8=this.cScale8||A(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||A(this.primaryColor,{h:270}),this.cScale10=this.cScale10||A(this.primaryColor,{h:300}),this.cScale11=this.cScale11||A(this.primaryColor,{h:330}),this.darkMode)for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScale"+w]=it(this["cScale"+w],75);else for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScale"+w]=it(this["cScale"+w],25);for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScaleInv"+w]=this["cScaleInv"+w]||q(this["cScale"+w]);for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this.darkMode?this["cScalePeer"+w]=this["cScalePeer"+w]||G(this["cScale"+w],10):this["cScalePeer"+w]=this["cScalePeer"+w]||it(this["cScale"+w],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScaleLabel"+w]=this["cScaleLabel"+w]||this.scaleLabelColor;const t=this.darkMode?-4:-1;for(let w=0;w<5;w++)this["surface"+w]=this["surface"+w]||A(this.mainBkg,{h:180,s:-15,l:t*(5+w*3)}),this["surfacePeer"+w]=this["surfacePeer"+w]||A(this.mainBkg,{h:180,s:-15,l:t*(8+w*3)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||A(this.primaryColor,{h:64}),this.fillType3=this.fillType3||A(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||A(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||A(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||A(this.primaryColor,{h:128}),this.fillType7=this.fillType7||A(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||A(this.primaryColor,{l:-10}),this.pie5=this.pie5||A(this.secondaryColor,{l:-10}),this.pie6=this.pie6||A(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||A(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||A(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||A(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||A(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||A(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||A(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.radar={axisColor:((r=this.radar)==null?void 0:r.axisColor)||this.lineColor,axisStrokeWidth:((i=this.radar)==null?void 0:i.axisStrokeWidth)||2,axisLabelFontSize:((a=this.radar)==null?void 0:a.axisLabelFontSize)||12,curveOpacity:((n=this.radar)==null?void 0:n.curveOpacity)||.5,curveStrokeWidth:((o=this.radar)==null?void 0:o.curveStrokeWidth)||2,graticuleColor:((s=this.radar)==null?void 0:s.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((c=this.radar)==null?void 0:c.graticuleStrokeWidth)||1,graticuleOpacity:((l=this.radar)==null?void 0:l.graticuleOpacity)||.3,legendBoxSize:((h=this.radar)==null?void 0:h.legendBoxSize)||12,legendFontSize:((u=this.radar)==null?void 0:u.legendFontSize)||12},this.archEdgeColor=this.archEdgeColor||"#777",this.archEdgeArrowColor=this.archEdgeArrowColor||"#777",this.archEdgeWidth=this.archEdgeWidth||"3",this.archGroupBorderColor=this.archGroupBorderColor||"#000",this.archGroupBorderWidth=this.archGroupBorderWidth||"2px",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||A(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||A(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||A(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||A(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||A(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||A(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||vi(this.quadrant1Fill)?G(this.quadrant1Fill):it(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((d=this.xyChart)==null?void 0:d.backgroundColor)||this.background,titleColor:((p=this.xyChart)==null?void 0:p.titleColor)||this.primaryTextColor,xAxisTitleColor:((g=this.xyChart)==null?void 0:g.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((m=this.xyChart)==null?void 0:m.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((y=this.xyChart)==null?void 0:y.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((b=this.xyChart)==null?void 0:b.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((k=this.xyChart)==null?void 0:k.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((L=this.xyChart)==null?void 0:L.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((B=this.xyChart)==null?void 0:B.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((C=this.xyChart)==null?void 0:C.plotColorPalette)||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?it(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||A(this.primaryColor,{h:-30}),this.git4=this.git4||A(this.primaryColor,{h:-60}),this.git5=this.git5||A(this.primaryColor,{h:-90}),this.git6=this.git6||A(this.primaryColor,{h:60}),this.git7=this.git7||A(this.primaryColor,{h:120}),this.darkMode?(this.git0=G(this.git0,25),this.git1=G(this.git1,25),this.git2=G(this.git2,25),this.git3=G(this.git3,25),this.git4=G(this.git4,25),this.git5=G(this.git5,25),this.git6=G(this.git6,25),this.git7=G(this.git7,25)):(this.git0=it(this.git0,25),this.git1=it(this.git1,25),this.git2=it(this.git2,25),this.git3=it(this.git3,25),this.git4=it(this.git4,25),this.git5=it(this.git5,25),this.git6=it(this.git6,25),this.git7=it(this.git7,25)),this.gitInv0=this.gitInv0||q(this.git0),this.gitInv1=this.gitInv1||q(this.git1),this.gitInv2=this.gitInv2||q(this.git2),this.gitInv3=this.gitInv3||q(this.git3),this.gitInv4=this.gitInv4||q(this.git4),this.gitInv5=this.gitInv5||q(this.git5),this.gitInv6=this.gitInv6||q(this.git6),this.gitInv7=this.gitInv7||q(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ma,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ea}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(vr,"Theme"),vr),pg=f(e=>{const t=new dg;return t.calculate(e),t},"getThemeVariables"),Sr,fg=(Sr=class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=G(this.primaryColor,16),this.tertiaryColor=A(this.primaryColor,{h:-160}),this.primaryBorderColor=q(this.background),this.secondaryBorderColor=Gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=q(this.primaryColor),this.secondaryTextColor=q(this.secondaryColor),this.tertiaryTextColor=q(this.tertiaryColor),this.lineColor=q(this.background),this.textColor=q(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=G(q("#323D47"),10),this.lineColor="calculated",this.border1="#ccc",this.border2=hi(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=it("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=it(this.sectionBkgColor,10),this.taskBorderColor=hi(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=hi(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||G(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||it(this.mainBkg,10),this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,d,p,g,m,y,b,x,k,L,B;this.secondBkg=G(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=G(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.actorBorder,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=G(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=A(this.primaryColor,{h:64}),this.fillType3=A(this.secondaryColor,{h:64}),this.fillType4=A(this.primaryColor,{h:-64}),this.fillType5=A(this.secondaryColor,{h:-64}),this.fillType6=A(this.primaryColor,{h:128}),this.fillType7=A(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||A(this.primaryColor,{h:30}),this.cScale4=this.cScale4||A(this.primaryColor,{h:60}),this.cScale5=this.cScale5||A(this.primaryColor,{h:90}),this.cScale6=this.cScale6||A(this.primaryColor,{h:120}),this.cScale7=this.cScale7||A(this.primaryColor,{h:150}),this.cScale8=this.cScale8||A(this.primaryColor,{h:210}),this.cScale9=this.cScale9||A(this.primaryColor,{h:270}),this.cScale10=this.cScale10||A(this.primaryColor,{h:300}),this.cScale11=this.cScale11||A(this.primaryColor,{h:330});for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||q(this["cScale"+C]);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScalePeer"+C]=this["cScalePeer"+C]||G(this["cScale"+C],10);for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||A(this.mainBkg,{h:30,s:-30,l:-(-10+C*4)}),this["surfacePeer"+C]=this["surfacePeer"+C]||A(this.mainBkg,{h:30,s:-30,l:-(-7+C*4)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.scaleLabelColor;for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["pie"+C]=this["cScale"+C];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||A(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||A(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||A(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||A(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||A(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||A(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||vi(this.quadrant1Fill)?G(this.quadrant1Fill):it(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background},this.radar={axisColor:((d=this.radar)==null?void 0:d.axisColor)||this.lineColor,axisStrokeWidth:((p=this.radar)==null?void 0:p.axisStrokeWidth)||2,axisLabelFontSize:((g=this.radar)==null?void 0:g.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((b=this.radar)==null?void 0:b.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((x=this.radar)==null?void 0:x.graticuleStrokeWidth)||1,graticuleOpacity:((k=this.radar)==null?void 0:k.graticuleOpacity)||.3,legendBoxSize:((L=this.radar)==null?void 0:L.legendBoxSize)||12,legendFontSize:((B=this.radar)==null?void 0:B.legendFontSize)||12},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?it(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=G(this.secondaryColor,20),this.git1=G(this.pie2||this.secondaryColor,20),this.git2=G(this.pie3||this.tertiaryColor,20),this.git3=G(this.pie4||A(this.primaryColor,{h:-30}),20),this.git4=G(this.pie5||A(this.primaryColor,{h:-60}),20),this.git5=G(this.pie6||A(this.primaryColor,{h:-90}),10),this.git6=G(this.pie7||A(this.primaryColor,{h:60}),10),this.git7=G(this.pie8||A(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||q(this.git0),this.gitInv1=this.gitInv1||q(this.git1),this.gitInv2=this.gitInv2||q(this.git2),this.gitInv3=this.gitInv3||q(this.git3),this.gitInv4=this.gitInv4||q(this.git4),this.gitInv5=this.gitInv5||q(this.git5),this.gitInv6=this.gitInv6||q(this.git6),this.gitInv7=this.gitInv7||q(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||q(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||q(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||G(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||G(this.background,2),this.nodeBorder=this.nodeBorder||"#999"}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(Sr,"Theme"),Sr),gg=f(e=>{const t=new fg;return t.calculate(e),t},"getThemeVariables"),Tr,mg=(Tr=class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=A(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=A(this.primaryColor,{h:-160}),this.primaryBorderColor=Gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=q(this.primaryColor),this.secondaryTextColor=q(this.secondaryColor),this.tertiaryTextColor=q(this.tertiaryColor),this.lineColor=q(this.background),this.textColor=q(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="rgba(232,232,232, 0.8)",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=hi(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd="calculated",this.rowEven="calculated",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,d,p,g,m,y,b,x,k,L,B;this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||A(this.primaryColor,{h:30}),this.cScale4=this.cScale4||A(this.primaryColor,{h:60}),this.cScale5=this.cScale5||A(this.primaryColor,{h:90}),this.cScale6=this.cScale6||A(this.primaryColor,{h:120}),this.cScale7=this.cScale7||A(this.primaryColor,{h:150}),this.cScale8=this.cScale8||A(this.primaryColor,{h:210}),this.cScale9=this.cScale9||A(this.primaryColor,{h:270}),this.cScale10=this.cScale10||A(this.primaryColor,{h:300}),this.cScale11=this.cScale11||A(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||it(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||it(this.tertiaryColor,40);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScale"+C]=it(this["cScale"+C],10),this["cScalePeer"+C]=this["cScalePeer"+C]||it(this["cScale"+C],25);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||A(this["cScale"+C],{h:180});for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||A(this.mainBkg,{h:30,l:-(5+C*5)}),this["surfacePeer"+C]=this["surfacePeer"+C]||A(this.mainBkg,{h:30,l:-(7+C*5)});if(this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||q(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||q(this.labelTextColor);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=G(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||G(this.primaryColor,75)||"#ffffff",this.rowEven=this.rowEven||G(this.primaryColor,1),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=A(this.primaryColor,{h:64}),this.fillType3=A(this.secondaryColor,{h:64}),this.fillType4=A(this.primaryColor,{h:-64}),this.fillType5=A(this.secondaryColor,{h:-64}),this.fillType6=A(this.primaryColor,{h:128}),this.fillType7=A(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||A(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||A(this.primaryColor,{l:-10}),this.pie5=this.pie5||A(this.secondaryColor,{l:-30}),this.pie6=this.pie6||A(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||A(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||A(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||A(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||A(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||A(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||A(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||A(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||A(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||A(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||A(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||A(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||A(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||vi(this.quadrant1Fill)?G(this.quadrant1Fill):it(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((a=this.radar)==null?void 0:a.curveOpacity)||.5,curveStrokeWidth:((n=this.radar)==null?void 0:n.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((d=this.xyChart)==null?void 0:d.titleColor)||this.primaryTextColor,xAxisTitleColor:((p=this.xyChart)==null?void 0:p.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((g=this.xyChart)==null?void 0:g.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((b=this.xyChart)==null?void 0:b.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((x=this.xyChart)==null?void 0:x.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((k=this.xyChart)==null?void 0:k.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((L=this.xyChart)==null?void 0:L.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((B=this.xyChart)==null?void 0:B.plotColorPalette)||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||A(this.primaryColor,{h:-30}),this.git4=this.git4||A(this.primaryColor,{h:-60}),this.git5=this.git5||A(this.primaryColor,{h:-90}),this.git6=this.git6||A(this.primaryColor,{h:60}),this.git7=this.git7||A(this.primaryColor,{h:120}),this.darkMode?(this.git0=G(this.git0,25),this.git1=G(this.git1,25),this.git2=G(this.git2,25),this.git3=G(this.git3,25),this.git4=G(this.git4,25),this.git5=G(this.git5,25),this.git6=G(this.git6,25),this.git7=G(this.git7,25)):(this.git0=it(this.git0,25),this.git1=it(this.git1,25),this.git2=it(this.git2,25),this.git3=it(this.git3,25),this.git4=it(this.git4,25),this.git5=it(this.git5,25),this.git6=it(this.git6,25),this.git7=it(this.git7,25)),this.gitInv0=this.gitInv0||it(q(this.git0),25),this.gitInv1=this.gitInv1||q(this.git1),this.gitInv2=this.gitInv2||q(this.git2),this.gitInv3=this.gitInv3||q(this.git3),this.gitInv4=this.gitInv4||q(this.git4),this.gitInv5=this.gitInv5||q(this.git5),this.gitInv6=this.gitInv6||q(this.git6),this.gitInv7=this.gitInv7||q(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||q(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||q(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ma,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ea}calculate(t){if(Object.keys(this).forEach(i=>{this[i]==="calculated"&&(this[i]=void 0)}),typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(Tr,"Theme"),Tr),yg=f(e=>{const t=new mg;return t.calculate(e),t},"getThemeVariables"),_r,bg=(_r=class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=G("#cde498",10),this.primaryBorderColor=Gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=q(this.primaryColor),this.secondaryTextColor=q(this.secondaryColor),this.tertiaryTextColor=q(this.primaryColor),this.lineColor=q(this.background),this.textColor=q(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,d,p,g,m,y,b,x,k,L,B;this.actorBorder=it(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||A(this.primaryColor,{h:30}),this.cScale4=this.cScale4||A(this.primaryColor,{h:60}),this.cScale5=this.cScale5||A(this.primaryColor,{h:90}),this.cScale6=this.cScale6||A(this.primaryColor,{h:120}),this.cScale7=this.cScale7||A(this.primaryColor,{h:150}),this.cScale8=this.cScale8||A(this.primaryColor,{h:210}),this.cScale9=this.cScale9||A(this.primaryColor,{h:270}),this.cScale10=this.cScale10||A(this.primaryColor,{h:300}),this.cScale11=this.cScale11||A(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||it(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||it(this.tertiaryColor,40);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScale"+C]=it(this["cScale"+C],10),this["cScalePeer"+C]=this["cScalePeer"+C]||it(this["cScale"+C],25);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||A(this["cScale"+C],{h:180});this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.scaleLabelColor;for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||A(this.mainBkg,{h:30,s:-30,l:-(5+C*5)}),this["surfacePeer"+C]=this["surfacePeer"+C]||A(this.mainBkg,{h:30,s:-30,l:-(8+C*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||G(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||G(this.mainBkg,20),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=A(this.primaryColor,{h:64}),this.fillType3=A(this.secondaryColor,{h:64}),this.fillType4=A(this.primaryColor,{h:-64}),this.fillType5=A(this.secondaryColor,{h:-64}),this.fillType6=A(this.primaryColor,{h:128}),this.fillType7=A(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||A(this.primaryColor,{l:-30}),this.pie5=this.pie5||A(this.secondaryColor,{l:-30}),this.pie6=this.pie6||A(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||A(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||A(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||A(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||A(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||A(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||A(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||A(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||A(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||A(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||A(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||A(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||A(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||vi(this.quadrant1Fill)?G(this.quadrant1Fill):it(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg},this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((a=this.radar)==null?void 0:a.curveOpacity)||.5,curveStrokeWidth:((n=this.radar)==null?void 0:n.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((d=this.xyChart)==null?void 0:d.titleColor)||this.primaryTextColor,xAxisTitleColor:((p=this.xyChart)==null?void 0:p.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((g=this.xyChart)==null?void 0:g.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((b=this.xyChart)==null?void 0:b.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((x=this.xyChart)==null?void 0:x.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((k=this.xyChart)==null?void 0:k.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((L=this.xyChart)==null?void 0:L.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((B=this.xyChart)==null?void 0:B.plotColorPalette)||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||A(this.primaryColor,{h:-30}),this.git4=this.git4||A(this.primaryColor,{h:-60}),this.git5=this.git5||A(this.primaryColor,{h:-90}),this.git6=this.git6||A(this.primaryColor,{h:60}),this.git7=this.git7||A(this.primaryColor,{h:120}),this.darkMode?(this.git0=G(this.git0,25),this.git1=G(this.git1,25),this.git2=G(this.git2,25),this.git3=G(this.git3,25),this.git4=G(this.git4,25),this.git5=G(this.git5,25),this.git6=G(this.git6,25),this.git7=G(this.git7,25)):(this.git0=it(this.git0,25),this.git1=it(this.git1,25),this.git2=it(this.git2,25),this.git3=it(this.git3,25),this.git4=it(this.git4,25),this.git5=it(this.git5,25),this.git6=it(this.git6,25),this.git7=it(this.git7,25)),this.gitInv0=this.gitInv0||q(this.git0),this.gitInv1=this.gitInv1||q(this.git1),this.gitInv2=this.gitInv2||q(this.git2),this.gitInv3=this.gitInv3||q(this.git3),this.gitInv4=this.gitInv4||q(this.git4),this.gitInv5=this.gitInv5||q(this.git5),this.gitInv6=this.gitInv6||q(this.git6),this.gitInv7=this.gitInv7||q(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||q(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||q(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ma,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ea}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(_r,"Theme"),_r),xg=f(e=>{const t=new bg;return t.calculate(e),t},"getThemeVariables"),Br,Cg=(Br=class{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=G(this.contrast,55),this.background="#ffffff",this.tertiaryColor=A(this.primaryColor,{h:-160}),this.primaryBorderColor=Gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=q(this.primaryColor),this.secondaryTextColor=q(this.secondaryColor),this.tertiaryTextColor=q(this.tertiaryColor),this.lineColor=q(this.background),this.textColor=q(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor=this.actorBorder,this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||G(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||"#f4f4f4",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,d,p,g,m,y,b,x,k,L,B;this.secondBkg=G(this.contrast,55),this.border2=this.contrast,this.actorBorder=G(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.actorBorder,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||q(this["cScale"+C]);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this.darkMode?this["cScalePeer"+C]=this["cScalePeer"+C]||G(this["cScale"+C],10):this["cScalePeer"+C]=this["cScalePeer"+C]||it(this["cScale"+C],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.scaleLabelColor;for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||A(this.mainBkg,{l:-(5+C*5)}),this["surfacePeer"+C]=this["surfacePeer"+C]||A(this.mainBkg,{l:-(8+C*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=G(this.contrast,30),this.sectionBkgColor2=G(this.contrast,30),this.taskBorderColor=it(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=G(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=it(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=A(this.primaryColor,{h:64}),this.fillType3=A(this.secondaryColor,{h:64}),this.fillType4=A(this.primaryColor,{h:-64}),this.fillType5=A(this.secondaryColor,{h:-64}),this.fillType6=A(this.primaryColor,{h:128}),this.fillType7=A(this.secondaryColor,{h:128});for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["pie"+C]=this["cScale"+C];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||A(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||A(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||A(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||A(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||A(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||A(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||vi(this.quadrant1Fill)?G(this.quadrant1Fill):it(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.radar={axisColor:((d=this.radar)==null?void 0:d.axisColor)||this.lineColor,axisStrokeWidth:((p=this.radar)==null?void 0:p.axisStrokeWidth)||2,axisLabelFontSize:((g=this.radar)==null?void 0:g.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((b=this.radar)==null?void 0:b.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((x=this.radar)==null?void 0:x.graticuleStrokeWidth)||1,graticuleOpacity:((k=this.radar)==null?void 0:k.graticuleOpacity)||.3,legendBoxSize:((L=this.radar)==null?void 0:L.legendBoxSize)||12,legendFontSize:((B=this.radar)==null?void 0:B.legendFontSize)||12},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=it(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||A(this.primaryColor,{h:-30}),this.git4=this.pie5||A(this.primaryColor,{h:-60}),this.git5=this.pie6||A(this.primaryColor,{h:-90}),this.git6=this.pie7||A(this.primaryColor,{h:60}),this.git7=this.pie8||A(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||q(this.git0),this.gitInv1=this.gitInv1||q(this.git1),this.gitInv2=this.gitInv2||q(this.git2),this.gitInv3=this.gitInv3||q(this.git3),this.gitInv4=this.gitInv4||q(this.git4),this.gitInv5=this.gitInv5||q(this.git5),this.gitInv6=this.gitInv6||q(this.git6),this.gitInv7=this.gitInv7||q(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ma,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Ea}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(Br,"Theme"),Br),wg=f(e=>{const t=new Cg;return t.calculate(e),t},"getThemeVariables"),Fe={base:{getThemeVariables:pg},dark:{getThemeVariables:gg},default:{getThemeVariables:yg},forest:{getThemeVariables:xg},neutral:{getThemeVariables:wg}},_e={flowchart:{useMaxWidth:!0,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1,hideEmptyMembersBox:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:!0,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,parallelCommits:!1,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},block:{useMaxWidth:!0,padding:8},packet:{useMaxWidth:!0,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:!0,paddingX:5,paddingY:5},architecture:{useMaxWidth:!0,padding:40,iconSize:80,fontSize:16},radar:{useMaxWidth:!0,width:600,height:600,marginTop:50,marginRight:50,marginBottom:50,marginLeft:50,axisScaleFactor:1,axisLabelFactor:1.05,curveTension:.17},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:!1,forceLegacyMathML:!1,deterministicIds:!1,fontSize:16,markdownAutoWrap:!0,suppressErrorRendering:!1},ic={..._e,deterministicIDSeed:void 0,elk:{mergeEdges:!1,nodePlacementStrategy:"BRANDES_KOEPF"},themeCSS:void 0,themeVariables:Fe.default.getThemeVariables(),sequence:{..._e.sequence,messageFont:f(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont"),noteFont:f(function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},"noteFont"),actorFont:f(function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}},"actorFont")},class:{hideEmptyMembersBox:!1},gantt:{..._e.gantt,tickInterval:void 0,useWidth:void 0},c4:{..._e.c4,useWidth:void 0,personFont:f(function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},"personFont"),external_personFont:f(function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},"external_personFont"),systemFont:f(function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},"systemFont"),external_systemFont:f(function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},"external_systemFont"),system_dbFont:f(function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},"system_dbFont"),external_system_dbFont:f(function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},"external_system_dbFont"),system_queueFont:f(function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},"system_queueFont"),external_system_queueFont:f(function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},"external_system_queueFont"),containerFont:f(function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},"containerFont"),external_containerFont:f(function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},"external_containerFont"),container_dbFont:f(function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},"container_dbFont"),external_container_dbFont:f(function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},"external_container_dbFont"),container_queueFont:f(function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},"container_queueFont"),external_container_queueFont:f(function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},"external_container_queueFont"),componentFont:f(function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},"componentFont"),external_componentFont:f(function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},"external_componentFont"),component_dbFont:f(function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},"component_dbFont"),external_component_dbFont:f(function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},"external_component_dbFont"),component_queueFont:f(function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},"component_queueFont"),external_component_queueFont:f(function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},"external_component_queueFont"),boundaryFont:f(function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},"boundaryFont"),messageFont:f(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont")},pie:{..._e.pie,useWidth:984},xyChart:{..._e.xyChart,useWidth:void 0},requirement:{..._e.requirement,useWidth:void 0},packet:{..._e.packet},radar:{..._e.radar}},ac=f((e,t="")=>Object.keys(e).reduce((r,i)=>Array.isArray(e[i])?r:typeof e[i]=="object"&&e[i]!==null?[...r,t+i,...ac(e[i],"")]:[...r,t+i],[]),"keyify"),kg=new Set(ac(ic,"")),nc=ic,aa=f(e=>{if(P.debug("sanitizeDirective called with",e),!(typeof e!="object"||e==null)){if(Array.isArray(e)){e.forEach(t=>aa(t));return}for(const t of Object.keys(e)){if(P.debug("Checking key",t),t.startsWith("__")||t.includes("proto")||t.includes("constr")||!kg.has(t)||e[t]==null){P.debug("sanitize deleting key: ",t),delete e[t];continue}if(typeof e[t]=="object"){P.debug("sanitizing object",t),aa(e[t]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r)t.includes(i)&&(P.debug("sanitizing css option",t),e[t]=vg(e[t]))}if(e.themeVariables)for(const t of Object.keys(e.themeVariables)){const r=e.themeVariables[t];r!=null&&r.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)&&(e.themeVariables[t]="")}P.debug("After sanitization",e)}},"sanitizeDirective"),vg=f(e=>{let t=0,r=0;for(const i of e){if(t<r)return"{ /* ERROR: Unbalanced CSS */ }";i==="{"?t++:i==="}"&&r++}return t!==r?"{ /* ERROR: Unbalanced CSS */ }":e},"sanitizeCss"),Fr=Object.freeze(nc),te=Rt({},Fr),sc,Dr=[],di=Rt({},Fr),Fa=f((e,t)=>{let r=Rt({},e),i={};for(const a of t)cc(a),i=Rt(i,a);if(r=Rt(r,i),i.theme&&i.theme in Fe){const a=Rt({},sc),n=Rt(a.themeVariables||{},i.themeVariables);r.theme&&r.theme in Fe&&(r.themeVariables=Fe[r.theme].getThemeVariables(n))}return di=r,hc(di),di},"updateCurrentConfig"),Sg=f(e=>(te=Rt({},Fr),te=Rt(te,e),e.theme&&Fe[e.theme]&&(te.themeVariables=Fe[e.theme].getThemeVariables(e.themeVariables)),Fa(te,Dr),te),"setSiteConfig"),Tg=f(e=>{sc=Rt({},e)},"saveConfigFromInitialize"),_g=f(e=>(te=Rt(te,e),Fa(te,Dr),te),"updateSiteConfig"),oc=f(()=>Rt({},te),"getSiteConfig"),lc=f(e=>(hc(e),Rt(di,e),re()),"setConfig"),re=f(()=>Rt({},di),"getConfig"),cc=f(e=>{e&&(["secure",...te.secure??[]].forEach(t=>{Object.hasOwn(e,t)&&(P.debug(`Denied attempt to modify a secure key ${t}`,e[t]),delete e[t])}),Object.keys(e).forEach(t=>{t.startsWith("__")&&delete e[t]}),Object.keys(e).forEach(t=>{typeof e[t]=="string"&&(e[t].includes("<")||e[t].includes(">")||e[t].includes("url(data:"))&&delete e[t],typeof e[t]=="object"&&cc(e[t])}))},"sanitize"),Bg=f(e=>{var t;aa(e),e.fontFamily&&!((t=e.themeVariables)!=null&&t.fontFamily)&&(e.themeVariables={...e.themeVariables,fontFamily:e.fontFamily}),Dr.push(e),Fa(te,Dr)},"addDirective"),na=f((e=te)=>{Dr=[],Fa(e,Dr)},"reset"),Lg={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},Eo={},Ag=f(e=>{Eo[e]||(P.warn(Lg[e]),Eo[e]=!0)},"issueWarning"),hc=f(e=>{e&&(e.lazyLoadedDiagrams||e.loadExternalDiagramsAtStartup)&&Ag("LAZY_LOAD_DEPRECATED")},"checkConfig"),Si=/<br\s*\/?>/gi,Mg=f(e=>e?pc(e).replace(/\\n/g,"#br#").split("#br#"):[""],"getRows"),Eg=(()=>{let e=!1;return()=>{e||(uc(),e=!0)}})();function uc(){const e="data-temp-href-target";Mr.addHook("beforeSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")??"")}),Mr.addHook("afterSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)??""),t.removeAttribute(e),t.getAttribute("target")==="_blank"&&t.setAttribute("rel","noopener"))})}f(uc,"setupDompurifyHooks");var dc=f(e=>(Eg(),Mr.sanitize(e)),"removeScript"),Fo=f((e,t)=>{var r;if(((r=t.flowchart)==null?void 0:r.htmlLabels)!==!1){const i=t.securityLevel;i==="antiscript"||i==="strict"?e=dc(e):i!=="loose"&&(e=pc(e),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;"),e=e.replace(/=/g,"&equals;"),e=$g(e))}return e},"sanitizeMore"),tr=f((e,t)=>e&&(t.dompurifyConfig?e=Mr.sanitize(Fo(e,t),t.dompurifyConfig).toString():e=Mr.sanitize(Fo(e,t),{FORBID_TAGS:["style"]}).toString(),e),"sanitizeText"),Fg=f((e,t)=>typeof e=="string"?tr(e,t):e.flat().map(r=>tr(r,t)),"sanitizeTextOrArray"),Dg=f(e=>Si.test(e),"hasBreaks"),Og=f(e=>e.split(Si),"splitBreaks"),$g=f(e=>e.replace(/#br#/g,"<br/>"),"placeholderToBreak"),pc=f(e=>e.replace(Si,"#br#"),"breakToPlaceholder"),Rg=f(e=>{let t="";return e&&(t=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,t=t.replaceAll(/\(/g,"\\("),t=t.replaceAll(/\)/g,"\\)")),t},"getUrl"),Mt=f(e=>!(e===!1||["false","null","0"].includes(String(e).trim().toLowerCase())),"evaluate"),Ig=f(function(...e){const t=e.filter(r=>!isNaN(r));return Math.max(...t)},"getMax"),Pg=f(function(...e){const t=e.filter(r=>!isNaN(r));return Math.min(...t)},"getMin"),Do=f(function(e){const t=e.split(/(,)/),r=[];for(let i=0;i<t.length;i++){let a=t[i];if(a===","&&i>0&&i+1<t.length){const n=t[i-1],o=t[i+1];Ng(n,o)&&(a=n+","+o,i++,r.pop())}r.push(zg(a))}return r.join("")},"parseGenericTypes"),Ln=f((e,t)=>Math.max(0,e.split(t).length-1),"countOccurrence"),Ng=f((e,t)=>{const r=Ln(e,"~"),i=Ln(t,"~");return r===1&&i===1},"shouldCombineSets"),zg=f(e=>{const t=Ln(e,"~");let r=!1;if(t<=1)return e;t%2!==0&&e.startsWith("~")&&(e=e.substring(1),r=!0);const i=[...e];let a=i.indexOf("~"),n=i.lastIndexOf("~");for(;a!==-1&&n!==-1&&a!==n;)i[a]="<",i[n]=">",a=i.indexOf("~"),n=i.lastIndexOf("~");return r&&i.unshift("~"),i.join("")},"processSet"),Oo=f(()=>window.MathMLElement!==void 0,"isMathMLSupported"),An=/\$\$(.*)\$\$/g,Or=f(e=>{var t;return(((t=e.match(An))==null?void 0:t.length)??0)>0},"hasKatex"),Qk=f(async(e,t)=>{e=await fs(e,t);const r=document.createElement("div");r.innerHTML=e,r.id="katex-temp",r.style.visibility="hidden",r.style.position="absolute",r.style.top="0";const i=document.querySelector("body");i==null||i.insertAdjacentElement("beforeend",r);const a={width:r.clientWidth,height:r.clientHeight};return r.remove(),a},"calculateMathMLDimensions"),fs=f(async(e,t)=>{if(!Or(e))return e;if(!(Oo()||t.legacyMathML||t.forceLegacyMathML))return e.replace(An,"MathML is unsupported in this environment.");const{default:r}=await xt(async()=>{const{default:a}=await import("./katex-ChWnQ-fc.js");return{default:a}},[],import.meta.url),i=t.forceLegacyMathML||!Oo()&&t.legacyMathML?"htmlAndMathml":"mathml";return e.split(Si).map(a=>Or(a)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${a}</div>`:`<div>${a}</div>`).join("").replace(An,(a,n)=>r.renderToString(n,{throwOnError:!0,displayMode:!0,output:i}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,""))},"renderKatex"),Nr={getRows:Mg,sanitizeText:tr,sanitizeTextOrArray:Fg,hasBreaks:Dg,splitBreaks:Og,lineBreakRegex:Si,removeScript:dc,getUrl:Rg,evaluate:Mt,getMax:Ig,getMin:Pg},Wg=f(function(e,t){for(let r of t)e.attr(r[0],r[1])},"d3Attrs"),qg=f(function(e,t,r){let i=new Map;return r?(i.set("width","100%"),i.set("style",`max-width: ${t}px;`)):(i.set("height",e),i.set("width",t)),i},"calculateSvgSizeAttrs"),fc=f(function(e,t,r,i){const a=qg(t,r,i);Wg(e,a)},"configureSvgSize"),jg=f(function(e,t,r,i){const a=t.node().getBBox(),n=a.width,o=a.height;P.info(`SVG bounds: ${n}x${o}`,a);let s=0,c=0;P.info(`Graph bounds: ${s}x${c}`,e),s=n+r*2,c=o+r*2,P.info(`Calculated bounds: ${s}x${c}`),fc(t,c,s,i);const l=`${a.x-r} ${a.y-r} ${a.width+2*r} ${a.height+2*r}`;t.attr("viewBox",l)},"setupGraphViewbox"),Xi={},Hg=f((e,t,r)=>{let i="";return e in Xi&&Xi[e]?i=Xi[e](r):P.warn(`No theme found for ${e}`),` & {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
    fill: ${r.textColor}
  }
  @keyframes edge-animation-frame {
    from {
      stroke-dashoffset: 0;
    }
  }
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }
  & .edge-animation-slow {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 50s linear infinite;
    stroke-linecap: round;
  }
  & .edge-animation-fast {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 20s linear infinite;
    stroke-linecap: round;
  }
  /* Classes common for multiple diagrams */

  & .error-icon {
    fill: ${r.errorBkgColor};
  }
  & .error-text {
    fill: ${r.errorTextColor};
    stroke: ${r.errorTextColor};
  }

  & .edge-thickness-normal {
    stroke-width: 1px;
  }
  & .edge-thickness-thick {
    stroke-width: 3.5px
  }
  & .edge-pattern-solid {
    stroke-dasharray: 0;
  }
  & .edge-thickness-invisible {
    stroke-width: 0;
    fill: none;
  }
  & .edge-pattern-dashed{
    stroke-dasharray: 3;
  }
  .edge-pattern-dotted {
    stroke-dasharray: 2;
  }

  & .marker {
    fill: ${r.lineColor};
    stroke: ${r.lineColor};
  }
  & .marker.cross {
    stroke: ${r.lineColor};
  }

  & svg {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
  }
   & p {
    margin: 0
   }

  ${i}

  ${t}
`},"getStyles"),Ug=f((e,t)=>{t!==void 0&&(Xi[e]=t)},"addStylesForDiagram"),Gg=Hg,gc={};cg(gc,{clear:()=>Yg,getAccDescription:()=>Kg,getAccTitle:()=>Xg,getDiagramTitle:()=>Jg,setAccDescription:()=>Zg,setAccTitle:()=>Vg,setDiagramTitle:()=>Qg});var gs="",ms="",ys="",bs=f(e=>tr(e,re()),"sanitizeText"),Yg=f(()=>{gs="",ys="",ms=""},"clear"),Vg=f(e=>{gs=bs(e).replace(/^\s+/g,"")},"setAccTitle"),Xg=f(()=>gs,"getAccTitle"),Zg=f(e=>{ys=bs(e).replace(/\n\s+/g,`
`)},"setAccDescription"),Kg=f(()=>ys,"getAccDescription"),Qg=f(e=>{ms=bs(e)},"setDiagramTitle"),Jg=f(()=>ms,"getDiagramTitle"),$o=P,tm=ds,mt=re,Jk=lc,tv=Fr,xs=f(e=>tr(e,mt()),"sanitizeText"),em=jg,rm=f(()=>gc,"getCommonDb"),sa={},oa=f((e,t,r)=>{var i;sa[e]&&$o.warn(`Diagram with id ${e} already registered. Overwriting.`),sa[e]=t,r&&rc(e,r),Ug(e,t.styles),(i=t.injectUtils)==null||i.call(t,$o,tm,mt,xs,em,rm(),()=>{})},"registerDiagram"),Mn=f(e=>{if(e in sa)return sa[e];throw new im(e)},"getDiagram"),Lr,im=(Lr=class extends Error{constructor(t){super(`Diagram ${t} not found.`)}},f(Lr,"DiagramNotFoundError"),Lr);function Cs(e){return typeof e>"u"||e===null}f(Cs,"isNothing");function mc(e){return typeof e=="object"&&e!==null}f(mc,"isObject");function yc(e){return Array.isArray(e)?e:Cs(e)?[]:[e]}f(yc,"toArray");function bc(e,t){var r,i,a,n;if(t)for(n=Object.keys(t),r=0,i=n.length;r<i;r+=1)a=n[r],e[a]=t[a];return e}f(bc,"extend");function xc(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}f(xc,"repeat");function Cc(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}f(Cc,"isNegativeZero");var am=Cs,nm=mc,sm=yc,om=xc,lm=Cc,cm=bc,At={isNothing:am,isObject:nm,toArray:sm,repeat:om,isNegativeZero:lm,extend:cm};function ws(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}f(ws,"formatError");function $r(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=ws(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}f($r,"YAMLException$1");$r.prototype=Object.create(Error.prototype);$r.prototype.constructor=$r;$r.prototype.toString=f(function(t){return this.name+": "+ws(this,t)},"toString");var ee=$r;function Zi(e,t,r,i,a){var n="",o="",s=Math.floor(a/2)-1;return i-t>s&&(n=" ... ",t=i-s+n.length),r-i>s&&(o=" ...",r=i+s-o.length),{str:n+e.slice(t,r).replace(/\t/g,"→")+o,pos:i-t+n.length}}f(Zi,"getLine");function Ki(e,t){return At.repeat(" ",t-e.length)+e}f(Ki,"padStart");function wc(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],a=[],n,o=-1;n=r.exec(e.buffer);)a.push(n.index),i.push(n.index+n[0].length),e.position<=n.index&&o<0&&(o=i.length-2);o<0&&(o=i.length-1);var s="",c,l,h=Math.min(e.line+t.linesAfter,a.length).toString().length,u=t.maxLength-(t.indent+h+3);for(c=1;c<=t.linesBefore&&!(o-c<0);c++)l=Zi(e.buffer,i[o-c],a[o-c],e.position-(i[o]-i[o-c]),u),s=At.repeat(" ",t.indent)+Ki((e.line-c+1).toString(),h)+" | "+l.str+`
`+s;for(l=Zi(e.buffer,i[o],a[o],e.position,u),s+=At.repeat(" ",t.indent)+Ki((e.line+1).toString(),h)+" | "+l.str+`
`,s+=At.repeat("-",t.indent+h+3+l.pos)+`^
`,c=1;c<=t.linesAfter&&!(o+c>=a.length);c++)l=Zi(e.buffer,i[o+c],a[o+c],e.position-(i[o]-i[o+c]),u),s+=At.repeat(" ",t.indent)+Ki((e.line+c+1).toString(),h)+" | "+l.str+`
`;return s.replace(/\n$/,"")}f(wc,"makeSnippet");var hm=wc,um=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],dm=["scalar","sequence","mapping"];function kc(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}f(kc,"compileStyleAliases");function vc(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(um.indexOf(r)===-1)throw new ee('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=kc(t.styleAliases||null),dm.indexOf(this.kind)===-1)throw new ee('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}f(vc,"Type$1");var qt=vc;function En(e,t){var r=[];return e[t].forEach(function(i){var a=r.length;r.forEach(function(n,o){n.tag===i.tag&&n.kind===i.kind&&n.multi===i.multi&&(a=o)}),r[a]=i}),r}f(En,"compileList");function Sc(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(a){a.multi?(e.multi[a.kind].push(a),e.multi.fallback.push(a)):e[a.kind][a.tag]=e.fallback[a.tag]=a}for(f(i,"collectType"),t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}f(Sc,"compileMap");function la(e){return this.extend(e)}f(la,"Schema$1");la.prototype.extend=f(function(t){var r=[],i=[];if(t instanceof qt)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new ee("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(n){if(!(n instanceof qt))throw new ee("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(n.loadKind&&n.loadKind!=="scalar")throw new ee("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(n.multi)throw new ee("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(n){if(!(n instanceof qt))throw new ee("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var a=Object.create(la.prototype);return a.implicit=(this.implicit||[]).concat(r),a.explicit=(this.explicit||[]).concat(i),a.compiledImplicit=En(a,"implicit"),a.compiledExplicit=En(a,"explicit"),a.compiledTypeMap=Sc(a.compiledImplicit,a.compiledExplicit),a},"extend");var pm=la,fm=new qt("tag:yaml.org,2002:str",{kind:"scalar",construct:f(function(e){return e!==null?e:""},"construct")}),gm=new qt("tag:yaml.org,2002:seq",{kind:"sequence",construct:f(function(e){return e!==null?e:[]},"construct")}),mm=new qt("tag:yaml.org,2002:map",{kind:"mapping",construct:f(function(e){return e!==null?e:{}},"construct")}),ym=new pm({explicit:[fm,gm,mm]});function Tc(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}f(Tc,"resolveYamlNull");function _c(){return null}f(_c,"constructYamlNull");function Bc(e){return e===null}f(Bc,"isNull");var bm=new qt("tag:yaml.org,2002:null",{kind:"scalar",resolve:Tc,construct:_c,predicate:Bc,represent:{canonical:f(function(){return"~"},"canonical"),lowercase:f(function(){return"null"},"lowercase"),uppercase:f(function(){return"NULL"},"uppercase"),camelcase:f(function(){return"Null"},"camelcase"),empty:f(function(){return""},"empty")},defaultStyle:"lowercase"});function Lc(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}f(Lc,"resolveYamlBoolean");function Ac(e){return e==="true"||e==="True"||e==="TRUE"}f(Ac,"constructYamlBoolean");function Mc(e){return Object.prototype.toString.call(e)==="[object Boolean]"}f(Mc,"isBoolean");var xm=new qt("tag:yaml.org,2002:bool",{kind:"scalar",resolve:Lc,construct:Ac,predicate:Mc,represent:{lowercase:f(function(e){return e?"true":"false"},"lowercase"),uppercase:f(function(e){return e?"TRUE":"FALSE"},"uppercase"),camelcase:f(function(e){return e?"True":"False"},"camelcase")},defaultStyle:"lowercase"});function Ec(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}f(Ec,"isHexCode");function Fc(e){return 48<=e&&e<=55}f(Fc,"isOctCode");function Dc(e){return 48<=e&&e<=57}f(Dc,"isDecCode");function Oc(e){if(e===null)return!1;var t=e.length,r=0,i=!1,a;if(!t)return!1;if(a=e[r],(a==="-"||a==="+")&&(a=e[++r]),a==="0"){if(r+1===t)return!0;if(a=e[++r],a==="b"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(a!=="0"&&a!=="1")return!1;i=!0}return i&&a!=="_"}if(a==="x"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!Ec(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}if(a==="o"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!Fc(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}}if(a==="_")return!1;for(;r<t;r++)if(a=e[r],a!=="_"){if(!Dc(e.charCodeAt(r)))return!1;i=!0}return!(!i||a==="_")}f(Oc,"resolveYamlInteger");function $c(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}f($c,"constructYamlInteger");function Rc(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!At.isNegativeZero(e)}f(Rc,"isInteger");var Cm=new qt("tag:yaml.org,2002:int",{kind:"scalar",resolve:Oc,construct:$c,predicate:Rc,represent:{binary:f(function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},"binary"),octal:f(function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},"octal"),decimal:f(function(e){return e.toString(10)},"decimal"),hexadecimal:f(function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)},"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),wm=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Ic(e){return!(e===null||!wm.test(e)||e[e.length-1]==="_")}f(Ic,"resolveYamlFloat");function Pc(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}f(Pc,"constructYamlFloat");var km=/^[-+]?[0-9]+e/;function Nc(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(At.isNegativeZero(e))return"-0.0";return r=e.toString(10),km.test(r)?r.replace("e",".e"):r}f(Nc,"representYamlFloat");function zc(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||At.isNegativeZero(e))}f(zc,"isFloat");var vm=new qt("tag:yaml.org,2002:float",{kind:"scalar",resolve:Ic,construct:Pc,predicate:zc,represent:Nc,defaultStyle:"lowercase"}),Wc=ym.extend({implicit:[bm,xm,Cm,vm]}),Sm=Wc,qc=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),jc=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Hc(e){return e===null?!1:qc.exec(e)!==null||jc.exec(e)!==null}f(Hc,"resolveYamlTimestamp");function Uc(e){var t,r,i,a,n,o,s,c=0,l=null,h,u,d;if(t=qc.exec(e),t===null&&(t=jc.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,a=+t[3],!t[4])return new Date(Date.UTC(r,i,a));if(n=+t[4],o=+t[5],s=+t[6],t[7]){for(c=t[7].slice(0,3);c.length<3;)c+="0";c=+c}return t[9]&&(h=+t[10],u=+(t[11]||0),l=(h*60+u)*6e4,t[9]==="-"&&(l=-l)),d=new Date(Date.UTC(r,i,a,n,o,s,c)),l&&d.setTime(d.getTime()-l),d}f(Uc,"constructYamlTimestamp");function Gc(e){return e.toISOString()}f(Gc,"representYamlTimestamp");var Tm=new qt("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Hc,construct:Uc,instanceOf:Date,represent:Gc});function Yc(e){return e==="<<"||e===null}f(Yc,"resolveYamlMerge");var _m=new qt("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Yc}),ks=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function Vc(e){if(e===null)return!1;var t,r,i=0,a=e.length,n=ks;for(r=0;r<a;r++)if(t=n.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}f(Vc,"resolveYamlBinary");function Xc(e){var t,r,i=e.replace(/[\r\n=]/g,""),a=i.length,n=ks,o=0,s=[];for(t=0;t<a;t++)t%4===0&&t&&(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)),o=o<<6|n.indexOf(i.charAt(t));return r=a%4*6,r===0?(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)):r===18?(s.push(o>>10&255),s.push(o>>2&255)):r===12&&s.push(o>>4&255),new Uint8Array(s)}f(Xc,"constructYamlBinary");function Zc(e){var t="",r=0,i,a,n=e.length,o=ks;for(i=0;i<n;i++)i%3===0&&i&&(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]),r=(r<<8)+e[i];return a=n%3,a===0?(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]):a===2?(t+=o[r>>10&63],t+=o[r>>4&63],t+=o[r<<2&63],t+=o[64]):a===1&&(t+=o[r>>2&63],t+=o[r<<4&63],t+=o[64],t+=o[64]),t}f(Zc,"representYamlBinary");function Kc(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}f(Kc,"isBinary");var Bm=new qt("tag:yaml.org,2002:binary",{kind:"scalar",resolve:Vc,construct:Xc,predicate:Kc,represent:Zc}),Lm=Object.prototype.hasOwnProperty,Am=Object.prototype.toString;function Qc(e){if(e===null)return!0;var t=[],r,i,a,n,o,s=e;for(r=0,i=s.length;r<i;r+=1){if(a=s[r],o=!1,Am.call(a)!=="[object Object]")return!1;for(n in a)if(Lm.call(a,n))if(!o)o=!0;else return!1;if(!o)return!1;if(t.indexOf(n)===-1)t.push(n);else return!1}return!0}f(Qc,"resolveYamlOmap");function Jc(e){return e!==null?e:[]}f(Jc,"constructYamlOmap");var Mm=new qt("tag:yaml.org,2002:omap",{kind:"sequence",resolve:Qc,construct:Jc}),Em=Object.prototype.toString;function th(e){if(e===null)return!0;var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1){if(i=o[t],Em.call(i)!=="[object Object]"||(a=Object.keys(i),a.length!==1))return!1;n[t]=[a[0],i[a[0]]]}return!0}f(th,"resolveYamlPairs");function eh(e){if(e===null)return[];var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1)i=o[t],a=Object.keys(i),n[t]=[a[0],i[a[0]]];return n}f(eh,"constructYamlPairs");var Fm=new qt("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:th,construct:eh}),Dm=Object.prototype.hasOwnProperty;function rh(e){if(e===null)return!0;var t,r=e;for(t in r)if(Dm.call(r,t)&&r[t]!==null)return!1;return!0}f(rh,"resolveYamlSet");function ih(e){return e!==null?e:{}}f(ih,"constructYamlSet");var Om=new qt("tag:yaml.org,2002:set",{kind:"mapping",resolve:rh,construct:ih}),ah=Sm.extend({implicit:[Tm,_m],explicit:[Bm,Mm,Fm,Om]}),We=Object.prototype.hasOwnProperty,ca=1,nh=2,sh=3,ha=4,un=1,$m=2,Ro=3,Rm=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Im=/[\x85\u2028\u2029]/,Pm=/[,\[\]\{\}]/,oh=/^(?:!|!!|![a-z\-]+!)$/i,lh=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Fn(e){return Object.prototype.toString.call(e)}f(Fn,"_class");function he(e){return e===10||e===13}f(he,"is_EOL");function ze(e){return e===9||e===32}f(ze,"is_WHITE_SPACE");function Yt(e){return e===9||e===32||e===10||e===13}f(Yt,"is_WS_OR_EOL");function Ze(e){return e===44||e===91||e===93||e===123||e===125}f(Ze,"is_FLOW_INDICATOR");function ch(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}f(ch,"fromHexCode");function hh(e){return e===120?2:e===117?4:e===85?8:0}f(hh,"escapedHexLen");function uh(e){return 48<=e&&e<=57?e-48:-1}f(uh,"fromDecimalCode");function Dn(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"":e===95?" ":e===76?"\u2028":e===80?"\u2029":""}f(Dn,"simpleEscapeSequence");function dh(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}f(dh,"charFromCodepoint");var ph=new Array(256),fh=new Array(256);for(Ge=0;Ge<256;Ge++)ph[Ge]=Dn(Ge)?1:0,fh[Ge]=Dn(Ge);var Ge;function gh(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||ah,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}f(gh,"State$1");function vs(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=hm(r),new ee(t,r)}f(vs,"generateError");function et(e,t){throw vs(e,t)}f(et,"throwError");function gi(e,t){e.onWarning&&e.onWarning.call(null,vs(e,t))}f(gi,"throwWarning");var Io={YAML:f(function(t,r,i){var a,n,o;t.version!==null&&et(t,"duplication of %YAML directive"),i.length!==1&&et(t,"YAML directive accepts exactly one argument"),a=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),a===null&&et(t,"ill-formed argument of the YAML directive"),n=parseInt(a[1],10),o=parseInt(a[2],10),n!==1&&et(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,o!==1&&o!==2&&gi(t,"unsupported YAML version of the document")},"handleYamlDirective"),TAG:f(function(t,r,i){var a,n;i.length!==2&&et(t,"TAG directive accepts exactly two arguments"),a=i[0],n=i[1],oh.test(a)||et(t,"ill-formed tag handle (first argument) of the TAG directive"),We.call(t.tagMap,a)&&et(t,'there is a previously declared suffix for "'+a+'" tag handle'),lh.test(n)||et(t,"ill-formed tag prefix (second argument) of the TAG directive");try{n=decodeURIComponent(n)}catch{et(t,"tag prefix is malformed: "+n)}t.tagMap[a]=n},"handleTagDirective")};function De(e,t,r,i){var a,n,o,s;if(t<r){if(s=e.input.slice(t,r),i)for(a=0,n=s.length;a<n;a+=1)o=s.charCodeAt(a),o===9||32<=o&&o<=1114111||et(e,"expected valid JSON character");else Rm.test(s)&&et(e,"the stream contains non-printable characters");e.result+=s}}f(De,"captureSegment");function On(e,t,r,i){var a,n,o,s;for(At.isObject(r)||et(e,"cannot merge mappings; the provided source object is unacceptable"),a=Object.keys(r),o=0,s=a.length;o<s;o+=1)n=a[o],We.call(t,n)||(t[n]=r[n],i[n]=!0)}f(On,"mergeMappings");function Ke(e,t,r,i,a,n,o,s,c){var l,h;if(Array.isArray(a))for(a=Array.prototype.slice.call(a),l=0,h=a.length;l<h;l+=1)Array.isArray(a[l])&&et(e,"nested arrays are not supported inside keys"),typeof a=="object"&&Fn(a[l])==="[object Object]"&&(a[l]="[object Object]");if(typeof a=="object"&&Fn(a)==="[object Object]"&&(a="[object Object]"),a=String(a),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(n))for(l=0,h=n.length;l<h;l+=1)On(e,t,n[l],r);else On(e,t,n,r);else!e.json&&!We.call(r,a)&&We.call(t,a)&&(e.line=o||e.line,e.lineStart=s||e.lineStart,e.position=c||e.position,et(e,"duplicated mapping key")),a==="__proto__"?Object.defineProperty(t,a,{configurable:!0,enumerable:!0,writable:!0,value:n}):t[a]=n,delete r[a];return t}f(Ke,"storeMappingPair");function Da(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):et(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}f(Da,"readLineBreak");function Tt(e,t,r){for(var i=0,a=e.input.charCodeAt(e.position);a!==0;){for(;ze(a);)a===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),a=e.input.charCodeAt(++e.position);if(t&&a===35)do a=e.input.charCodeAt(++e.position);while(a!==10&&a!==13&&a!==0);if(he(a))for(Da(e),a=e.input.charCodeAt(e.position),i++,e.lineIndent=0;a===32;)e.lineIndent++,a=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&gi(e,"deficient indentation"),i}f(Tt,"skipSeparationSpace");function Ti(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||Yt(r)))}f(Ti,"testDocumentSeparator");function Oa(e,t){t===1?e.result+=" ":t>1&&(e.result+=At.repeat(`
`,t-1))}f(Oa,"writeFoldedLines");function mh(e,t,r){var i,a,n,o,s,c,l,h,u=e.kind,d=e.result,p;if(p=e.input.charCodeAt(e.position),Yt(p)||Ze(p)||p===35||p===38||p===42||p===33||p===124||p===62||p===39||p===34||p===37||p===64||p===96||(p===63||p===45)&&(a=e.input.charCodeAt(e.position+1),Yt(a)||r&&Ze(a)))return!1;for(e.kind="scalar",e.result="",n=o=e.position,s=!1;p!==0;){if(p===58){if(a=e.input.charCodeAt(e.position+1),Yt(a)||r&&Ze(a))break}else if(p===35){if(i=e.input.charCodeAt(e.position-1),Yt(i))break}else{if(e.position===e.lineStart&&Ti(e)||r&&Ze(p))break;if(he(p))if(c=e.line,l=e.lineStart,h=e.lineIndent,Tt(e,!1,-1),e.lineIndent>=t){s=!0,p=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=c,e.lineStart=l,e.lineIndent=h;break}}s&&(De(e,n,o,!1),Oa(e,e.line-c),n=o=e.position,s=!1),ze(p)||(o=e.position+1),p=e.input.charCodeAt(++e.position)}return De(e,n,o,!1),e.result?!0:(e.kind=u,e.result=d,!1)}f(mh,"readPlainScalar");function yh(e,t){var r,i,a;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=a=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(De(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,a=e.position;else return!0;else he(r)?(De(e,i,a,!0),Oa(e,Tt(e,!1,t)),i=a=e.position):e.position===e.lineStart&&Ti(e)?et(e,"unexpected end of the document within a single quoted scalar"):(e.position++,a=e.position);et(e,"unexpected end of the stream within a single quoted scalar")}f(yh,"readSingleQuotedScalar");function bh(e,t){var r,i,a,n,o,s;if(s=e.input.charCodeAt(e.position),s!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(s=e.input.charCodeAt(e.position))!==0;){if(s===34)return De(e,r,e.position,!0),e.position++,!0;if(s===92){if(De(e,r,e.position,!0),s=e.input.charCodeAt(++e.position),he(s))Tt(e,!1,t);else if(s<256&&ph[s])e.result+=fh[s],e.position++;else if((o=hh(s))>0){for(a=o,n=0;a>0;a--)s=e.input.charCodeAt(++e.position),(o=ch(s))>=0?n=(n<<4)+o:et(e,"expected hexadecimal character");e.result+=dh(n),e.position++}else et(e,"unknown escape sequence");r=i=e.position}else he(s)?(De(e,r,i,!0),Oa(e,Tt(e,!1,t)),r=i=e.position):e.position===e.lineStart&&Ti(e)?et(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}et(e,"unexpected end of the stream within a double quoted scalar")}f(bh,"readDoubleQuotedScalar");function xh(e,t){var r=!0,i,a,n,o=e.tag,s,c=e.anchor,l,h,u,d,p,g=Object.create(null),m,y,b,x;if(x=e.input.charCodeAt(e.position),x===91)h=93,p=!1,s=[];else if(x===123)h=125,p=!0,s={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=s),x=e.input.charCodeAt(++e.position);x!==0;){if(Tt(e,!0,t),x=e.input.charCodeAt(e.position),x===h)return e.position++,e.tag=o,e.anchor=c,e.kind=p?"mapping":"sequence",e.result=s,!0;r?x===44&&et(e,"expected the node content, but found ','"):et(e,"missed comma between flow collection entries"),y=m=b=null,u=d=!1,x===63&&(l=e.input.charCodeAt(e.position+1),Yt(l)&&(u=d=!0,e.position++,Tt(e,!0,t))),i=e.line,a=e.lineStart,n=e.position,er(e,t,ca,!1,!0),y=e.tag,m=e.result,Tt(e,!0,t),x=e.input.charCodeAt(e.position),(d||e.line===i)&&x===58&&(u=!0,x=e.input.charCodeAt(++e.position),Tt(e,!0,t),er(e,t,ca,!1,!0),b=e.result),p?Ke(e,s,g,y,m,b,i,a,n):u?s.push(Ke(e,null,g,y,m,b,i,a,n)):s.push(m),Tt(e,!0,t),x=e.input.charCodeAt(e.position),x===44?(r=!0,x=e.input.charCodeAt(++e.position)):r=!1}et(e,"unexpected end of the stream within a flow collection")}f(xh,"readFlowCollection");function Ch(e,t){var r,i,a=un,n=!1,o=!1,s=t,c=0,l=!1,h,u;if(u=e.input.charCodeAt(e.position),u===124)i=!1;else if(u===62)i=!0;else return!1;for(e.kind="scalar",e.result="";u!==0;)if(u=e.input.charCodeAt(++e.position),u===43||u===45)un===a?a=u===43?Ro:$m:et(e,"repeat of a chomping mode identifier");else if((h=uh(u))>=0)h===0?et(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?et(e,"repeat of an indentation width identifier"):(s=t+h-1,o=!0);else break;if(ze(u)){do u=e.input.charCodeAt(++e.position);while(ze(u));if(u===35)do u=e.input.charCodeAt(++e.position);while(!he(u)&&u!==0)}for(;u!==0;){for(Da(e),e.lineIndent=0,u=e.input.charCodeAt(e.position);(!o||e.lineIndent<s)&&u===32;)e.lineIndent++,u=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>s&&(s=e.lineIndent),he(u)){c++;continue}if(e.lineIndent<s){a===Ro?e.result+=At.repeat(`
`,n?1+c:c):a===un&&n&&(e.result+=`
`);break}for(i?ze(u)?(l=!0,e.result+=At.repeat(`
`,n?1+c:c)):l?(l=!1,e.result+=At.repeat(`
`,c+1)):c===0?n&&(e.result+=" "):e.result+=At.repeat(`
`,c):e.result+=At.repeat(`
`,n?1+c:c),n=!0,o=!0,c=0,r=e.position;!he(u)&&u!==0;)u=e.input.charCodeAt(++e.position);De(e,r,e.position,!1)}return!0}f(Ch,"readBlockScalar");function $n(e,t){var r,i=e.tag,a=e.anchor,n=[],o,s=!1,c;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=n),c=e.input.charCodeAt(e.position);c!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,et(e,"tab characters must not be used in indentation")),!(c!==45||(o=e.input.charCodeAt(e.position+1),!Yt(o))));){if(s=!0,e.position++,Tt(e,!0,-1)&&e.lineIndent<=t){n.push(null),c=e.input.charCodeAt(e.position);continue}if(r=e.line,er(e,t,sh,!1,!0),n.push(e.result),Tt(e,!0,-1),c=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&c!==0)et(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return s?(e.tag=i,e.anchor=a,e.kind="sequence",e.result=n,!0):!1}f($n,"readBlockSequence");function wh(e,t,r){var i,a,n,o,s,c,l=e.tag,h=e.anchor,u={},d=Object.create(null),p=null,g=null,m=null,y=!1,b=!1,x;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),x=e.input.charCodeAt(e.position);x!==0;){if(!y&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,et(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),n=e.line,(x===63||x===58)&&Yt(i))x===63?(y&&(Ke(e,u,d,p,g,null,o,s,c),p=g=m=null),b=!0,y=!0,a=!0):y?(y=!1,a=!0):et(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,x=i;else{if(o=e.line,s=e.lineStart,c=e.position,!er(e,r,nh,!1,!0))break;if(e.line===n){for(x=e.input.charCodeAt(e.position);ze(x);)x=e.input.charCodeAt(++e.position);if(x===58)x=e.input.charCodeAt(++e.position),Yt(x)||et(e,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(Ke(e,u,d,p,g,null,o,s,c),p=g=m=null),b=!0,y=!1,a=!1,p=e.tag,g=e.result;else if(b)et(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=l,e.anchor=h,!0}else if(b)et(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=l,e.anchor=h,!0}if((e.line===n||e.lineIndent>t)&&(y&&(o=e.line,s=e.lineStart,c=e.position),er(e,t,ha,!0,a)&&(y?g=e.result:m=e.result),y||(Ke(e,u,d,p,g,m,o,s,c),p=g=m=null),Tt(e,!0,-1),x=e.input.charCodeAt(e.position)),(e.line===n||e.lineIndent>t)&&x!==0)et(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return y&&Ke(e,u,d,p,g,null,o,s,c),b&&(e.tag=l,e.anchor=h,e.kind="mapping",e.result=u),b}f(wh,"readBlockMapping");function kh(e){var t,r=!1,i=!1,a,n,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&et(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(r=!0,o=e.input.charCodeAt(++e.position)):o===33?(i=!0,a="!!",o=e.input.charCodeAt(++e.position)):a="!",t=e.position,r){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(n=e.input.slice(t,e.position),o=e.input.charCodeAt(++e.position)):et(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!Yt(o);)o===33&&(i?et(e,"tag suffix cannot contain exclamation marks"):(a=e.input.slice(t-1,e.position+1),oh.test(a)||et(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),o=e.input.charCodeAt(++e.position);n=e.input.slice(t,e.position),Pm.test(n)&&et(e,"tag suffix cannot contain flow indicator characters")}n&&!lh.test(n)&&et(e,"tag name cannot contain such characters: "+n);try{n=decodeURIComponent(n)}catch{et(e,"tag name is malformed: "+n)}return r?e.tag=n:We.call(e.tagMap,a)?e.tag=e.tagMap[a]+n:a==="!"?e.tag="!"+n:a==="!!"?e.tag="tag:yaml.org,2002:"+n:et(e,'undeclared tag handle "'+a+'"'),!0}f(kh,"readTagProperty");function vh(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&et(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!Yt(r)&&!Ze(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&et(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}f(vh,"readAnchorProperty");function Sh(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!Yt(i)&&!Ze(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&et(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),We.call(e.anchorMap,r)||et(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],Tt(e,!0,-1),!0}f(Sh,"readAlias");function er(e,t,r,i,a){var n,o,s,c=1,l=!1,h=!1,u,d,p,g,m,y;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,n=o=s=ha===r||sh===r,i&&Tt(e,!0,-1)&&(l=!0,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)),c===1)for(;kh(e)||vh(e);)Tt(e,!0,-1)?(l=!0,s=n,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)):s=!1;if(s&&(s=l||a),(c===1||ha===r)&&(ca===r||nh===r?m=t:m=t+1,y=e.position-e.lineStart,c===1?s&&($n(e,y)||wh(e,y,m))||xh(e,m)?h=!0:(o&&Ch(e,m)||yh(e,m)||bh(e,m)?h=!0:Sh(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&et(e,"alias node should not have any properties")):mh(e,m,ca===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):c===0&&(h=s&&$n(e,y))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&et(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,d=e.implicitTypes.length;u<d;u+=1)if(g=e.implicitTypes[u],g.resolve(e.result)){e.result=g.construct(e.result),e.tag=g.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(We.call(e.typeMap[e.kind||"fallback"],e.tag))g=e.typeMap[e.kind||"fallback"][e.tag];else for(g=null,p=e.typeMap.multi[e.kind||"fallback"],u=0,d=p.length;u<d;u+=1)if(e.tag.slice(0,p[u].tag.length)===p[u].tag){g=p[u];break}g||et(e,"unknown tag !<"+e.tag+">"),e.result!==null&&g.kind!==e.kind&&et(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+g.kind+'", not "'+e.kind+'"'),g.resolve(e.result,e.tag)?(e.result=g.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):et(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}f(er,"composeNode");function Th(e){var t=e.position,r,i,a,n=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(Tt(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(n=!0,o=e.input.charCodeAt(++e.position),r=e.position;o!==0&&!Yt(o);)o=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),a=[],i.length<1&&et(e,"directive name must not be less than one character in length");o!==0;){for(;ze(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!he(o));break}if(he(o))break;for(r=e.position;o!==0&&!Yt(o);)o=e.input.charCodeAt(++e.position);a.push(e.input.slice(r,e.position))}o!==0&&Da(e),We.call(Io,i)?Io[i](e,i,a):gi(e,'unknown document directive "'+i+'"')}if(Tt(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,Tt(e,!0,-1)):n&&et(e,"directives end mark is expected"),er(e,e.lineIndent-1,ha,!1,!0),Tt(e,!0,-1),e.checkLineBreaks&&Im.test(e.input.slice(t,e.position))&&gi(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Ti(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,Tt(e,!0,-1));return}if(e.position<e.length-1)et(e,"end of the stream or a document separator is expected");else return}f(Th,"readDocument");function Ss(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new gh(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,et(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)Th(r);return r.documents}f(Ss,"loadDocuments");function Nm(e,t,r){t!==null&&typeof t=="object"&&typeof r>"u"&&(r=t,t=null);var i=Ss(e,r);if(typeof t!="function")return i;for(var a=0,n=i.length;a<n;a+=1)t(i[a])}f(Nm,"loadAll$1");function _h(e,t){var r=Ss(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new ee("expected a single document in the stream, but found more")}}f(_h,"load$1");var zm=_h,Wm={load:zm},Bh=Object.prototype.toString,Lh=Object.prototype.hasOwnProperty,Ts=65279,qm=9,mi=10,jm=13,Hm=32,Um=33,Gm=34,Rn=35,Ym=37,Vm=38,Xm=39,Zm=42,Ah=44,Km=45,ua=58,Qm=61,Jm=62,ty=63,ey=64,Mh=91,Eh=93,ry=96,Fh=123,iy=124,Dh=125,jt={};jt[0]="\\0";jt[7]="\\a";jt[8]="\\b";jt[9]="\\t";jt[10]="\\n";jt[11]="\\v";jt[12]="\\f";jt[13]="\\r";jt[27]="\\e";jt[34]='\\"';jt[92]="\\\\";jt[133]="\\N";jt[160]="\\_";jt[8232]="\\L";jt[8233]="\\P";var ay=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],ny=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Oh(e,t){var r,i,a,n,o,s,c;if(t===null)return{};for(r={},i=Object.keys(t),a=0,n=i.length;a<n;a+=1)o=i[a],s=String(t[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),c=e.compiledTypeMap.fallback[o],c&&Lh.call(c.styleAliases,s)&&(s=c.styleAliases[s]),r[o]=s;return r}f(Oh,"compileStyleMap");function $h(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new ee("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+At.repeat("0",i-t.length)+t}f($h,"encodeHex");var sy=1,yi=2;function Rh(e){this.schema=e.schema||ah,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=At.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Oh(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?yi:sy,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}f(Rh,"State");function In(e,t){for(var r=At.repeat(" ",t),i=0,a=-1,n="",o,s=e.length;i<s;)a=e.indexOf(`
`,i),a===-1?(o=e.slice(i),i=s):(o=e.slice(i,a+1),i=a+1),o.length&&o!==`
`&&(n+=r),n+=o;return n}f(In,"indentString");function da(e,t){return`
`+At.repeat(" ",e.indent*t)}f(da,"generateNextLine");function Ih(e,t){var r,i,a;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(a=e.implicitTypes[r],a.resolve(t))return!0;return!1}f(Ih,"testImplicitResolving");function bi(e){return e===Hm||e===qm}f(bi,"isWhitespace");function Rr(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Ts||65536<=e&&e<=1114111}f(Rr,"isPrintable");function Pn(e){return Rr(e)&&e!==Ts&&e!==jm&&e!==mi}f(Pn,"isNsCharOrWhitespace");function Nn(e,t,r){var i=Pn(e),a=i&&!bi(e);return(r?i:i&&e!==Ah&&e!==Mh&&e!==Eh&&e!==Fh&&e!==Dh)&&e!==Rn&&!(t===ua&&!a)||Pn(t)&&!bi(t)&&e===Rn||t===ua&&a}f(Nn,"isPlainSafe");function Ph(e){return Rr(e)&&e!==Ts&&!bi(e)&&e!==Km&&e!==ty&&e!==ua&&e!==Ah&&e!==Mh&&e!==Eh&&e!==Fh&&e!==Dh&&e!==Rn&&e!==Vm&&e!==Zm&&e!==Um&&e!==iy&&e!==Qm&&e!==Jm&&e!==Xm&&e!==Gm&&e!==Ym&&e!==ey&&e!==ry}f(Ph,"isPlainSafeFirst");function Nh(e){return!bi(e)&&e!==ua}f(Nh,"isPlainSafeLast");function xr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}f(xr,"codePointAt");function _s(e){var t=/^\n* /;return t.test(e)}f(_s,"needIndentIndicator");var zh=1,zn=2,Wh=3,qh=4,yr=5;function jh(e,t,r,i,a,n,o,s){var c,l=0,h=null,u=!1,d=!1,p=i!==-1,g=-1,m=Ph(xr(e,0))&&Nh(xr(e,e.length-1));if(t||o)for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=xr(e,c),!Rr(l))return yr;m=m&&Nn(l,h,s),h=l}else{for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=xr(e,c),l===mi)u=!0,p&&(d=d||c-g-1>i&&e[g+1]!==" ",g=c);else if(!Rr(l))return yr;m=m&&Nn(l,h,s),h=l}d=d||p&&c-g-1>i&&e[g+1]!==" "}return!u&&!d?m&&!o&&!a(e)?zh:n===yi?yr:zn:r>9&&_s(e)?yr:o?n===yi?yr:zn:d?qh:Wh}f(jh,"chooseScalarStyle");function Hh(e,t,r,i,a){e.dump=function(){if(t.length===0)return e.quotingType===yi?'""':"''";if(!e.noCompatMode&&(ay.indexOf(t)!==-1||ny.test(t)))return e.quotingType===yi?'"'+t+'"':"'"+t+"'";var n=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-n),s=i||e.flowLevel>-1&&r>=e.flowLevel;function c(l){return Ih(e,l)}switch(f(c,"testAmbiguity"),jh(t,s,e.indent,o,c,e.quotingType,e.forceQuotes&&!i,a)){case zh:return t;case zn:return"'"+t.replace(/'/g,"''")+"'";case Wh:return"|"+Wn(t,e.indent)+qn(In(t,n));case qh:return">"+Wn(t,e.indent)+qn(In(Uh(t,o),n));case yr:return'"'+Gh(t)+'"';default:throw new ee("impossible error: invalid scalar style")}}()}f(Hh,"writeScalar");function Wn(e,t){var r=_s(e)?String(t):"",i=e[e.length-1]===`
`,a=i&&(e[e.length-2]===`
`||e===`
`),n=a?"+":i?"":"-";return r+n+`
`}f(Wn,"blockHeader");function qn(e){return e[e.length-1]===`
`?e.slice(0,-1):e}f(qn,"dropEndingNewline");function Uh(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var l=e.indexOf(`
`);return l=l!==-1?l:e.length,r.lastIndex=l,jn(e.slice(0,l),t)}(),a=e[0]===`
`||e[0]===" ",n,o;o=r.exec(e);){var s=o[1],c=o[2];n=c[0]===" ",i+=s+(!a&&!n&&c!==""?`
`:"")+jn(c,t),a=n}return i}f(Uh,"foldString");function jn(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,a=0,n,o=0,s=0,c="";i=r.exec(e);)s=i.index,s-a>t&&(n=o>a?o:s,c+=`
`+e.slice(a,n),a=n+1),o=s;return c+=`
`,e.length-a>t&&o>a?c+=e.slice(a,o)+`
`+e.slice(o+1):c+=e.slice(a),c.slice(1)}f(jn,"foldLine");function Gh(e){for(var t="",r=0,i,a=0;a<e.length;r>=65536?a+=2:a++)r=xr(e,a),i=jt[r],!i&&Rr(r)?(t+=e[a],r>=65536&&(t+=e[a+1])):t+=i||$h(r);return t}f(Gh,"escapeString");function Yh(e,t,r){var i="",a=e.tag,n,o,s;for(n=0,o=r.length;n<o;n+=1)s=r[n],e.replacer&&(s=e.replacer.call(r,String(n),s)),(xe(e,t,s,!1,!1)||typeof s>"u"&&xe(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=a,e.dump="["+i+"]"}f(Yh,"writeFlowSequence");function Hn(e,t,r,i){var a="",n=e.tag,o,s,c;for(o=0,s=r.length;o<s;o+=1)c=r[o],e.replacer&&(c=e.replacer.call(r,String(o),c)),(xe(e,t+1,c,!0,!0,!1,!0)||typeof c>"u"&&xe(e,t+1,null,!0,!0,!1,!0))&&((!i||a!=="")&&(a+=da(e,t)),e.dump&&mi===e.dump.charCodeAt(0)?a+="-":a+="- ",a+=e.dump);e.tag=n,e.dump=a||"[]"}f(Hn,"writeBlockSequence");function Vh(e,t,r){var i="",a=e.tag,n=Object.keys(r),o,s,c,l,h;for(o=0,s=n.length;o<s;o+=1)h="",i!==""&&(h+=", "),e.condenseFlow&&(h+='"'),c=n[o],l=r[c],e.replacer&&(l=e.replacer.call(r,c,l)),xe(e,t,c,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),xe(e,t,l,!1,!1)&&(h+=e.dump,i+=h));e.tag=a,e.dump="{"+i+"}"}f(Vh,"writeFlowMapping");function Xh(e,t,r,i){var a="",n=e.tag,o=Object.keys(r),s,c,l,h,u,d;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new ee("sortKeys must be a boolean or a function");for(s=0,c=o.length;s<c;s+=1)d="",(!i||a!=="")&&(d+=da(e,t)),l=o[s],h=r[l],e.replacer&&(h=e.replacer.call(r,l,h)),xe(e,t+1,l,!0,!0,!0)&&(u=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,u&&(e.dump&&mi===e.dump.charCodeAt(0)?d+="?":d+="? "),d+=e.dump,u&&(d+=da(e,t)),xe(e,t+1,h,!0,u)&&(e.dump&&mi===e.dump.charCodeAt(0)?d+=":":d+=": ",d+=e.dump,a+=d));e.tag=n,e.dump=a||"{}"}f(Xh,"writeBlockMapping");function Un(e,t,r){var i,a,n,o,s,c;for(a=r?e.explicitTypes:e.implicitTypes,n=0,o=a.length;n<o;n+=1)if(s=a[n],(s.instanceOf||s.predicate)&&(!s.instanceOf||typeof t=="object"&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(r?s.multi&&s.representName?e.tag=s.representName(t):e.tag=s.tag:e.tag="?",s.represent){if(c=e.styleMap[s.tag]||s.defaultStyle,Bh.call(s.represent)==="[object Function]")i=s.represent(t,c);else if(Lh.call(s.represent,c))i=s.represent[c](t,c);else throw new ee("!<"+s.tag+'> tag resolver accepts not "'+c+'" style');e.dump=i}return!0}return!1}f(Un,"detectType");function xe(e,t,r,i,a,n,o){e.tag=null,e.dump=r,Un(e,r,!1)||Un(e,r,!0);var s=Bh.call(e.dump),c=i,l;i&&(i=e.flowLevel<0||e.flowLevel>t);var h=s==="[object Object]"||s==="[object Array]",u,d;if(h&&(u=e.duplicates.indexOf(r),d=u!==-1),(e.tag!==null&&e.tag!=="?"||d||e.indent!==2&&t>0)&&(a=!1),d&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(h&&d&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),s==="[object Object]")i&&Object.keys(e.dump).length!==0?(Xh(e,t,e.dump,a),d&&(e.dump="&ref_"+u+e.dump)):(Vh(e,t,e.dump),d&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!o&&t>0?Hn(e,t-1,e.dump,a):Hn(e,t,e.dump,a),d&&(e.dump="&ref_"+u+e.dump)):(Yh(e,t,e.dump),d&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object String]")e.tag!=="?"&&Hh(e,e.dump,t,n,c);else{if(s==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new ee("unacceptable kind of an object to dump "+s)}e.tag!==null&&e.tag!=="?"&&(l=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?l="!"+l:l.slice(0,18)==="tag:yaml.org,2002:"?l="!!"+l.slice(18):l="!<"+l+">",e.dump=l+" "+e.dump)}return!0}f(xe,"writeNode");function Zh(e,t){var r=[],i=[],a,n;for(pa(e,r,i),a=0,n=i.length;a<n;a+=1)t.duplicates.push(r[i[a]]);t.usedDuplicates=new Array(n)}f(Zh,"getDuplicateReferences");function pa(e,t,r){var i,a,n;if(e!==null&&typeof e=="object")if(a=t.indexOf(e),a!==-1)r.indexOf(a)===-1&&r.push(a);else if(t.push(e),Array.isArray(e))for(a=0,n=e.length;a<n;a+=1)pa(e[a],t,r);else for(i=Object.keys(e),a=0,n=i.length;a<n;a+=1)pa(e[i[a]],t,r)}f(pa,"inspectNode");function oy(e,t){t=t||{};var r=new Rh(t);r.noRefs||Zh(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),xe(r,0,i,!0,!0)?r.dump+`
`:""}f(oy,"dump$1");function ly(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}f(ly,"renamed");var cy=Wc,hy=Wm.load;/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/var se={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function si(e,t){if(e===void 0||t===void 0)return{angle:0,deltaX:0,deltaY:0};e=St(e),t=St(t);const[r,i]=[e.x,e.y],[a,n]=[t.x,t.y],o=a-r,s=n-i;return{angle:Math.atan(s/o),deltaX:o,deltaY:s}}f(si,"calculateDeltaAndAngle");var St=f(e=>Array.isArray(e)?{x:e[0],y:e[1]}:e,"pointTransformer"),uy=f(e=>({x:f(function(t,r,i){let a=0;const n=St(i[0]).x<St(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(se,e.arrowTypeStart)){const{angle:p,deltaX:g}=si(i[0],i[1]);a=se[e.arrowTypeStart]*Math.cos(p)*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(se,e.arrowTypeEnd)){const{angle:p,deltaX:g}=si(i[i.length-1],i[i.length-2]);a=se[e.arrowTypeEnd]*Math.cos(p)*(g>=0?1:-1)}const o=Math.abs(St(t).x-St(i[i.length-1]).x),s=Math.abs(St(t).y-St(i[i.length-1]).y),c=Math.abs(St(t).x-St(i[0]).x),l=Math.abs(St(t).y-St(i[0]).y),h=se[e.arrowTypeStart],u=se[e.arrowTypeEnd],d=1;if(o<u&&o>0&&s<u){let p=u+d-o;p*=n==="right"?-1:1,a-=p}if(c<h&&c>0&&l<h){let p=h+d-c;p*=n==="right"?-1:1,a+=p}return St(t).x+a},"x"),y:f(function(t,r,i){let a=0;const n=St(i[0]).y<St(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(se,e.arrowTypeStart)){const{angle:p,deltaY:g}=si(i[0],i[1]);a=se[e.arrowTypeStart]*Math.abs(Math.sin(p))*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(se,e.arrowTypeEnd)){const{angle:p,deltaY:g}=si(i[i.length-1],i[i.length-2]);a=se[e.arrowTypeEnd]*Math.abs(Math.sin(p))*(g>=0?1:-1)}const o=Math.abs(St(t).y-St(i[i.length-1]).y),s=Math.abs(St(t).x-St(i[i.length-1]).x),c=Math.abs(St(t).y-St(i[0]).y),l=Math.abs(St(t).x-St(i[0]).x),h=se[e.arrowTypeStart],u=se[e.arrowTypeEnd],d=1;if(o<u&&o>0&&s<u){let p=u+d-o;p*=n==="up"?-1:1,a-=p}if(c<h&&c>0&&l<h){let p=h+d-c;p*=n==="up"?-1:1,a+=p}return St(t).y+a},"y")}),"getLineFunctionsWithOffset"),Bs=f(({flowchart:e})=>{var a,n;const t=((a=e==null?void 0:e.subGraphTitleMargin)==null?void 0:a.top)??0,r=((n=e==null?void 0:e.subGraphTitleMargin)==null?void 0:n.bottom)??0,i=t+r;return{subGraphTitleTopMargin:t,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}},"getSubGraphTitleMargins");const dy=Object.freeze({left:0,top:0,width:16,height:16}),fa=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),Kh=Object.freeze({...dy,...fa}),py=Object.freeze({...Kh,body:"",hidden:!1}),fy=Object.freeze({width:null,height:null}),gy=Object.freeze({...fy,...fa}),my=(e,t,r,i="")=>{const a=e.split(":");if(e.slice(0,1)==="@"){if(a.length<2||a.length>3)return null;i=a.shift().slice(1)}if(a.length>3||!a.length)return null;if(a.length>1){const s=a.pop(),c=a.pop(),l={provider:a.length>0?a[0]:i,prefix:c,name:s};return dn(l)?l:null}const n=a[0],o=n.split("-");if(o.length>1){const s={provider:i,prefix:o.shift(),name:o.join("-")};return dn(s)?s:null}if(r&&i===""){const s={provider:i,prefix:"",name:n};return dn(s,r)?s:null}return null},dn=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1;function yy(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const i=((e.rotate||0)+(t.rotate||0))%4;return i&&(r.rotate=i),r}function Po(e,t){const r=yy(e,t);for(const i in py)i in fa?i in e&&!(i in r)&&(r[i]=fa[i]):i in t?r[i]=t[i]:i in e&&(r[i]=e[i]);return r}function by(e,t){const r=e.icons,i=e.aliases||Object.create(null),a=Object.create(null);function n(o){if(r[o])return a[o]=[];if(!(o in a)){a[o]=null;const s=i[o]&&i[o].parent,c=s&&n(s);c&&(a[o]=[s].concat(c))}return a[o]}return(t||Object.keys(r).concat(Object.keys(i))).forEach(n),a}function No(e,t,r){const i=e.icons,a=e.aliases||Object.create(null);let n={};function o(s){n=Po(i[s]||a[s],n)}return o(t),r.forEach(o),Po(e,n)}function xy(e,t){if(e.icons[t])return No(e,t,[]);const r=by(e,[t])[t];return r?No(e,t,r):null}const Cy=/(-?[0-9.]*[0-9]+[0-9.]*)/g,wy=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function zo(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const i=e.split(Cy);if(i===null||!i.length)return e;const a=[];let n=i.shift(),o=wy.test(n);for(;;){if(o){const s=parseFloat(n);isNaN(s)?a.push(n):a.push(Math.ceil(s*t*r)/r)}else a.push(n);if(n=i.shift(),n===void 0)return a.join("");o=!o}}function ky(e,t="defs"){let r="";const i=e.indexOf("<"+t);for(;i>=0;){const a=e.indexOf(">",i),n=e.indexOf("</"+t);if(a===-1||n===-1)break;const o=e.indexOf(">",n);if(o===-1)break;r+=e.slice(a+1,n).trim(),e=e.slice(0,i).trim()+e.slice(o+1)}return{defs:r,content:e}}function vy(e,t){return e?"<defs>"+e+"</defs>"+t:t}function Sy(e,t,r){const i=ky(e);return vy(i.defs,t+i.content+r)}const Ty=e=>e==="unset"||e==="undefined"||e==="none";function _y(e,t){const r={...Kh,...e},i={...gy,...t},a={left:r.left,top:r.top,width:r.width,height:r.height};let n=r.body;[r,i].forEach(m=>{const y=[],b=m.hFlip,x=m.vFlip;let k=m.rotate;b?x?k+=2:(y.push("translate("+(a.width+a.left).toString()+" "+(0-a.top).toString()+")"),y.push("scale(-1 1)"),a.top=a.left=0):x&&(y.push("translate("+(0-a.left).toString()+" "+(a.height+a.top).toString()+")"),y.push("scale(1 -1)"),a.top=a.left=0);let L;switch(k<0&&(k-=Math.floor(k/4)*4),k=k%4,k){case 1:L=a.height/2+a.top,y.unshift("rotate(90 "+L.toString()+" "+L.toString()+")");break;case 2:y.unshift("rotate(180 "+(a.width/2+a.left).toString()+" "+(a.height/2+a.top).toString()+")");break;case 3:L=a.width/2+a.left,y.unshift("rotate(-90 "+L.toString()+" "+L.toString()+")");break}k%2===1&&(a.left!==a.top&&(L=a.left,a.left=a.top,a.top=L),a.width!==a.height&&(L=a.width,a.width=a.height,a.height=L)),y.length&&(n=Sy(n,'<g transform="'+y.join(" ")+'">',"</g>"))});const o=i.width,s=i.height,c=a.width,l=a.height;let h,u;o===null?(u=s===null?"1em":s==="auto"?l:s,h=zo(u,c/l)):(h=o==="auto"?c:o,u=s===null?zo(h,l/c):s==="auto"?l:s);const d={},p=(m,y)=>{Ty(y)||(d[m]=y.toString())};p("width",h),p("height",u);const g=[a.left,a.top,c,l];return d.viewBox=g.join(" "),{attributes:d,viewBox:g,body:n}}const By=/\sid="(\S+)"/g,Ly="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let Ay=0;function My(e,t=Ly){const r=[];let i;for(;i=By.exec(e);)r.push(i[1]);if(!r.length)return e;const a="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(n=>{const o=typeof t=="function"?t(n):t+(Ay++).toString(),s=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+s+')([")]|\\.[a-z])',"g"),"$1"+o+a+"$3")}),e=e.replace(new RegExp(a,"g"),""),e}function Ey(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in t)r+=" "+i+'="'+t[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}var Fy={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},Gn=new Map,Qh=new Map,Dy=f(e=>{for(const t of e){if(!t.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if(P.debug("Registering icon pack:",t.name),"loader"in t)Qh.set(t.name,t.loader);else if("icons"in t)Gn.set(t.name,t.icons);else throw P.error("Invalid icon loader:",t),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),Oy=f(async(e,t)=>{const r=my(e,!0,t!==void 0);if(!r)throw new Error(`Invalid icon name: ${e}`);const i=r.prefix||t;if(!i)throw new Error(`Icon name must contain a prefix: ${e}`);let a=Gn.get(i);if(!a){const o=Qh.get(i);if(!o)throw new Error(`Icon set not found: ${r.prefix}`);try{a={...await o(),prefix:i},Gn.set(i,a)}catch(s){throw P.error(s),new Error(`Failed to load icon set: ${r.prefix}`)}}const n=xy(a,r.name);if(!n)throw new Error(`Icon not found: ${e}`);return n},"getRegisteredIconData"),$a=f(async(e,t)=>{let r;try{r=await Oy(e,t==null?void 0:t.fallbackPrefix)}catch(n){P.error(n),r=Fy}const i=_y(r,t);return Ey(My(i.body),i.attributes)},"getIconSVG"),Ls={},Ft={};Object.defineProperty(Ft,"__esModule",{value:!0});Ft.BLANK_URL=Ft.relativeFirstCharacters=Ft.whitespaceEscapeCharsRegex=Ft.urlSchemeRegex=Ft.ctrlCharactersRegex=Ft.htmlCtrlEntityRegex=Ft.htmlEntitiesRegex=Ft.invalidProtocolRegex=void 0;Ft.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;Ft.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;Ft.htmlCtrlEntityRegex=/&(newline|tab);/gi;Ft.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;Ft.urlSchemeRegex=/^.+(:|&colon;)/gim;Ft.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;Ft.relativeFirstCharacters=[".","/"];Ft.BLANK_URL="about:blank";Object.defineProperty(Ls,"__esModule",{value:!0});var Jh=Ls.sanitizeUrl=void 0,Wt=Ft;function $y(e){return Wt.relativeFirstCharacters.indexOf(e[0])>-1}function Ry(e){var t=e.replace(Wt.ctrlCharactersRegex,"");return t.replace(Wt.htmlEntitiesRegex,function(r,i){return String.fromCharCode(i)})}function Iy(e){return URL.canParse(e)}function Wo(e){try{return decodeURIComponent(e)}catch{return e}}function Py(e){if(!e)return Wt.BLANK_URL;var t,r=Wo(e.trim());do r=Ry(r).replace(Wt.htmlCtrlEntityRegex,"").replace(Wt.ctrlCharactersRegex,"").replace(Wt.whitespaceEscapeCharsRegex,"").trim(),r=Wo(r),t=r.match(Wt.ctrlCharactersRegex)||r.match(Wt.htmlEntitiesRegex)||r.match(Wt.htmlCtrlEntityRegex)||r.match(Wt.whitespaceEscapeCharsRegex);while(t&&t.length>0);var i=r;if(!i)return Wt.BLANK_URL;if($y(i))return i;var a=i.trimStart(),n=a.match(Wt.urlSchemeRegex);if(!n)return i;var o=n[0].toLowerCase().trim();if(Wt.invalidProtocolRegex.test(o))return Wt.BLANK_URL;var s=a.replace(/\\/g,"/");if(o==="mailto:"||o.includes("://"))return s;if(o==="http:"||o==="https:"){if(!Iy(s))return Wt.BLANK_URL;var c=new URL(s);return c.protocol=c.protocol.toLowerCase(),c.hostname=c.hostname.toLowerCase(),c.toString()}return s}Jh=Ls.sanitizeUrl=Py;var tu=typeof Ii=="object"&&Ii&&Ii.Object===Object&&Ii,Ny=typeof self=="object"&&self&&self.Object===Object&&self,Ce=tu||Ny||Function("return this")(),ga=Ce.Symbol,eu=Object.prototype,zy=eu.hasOwnProperty,Wy=eu.toString,Jr=ga?ga.toStringTag:void 0;function qy(e){var t=zy.call(e,Jr),r=e[Jr];try{e[Jr]=void 0;var i=!0}catch{}var a=Wy.call(e);return i&&(t?e[Jr]=r:delete e[Jr]),a}var jy=Object.prototype,Hy=jy.toString;function Uy(e){return Hy.call(e)}var Gy="[object Null]",Yy="[object Undefined]",qo=ga?ga.toStringTag:void 0;function zr(e){return e==null?e===void 0?Yy:Gy:qo&&qo in Object(e)?qy(e):Uy(e)}function nr(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Vy="[object AsyncFunction]",Xy="[object Function]",Zy="[object GeneratorFunction]",Ky="[object Proxy]";function As(e){if(!nr(e))return!1;var t=zr(e);return t==Xy||t==Zy||t==Vy||t==Ky}var pn=Ce["__core-js_shared__"],jo=function(){var e=/[^.]+$/.exec(pn&&pn.keys&&pn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Qy(e){return!!jo&&jo in e}var Jy=Function.prototype,t0=Jy.toString;function sr(e){if(e!=null){try{return t0.call(e)}catch{}try{return e+""}catch{}}return""}var e0=/[\\^$.*+?()[\]{}|]/g,r0=/^\[object .+?Constructor\]$/,i0=Function.prototype,a0=Object.prototype,n0=i0.toString,s0=a0.hasOwnProperty,o0=RegExp("^"+n0.call(s0).replace(e0,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l0(e){if(!nr(e)||Qy(e))return!1;var t=As(e)?o0:r0;return t.test(sr(e))}function c0(e,t){return e==null?void 0:e[t]}function or(e,t){var r=c0(e,t);return l0(r)?r:void 0}var xi=or(Object,"create");function h0(){this.__data__=xi?xi(null):{},this.size=0}function u0(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var d0="__lodash_hash_undefined__",p0=Object.prototype,f0=p0.hasOwnProperty;function g0(e){var t=this.__data__;if(xi){var r=t[e];return r===d0?void 0:r}return f0.call(t,e)?t[e]:void 0}var m0=Object.prototype,y0=m0.hasOwnProperty;function b0(e){var t=this.__data__;return xi?t[e]!==void 0:y0.call(t,e)}var x0="__lodash_hash_undefined__";function C0(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=xi&&t===void 0?x0:t,this}function rr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}rr.prototype.clear=h0;rr.prototype.delete=u0;rr.prototype.get=g0;rr.prototype.has=b0;rr.prototype.set=C0;function w0(){this.__data__=[],this.size=0}function Ra(e,t){return e===t||e!==e&&t!==t}function Ia(e,t){for(var r=e.length;r--;)if(Ra(e[r][0],t))return r;return-1}var k0=Array.prototype,v0=k0.splice;function S0(e){var t=this.__data__,r=Ia(t,e);if(r<0)return!1;var i=t.length-1;return r==i?t.pop():v0.call(t,r,1),--this.size,!0}function T0(e){var t=this.__data__,r=Ia(t,e);return r<0?void 0:t[r][1]}function _0(e){return Ia(this.__data__,e)>-1}function B0(e,t){var r=this.__data__,i=Ia(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this}function $e(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}$e.prototype.clear=w0;$e.prototype.delete=S0;$e.prototype.get=T0;$e.prototype.has=_0;$e.prototype.set=B0;var Ci=or(Ce,"Map");function L0(){this.size=0,this.__data__={hash:new rr,map:new(Ci||$e),string:new rr}}function A0(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Pa(e,t){var r=e.__data__;return A0(t)?r[typeof t=="string"?"string":"hash"]:r.map}function M0(e){var t=Pa(this,e).delete(e);return this.size-=t?1:0,t}function E0(e){return Pa(this,e).get(e)}function F0(e){return Pa(this,e).has(e)}function D0(e,t){var r=Pa(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this}function He(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}He.prototype.clear=L0;He.prototype.delete=M0;He.prototype.get=E0;He.prototype.has=F0;He.prototype.set=D0;var O0="Expected a function";function _i(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(O0);var r=function(){var i=arguments,a=t?t.apply(this,i):i[0],n=r.cache;if(n.has(a))return n.get(a);var o=e.apply(this,i);return r.cache=n.set(a,o)||n,o};return r.cache=new(_i.Cache||He),r}_i.Cache=He;function $0(){this.__data__=new $e,this.size=0}function R0(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function I0(e){return this.__data__.get(e)}function P0(e){return this.__data__.has(e)}var N0=200;function z0(e,t){var r=this.__data__;if(r instanceof $e){var i=r.__data__;if(!Ci||i.length<N0-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new He(i)}return r.set(e,t),this.size=r.size,this}function Wr(e){var t=this.__data__=new $e(e);this.size=t.size}Wr.prototype.clear=$0;Wr.prototype.delete=R0;Wr.prototype.get=I0;Wr.prototype.has=P0;Wr.prototype.set=z0;var ma=function(){try{var e=or(Object,"defineProperty");return e({},"",{}),e}catch{}}();function Ms(e,t,r){t=="__proto__"&&ma?ma(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function Yn(e,t,r){(r!==void 0&&!Ra(e[t],r)||r===void 0&&!(t in e))&&Ms(e,t,r)}function W0(e){return function(t,r,i){for(var a=-1,n=Object(t),o=i(t),s=o.length;s--;){var c=o[++a];if(r(n[c],c,n)===!1)break}return t}}var q0=W0(),ru=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ho=ru&&typeof module=="object"&&module&&!module.nodeType&&module,j0=Ho&&Ho.exports===ru,Uo=j0?Ce.Buffer:void 0,Go=Uo?Uo.allocUnsafe:void 0;function H0(e,t){if(t)return e.slice();var r=e.length,i=Go?Go(r):new e.constructor(r);return e.copy(i),i}var Yo=Ce.Uint8Array;function U0(e){var t=new e.constructor(e.byteLength);return new Yo(t).set(new Yo(e)),t}function G0(e,t){var r=t?U0(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function Y0(e,t){var r=-1,i=e.length;for(t||(t=Array(i));++r<i;)t[r]=e[r];return t}var Vo=Object.create,V0=function(){function e(){}return function(t){if(!nr(t))return{};if(Vo)return Vo(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function iu(e,t){return function(r){return e(t(r))}}var au=iu(Object.getPrototypeOf,Object),X0=Object.prototype;function Na(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||X0;return e===r}function Z0(e){return typeof e.constructor=="function"&&!Na(e)?V0(au(e)):{}}function Bi(e){return e!=null&&typeof e=="object"}var K0="[object Arguments]";function Xo(e){return Bi(e)&&zr(e)==K0}var nu=Object.prototype,Q0=nu.hasOwnProperty,J0=nu.propertyIsEnumerable,ya=Xo(function(){return arguments}())?Xo:function(e){return Bi(e)&&Q0.call(e,"callee")&&!J0.call(e,"callee")},ba=Array.isArray,tb=9007199254740991;function su(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=tb}function za(e){return e!=null&&su(e.length)&&!As(e)}function eb(e){return Bi(e)&&za(e)}function rb(){return!1}var ou=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Zo=ou&&typeof module=="object"&&module&&!module.nodeType&&module,ib=Zo&&Zo.exports===ou,Ko=ib?Ce.Buffer:void 0,ab=Ko?Ko.isBuffer:void 0,Es=ab||rb,nb="[object Object]",sb=Function.prototype,ob=Object.prototype,lu=sb.toString,lb=ob.hasOwnProperty,cb=lu.call(Object);function hb(e){if(!Bi(e)||zr(e)!=nb)return!1;var t=au(e);if(t===null)return!0;var r=lb.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&lu.call(r)==cb}var ub="[object Arguments]",db="[object Array]",pb="[object Boolean]",fb="[object Date]",gb="[object Error]",mb="[object Function]",yb="[object Map]",bb="[object Number]",xb="[object Object]",Cb="[object RegExp]",wb="[object Set]",kb="[object String]",vb="[object WeakMap]",Sb="[object ArrayBuffer]",Tb="[object DataView]",_b="[object Float32Array]",Bb="[object Float64Array]",Lb="[object Int8Array]",Ab="[object Int16Array]",Mb="[object Int32Array]",Eb="[object Uint8Array]",Fb="[object Uint8ClampedArray]",Db="[object Uint16Array]",Ob="[object Uint32Array]",kt={};kt[_b]=kt[Bb]=kt[Lb]=kt[Ab]=kt[Mb]=kt[Eb]=kt[Fb]=kt[Db]=kt[Ob]=!0;kt[ub]=kt[db]=kt[Sb]=kt[pb]=kt[Tb]=kt[fb]=kt[gb]=kt[mb]=kt[yb]=kt[bb]=kt[xb]=kt[Cb]=kt[wb]=kt[kb]=kt[vb]=!1;function $b(e){return Bi(e)&&su(e.length)&&!!kt[zr(e)]}function Rb(e){return function(t){return e(t)}}var cu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,pi=cu&&typeof module=="object"&&module&&!module.nodeType&&module,Ib=pi&&pi.exports===cu,fn=Ib&&tu.process,Qo=function(){try{var e=pi&&pi.require&&pi.require("util").types;return e||fn&&fn.binding&&fn.binding("util")}catch{}}(),Jo=Qo&&Qo.isTypedArray,Fs=Jo?Rb(Jo):$b;function Vn(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var Pb=Object.prototype,Nb=Pb.hasOwnProperty;function zb(e,t,r){var i=e[t];(!(Nb.call(e,t)&&Ra(i,r))||r===void 0&&!(t in e))&&Ms(e,t,r)}function Wb(e,t,r,i){var a=!r;r||(r={});for(var n=-1,o=t.length;++n<o;){var s=t[n],c=void 0;c===void 0&&(c=e[s]),a?Ms(r,s,c):zb(r,s,c)}return r}function qb(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}var jb=9007199254740991,Hb=/^(?:0|[1-9]\d*)$/;function hu(e,t){var r=typeof e;return t=t??jb,!!t&&(r=="number"||r!="symbol"&&Hb.test(e))&&e>-1&&e%1==0&&e<t}var Ub=Object.prototype,Gb=Ub.hasOwnProperty;function Yb(e,t){var r=ba(e),i=!r&&ya(e),a=!r&&!i&&Es(e),n=!r&&!i&&!a&&Fs(e),o=r||i||a||n,s=o?qb(e.length,String):[],c=s.length;for(var l in e)(t||Gb.call(e,l))&&!(o&&(l=="length"||a&&(l=="offset"||l=="parent")||n&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||hu(l,c)))&&s.push(l);return s}function Vb(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var Xb=Object.prototype,Zb=Xb.hasOwnProperty;function Kb(e){if(!nr(e))return Vb(e);var t=Na(e),r=[];for(var i in e)i=="constructor"&&(t||!Zb.call(e,i))||r.push(i);return r}function uu(e){return za(e)?Yb(e,!0):Kb(e)}function Qb(e){return Wb(e,uu(e))}function Jb(e,t,r,i,a,n,o){var s=Vn(e,r),c=Vn(t,r),l=o.get(c);if(l){Yn(e,r,l);return}var h=n?n(s,c,r+"",e,t,o):void 0,u=h===void 0;if(u){var d=ba(c),p=!d&&Es(c),g=!d&&!p&&Fs(c);h=c,d||p||g?ba(s)?h=s:eb(s)?h=Y0(s):p?(u=!1,h=H0(c,!0)):g?(u=!1,h=G0(c,!0)):h=[]:hb(c)||ya(c)?(h=s,ya(s)?h=Qb(s):(!nr(s)||As(s))&&(h=Z0(c))):u=!1}u&&(o.set(c,h),a(h,c,i,n,o),o.delete(c)),Yn(e,r,h)}function du(e,t,r,i,a){e!==t&&q0(t,function(n,o){if(a||(a=new Wr),nr(n))Jb(e,t,o,r,du,i,a);else{var s=i?i(Vn(e,o),n,o+"",e,t,a):void 0;s===void 0&&(s=n),Yn(e,o,s)}},uu)}function pu(e){return e}function tx(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var tl=Math.max;function ex(e,t,r){return t=tl(t===void 0?e.length-1:t,0),function(){for(var i=arguments,a=-1,n=tl(i.length-t,0),o=Array(n);++a<n;)o[a]=i[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=i[a];return s[t]=r(o),tx(e,this,s)}}function rx(e){return function(){return e}}var ix=ma?function(e,t){return ma(e,"toString",{configurable:!0,enumerable:!1,value:rx(t),writable:!0})}:pu,ax=800,nx=16,sx=Date.now;function ox(e){var t=0,r=0;return function(){var i=sx(),a=nx-(i-r);if(r=i,a>0){if(++t>=ax)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var lx=ox(ix);function cx(e,t){return lx(ex(e,t,pu),e+"")}function hx(e,t,r){if(!nr(r))return!1;var i=typeof t;return(i=="number"?za(r)&&hu(t,r.length):i=="string"&&t in r)?Ra(r[t],e):!1}function ux(e){return cx(function(t,r){var i=-1,a=r.length,n=a>1?r[a-1]:void 0,o=a>2?r[2]:void 0;for(n=e.length>3&&typeof n=="function"?(a--,n):void 0,o&&hx(r[0],r[1],o)&&(n=a<3?void 0:n,a=1),t=Object(t);++i<a;){var s=r[i];s&&e(t,s,i,n)}return t})}var dx=ux(function(e,t,r){du(e,t,r)}),px="​",fx={curveBasis:Gi,curveBasisClosed:Mf,curveBasisOpen:Af,curveBumpX:jl,curveBumpY:ql,curveBundle:Lf,curveCardinalClosed:Bf,curveCardinalOpen:_f,curveCardinal:Wl,curveCatmullRomClosed:Tf,curveCatmullRomOpen:Sf,curveCatmullRom:zl,curveLinear:Sn,curveLinearClosed:vf,curveMonotoneX:Nl,curveMonotoneY:Pl,curveNatural:Il,curveStep:Rl,curveStepAfter:$l,curveStepBefore:Ol},gx=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,mx=f(function(e,t){const r=fu(e,/(?:init\b)|(?:initialize\b)/);let i={};if(Array.isArray(r)){const o=r.map(s=>s.args);aa(o),i=Rt(i,[...o])}else i=r.args;if(!i)return;let a=ps(e,t);const n="config";return i[n]!==void 0&&(a==="flowchart-v2"&&(a="flowchart"),i[a]=i[n],delete i[n]),i},"detectInit"),fu=f(function(e,t=null){var r,i;try{const a=new RegExp(`[%]{2}(?![{]${gx.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(a,"").replace(/'/gm,'"'),P.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let n;const o=[];for(;(n=ui.exec(e))!==null;)if(n.index===ui.lastIndex&&ui.lastIndex++,n&&!t||t&&((r=n[1])!=null&&r.match(t))||t&&((i=n[2])!=null&&i.match(t))){const s=n[1]?n[1]:n[2],c=n[3]?n[3].trim():n[4]?JSON.parse(n[4].trim()):null;o.push({type:s,args:c})}return o.length===0?{type:e,args:null}:o.length===1?o[0]:o}catch(a){return P.error(`ERROR: ${a.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),yx=f(function(e){return e.replace(ui,"")},"removeDirectives"),bx=f(function(e,t){for(const[r,i]of t.entries())if(i.match(e))return r;return-1},"isSubstringInArray");function Ds(e,t){if(!e)return t;const r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return fx[r]??t}f(Ds,"interpolateToCurve");function gu(e,t){const r=e.trim();if(r)return t.securityLevel!=="loose"?Jh(r):r}f(gu,"formatUrl");var xx=f((e,...t)=>{const r=e.split("."),i=r.length-1,a=r[i];let n=window;for(let o=0;o<i;o++)if(n=n[r[o]],!n){P.error(`Function name: ${e} not found in window`);return}n[a](...t)},"runFunc");function Os(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}f(Os,"distance");function mu(e){let t,r=0;e.forEach(a=>{r+=Os(a,t),t=a});const i=r/2;return $s(e,i)}f(mu,"traverseEdge");function yu(e){return e.length===1?e[0]:mu(e)}f(yu,"calcLabelPosition");var el=f((e,t=2)=>{const r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),$s=f((e,t)=>{let r,i=t;for(const a of e){if(r){const n=Os(a,r);if(n===0)return r;if(n<i)i-=n;else{const o=i/n;if(o<=0)return r;if(o>=1)return{x:a.x,y:a.y};if(o>0&&o<1)return{x:el((1-o)*r.x+o*a.x,5),y:el((1-o)*r.y+o*a.y,5)}}}r=a}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),Cx=f((e,t,r)=>{P.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());const a=$s(t,25),n=e?10:5,o=Math.atan2(t[0].y-a.y,t[0].x-a.x),s={x:0,y:0};return s.x=Math.sin(o)*n+(t[0].x+a.x)/2,s.y=-Math.cos(o)*n+(t[0].y+a.y)/2,s},"calcCardinalityPosition");function bu(e,t,r){const i=structuredClone(r);P.info("our points",i),t!=="start_left"&&t!=="start_right"&&i.reverse();const a=25+e,n=$s(i,a),o=10+e*.5,s=Math.atan2(i[0].y-n.y,i[0].x-n.x),c={x:0,y:0};return t==="start_left"?(c.x=Math.sin(s+Math.PI)*o+(i[0].x+n.x)/2,c.y=-Math.cos(s+Math.PI)*o+(i[0].y+n.y)/2):t==="end_right"?(c.x=Math.sin(s-Math.PI)*o+(i[0].x+n.x)/2-5,c.y=-Math.cos(s-Math.PI)*o+(i[0].y+n.y)/2-5):t==="end_left"?(c.x=Math.sin(s)*o+(i[0].x+n.x)/2-5,c.y=-Math.cos(s)*o+(i[0].y+n.y)/2-5):(c.x=Math.sin(s)*o+(i[0].x+n.x)/2,c.y=-Math.cos(s)*o+(i[0].y+n.y)/2),c}f(bu,"calcTerminalLabelPosition");function xu(e){let t="",r="";for(const i of e)i!==void 0&&(i.startsWith("color:")||i.startsWith("text-align:")?r=r+i+";":t=t+i+";");return{style:t,labelStyle:r}}f(xu,"getStylesFromArray");var rl=0,wx=f(()=>(rl++,"id-"+Math.random().toString(36).substr(2,12)+"-"+rl),"generateId");function Cu(e){let t="";const r="0123456789abcdef",i=r.length;for(let a=0;a<e;a++)t+=r.charAt(Math.floor(Math.random()*i));return t}f(Cu,"makeRandomHex");var kx=f(e=>Cu(e.length),"random"),vx=f(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),Sx=f(function(e,t){const r=t.text.replace(Nr.lineBreakRegex," "),[,i]=Wa(t.fontSize),a=e.append("text");a.attr("x",t.x),a.attr("y",t.y),a.style("text-anchor",t.anchor),a.style("font-family",t.fontFamily),a.style("font-size",i),a.style("font-weight",t.fontWeight),a.attr("fill",t.fill),t.class!==void 0&&a.attr("class",t.class);const n=a.append("tspan");return n.attr("x",t.x+t.textMargin*2),n.attr("fill",t.fill),n.text(r),a},"drawSimpleText"),Tx=_i((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),Nr.lineBreakRegex.test(e)))return e;const i=e.split(" ").filter(Boolean),a=[];let n="";return i.forEach((o,s)=>{const c=Oe(`${o} `,r),l=Oe(n,r);if(c>t){const{hyphenatedStrings:d,remainingWord:p}=_x(o,t,"-",r);a.push(n,...d),n=p}else l+c>=t?(a.push(n),n=o):n=[n,o].filter(Boolean).join(" ");s+1===i.length&&a.push(n)}),a.filter(o=>o!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),_x=_i((e,t,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const a=[...e],n=[];let o="";return a.forEach((s,c)=>{const l=`${o}${s}`;if(Oe(l,i)>=t){const u=c+1,d=a.length===u,p=`${l}${r}`;n.push(d?l:p),o=""}else o=l}),{hyphenatedStrings:n,remainingWord:o}},(e,t,r="-",i)=>`${e}${t}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`);function wu(e,t){return Rs(e,t).height}f(wu,"calculateTextHeight");function Oe(e,t){return Rs(e,t).width}f(Oe,"calculateTextWidth");var Rs=_i((e,t)=>{const{fontSize:r=12,fontFamily:i="Arial",fontWeight:a=400}=t;if(!e)return{width:0,height:0};const[,n]=Wa(r),o=["sans-serif",i],s=e.split(Nr.lineBreakRegex),c=[],l=pt("body");if(!l.remove)return{width:0,height:0,lineHeight:0};const h=l.append("svg");for(const d of o){let p=0;const g={width:0,height:0,lineHeight:0};for(const m of s){const y=vx();y.text=m||px;const b=Sx(h,y).style("font-size",n).style("font-weight",a).style("font-family",d),x=(b._groups||b)[0][0].getBBox();if(x.width===0&&x.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,x.width)),p=Math.round(x.height),g.height+=p,g.lineHeight=Math.round(Math.max(g.lineHeight,p))}c.push(g)}h.remove();const u=isNaN(c[1].height)||isNaN(c[1].width)||isNaN(c[1].lineHeight)||c[0].height>c[1].height&&c[0].width>c[1].width&&c[0].lineHeight>c[1].lineHeight?0:1;return c[u]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),Ar,Bx=(Ar=class{constructor(t=!1,r){this.count=0,this.count=r?r.length:0,this.next=t?()=>this.count++:()=>Date.now()}},f(Ar,"InitIDGenerator"),Ar),zi,Lx=f(function(e){return zi=zi||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),zi.innerHTML=e,unescape(zi.textContent)},"entityDecode");function Is(e){return"str"in e}f(Is,"isDetailedError");var Ax=f((e,t,r,i)=>{var n;if(!i)return;const a=(n=e.node())==null?void 0:n.getBBox();a&&e.append("text").text(i).attr("text-anchor","middle").attr("x",a.x+a.width/2).attr("y",-r).attr("class",t)},"insertTitle"),Wa=f(e=>{if(typeof e=="number")return[e,e+"px"];const t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function Ps(e,t){return dx({},e,t)}f(Ps,"cleanAndMerge");var ye={assignWithDepth:Rt,wrapLabel:Tx,calculateTextHeight:wu,calculateTextWidth:Oe,calculateTextDimensions:Rs,cleanAndMerge:Ps,detectInit:mx,detectDirective:fu,isSubstringInArray:bx,interpolateToCurve:Ds,calcLabelPosition:yu,calcCardinalityPosition:Cx,calcTerminalLabelPosition:bu,formatUrl:gu,getStylesFromArray:xu,generateId:wx,random:kx,runFunc:xx,entityDecode:Lx,insertTitle:Ax,parseFontSize:Wa,InitIDGenerator:Bx},Mx=f(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){const i=r.substring(1,r.length-1);return/^\+?\d+$/.test(i)?"ﬂ°°"+i+"¶ß":"ﬂ°"+i+"¶ß"}),t},"encodeEntities"),lr=f(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities"),ev=f((e,t,{counter:r=0,prefix:i,suffix:a},n)=>n||`${i?`${i}_`:""}${e}_${t}_${r}${a?`_${a}`:""}`,"getEdgeId");function Xt(e){return e??null}f(Xt,"handleUndefinedAttr");function Ns(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var cr=Ns();function ku(e){cr=e}var fi={exec:()=>null};function bt(e,t=""){let r=typeof e=="string"?e:e.source;const i={replace:(a,n)=>{let o=typeof n=="string"?n:n.source;return o=o.replace(Vt.caret,"$1"),r=r.replace(a,o),i},getRegex:()=>new RegExp(r,t)};return i}var Vt={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},Ex=/^(?:[ \t]*(?:\n|$))+/,Fx=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Dx=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Li=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Ox=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,zs=/(?:[*+-]|\d{1,9}[.)])/,vu=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Su=bt(vu).replace(/bull/g,zs).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),$x=bt(vu).replace(/bull/g,zs).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Ws=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Rx=/^[^\n]+/,qs=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ix=bt(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",qs).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Px=bt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,zs).getRegex(),qa="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",js=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Nx=bt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",js).replace("tag",qa).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Tu=bt(Ws).replace("hr",Li).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",qa).getRegex(),zx=bt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Tu).getRegex(),Hs={blockquote:zx,code:Fx,def:Ix,fences:Dx,heading:Ox,hr:Li,html:Nx,lheading:Su,list:Px,newline:Ex,paragraph:Tu,table:fi,text:Rx},il=bt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Li).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",qa).getRegex(),Wx={...Hs,lheading:$x,table:il,paragraph:bt(Ws).replace("hr",Li).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",il).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",qa).getRegex()},qx={...Hs,html:bt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",js).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:fi,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:bt(Ws).replace("hr",Li).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Su).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},jx=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Hx=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,_u=/^( {2,}|\\)\n(?!\s*$)/,Ux=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,ja=/[\p{P}\p{S}]/u,Us=/[\s\p{P}\p{S}]/u,Bu=/[^\s\p{P}\p{S}]/u,Gx=bt(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Us).getRegex(),Lu=/(?!~)[\p{P}\p{S}]/u,Yx=/(?!~)[\s\p{P}\p{S}]/u,Vx=/(?:[^\s\p{P}\p{S}]|~)/u,Xx=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Au=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,Zx=bt(Au,"u").replace(/punct/g,ja).getRegex(),Kx=bt(Au,"u").replace(/punct/g,Lu).getRegex(),Mu="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Qx=bt(Mu,"gu").replace(/notPunctSpace/g,Bu).replace(/punctSpace/g,Us).replace(/punct/g,ja).getRegex(),Jx=bt(Mu,"gu").replace(/notPunctSpace/g,Vx).replace(/punctSpace/g,Yx).replace(/punct/g,Lu).getRegex(),tC=bt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Bu).replace(/punctSpace/g,Us).replace(/punct/g,ja).getRegex(),eC=bt(/\\(punct)/,"gu").replace(/punct/g,ja).getRegex(),rC=bt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),iC=bt(js).replace("(?:-->|$)","-->").getRegex(),aC=bt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",iC).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),xa=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,nC=bt(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",xa).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Eu=bt(/^!?\[(label)\]\[(ref)\]/).replace("label",xa).replace("ref",qs).getRegex(),Fu=bt(/^!?\[(ref)\](?:\[\])?/).replace("ref",qs).getRegex(),sC=bt("reflink|nolink(?!\\()","g").replace("reflink",Eu).replace("nolink",Fu).getRegex(),Gs={_backpedal:fi,anyPunctuation:eC,autolink:rC,blockSkip:Xx,br:_u,code:Hx,del:fi,emStrongLDelim:Zx,emStrongRDelimAst:Qx,emStrongRDelimUnd:tC,escape:jx,link:nC,nolink:Fu,punctuation:Gx,reflink:Eu,reflinkSearch:sC,tag:aC,text:Ux,url:fi},oC={...Gs,link:bt(/^!?\[(label)\]\((.*?)\)/).replace("label",xa).getRegex(),reflink:bt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",xa).getRegex()},Xn={...Gs,emStrongRDelimAst:Jx,emStrongLDelim:Kx,url:bt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},lC={...Xn,br:bt(_u).replace("{2,}","*").getRegex(),text:bt(Xn.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Wi={normal:Hs,gfm:Wx,pedantic:qx},ti={normal:Gs,gfm:Xn,breaks:lC,pedantic:oC},cC={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},al=e=>cC[e];function fe(e,t){if(t){if(Vt.escapeTest.test(e))return e.replace(Vt.escapeReplace,al)}else if(Vt.escapeTestNoEncode.test(e))return e.replace(Vt.escapeReplaceNoEncode,al);return e}function nl(e){try{e=encodeURI(e).replace(Vt.percentDecode,"%")}catch{return null}return e}function sl(e,t){var n;const r=e.replace(Vt.findPipe,(o,s,c)=>{let l=!1,h=s;for(;--h>=0&&c[h]==="\\";)l=!l;return l?"|":" |"}),i=r.split(Vt.splitPipe);let a=0;if(i[0].trim()||i.shift(),i.length>0&&!((n=i.at(-1))!=null&&n.trim())&&i.pop(),t)if(i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;a<i.length;a++)i[a]=i[a].trim().replace(Vt.slashPipe,"|");return i}function ei(e,t,r){const i=e.length;if(i===0)return"";let a=0;for(;a<i&&e.charAt(i-a-1)===t;)a++;return e.slice(0,i-a)}function hC(e,t){if(e.indexOf(t[1])===-1)return-1;let r=0;for(let i=0;i<e.length;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return r>0?-2:-1}function ol(e,t,r,i,a){const n=t.href,o=t.title||null,s=e[1].replace(a.other.outputLinkReplace,"$1");i.state.inLink=!0;const c={type:e[0].charAt(0)==="!"?"image":"link",raw:r,href:n,title:o,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,c}function uC(e,t,r){const i=e.match(r.other.indentCodeCompensation);if(i===null)return t;const a=i[1];return t.split(`
`).map(n=>{const o=n.match(r.other.beginningSpace);if(o===null)return n;const[s]=o;return s.length>=a.length?n.slice(a.length):n}).join(`
`)}var Ca=class{constructor(e){wt(this,"options");wt(this,"rules");wt(this,"lexer");this.options=e||cr}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const r=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?r:ei(r,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const r=t[0],i=uC(r,t[3]||"",this.rules);return{type:"code",raw:r,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:i}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let r=t[2].trim();if(this.rules.other.endingHash.test(r)){const i=ei(r,"#");(this.options.pedantic||!i||this.rules.other.endingSpaceChar.test(i))&&(r=i.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:ei(t[0],`
`)}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let r=ei(t[0],`
`).split(`
`),i="",a="";const n=[];for(;r.length>0;){let o=!1;const s=[];let c;for(c=0;c<r.length;c++)if(this.rules.other.blockquoteStart.test(r[c]))s.push(r[c]),o=!0;else if(!o)s.push(r[c]);else break;r=r.slice(c);const l=s.join(`
`),h=l.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");i=i?`${i}
${l}`:l,a=a?`${a}
${h}`:h;const u=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(h,n,!0),this.lexer.state.top=u,r.length===0)break;const d=n.at(-1);if((d==null?void 0:d.type)==="code")break;if((d==null?void 0:d.type)==="blockquote"){const p=d,g=p.raw+`
`+r.join(`
`),m=this.blockquote(g);n[n.length-1]=m,i=i.substring(0,i.length-p.raw.length)+m.raw,a=a.substring(0,a.length-p.text.length)+m.text;break}else if((d==null?void 0:d.type)==="list"){const p=d,g=p.raw+`
`+r.join(`
`),m=this.list(g);n[n.length-1]=m,i=i.substring(0,i.length-d.raw.length)+m.raw,a=a.substring(0,a.length-p.raw.length)+m.raw,r=g.substring(n.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:i,tokens:n,text:a}}}list(e){let t=this.rules.block.list.exec(e);if(t){let r=t[1].trim();const i=r.length>1,a={type:"list",raw:"",ordered:i,start:i?+r.slice(0,-1):"",loose:!1,items:[]};r=i?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=i?r:"[*+-]");const n=this.rules.other.listItemRegex(r);let o=!1;for(;e;){let c=!1,l="",h="";if(!(t=n.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let u=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,b=>" ".repeat(3*b.length)),d=e.split(`
`,1)[0],p=!u.trim(),g=0;if(this.options.pedantic?(g=2,h=u.trimStart()):p?g=t[1].length+1:(g=t[2].search(this.rules.other.nonSpaceChar),g=g>4?1:g,h=u.slice(g),g+=t[1].length),p&&this.rules.other.blankLine.test(d)&&(l+=d+`
`,e=e.substring(d.length+1),c=!0),!c){const b=this.rules.other.nextBulletRegex(g),x=this.rules.other.hrRegex(g),k=this.rules.other.fencesBeginRegex(g),L=this.rules.other.headingBeginRegex(g),B=this.rules.other.htmlBeginRegex(g);for(;e;){const C=e.split(`
`,1)[0];let w;if(d=C,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),w=d):w=d.replace(this.rules.other.tabCharGlobal,"    "),k.test(d)||L.test(d)||B.test(d)||b.test(d)||x.test(d))break;if(w.search(this.rules.other.nonSpaceChar)>=g||!d.trim())h+=`
`+w.slice(g);else{if(p||u.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||k.test(u)||L.test(u)||x.test(u))break;h+=`
`+d}!p&&!d.trim()&&(p=!0),l+=C+`
`,e=e.substring(C.length+1),u=w.slice(g)}}a.loose||(o?a.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(o=!0));let m=null,y;this.options.gfm&&(m=this.rules.other.listIsTask.exec(h),m&&(y=m[0]!=="[ ] ",h=h.replace(this.rules.other.listReplaceTask,""))),a.items.push({type:"list_item",raw:l,task:!!m,checked:y,loose:!1,text:h,tokens:[]}),a.raw+=l}const s=a.items.at(-1);if(s)s.raw=s.raw.trimEnd(),s.text=s.text.trimEnd();else return;a.raw=a.raw.trimEnd();for(let c=0;c<a.items.length;c++)if(this.lexer.state.top=!1,a.items[c].tokens=this.lexer.blockTokens(a.items[c].text,[]),!a.loose){const l=a.items[c].tokens.filter(u=>u.type==="space"),h=l.length>0&&l.some(u=>this.rules.other.anyLine.test(u.raw));a.loose=h}if(a.loose)for(let c=0;c<a.items.length;c++)a.items[c].loose=!0;return a}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const r=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),i=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:r,raw:t[0],href:i,title:a}}}table(e){var o;const t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;const r=sl(t[1]),i=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),a=(o=t[3])!=null&&o.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],n={type:"table",raw:t[0],header:[],align:[],rows:[]};if(r.length===i.length){for(const s of i)this.rules.other.tableAlignRight.test(s)?n.align.push("right"):this.rules.other.tableAlignCenter.test(s)?n.align.push("center"):this.rules.other.tableAlignLeft.test(s)?n.align.push("left"):n.align.push(null);for(let s=0;s<r.length;s++)n.header.push({text:r[s],tokens:this.lexer.inline(r[s]),header:!0,align:n.align[s]});for(const s of a)n.rows.push(sl(s,n.header.length).map((c,l)=>({text:c,tokens:this.lexer.inline(c),header:!1,align:n.align[l]})));return n}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const r=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:r,tokens:this.lexer.inline(r)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const r=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(r)){if(!this.rules.other.endAngleBracket.test(r))return;const n=ei(r.slice(0,-1),"\\");if((r.length-n.length)%2===0)return}else{const n=hC(t[2],"()");if(n===-2)return;if(n>-1){const s=(t[0].indexOf("!")===0?5:4)+t[1].length+n;t[2]=t[2].substring(0,n),t[0]=t[0].substring(0,s).trim(),t[3]=""}}let i=t[2],a="";if(this.options.pedantic){const n=this.rules.other.pedanticHrefTitle.exec(i);n&&(i=n[1],a=n[3])}else a=t[3]?t[3].slice(1,-1):"";return i=i.trim(),this.rules.other.startAngleBracket.test(i)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(r)?i=i.slice(1):i=i.slice(1,-1)),ol(t,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:a&&a.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let r;if((r=this.rules.inline.reflink.exec(e))||(r=this.rules.inline.nolink.exec(e))){const i=(r[2]||r[1]).replace(this.rules.other.multipleSpaceGlobal," "),a=t[i.toLowerCase()];if(!a){const n=r[0].charAt(0);return{type:"text",raw:n,text:n}}return ol(r,a,r[0],this.lexer,this.rules)}}emStrong(e,t,r=""){let i=this.rules.inline.emStrongLDelim.exec(e);if(!i||i[3]&&r.match(this.rules.other.unicodeAlphaNumeric))return;if(!(i[1]||i[2]||"")||!r||this.rules.inline.punctuation.exec(r)){const n=[...i[0]].length-1;let o,s,c=n,l=0;const h=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,t=t.slice(-1*e.length+n);(i=h.exec(t))!=null;){if(o=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!o)continue;if(s=[...o].length,i[3]||i[4]){c+=s;continue}else if((i[5]||i[6])&&n%3&&!((n+s)%3)){l+=s;continue}if(c-=s,c>0)continue;s=Math.min(s,s+c+l);const u=[...i[0]][0].length,d=e.slice(0,n+i.index+u+s);if(Math.min(n,s)%2){const g=d.slice(1,-1);return{type:"em",raw:d,text:g,tokens:this.lexer.inlineTokens(g)}}const p=d.slice(2,-2);return{type:"strong",raw:d,text:p,tokens:this.lexer.inlineTokens(p)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let r=t[2].replace(this.rules.other.newLineCharGlobal," ");const i=this.rules.other.nonSpaceChar.test(r),a=this.rules.other.startingSpaceChar.test(r)&&this.rules.other.endingSpaceChar.test(r);return i&&a&&(r=r.substring(1,r.length-1)),{type:"codespan",raw:t[0],text:r}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let r,i;return t[2]==="@"?(r=t[1],i="mailto:"+r):(r=t[1],i=r),{type:"link",raw:t[0],text:r,href:i,tokens:[{type:"text",raw:r,text:r}]}}}url(e){var r;let t;if(t=this.rules.inline.url.exec(e)){let i,a;if(t[2]==="@")i=t[0],a="mailto:"+i;else{let n;do n=t[0],t[0]=((r=this.rules.inline._backpedal.exec(t[0]))==null?void 0:r[0])??"";while(n!==t[0]);i=t[0],t[1]==="www."?a="http://"+t[0]:a=t[0]}return{type:"link",raw:t[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const r=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:r}}}},Me=class Zn{constructor(t){wt(this,"tokens");wt(this,"options");wt(this,"state");wt(this,"tokenizer");wt(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||cr,this.options.tokenizer=this.options.tokenizer||new Ca,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const r={other:Vt,block:Wi.normal,inline:ti.normal};this.options.pedantic?(r.block=Wi.pedantic,r.inline=ti.pedantic):this.options.gfm&&(r.block=Wi.gfm,this.options.breaks?r.inline=ti.breaks:r.inline=ti.gfm),this.tokenizer.rules=r}static get rules(){return{block:Wi,inline:ti}}static lex(t,r){return new Zn(r).lex(t)}static lexInline(t,r){return new Zn(r).inlineTokens(t)}lex(t){t=t.replace(Vt.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let r=0;r<this.inlineQueue.length;r++){const i=this.inlineQueue[r];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,r=[],i=!1){var a,n,o;for(this.options.pedantic&&(t=t.replace(Vt.tabCharGlobal,"    ").replace(Vt.spaceLine,""));t;){let s;if((n=(a=this.options.extensions)==null?void 0:a.block)!=null&&n.some(l=>(s=l.call({lexer:this},t,r))?(t=t.substring(s.raw.length),r.push(s),!0):!1))continue;if(s=this.tokenizer.space(t)){t=t.substring(s.raw.length);const l=r.at(-1);s.raw.length===1&&l!==void 0?l.raw+=`
`:r.push(s);continue}if(s=this.tokenizer.code(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(s=this.tokenizer.fences(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.heading(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.hr(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.blockquote(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.list(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.html(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.def(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.lheading(t)){t=t.substring(s.raw.length),r.push(s);continue}let c=t;if((o=this.options.extensions)!=null&&o.startBlock){let l=1/0;const h=t.slice(1);let u;this.options.extensions.startBlock.forEach(d=>{u=d.call({lexer:this},h),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(c=t.substring(0,l+1))}if(this.state.top&&(s=this.tokenizer.paragraph(c))){const l=r.at(-1);i&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s),i=c.length!==t.length,t=t.substring(s.raw.length);continue}if(s=this.tokenizer.text(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,r}inline(t,r=[]){return this.inlineQueue.push({src:t,tokens:r}),r}inlineTokens(t,r=[]){var s,c,l;let i=t,a=null;if(this.tokens.links){const h=Object.keys(this.tokens.links);if(h.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)h.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,a.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(a=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let n=!1,o="";for(;t;){n||(o=""),n=!1;let h;if((c=(s=this.options.extensions)==null?void 0:s.inline)!=null&&c.some(d=>(h=d.call({lexer:this},t,r))?(t=t.substring(h.raw.length),r.push(h),!0):!1))continue;if(h=this.tokenizer.escape(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.tag(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.link(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(h.raw.length);const d=r.at(-1);h.type==="text"&&(d==null?void 0:d.type)==="text"?(d.raw+=h.raw,d.text+=h.text):r.push(h);continue}if(h=this.tokenizer.emStrong(t,i,o)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.codespan(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.br(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.del(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.autolink(t)){t=t.substring(h.raw.length),r.push(h);continue}if(!this.state.inLink&&(h=this.tokenizer.url(t))){t=t.substring(h.raw.length),r.push(h);continue}let u=t;if((l=this.options.extensions)!=null&&l.startInline){let d=1/0;const p=t.slice(1);let g;this.options.extensions.startInline.forEach(m=>{g=m.call({lexer:this},p),typeof g=="number"&&g>=0&&(d=Math.min(d,g))}),d<1/0&&d>=0&&(u=t.substring(0,d+1))}if(h=this.tokenizer.inlineText(u)){t=t.substring(h.raw.length),h.raw.slice(-1)!=="_"&&(o=h.raw.slice(-1)),n=!0;const d=r.at(-1);(d==null?void 0:d.type)==="text"?(d.raw+=h.raw,d.text+=h.text):r.push(h);continue}if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}else throw new Error(d)}}return r}},wa=class{constructor(e){wt(this,"options");wt(this,"parser");this.options=e||cr}space(e){return""}code({text:e,lang:t,escaped:r}){var n;const i=(n=(t||"").match(Vt.notSpaceStart))==null?void 0:n[0],a=e.replace(Vt.endingNewline,"")+`
`;return i?'<pre><code class="language-'+fe(i)+'">'+(r?a:fe(a,!0))+`</code></pre>
`:"<pre><code>"+(r?a:fe(a,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){const t=e.ordered,r=e.start;let i="";for(let o=0;o<e.items.length;o++){const s=e.items[o];i+=this.listitem(s)}const a=t?"ol":"ul",n=t&&r!==1?' start="'+r+'"':"";return"<"+a+n+`>
`+i+"</"+a+`>
`}listitem(e){var r;let t="";if(e.task){const i=this.checkbox({checked:!!e.checked});e.loose?((r=e.tokens[0])==null?void 0:r.type)==="paragraph"?(e.tokens[0].text=i+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=i+" "+fe(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:i+" ",text:i+" ",escaped:!0}):t+=i+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",r="";for(let a=0;a<e.header.length;a++)r+=this.tablecell(e.header[a]);t+=this.tablerow({text:r});let i="";for(let a=0;a<e.rows.length;a++){const n=e.rows[a];r="";for(let o=0;o<n.length;o++)r+=this.tablecell(n[o]);i+=this.tablerow({text:r})}return i&&(i=`<tbody>${i}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+i+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const t=this.parser.parseInline(e.tokens),r=e.header?"th":"td";return(e.align?`<${r} align="${e.align}">`:`<${r}>`)+t+`</${r}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${fe(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:r}){const i=this.parser.parseInline(r),a=nl(e);if(a===null)return i;e=a;let n='<a href="'+e+'"';return t&&(n+=' title="'+fe(t)+'"'),n+=">"+i+"</a>",n}image({href:e,title:t,text:r,tokens:i}){i&&(r=this.parser.parseInline(i,this.parser.textRenderer));const a=nl(e);if(a===null)return fe(r);e=a;let n=`<img src="${e}" alt="${r}"`;return t&&(n+=` title="${fe(t)}"`),n+=">",n}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:fe(e.text)}},Ys=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},Ee=class Kn{constructor(t){wt(this,"options");wt(this,"renderer");wt(this,"textRenderer");this.options=t||cr,this.options.renderer=this.options.renderer||new wa,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Ys}static parse(t,r){return new Kn(r).parse(t)}static parseInline(t,r){return new Kn(r).parseInline(t)}parse(t,r=!0){var a,n;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((n=(a=this.options.extensions)==null?void 0:a.renderers)!=null&&n[s.type]){const l=s,h=this.options.extensions.renderers[l.type].call({parser:this},l);if(h!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){i+=h||"";continue}}const c=s;switch(c.type){case"space":{i+=this.renderer.space(c);continue}case"hr":{i+=this.renderer.hr(c);continue}case"heading":{i+=this.renderer.heading(c);continue}case"code":{i+=this.renderer.code(c);continue}case"table":{i+=this.renderer.table(c);continue}case"blockquote":{i+=this.renderer.blockquote(c);continue}case"list":{i+=this.renderer.list(c);continue}case"html":{i+=this.renderer.html(c);continue}case"paragraph":{i+=this.renderer.paragraph(c);continue}case"text":{let l=c,h=this.renderer.text(l);for(;o+1<t.length&&t[o+1].type==="text";)l=t[++o],h+=`
`+this.renderer.text(l);r?i+=this.renderer.paragraph({type:"paragraph",raw:h,text:h,tokens:[{type:"text",raw:h,text:h,escaped:!0}]}):i+=h;continue}default:{const l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return i}parseInline(t,r=this.renderer){var a,n;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((n=(a=this.options.extensions)==null?void 0:a.renderers)!=null&&n[s.type]){const l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){i+=l||"";continue}}const c=s;switch(c.type){case"escape":{i+=r.text(c);break}case"html":{i+=r.html(c);break}case"link":{i+=r.link(c);break}case"image":{i+=r.image(c);break}case"strong":{i+=r.strong(c);break}case"em":{i+=r.em(c);break}case"codespan":{i+=r.codespan(c);break}case"br":{i+=r.br(c);break}case"del":{i+=r.del(c);break}case"text":{i+=r.text(c);break}default:{const l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return i}},vn,Qi=(vn=class{constructor(e){wt(this,"options");wt(this,"block");this.options=e||cr}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?Me.lex:Me.lexInline}provideParser(){return this.block?Ee.parse:Ee.parseInline}},wt(vn,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),vn),dC=class{constructor(...e){wt(this,"defaults",Ns());wt(this,"options",this.setOptions);wt(this,"parse",this.parseMarkdown(!0));wt(this,"parseInline",this.parseMarkdown(!1));wt(this,"Parser",Ee);wt(this,"Renderer",wa);wt(this,"TextRenderer",Ys);wt(this,"Lexer",Me);wt(this,"Tokenizer",Ca);wt(this,"Hooks",Qi);this.use(...e)}walkTokens(e,t){var i,a;let r=[];for(const n of e)switch(r=r.concat(t.call(this,n)),n.type){case"table":{const o=n;for(const s of o.header)r=r.concat(this.walkTokens(s.tokens,t));for(const s of o.rows)for(const c of s)r=r.concat(this.walkTokens(c.tokens,t));break}case"list":{const o=n;r=r.concat(this.walkTokens(o.items,t));break}default:{const o=n;(a=(i=this.defaults.extensions)==null?void 0:i.childTokens)!=null&&a[o.type]?this.defaults.extensions.childTokens[o.type].forEach(s=>{const c=o[s].flat(1/0);r=r.concat(this.walkTokens(c,t))}):o.tokens&&(r=r.concat(this.walkTokens(o.tokens,t)))}}return r}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(r=>{const i={...r};if(i.async=this.defaults.async||i.async||!1,r.extensions&&(r.extensions.forEach(a=>{if(!a.name)throw new Error("extension name required");if("renderer"in a){const n=t.renderers[a.name];n?t.renderers[a.name]=function(...o){let s=a.renderer.apply(this,o);return s===!1&&(s=n.apply(this,o)),s}:t.renderers[a.name]=a.renderer}if("tokenizer"in a){if(!a.level||a.level!=="block"&&a.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const n=t[a.level];n?n.unshift(a.tokenizer):t[a.level]=[a.tokenizer],a.start&&(a.level==="block"?t.startBlock?t.startBlock.push(a.start):t.startBlock=[a.start]:a.level==="inline"&&(t.startInline?t.startInline.push(a.start):t.startInline=[a.start]))}"childTokens"in a&&a.childTokens&&(t.childTokens[a.name]=a.childTokens)}),i.extensions=t),r.renderer){const a=this.defaults.renderer||new wa(this.defaults);for(const n in r.renderer){if(!(n in a))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;const o=n,s=r.renderer[o],c=a[o];a[o]=(...l)=>{let h=s.apply(a,l);return h===!1&&(h=c.apply(a,l)),h||""}}i.renderer=a}if(r.tokenizer){const a=this.defaults.tokenizer||new Ca(this.defaults);for(const n in r.tokenizer){if(!(n in a))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;const o=n,s=r.tokenizer[o],c=a[o];a[o]=(...l)=>{let h=s.apply(a,l);return h===!1&&(h=c.apply(a,l)),h}}i.tokenizer=a}if(r.hooks){const a=this.defaults.hooks||new Qi;for(const n in r.hooks){if(!(n in a))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;const o=n,s=r.hooks[o],c=a[o];Qi.passThroughHooks.has(n)?a[o]=l=>{if(this.defaults.async)return Promise.resolve(s.call(a,l)).then(u=>c.call(a,u));const h=s.call(a,l);return c.call(a,h)}:a[o]=(...l)=>{let h=s.apply(a,l);return h===!1&&(h=c.apply(a,l)),h}}i.hooks=a}if(r.walkTokens){const a=this.defaults.walkTokens,n=r.walkTokens;i.walkTokens=function(o){let s=[];return s.push(n.call(this,o)),a&&(s=s.concat(a.call(this,o))),s}}this.defaults={...this.defaults,...i}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return Me.lex(e,t??this.defaults)}parser(e,t){return Ee.parse(e,t??this.defaults)}parseMarkdown(e){return(r,i)=>{const a={...i},n={...this.defaults,...a},o=this.onError(!!n.silent,!!n.async);if(this.defaults.async===!0&&a.async===!1)return o(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof r>"u"||r===null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));n.hooks&&(n.hooks.options=n,n.hooks.block=e);const s=n.hooks?n.hooks.provideLexer():e?Me.lex:Me.lexInline,c=n.hooks?n.hooks.provideParser():e?Ee.parse:Ee.parseInline;if(n.async)return Promise.resolve(n.hooks?n.hooks.preprocess(r):r).then(l=>s(l,n)).then(l=>n.hooks?n.hooks.processAllTokens(l):l).then(l=>n.walkTokens?Promise.all(this.walkTokens(l,n.walkTokens)).then(()=>l):l).then(l=>c(l,n)).then(l=>n.hooks?n.hooks.postprocess(l):l).catch(o);try{n.hooks&&(r=n.hooks.preprocess(r));let l=s(r,n);n.hooks&&(l=n.hooks.processAllTokens(l)),n.walkTokens&&this.walkTokens(l,n.walkTokens);let h=c(l,n);return n.hooks&&(h=n.hooks.postprocess(h)),h}catch(l){return o(l)}}}onError(e,t){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+fe(r.message+"",!0)+"</pre>";return t?Promise.resolve(i):i}if(t)return Promise.reject(r);throw r}}},ir=new dC;function yt(e,t){return ir.parse(e,t)}yt.options=yt.setOptions=function(e){return ir.setOptions(e),yt.defaults=ir.defaults,ku(yt.defaults),yt};yt.getDefaults=Ns;yt.defaults=cr;yt.use=function(...e){return ir.use(...e),yt.defaults=ir.defaults,ku(yt.defaults),yt};yt.walkTokens=function(e,t){return ir.walkTokens(e,t)};yt.parseInline=ir.parseInline;yt.Parser=Ee;yt.parser=Ee.parse;yt.Renderer=wa;yt.TextRenderer=Ys;yt.Lexer=Me;yt.lexer=Me.lex;yt.Tokenizer=Ca;yt.Hooks=Qi;yt.parse=yt;yt.options;yt.setOptions;yt.use;yt.walkTokens;yt.parseInline;Ee.parse;Me.lex;function Du(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var i=Array.from(typeof e=="string"?[e]:e);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var a=i.reduce(function(s,c){var l=c.match(/\n([\t ]+|(?!\s).)/g);return l?s.concat(l.map(function(h){var u,d;return(d=(u=h.match(/[\t ]/g))===null||u===void 0?void 0:u.length)!==null&&d!==void 0?d:0})):s},[]);if(a.length){var n=new RegExp(`
[	 ]{`+Math.min.apply(Math,a)+"}","g");i=i.map(function(s){return s.replace(n,`
`)})}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];return t.forEach(function(s,c){var l=o.match(/(?:^|\n)( *)$/),h=l?l[1]:"",u=s;typeof s=="string"&&s.includes(`
`)&&(u=String(s).split(`
`).map(function(d,p){return p===0?d:""+h+d}).join(`
`)),o+=u+i[c+1]}),o}function Ou(e,{markdownAutoWrap:t}){const i=e.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),a=Du(i);return t===!1?a.replace(/ /g,"&nbsp;"):a}f(Ou,"preprocessMarkdown");function $u(e,t={}){const r=Ou(e,t),i=yt.lexer(r),a=[[]];let n=0;function o(s,c="normal"){s.type==="text"?s.text.split(`
`).forEach((h,u)=>{u!==0&&(n++,a.push([])),h.split(" ").forEach(d=>{d=d.replace(/&#39;/g,"'"),d&&a[n].push({content:d,type:c})})}):s.type==="strong"||s.type==="em"?s.tokens.forEach(l=>{o(l,s.type)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}return f(o,"processNode"),i.forEach(s=>{var c;s.type==="paragraph"?(c=s.tokens)==null||c.forEach(l=>{o(l)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}),a}f($u,"markdownToLines");function Ru(e,{markdownAutoWrap:t}={}){const r=yt.lexer(e);function i(a){var n,o,s;return a.type==="text"?t===!1?a.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):a.text.replace(/\n */g,"<br/>"):a.type==="strong"?`<strong>${(n=a.tokens)==null?void 0:n.map(i).join("")}</strong>`:a.type==="em"?`<em>${(o=a.tokens)==null?void 0:o.map(i).join("")}</em>`:a.type==="paragraph"?`<p>${(s=a.tokens)==null?void 0:s.map(i).join("")}</p>`:a.type==="space"?"":a.type==="html"?`${a.text}`:a.type==="escape"?a.text:`Unsupported markdown: ${a.type}`}return f(i,"output"),r.map(i).join("")}f(Ru,"markdownToHTML");function Iu(e){return Intl.Segmenter?[...new Intl.Segmenter().segment(e)].map(t=>t.segment):[...e]}f(Iu,"splitTextToChars");function Pu(e,t){const r=Iu(t.content);return Vs(e,[],r,t.type)}f(Pu,"splitWordToFitWidth");function Vs(e,t,r,i){if(r.length===0)return[{content:t.join(""),type:i},{content:"",type:i}];const[a,...n]=r,o=[...t,a];return e([{content:o.join(""),type:i}])?Vs(e,o,n,i):(t.length===0&&a&&(t.push(a),r.shift()),[{content:t.join(""),type:i},{content:r.join(""),type:i}])}f(Vs,"splitWordToFitWidthRecursion");function Nu(e,t){if(e.some(({content:r})=>r.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return ka(e,t)}f(Nu,"splitLineToFitWidth");function ka(e,t,r=[],i=[]){if(e.length===0)return i.length>0&&r.push(i),r.length>0?r:[];let a="";e[0].content===" "&&(a=" ",e.shift());const n=e.shift()??{content:" ",type:"normal"},o=[...i];if(a!==""&&o.push({content:a,type:"normal"}),o.push(n),t(o))return ka(e,t,r,o);if(i.length>0)r.push(i),e.unshift(n);else if(n.content){const[s,c]=Pu(t,n);r.push([s]),c.content&&e.unshift(c)}return ka(e,t,r)}f(ka,"splitLineToFitWidthRecursion");function Qn(e,t){t&&e.attr("style",t)}f(Qn,"applyStyle");async function zu(e,t,r,i,a=!1){const n=e.append("foreignObject");n.attr("width",`${10*r}px`),n.attr("height",`${10*r}px`);const o=n.append("xhtml:div");let s=t.label;t.label&&Or(t.label)&&(s=await fs(t.label.replace(Nr.lineBreakRegex,`
`),mt()));const c=t.isNode?"nodeLabel":"edgeLabel",l=o.append("span");l.html(s),Qn(l,t.labelStyle),l.attr("class",`${c} ${i}`),Qn(o,t.labelStyle),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("line-height","1.5"),o.style("max-width",r+"px"),o.style("text-align","center"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),a&&o.attr("class","labelBkg");let h=o.node().getBoundingClientRect();return h.width===r&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",r+"px"),h=o.node().getBoundingClientRect()),n.node()}f(zu,"addHtmlSpan");function Ha(e,t,r){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",t*r-.1+"em").attr("dy",r+"em")}f(Ha,"createTspan");function Wu(e,t,r){const i=e.append("text"),a=Ha(i,1,t);Ua(a,r);const n=a.node().getComputedTextLength();return i.remove(),n}f(Wu,"computeWidthOfText");function pC(e,t,r){var o;const i=e.append("text"),a=Ha(i,1,t);Ua(a,[{content:r,type:"normal"}]);const n=(o=a.node())==null?void 0:o.getBoundingClientRect();return n&&i.remove(),n}f(pC,"computeDimensionOfText");function qu(e,t,r,i=!1){const n=t.append("g"),o=n.insert("rect").attr("class","background").attr("style","stroke: none"),s=n.append("text").attr("y","-10.1");let c=0;for(const l of r){const h=f(d=>Wu(n,1.1,d)<=e,"checkWidth"),u=h(l)?[l]:Nu(l,h);for(const d of u){const p=Ha(s,c,1.1);Ua(p,d),c++}}if(i){const l=s.node().getBBox(),h=2;return o.attr("x",l.x-h).attr("y",l.y-h).attr("width",l.width+2*h).attr("height",l.height+2*h),n.node()}else return s.node()}f(qu,"createFormattedText");function Ua(e,t){e.text(""),t.forEach((r,i)=>{const a=e.append("tspan").attr("font-style",r.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",r.type==="strong"?"bold":"normal");i===0?a.text(r.content):a.text(" "+r.content)})}f(Ua,"updateTextContentAndStyles");function ju(e){return e.replace(/fa[bklrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`)}f(ju,"replaceIconSubstring");var Ue=f(async(e,t="",{style:r="",isTitle:i=!1,classes:a="",useHtmlLabels:n=!0,isNode:o=!0,width:s=200,addSvgBackground:c=!1}={},l)=>{if(P.debug("XYZ createText",t,r,i,a,n,o,"addSvgBackground: ",c),n){const h=Ru(t,l),u=ju(lr(h)),d=t.replace(/\\\\/g,"\\"),p={isNode:o,label:Or(t)?d:u,labelStyle:r.replace("fill:","color:")};return await zu(e,p,s,a,c)}else{const h=t.replace(/<br\s*\/?>/g,"<br/>"),u=$u(h.replace("<br>","<br/>"),l),d=qu(s,e,u,t?c:!1);if(o){/stroke:/.exec(r)&&(r=r.replace("stroke:","lineColor:"));const p=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");pt(d).attr("style",p)}else{const p=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");pt(d).select("rect").attr("style",p.replace(/background:/g,"fill:"));const g=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");pt(d).select("text").attr("style",g)}return d}},"createText");function gn(e,t,r){if(e&&e.length){const[i,a]=t,n=Math.PI/180*r,o=Math.cos(n),s=Math.sin(n);for(const c of e){const[l,h]=c;c[0]=(l-i)*o-(h-a)*s+i,c[1]=(l-i)*s+(h-a)*o+a}}}function fC(e,t){return e[0]===t[0]&&e[1]===t[1]}function gC(e,t,r,i=1){const a=r,n=Math.max(t,.1),o=e[0]&&e[0][0]&&typeof e[0][0]=="number"?[e]:e,s=[0,0];if(a)for(const l of o)gn(l,s,a);const c=function(l,h,u){const d=[];for(const x of l){const k=[...x];fC(k[0],k[k.length-1])||k.push([k[0][0],k[0][1]]),k.length>2&&d.push(k)}const p=[];h=Math.max(h,.1);const g=[];for(const x of d)for(let k=0;k<x.length-1;k++){const L=x[k],B=x[k+1];if(L[1]!==B[1]){const C=Math.min(L[1],B[1]);g.push({ymin:C,ymax:Math.max(L[1],B[1]),x:C===L[1]?L[0]:B[0],islope:(B[0]-L[0])/(B[1]-L[1])})}}if(g.sort((x,k)=>x.ymin<k.ymin?-1:x.ymin>k.ymin?1:x.x<k.x?-1:x.x>k.x?1:x.ymax===k.ymax?0:(x.ymax-k.ymax)/Math.abs(x.ymax-k.ymax)),!g.length)return p;let m=[],y=g[0].ymin,b=0;for(;m.length||g.length;){if(g.length){let x=-1;for(let k=0;k<g.length&&!(g[k].ymin>y);k++)x=k;g.splice(0,x+1).forEach(k=>{m.push({s:y,edge:k})})}if(m=m.filter(x=>!(x.edge.ymax<=y)),m.sort((x,k)=>x.edge.x===k.edge.x?0:(x.edge.x-k.edge.x)/Math.abs(x.edge.x-k.edge.x)),(u!==1||b%h==0)&&m.length>1)for(let x=0;x<m.length;x+=2){const k=x+1;if(k>=m.length)break;const L=m[x].edge,B=m[k].edge;p.push([[Math.round(L.x),y],[Math.round(B.x),y]])}y+=u,m.forEach(x=>{x.edge.x=x.edge.x+u*x.edge.islope}),b++}return p}(o,n,i);if(a){for(const l of o)gn(l,s,-a);(function(l,h,u){const d=[];l.forEach(p=>d.push(...p)),gn(d,h,u)})(c,s,-a)}return c}function Ai(e,t){var r;const i=t.hachureAngle+90;let a=t.hachureGap;a<0&&(a=4*t.strokeWidth),a=Math.round(Math.max(a,.1));let n=1;return t.roughness>=1&&(((r=t.randomizer)===null||r===void 0?void 0:r.next())||Math.random())>.7&&(n=a),gC(e,a,i,n||1)}class Xs{constructor(t){this.helper=t}fillPolygons(t,r){return this._fillPolygons(t,r)}_fillPolygons(t,r){const i=Ai(t,r);return{type:"fillSketch",ops:this.renderLines(i,r)}}renderLines(t,r){const i=[];for(const a of t)i.push(...this.helper.doubleLineOps(a[0][0],a[0][1],a[1][0],a[1][1],r));return i}}function Ga(e){const t=e[0],r=e[1];return Math.sqrt(Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2))}class mC extends Xs{fillPolygons(t,r){let i=r.hachureGap;i<0&&(i=4*r.strokeWidth),i=Math.max(i,.1);const a=Ai(t,Object.assign({},r,{hachureGap:i})),n=Math.PI/180*r.hachureAngle,o=[],s=.5*i*Math.cos(n),c=.5*i*Math.sin(n);for(const[l,h]of a)Ga([l,h])&&o.push([[l[0]-s,l[1]+c],[...h]],[[l[0]+s,l[1]-c],[...h]]);return{type:"fillSketch",ops:this.renderLines(o,r)}}}class yC extends Xs{fillPolygons(t,r){const i=this._fillPolygons(t,r),a=Object.assign({},r,{hachureAngle:r.hachureAngle+90}),n=this._fillPolygons(t,a);return i.ops=i.ops.concat(n.ops),i}}class bC{constructor(t){this.helper=t}fillPolygons(t,r){const i=Ai(t,r=Object.assign({},r,{hachureAngle:0}));return this.dotsOnLines(i,r)}dotsOnLines(t,r){const i=[];let a=r.hachureGap;a<0&&(a=4*r.strokeWidth),a=Math.max(a,.1);let n=r.fillWeight;n<0&&(n=r.strokeWidth/2);const o=a/4;for(const s of t){const c=Ga(s),l=c/a,h=Math.ceil(l)-1,u=c-h*a,d=(s[0][0]+s[1][0])/2-a/4,p=Math.min(s[0][1],s[1][1]);for(let g=0;g<h;g++){const m=p+u+g*a,y=d-o+2*Math.random()*o,b=m-o+2*Math.random()*o,x=this.helper.ellipse(y,b,n,n,r);i.push(...x.ops)}}return{type:"fillSketch",ops:i}}}class xC{constructor(t){this.helper=t}fillPolygons(t,r){const i=Ai(t,r);return{type:"fillSketch",ops:this.dashedLine(i,r)}}dashedLine(t,r){const i=r.dashOffset<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashOffset,a=r.dashGap<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashGap,n=[];return t.forEach(o=>{const s=Ga(o),c=Math.floor(s/(i+a)),l=(s+a-c*(i+a))/2;let h=o[0],u=o[1];h[0]>u[0]&&(h=o[1],u=o[0]);const d=Math.atan((u[1]-h[1])/(u[0]-h[0]));for(let p=0;p<c;p++){const g=p*(i+a),m=g+i,y=[h[0]+g*Math.cos(d)+l*Math.cos(d),h[1]+g*Math.sin(d)+l*Math.sin(d)],b=[h[0]+m*Math.cos(d)+l*Math.cos(d),h[1]+m*Math.sin(d)+l*Math.sin(d)];n.push(...this.helper.doubleLineOps(y[0],y[1],b[0],b[1],r))}}),n}}class CC{constructor(t){this.helper=t}fillPolygons(t,r){const i=r.hachureGap<0?4*r.strokeWidth:r.hachureGap,a=r.zigzagOffset<0?i:r.zigzagOffset,n=Ai(t,r=Object.assign({},r,{hachureGap:i+a}));return{type:"fillSketch",ops:this.zigzagLines(n,a,r)}}zigzagLines(t,r,i){const a=[];return t.forEach(n=>{const o=Ga(n),s=Math.round(o/(2*r));let c=n[0],l=n[1];c[0]>l[0]&&(c=n[1],l=n[0]);const h=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let u=0;u<s;u++){const d=2*u*r,p=2*(u+1)*r,g=Math.sqrt(2*Math.pow(r,2)),m=[c[0]+d*Math.cos(h),c[1]+d*Math.sin(h)],y=[c[0]+p*Math.cos(h),c[1]+p*Math.sin(h)],b=[m[0]+g*Math.cos(h+Math.PI/4),m[1]+g*Math.sin(h+Math.PI/4)];a.push(...this.helper.doubleLineOps(m[0],m[1],b[0],b[1],i),...this.helper.doubleLineOps(b[0],b[1],y[0],y[1],i))}}),a}}const Jt={};class wC{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const kC=0,mn=1,ll=2,qi={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function yn(e,t){return e.type===t}function Zs(e){const t=[],r=function(o){const s=new Array;for(;o!=="";)if(o.match(/^([ \t\r\n,]+)/))o=o.substr(RegExp.$1.length);else if(o.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:kC,text:RegExp.$1},o=o.substr(RegExp.$1.length);else{if(!o.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:mn,text:`${parseFloat(RegExp.$1)}`},o=o.substr(RegExp.$1.length)}return s[s.length]={type:ll,text:""},s}(e);let i="BOD",a=0,n=r[a];for(;!yn(n,ll);){let o=0;const s=[];if(i==="BOD"){if(n.text!=="M"&&n.text!=="m")return Zs("M0,0"+e);a++,o=qi[n.text],i=n.text}else yn(n,mn)?o=qi[i]:(a++,o=qi[n.text],i=n.text);if(!(a+o<r.length))throw new Error("Path data ended short");for(let c=a;c<a+o;c++){const l=r[c];if(!yn(l,mn))throw new Error("Param not a number: "+i+","+l.text);s[s.length]=+l.text}if(typeof qi[i]!="number")throw new Error("Bad segment: "+i);{const c={key:i,data:s};t.push(c),a+=o,n=r[a],i==="M"&&(i="L"),i==="m"&&(i="l")}}return t}function Hu(e){let t=0,r=0,i=0,a=0;const n=[];for(const{key:o,data:s}of e)switch(o){case"M":n.push({key:"M",data:[...s]}),[t,r]=s,[i,a]=s;break;case"m":t+=s[0],r+=s[1],n.push({key:"M",data:[t,r]}),i=t,a=r;break;case"L":n.push({key:"L",data:[...s]}),[t,r]=s;break;case"l":t+=s[0],r+=s[1],n.push({key:"L",data:[t,r]});break;case"C":n.push({key:"C",data:[...s]}),t=s[4],r=s[5];break;case"c":{const c=s.map((l,h)=>h%2?l+r:l+t);n.push({key:"C",data:c}),t=c[4],r=c[5];break}case"Q":n.push({key:"Q",data:[...s]}),t=s[2],r=s[3];break;case"q":{const c=s.map((l,h)=>h%2?l+r:l+t);n.push({key:"Q",data:c}),t=c[2],r=c[3];break}case"A":n.push({key:"A",data:[...s]}),t=s[5],r=s[6];break;case"a":t+=s[5],r+=s[6],n.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,r]});break;case"H":n.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],n.push({key:"H",data:[t]});break;case"V":n.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],n.push({key:"V",data:[r]});break;case"S":n.push({key:"S",data:[...s]}),t=s[2],r=s[3];break;case"s":{const c=s.map((l,h)=>h%2?l+r:l+t);n.push({key:"S",data:c}),t=c[2],r=c[3];break}case"T":n.push({key:"T",data:[...s]}),t=s[0],r=s[1];break;case"t":t+=s[0],r+=s[1],n.push({key:"T",data:[t,r]});break;case"Z":case"z":n.push({key:"Z",data:[]}),t=i,r=a}return n}function Uu(e){const t=[];let r="",i=0,a=0,n=0,o=0,s=0,c=0;for(const{key:l,data:h}of e){switch(l){case"M":t.push({key:"M",data:[...h]}),[i,a]=h,[n,o]=h;break;case"C":t.push({key:"C",data:[...h]}),i=h[4],a=h[5],s=h[2],c=h[3];break;case"L":t.push({key:"L",data:[...h]}),[i,a]=h;break;case"H":i=h[0],t.push({key:"L",data:[i,a]});break;case"V":a=h[0],t.push({key:"L",data:[i,a]});break;case"S":{let u=0,d=0;r==="C"||r==="S"?(u=i+(i-s),d=a+(a-c)):(u=i,d=a),t.push({key:"C",data:[u,d,...h]}),s=h[0],c=h[1],i=h[2],a=h[3];break}case"T":{const[u,d]=h;let p=0,g=0;r==="Q"||r==="T"?(p=i+(i-s),g=a+(a-c)):(p=i,g=a);const m=i+2*(p-i)/3,y=a+2*(g-a)/3,b=u+2*(p-u)/3,x=d+2*(g-d)/3;t.push({key:"C",data:[m,y,b,x,u,d]}),s=p,c=g,i=u,a=d;break}case"Q":{const[u,d,p,g]=h,m=i+2*(u-i)/3,y=a+2*(d-a)/3,b=p+2*(u-p)/3,x=g+2*(d-g)/3;t.push({key:"C",data:[m,y,b,x,p,g]}),s=u,c=d,i=p,a=g;break}case"A":{const u=Math.abs(h[0]),d=Math.abs(h[1]),p=h[2],g=h[3],m=h[4],y=h[5],b=h[6];u===0||d===0?(t.push({key:"C",data:[i,a,y,b,y,b]}),i=y,a=b):(i!==y||a!==b)&&(Gu(i,a,y,b,u,d,p,g,m).forEach(function(x){t.push({key:"C",data:x})}),i=y,a=b);break}case"Z":t.push({key:"Z",data:[]}),i=n,a=o}r=l}return t}function ri(e,t,r){return[e*Math.cos(r)-t*Math.sin(r),e*Math.sin(r)+t*Math.cos(r)]}function Gu(e,t,r,i,a,n,o,s,c,l){const h=(u=o,Math.PI*u/180);var u;let d=[],p=0,g=0,m=0,y=0;if(l)[p,g,m,y]=l;else{[e,t]=ri(e,t,-h),[r,i]=ri(r,i,-h);const R=(e-r)/2,M=(t-i)/2;let O=R*R/(a*a)+M*M/(n*n);O>1&&(O=Math.sqrt(O),a*=O,n*=O);const F=a*a,I=n*n,D=F*I-F*M*M-I*R*R,W=F*M*M+I*R*R,j=(s===c?-1:1)*Math.sqrt(Math.abs(D/W));m=j*a*M/n+(e+r)/2,y=j*-n*R/a+(t+i)/2,p=Math.asin(parseFloat(((t-y)/n).toFixed(9))),g=Math.asin(parseFloat(((i-y)/n).toFixed(9))),e<m&&(p=Math.PI-p),r<m&&(g=Math.PI-g),p<0&&(p=2*Math.PI+p),g<0&&(g=2*Math.PI+g),c&&p>g&&(p-=2*Math.PI),!c&&g>p&&(g-=2*Math.PI)}let b=g-p;if(Math.abs(b)>120*Math.PI/180){const R=g,M=r,O=i;g=c&&g>p?p+120*Math.PI/180*1:p+120*Math.PI/180*-1,d=Gu(r=m+a*Math.cos(g),i=y+n*Math.sin(g),M,O,a,n,o,0,c,[g,R,m,y])}b=g-p;const x=Math.cos(p),k=Math.sin(p),L=Math.cos(g),B=Math.sin(g),C=Math.tan(b/4),w=4/3*a*C,v=4/3*n*C,E=[e,t],_=[e+w*k,t-v*x],S=[r+w*B,i-v*L],$=[r,i];if(_[0]=2*E[0]-_[0],_[1]=2*E[1]-_[1],l)return[_,S,$].concat(d);{d=[_,S,$].concat(d);const R=[];for(let M=0;M<d.length;M+=3){const O=ri(d[M][0],d[M][1],h),F=ri(d[M+1][0],d[M+1][1],h),I=ri(d[M+2][0],d[M+2][1],h);R.push([O[0],O[1],F[0],F[1],I[0],I[1]])}return R}}const vC={randOffset:function(e,t){return nt(e,t)},randOffsetWithRange:function(e,t,r){return va(e,t,r)},ellipse:function(e,t,r,i,a){const n=Vu(r,i,a);return Jn(e,t,a,n).opset},doubleLineOps:function(e,t,r,i,a){return qe(e,t,r,i,a,!0)}};function Yu(e,t,r,i,a){return{type:"path",ops:qe(e,t,r,i,a)}}function Ji(e,t,r){const i=(e||[]).length;if(i>2){const a=[];for(let n=0;n<i-1;n++)a.push(...qe(e[n][0],e[n][1],e[n+1][0],e[n+1][1],r));return t&&a.push(...qe(e[i-1][0],e[i-1][1],e[0][0],e[0][1],r)),{type:"path",ops:a}}return i===2?Yu(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function SC(e,t,r,i,a){return function(n,o){return Ji(n,!0,o)}([[e,t],[e+r,t],[e+r,t+i],[e,t+i]],a)}function cl(e,t){if(e.length){const r=typeof e[0][0]=="number"?[e]:e,i=ji(r[0],1*(1+.2*t.roughness),t),a=t.disableMultiStroke?[]:ji(r[0],1.5*(1+.22*t.roughness),dl(t));for(let n=1;n<r.length;n++){const o=r[n];if(o.length){const s=ji(o,1*(1+.2*t.roughness),t),c=t.disableMultiStroke?[]:ji(o,1.5*(1+.22*t.roughness),dl(t));for(const l of s)l.op!=="move"&&i.push(l);for(const l of c)l.op!=="move"&&a.push(l)}}return{type:"path",ops:i.concat(a)}}return{type:"path",ops:[]}}function Vu(e,t,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(e/2,2)+Math.pow(t/2,2))/2)),a=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),n=2*Math.PI/a;let o=Math.abs(e/2),s=Math.abs(t/2);const c=1-r.curveFitting;return o+=nt(o*c,r),s+=nt(s*c,r),{increment:n,rx:o,ry:s}}function Jn(e,t,r,i){const[a,n]=pl(i.increment,e,t,i.rx,i.ry,1,i.increment*va(.1,va(.4,1,r),r),r);let o=Sa(a,null,r);if(!r.disableMultiStroke&&r.roughness!==0){const[s]=pl(i.increment,e,t,i.rx,i.ry,1.5,0,r),c=Sa(s,null,r);o=o.concat(c)}return{estimatedPoints:n,opset:{type:"path",ops:o}}}function hl(e,t,r,i,a,n,o,s,c){const l=e,h=t;let u=Math.abs(r/2),d=Math.abs(i/2);u+=nt(.01*u,c),d+=nt(.01*d,c);let p=a,g=n;for(;p<0;)p+=2*Math.PI,g+=2*Math.PI;g-p>2*Math.PI&&(p=0,g=2*Math.PI);const m=2*Math.PI/c.curveStepCount,y=Math.min(m/2,(g-p)/2),b=fl(y,l,h,u,d,p,g,1,c);if(!c.disableMultiStroke){const x=fl(y,l,h,u,d,p,g,1.5,c);b.push(...x)}return o&&(s?b.push(...qe(l,h,l+u*Math.cos(p),h+d*Math.sin(p),c),...qe(l,h,l+u*Math.cos(g),h+d*Math.sin(g),c)):b.push({op:"lineTo",data:[l,h]},{op:"lineTo",data:[l+u*Math.cos(p),h+d*Math.sin(p)]})),{type:"path",ops:b}}function ul(e,t){const r=Uu(Hu(Zs(e))),i=[];let a=[0,0],n=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":n=[s[0],s[1]],a=[s[0],s[1]];break;case"L":i.push(...qe(n[0],n[1],s[0],s[1],t)),n=[s[0],s[1]];break;case"C":{const[c,l,h,u,d,p]=s;i.push(...TC(c,l,h,u,d,p,n,t)),n=[d,p];break}case"Z":i.push(...qe(n[0],n[1],a[0],a[1],t)),n=[a[0],a[1]]}return{type:"path",ops:i}}function bn(e,t){const r=[];for(const i of e)if(i.length){const a=t.maxRandomnessOffset||0,n=i.length;if(n>2){r.push({op:"move",data:[i[0][0]+nt(a,t),i[0][1]+nt(a,t)]});for(let o=1;o<n;o++)r.push({op:"lineTo",data:[i[o][0]+nt(a,t),i[o][1]+nt(a,t)]})}}return{type:"fillPath",ops:r}}function mr(e,t){return function(r,i){let a=r.fillStyle||"hachure";if(!Jt[a])switch(a){case"zigzag":Jt[a]||(Jt[a]=new mC(i));break;case"cross-hatch":Jt[a]||(Jt[a]=new yC(i));break;case"dots":Jt[a]||(Jt[a]=new bC(i));break;case"dashed":Jt[a]||(Jt[a]=new xC(i));break;case"zigzag-line":Jt[a]||(Jt[a]=new CC(i));break;default:a="hachure",Jt[a]||(Jt[a]=new Xs(i))}return Jt[a]}(t,vC).fillPolygons(e,t)}function dl(e){const t=Object.assign({},e);return t.randomizer=void 0,e.seed&&(t.seed=e.seed+1),t}function Xu(e){return e.randomizer||(e.randomizer=new wC(e.seed||0)),e.randomizer.next()}function va(e,t,r,i=1){return r.roughness*i*(Xu(r)*(t-e)+e)}function nt(e,t,r=1){return va(-e,e,t,r)}function qe(e,t,r,i,a,n=!1){const o=n?a.disableMultiStrokeFill:a.disableMultiStroke,s=ts(e,t,r,i,a,!0,!1);if(o)return s;const c=ts(e,t,r,i,a,!0,!0);return s.concat(c)}function ts(e,t,r,i,a,n,o){const s=Math.pow(e-r,2)+Math.pow(t-i,2),c=Math.sqrt(s);let l=1;l=c<200?1:c>500?.4:-.0016668*c+1.233334;let h=a.maxRandomnessOffset||0;h*h*100>s&&(h=c/10);const u=h/2,d=.2+.2*Xu(a);let p=a.bowing*a.maxRandomnessOffset*(i-t)/200,g=a.bowing*a.maxRandomnessOffset*(e-r)/200;p=nt(p,a,l),g=nt(g,a,l);const m=[],y=()=>nt(u,a,l),b=()=>nt(h,a,l),x=a.preserveVertices;return o?m.push({op:"move",data:[e+(x?0:y()),t+(x?0:y())]}):m.push({op:"move",data:[e+(x?0:nt(h,a,l)),t+(x?0:nt(h,a,l))]}),o?m.push({op:"bcurveTo",data:[p+e+(r-e)*d+y(),g+t+(i-t)*d+y(),p+e+2*(r-e)*d+y(),g+t+2*(i-t)*d+y(),r+(x?0:y()),i+(x?0:y())]}):m.push({op:"bcurveTo",data:[p+e+(r-e)*d+b(),g+t+(i-t)*d+b(),p+e+2*(r-e)*d+b(),g+t+2*(i-t)*d+b(),r+(x?0:b()),i+(x?0:b())]}),m}function ji(e,t,r){if(!e.length)return[];const i=[];i.push([e[0][0]+nt(t,r),e[0][1]+nt(t,r)]),i.push([e[0][0]+nt(t,r),e[0][1]+nt(t,r)]);for(let a=1;a<e.length;a++)i.push([e[a][0]+nt(t,r),e[a][1]+nt(t,r)]),a===e.length-1&&i.push([e[a][0]+nt(t,r),e[a][1]+nt(t,r)]);return Sa(i,null,r)}function Sa(e,t,r){const i=e.length,a=[];if(i>3){const n=[],o=1-r.curveTightness;a.push({op:"move",data:[e[1][0],e[1][1]]});for(let s=1;s+2<i;s++){const c=e[s];n[0]=[c[0],c[1]],n[1]=[c[0]+(o*e[s+1][0]-o*e[s-1][0])/6,c[1]+(o*e[s+1][1]-o*e[s-1][1])/6],n[2]=[e[s+1][0]+(o*e[s][0]-o*e[s+2][0])/6,e[s+1][1]+(o*e[s][1]-o*e[s+2][1])/6],n[3]=[e[s+1][0],e[s+1][1]],a.push({op:"bcurveTo",data:[n[1][0],n[1][1],n[2][0],n[2][1],n[3][0],n[3][1]]})}}else i===3?(a.push({op:"move",data:[e[1][0],e[1][1]]}),a.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):i===2&&a.push(...ts(e[0][0],e[0][1],e[1][0],e[1][1],r,!0,!0));return a}function pl(e,t,r,i,a,n,o,s){const c=[],l=[];if(s.roughness===0){e/=4,l.push([t+i*Math.cos(-e),r+a*Math.sin(-e)]);for(let h=0;h<=2*Math.PI;h+=e){const u=[t+i*Math.cos(h),r+a*Math.sin(h)];c.push(u),l.push(u)}l.push([t+i*Math.cos(0),r+a*Math.sin(0)]),l.push([t+i*Math.cos(e),r+a*Math.sin(e)])}else{const h=nt(.5,s)-Math.PI/2;l.push([nt(n,s)+t+.9*i*Math.cos(h-e),nt(n,s)+r+.9*a*Math.sin(h-e)]);const u=2*Math.PI+h-.01;for(let d=h;d<u;d+=e){const p=[nt(n,s)+t+i*Math.cos(d),nt(n,s)+r+a*Math.sin(d)];c.push(p),l.push(p)}l.push([nt(n,s)+t+i*Math.cos(h+2*Math.PI+.5*o),nt(n,s)+r+a*Math.sin(h+2*Math.PI+.5*o)]),l.push([nt(n,s)+t+.98*i*Math.cos(h+o),nt(n,s)+r+.98*a*Math.sin(h+o)]),l.push([nt(n,s)+t+.9*i*Math.cos(h+.5*o),nt(n,s)+r+.9*a*Math.sin(h+.5*o)])}return[l,c]}function fl(e,t,r,i,a,n,o,s,c){const l=n+nt(.1,c),h=[];h.push([nt(s,c)+t+.9*i*Math.cos(l-e),nt(s,c)+r+.9*a*Math.sin(l-e)]);for(let u=l;u<=o;u+=e)h.push([nt(s,c)+t+i*Math.cos(u),nt(s,c)+r+a*Math.sin(u)]);return h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),Sa(h,null,c)}function TC(e,t,r,i,a,n,o,s){const c=[],l=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const u=s.disableMultiStroke?1:2,d=s.preserveVertices;for(let p=0;p<u;p++)p===0?c.push({op:"move",data:[o[0],o[1]]}):c.push({op:"move",data:[o[0]+(d?0:nt(l[0],s)),o[1]+(d?0:nt(l[0],s))]}),h=d?[a,n]:[a+nt(l[p],s),n+nt(l[p],s)],c.push({op:"bcurveTo",data:[e+nt(l[p],s),t+nt(l[p],s),r+nt(l[p],s),i+nt(l[p],s),h[0],h[1]]});return c}function ii(e){return[...e]}function gl(e,t=0){const r=e.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(r===3)i.push(ii(e[0]),ii(e[1]),ii(e[2]),ii(e[2]));else{const a=[];a.push(e[0],e[0]);for(let s=1;s<e.length;s++)a.push(e[s]),s===e.length-1&&a.push(e[s]);const n=[],o=1-t;i.push(ii(a[0]));for(let s=1;s+2<a.length;s++){const c=a[s];n[0]=[c[0],c[1]],n[1]=[c[0]+(o*a[s+1][0]-o*a[s-1][0])/6,c[1]+(o*a[s+1][1]-o*a[s-1][1])/6],n[2]=[a[s+1][0]+(o*a[s][0]-o*a[s+2][0])/6,a[s+1][1]+(o*a[s][1]-o*a[s+2][1])/6],n[3]=[a[s+1][0],a[s+1][1]],i.push(n[1],n[2],n[3])}}return i}function ta(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)}function _C(e,t,r){const i=ta(t,r);if(i===0)return ta(e,t);let a=((e[0]-t[0])*(r[0]-t[0])+(e[1]-t[1])*(r[1]-t[1]))/i;return a=Math.max(0,Math.min(1,a)),ta(e,Ye(t,r,a))}function Ye(e,t,r){return[e[0]+(t[0]-e[0])*r,e[1]+(t[1]-e[1])*r]}function es(e,t,r,i){const a=i||[];if(function(s,c){const l=s[c+0],h=s[c+1],u=s[c+2],d=s[c+3];let p=3*h[0]-2*l[0]-d[0];p*=p;let g=3*h[1]-2*l[1]-d[1];g*=g;let m=3*u[0]-2*d[0]-l[0];m*=m;let y=3*u[1]-2*d[1]-l[1];return y*=y,p<m&&(p=m),g<y&&(g=y),p+g}(e,t)<r){const s=e[t+0];a.length?(n=a[a.length-1],o=s,Math.sqrt(ta(n,o))>1&&a.push(s)):a.push(s),a.push(e[t+3])}else{const c=e[t+0],l=e[t+1],h=e[t+2],u=e[t+3],d=Ye(c,l,.5),p=Ye(l,h,.5),g=Ye(h,u,.5),m=Ye(d,p,.5),y=Ye(p,g,.5),b=Ye(m,y,.5);es([c,d,m,b],0,r,a),es([b,y,g,u],0,r,a)}var n,o;return a}function BC(e,t){return Ta(e,0,e.length,t)}function Ta(e,t,r,i,a){const n=a||[],o=e[t],s=e[r-1];let c=0,l=1;for(let h=t+1;h<r-1;++h){const u=_C(e[h],o,s);u>c&&(c=u,l=h)}return Math.sqrt(c)>i?(Ta(e,t,l+1,i,n),Ta(e,l,r,i,n)):(n.length||n.push(o),n.push(s)),n}function xn(e,t=.15,r){const i=[],a=(e.length-1)/3;for(let n=0;n<a;n++)es(e,3*n,t,i);return r&&r>0?Ta(i,0,i.length,r):i}const ie="none";class _a{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,r,i){return{shape:t,sets:r||[],options:i||this.defaultOptions}}line(t,r,i,a,n){const o=this._o(n);return this._d("line",[Yu(t,r,i,a,o)],o)}rectangle(t,r,i,a,n){const o=this._o(n),s=[],c=SC(t,r,i,a,o);if(o.fill){const l=[[t,r],[t+i,r],[t+i,r+a],[t,r+a]];o.fillStyle==="solid"?s.push(bn([l],o)):s.push(mr([l],o))}return o.stroke!==ie&&s.push(c),this._d("rectangle",s,o)}ellipse(t,r,i,a,n){const o=this._o(n),s=[],c=Vu(i,a,o),l=Jn(t,r,o,c);if(o.fill)if(o.fillStyle==="solid"){const h=Jn(t,r,o,c).opset;h.type="fillPath",s.push(h)}else s.push(mr([l.estimatedPoints],o));return o.stroke!==ie&&s.push(l.opset),this._d("ellipse",s,o)}circle(t,r,i,a){const n=this.ellipse(t,r,i,i,a);return n.shape="circle",n}linearPath(t,r){const i=this._o(r);return this._d("linearPath",[Ji(t,!1,i)],i)}arc(t,r,i,a,n,o,s=!1,c){const l=this._o(c),h=[],u=hl(t,r,i,a,n,o,s,!0,l);if(s&&l.fill)if(l.fillStyle==="solid"){const d=Object.assign({},l);d.disableMultiStroke=!0;const p=hl(t,r,i,a,n,o,!0,!1,d);p.type="fillPath",h.push(p)}else h.push(function(d,p,g,m,y,b,x){const k=d,L=p;let B=Math.abs(g/2),C=Math.abs(m/2);B+=nt(.01*B,x),C+=nt(.01*C,x);let w=y,v=b;for(;w<0;)w+=2*Math.PI,v+=2*Math.PI;v-w>2*Math.PI&&(w=0,v=2*Math.PI);const E=(v-w)/x.curveStepCount,_=[];for(let S=w;S<=v;S+=E)_.push([k+B*Math.cos(S),L+C*Math.sin(S)]);return _.push([k+B*Math.cos(v),L+C*Math.sin(v)]),_.push([k,L]),mr([_],x)}(t,r,i,a,n,o,l));return l.stroke!==ie&&h.push(u),this._d("arc",h,l)}curve(t,r){const i=this._o(r),a=[],n=cl(t,i);if(i.fill&&i.fill!==ie)if(i.fillStyle==="solid"){const o=cl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{const o=[],s=t;if(s.length){const c=typeof s[0][0]=="number"?[s]:s;for(const l of c)l.length<3?o.push(...l):l.length===3?o.push(...xn(gl([l[0],l[0],l[1],l[2]]),10,(1+i.roughness)/2)):o.push(...xn(gl(l),10,(1+i.roughness)/2))}o.length&&a.push(mr([o],i))}return i.stroke!==ie&&a.push(n),this._d("curve",a,i)}polygon(t,r){const i=this._o(r),a=[],n=Ji(t,!0,i);return i.fill&&(i.fillStyle==="solid"?a.push(bn([t],i)):a.push(mr([t],i))),i.stroke!==ie&&a.push(n),this._d("polygon",a,i)}path(t,r){const i=this._o(r),a=[];if(!t)return this._d("path",a,i);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const n=i.fill&&i.fill!=="transparent"&&i.fill!==ie,o=i.stroke!==ie,s=!!(i.simplification&&i.simplification<1),c=function(h,u,d){const p=Uu(Hu(Zs(h))),g=[];let m=[],y=[0,0],b=[];const x=()=>{b.length>=4&&m.push(...xn(b,u)),b=[]},k=()=>{x(),m.length&&(g.push(m),m=[])};for(const{key:B,data:C}of p)switch(B){case"M":k(),y=[C[0],C[1]],m.push(y);break;case"L":x(),m.push([C[0],C[1]]);break;case"C":if(!b.length){const w=m.length?m[m.length-1]:y;b.push([w[0],w[1]])}b.push([C[0],C[1]]),b.push([C[2],C[3]]),b.push([C[4],C[5]]);break;case"Z":x(),m.push([y[0],y[1]])}if(k(),!d)return g;const L=[];for(const B of g){const C=BC(B,d);C.length&&L.push(C)}return L}(t,1,s?4-4*(i.simplification||1):(1+i.roughness)/2),l=ul(t,i);if(n)if(i.fillStyle==="solid")if(c.length===1){const h=ul(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else a.push(bn(c,i));else a.push(mr(c,i));return o&&(s?c.forEach(h=>{a.push(Ji(h,!1,i))}):a.push(l)),this._d("path",a,i)}opsToPath(t,r){let i="";for(const a of t.ops){const n=typeof r=="number"&&r>=0?a.data.map(o=>+o.toFixed(r)):a.data;switch(a.op){case"move":i+=`M${n[0]} ${n[1]} `;break;case"bcurveTo":i+=`C${n[0]} ${n[1]}, ${n[2]} ${n[3]}, ${n[4]} ${n[5]} `;break;case"lineTo":i+=`L${n[0]} ${n[1]} `}}return i.trim()}toPaths(t){const r=t.sets||[],i=t.options||this.defaultOptions,a=[];for(const n of r){let o=null;switch(n.type){case"path":o={d:this.opsToPath(n),stroke:i.stroke,strokeWidth:i.strokeWidth,fill:ie};break;case"fillPath":o={d:this.opsToPath(n),stroke:ie,strokeWidth:0,fill:i.fill||ie};break;case"fillSketch":o=this.fillSketch(n,i)}o&&a.push(o)}return a}fillSketch(t,r){let i=r.fillWeight;return i<0&&(i=r.strokeWidth/2),{d:this.opsToPath(t),stroke:r.fill||ie,strokeWidth:i,fill:ie}}_mergedShape(t){return t.filter((r,i)=>i===0||r.op!=="move")}}class LC{constructor(t,r){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new _a(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.ctx,n=t.options.fixedDecimalPlaceDigits;for(const o of r)switch(o.type){case"path":a.save(),a.strokeStyle=i.stroke==="none"?"transparent":i.stroke,a.lineWidth=i.strokeWidth,i.strokeLineDash&&a.setLineDash(i.strokeLineDash),i.strokeLineDashOffset&&(a.lineDashOffset=i.strokeLineDashOffset),this._drawToContext(a,o,n),a.restore();break;case"fillPath":{a.save(),a.fillStyle=i.fill||"";const s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(a,o,n,s),a.restore();break}case"fillSketch":this.fillSketch(a,o,i)}}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2),t.save(),i.fillLineDash&&t.setLineDash(i.fillLineDash),i.fillLineDashOffset&&(t.lineDashOffset=i.fillLineDashOffset),t.strokeStyle=i.fill||"",t.lineWidth=a,this._drawToContext(t,r,i.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,r,i,a="nonzero"){t.beginPath();for(const n of r.ops){const o=typeof i=="number"&&i>=0?n.data.map(s=>+s.toFixed(i)):n.data;switch(n.op){case"move":t.moveTo(o[0],o[1]);break;case"bcurveTo":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"lineTo":t.lineTo(o[0],o[1])}}r.type==="fillPath"?t.fill(a):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o),o}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o),o}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o),o}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n),n}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i),i}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i),i}arc(t,r,i,a,n,o,s=!1,c){const l=this.gen.arc(t,r,i,a,n,o,s,c);return this.draw(l),l}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i),i}path(t,r){const i=this.gen.path(t,r);return this.draw(i),i}}const Hi="http://www.w3.org/2000/svg";class AC{constructor(t,r){this.svg=t,this.gen=new _a(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.svg.ownerDocument||window.document,n=a.createElementNS(Hi,"g"),o=t.options.fixedDecimalPlaceDigits;for(const s of r){let c=null;switch(s.type){case"path":c=a.createElementNS(Hi,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke",i.stroke),c.setAttribute("stroke-width",i.strokeWidth+""),c.setAttribute("fill","none"),i.strokeLineDash&&c.setAttribute("stroke-dasharray",i.strokeLineDash.join(" ").trim()),i.strokeLineDashOffset&&c.setAttribute("stroke-dashoffset",`${i.strokeLineDashOffset}`);break;case"fillPath":c=a.createElementNS(Hi,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke","none"),c.setAttribute("stroke-width","0"),c.setAttribute("fill",i.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||c.setAttribute("fill-rule","evenodd");break;case"fillSketch":c=this.fillSketch(a,s,i)}c&&n.appendChild(c)}return n}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2);const n=t.createElementNS(Hi,"path");return n.setAttribute("d",this.opsToPath(r,i.fixedDecimalPlaceDigits)),n.setAttribute("stroke",i.fill||""),n.setAttribute("stroke-width",a+""),n.setAttribute("fill","none"),i.fillLineDash&&n.setAttribute("stroke-dasharray",i.fillLineDash.join(" ").trim()),i.fillLineDashOffset&&n.setAttribute("stroke-dashoffset",`${i.fillLineDashOffset}`),n}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,r){return this.gen.opsToPath(t,r)}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o)}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o)}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o)}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n)}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i)}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i)}arc(t,r,i,a,n,o,s=!1,c){const l=this.gen.arc(t,r,i,a,n,o,s,c);return this.draw(l)}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i)}path(t,r){const i=this.gen.path(t,r);return this.draw(i)}}var X={canvas:(e,t)=>new LC(e,t),svg:(e,t)=>new AC(e,t),generator:e=>new _a(e),newSeed:()=>_a.newSeed()},lt=f(async(e,t,r)=>{var u,d;let i;const a=t.useHtmlLabels||Mt((u=mt())==null?void 0:u.htmlLabels);r?i=r:i="node default";const n=e.insert("g").attr("class",i).attr("id",t.domId||t.id),o=n.insert("g").attr("class","label").attr("style",Xt(t.labelStyle));let s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];const c=await Ue(o,tr(lr(s),mt()),{useHtmlLabels:a,width:t.width||((d=mt().flowchart)==null?void 0:d.wrappingWidth),cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img});let l=c.getBBox();const h=((t==null?void 0:t.padding)??0)/2;if(a){const p=c.children[0],g=pt(c),m=p.getElementsByTagName("img");if(m){const y=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(b=>new Promise(x=>{function k(){if(b.style.display="flex",b.style.flexDirection="column",y){const L=mt().fontSize?mt().fontSize:window.getComputedStyle(document.body).fontSize,B=5,[C=nc.fontSize]=Wa(L),w=C*B+"px";b.style.minWidth=w,b.style.maxWidth=w}else b.style.width="100%";x(b)}f(k,"setupImage"),setTimeout(()=>{b.complete&&k()}),b.addEventListener("error",k),b.addEventListener("load",k)})))}l=p.getBoundingClientRect(),g.attr("width",l.width),g.attr("height",l.height)}return a?o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"):o.attr("transform","translate(0, "+-l.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:n,bbox:l,halfPadding:h,label:o}},"labelHelper"),Cn=f(async(e,t,r)=>{var c,l,h,u,d,p;const i=r.useHtmlLabels||Mt((l=(c=mt())==null?void 0:c.flowchart)==null?void 0:l.htmlLabels),a=e.insert("g").attr("class","label").attr("style",r.labelStyle||""),n=await Ue(a,tr(lr(t),mt()),{useHtmlLabels:i,width:r.width||((u=(h=mt())==null?void 0:h.flowchart)==null?void 0:u.wrappingWidth),style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let o=n.getBBox();const s=r.padding/2;if(Mt((p=(d=mt())==null?void 0:d.flowchart)==null?void 0:p.htmlLabels)){const g=n.children[0],m=pt(n);o=g.getBoundingClientRect(),m.attr("width",o.width),m.attr("height",o.height)}return i?a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):a.attr("transform","translate(0, "+-o.height/2+")"),r.centerLabel&&a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),a.insert("rect",":first-child"),{shapeSvg:e,bbox:o,halfPadding:s,label:a}},"insertLabel"),J=f((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds"),st=f((e,t)=>(e.look==="handDrawn"?"rough-node":"node")+" "+e.cssClasses+" "+(t||""),"getNodeClasses");function ft(e){const t=e.map((r,i)=>`${i===0?"M":"L"}${r.x},${r.y}`);return t.push("Z"),t.join(" ")}f(ft,"createPathFromPoints");function je(e,t,r,i,a,n){const o=[],c=r-e,l=i-t,h=c/n,u=2*Math.PI/h,d=t+l/2;for(let p=0;p<=50;p++){const g=p/50,m=e+g*c,y=d+a*Math.sin(u*(m-e));o.push({x:m,y})}return o}f(je,"generateFullSineWavePoints");function Ks(e,t,r,i,a,n){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const d=s+u*h,p=e+r*Math.cos(d),g=t+r*Math.sin(d);o.push({x:-p,y:-g})}return o}f(Ks,"generateCirclePoints");var MC=f((e,t)=>{var r=e.x,i=e.y,a=t.x-r,n=t.y-i,o=e.width/2,s=e.height/2,c,l;return Math.abs(n)*o>Math.abs(a)*s?(n<0&&(s=-s),c=n===0?0:s*a/n,l=s):(a<0&&(o=-o),c=o,l=a===0?0:o*n/a),{x:r+c,y:i+l}},"intersectRect"),qr=MC;function Zu(e,t){t&&e.attr("style",t)}f(Zu,"applyStyle");async function Ku(e){const t=pt(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div");let i=e.label;e.label&&Or(e.label)&&(i=await fs(e.label.replace(Nr.lineBreakRegex,`
`),mt()));const a=e.isNode?"nodeLabel":"edgeLabel";return r.html('<span class="'+a+'" '+(e.labelStyle?'style="'+e.labelStyle+'"':"")+">"+i+"</span>"),Zu(r,e.labelStyle),r.style("display","inline-block"),r.style("padding-right","1px"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}f(Ku,"addHtmlLabel");var EC=f(async(e,t,r,i)=>{let a=e||"";if(typeof a=="object"&&(a=a[0]),Mt(mt().flowchart.htmlLabels)){a=a.replace(/\\n|\n/g,"<br />"),P.info("vertexText"+a);const n={isNode:i,label:lr(a).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await Ku(n)}else{const n=document.createElementNS("http://www.w3.org/2000/svg","text");n.setAttribute("style",t.replace("color:","fill:"));let o=[];typeof a=="string"?o=a.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(a)?o=a:o=[];for(const s of o){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),r?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=s.trim(),n.appendChild(c)}return n}},"createLabel"),Qe=EC,Re=f((e,t,r,i,a)=>["M",e+a,t,"H",e+r-a,"A",a,a,0,0,1,e+r,t+a,"V",t+i-a,"A",a,a,0,0,1,e+r-a,t+i,"H",e+a,"A",a,a,0,0,1,e,t+i-a,"V",t+a,"A",a,a,0,0,1,e+a,t,"Z"].join(" "),"createRoundedRectPathD"),FC=f(e=>{const{handDrawnSeed:t}=mt();return{fill:e,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:e,seed:t}},"solidStateFill"),jr=f(e=>{const t=DC([...e.cssCompiledStyles||[],...e.cssStyles||[]]);return{stylesMap:t,stylesArray:[...t]}},"compileStyles"),DC=f(e=>{const t=new Map;return e.forEach(r=>{const[i,a]=r.split(":");t.set(i.trim(),a==null?void 0:a.trim())}),t},"styles2Map"),Qu=f(e=>e==="color"||e==="font-size"||e==="font-family"||e==="font-weight"||e==="font-style"||e==="text-decoration"||e==="text-align"||e==="text-transform"||e==="line-height"||e==="letter-spacing"||e==="word-spacing"||e==="text-shadow"||e==="text-overflow"||e==="white-space"||e==="word-wrap"||e==="word-break"||e==="overflow-wrap"||e==="hyphens","isLabelStyle"),tt=f(e=>{const{stylesArray:t}=jr(e),r=[],i=[],a=[],n=[];return t.forEach(o=>{const s=o[0];Qu(s)?r.push(o.join(":")+" !important"):(i.push(o.join(":")+" !important"),s.includes("stroke")&&a.push(o.join(":")+" !important"),s==="fill"&&n.push(o.join(":")+" !important"))}),{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:t,borderStyles:a,backgroundStyles:n}},"styles2String"),Q=f((e,t)=>{var c;const{themeVariables:r,handDrawnSeed:i}=mt(),{nodeBorder:a,mainBkg:n}=r,{stylesMap:o}=jr(e);return Object.assign({roughness:.7,fill:o.get("fill")||n,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||a,seed:i,strokeWidth:((c=o.get("stroke-width"))==null?void 0:c.replace("px",""))||1.3,fillLineDash:[0,0]},t)},"userNodeOverrides"),Ju=f(async(e,t)=>{P.info("Creating subgraph rect for ",t.id,t);const r=mt(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=tt(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),d=Mt(r.flowchart.htmlLabels),p=u.insert("g").attr("class","cluster-label "),g=await Ue(p,t.label,{style:t.labelStyle,useHtmlLabels:d,isNode:!0});let m=g.getBBox();if(Mt(r.flowchart.htmlLabels)){const w=g.children[0],v=pt(g);m=w.getBoundingClientRect(),v.attr("width",m.width),v.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height,x=t.x-y/2,k=t.y-b/2;P.trace("Data ",t,JSON.stringify(t));let L;if(t.look==="handDrawn"){const w=X.svg(u),v=Q(t,{roughness:.7,fill:n,stroke:o,fillWeight:3,seed:a}),E=w.path(Re(x,k,y,b,0),v);L=u.insert(()=>(P.debug("Rough node insert CXC",E),E),":first-child"),L.select("path:nth-child(2)").attr("style",l.join(";")),L.select("path").attr("style",h.join(";").replace("fill","stroke"))}else L=u.insert("rect",":first-child"),L.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",x).attr("y",k).attr("width",y).attr("height",b);const{subGraphTitleTopMargin:B}=Bs(r);if(p.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+B})`),s){const w=p.select("span");w&&w.attr("style",s)}const C=L.node().getBBox();return t.offsetX=0,t.width=C.width,t.height=C.height,t.offsetY=m.height-t.padding/2,t.intersect=function(w){return qr(t,w)},{cluster:u,labelBBox:m}},"rect"),OC=f((e,t)=>{const r=e.insert("g").attr("class","note-cluster").attr("id",t.id),i=r.insert("rect",":first-child"),a=0*t.padding,n=a/2;i.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-n).attr("y",t.y-t.height/2-n).attr("width",t.width+a).attr("height",t.height+a).attr("fill","none");const o=i.node().getBBox();return t.width=o.width,t.height=o.height,t.intersect=function(s){return qr(t,s)},{cluster:r,labelBBox:{width:0,height:0}}},"noteGroup"),$C=f(async(e,t)=>{const r=mt(),{themeVariables:i,handDrawnSeed:a}=r,{altBackground:n,compositeBackground:o,compositeTitleBackground:s,nodeBorder:c}=i,l=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),h=l.insert("g",":first-child"),u=l.insert("g").attr("class","cluster-label");let d=l.append("rect");const p=u.node().appendChild(await Qe(t.label,t.labelStyle,void 0,!0));let g=p.getBBox();if(Mt(r.flowchart.htmlLabels)){const E=p.children[0],_=pt(p);g=E.getBoundingClientRect(),_.attr("width",g.width),_.attr("height",g.height)}const m=0*t.padding,y=m/2,b=(t.width<=g.width+t.padding?g.width+t.padding:t.width)+m;t.width<=g.width+t.padding?t.diff=(b-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height+m,k=t.height+m-g.height-6,L=t.x-b/2,B=t.y-x/2;t.width=b;const C=t.y-t.height/2-y+g.height+2;let w;if(t.look==="handDrawn"){const E=t.cssClasses.includes("statediagram-cluster-alt"),_=X.svg(l),S=t.rx||t.ry?_.path(Re(L,B,b,x,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:c,seed:a}):_.rectangle(L,B,b,x,{seed:a});w=l.insert(()=>S,":first-child");const $=_.rectangle(L,C,b,k,{fill:E?n:o,fillStyle:E?"hachure":"solid",stroke:c,seed:a});w=l.insert(()=>S,":first-child"),d=l.insert(()=>$)}else w=h.insert("rect",":first-child"),w.attr("class","outer").attr("x",L).attr("y",B).attr("width",b).attr("height",x).attr("data-look",t.look),d.attr("class","inner").attr("x",L).attr("y",C).attr("width",b).attr("height",k);u.attr("transform",`translate(${t.x-g.width/2}, ${B+1-(Mt(r.flowchart.htmlLabels)?0:3)})`);const v=w.node().getBBox();return t.height=v.height,t.offsetX=0,t.offsetY=g.height-t.padding/2,t.labelBBox=g,t.intersect=function(E){return qr(t,E)},{cluster:l,labelBBox:g}},"roundedWithTitle"),RC=f(async(e,t)=>{P.info("Creating subgraph rect for ",t.id,t);const r=mt(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=tt(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),d=Mt(r.flowchart.htmlLabels),p=u.insert("g").attr("class","cluster-label "),g=await Ue(p,t.label,{style:t.labelStyle,useHtmlLabels:d,isNode:!0,width:t.width});let m=g.getBBox();if(Mt(r.flowchart.htmlLabels)){const w=g.children[0],v=pt(g);m=w.getBoundingClientRect(),v.attr("width",m.width),v.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height,x=t.x-y/2,k=t.y-b/2;P.trace("Data ",t,JSON.stringify(t));let L;if(t.look==="handDrawn"){const w=X.svg(u),v=Q(t,{roughness:.7,fill:n,stroke:o,fillWeight:4,seed:a}),E=w.path(Re(x,k,y,b,t.rx),v);L=u.insert(()=>(P.debug("Rough node insert CXC",E),E),":first-child"),L.select("path:nth-child(2)").attr("style",l.join(";")),L.select("path").attr("style",h.join(";").replace("fill","stroke"))}else L=u.insert("rect",":first-child"),L.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",x).attr("y",k).attr("width",y).attr("height",b);const{subGraphTitleTopMargin:B}=Bs(r);if(p.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+B})`),s){const w=p.select("span");w&&w.attr("style",s)}const C=L.node().getBBox();return t.offsetX=0,t.width=C.width,t.height=C.height,t.offsetY=m.height-t.padding/2,t.intersect=function(w){return qr(t,w)},{cluster:u,labelBBox:m}},"kanbanSection"),IC=f((e,t)=>{const r=mt(),{themeVariables:i,handDrawnSeed:a}=r,{nodeBorder:n}=i,o=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=o.insert("g",":first-child"),c=0*t.padding,l=t.width+c;t.diff=-t.padding;const h=t.height+c,u=t.x-l/2,d=t.y-h/2;t.width=l;let p;if(t.look==="handDrawn"){const y=X.svg(o).rectangle(u,d,l,h,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:n,seed:a});p=o.insert(()=>y,":first-child")}else p=s.insert("rect",":first-child"),p.attr("class","divider").attr("x",u).attr("y",d).attr("width",l).attr("height",h).attr("data-look",t.look);const g=p.node().getBBox();return t.height=g.height,t.offsetX=0,t.offsetY=0,t.intersect=function(m){return qr(t,m)},{cluster:o,labelBBox:{}}},"divider"),PC=Ju,NC={rect:Ju,squareRect:PC,roundedWithTitle:$C,noteGroup:OC,divider:IC,kanbanSection:RC},td=new Map,zC=f(async(e,t)=>{const r=t.shape||"rect",i=await NC[r](e,t);return td.set(t.id,i),i},"insertCluster"),rv=f(()=>{td=new Map},"clear");function ed(e,t){return e.intersect(t)}f(ed,"intersectNode");var WC=ed;function rd(e,t,r,i){var a=e.x,n=e.y,o=a-i.x,s=n-i.y,c=Math.sqrt(t*t*s*s+r*r*o*o),l=Math.abs(t*r*o/c);i.x<a&&(l=-l);var h=Math.abs(t*r*s/c);return i.y<n&&(h=-h),{x:a+l,y:n+h}}f(rd,"intersectEllipse");var id=rd;function ad(e,t,r){return id(e,t,t,r)}f(ad,"intersectCircle");var qC=ad;function nd(e,t,r,i){var a,n,o,s,c,l,h,u,d,p,g,m,y,b,x;if(a=t.y-e.y,o=e.x-t.x,c=t.x*e.y-e.x*t.y,d=a*r.x+o*r.y+c,p=a*i.x+o*i.y+c,!(d!==0&&p!==0&&rs(d,p))&&(n=i.y-r.y,s=r.x-i.x,l=i.x*r.y-r.x*i.y,h=n*e.x+s*e.y+l,u=n*t.x+s*t.y+l,!(h!==0&&u!==0&&rs(h,u))&&(g=a*s-n*o,g!==0)))return m=Math.abs(g/2),y=o*l-s*c,b=y<0?(y-m)/g:(y+m)/g,y=n*c-a*l,x=y<0?(y-m)/g:(y+m)/g,{x:b,y:x}}f(nd,"intersectLine");function rs(e,t){return e*t>0}f(rs,"sameSign");var jC=nd;function sd(e,t,r){let i=e.x,a=e.y,n=[],o=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(h){o=Math.min(o,h.x),s=Math.min(s,h.y)}):(o=Math.min(o,t.x),s=Math.min(s,t.y));let c=i-e.width/2-o,l=a-e.height/2-s;for(let h=0;h<t.length;h++){let u=t[h],d=t[h<t.length-1?h+1:0],p=jC(e,r,{x:c+u.x,y:l+u.y},{x:c+d.x,y:l+d.y});p&&n.push(p)}return n.length?(n.length>1&&n.sort(function(h,u){let d=h.x-r.x,p=h.y-r.y,g=Math.sqrt(d*d+p*p),m=u.x-r.x,y=u.y-r.y,b=Math.sqrt(m*m+y*y);return g<b?-1:g===b?0:1}),n[0]):e}f(sd,"intersectPolygon");var HC=sd,Y={node:WC,circle:qC,ellipse:id,polygon:HC,rect:qr};function od(e,t){const{labelStyles:r}=tt(t);t.labelStyle=r;const i=st(t);let a=i;i||(a="anchor");const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=1,{cssStyles:s}=t,c=X.svg(n),l=Q(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(l.roughness=0);const h=c.circle(0,0,o*2,l),u=n.insert(()=>h,":first-child");return u.attr("class","anchor").attr("style",Xt(s)),J(t,u),t.intersect=function(d){return P.info("Circle intersect",t,o,d),Y.circle(t,o,d)},n}f(od,"anchor");function is(e,t,r,i,a,n,o){const c=(e+r)/2,l=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,d=(i-t)/2,p=u/a,g=d/n,m=Math.sqrt(p**2+g**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),b=c+y*n*Math.sin(h)*(o?-1:1),x=l-y*a*Math.cos(h)*(o?-1:1),k=Math.atan2((t-x)/n,(e-b)/a);let B=Math.atan2((i-x)/n,(r-b)/a)-k;o&&B<0&&(B+=2*Math.PI),!o&&B>0&&(B-=2*Math.PI);const C=[];for(let w=0;w<20;w++){const v=w/19,E=k+v*B,_=b+a*Math.cos(E),S=x+n*Math.sin(E);C.push({x:_,y:S})}return C}f(is,"generateArcPoints");async function ld(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=n.width+t.padding+20,s=n.height+t.padding,c=s/2,l=c/(2.5+s/50),{cssStyles:h}=t,u=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...is(-o/2,-s/2,-o/2,s/2,l,c,!1),{x:o/2,y:s/2},...is(o/2,s/2,o/2,-s/2,l,c,!0)],d=X.svg(a),p=Q(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=ft(u),m=d.path(g,p),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(${l/2}, 0)`),J(t,y),t.intersect=function(b){return Y.polygon(t,u,b)},a}f(ld,"bowTieRect");function Ie(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}f(Ie,"insertPolygonShape");async function cd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=n.height+t.padding,s=12,c=n.width+t.padding+s,l=0,h=c,u=-o,d=0,p=[{x:l+s,y:u},{x:h,y:u},{x:h,y:d},{x:l,y:d},{x:l,y:u+s},{x:l+s,y:u}];let g;const{cssStyles:m}=t;if(t.look==="handDrawn"){const y=X.svg(a),b=Q(t,{}),x=ft(p),k=y.path(x,b);g=a.insert(()=>k,":first-child").attr("transform",`translate(${-c/2}, ${o/2})`),m&&g.attr("style",m)}else g=Ie(a,c,o,p);return i&&g.attr("style",i),J(t,g),t.intersect=function(y){return Y.polygon(t,p,y)},a}f(cd,"card");function hd(e,t){const{nodeStyles:r}=tt(t);t.label="";const i=e.insert("g").attr("class",st(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,n=Math.max(28,t.width??0),o=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}],s=X.svg(i),c=Q(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=ft(o),h=s.path(l,c),u=i.insert(()=>h,":first-child");return a&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",a),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),t.width=28,t.height=28,t.intersect=function(d){return Y.polygon(t,o,d)},i}f(hd,"choice");async function ud(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await lt(e,t,st(t)),s=n.width/2+o;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=X.svg(a),u=Q(t,{}),d=h.circle(0,0,s*2,u);c=a.insert(()=>d,":first-child"),c.attr("class","basic label-container").attr("style",Xt(l))}else c=a.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",s).attr("cx",0).attr("cy",0);return J(t,c),t.intersect=function(h){return P.info("Circle intersect",t,s,h),Y.circle(t,s,h)},a}f(ud,"circle");function dd(e){const t=Math.cos(Math.PI/4),r=Math.sin(Math.PI/4),i=e*2,a={x:i/2*t,y:i/2*r},n={x:-(i/2)*t,y:i/2*r},o={x:-(i/2)*t,y:-(i/2)*r},s={x:i/2*t,y:-(i/2)*r};return`M ${n.x},${n.y} L ${s.x},${s.y}
                   M ${a.x},${a.y} L ${o.x},${o.y}`}f(dd,"createLine");function pd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r,t.label="";const a=e.insert("g").attr("class",st(t)).attr("id",t.domId??t.id),n=Math.max(30,(t==null?void 0:t.width)??0),{cssStyles:o}=t,s=X.svg(a),c=Q(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=s.circle(0,0,n*2,c),h=dd(n),u=s.path(h,c),d=a.insert(()=>l,":first-child");return d.insert(()=>u),o&&t.look!=="handDrawn"&&d.selectAll("path").attr("style",o),i&&t.look!=="handDrawn"&&d.selectAll("path").attr("style",i),J(t,d),t.intersect=function(p){return P.info("crossedCircle intersect",t,{radius:n,point:p}),Y.circle(t,n,p)},a}f(pd,"crossedCircle");function Le(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const d=s+u*h,p=e+r*Math.cos(d),g=t+r*Math.sin(d);o.push({x:-p,y:-g})}return o}f(Le,"generateCirclePoints");async function fd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=n.width+(t.padding??0),c=n.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Le(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Le(s/2+l*2,-l,l,20,-180,-270),...Le(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Le(s/2,c/2,l,20,0,90)],d=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Le(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Le(s/2+s*.1,-l,l,20,-180,-270),...Le(s/2+s*.1,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Le(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2,y:c/2+l}],p=X.svg(a),g=Q(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ft(u).replace("Z",""),b=p.path(y,g),x=ft(d),k=p.path(x,{...g}),L=a.insert("g",":first-child");return L.insert(()=>k,":first-child").attr("stroke-opacity",0),L.insert(()=>b,":first-child"),L.attr("class","text"),h&&t.look!=="handDrawn"&&L.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&L.selectAll("path").attr("style",i),L.attr("transform",`translate(${l}, 0)`),o.attr("transform",`translate(${-s/2+l-(n.x-(n.left??0))},${-c/2+(t.padding??0)/2-(n.y-(n.top??0))})`),J(t,L),t.intersect=function(B){return Y.polygon(t,d,B)},a}f(fd,"curlyBraceLeft");function Ae(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const d=s+u*h,p=e+r*Math.cos(d),g=t+r*Math.sin(d);o.push({x:p,y:g})}return o}f(Ae,"generateCirclePoints");async function gd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=n.width+(t.padding??0),c=n.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Ae(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...Ae(s/2+l*2,-l,l,20,-180,-270),...Ae(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...Ae(s/2,c/2,l,20,0,90)],d=[{x:-s/2,y:-c/2-l},{x:s/2,y:-c/2-l},...Ae(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...Ae(s/2+l*2,-l,l,20,-180,-270),...Ae(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...Ae(s/2,c/2,l,20,0,90),{x:s/2,y:c/2+l},{x:-s/2,y:c/2+l}],p=X.svg(a),g=Q(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ft(u).replace("Z",""),b=p.path(y,g),x=ft(d),k=p.path(x,{...g}),L=a.insert("g",":first-child");return L.insert(()=>k,":first-child").attr("stroke-opacity",0),L.insert(()=>b,":first-child"),L.attr("class","text"),h&&t.look!=="handDrawn"&&L.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&L.selectAll("path").attr("style",i),L.attr("transform",`translate(${-l}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-c/2+(t.padding??0)/2-(n.y-(n.top??0))})`),J(t,L),t.intersect=function(B){return Y.polygon(t,d,B)},a}f(gd,"curlyBraceRight");function Ot(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const d=s+u*h,p=e+r*Math.cos(d),g=t+r*Math.sin(d);o.push({x:-p,y:-g})}return o}f(Ot,"generateCirclePoints");async function md(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=n.width+(t.padding??0),c=n.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Ot(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Ot(s/2+l*2,-l,l,20,-180,-270),...Ot(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Ot(s/2,c/2,l,20,0,90)],d=[...Ot(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Ot(-s/2-l/2,-l,l,20,0,90),...Ot(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Ot(-s/2+l+l/2,c/2,l,30,-180,-270)],p=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Ot(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Ot(s/2+l*2,-l,l,20,-180,-270),...Ot(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Ot(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2-l-l/2,y:c/2+l},...Ot(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Ot(-s/2-l/2,-l,l,20,0,90),...Ot(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Ot(-s/2+l+l/2,c/2,l,30,-180,-270)],g=X.svg(a),m=Q(t,{fill:"none"});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const b=ft(u).replace("Z",""),x=g.path(b,m),L=ft(d).replace("Z",""),B=g.path(L,m),C=ft(p),w=g.path(C,{...m}),v=a.insert("g",":first-child");return v.insert(()=>w,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.insert(()=>B,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${l-l/4}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-c/2+(t.padding??0)/2-(n.y-(n.top??0))})`),J(t,v),t.intersect=function(E){return Y.polygon(t,p,E)},a}f(md,"curlyBraces");async function yd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=80,s=20,c=Math.max(o,(n.width+(t.padding??0)*2)*1.25,(t==null?void 0:t.width)??0),l=Math.max(s,n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,d=X.svg(a),p=Q(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=c,m=l,y=g-h,b=m/4,x=[{x:y,y:0},{x:b,y:0},{x:0,y:m/2},{x:b,y:m},{x:y,y:m},...Ks(-y,-m/2,h,50,270,90)],k=ft(x),L=d.path(k,p),B=a.insert(()=>L,":first-child");return B.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&B.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&B.selectChildren("path").attr("style",i),B.attr("transform",`translate(${-c/2}, ${-l/2})`),J(t,B),t.intersect=function(C){return Y.polygon(t,x,C)},a}f(yd,"curvedTrapezoid");var UC=f((e,t,r,i,a,n)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createCylinderPathD"),GC=f((e,t,r,i,a,n)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createOuterCylinderPathD"),YC=f((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function bd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+t.padding,t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(n.height+l+t.padding,t.height??0);let u;const{cssStyles:d}=t;if(t.look==="handDrawn"){const p=X.svg(a),g=GC(0,0,s,h,c,l),m=YC(0,l,s,h,c,l),y=p.path(g,Q(t,{})),b=p.path(m,Q(t,{fill:"none"}));u=a.insert(()=>b,":first-child"),u=a.insert(()=>y,":first-child"),u.attr("class","basic label-container"),d&&u.attr("style",d)}else{const p=UC(0,0,s,h,c,l);u=a.insert("path",":first-child").attr("d",p).attr("class","basic label-container").attr("style",Xt(d)).attr("style",i)}return u.attr("label-offset-y",l),u.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),J(t,u),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+(t.padding??0)/1.5-(n.y-(n.top??0))})`),t.intersect=function(p){const g=Y.rect(t,p),m=g.x-(t.x??0);if(c!=0&&(Math.abs(m)<(t.width??0)/2||Math.abs(m)==(t.width??0)/2&&Math.abs(g.y-(t.y??0))>(t.height??0)/2-l)){let y=l*l*(1-m*m/(c*c));y>0&&(y=Math.sqrt(y)),y=l-y,p.y-(t.y??0)>0&&(y=-y),g.y+=y}return g},a}f(bd,"cylinder");async function xd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=n.width+t.padding,c=n.height+t.padding,l=c*.2,h=-s/2,u=-c/2-l/2,{cssStyles:d}=t,p=X.svg(a),g=Q(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u+l},{x:-h,y:u+l},{x:-h,y:-u},{x:h,y:-u},{x:h,y:u},{x:-h,y:u},{x:-h,y:u+l}],y=p.polygon(m.map(x=>[x.x,x.y]),g),b=a.insert(()=>y,":first-child");return b.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${h+(t.padding??0)/2-(n.x-(n.left??0))}, ${u+l+(t.padding??0)/2-(n.y-(n.top??0))})`),J(t,b),t.intersect=function(x){return Y.rect(t,x)},a}f(xd,"dividedRectangle");async function Cd(e,t){var d,p;const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await lt(e,t,st(t)),c=n.width/2+o+5,l=n.width/2+o;let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const g=X.svg(a),m=Q(t,{roughness:.2,strokeWidth:2.5}),y=Q(t,{roughness:.2,strokeWidth:1.5}),b=g.circle(0,0,c*2,m),x=g.circle(0,0,l*2,y);h=a.insert("g",":first-child"),h.attr("class",Xt(t.cssClasses)).attr("style",Xt(u)),(d=h.node())==null||d.appendChild(b),(p=h.node())==null||p.appendChild(x)}else{h=a.insert("g",":first-child");const g=h.insert("circle",":first-child"),m=h.insert("circle");h.attr("class","basic label-container").attr("style",i),g.attr("class","outer-circle").attr("style",i).attr("r",c).attr("cx",0).attr("cy",0),m.attr("class","inner-circle").attr("style",i).attr("r",l).attr("cx",0).attr("cy",0)}return J(t,h),t.intersect=function(g){return P.info("DoubleCircle intersect",t,c,g),Y.circle(t,c,g)},a}f(Cd,"doublecircle");function wd(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=tt(t);t.label="",t.labelStyle=i;const n=e.insert("g").attr("class",st(t)).attr("id",t.domId??t.id),o=7,{cssStyles:s}=t,c=X.svg(n),{nodeBorder:l}=r,h=Q(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(h.roughness=0);const u=c.circle(0,0,o*2,h),d=n.insert(()=>u,":first-child");return d.selectAll("path").attr("style",`fill: ${l} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&d.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&d.selectAll("path").attr("style",a),J(t,d),t.intersect=function(p){return P.info("filledCircle intersect",t,{radius:o,point:p}),Y.circle(t,o,p)},n}f(wd,"filledCircle");async function kd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=n.width+(t.padding??0),c=s+n.height,l=s+n.height,h=[{x:0,y:-c},{x:l,y:-c},{x:l/2,y:0}],{cssStyles:u}=t,d=X.svg(a),p=Q(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=ft(h),m=d.path(g,p),y=a.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return u&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),t.width=s,t.height=c,J(t,y),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${-c/2+(t.padding??0)/2+(n.y-(n.top??0))})`),t.intersect=function(b){return P.info("Triangle intersect",t,h,b),Y.polygon(t,h,b)},a}f(kd,"flippedTriangle");function vd(e,t,{dir:r,config:{state:i,themeVariables:a}}){const{nodeStyles:n}=tt(t);t.label="";const o=e.insert("g").attr("class",st(t)).attr("id",t.domId??t.id),{cssStyles:s}=t;let c=Math.max(70,(t==null?void 0:t.width)??0),l=Math.max(10,(t==null?void 0:t.height)??0);r==="LR"&&(c=Math.max(10,(t==null?void 0:t.width)??0),l=Math.max(70,(t==null?void 0:t.height)??0));const h=-1*c/2,u=-1*l/2,d=X.svg(o),p=Q(t,{stroke:a.lineColor,fill:a.lineColor});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=d.rectangle(h,u,c,l,p),m=o.insert(()=>g,":first-child");s&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",s),n&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",n),J(t,m);const y=(i==null?void 0:i.padding)??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(b){return Y.rect(t,b)},o}f(vd,"forkJoin");async function Sd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const a=80,n=50,{shapeSvg:o,bbox:s}=await lt(e,t,st(t)),c=Math.max(a,s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n,s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,d=X.svg(o),p=Q(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=[{x:-c/2,y:-l/2},{x:c/2-h,y:-l/2},...Ks(-c/2+h,0,h,50,90,270),{x:c/2-h,y:l/2},{x:-c/2,y:l/2}],m=ft(g),y=d.path(m,p),b=o.insert(()=>y,":first-child");return b.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",i),J(t,b),t.intersect=function(x){return P.info("Pill intersect",t,{radius:h,point:x}),Y.polygon(t,g,x)},o}f(Sd,"halfRoundedRectangle");var VC=f((e,t,r,i,a)=>[`M${e+a},${t}`,`L${e+r-a},${t}`,`L${e+r},${t-i/2}`,`L${e+r-a},${t-i}`,`L${e+a},${t-i}`,`L${e},${t-i/2}`,"Z"].join(" "),"createHexagonPathD");async function Td(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=4,s=n.height+t.padding,c=s/o,l=n.width+2*c+t.padding,h=[{x:c,y:0},{x:l-c,y:0},{x:l,y:-s/2},{x:l-c,y:-s},{x:c,y:-s},{x:0,y:-s/2}];let u;const{cssStyles:d}=t;if(t.look==="handDrawn"){const p=X.svg(a),g=Q(t,{}),m=VC(0,0,l,s,c),y=p.path(m,g);u=a.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${s/2})`),d&&u.attr("style",d)}else u=Ie(a,l,s,h);return i&&u.attr("style",i),t.width=l,t.height=s,J(t,u),t.intersect=function(p){return Y.polygon(t,h,p)},a}f(Td,"hexagon");async function _d(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.label="",t.labelStyle=r;const{shapeSvg:a}=await lt(e,t,st(t)),n=Math.max(30,(t==null?void 0:t.width)??0),o=Math.max(30,(t==null?void 0:t.height)??0),{cssStyles:s}=t,c=X.svg(a),l=Q(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const h=[{x:0,y:0},{x:n,y:0},{x:0,y:o},{x:n,y:o}],u=ft(h),d=c.path(u,l),p=a.insert(()=>d,":first-child");return p.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&p.selectChildren("path").attr("style",s),i&&t.look!=="handDrawn"&&p.selectChildren("path").attr("style",i),p.attr("transform",`translate(${-n/2}, ${-o/2})`),J(t,p),t.intersect=function(g){return P.info("Pill intersect",t,{points:h}),Y.polygon(t,h,g)},a}f(_d,"hourglass");async function Bd(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=tt(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await lt(e,t,"icon-shape default"),d=t.pos==="t",p=s,g=s,{nodeBorder:m}=r,{stylesMap:y}=jr(t),b=-g/2,x=-p/2,k=t.label?8:0,L=X.svg(l),B=Q(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(B.roughness=0,B.fillStyle="solid");const C=L.rectangle(b,x,g,p,B),w=Math.max(g,h.width),v=p+h.height+k,E=L.rectangle(-w/2,-v/2,w,v,{...B,fill:"transparent",stroke:"none"}),_=l.insert(()=>C,":first-child"),S=l.insert(()=>E);if(t.icon){const $=l.append("g");$.html(`<g>${await $a(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const R=$.node().getBBox(),M=R.width,O=R.height,F=R.x,I=R.y;$.attr("transform",`translate(${-M/2-F},${d?h.height/2+k/2-O/2-I:-h.height/2-k/2-O/2-I})`),$.attr("style",`color: ${y.get("stroke")??m};`)}return u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-v/2:v/2-h.height})`),_.attr("transform",`translate(0,${d?h.height/2+k/2:-h.height/2-k/2})`),J(t,S),t.intersect=function($){if(P.info("iconSquare intersect",t,$),!t.label)return Y.rect(t,$);const R=t.x??0,M=t.y??0,O=t.height??0;let F=[];return d?F=[{x:R-h.width/2,y:M-O/2},{x:R+h.width/2,y:M-O/2},{x:R+h.width/2,y:M-O/2+h.height+k},{x:R+g/2,y:M-O/2+h.height+k},{x:R+g/2,y:M+O/2},{x:R-g/2,y:M+O/2},{x:R-g/2,y:M-O/2+h.height+k},{x:R-h.width/2,y:M-O/2+h.height+k}]:F=[{x:R-g/2,y:M-O/2},{x:R+g/2,y:M-O/2},{x:R+g/2,y:M-O/2+p},{x:R+h.width/2,y:M-O/2+p},{x:R+h.width/2/2,y:M+O/2},{x:R-h.width/2,y:M+O/2},{x:R-h.width/2,y:M-O/2+p},{x:R-g/2,y:M-O/2+p}],Y.polygon(t,F,$)},l}f(Bd,"icon");async function Ld(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=tt(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await lt(e,t,"icon-shape default"),d=20,p=t.label?8:0,g=t.pos==="t",{nodeBorder:m,mainBkg:y}=r,{stylesMap:b}=jr(t),x=X.svg(l),k=Q(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const L=b.get("fill");k.stroke=L??y;const B=l.append("g");t.icon&&B.html(`<g>${await $a(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const C=B.node().getBBox(),w=C.width,v=C.height,E=C.x,_=C.y,S=Math.max(w,v)*Math.SQRT2+d*2,$=x.circle(0,0,S,k),R=Math.max(S,h.width),M=S+h.height+p,O=x.rectangle(-R/2,-M/2,R,M,{...k,fill:"transparent",stroke:"none"}),F=l.insert(()=>$,":first-child"),I=l.insert(()=>O);return B.attr("transform",`translate(${-w/2-E},${g?h.height/2+p/2-v/2-_:-h.height/2-p/2-v/2-_})`),B.attr("style",`color: ${b.get("stroke")??m};`),u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${g?-M/2:M/2-h.height})`),F.attr("transform",`translate(0,${g?h.height/2+p/2:-h.height/2-p/2})`),J(t,I),t.intersect=function(D){return P.info("iconSquare intersect",t,D),Y.rect(t,D)},l}f(Ld,"iconCircle");async function Ad(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=tt(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:d}=await lt(e,t,"icon-shape default"),p=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:b}=r,{stylesMap:x}=jr(t),k=-m/2,L=-g/2,B=t.label?8:0,C=X.svg(l),w=Q(t,{});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");const v=x.get("fill");w.stroke=v??b;const E=C.path(Re(k,L,m,g,5),w),_=Math.max(m,h.width),S=g+h.height+B,$=C.rectangle(-_/2,-S/2,_,S,{...w,fill:"transparent",stroke:"none"}),R=l.insert(()=>E,":first-child").attr("class","icon-shape2"),M=l.insert(()=>$);if(t.icon){const O=l.append("g");O.html(`<g>${await $a(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const F=O.node().getBBox(),I=F.width,D=F.height,W=F.x,j=F.y;O.attr("transform",`translate(${-I/2-W},${p?h.height/2+B/2-D/2-j:-h.height/2-B/2-D/2-j})`),O.attr("style",`color: ${x.get("stroke")??y};`)}return d.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-S/2:S/2-h.height})`),R.attr("transform",`translate(0,${p?h.height/2+B/2:-h.height/2-B/2})`),J(t,M),t.intersect=function(O){if(P.info("iconSquare intersect",t,O),!t.label)return Y.rect(t,O);const F=t.x??0,I=t.y??0,D=t.height??0;let W=[];return p?W=[{x:F-h.width/2,y:I-D/2},{x:F+h.width/2,y:I-D/2},{x:F+h.width/2,y:I-D/2+h.height+B},{x:F+m/2,y:I-D/2+h.height+B},{x:F+m/2,y:I+D/2},{x:F-m/2,y:I+D/2},{x:F-m/2,y:I-D/2+h.height+B},{x:F-h.width/2,y:I-D/2+h.height+B}]:W=[{x:F-m/2,y:I-D/2},{x:F+m/2,y:I-D/2},{x:F+m/2,y:I-D/2+g},{x:F+h.width/2,y:I-D/2+g},{x:F+h.width/2/2,y:I+D/2},{x:F-h.width/2,y:I+D/2},{x:F-h.width/2,y:I-D/2+g},{x:F-m/2,y:I-D/2+g}],Y.polygon(t,W,O)},l}f(Ad,"iconRounded");async function Md(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=tt(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:d}=await lt(e,t,"icon-shape default"),p=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:b}=r,{stylesMap:x}=jr(t),k=-m/2,L=-g/2,B=t.label?8:0,C=X.svg(l),w=Q(t,{});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");const v=x.get("fill");w.stroke=v??b;const E=C.path(Re(k,L,m,g,.1),w),_=Math.max(m,h.width),S=g+h.height+B,$=C.rectangle(-_/2,-S/2,_,S,{...w,fill:"transparent",stroke:"none"}),R=l.insert(()=>E,":first-child"),M=l.insert(()=>$);if(t.icon){const O=l.append("g");O.html(`<g>${await $a(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const F=O.node().getBBox(),I=F.width,D=F.height,W=F.x,j=F.y;O.attr("transform",`translate(${-I/2-W},${p?h.height/2+B/2-D/2-j:-h.height/2-B/2-D/2-j})`),O.attr("style",`color: ${x.get("stroke")??y};`)}return d.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-S/2:S/2-h.height})`),R.attr("transform",`translate(0,${p?h.height/2+B/2:-h.height/2-B/2})`),J(t,M),t.intersect=function(O){if(P.info("iconSquare intersect",t,O),!t.label)return Y.rect(t,O);const F=t.x??0,I=t.y??0,D=t.height??0;let W=[];return p?W=[{x:F-h.width/2,y:I-D/2},{x:F+h.width/2,y:I-D/2},{x:F+h.width/2,y:I-D/2+h.height+B},{x:F+m/2,y:I-D/2+h.height+B},{x:F+m/2,y:I+D/2},{x:F-m/2,y:I+D/2},{x:F-m/2,y:I-D/2+h.height+B},{x:F-h.width/2,y:I-D/2+h.height+B}]:W=[{x:F-m/2,y:I-D/2},{x:F+m/2,y:I-D/2},{x:F+m/2,y:I-D/2+g},{x:F+h.width/2,y:I-D/2+g},{x:F+h.width/2/2,y:I+D/2},{x:F-h.width/2,y:I+D/2},{x:F-h.width/2,y:I-D/2+g},{x:F-m/2,y:I-D/2+g}],Y.polygon(t,W,O)},l}f(Md,"iconSquare");async function Ed(e,t,{config:{flowchart:r}}){const i=new Image;i.src=(t==null?void 0:t.img)??"",await i.decode();const a=Number(i.naturalWidth.toString().replace("px","")),n=Number(i.naturalHeight.toString().replace("px",""));t.imageAspectRatio=a/n;const{labelStyles:o}=tt(t);t.labelStyle=o;const s=r==null?void 0:r.wrappingWidth;t.defaultWidth=r==null?void 0:r.wrappingWidth;const c=Math.max(t.label?s??0:0,(t==null?void 0:t.assetWidth)??a),l=t.constraint==="on"&&t!=null&&t.assetHeight?t.assetHeight*t.imageAspectRatio:c,h=t.constraint==="on"?l/t.imageAspectRatio:(t==null?void 0:t.assetHeight)??n;t.width=Math.max(l,s??0);const{shapeSvg:u,bbox:d,label:p}=await lt(e,t,"image-shape default"),g=t.pos==="t",m=-l/2,y=-h/2,b=t.label?8:0,x=X.svg(u),k=Q(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const L=x.rectangle(m,y,l,h,k),B=Math.max(l,d.width),C=h+d.height+b,w=x.rectangle(-B/2,-C/2,B,C,{...k,fill:"none",stroke:"none"}),v=u.insert(()=>L,":first-child"),E=u.insert(()=>w);if(t.img){const _=u.append("image");_.attr("href",t.img),_.attr("width",l),_.attr("height",h),_.attr("preserveAspectRatio","none"),_.attr("transform",`translate(${-l/2},${g?C/2-h:-C/2})`)}return p.attr("transform",`translate(${-d.width/2-(d.x-(d.left??0))},${g?-h/2-d.height/2-b/2:h/2-d.height/2+b/2})`),v.attr("transform",`translate(0,${g?d.height/2+b/2:-d.height/2-b/2})`),J(t,E),t.intersect=function(_){if(P.info("iconSquare intersect",t,_),!t.label)return Y.rect(t,_);const S=t.x??0,$=t.y??0,R=t.height??0;let M=[];return g?M=[{x:S-d.width/2,y:$-R/2},{x:S+d.width/2,y:$-R/2},{x:S+d.width/2,y:$-R/2+d.height+b},{x:S+l/2,y:$-R/2+d.height+b},{x:S+l/2,y:$+R/2},{x:S-l/2,y:$+R/2},{x:S-l/2,y:$-R/2+d.height+b},{x:S-d.width/2,y:$-R/2+d.height+b}]:M=[{x:S-l/2,y:$-R/2},{x:S+l/2,y:$-R/2},{x:S+l/2,y:$-R/2+h},{x:S+d.width/2,y:$-R/2+h},{x:S+d.width/2/2,y:$+R/2},{x:S-d.width/2,y:$+R/2},{x:S-d.width/2,y:$-R/2+h},{x:S-l/2,y:$-R/2+h}],Y.polygon(t,M,_)},u}f(Ed,"imageSquare");async function Fd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=X.svg(a),d=Q(t,{}),p=ft(c),g=u.path(p,d);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,J(t,l),t.intersect=function(u){return Y.polygon(t,c,u)},a}f(Fd,"inv_trapezoid");async function Mi(e,t,r){const{labelStyles:i,nodeStyles:a}=tt(t);t.labelStyle=i;const{shapeSvg:n,bbox:o}=await lt(e,t,st(t)),s=Math.max(o.width+r.labelPaddingX*2,(t==null?void 0:t.width)||0),c=Math.max(o.height+r.labelPaddingY*2,(t==null?void 0:t.height)||0),l=-s/2,h=-c/2;let u,{rx:d,ry:p}=t;const{cssStyles:g}=t;if(r!=null&&r.rx&&r.ry&&(d=r.rx,p=r.ry),t.look==="handDrawn"){const m=X.svg(n),y=Q(t,{}),b=d||p?m.path(Re(l,h,s,c,d||0),y):m.rectangle(l,h,s,c,y);u=n.insert(()=>b,":first-child"),u.attr("class","basic label-container").attr("style",Xt(g))}else u=n.insert("rect",":first-child"),u.attr("class","basic label-container").attr("style",a).attr("rx",Xt(d)).attr("ry",Xt(p)).attr("x",l).attr("y",h).attr("width",s).attr("height",c);return J(t,u),t.intersect=function(m){return Y.rect(t,m)},n}f(Mi,"drawRect");async function Dd(e,t){const{shapeSvg:r,bbox:i,label:a}=await lt(e,t,"label"),n=r.insert("rect",":first-child");return n.attr("width",.1).attr("height",.1),r.attr("class","label edgeLabel"),a.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),J(t,n),t.intersect=function(c){return Y.rect(t,c)},r}f(Dd,"labelRect");async function Od(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=X.svg(a),d=Q(t,{}),p=ft(c),g=u.path(p,d);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,J(t,l),t.intersect=function(u){return Y.polygon(t,c,u)},a}f(Od,"lean_left");async function $d(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=X.svg(a),d=Q(t,{}),p=ft(c),g=u.path(p,d);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,J(t,l),t.intersect=function(u){return Y.polygon(t,c,u)},a}f($d,"lean_right");function Rd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.label="",t.labelStyle=r;const a=e.insert("g").attr("class",st(t)).attr("id",t.domId??t.id),{cssStyles:n}=t,o=Math.max(35,(t==null?void 0:t.width)??0),s=Math.max(35,(t==null?void 0:t.height)??0),c=7,l=[{x:o,y:0},{x:0,y:s+c/2},{x:o-2*c,y:s+c/2},{x:0,y:2*s},{x:o,y:s-c/2},{x:2*c,y:s-c/2}],h=X.svg(a),u=Q(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const d=ft(l),p=h.path(d,u),g=a.insert(()=>p,":first-child");return n&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",n),i&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",i),g.attr("transform",`translate(-${o/2},${-s})`),J(t,g),t.intersect=function(m){return P.info("lightningBolt intersect",t,m),Y.polygon(t,l,m)},a}f(Rd,"lightningBolt");var XC=f((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createCylinderPathD"),ZC=f((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createOuterCylinderPathD"),KC=f((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Id(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0),t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(n.height+l+(t.padding??0),t.height??0),u=h*.1;let d;const{cssStyles:p}=t;if(t.look==="handDrawn"){const g=X.svg(a),m=ZC(0,0,s,h,c,l,u),y=KC(0,l,s,h,c,l),b=Q(t,{}),x=g.path(m,b),k=g.path(y,b);a.insert(()=>k,":first-child").attr("class","line"),d=a.insert(()=>x,":first-child"),d.attr("class","basic label-container"),p&&d.attr("style",p)}else{const g=XC(0,0,s,h,c,l,u);d=a.insert("path",":first-child").attr("d",g).attr("class","basic label-container").attr("style",Xt(p)).attr("style",i)}return d.attr("label-offset-y",l),d.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),J(t,d),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+l-(n.y-(n.top??0))})`),t.intersect=function(g){const m=Y.rect(t,g),y=m.x-(t.x??0);if(c!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(m.y-(t.y??0))>(t.height??0)/2-l)){let b=l*l*(1-y*y/(c*c));b>0&&(b=Math.sqrt(b)),b=l-b,g.y-(t.y??0)>0&&(b=-b),m.y+=b}return m},a}f(Id,"linedCylinder");async function Pd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,{cssStyles:u}=t,d=X.svg(a),p=Q(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=[{x:-s/2-s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:h/2},...je(-s/2-s/2*.1,h/2,s/2+s/2*.1,h/2,l,.8),{x:s/2+s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:-h/2},{x:-s/2,y:-h/2},{x:-s/2,y:h/2*1.1},{x:-s/2,y:-h/2}],m=d.polygon(g.map(b=>[b.x,b.y]),p),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(n.x-(n.left??0))},${-c/2+(t.padding??0)-l/2-(n.y-(n.top??0))})`),J(t,y),t.intersect=function(b){return Y.polygon(t,g,b)},a}f(Pd,"linedWaveEdgedRect");async function Nd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:d}=t,p=X.svg(a),g=Q(t,{}),m=[{x:h-l,y:u+l},{x:h-l,y:u+c+l},{x:h+s-l,y:u+c+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u+c-l},{x:h+s+l,y:u+c-l},{x:h+s+l,y:u-l},{x:h+l,y:u-l},{x:h+l,y:u},{x:h,y:u},{x:h,y:u+l}],y=[{x:h,y:u+l},{x:h+s-l,y:u+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u},{x:h,y:u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const b=ft(m),x=p.path(b,g),k=ft(y),L=p.path(k,{...g,fill:"none"}),B=a.insert(()=>L,":first-child");return B.insert(()=>x,":first-child"),B.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&B.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&B.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)-l-(n.x-(n.left??0))}, ${-(n.height/2)+l-(n.y-(n.top??0))})`),J(t,B),t.intersect=function(C){return Y.polygon(t,m,C)},a}f(Nd,"multiRect");async function zd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,u=-s/2,d=-h/2,p=5,{cssStyles:g}=t,m=je(u-p,d+h+p,u+s-p,d+h+p,l,.8),y=m==null?void 0:m[m.length-1],b=[{x:u-p,y:d+p},{x:u-p,y:d+h+p},...m,{x:u+s-p,y:y.y-p},{x:u+s,y:y.y-p},{x:u+s,y:y.y-2*p},{x:u+s+p,y:y.y-2*p},{x:u+s+p,y:d-p},{x:u+p,y:d-p},{x:u+p,y:d},{x:u,y:d},{x:u,y:d+p}],x=[{x:u,y:d+p},{x:u+s-p,y:d+p},{x:u+s-p,y:y.y-p},{x:u+s,y:y.y-p},{x:u+s,y:d},{x:u,y:d}],k=X.svg(a),L=Q(t,{});t.look!=="handDrawn"&&(L.roughness=0,L.fillStyle="solid");const B=ft(b),C=k.path(B,L),w=ft(x),v=k.path(w,L),E=a.insert(()=>C,":first-child");return E.insert(()=>v),E.attr("class","basic label-container"),g&&t.look!=="handDrawn"&&E.selectAll("path").attr("style",g),i&&t.look!=="handDrawn"&&E.selectAll("path").attr("style",i),E.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-(n.width/2)-p-(n.x-(n.left??0))}, ${-(n.height/2)+p-l/2-(n.y-(n.top??0))})`),J(t,E),t.intersect=function(_){return Y.polygon(t,b,_)},a}f(zd,"multiWaveEdgedRectangle");async function Wd(e,t,{config:{themeVariables:r}}){var b;const{labelStyles:i,nodeStyles:a}=tt(t);t.labelStyle=i,t.useHtmlLabels||((b=re().flowchart)==null?void 0:b.htmlLabels)!==!1||(t.centerLabel=!0);const{shapeSvg:o,bbox:s}=await lt(e,t,st(t)),c=Math.max(s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-c/2,u=-l/2,{cssStyles:d}=t,p=X.svg(o),g=Q(t,{fill:r.noteBkgColor,stroke:r.noteBorderColor});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=p.rectangle(h,u,c,l,g),y=o.insert(()=>m,":first-child");return y.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",d),a&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",a),J(t,y),t.intersect=function(x){return Y.rect(t,x)},o}f(Wd,"note");var QC=f((e,t,r)=>[`M${e+r/2},${t}`,`L${e+r},${t-r/2}`,`L${e+r/2},${t-r}`,`L${e},${t-r/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function qd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=n.width+t.padding,s=n.height+t.padding,c=o+s,l=[{x:c/2,y:0},{x:c,y:-c/2},{x:c/2,y:-c},{x:0,y:-c/2}];let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const d=X.svg(a),p=Q(t,{}),g=QC(0,0,c),m=d.path(g,p);h=a.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`),u&&h.attr("style",u)}else h=Ie(a,c,c,l);return i&&h.attr("style",i),J(t,h),t.intersect=function(d){return P.debug(`APA12 Intersect called SPLIT
point:`,d,`
node:
`,t,`
res:`,Y.polygon(t,l,d)),Y.polygon(t,l,d)},a}f(qd,"question");async function jd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,u=h/2,d=[{x:l+u,y:h},{x:l,y:0},{x:l+u,y:-h},{x:-l,y:-h},{x:-l,y:h}],{cssStyles:p}=t,g=X.svg(a),m=Q(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=ft(d),b=g.path(y,m),x=a.insert(()=>b,":first-child");return x.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),x.attr("transform",`translate(${-u/2},0)`),o.attr("transform",`translate(${-u/2-n.width/2-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),J(t,x),t.intersect=function(k){return Y.polygon(t,d,k)},a}f(jd,"rect_left_inv_arrow");async function Hd(e,t){var v,E;const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;let a;t.cssClasses?a="node "+t.cssClasses:a="node default";const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=n.insert("g"),s=n.insert("g").attr("class","label").attr("style",i),c=t.description,l=t.label,h=s.node().appendChild(await Qe(l,t.labelStyle,!0,!0));let u={width:0,height:0};if(Mt((E=(v=mt())==null?void 0:v.flowchart)==null?void 0:E.htmlLabels)){const _=h.children[0],S=pt(h);u=_.getBoundingClientRect(),S.attr("width",u.width),S.attr("height",u.height)}P.info("Text 2",c);const d=c||[],p=h.getBBox(),g=s.node().appendChild(await Qe(d.join?d.join("<br/>"):d,t.labelStyle,!0,!0)),m=g.children[0],y=pt(g);u=m.getBoundingClientRect(),y.attr("width",u.width),y.attr("height",u.height);const b=(t.padding||0)/2;pt(g).attr("transform","translate( "+(u.width>p.width?0:(p.width-u.width)/2)+", "+(p.height+b+5)+")"),pt(h).attr("transform","translate( "+(u.width<p.width?0:-(p.width-u.width)/2)+", 0)"),u=s.node().getBBox(),s.attr("transform","translate("+-u.width/2+", "+(-u.height/2-b+3)+")");const x=u.width+(t.padding||0),k=u.height+(t.padding||0),L=-u.width/2-b,B=-u.height/2-b;let C,w;if(t.look==="handDrawn"){const _=X.svg(n),S=Q(t,{}),$=_.path(Re(L,B,x,k,t.rx||0),S),R=_.line(-u.width/2-b,-u.height/2-b+p.height+b,u.width/2+b,-u.height/2-b+p.height+b,S);w=n.insert(()=>(P.debug("Rough node insert CXC",$),R),":first-child"),C=n.insert(()=>(P.debug("Rough node insert CXC",$),$),":first-child")}else C=o.insert("rect",":first-child"),w=o.insert("line"),C.attr("class","outer title-state").attr("style",i).attr("x",-u.width/2-b).attr("y",-u.height/2-b).attr("width",u.width+(t.padding||0)).attr("height",u.height+(t.padding||0)),w.attr("class","divider").attr("x1",-u.width/2-b).attr("x2",u.width/2+b).attr("y1",-u.height/2-b+p.height+b).attr("y2",-u.height/2-b+p.height+b);return J(t,C),t.intersect=function(_){return Y.rect(t,_)},n}f(Hd,"rectWithTitle");async function Ud(e,t){const r={rx:5,ry:5,labelPaddingX:((t==null?void 0:t.padding)||0)*1,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return Mi(e,t,r)}f(Ud,"roundedRect");async function Gd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=(t==null?void 0:t.padding)??0,c=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-n.width/2-s,u=-n.height/2-s,{cssStyles:d}=t,p=X.svg(a),g=Q(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u},{x:h+c+8,y:u},{x:h+c+8,y:u+l},{x:h-8,y:u+l},{x:h-8,y:u},{x:h,y:u},{x:h,y:u+l}],y=p.polygon(m.map(x=>[x.x,x.y]),g),b=a.insert(()=>y,":first-child");return b.attr("class","basic label-container").attr("style",Xt(d)),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),d&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${-c/2+4+(t.padding??0)-(n.x-(n.left??0))},${-l/2+(t.padding??0)-(n.y-(n.top??0))})`),J(t,b),t.intersect=function(x){return Y.rect(t,x)},a}f(Gd,"shadedProcess");async function Yd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,{cssStyles:u}=t,d=X.svg(a),p=Q(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const g=[{x:l,y:h},{x:l,y:h+c},{x:l+s,y:h+c},{x:l+s,y:h-c/2}],m=ft(g),y=d.path(m,p),b=a.insert(()=>y,":first-child");return b.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",i),b.attr("transform",`translate(0, ${c/4})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))}, ${-c/4+(t.padding??0)-(n.y-(n.top??0))})`),J(t,b),t.intersect=function(x){return Y.polygon(t,g,x)},a}f(Yd,"slopedRect");async function Vd(e,t){const r={rx:0,ry:0,labelPaddingX:((t==null?void 0:t.padding)||0)*2,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return Mi(e,t,r)}f(Vd,"squareRect");async function Xd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=n.height+t.padding,s=n.width+o/4+t.padding;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=X.svg(a),u=Q(t,{}),d=Re(-s/2,-o/2,s,o,o/2),p=h.path(d,u);c=a.insert(()=>p,":first-child"),c.attr("class","basic label-container").attr("style",Xt(l))}else c=a.insert("rect",":first-child"),c.attr("class","basic label-container").attr("style",i).attr("rx",o/2).attr("ry",o/2).attr("x",-s/2).attr("y",-o/2).attr("width",s).attr("height",o);return J(t,c),t.intersect=function(h){return Y.rect(t,h)},a}f(Xd,"stadium");async function Zd(e,t){return Mi(e,t,{rx:5,ry:5})}f(Zd,"state");function Kd(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=tt(t);t.labelStyle=i;const{cssStyles:n}=t,{lineColor:o,stateBorder:s,nodeBorder:c}=r,l=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),h=X.svg(l),u=Q(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const d=h.circle(0,0,14,{...u,stroke:o,strokeWidth:2}),p=s??c,g=h.circle(0,0,5,{...u,fill:p,stroke:p,strokeWidth:2,fillStyle:"solid"}),m=l.insert(()=>d,":first-child");return m.insert(()=>g),n&&m.selectAll("path").attr("style",n),a&&m.selectAll("path").attr("style",a),J(t,m),t.intersect=function(y){return Y.circle(t,7,y)},l}f(Kd,"stateEnd");function Qd(e,t,{config:{themeVariables:r}}){const{lineColor:i}=r,a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let n;if(t.look==="handDrawn"){const s=X.svg(a).circle(0,0,14,FC(i));n=a.insert(()=>s),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else n=a.insert("circle",":first-child"),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return J(t,n),t.intersect=function(o){return Y.circle(t,7,o)},a}f(Qd,"stateStart");async function Jd(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=((t==null?void 0:t.padding)||0)/2,s=n.width+t.padding,c=n.height+t.padding,l=-n.width/2-o,h=-n.height/2-o,u=[{x:0,y:0},{x:s,y:0},{x:s,y:-c},{x:0,y:-c},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-c},{x:-8,y:-c},{x:-8,y:0}];if(t.look==="handDrawn"){const d=X.svg(a),p=Q(t,{}),g=d.rectangle(l-8,h,s+16,c,p),m=d.line(l,h,l,h+c,p),y=d.line(l+s,h,l+s,h+c,p);a.insert(()=>m,":first-child"),a.insert(()=>y,":first-child");const b=a.insert(()=>g,":first-child"),{cssStyles:x}=t;b.attr("class","basic label-container").attr("style",Xt(x)),J(t,b)}else{const d=Ie(a,s,c,u);i&&d.attr("style",i),J(t,d)}return t.intersect=function(d){return Y.polygon(t,u,d)},a}f(Jd,"subroutine");async function tp(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=-o/2,l=-s/2,h=.2*s,u=.2*s,{cssStyles:d}=t,p=X.svg(a),g=Q(t,{}),m=[{x:c-h/2,y:l},{x:c+o+h/2,y:l},{x:c+o+h/2,y:l+s},{x:c-h/2,y:l+s}],y=[{x:c+o-h/2,y:l+s},{x:c+o+h/2,y:l+s},{x:c+o+h/2,y:l+s-u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const b=ft(m),x=p.path(b,g),k=ft(y),L=p.path(k,{...g,fillStyle:"solid"}),B=a.insert(()=>L,":first-child");return B.insert(()=>x,":first-child"),B.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&B.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&B.selectAll("path").attr("style",i),J(t,B),t.intersect=function(C){return Y.polygon(t,m,C)},a}f(tp,"taggedRect");async function ep(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=.2*s,u=.2*c,d=c+l,{cssStyles:p}=t,g=X.svg(a),m=Q(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=[{x:-s/2-s/2*.1,y:d/2},...je(-s/2-s/2*.1,d/2,s/2+s/2*.1,d/2,l,.8),{x:s/2+s/2*.1,y:-d/2},{x:-s/2-s/2*.1,y:-d/2}],b=-s/2+s/2*.1,x=-d/2-u*.4,k=[{x:b+s-h,y:(x+c)*1.4},{x:b+s,y:x+c-u},{x:b+s,y:(x+c)*.9},...je(b+s,(x+c)*1.3,b+s-h,(x+c)*1.5,-c*.03,.5)],L=ft(y),B=g.path(L,m),C=ft(k),w=g.path(C,{...m,fillStyle:"solid"}),v=a.insert(()=>w,":first-child");return v.insert(()=>B,":first-child"),v.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-c/2+(t.padding??0)-l/2-(n.y-(n.top??0))})`),J(t,v),t.intersect=function(E){return Y.polygon(t,y,E)},a}f(ep,"taggedWaveEdgedRectangle");async function rp(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=Math.max(n.width+t.padding,(t==null?void 0:t.width)||0),s=Math.max(n.height+t.padding,(t==null?void 0:t.height)||0),c=-o/2,l=-s/2,h=a.insert("rect",":first-child");return h.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",c).attr("y",l).attr("width",o).attr("height",s),J(t,h),t.intersect=function(u){return Y.rect(t,u)},a}f(rp,"text");var JC=f((e,t,r,i,a,n)=>`M${e},${t}
    a${a},${n} 0,0,1 0,${-i}
    l${r},0
    a${a},${n} 0,0,1 0,${i}
    M${r},${-i}
    a${a},${n} 0,0,0 0,${i}
    l${-r},0`,"createCylinderPathD"),t1=f((e,t,r,i,a,n)=>[`M${e},${t}`,`M${e+r},${t}`,`a${a},${n} 0,0,0 0,${-i}`,`l${-r},0`,`a${a},${n} 0,0,0 0,${i}`,`l${r},0`].join(" "),"createOuterCylinderPathD"),e1=f((e,t,r,i,a,n)=>[`M${e+r/2},${-i/2}`,`a${a},${n} 0,0,0 0,${i}`].join(" "),"createInnerCylinderPathD");async function ip(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o,halfPadding:s}=await lt(e,t,st(t)),c=t.look==="neo"?s*2:s,l=n.height+c,h=l/2,u=h/(2.5+l/50),d=n.width+u+c,{cssStyles:p}=t;let g;if(t.look==="handDrawn"){const m=X.svg(a),y=t1(0,0,d,l,u,h),b=e1(0,0,d,l,u,h),x=m.path(y,Q(t,{})),k=m.path(b,Q(t,{fill:"none"}));g=a.insert(()=>k,":first-child"),g=a.insert(()=>x,":first-child"),g.attr("class","basic label-container"),p&&g.attr("style",p)}else{const m=JC(0,0,d,l,u,h);g=a.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",Xt(p)).attr("style",i),g.attr("class","basic label-container"),p&&g.selectAll("path").attr("style",p),i&&g.selectAll("path").attr("style",i)}return g.attr("label-offset-x",u),g.attr("transform",`translate(${-d/2}, ${l/2} )`),o.attr("transform",`translate(${-(n.width/2)-u-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),J(t,g),t.intersect=function(m){const y=Y.rect(t,m),b=y.y-(t.y??0);if(h!=0&&(Math.abs(b)<(t.height??0)/2||Math.abs(b)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-u)){let x=u*u*(1-b*b/(h*h));x!=0&&(x=Math.sqrt(Math.abs(x))),x=u-x,m.x-(t.x??0)>0&&(x=-x),y.x+=x}return y},a}f(ip,"tiltedCylinder");async function ap(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=n.width+t.padding,s=n.height+t.padding,c=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=X.svg(a),d=Q(t,{}),p=ft(c),g=u.path(p,d);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,J(t,l),t.intersect=function(u){return Y.polygon(t,c,u)},a}f(ap,"trapezoid");async function np(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=60,s=20,c=Math.max(o,n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s,n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),{cssStyles:h}=t,u=X.svg(a),d=Q(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=[{x:-c/2*.8,y:-l/2},{x:c/2*.8,y:-l/2},{x:c/2,y:-l/2*.6},{x:c/2,y:l/2},{x:-c/2,y:l/2},{x:-c/2,y:-l/2*.6}],g=ft(p),m=u.path(g,d),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),J(t,y),t.intersect=function(b){return Y.polygon(t,p,b)},a}f(np,"trapezoidalPentagon");async function sp(e,t){var x;const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Mt((x=mt().flowchart)==null?void 0:x.htmlLabels),c=n.width+(t.padding??0),l=c+n.height,h=c+n.height,u=[{x:0,y:0},{x:h,y:0},{x:h/2,y:-l}],{cssStyles:d}=t,p=X.svg(a),g=Q(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=ft(u),y=p.path(m,g),b=a.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return d&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",d),i&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",i),t.width=c,t.height=l,J(t,b),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${l/2-(n.height+(t.padding??0)/(s?2:1)-(n.y-(n.top??0)))})`),t.intersect=function(k){return P.info("Triangle intersect",t,u,k),Y.polygon(t,u,k)},a}f(sp,"triangle");async function op(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/8,h=c+l,{cssStyles:u}=t,p=70-s,g=p>0?p/2:0,m=X.svg(a),y=Q(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");const b=[{x:-s/2-g,y:h/2},...je(-s/2-g,h/2,s/2+g,h/2,l,.8),{x:s/2+g,y:-h/2},{x:-s/2-g,y:-h/2}],x=ft(b),k=m.path(x,y),L=a.insert(()=>k,":first-child");return L.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&L.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&L.selectAll("path").attr("style",i),L.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-c/2+(t.padding??0)-l-(n.y-(n.top??0))})`),J(t,L),t.intersect=function(B){return Y.polygon(t,b,B)},a}f(op,"waveEdgedRectangle");async function lp(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await lt(e,t,st(t)),o=100,s=50,c=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=c/l;let u=c,d=l;u>d*h?d=u/h:u=d*h,u=Math.max(u,o),d=Math.max(d,s);const p=Math.min(d*.2,d/4),g=d+p*2,{cssStyles:m}=t,y=X.svg(a),b=Q(t,{});t.look!=="handDrawn"&&(b.roughness=0,b.fillStyle="solid");const x=[{x:-u/2,y:g/2},...je(-u/2,g/2,u/2,g/2,p,1),{x:u/2,y:-g/2},...je(u/2,-g/2,-u/2,-g/2,p,-1)],k=ft(x),L=y.path(k,b),B=a.insert(()=>L,":first-child");return B.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&B.selectAll("path").attr("style",m),i&&t.look!=="handDrawn"&&B.selectAll("path").attr("style",i),J(t,B),t.intersect=function(C){return Y.polygon(t,x,C)},a}f(lp,"waveRectangle");async function cp(e,t){const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await lt(e,t,st(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:d}=t,p=X.svg(a),g=Q(t,{}),m=[{x:h-l,y:u-l},{x:h-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u-l}],y=`M${h-l},${u-l} L${h+s},${u-l} L${h+s},${u+c} L${h-l},${u+c} L${h-l},${u-l}
                M${h-l},${u} L${h+s},${u}
                M${h},${u-l} L${h},${u+c}`;t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const b=p.path(y,g),x=a.insert(()=>b,":first-child");return x.attr("transform",`translate(${l/2}, ${l/2})`),x.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)+l/2-(n.x-(n.left??0))}, ${-(n.height/2)+l/2-(n.y-(n.top??0))})`),J(t,x),t.intersect=function(k){return Y.polygon(t,m,k)},a}f(cp,"windowPane");async function Qs(e,t){var W,j,U;const r=t;if(r.alias&&(t.label=r.alias),t.look==="handDrawn"){const{themeVariables:Z}=re(),{background:V}=Z,ht={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${V}`]};await Qs(e,ht)}const i=re();t.useHtmlLabels=i.htmlLabels;let a=((W=i.er)==null?void 0:W.diagramPadding)??10,n=((j=i.er)==null?void 0:j.entityPadding)??6;const{cssStyles:o}=t,{labelStyles:s}=tt(t);if(r.attributes.length===0&&t.label){const Z={rx:0,ry:0,labelPaddingX:a,labelPaddingY:a*1.5};Oe(t.label,i)+Z.labelPaddingX*2<i.er.minEntityWidth&&(t.width=i.er.minEntityWidth);const V=await Mi(e,t,Z);if(!Mt(i.htmlLabels)){const ht=V.select("text"),N=(U=ht.node())==null?void 0:U.getBBox();ht.attr("transform",`translate(${-N.width/2}, 0)`)}return V}i.htmlLabels||(a*=1.25,n*=1.25);let c=st(t);c||(c="node default");const l=e.insert("g").attr("class",c).attr("id",t.domId||t.id),h=await br(l,t.label??"",i,0,0,["name"],s);h.height+=n;let u=0;const d=[];let p=0,g=0,m=0,y=0,b=!0,x=!0;for(const Z of r.attributes){const V=await br(l,Z.type,i,0,u,["attribute-type"],s);p=Math.max(p,V.width+a);const ht=await br(l,Z.name,i,0,u,["attribute-name"],s);g=Math.max(g,ht.width+a);const N=await br(l,Z.keys.join(),i,0,u,["attribute-keys"],s);m=Math.max(m,N.width+a);const at=await br(l,Z.comment,i,0,u,["attribute-comment"],s);y=Math.max(y,at.width+a),u+=Math.max(V.height,ht.height,N.height,at.height)+n,d.push(u)}d.pop();let k=4;m<=a&&(b=!1,m=0,k--),y<=a&&(x=!1,y=0,k--);const L=l.node().getBBox();if(h.width+a*2-(p+g+m+y)>0){const Z=h.width+a*2-(p+g+m+y);p+=Z/k,g+=Z/k,m>0&&(m+=Z/k),y>0&&(y+=Z/k)}const B=p+g+m+y,C=X.svg(l),w=Q(t,{});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");const v=Math.max(L.width+a*2,(t==null?void 0:t.width)||0,B),E=Math.max(L.height+(d[0]||u)+n,(t==null?void 0:t.height)||0),_=-v/2,S=-E/2;l.selectAll("g:not(:first-child)").each((Z,V,ht)=>{const N=pt(ht[V]),at=N.attr("transform");let ct=0,ut=0;if(at){const gt=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(at);gt&&(ct=parseFloat(gt[1]),ut=parseFloat(gt[2]),N.attr("class").includes("attribute-name")?ct+=p:N.attr("class").includes("attribute-keys")?ct+=p+g:N.attr("class").includes("attribute-comment")&&(ct+=p+g+m))}N.attr("transform",`translate(${_+a/2+ct}, ${ut+S+h.height+n/2})`)}),l.select(".name").attr("transform","translate("+-h.width/2+", "+(S+n/2)+")");const $=C.rectangle(_,S,v,E,w),R=l.insert(()=>$,":first-child").attr("style",o.join("")),{themeVariables:M}=re(),{rowEven:O,rowOdd:F,nodeBorder:I}=M;d.push(0);for(const[Z,V]of d.entries()){if(Z===0&&d.length>1)continue;const ht=Z%2===0&&V!==0,N=C.rectangle(_,h.height+S+V,v,h.height,{...w,fill:ht?O:F,stroke:I});l.insert(()=>N,"g.label").attr("style",o.join("")).attr("class",`row-rect-${Z%2===0?"even":"odd"}`)}let D=C.line(_,h.height+S,v+_,h.height+S,w);l.insert(()=>D).attr("class","divider"),D=C.line(p+_,h.height+S,p+_,E+S,w),l.insert(()=>D).attr("class","divider"),b&&(D=C.line(p+g+_,h.height+S,p+g+_,E+S,w),l.insert(()=>D).attr("class","divider")),x&&(D=C.line(p+g+m+_,h.height+S,p+g+m+_,E+S,w),l.insert(()=>D).attr("class","divider"));for(const Z of d)D=C.line(_,h.height+S+Z,v+_,h.height+S+Z,w),l.insert(()=>D).attr("class","divider");return J(t,R),t.intersect=function(Z){return Y.rect(t,Z)},l}f(Qs,"erBox");async function br(e,t,r,i=0,a=0,n=[],o=""){const s=e.insert("g").attr("class",`label ${n.join(" ")}`).attr("transform",`translate(${i}, ${a})`).attr("style",o);t!==Do(t)&&(t=Do(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));const c=s.node().appendChild(await Ue(s,t,{width:Oe(t,r)+100,style:o,useHtmlLabels:r.htmlLabels},r));if(t.includes("&lt;")||t.includes("&gt;")){let h=c.children[0];for(h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");h.childNodes[0];)h=h.childNodes[0],h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let l=c.getBBox();if(Mt(r.htmlLabels)){const h=c.children[0];h.style.textAlign="start";const u=pt(c);l=h.getBoundingClientRect(),u.attr("width",l.width),u.attr("height",l.height)}return l}f(br,"addText");async function hp(e,t,r,i,a=r.class.padding??12){const n=i?0:3,o=e.insert("g").attr("class",st(t)).attr("id",t.domId||t.id);let s=null,c=null,l=null,h=null,u=0,d=0,p=0;if(s=o.insert("g").attr("class","annotation-group text"),t.annotations.length>0){const x=t.annotations[0];await oi(s,{text:`«${x}»`},0),u=s.node().getBBox().height}c=o.insert("g").attr("class","label-group text"),await oi(c,t,0,["font-weight: bolder"]);const g=c.node().getBBox();d=g.height,l=o.insert("g").attr("class","members-group text");let m=0;for(const x of t.members){const k=await oi(l,x,m,[x.parseClassifier()]);m+=k+n}p=l.node().getBBox().height,p<=0&&(p=a/2),h=o.insert("g").attr("class","methods-group text");let y=0;for(const x of t.methods){const k=await oi(h,x,y,[x.parseClassifier()]);y+=k+n}let b=o.node().getBBox();if(s!==null){const x=s.node().getBBox();s.attr("transform",`translate(${-x.width/2})`)}return c.attr("transform",`translate(${-g.width/2}, ${u})`),b=o.node().getBBox(),l.attr("transform",`translate(0, ${u+d+a*2})`),b=o.node().getBBox(),h.attr("transform",`translate(0, ${u+d+(p?p+a*4:a*2)})`),b=o.node().getBBox(),{shapeSvg:o,bbox:b}}f(hp,"textHelper");async function oi(e,t,r,i=[]){const a=e.insert("g").attr("class","label").attr("style",i.join("; ")),n=re();let o="useHtmlLabels"in t?t.useHtmlLabels:Mt(n.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!o&&s.startsWith("\\")&&(s=s.substring(1)),Or(s)&&(o=!0);const c=await Ue(a,xs(lr(s)),{width:Oe(s,n)+50,classes:"markdown-node-label",useHtmlLabels:o},n);let l,h=1;if(o){const u=c.children[0],d=pt(c);h=u.innerHTML.split("<br>").length,u.innerHTML.includes("</math>")&&(h+=u.innerHTML.split("<mrow>").length-1);const p=u.getElementsByTagName("img");if(p){const g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...p].map(m=>new Promise(y=>{function b(){var x;if(m.style.display="flex",m.style.flexDirection="column",g){const k=((x=n.fontSize)==null?void 0:x.toString())??window.getComputedStyle(document.body).fontSize,B=parseInt(k,10)*5+"px";m.style.minWidth=B,m.style.maxWidth=B}else m.style.width="100%";y(m)}f(b,"setupImage"),setTimeout(()=>{m.complete&&b()}),m.addEventListener("error",b),m.addEventListener("load",b)})))}l=u.getBoundingClientRect(),d.attr("width",l.width),d.attr("height",l.height)}else{i.includes("font-weight: bolder")&&pt(c).selectAll("tspan").attr("font-weight",""),h=c.children.length;const u=c.children[0];(c.textContent===""||c.textContent.includes("&gt"))&&(u.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(u.textContent=u.textContent[0]+" "+u.textContent.substring(1))),u.textContent==="undefined"&&(u.textContent=""),l=c.getBBox()}return a.attr("transform","translate(0,"+(-l.height/(2*h)+r)+")"),l.height}f(oi,"addText");async function up(e,t){var E,_;const r=mt(),i=r.class.padding??12,a=i,n=t.useHtmlLabels??Mt(r.htmlLabels)??!0,o=t;o.annotations=o.annotations??[],o.members=o.members??[],o.methods=o.methods??[];const{shapeSvg:s,bbox:c}=await hp(e,t,r,n,a),{labelStyles:l,nodeStyles:h}=tt(t);t.labelStyle=l,t.cssStyles=o.styles||"";const u=((E=o.styles)==null?void 0:E.join(";"))||h||"";t.cssStyles||(t.cssStyles=u.replaceAll("!important","").split(";"));const d=o.members.length===0&&o.methods.length===0&&!((_=r.class)!=null&&_.hideEmptyMembersBox),p=X.svg(s),g=Q(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=c.width;let y=c.height;o.members.length===0&&o.methods.length===0?y+=a:o.members.length>0&&o.methods.length===0&&(y+=a*2);const b=-m/2,x=-y/2,k=p.rectangle(b-i,x-i-(d?i:o.members.length===0&&o.methods.length===0?-i/2:0),m+2*i,y+2*i+(d?i*2:o.members.length===0&&o.methods.length===0?-i:0),g),L=s.insert(()=>k,":first-child");L.attr("class","basic label-container");const B=L.node().getBBox();s.selectAll(".text").each((S,$,R)=>{var W;const M=pt(R[$]),O=M.attr("transform");let F=0;if(O){const U=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(O);U&&(F=parseFloat(U[2]))}let I=F+x+i-(d?i:o.members.length===0&&o.methods.length===0?-i/2:0);n||(I-=4);let D=b;(M.attr("class").includes("label-group")||M.attr("class").includes("annotation-group"))&&(D=-((W=M.node())==null?void 0:W.getBBox().width)/2||0,s.selectAll("text").each(function(j,U,Z){window.getComputedStyle(Z[U]).textAnchor==="middle"&&(D=0)})),M.attr("transform",`translate(${D}, ${I})`)});const C=s.select(".annotation-group").node().getBBox().height-(d?i/2:0)||0,w=s.select(".label-group").node().getBBox().height-(d?i/2:0)||0,v=s.select(".members-group").node().getBBox().height-(d?i/2:0)||0;if(o.members.length>0||o.methods.length>0||d){const S=p.line(B.x,C+w+x+i,B.x+B.width,C+w+x+i,g);s.insert(()=>S).attr("class","divider").attr("style",u)}if(d||o.members.length>0||o.methods.length>0){const S=p.line(B.x,C+w+v+x+a*2+i,B.x+B.width,C+w+v+x+i+a*2,g);s.insert(()=>S).attr("class","divider").attr("style",u)}if(o.look!=="handDrawn"&&s.selectAll("path").attr("style",u),L.select(":nth-child(2)").attr("style",u),s.selectAll(".divider").select("path").attr("style",u),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",u),!n){const S=RegExp(/color\s*:\s*([^;]*)/),$=S.exec(u);if($){const R=$[0].replace("color","fill");s.selectAll("tspan").attr("style",R)}else if(l){const R=S.exec(l);if(R){const M=R[0].replace("color","fill");s.selectAll("tspan").attr("style",M)}}}return J(t,L),t.intersect=function(S){return Y.rect(t,S)},s}f(up,"classBox");async function dp(e,t){var C,w;const{labelStyles:r,nodeStyles:i}=tt(t);t.labelStyle=r;const a=t,n=t,o=20,s=20,c="verifyMethod"in t,l=st(t),h=e.insert("g").attr("class",l).attr("id",t.domId??t.id);let u;c?u=await ge(h,`&lt;&lt;${a.type}&gt;&gt;`,0,t.labelStyle):u=await ge(h,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let d=u;const p=await ge(h,a.name,d,t.labelStyle+"; font-weight: bold;");if(d+=p+s,c){const v=await ge(h,`${a.requirementId?`Id: ${a.requirementId}`:""}`,d,t.labelStyle);d+=v;const E=await ge(h,`${a.text?`Text: ${a.text}`:""}`,d,t.labelStyle);d+=E;const _=await ge(h,`${a.risk?`Risk: ${a.risk}`:""}`,d,t.labelStyle);d+=_,await ge(h,`${a.verifyMethod?`Verification: ${a.verifyMethod}`:""}`,d,t.labelStyle)}else{const v=await ge(h,`${n.type?`Type: ${n.type}`:""}`,d,t.labelStyle);d+=v,await ge(h,`${n.docRef?`Doc Ref: ${n.docRef}`:""}`,d,t.labelStyle)}const g=(((C=h.node())==null?void 0:C.getBBox().width)??200)+o,m=(((w=h.node())==null?void 0:w.getBBox().height)??200)+o,y=-g/2,b=-m/2,x=X.svg(h),k=Q(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const L=x.rectangle(y,b,g,m,k),B=h.insert(()=>L,":first-child");if(B.attr("class","basic label-container").attr("style",i),h.selectAll(".label").each((v,E,_)=>{const S=pt(_[E]),$=S.attr("transform");let R=0,M=0;if($){const D=RegExp(/translate\(([^,]+),([^)]+)\)/).exec($);D&&(R=parseFloat(D[1]),M=parseFloat(D[2]))}const O=M-m/2;let F=y+o/2;(E===0||E===1)&&(F=R),S.attr("transform",`translate(${F}, ${O+o})`)}),d>u+p+s){const v=x.line(y,b+u+p+s,y+g,b+u+p+s,k);h.insert(()=>v).attr("style",i)}return J(t,B),t.intersect=function(v){return Y.rect(t,v)},h}f(dp,"requirementBox");async function ge(e,t,r,i=""){if(t==="")return 0;const a=e.insert("g").attr("class","label").attr("style",i),n=mt(),o=n.htmlLabels??!0,s=await Ue(a,xs(lr(t)),{width:Oe(t,n)+50,classes:"markdown-node-label",useHtmlLabels:o,style:i},n);let c;if(o){const l=s.children[0],h=pt(s);c=l.getBoundingClientRect(),h.attr("width",c.width),h.attr("height",c.height)}else{const l=s.children[0];for(const h of l.children)h.textContent=h.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),i&&h.setAttribute("style",i);c=s.getBBox(),c.height+=6}return a.attr("transform",`translate(${-c.width/2},${-c.height/2+r})`),c.height}f(ge,"addText");var r1=f(e=>{switch(e){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function pp(e,t,{config:r}){var $,R;const{labelStyles:i,nodeStyles:a}=tt(t);t.labelStyle=i||"";const n=10,o=t.width;t.width=(t.width??200)-10;const{shapeSvg:s,bbox:c,label:l}=await lt(e,t,st(t)),h=t.padding||10;let u="",d;"ticket"in t&&t.ticket&&(($=r==null?void 0:r.kanban)!=null&&$.ticketBaseUrl)&&(u=(R=r==null?void 0:r.kanban)==null?void 0:R.ticketBaseUrl.replace("#TICKET#",t.ticket),d=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",u).attr("target","_blank"));const p={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1};let g,m;d?{label:g,bbox:m}=await Cn(d,"ticket"in t&&t.ticket||"",p):{label:g,bbox:m}=await Cn(s,"ticket"in t&&t.ticket||"",p);const{label:y,bbox:b}=await Cn(s,"assigned"in t&&t.assigned||"",p);t.width=o;const x=10,k=(t==null?void 0:t.width)||0,L=Math.max(m.height,b.height)/2,B=Math.max(c.height+x*2,(t==null?void 0:t.height)||0)+L,C=-k/2,w=-B/2;l.attr("transform","translate("+(h-k/2)+", "+(-L-c.height/2)+")"),g.attr("transform","translate("+(h-k/2)+", "+(-L+c.height/2)+")"),y.attr("transform","translate("+(h+k/2-b.width-2*n)+", "+(-L+c.height/2)+")");let v;const{rx:E,ry:_}=t,{cssStyles:S}=t;if(t.look==="handDrawn"){const M=X.svg(s),O=Q(t,{}),F=E||_?M.path(Re(C,w,k,B,E||0),O):M.rectangle(C,w,k,B,O);v=s.insert(()=>F,":first-child"),v.attr("class","basic label-container").attr("style",S||null)}else{v=s.insert("rect",":first-child"),v.attr("class","basic label-container __APA__").attr("style",a).attr("rx",E??5).attr("ry",_??5).attr("x",C).attr("y",w).attr("width",k).attr("height",B);const M="priority"in t&&t.priority;if(M){const O=s.append("line"),F=C+2,I=w+Math.floor((E??0)/2),D=w+B-Math.floor((E??0)/2);O.attr("x1",F).attr("y1",I).attr("x2",F).attr("y2",D).attr("stroke-width","4").attr("stroke",r1(M))}}return J(t,v),t.height=B,t.intersect=function(M){return Y.rect(t,M)},s}f(pp,"kanbanItem");var i1=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:Vd},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:Ud},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:Xd},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:Jd},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:bd},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:ud},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:qd},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:Td},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:$d},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:Od},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:ap},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:Fd},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:Cd},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:rp},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:cd},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:Gd},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:Qd},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:Kd},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:vd},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:_d},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:fd},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:gd},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:md},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:Rd},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:op},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:Sd},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:ip},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:Id},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:yd},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:xd},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:sp},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:cp},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:wd},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:np},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:kd},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:Yd},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:zd},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:Nd},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:ld},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:pd},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:ep},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:tp},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:lp},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:jd},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:Pd}],a1=f(()=>{const t=[...Object.entries({state:Zd,choice:hd,note:Wd,rectWithTitle:Hd,labelRect:Dd,iconSquare:Md,iconCircle:Ld,icon:Bd,iconRounded:Ad,imageSquare:Ed,anchor:od,kanbanItem:pp,classBox:up,erBox:Qs,requirementBox:dp}),...i1.flatMap(r=>[r.shortName,..."aliases"in r?r.aliases:[],..."internalAliases"in r?r.internalAliases:[]].map(a=>[a,r.handler]))];return Object.fromEntries(t)},"generateShapeMap"),fp=a1();function n1(e){return e in fp}f(n1,"isValidShape");var Ya=new Map;async function gp(e,t,r){let i,a;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");const n=t.shape?fp[t.shape]:void 0;if(!n)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let o;r.config.securityLevel==="sandbox"?o="_top":t.linkTarget&&(o=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",o??null),a=await n(i,t,r)}else a=await n(e,t,r),i=a;return t.tooltip&&a.attr("title",t.tooltip),Ya.set(t.id,i),t.haveCallback&&i.attr("class",i.attr("class")+" clickable"),i}f(gp,"insertNode");var iv=f((e,t)=>{Ya.set(t.id,e)},"setNodeElem"),av=f(()=>{Ya.clear()},"clear"),nv=f(e=>{const t=Ya.get(e.id);P.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode"),s1=f((e,t,r,i,a,n)=>{t.arrowTypeStart&&ml(e,"start",t.arrowTypeStart,r,i,a,n),t.arrowTypeEnd&&ml(e,"end",t.arrowTypeEnd,r,i,a,n)},"addEdgeMarkers"),o1={arrow_cross:{type:"cross",fill:!1},arrow_point:{type:"point",fill:!0},arrow_barb:{type:"barb",fill:!0},arrow_circle:{type:"circle",fill:!1},aggregation:{type:"aggregation",fill:!1},extension:{type:"extension",fill:!1},composition:{type:"composition",fill:!0},dependency:{type:"dependency",fill:!0},lollipop:{type:"lollipop",fill:!1},only_one:{type:"onlyOne",fill:!1},zero_or_one:{type:"zeroOrOne",fill:!1},one_or_more:{type:"oneOrMore",fill:!1},zero_or_more:{type:"zeroOrMore",fill:!1},requirement_arrow:{type:"requirement_arrow",fill:!1},requirement_contains:{type:"requirement_contains",fill:!1}},ml=f((e,t,r,i,a,n,o)=>{var u;const s=o1[r];if(!s){P.warn(`Unknown arrow type: ${r}`);return}const c=s.type,h=`${a}_${n}-${c}${t==="start"?"Start":"End"}`;if(o&&o.trim()!==""){const d=o.replace(/[^\dA-Za-z]/g,"_"),p=`${h}_${d}`;if(!document.getElementById(p)){const g=document.getElementById(h);if(g){const m=g.cloneNode(!0);m.id=p,m.querySelectorAll("path, circle, line").forEach(b=>{b.setAttribute("stroke",o),s.fill&&b.setAttribute("fill",o)}),(u=g.parentNode)==null||u.appendChild(m)}}e.attr(`marker-${t}`,`url(${i}#${p})`)}else e.attr(`marker-${t}`,`url(${i}#${h})`)},"addEdgeMarker"),Ba=new Map,$t=new Map,sv=f(()=>{Ba.clear(),$t.clear()},"clear"),ai=f(e=>e?e.reduce((r,i)=>r+";"+i,""):"","getLabelStyles"),l1=f(async(e,t)=>{let r=Mt(mt().flowchart.htmlLabels);const i=await Ue(e,t.label,{style:ai(t.labelStyle),useHtmlLabels:r,addSvgBackground:!0,isNode:!1});P.info("abc82",t,t.labelType);const a=e.insert("g").attr("class","edgeLabel"),n=a.insert("g").attr("class","label");n.node().appendChild(i);let o=i.getBBox();if(r){const c=i.children[0],l=pt(i);o=c.getBoundingClientRect(),l.attr("width",o.width),l.attr("height",o.height)}n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),Ba.set(t.id,a),t.width=o.width,t.height=o.height;let s;if(t.startLabelLeft){const c=await Qe(t.startLabelLeft,ai(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).startLeft=l,li(s,t.startLabelLeft)}if(t.startLabelRight){const c=await Qe(t.startLabelRight,ai(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=l.node().appendChild(c),h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).startRight=l,li(s,t.startLabelRight)}if(t.endLabelLeft){const c=await Qe(t.endLabelLeft,ai(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).endLeft=l,li(s,t.endLabelLeft)}if(t.endLabelRight){const c=await Qe(t.endLabelRight,ai(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).endRight=l,li(s,t.endLabelRight)}return i},"insertEdgeLabel");function li(e,t){mt().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}f(li,"setTerminalWidth");var c1=f((e,t)=>{P.debug("Moving label abc88 ",e.id,e.label,Ba.get(e.id),t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=mt(),{subGraphTitleTotalMargin:a}=Bs(i);if(e.label){const n=Ba.get(e.id);let o=e.x,s=e.y;if(r){const c=ye.calcLabelPosition(r);P.debug("Moving label "+e.label+" from (",o,",",s,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(o=c.x,s=c.y)}n.attr("transform",`translate(${o}, ${s+a/2})`)}if(e.startLabelLeft){const n=$t.get(e.id).startLeft;let o=e.x,s=e.y;if(r){const c=ye.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.startLabelRight){const n=$t.get(e.id).startRight;let o=e.x,s=e.y;if(r){const c=ye.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelLeft){const n=$t.get(e.id).endLeft;let o=e.x,s=e.y;if(r){const c=ye.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelRight){const n=$t.get(e.id).endRight;let o=e.x,s=e.y;if(r){const c=ye.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}},"positionEdgeLabel"),h1=f((e,t)=>{const r=e.x,i=e.y,a=Math.abs(t.x-r),n=Math.abs(t.y-i),o=e.width/2,s=e.height/2;return a>=o||n>=s},"outsideNode"),u1=f((e,t,r)=>{P.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,a=e.y,n=Math.abs(i-r.x),o=e.width/2;let s=r.x<t.x?o-n:o+n;const c=e.height/2,l=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(a-t.y)*o>Math.abs(i-t.x)*c){let u=r.y<t.y?t.y-c-a:a-c-t.y;s=h*u/l;const d={x:r.x<t.x?r.x+s:r.x-h+s,y:r.y<t.y?r.y+l-u:r.y-l+u};return s===0&&(d.x=t.x,d.y=t.y),h===0&&(d.x=t.x),l===0&&(d.y=t.y),P.debug(`abc89 top/bottom calc, Q ${l}, q ${u}, R ${h}, r ${s}`,d),d}else{r.x<t.x?s=t.x-o-i:s=i-o-t.x;let u=l*s/h,d=r.x<t.x?r.x+h-s:r.x-h+s,p=r.y<t.y?r.y+u:r.y-u;return P.debug(`sides calc abc89, Q ${l}, q ${u}, R ${h}, r ${s}`,{_x:d,_y:p}),s===0&&(d=t.x,p=t.y),h===0&&(d=t.x),l===0&&(p=t.y),{x:d,y:p}}},"intersection"),yl=f((e,t)=>{P.warn("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],a=!1;return e.forEach(n=>{if(P.info("abc88 checking point",n,t),!h1(t,n)&&!a){const o=u1(t,i,n);P.debug("abc88 inside",n,i,o),P.debug("abc88 intersection",o,t);let s=!1;r.forEach(c=>{s=s||c.x===o.x&&c.y===o.y}),r.some(c=>c.x===o.x&&c.y===o.y)?P.warn("abc88 no intersect",o,r):r.push(o),a=!0}else P.warn("abc88 outside",n,i),i=n,a||r.push(n)}),P.debug("returning points",r),r},"cutPathAtIntersect");function mp(e){const t=[],r=[];for(let i=1;i<e.length-1;i++){const a=e[i-1],n=e[i],o=e[i+1];(a.x===n.x&&n.y===o.y&&Math.abs(n.x-o.x)>5&&Math.abs(n.y-a.y)>5||a.y===n.y&&n.x===o.x&&Math.abs(n.x-a.x)>5&&Math.abs(n.y-o.y)>5)&&(t.push(n),r.push(i))}return{cornerPoints:t,cornerPointPositions:r}}f(mp,"extractCornerPoints");var bl=f(function(e,t,r){const i=t.x-e.x,a=t.y-e.y,n=Math.sqrt(i*i+a*a),o=r/n;return{x:t.x-o*i,y:t.y-o*a}},"findAdjacentPoint"),d1=f(function(e){const{cornerPointPositions:t}=mp(e),r=[];for(let i=0;i<e.length;i++)if(t.includes(i)){const a=e[i-1],n=e[i+1],o=e[i],s=bl(a,o,5),c=bl(n,o,5),l=c.x-s.x,h=c.y-s.y;r.push(s);const u=Math.sqrt(2)*2;let d={x:o.x,y:o.y};if(Math.abs(n.x-a.x)>10&&Math.abs(n.y-a.y)>=10){P.debug("Corner point fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));const p=5;o.x===s.x?d={x:l<0?s.x-p+u:s.x+p-u,y:h<0?s.y-u:s.y+u}:d={x:l<0?s.x-u:s.x+u,y:h<0?s.y-p+u:s.y+p-u}}else P.debug("Corner point skipping fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));r.push(d,c)}else r.push(e[i]);return r},"fixCorners"),p1=f(function(e,t,r,i,a,n,o){var E;const{handDrawnSeed:s}=mt();let c=t.points,l=!1;const h=a;var u=n;const d=[];for(const _ in t.cssCompiledStyles)Qu(_)||d.push(t.cssCompiledStyles[_]);u.intersect&&h.intersect&&(c=c.slice(1,t.points.length-1),c.unshift(h.intersect(c[0])),P.debug("Last point APA12",t.start,"-->",t.end,c[c.length-1],u,u.intersect(c[c.length-1])),c.push(u.intersect(c[c.length-1]))),t.toCluster&&(P.info("to cluster abc88",r.get(t.toCluster)),c=yl(t.points,r.get(t.toCluster).node),l=!0),t.fromCluster&&(P.debug("from cluster abc88",r.get(t.fromCluster),JSON.stringify(c,null,2)),c=yl(c.reverse(),r.get(t.fromCluster).node).reverse(),l=!0);let p=c.filter(_=>!Number.isNaN(_.y));p=d1(p);let g=Gi;switch(g=Sn,t.curve){case"linear":g=Sn;break;case"basis":g=Gi;break;case"cardinal":g=Wl;break;case"bumpX":g=jl;break;case"bumpY":g=ql;break;case"catmullRom":g=zl;break;case"monotoneX":g=Nl;break;case"monotoneY":g=Pl;break;case"natural":g=Il;break;case"step":g=Rl;break;case"stepAfter":g=$l;break;case"stepBefore":g=Ol;break;default:g=Gi}const{x:m,y}=uy(t),b=Ef().x(m).y(y).curve(g);let x;switch(t.thickness){case"normal":x="edge-thickness-normal";break;case"thick":x="edge-thickness-thick";break;case"invisible":x="edge-thickness-invisible";break;default:x="edge-thickness-normal"}switch(t.pattern){case"solid":x+=" edge-pattern-solid";break;case"dotted":x+=" edge-pattern-dotted";break;case"dashed":x+=" edge-pattern-dashed";break;default:x+=" edge-pattern-solid"}let k,L=b(p);const B=Array.isArray(t.style)?t.style:[t.style];let C=B.find(_=>_==null?void 0:_.startsWith("stroke:"));if(t.look==="handDrawn"){const _=X.svg(e);Object.assign([],p);const S=_.path(L,{roughness:.3,seed:s});x+=" transition",k=pt(S).select("path").attr("id",t.id).attr("class"," "+x+(t.classes?" "+t.classes:"")).attr("style",B?B.reduce((R,M)=>R+";"+M,""):"");let $=k.attr("d");k.attr("d",$),e.node().appendChild(k.node())}else{const _=d.join(";"),S=B?B.reduce((M,O)=>M+O+";",""):"";let $="";t.animate&&($=" edge-animation-fast"),t.animation&&($=" edge-animation-"+t.animation);const R=_?_+";"+S+";":S;k=e.append("path").attr("d",L).attr("id",t.id).attr("class"," "+x+(t.classes?" "+t.classes:"")+($??"")).attr("style",R),C=(E=R.match(/stroke:([^;]+)/))==null?void 0:E[1]}let w="";(mt().flowchart.arrowMarkerAbsolute||mt().state.arrowMarkerAbsolute)&&(w=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,w=w.replace(/\(/g,"\\(").replace(/\)/g,"\\)")),P.info("arrowTypeStart",t.arrowTypeStart),P.info("arrowTypeEnd",t.arrowTypeEnd),s1(k,t,w,o,i,C);let v={};return l&&(v.updatedPath=c),v.originalPath=t.points,v},"insertEdge"),f1=f((e,t,r,i)=>{t.forEach(a=>{M1[a](e,r,i)})},"insertMarkers"),g1=f((e,t,r)=>{P.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),m1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),y1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),b1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),x1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),C1=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),w1=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),k1=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),v1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),S1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneStart").attr("class","marker onlyOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18"),e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneEnd").attr("class","marker onlyOne "+t).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")},"only_one"),T1=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneStart").attr("class","marker zeroOrOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),i.append("path").attr("d","M9,0 L9,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+t).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),a.append("path").attr("d","M21,0 L21,18")},"zero_or_one"),_1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreStart").attr("class","marker oneOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreEnd").attr("class","marker oneOrMore "+t).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")},"one_or_more"),B1=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+t).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),a.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"zero_or_more"),L1=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0
      L20,10
      M20,10
      L0,20`)},"requirement_arrow"),A1=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_containsStart").attr("refX",0).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",9).attr("fill","none"),i.append("line").attr("x1",1).attr("x2",19).attr("y1",10).attr("y2",10),i.append("line").attr("y1",1).attr("y2",19).attr("x1",10).attr("x2",10)},"requirement_contains"),M1={extension:g1,composition:m1,aggregation:y1,dependency:b1,lollipop:x1,point:C1,circle:w1,cross:k1,barb:v1,only_one:S1,zero_or_one:T1,one_or_more:_1,zero_or_more:B1,requirement_arrow:L1,requirement_contains:A1},E1=f1,F1={common:Nr,getConfig:re,insertCluster:zC,insertEdge:p1,insertEdgeLabel:l1,insertMarkers:E1,insertNode:gp,interpolateToCurve:Ds,labelHelper:lt,log:P,positionEdgeLabel:c1},wi={},yp=f(e=>{for(const t of e)wi[t.name]=t},"registerLayoutLoaders"),D1=f(()=>{yp([{name:"dagre",loader:f(async()=>await xt(()=>import("./dagre-OKDRZEBW-demB6D4E.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]),import.meta.url),"loader")}])},"registerDefaultLayoutLoaders");D1();var ov=f(async(e,t)=>{if(!(e.layoutAlgorithm in wi))throw new Error(`Unknown layout algorithm: ${e.layoutAlgorithm}`);const r=wi[e.layoutAlgorithm];return(await r.loader()).render(e,t,F1,{algorithm:r.algorithm})},"render"),lv=f((e="",{fallback:t="dagre"}={})=>{if(e in wi)return e;if(t in wi)return P.warn(`Layout algorithm ${e} is not registered. Using ${t} as fallback.`),t;throw new Error(`Both layout algorithms ${e} and ${t} are not registered.`)},"getRegisteredLayoutAlgorithm"),xl={name:"mermaid",version:"11.6.0",description:"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.",type:"module",module:"./dist/mermaid.core.mjs",types:"./dist/mermaid.d.ts",exports:{".":{types:"./dist/mermaid.d.ts",import:"./dist/mermaid.core.mjs",default:"./dist/mermaid.core.mjs"},"./*":"./*"},keywords:["diagram","markdown","flowchart","sequence diagram","gantt","class diagram","git graph","mindmap","packet diagram","c4 diagram","er diagram","pie chart","pie diagram","quadrant chart","requirement diagram","graph"],scripts:{clean:"rimraf dist",dev:"pnpm -w dev","docs:code":"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup","docs:build":"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts","docs:verify":"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify","docs:pre:vitepress":"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts","docs:build:vitepress":"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing","docs:dev":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:dev:docker":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev:docker" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:serve":"pnpm docs:build:vitepress && vitepress serve src/vitepress","docs:spellcheck":'cspell "src/docs/**/*.md"',"docs:release-version":"tsx scripts/update-release-version.mts","docs:verify-version":"tsx scripts/update-release-version.mts --verify","types:build-config":"tsx scripts/create-types-from-json-schema.mts","types:verify-config":"tsx scripts/create-types-from-json-schema.mts --verify",checkCircle:"npx madge --circular ./src",prepublishOnly:"pnpm docs:verify-version"},repository:{type:"git",url:"https://github.com/mermaid-js/mermaid"},author:"Knut Sveidqvist",license:"MIT",standard:{ignore:["**/parser/*.js","dist/**/*.js","cypress/**/*.js"],globals:["page"]},dependencies:{"@braintree/sanitize-url":"^7.0.4","@iconify/utils":"^2.1.33","@mermaid-js/parser":"workspace:^","@types/d3":"^7.4.3",cytoscape:"^3.29.3","cytoscape-cose-bilkent":"^4.1.0","cytoscape-fcose":"^2.2.0",d3:"^7.9.0","d3-sankey":"^0.12.3","dagre-d3-es":"7.0.11",dayjs:"^1.11.13",dompurify:"^3.2.4",katex:"^0.16.9",khroma:"^2.1.0","lodash-es":"^4.17.21",marked:"^15.0.7",roughjs:"^4.6.6",stylis:"^4.3.6","ts-dedent":"^2.2.0",uuid:"^11.1.0"},devDependencies:{"@adobe/jsonschema2md":"^8.0.2","@iconify/types":"^2.0.0","@types/cytoscape":"^3.21.9","@types/cytoscape-fcose":"^2.2.4","@types/d3-sankey":"^0.12.4","@types/d3-scale":"^4.0.9","@types/d3-scale-chromatic":"^3.1.0","@types/d3-selection":"^3.0.11","@types/d3-shape":"^3.1.7","@types/jsdom":"^21.1.7","@types/katex":"^0.16.7","@types/lodash-es":"^4.17.12","@types/micromatch":"^4.0.9","@types/stylis":"^4.2.7","@types/uuid":"^10.0.0",ajv:"^8.17.1",chokidar:"^4.0.3",concurrently:"^9.1.2","csstree-validator":"^4.0.1",globby:"^14.0.2",jison:"^0.4.18","js-base64":"^3.7.7",jsdom:"^26.0.0","json-schema-to-typescript":"^15.0.4",micromatch:"^4.0.8","path-browserify":"^1.0.1",prettier:"^3.5.2",remark:"^15.0.1","remark-frontmatter":"^5.0.0","remark-gfm":"^4.0.1",rimraf:"^6.0.1","start-server-and-test":"^2.0.10","type-fest":"^4.35.0",typedoc:"^0.27.8","typedoc-plugin-markdown":"^4.4.2",typescript:"~5.7.3","unist-util-flatmap":"^1.0.0","unist-util-visit":"^5.0.0",vitepress:"^1.0.2","vitepress-plugin-search":"1.0.4-alpha.22"},files:["dist/","README.md"],publishConfig:{access:"public"}},O1=f(e=>{var a;const{securityLevel:t}=mt();let r=pt("body");if(t==="sandbox"){const o=((a=pt(`#i${e}`).node())==null?void 0:a.contentDocument)??document;r=pt(o.body)}return r.select(`#${e}`)},"selectSvgElement"),bp="comm",xp="rule",Cp="decl",$1="@import",R1="@namespace",I1="@keyframes",P1="@layer",wp=Math.abs,Js=String.fromCharCode;function kp(e){return e.trim()}function ea(e,t,r){return e.replace(t,r)}function N1(e,t,r){return e.indexOf(t,r)}function wr(e,t){return e.charCodeAt(t)|0}function Ir(e,t,r){return e.slice(t,r)}function me(e){return e.length}function z1(e){return e.length}function Ui(e,t){return t.push(e),e}var Va=1,Pr=1,vp=0,le=0,_t=0,Hr="";function to(e,t,r,i,a,n,o,s){return{value:e,root:t,parent:r,type:i,props:a,children:n,line:Va,column:Pr,length:o,return:"",siblings:s}}function W1(){return _t}function q1(){return _t=le>0?wr(Hr,--le):0,Pr--,_t===10&&(Pr=1,Va--),_t}function ue(){return _t=le<vp?wr(Hr,le++):0,Pr++,_t===10&&(Pr=1,Va++),_t}function Ne(){return wr(Hr,le)}function ra(){return le}function Xa(e,t){return Ir(Hr,e,t)}function ki(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function j1(e){return Va=Pr=1,vp=me(Hr=e),le=0,[]}function H1(e){return Hr="",e}function wn(e){return kp(Xa(le-1,as(e===91?e+2:e===40?e+1:e)))}function U1(e){for(;(_t=Ne())&&_t<33;)ue();return ki(e)>2||ki(_t)>3?"":" "}function G1(e,t){for(;--t&&ue()&&!(_t<48||_t>102||_t>57&&_t<65||_t>70&&_t<97););return Xa(e,ra()+(t<6&&Ne()==32&&ue()==32))}function as(e){for(;ue();)switch(_t){case e:return le;case 34:case 39:e!==34&&e!==39&&as(_t);break;case 40:e===41&&as(e);break;case 92:ue();break}return le}function Y1(e,t){for(;ue()&&e+_t!==57;)if(e+_t===84&&Ne()===47)break;return"/*"+Xa(t,le-1)+"*"+Js(e===47?e:ue())}function V1(e){for(;!ki(Ne());)ue();return Xa(e,le)}function X1(e){return H1(ia("",null,null,null,[""],e=j1(e),0,[0],e))}function ia(e,t,r,i,a,n,o,s,c){for(var l=0,h=0,u=o,d=0,p=0,g=0,m=1,y=1,b=1,x=0,k="",L=a,B=n,C=i,w=k;y;)switch(g=x,x=ue()){case 40:if(g!=108&&wr(w,u-1)==58){N1(w+=ea(wn(x),"&","&\f"),"&\f",wp(l?s[l-1]:0))!=-1&&(b=-1);break}case 34:case 39:case 91:w+=wn(x);break;case 9:case 10:case 13:case 32:w+=U1(g);break;case 92:w+=G1(ra()-1,7);continue;case 47:switch(Ne()){case 42:case 47:Ui(Z1(Y1(ue(),ra()),t,r,c),c),(ki(g||1)==5||ki(Ne()||1)==5)&&me(w)&&Ir(w,-1,void 0)!==" "&&(w+=" ");break;default:w+="/"}break;case 123*m:s[l++]=me(w)*b;case 125*m:case 59:case 0:switch(x){case 0:case 125:y=0;case 59+h:b==-1&&(w=ea(w,/\f/g,"")),p>0&&(me(w)-u||m===0&&g===47)&&Ui(p>32?wl(w+";",i,r,u-1,c):wl(ea(w," ","")+";",i,r,u-2,c),c);break;case 59:w+=";";default:if(Ui(C=Cl(w,t,r,l,h,a,s,k,L=[],B=[],u,n),n),x===123)if(h===0)ia(w,t,C,C,L,n,u,s,B);else{switch(d){case 99:if(wr(w,3)===110)break;case 108:if(wr(w,2)===97)break;default:h=0;case 100:case 109:case 115:}h?ia(e,C,C,i&&Ui(Cl(e,C,C,0,0,a,s,k,a,L=[],u,B),B),a,B,u,s,i?L:B):ia(w,C,C,C,[""],B,0,s,B)}}l=h=p=0,m=b=1,k=w="",u=o;break;case 58:u=1+me(w),p=g;default:if(m<1){if(x==123)--m;else if(x==125&&m++==0&&q1()==125)continue}switch(w+=Js(x),x*m){case 38:b=h>0?1:(w+="\f",-1);break;case 44:s[l++]=(me(w)-1)*b,b=1;break;case 64:Ne()===45&&(w+=wn(ue())),d=Ne(),h=u=me(k=w+=V1(ra())),x++;break;case 45:g===45&&me(w)==2&&(m=0)}}return n}function Cl(e,t,r,i,a,n,o,s,c,l,h,u){for(var d=a-1,p=a===0?n:[""],g=z1(p),m=0,y=0,b=0;m<i;++m)for(var x=0,k=Ir(e,d+1,d=wp(y=o[m])),L=e;x<g;++x)(L=kp(y>0?p[x]+" "+k:ea(k,/&\f/g,p[x])))&&(c[b++]=L);return to(e,t,r,a===0?xp:s,c,l,h,u)}function Z1(e,t,r,i){return to(e,t,r,bp,Js(W1()),Ir(e,2,-2),0,i)}function wl(e,t,r,i,a){return to(e,t,r,Cp,Ir(e,0,i),Ir(e,i+1,-1),i,a)}function ns(e,t){for(var r="",i=0;i<e.length;i++)r+=t(e[i],i,e,t)||"";return r}function K1(e,t,r,i){switch(e.type){case P1:if(e.children.length)break;case $1:case R1:case Cp:return e.return=e.return||e.value;case bp:return"";case I1:return e.return=e.value+"{"+ns(e.children,i)+"}";case xp:if(!me(e.value=e.props.join(",")))return""}return me(r=ns(e.children,i))?e.return=e.value+"{"+r+"}":""}var Q1=iu(Object.keys,Object),J1=Object.prototype,t2=J1.hasOwnProperty;function e2(e){if(!Na(e))return Q1(e);var t=[];for(var r in Object(e))t2.call(e,r)&&r!="constructor"&&t.push(r);return t}var ss=or(Ce,"DataView"),os=or(Ce,"Promise"),ls=or(Ce,"Set"),cs=or(Ce,"WeakMap"),kl="[object Map]",r2="[object Object]",vl="[object Promise]",Sl="[object Set]",Tl="[object WeakMap]",_l="[object DataView]",i2=sr(ss),a2=sr(Ci),n2=sr(os),s2=sr(ls),o2=sr(cs),Ve=zr;(ss&&Ve(new ss(new ArrayBuffer(1)))!=_l||Ci&&Ve(new Ci)!=kl||os&&Ve(os.resolve())!=vl||ls&&Ve(new ls)!=Sl||cs&&Ve(new cs)!=Tl)&&(Ve=function(e){var t=zr(e),r=t==r2?e.constructor:void 0,i=r?sr(r):"";if(i)switch(i){case i2:return _l;case a2:return kl;case n2:return vl;case s2:return Sl;case o2:return Tl}return t});var l2="[object Map]",c2="[object Set]",h2=Object.prototype,u2=h2.hasOwnProperty;function Bl(e){if(e==null)return!0;if(za(e)&&(ba(e)||typeof e=="string"||typeof e.splice=="function"||Es(e)||Fs(e)||ya(e)))return!e.length;var t=Ve(e);if(t==l2||t==c2)return!e.size;if(Na(e))return!e2(e).length;for(var r in e)if(u2.call(e,r))return!1;return!0}var Sp="c4",d2=f(e=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(e),"detector"),p2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./c4Diagram-VJAJSXHY-BmFvRjRz.js");return{diagram:t}},__vite__mapDeps([17,18,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Sp,diagram:e}},"loader"),f2={id:Sp,detector:d2,loader:p2},g2=f2,Tp="flowchart",m2=f((e,t)=>{var r,i;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-wrapper"||((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"?!1:/^\s*graph/.test(e)},"detector"),y2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-CJF4d5Iv.js");return{diagram:t}},__vite__mapDeps([19,20,11,6,7,21,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Tp,diagram:e}},"loader"),b2={id:Tp,detector:m2,loader:y2},x2=b2,_p="flowchart-v2",C2=f((e,t)=>{var r,i,a;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-d3"?!1:(((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"&&(t.layout="elk"),/^\s*graph/.test(e)&&((a=t==null?void 0:t.flowchart)==null?void 0:a.defaultRenderer)==="dagre-wrapper"?!0:/^\s*flowchart/.test(e))},"detector"),w2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-CJF4d5Iv.js");return{diagram:t}},__vite__mapDeps([19,20,11,6,7,21,8,9,10,12,13,14,15,16]),import.meta.url);return{id:_p,diagram:e}},"loader"),k2={id:_p,detector:C2,loader:w2},v2=k2,Bp="er",S2=f(e=>/^\s*erDiagram/.test(e),"detector"),T2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./erDiagram-Q7BY3M3F-BCgjx10N.js");return{diagram:t}},__vite__mapDeps([22,20,11,6,7,21,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Bp,diagram:e}},"loader"),_2={id:Bp,detector:S2,loader:T2},B2=_2,Lp="gitGraph",L2=f(e=>/^\s*gitGraph/.test(e),"detector"),A2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./gitGraphDiagram-7IBYFJ6S-CugateeO.js");return{diagram:t}},__vite__mapDeps([23,24,25,26,8,7,9,10,6,11,12,2,4,5,13,14,15,16]),import.meta.url);return{id:Lp,diagram:e}},"loader"),M2={id:Lp,detector:L2,loader:A2},E2=M2,Ap="gantt",F2=f(e=>/^\s*gantt/.test(e),"detector"),D2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./ganttDiagram-APWFNJXF-CQe5BOum.js");return{diagram:t}},__vite__mapDeps([27,7,11,6,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Ap,diagram:e}},"loader"),O2={id:Ap,detector:F2,loader:D2},$2=O2,Mp="info",R2=f(e=>/^\s*info/.test(e),"detector"),I2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./infoDiagram-PH2N3AL5-799nGRxK.js");return{diagram:t}},__vite__mapDeps([28,26,8,7,9,10,6,11,12,2,4,5,13,14,15,16]),import.meta.url);return{id:Mp,diagram:e}},"loader"),P2={id:Mp,detector:R2,loader:I2},Ep="pie",N2=f(e=>/^\s*pie/.test(e),"detector"),z2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./pieDiagram-IB7DONF6-BdfZURv8.js");return{diagram:t}},__vite__mapDeps([29,24,26,8,7,9,10,6,11,12,2,4,5,13,14,15,16]),import.meta.url);return{id:Ep,diagram:e}},"loader"),W2={id:Ep,detector:N2,loader:z2},Fp="quadrantChart",q2=f(e=>/^\s*quadrantChart/.test(e),"detector"),j2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./quadrantDiagram-7GDLP6J5-DlPiuWL7.js");return{diagram:t}},__vite__mapDeps([30,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Fp,diagram:e}},"loader"),H2={id:Fp,detector:q2,loader:j2},U2=H2,Dp="xychart",G2=f(e=>/^\s*xychart-beta/.test(e),"detector"),Y2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./xychartDiagram-VJFVF3MP-Ddgq57v-.js");return{diagram:t}},__vite__mapDeps([31,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Dp,diagram:e}},"loader"),V2={id:Dp,detector:G2,loader:Y2},X2=V2,Op="requirement",Z2=f(e=>/^\s*requirement(Diagram)?/.test(e),"detector"),K2=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./requirementDiagram-KVF5MWMF-Ge3Zv2Cg.js");return{diagram:t}},__vite__mapDeps([32,20,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Op,diagram:e}},"loader"),Q2={id:Op,detector:Z2,loader:K2},J2=Q2,$p="sequence",tw=f(e=>/^\s*sequenceDiagram/.test(e),"detector"),ew=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./sequenceDiagram-X6HHIX6F-vfl950AQ.js");return{diagram:t}},__vite__mapDeps([33,18,25,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:$p,diagram:e}},"loader"),rw={id:$p,detector:tw,loader:ew},iw=rw,Rp="class",aw=f((e,t)=>{var r;return((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*classDiagram/.test(e)},"detector"),nw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./classDiagram-GIVACNV2-OWvnKXaf.js");return{diagram:t}},__vite__mapDeps([34,35,20,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Rp,diagram:e}},"loader"),sw={id:Rp,detector:aw,loader:nw},ow=sw,Ip="classDiagram",lw=f((e,t)=>{var r;return/^\s*classDiagram/.test(e)&&((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(e)},"detector"),cw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./classDiagram-v2-COTLJTTW-OWvnKXaf.js");return{diagram:t}},__vite__mapDeps([36,35,20,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Ip,diagram:e}},"loader"),hw={id:Ip,detector:lw,loader:cw},uw=hw,Pp="state",dw=f((e,t)=>{var r;return((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(e)},"detector"),pw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./stateDiagram-DGXRK772-JjgLOe5W.js");return{diagram:t}},__vite__mapDeps([37,38,20,11,6,7,1,2,3,4,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Pp,diagram:e}},"loader"),fw={id:Pp,detector:dw,loader:pw},gw=fw,Np="stateDiagram",mw=f((e,t)=>{var r;return!!(/^\s*stateDiagram-v2/.test(e)||/^\s*stateDiagram/.test(e)&&((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper")},"detector"),yw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./stateDiagram-v2-YXO3MK2T-q5rp7kkZ.js");return{diagram:t}},__vite__mapDeps([39,38,20,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Np,diagram:e}},"loader"),bw={id:Np,detector:mw,loader:yw},xw=bw,zp="journey",Cw=f(e=>/^\s*journey/.test(e),"detector"),ww=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./journeyDiagram-U35MCT3I-DPyCxeo8.js");return{diagram:t}},__vite__mapDeps([40,18,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:zp,diagram:e}},"loader"),kw={id:zp,detector:Cw,loader:ww},vw=kw,Sw=f((e,t,r)=>{P.debug(`rendering svg for syntax error
`);const i=O1(t),a=i.append("g");i.attr("viewBox","0 0 2412 512"),fc(i,100,512,!0),a.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),a.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),a.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),a.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),a.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),a.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),a.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),a.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)},"draw"),Wp={draw:Sw},Tw=Wp,_w={db:{},renderer:Wp,parser:{parse:f(()=>{},"parse")}},Bw=_w,qp="flowchart-elk",Lw=f((e,t={})=>{var r;return/^\s*flowchart-elk/.test(e)||/^\s*flowchart|graph/.test(e)&&((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="elk"?(t.layout="elk",!0):!1},"detector"),Aw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-CJF4d5Iv.js");return{diagram:t}},__vite__mapDeps([19,20,11,6,7,21,8,9,10,12,13,14,15,16]),import.meta.url);return{id:qp,diagram:e}},"loader"),Mw={id:qp,detector:Lw,loader:Aw},Ew=Mw,jp="timeline",Fw=f(e=>/^\s*timeline/.test(e),"detector"),Dw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./timeline-definition-BDJGKUSR-b8pClVqI.js");return{diagram:t}},__vite__mapDeps([41,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:jp,diagram:e}},"loader"),Ow={id:jp,detector:Fw,loader:Dw},$w=Ow,Hp="mindmap",Rw=f(e=>/^\s*mindmap/.test(e),"detector"),Iw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./mindmap-definition-ALO5MXBD-qC5qjWgw.js");return{diagram:t}},__vite__mapDeps([42,43,9,10,7,6,11,12,8,13,14,15,16]),import.meta.url);return{id:Hp,diagram:e}},"loader"),Pw={id:Hp,detector:Rw,loader:Iw},Nw=Pw,Up="kanban",zw=f(e=>/^\s*kanban/.test(e),"detector"),Ww=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./kanban-definition-NDS4AKOZ-DIMQ9e9X.js");return{diagram:t}},__vite__mapDeps([44,6,7,8,9,10,11,12,13,14,15,16]),import.meta.url);return{id:Up,diagram:e}},"loader"),qw={id:Up,detector:zw,loader:Ww},jw=qw,Gp="sankey",Hw=f(e=>/^\s*sankey-beta/.test(e),"detector"),Uw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./sankeyDiagram-QLVOVGJD-BswtmS1a.js");return{diagram:t}},__vite__mapDeps([45,11,6,7,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Gp,diagram:e}},"loader"),Gw={id:Gp,detector:Hw,loader:Uw},Yw=Gw,Yp="packet",Vw=f(e=>/^\s*packet-beta/.test(e),"detector"),Xw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./diagram-VNBRO52H-C86j_mW_.js");return{diagram:t}},__vite__mapDeps([46,24,26,8,7,9,10,6,11,12,2,4,5,13,14,15,16]),import.meta.url);return{id:Yp,diagram:e}},"loader"),Zw={id:Yp,detector:Vw,loader:Xw},Vp="radar",Kw=f(e=>/^\s*radar-beta/.test(e),"detector"),Qw=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./diagram-SSKATNLV-CjaIi2bO.js");return{diagram:t}},__vite__mapDeps([47,24,26,8,7,9,10,6,11,12,2,4,5,13,14,15,16]),import.meta.url);return{id:Vp,diagram:e}},"loader"),Jw={id:Vp,detector:Kw,loader:Qw},Xp="block",tk=f(e=>/^\s*block-beta/.test(e),"detector"),ek=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./blockDiagram-JOT3LUYC-RLlEH1Zn.js");return{diagram:t}},__vite__mapDeps([48,5,2,11,6,7,1,21,8,9,10,12,13,14,15,16]),import.meta.url);return{id:Xp,diagram:e}},"loader"),rk={id:Xp,detector:tk,loader:ek},ik=rk,Zp="architecture",ak=f(e=>/^\s*architecture/.test(e),"detector"),nk=f(async()=>{const{diagram:e}=await xt(async()=>{const{diagram:t}=await import("./architectureDiagram-IEHRJDOE-BVFoC4YC.js");return{diagram:t}},__vite__mapDeps([49,24,25,26,8,7,9,10,6,11,12,2,4,5,43,13,14,15,16]),import.meta.url);return{id:Zp,diagram:e}},"loader"),sk={id:Zp,detector:ak,loader:nk},ok=sk,Ll=!1,Za=f(()=>{Ll||(Ll=!0,oa("error",Bw,e=>e.toLowerCase().trim()==="error"),oa("---",{db:{clear:f(()=>{},"clear")},styles:{},renderer:{draw:f(()=>{},"draw")},parser:{parse:f(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:f(()=>null,"init")},e=>e.toLowerCase().trimStart().startsWith("---")),ec(g2,jw,uw,ow,B2,$2,P2,W2,J2,iw,Ew,v2,x2,Nw,$w,E2,xw,gw,vw,U2,Yw,Zw,X2,ik,ok,Jw))},"addDiagrams"),lk=f(async()=>{P.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(Er).map(async([r,{detector:i,loader:a}])=>{if(a)try{Mn(r)}catch{try{const{diagram:n,id:o}=await a();oa(o,n,i)}catch(n){throw P.error(`Failed to load external diagram with key ${r}. Removing from detectors.`),delete Er[r],n}}}))).filter(r=>r.status==="rejected");if(t.length>0){P.error(`Failed to load ${t.length} external diagrams`);for(const r of t)P.error(r);throw new Error(`Failed to load ${t.length} external diagrams`)}},"loadRegisteredDiagrams"),ck="graphics-document document";function Kp(e,t){e.attr("role",ck),t!==""&&e.attr("aria-roledescription",t)}f(Kp,"setA11yDiagramInfo");function Qp(e,t,r,i){if(e.insert!==void 0){if(r){const a=`chart-desc-${i}`;e.attr("aria-describedby",a),e.insert("desc",":first-child").attr("id",a).text(r)}if(t){const a=`chart-title-${i}`;e.attr("aria-labelledby",a),e.insert("title",":first-child").attr("id",a).text(t)}}}f(Qp,"addSVGa11yTitleDescription");var Je,hs=(Je=class{constructor(t,r,i,a,n){this.type=t,this.text=r,this.db=i,this.parser=a,this.renderer=n}static async fromText(t,r={}){var l,h;const i=re(),a=ps(t,i);t=Mx(t)+`
`;try{Mn(a)}catch{const u=ug(a);if(!u)throw new tc(`Diagram ${a} not found.`);const{id:d,diagram:p}=await u();oa(d,p)}const{db:n,parser:o,renderer:s,init:c}=Mn(a);return o.parser&&(o.parser.yy=n),(l=n.clear)==null||l.call(n),c==null||c(i),r.title&&((h=n.setDiagramTitle)==null||h.call(n,r.title)),await o.parse(t),new Je(a,t,n,o,s)}async render(t,r){await this.renderer.draw(this.text,t,r,this)}getParser(){return this.parser}getType(){return this.type}},f(Je,"Diagram"),Je),Al=[],hk=f(()=>{Al.forEach(e=>{e()}),Al=[]},"attachFunctions"),uk=f(e=>e.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function Jp(e){const t=e.match(Jl);if(!t)return{text:e,metadata:{}};let r=hy(t[1],{schema:cy})??{};r=typeof r=="object"&&!Array.isArray(r)?r:{};const i={};return r.displayMode&&(i.displayMode=r.displayMode.toString()),r.title&&(i.title=r.title.toString()),r.config&&(i.config=r.config),{text:e.slice(t[0].length),metadata:i}}f(Jp,"extractFrontMatter");var dk=f(e=>e.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(t,r,i)=>"<"+r+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),pk=f(e=>{const{text:t,metadata:r}=Jp(e),{displayMode:i,title:a,config:n={}}=r;return i&&(n.gantt||(n.gantt={}),n.gantt.displayMode=i),{title:a,config:n,text:t}},"processFrontmatter"),fk=f(e=>{const t=ye.detectInit(e)??{},r=ye.detectDirective(e,"wrap");return Array.isArray(r)?t.wrap=r.some(({type:i})=>i==="wrap"):(r==null?void 0:r.type)==="wrap"&&(t.wrap=!0),{text:yx(e),directive:t}},"processDirectives");function eo(e){const t=dk(e),r=pk(t),i=fk(r.text),a=Ps(r.config,i.directive);return e=uk(i.text),{code:e,title:r.title,config:a}}f(eo,"preprocessDiagram");function tf(e){const t=new TextEncoder().encode(e),r=Array.from(t,i=>String.fromCodePoint(i)).join("");return btoa(r)}f(tf,"toBase64");var gk=5e4,mk="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",yk="sandbox",bk="loose",xk="http://www.w3.org/2000/svg",Ck="http://www.w3.org/1999/xlink",wk="http://www.w3.org/1999/xhtml",kk="100%",vk="100%",Sk="border:0;margin:0;",Tk="margin:0",_k="allow-top-navigation-by-user-activation allow-popups",Bk='The "iframe" tag is not supported by your browser.',Lk=["foreignobject"],Ak=["dominant-baseline"];function ro(e){const t=eo(e);return na(),Bg(t.config??{}),t}f(ro,"processAndSetConfigs");async function ef(e,t){Za();try{const{code:r,config:i}=ro(e);return{diagramType:(await af(r)).type,config:i}}catch(r){if(t!=null&&t.suppressErrors)return!1;throw r}}f(ef,"parse");var Ml=f((e,t,r=[])=>`
.${e} ${t} { ${r.join(" !important; ")} !important; }`,"cssImportantStyles"),Mk=f((e,t=new Map)=>{var i;let r="";if(e.themeCSS!==void 0&&(r+=`
${e.themeCSS}`),e.fontFamily!==void 0&&(r+=`
:root { --mermaid-font-family: ${e.fontFamily}}`),e.altFontFamily!==void 0&&(r+=`
:root { --mermaid-alt-font-family: ${e.altFontFamily}}`),t instanceof Map){const s=e.htmlLabels??((i=e.flowchart)==null?void 0:i.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];t.forEach(c=>{Bl(c.styles)||s.forEach(l=>{r+=Ml(c.id,l,c.styles)}),Bl(c.textStyles)||(r+=Ml(c.id,"tspan",((c==null?void 0:c.textStyles)||[]).map(l=>l.replace("color","fill"))))})}return r},"createCssStyles"),Ek=f((e,t,r,i)=>{const a=Mk(e,r),n=Gg(t,a,e.themeVariables);return ns(X1(`${i}{${n}}`),K1)},"createUserStyles"),Fk=f((e="",t,r)=>{let i=e;return!r&&!t&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=lr(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),Dk=f((e="",t)=>{var a,n;const r=(n=(a=t==null?void 0:t.viewBox)==null?void 0:a.baseVal)!=null&&n.height?t.viewBox.baseVal.height+"px":vk,i=tf(`<body style="${Tk}">${e}</body>`);return`<iframe style="width:${kk};height:${r};${Sk}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${_k}">
  ${Bk}
</iframe>`},"putIntoIFrame"),El=f((e,t,r,i,a)=>{const n=e.append("div");n.attr("id",r),i&&n.attr("style",i);const o=n.append("svg").attr("id",t).attr("width","100%").attr("xmlns",xk);return a&&o.attr("xmlns:xlink",a),o.append("g"),e},"appendDivSvgG");function us(e,t){return e.append("iframe").attr("id",t).attr("style","width: 100%; height: 100%;").attr("sandbox","")}f(us,"sandboxedIframe");var Ok=f((e,t,r,i)=>{var a,n,o;(a=e.getElementById(t))==null||a.remove(),(n=e.getElementById(r))==null||n.remove(),(o=e.getElementById(i))==null||o.remove()},"removeExistingElements"),$k=f(async function(e,t,r){var $,R,M,O,F,I;Za();const i=ro(t);t=i.code;const a=re();P.debug(a),t.length>((a==null?void 0:a.maxTextSize)??gk)&&(t=mk);const n="#"+e,o="i"+e,s="#"+o,c="d"+e,l="#"+c,h=f(()=>{const W=pt(d?s:l).node();W&&"remove"in W&&W.remove()},"removeTempElements");let u=pt("body");const d=a.securityLevel===yk,p=a.securityLevel===bk,g=a.fontFamily;if(r!==void 0){if(r&&(r.innerHTML=""),d){const D=us(pt(r),o);u=pt(D.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=pt(r);El(u,e,c,`font-family: ${g}`,Ck)}else{if(Ok(document,e,c,o),d){const D=us(pt("body"),o);u=pt(D.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=pt("body");El(u,e,c)}let m,y;try{m=await hs.fromText(t,{title:i.title})}catch(D){if(a.suppressErrorRendering)throw h(),D;m=await hs.fromText("error"),y=D}const b=u.select(l).node(),x=m.type,k=b.firstChild,L=k.firstChild,B=(R=($=m.renderer).getClasses)==null?void 0:R.call($,t,m),C=Ek(a,x,B,n),w=document.createElement("style");w.innerHTML=C,k.insertBefore(w,L);try{await m.renderer.draw(t,e,xl.version,m)}catch(D){throw a.suppressErrorRendering?h():Tw.draw(t,e,xl.version),D}const v=u.select(`${l} svg`),E=(O=(M=m.db).getAccTitle)==null?void 0:O.call(M),_=(I=(F=m.db).getAccDescription)==null?void 0:I.call(F);nf(x,v,E,_),u.select(`[id="${e}"]`).selectAll("foreignobject > *").attr("xmlns",wk);let S=u.select(l).node().innerHTML;if(P.debug("config.arrowMarkerAbsolute",a.arrowMarkerAbsolute),S=Fk(S,d,Mt(a.arrowMarkerAbsolute)),d){const D=u.select(l+" svg").node();S=Dk(S,D)}else p||(S=Mr.sanitize(S,{ADD_TAGS:Lk,ADD_ATTR:Ak,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(hk(),y)throw y;return h(),{diagramType:x,svg:S,bindFunctions:m.db.bindFunctions}},"render");function rf(e={}){var i;const t=Rt({},e);t!=null&&t.fontFamily&&!((i=t.themeVariables)!=null&&i.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),Tg(t),t!=null&&t.theme&&t.theme in Fe?t.themeVariables=Fe[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Fe.default.getThemeVariables(t.themeVariables));const r=typeof t=="object"?Sg(t):oc();ds(r.logLevel),Za()}f(rf,"initialize");var af=f((e,t={})=>{const{code:r}=eo(e);return hs.fromText(r,t)},"getDiagramFromText");function nf(e,t,r,i){Kp(t,e),Qp(t,r,i,t.attr("id"))}f(nf,"addA11yInfo");var ar=Object.freeze({render:$k,parse:ef,getDiagramFromText:af,initialize:rf,getConfig:re,setConfig:lc,getSiteConfig:oc,updateSiteConfig:_g,reset:f(()=>{na()},"reset"),globalReset:f(()=>{na(Fr)},"globalReset"),defaultConfig:Fr});ds(re().logLevel);na(re());var Rk=f((e,t,r)=>{P.warn(e),Is(e)?(r&&r(e.str,e.hash),t.push({...e,message:e.str,error:e})):(r&&r(e),e instanceof Error&&t.push({str:e.message,message:e.message,hash:e.name,error:e}))},"handleError"),sf=f(async function(e={querySelector:".mermaid"}){try{await Ik(e)}catch(t){if(Is(t)&&P.error(t.str),ae.parseError&&ae.parseError(t),!e.suppressErrors)throw P.error("Use the suppressErrors option to suppress these errors"),t}},"run"),Ik=f(async function({postRenderCallback:e,querySelector:t,nodes:r}={querySelector:".mermaid"}){const i=ar.getConfig();P.debug(`${e?"":"No "}Callback function found`);let a;if(r)a=r;else if(t)a=document.querySelectorAll(t);else throw new Error("Nodes and querySelector are both undefined");P.debug(`Found ${a.length} diagrams`),(i==null?void 0:i.startOnLoad)!==void 0&&(P.debug("Start On Load: "+(i==null?void 0:i.startOnLoad)),ar.updateSiteConfig({startOnLoad:i==null?void 0:i.startOnLoad}));const n=new ye.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const c of Array.from(a)){if(P.info("Rendering diagram: "+c.id),c.getAttribute("data-processed"))continue;c.setAttribute("data-processed","true");const l=`mermaid-${n.next()}`;o=c.innerHTML,o=Du(ye.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const h=ye.detectInit(o);h&&P.debug("Detected early reinit: ",h);try{const{svg:u,bindFunctions:d}=await hf(l,o,c);c.innerHTML=u,e&&await e(l),d&&d(c)}catch(u){Rk(u,s,ae.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),of=f(function(e){ar.initialize(e)},"initialize"),Pk=f(async function(e,t,r){P.warn("mermaid.init is deprecated. Please use run instead."),e&&of(e);const i={postRenderCallback:r,querySelector:".mermaid"};typeof t=="string"?i.querySelector=t:t&&(t instanceof HTMLElement?i.nodes=[t]:i.nodes=t),await sf(i)},"init"),Nk=f(async(e,{lazyLoad:t=!0}={})=>{Za(),ec(...e),t===!1&&await lk()},"registerExternalDiagrams"),lf=f(function(){if(ae.startOnLoad){const{startOnLoad:e}=ar.getConfig();e&&ae.run().catch(t=>P.error("Mermaid failed to initialize",t))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",lf,!1);var zk=f(function(e){ae.parseError=e},"setParseErrorHandler"),La=[],kn=!1,cf=f(async()=>{if(!kn){for(kn=!0;La.length>0;){const e=La.shift();if(e)try{await e()}catch(t){P.error("Error executing queue",t)}}kn=!1}},"executeQueue"),Wk=f(async(e,t)=>new Promise((r,i)=>{const a=f(()=>new Promise((n,o)=>{ar.parse(e,t).then(s=>{n(s),r(s)},s=>{var c;P.error("Error parsing",s),(c=ae.parseError)==null||c.call(ae,s),o(s),i(s)})}),"performCall");La.push(a),cf().catch(i)}),"parse"),hf=f((e,t,r)=>new Promise((i,a)=>{const n=f(()=>new Promise((o,s)=>{ar.render(e,t,r).then(c=>{o(c),i(c)},c=>{var l;P.error("Error parsing",c),(l=ae.parseError)==null||l.call(ae,c),s(c),a(c)})}),"performCall");La.push(n),cf().catch(a)}),"render"),ae={startOnLoad:!0,mermaidAPI:ar,parse:Wk,render:hf,init:Pk,run:sf,registerExternalDiagrams:Nk,registerLayoutLoaders:yp,initialize:of,parseError:void 0,contentLoaded:lf,setParseErrorHandler:zk,detectType:ps,registerIconPacks:Dy},Fl=ae;/*! Check if previously processed *//*!
 * Wait for document loaded before starting the execution
 */var uf={exports:{}};(function(e){(function(t){var r=k(),i=L(),a=B(),n=C(),o={imagePlaceholder:void 0,cacheBust:!1},s={toSvg:c,toPng:h,toJpeg:u,toBlob:d,toPixelData:l,impl:{fontFaces:a,images:n,util:r,inliner:i,options:{}}};e.exports=s;function c(w,v){return v=v||{},p(v),Promise.resolve(w).then(function(_){return m(_,v.filter,!0)}).then(y).then(b).then(E).then(function(_){return x(_,v.width||r.width(w),v.height||r.height(w))});function E(_){return v.bgcolor&&(_.style.backgroundColor=v.bgcolor),v.width&&(_.style.width=v.width+"px"),v.height&&(_.style.height=v.height+"px"),v.style&&Object.keys(v.style).forEach(function(S){_.style[S]=v.style[S]}),_}}function l(w,v){return g(w,v||{}).then(function(E){return E.getContext("2d").getImageData(0,0,r.width(w),r.height(w)).data})}function h(w,v){return g(w,v||{}).then(function(E){return E.toDataURL()})}function u(w,v){return v=v||{},g(w,v).then(function(E){return E.toDataURL("image/jpeg",v.quality||1)})}function d(w,v){return g(w,v||{}).then(r.canvasToBlob)}function p(w){typeof w.imagePlaceholder>"u"?s.impl.options.imagePlaceholder=o.imagePlaceholder:s.impl.options.imagePlaceholder=w.imagePlaceholder,typeof w.cacheBust>"u"?s.impl.options.cacheBust=o.cacheBust:s.impl.options.cacheBust=w.cacheBust}function g(w,v){return c(w,v).then(r.makeImage).then(r.delay(100)).then(function(_){var S=E(w);return S.getContext("2d").drawImage(_,0,0),S});function E(_){var S=document.createElement("canvas");if(S.width=v.width||r.width(_),S.height=v.height||r.height(_),v.bgcolor){var $=S.getContext("2d");$.fillStyle=v.bgcolor,$.fillRect(0,0,S.width,S.height)}return S}}function m(w,v,E){if(!E&&v&&!v(w))return Promise.resolve();return Promise.resolve(w).then(_).then(function(R){return S(w,R,v)}).then(function(R){return $(w,R)});function _(R){return R instanceof HTMLCanvasElement?r.makeImage(R.toDataURL()):R.cloneNode(!1)}function S(R,M,O){var F=R.childNodes;if(F.length===0)return Promise.resolve(M);return I(M,r.asArray(F),O).then(function(){return M});function I(D,W,j){var U=Promise.resolve();return W.forEach(function(Z){U=U.then(function(){return m(Z,j)}).then(function(V){V&&D.appendChild(V)})}),U}}function $(R,M){if(!(M instanceof Element))return M;return Promise.resolve().then(O).then(F).then(I).then(D).then(function(){return M});function O(){W(window.getComputedStyle(R),M.style);function W(j,U){j.cssText?U.cssText=j.cssText:Z(j,U);function Z(V,ht){r.asArray(V).forEach(function(N){ht.setProperty(N,V.getPropertyValue(N),V.getPropertyPriority(N))})}}}function F(){[":before",":after"].forEach(function(j){W(j)});function W(j){var U=window.getComputedStyle(R,j),Z=U.getPropertyValue("content");if(Z===""||Z==="none")return;var V=r.uid();M.className=M.className+" "+V;var ht=document.createElement("style");ht.appendChild(N(V,j,U)),M.appendChild(ht);function N(at,ct,ut){var Ct="."+at+":"+ct,gt=ut.cssText?Ht(ut):It(ut);return document.createTextNode(Ct+"{"+gt+"}");function Ht(Bt){var Dt=Bt.getPropertyValue("content");return Bt.cssText+" content: "+Dt+";"}function It(Bt){return r.asArray(Bt).map(Dt).join("; ")+";";function Dt(we){return we+": "+Bt.getPropertyValue(we)+(Bt.getPropertyPriority(we)?" !important":"")}}}}}function I(){R instanceof HTMLTextAreaElement&&(M.innerHTML=R.value),R instanceof HTMLInputElement&&M.setAttribute("value",R.value)}function D(){M instanceof SVGElement&&(M.setAttribute("xmlns","http://www.w3.org/2000/svg"),M instanceof SVGRectElement&&["width","height"].forEach(function(W){var j=M.getAttribute(W);j&&M.style.setProperty(W,j)}))}}}function y(w){return a.resolveAll().then(function(v){var E=document.createElement("style");return w.appendChild(E),E.appendChild(document.createTextNode(v)),w})}function b(w){return n.inlineAll(w).then(function(){return w})}function x(w,v,E){return Promise.resolve(w).then(function(_){return _.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),new XMLSerializer().serializeToString(_)}).then(r.escapeXhtml).then(function(_){return'<foreignObject x="0" y="0" width="100%" height="100%">'+_+"</foreignObject>"}).then(function(_){return'<svg xmlns="http://www.w3.org/2000/svg" width="'+v+'" height="'+E+'">'+_+"</svg>"}).then(function(_){return"data:image/svg+xml;charset=utf-8,"+_})}function k(){return{escape:D,parseExtension:v,mimeType:E,dataAsUrl:I,isDataUrl:_,canvasToBlob:$,resolveUrl:R,getAndEncode:F,uid:M(),delay:W,asArray:j,escapeXhtml:U,makeImage:O,width:Z,height:V};function w(){var N="application/font-woff",at="image/jpeg";return{woff:N,woff2:N,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:at,jpeg:at,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml"}}function v(N){var at=/\.([^\.\/]*?)$/g.exec(N);return at?at[1]:""}function E(N){var at=v(N).toLowerCase();return w()[at]||""}function _(N){return N.search(/^(data:)/)!==-1}function S(N){return new Promise(function(at){for(var ct=window.atob(N.toDataURL().split(",")[1]),ut=ct.length,Ct=new Uint8Array(ut),gt=0;gt<ut;gt++)Ct[gt]=ct.charCodeAt(gt);at(new Blob([Ct],{type:"image/png"}))})}function $(N){return N.toBlob?new Promise(function(at){N.toBlob(at)}):S(N)}function R(N,at){var ct=document.implementation.createHTMLDocument(),ut=ct.createElement("base");ct.head.appendChild(ut);var Ct=ct.createElement("a");return ct.body.appendChild(Ct),ut.href=at,Ct.href=N,Ct.href}function M(){var N=0;return function(){return"u"+at()+N++;function at(){return("0000"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)}}}function O(N){return new Promise(function(at,ct){var ut=new Image;ut.onload=function(){at(ut)},ut.onerror=ct,ut.src=N})}function F(N){var at=3e4;return s.impl.options.cacheBust&&(N+=(/\?/.test(N)?"&":"?")+new Date().getTime()),new Promise(function(ct){var ut=new XMLHttpRequest;ut.onreadystatechange=Ht,ut.ontimeout=It,ut.responseType="blob",ut.timeout=at,ut.open("GET",N,!0),ut.send();var Ct;if(s.impl.options.imagePlaceholder){var gt=s.impl.options.imagePlaceholder.split(/,/);gt&&gt[1]&&(Ct=gt[1])}function Ht(){if(ut.readyState===4){if(ut.status!==200){Ct?ct(Ct):Bt("cannot fetch resource: "+N+", status: "+ut.status);return}var Dt=new FileReader;Dt.onloadend=function(){var we=Dt.result.split(/,/)[1];ct(we)},Dt.readAsDataURL(ut.response)}}function It(){Ct?ct(Ct):Bt("timeout of "+at+"ms occured while fetching resource: "+N)}function Bt(Dt){console.error(Dt),ct("")}})}function I(N,at){return"data:"+at+";base64,"+N}function D(N){return N.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1")}function W(N){return function(at){return new Promise(function(ct){setTimeout(function(){ct(at)},N)})}}function j(N){for(var at=[],ct=N.length,ut=0;ut<ct;ut++)at.push(N[ut]);return at}function U(N){return N.replace(/#/g,"%23").replace(/\n/g,"%0A")}function Z(N){var at=ht(N,"border-left-width"),ct=ht(N,"border-right-width");return N.scrollWidth+at+ct}function V(N){var at=ht(N,"border-top-width"),ct=ht(N,"border-bottom-width");return N.scrollHeight+at+ct}function ht(N,at){var ct=window.getComputedStyle(N).getPropertyValue(at);return parseFloat(ct.replace("px",""))}}function L(){var w=/url\(['"]?([^'"]+?)['"]?\)/g;return{inlineAll:S,shouldProcess:v,impl:{readUrls:E,inline:_}};function v($){return $.search(w)!==-1}function E($){for(var R=[],M;(M=w.exec($))!==null;)R.push(M[1]);return R.filter(function(O){return!r.isDataUrl(O)})}function _($,R,M,O){return Promise.resolve(R).then(function(I){return M?r.resolveUrl(I,M):I}).then(O||r.getAndEncode).then(function(I){return r.dataAsUrl(I,r.mimeType(R))}).then(function(I){return $.replace(F(R),"$1"+I+"$3")});function F(I){return new RegExp(`(url\\(['"]?)(`+r.escape(I)+`)(['"]?\\))`,"g")}}function S($,R,M){if(O())return Promise.resolve($);return Promise.resolve($).then(E).then(function(F){var I=Promise.resolve($);return F.forEach(function(D){I=I.then(function(W){return _(W,D,R,M)})}),I});function O(){return!v($)}}}function B(){return{resolveAll:w,impl:{readAll:v}};function w(){return v().then(function(E){return Promise.all(E.map(function(_){return _.resolve()}))}).then(function(E){return E.join(`
`)})}function v(){return Promise.resolve(r.asArray(document.styleSheets)).then(_).then(E).then(function($){return $.map(S)});function E($){return $.filter(function(R){return R.type===CSSRule.FONT_FACE_RULE}).filter(function(R){return i.shouldProcess(R.style.getPropertyValue("src"))})}function _($){var R=[];return $.forEach(function(M){try{r.asArray(M.cssRules||[]).forEach(R.push.bind(R))}catch(O){console.log("Error while reading CSS rules from "+M.href,O.toString())}}),R}function S($){return{resolve:function(){var M=($.parentStyleSheet||{}).href;return i.inlineAll($.cssText,M)},src:function(){return $.style.getPropertyValue("src")}}}}}function C(){return{inlineAll:v,impl:{newImage:w}};function w(E){return{inline:_};function _(S){return r.isDataUrl(E.src)?Promise.resolve():Promise.resolve(E.src).then(S||r.getAndEncode).then(function($){return r.dataAsUrl($,r.mimeType(E.src))}).then(function($){return new Promise(function(R,M){E.onload=R,E.onerror=M,E.src=$})})}}function v(E){if(!(E instanceof Element))return Promise.resolve(E);return _(E).then(function(){return E instanceof HTMLImageElement?w(E).inline():Promise.all(r.asArray(E.childNodes).map(function(S){return v(S)}))});function _(S){var $=S.style.getPropertyValue("background");return $?i.inlineAll($).then(function(R){S.style.setProperty("background",R,S.style.getPropertyPriority("background"))}).then(function(){return S}):Promise.resolve(S)}}}})()})(uf);var qk=uf.exports;const jk=Dl(qk),Hk=()=>{const e=pe.useRef(null),t=pe.useRef(0),[r,i]=pe.useState([{id:"1",label:"Assessed for Eligibility",count:100,color:"#4CAF50",flowType:"straight"},{id:"2",label:"Excluded",count:10,color:"#f44336",flowType:"branch-right"},{id:"3",label:"Randomized",count:90,color:"#2196F3",flowType:"straight"},{id:"4",label:"Lost to Follow-up",count:5,color:"#ff9800",flowType:"branch-right"},{id:"5",label:"Completed Study",count:85,color:"#4CAF50",flowType:"end"}]),[a,n]=pe.useState(""),[o,s]=pe.useState("TD"),[c,l]=pe.useState(!0),[h,u]=pe.useState("default"),d=[{name:"Clinical Trial",stages:[{label:"Screened for Eligibility",count:250,color:"#4CAF50",flowType:"straight"},{label:"Excluded (Not Meeting Criteria)",count:50,color:"#f44336",flowType:"branch-right"},{label:"Eligible Participants",count:200,color:"#2196F3",flowType:"straight"},{label:"Declined to Participate",count:20,color:"#ff9800",flowType:"branch-right"},{label:"Randomized",count:180,color:"#2196F3",flowType:"straight"},{label:"Allocated to Intervention",count:90,color:"#9C27B0",flowType:"straight"},{label:"Allocated to Control",count:90,color:"#9C27B0",flowType:"branch-right",connectTo:"5"},{label:"Lost to Follow-up (Intervention)",count:5,color:"#ff9800",flowType:"branch-right"},{label:"Analyzed (Intervention)",count:85,color:"#4CAF50",flowType:"straight"},{label:"Lost to Follow-up (Control)",count:7,color:"#ff9800",flowType:"branch-right",connectTo:"6"},{label:"Analyzed (Control)",count:83,color:"#4CAF50",flowType:"end"}]},{name:"PRISMA Flow Diagram",stages:[{label:"Records identified through database searching",count:1250,color:"#9C27B0",flowType:"merge"},{label:"Additional records identified through other sources",count:75,color:"#9C27B0",flowType:"merge"},{label:"Records after duplicates removed",count:980,color:"#2196F3",flowType:"straight"},{label:"Records screened",count:980,color:"#2196F3",flowType:"straight"},{label:"Records excluded",count:850,color:"#f44336",flowType:"branch-right"},{label:"Full-text articles assessed for eligibility",count:130,color:"#2196F3",flowType:"straight"},{label:"Full-text articles excluded, with reasons",count:85,color:"#f44336",flowType:"branch-right"},{label:"Studies included in qualitative synthesis",count:45,color:"#4CAF50",flowType:"straight"},{label:"Studies included in quantitative synthesis (meta-analysis)",count:38,color:"#4CAF50",flowType:"end"}]},{name:"Survey Based Studies",stages:[{label:"Survey Invitations Sent",count:5e3,color:"#FF5722",flowType:"straight"},{label:"No Response",count:3e3,color:"#f44336",flowType:"branch-right"},{label:"Total Responses Received",count:2e3,color:"#FF9800",flowType:"straight"},{label:"Incomplete Responses",count:450,color:"#f44336",flowType:"branch-right"},{label:"Complete Responses",count:1550,color:"#FFC107",flowType:"straight"},{label:"Excluded (Failed Quality Checks)",count:150,color:"#f44336",flowType:"branch-right"},{label:"Valid Responses",count:1400,color:"#8BC34A",flowType:"straight"},{label:"Excluded (Outside Target Population)",count:200,color:"#f44336",flowType:"branch-right"},{label:"Final Sample Size",count:1200,color:"#4CAF50",flowType:"end"}]},{name:"Cohort Study",stages:[{label:"Initial Population: Total subjects identified",count:1e3,color:"#4CAF50",flowType:"straight"},{label:"Eligibility Screening: Subjects meeting inclusion criteria",count:900,color:"#2196F3",flowType:"straight"},{label:"Subjects excluded (with reasons)",count:100,color:"#f44336",flowType:"branch-right"},{label:"Cohort Grouping: Exposed group",count:450,color:"#9C27B0",flowType:"straight"},{label:"Non-exposed group",count:450,color:"#9C27B0",flowType:"branch-right",connectTo:"3"},{label:"Follow-up: Subjects lost to follow-up (with reasons)",count:50,color:"#ff9800",flowType:"branch-right"},{label:"Subjects continuing in the study",count:850,color:"#FFC107",flowType:"straight"},{label:"Outcome Assessment: Subjects with the outcome",count:200,color:"#8BC34A",flowType:"straight"},{label:"Subjects without the outcome",count:650,color:"#8BC34A",flowType:"branch-right",connectTo:"7"},{label:"Analysis: Statistical analysis performed",count:850,color:"#4CAF50",flowType:"straight"},{label:"Adjustments for confounders",count:850,color:"#4CAF50",flowType:"end"}]}],p=()=>{const v={id:Date.now().toString(),label:"",count:0,color:"#2196F3",flowType:"straight"};i([...r,v])},g=(v,E,_)=>{const S=r.map($=>$.id===v?{...$,[E]:_}:$);i(S)},m=v=>{r.length>1&&i(r.filter(E=>E.id!==v))},y=v=>{if(v>0){const E=[...r];[E[v-1],E[v]]=[E[v],E[v-1]],i(E)}},b=v=>{if(v<r.length-1){const E=[...r];[E[v],E[v+1]]=[E[v+1],E[v]],i(E)}},x=v=>{const E=v.stages.map((_,S)=>({..._,id:Date.now().toString()+S}));i(E)},k=()=>{i([{id:Date.now().toString(),label:"",count:0,color:"#2196F3",flowType:"straight"}]),n("")},L=(v,E=25)=>{const _=v.split(" "),S=[];let $="";return _.forEach(R=>{($+" "+R).trim().length<=E?$=($+" "+R).trim():($&&S.push($),$=R)}),$&&S.push($),S.join("<br/>")},B=()=>{let v=`graph ${o}
`,E=0,_={};r.forEach((S,$)=>{var O;const R=L(S.label),M=S.color?`style id${$} fill:${S.color},stroke:#333,stroke-width:2px,color:#fff`:"";if(v+=`    id${$}["${R}<br/><b>n=${S.count}</b>"]
`,M&&(v+=`    ${M}
`),$>0){const F=r[$-1];S.flowType==="merge"&&$===1?_[S.id]=$:F.flowType==="merge"&&((O=r[$-2])==null?void 0:O.flowType)==="merge"?(v+=`    id${$-2} --> id${$}
`,v+=`    id${$-1} --> id${$}
`,E=$):F.flowType==="branch-right"?(E>0&&r[E]&&(v+=`    id${E} --> id${$}
`),E=$):S.flowType==="branch-right"?v+=`    id${$-1} --> id${$}
`:(v+=`    id${$-1} --> id${$}
`,F.flowType&&F.flowType!=="end"&&(E=$))}else S.flowType!=="merge"&&(E=$)}),n(v)},C=()=>{navigator.clipboard.writeText(a),alert("Mermaid syntax copied to clipboard!")},w=v=>{if(e.current){if(v==="png")jk.toPng(e.current).then(E=>{const _=document.createElement("a");_.download="flow-diagram.png",_.href=E,_.click()}).catch(E=>{console.error("Export failed:",E),alert("Failed to export diagram")});else if(v==="svg"){const E=e.current.querySelector("svg");if(E){const _=new XMLSerializer().serializeToString(E),S=new Blob([_],{type:"image/svg+xml;charset=utf-8"}),$=URL.createObjectURL(S),R=document.createElement("a");R.download="flow-diagram.svg",R.href=$,R.click()}}}};return pe.useEffect(()=>{Fl.initialize({startOnLoad:!1,theme:h,flowchart:{useMaxWidth:!0,htmlLabels:!0,curve:"basis",nodeSpacing:50,rankSpacing:50,padding:20}})},[h]),pe.useEffect(()=>{c&&r.every(v=>v.label)&&B()},[r,o,c]),pe.useEffect(()=>{(async()=>{var E;if(a&&e.current)try{e.current.innerHTML="";const _=`mermaid-diagram-${t.current++}`,{svg:S}=await Fl.render(_,a);e.current&&(e.current.innerHTML=S,(E=e.current.parentElement)==null||E.scrollTo(0,0))}catch(_){if(console.error("Error rendering diagram:",_),e.current){const S=_ instanceof Error?_.message:String(_);e.current.innerHTML=`<p class="error-message">Error rendering diagram: ${S}</p>`}}})()},[a]),H.jsx(Ff,{children:H.jsx("div",{className:"flow-diagram-container",children:H.jsxs("div",{className:"main-layout",children:[H.jsxs("div",{className:"left-panel",children:[H.jsxs("div",{className:"templates-section",children:[H.jsx("h3",{children:"📋 Research Templates"}),H.jsxs("div",{className:"template-grid",children:[H.jsx("button",{onClick:()=>x(d[0]),className:"template-btn",children:"Clinical Trial"}),H.jsx("button",{onClick:()=>x(d[1]),className:"template-btn",children:"PRISMA Flow Diagram"}),H.jsx("button",{onClick:()=>x(d[2]),className:"template-btn",children:"Survey Based Studies"}),H.jsx("button",{onClick:()=>x(d[3]),className:"template-btn",children:"Cohort Study"}),H.jsx("button",{onClick:k,className:"template-btn clear-btn",children:"Clear All"})]})]}),H.jsxs("div",{className:"settings-section",children:[H.jsx("h3",{children:"⚙️ Diagram Settings"}),H.jsxs("div",{className:"settings-row",children:[H.jsxs("label",{children:["Direction:",H.jsxs("select",{value:o,onChange:v=>s(v.target.value),children:[H.jsx("option",{value:"TD",children:"Top to Bottom"}),H.jsx("option",{value:"LR",children:"Left to Right"})]})]}),H.jsxs("label",{children:["Theme:",H.jsxs("select",{value:h,onChange:v=>u(v.target.value),children:[H.jsx("option",{value:"default",children:"Default"}),H.jsx("option",{value:"dark",children:"Dark"}),H.jsx("option",{value:"forest",children:"Forest"}),H.jsx("option",{value:"neutral",children:"Neutral"})]})]})]}),H.jsxs("label",{className:"checkbox-label",children:[H.jsx("input",{type:"checkbox",checked:c,onChange:v=>l(v.target.checked)}),"Auto-generate on change"]})]}),H.jsxs("div",{className:"stages-section",children:[H.jsxs("div",{className:"stages-header",children:[H.jsx("h3",{children:"📊 Study Stages"}),H.jsx("button",{onClick:p,className:"add-btn",children:"+ Add"})]}),H.jsx("div",{className:"stages-list",children:r.map((v,E)=>H.jsxs("div",{className:"stage-item",children:[H.jsx("span",{className:"stage-number",children:E+1}),H.jsxs("div",{className:"stage-controls",children:[H.jsx("button",{onClick:()=>y(E),disabled:E===0,className:"arrow-btn",title:"Move up",children:"↑"}),H.jsx("button",{onClick:()=>b(E),disabled:E===r.length-1,className:"arrow-btn",title:"Move down",children:"↓"})]}),H.jsxs("div",{className:"stage-content",children:[H.jsx("input",{type:"text",placeholder:"Stage Label",value:v.label,onChange:_=>g(v.id,"label",_.target.value),className:"stage-label-input"}),H.jsxs("div",{className:"stage-row",children:[H.jsx("input",{type:"number",placeholder:"n",value:v.count,onChange:_=>g(v.id,"count",parseInt(_.target.value,10)||0),className:"stage-count-input"}),H.jsxs("select",{value:v.flowType||"straight",onChange:_=>g(v.id,"flowType",_.target.value),className:"flow-type-select",title:"Flow direction",children:[H.jsx("option",{value:"straight",children:"↓ Straight"}),H.jsx("option",{value:"branch-right",children:"→ Branch Right"}),H.jsx("option",{value:"merge",children:"⤵ Merge"}),H.jsx("option",{value:"end",children:"⬤ End"})]}),H.jsx("input",{type:"color",value:v.color||"#2196F3",onChange:_=>g(v.id,"color",_.target.value),className:"color-picker"}),H.jsx("button",{onClick:()=>m(v.id),className:"delete-btn",disabled:r.length===1,title:"Delete",children:"×"})]})]})]},v.id))}),H.jsxs("div",{className:"flow-tips",children:[H.jsx("p",{children:H.jsx("strong",{children:"Flow Types:"})}),H.jsxs("ul",{children:[H.jsxs("li",{children:[H.jsx("strong",{children:"Straight:"})," Continue down"]}),H.jsxs("li",{children:[H.jsx("strong",{children:"Branch Right:"})," Side branch (exclusions)"]}),H.jsxs("li",{children:[H.jsx("strong",{children:"Merge:"})," Multiple sources joining"]})]})]})]}),!c&&H.jsx("button",{onClick:B,className:"generate-btn",children:"Generate Diagram"})]}),H.jsxs("div",{className:"right-panel",children:[H.jsxs("div",{className:"preview-header",children:[H.jsx("h3",{children:"📈 Diagram Preview"}),H.jsxs("div",{className:"export-buttons",children:[H.jsx("button",{onClick:()=>w("png"),disabled:!a,className:"export-btn",children:"📷 PNG"}),H.jsx("button",{onClick:()=>w("svg"),disabled:!a,className:"export-btn",children:"📐 SVG"}),H.jsx("button",{onClick:C,disabled:!a,className:"export-btn",children:"📋 Copy Syntax"})]})]}),H.jsx("div",{className:"diagram-area",children:H.jsx("div",{ref:e,className:"diagram-container",children:!a&&H.jsx("div",{className:"empty-state",children:H.jsx("p",{children:"Select a template or configure stages to begin"})})})})]})]})})})},cv=Object.freeze(Object.defineProperty({__proto__:null,default:Hk},Symbol.toStringTag,{value:"Module"}));export{gc as $,hi as A,cg as B,be as C,em as D,Ps as E,Hk as F,re as G,nc as H,kx as I,cy as J,O1 as K,xl as L,Wa as M,yg as N,pC as O,Or as P,Qk as Q,fs as R,Do as S,wx as T,ot as U,jg as V,vi as W,G as X,it as Y,px as Z,f as _,Xg as a,Fs as a$,zC as a0,gp as a1,nv as a2,uy as a3,Mt as a4,Ue as a5,Bs as a6,ju as a7,lr as a8,xu as a9,zb as aA,lx as aB,ex as aC,q0 as aD,Ms as aE,Rb as aF,dx as aG,Bi as aH,zr as aI,ga as aJ,Yb as aK,e2 as aL,_i as aM,ya as aN,Wb as aO,au as aP,U0 as aQ,G0 as aR,Ve as aS,Qo as aT,Y0 as aU,Es as aV,H0 as aW,Z0 as aX,Wr as aY,He as aZ,Yo as a_,cx as aa,eb as ab,rx as ac,As as ad,Bl as ae,E1 as af,av as ag,sv as ah,rv as ai,J as aj,iv as ak,p1 as al,c1 as am,l1 as an,Of as ao,$a as ap,Dy as aq,Fy as ar,nr as as,hx as at,uu as au,Ra as av,za as aw,ba as ax,pu as ay,hu as az,Vg as b,su as b0,ls as b1,ux as b2,Na as b3,cv as b4,mt as c,fc as d,Rt as e,Oe as f,Kg as g,tr as h,Jh as i,Nr as j,wu as k,P as l,Si as m,Jk as n,lv as o,Qg as p,Jg as q,ov as r,Zg as s,hy as t,ye as u,n1 as v,Tx as w,ev as x,Yg as y,tv as z};
