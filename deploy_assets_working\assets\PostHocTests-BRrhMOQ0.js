import{u as It,a as Fe,j as e,B as l,e as n,k as K,bz as He,aW as wt,l as X,G as y,ai as J,b9 as pe,ba as me,bb as B,bL as Mt,bM as je,V as Pt,bn as Tt,bo as Wt,bW as Oe,bX as Ge,aD as _e,bY as Le,c6 as Vt,a_ as At,aj as R,ak as Q,br as fe,bN as Bt,bc as $e,f as be,aN as Ue,ae as Rt,g as zt,a0 as qe,a6 as Et,a7 as Z,Z as Dt,W as Nt,aF as Ft,R as z,bR as Ht,ao as Ye,ap as Ke,aq as Xe,ar as ee,as as h,at as Je,h as Qe}from"./mui-libs-CfwFIaTD.js";import{r as j}from"./react-libs-Cr2nE3UY.js";import{a as Ot,f as Gt,D as te,g as _t}from"./index-Bpan7Tbe.js";import{A as Lt}from"./AddToResultsButton-BwSXKCt2.js";import{P as $t}from"./PublicationReadyGate-BGFbKbJc.js";import{a as E}from"./other-utils-CR9xr_gI.js";import{R as Ut,B as qt,C as Yt,X as Kt,Y as Xt,T as Jt,L as Qt,a as Zt,E as es}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";function se(P){const{children:i,value:L,index:D,...c}=P;return e.jsx("div",{role:"tabpanel",hidden:L!==D,id:`posthoc-tabpanel-${D}`,"aria-labelledby":`posthoc-tab-${D}`,...c,children:L===D&&e.jsx(l,{sx:{py:3},children:i})})}const hs=()=>{var Ie,we;const{datasets:P,currentDataset:i,setCurrentDataset:L}=Ot(),{addResult:D}=Gt(),c=It(),u=Fe(c.breakpoints.down("md")),ve=Fe(c.breakpoints.down("lg")),[Ze,et]=j.useState((i==null?void 0:i.id)||""),[T,ye]=j.useState(""),[I,Ce]=j.useState(""),[N,tt]=j.useState("tukey"),[w,st]=j.useState(.05),[M,nt]=j.useState(!0),[F,at]=j.useState(!0),[ne,ae]=j.useState("tabs"),[H,Se]=j.useState(0),[O,re]=j.useState(!1),[ke,$]=j.useState(null),[d,W]=j.useState(null);j.useEffect(()=>{const t=localStorage.getItem("posthoc_test_results");if(t)try{W(JSON.parse(t))}catch(s){console.error("Error parsing saved PostHoc test results:",s)}},[]),j.useEffect(()=>{u?ae("tabs"):ve&&ae("stacked")},[u,ve]);const ie=(i==null?void 0:i.columns.filter(t=>t.type===te.NUMERIC))||[],oe=(i==null?void 0:i.columns.filter(t=>t.type===te.CATEGORICAL||t.type===te.ORDINAL||t.type===te.BOOLEAN))||[],rt=t=>{const s=t.target.value;et(s),ye(""),Ce(""),W(null),localStorage.removeItem("posthoc_test_results");const r=P.find(x=>x.id===s);r&&L(r)},it=t=>{ye(t.target.value),W(null),localStorage.removeItem("posthoc_test_results")},ot=t=>{Ce(t.target.value),W(null),localStorage.removeItem("posthoc_test_results")},lt=(t,s)=>{s!==null&&(ae(s),Se(0))},G=()=>{W(null),localStorage.removeItem("posthoc_test_results")},[ct,U]=j.useState(!1),[dt,le]=j.useState(""),ce=t=>t.reduce((s,r)=>s+r,0)/t.length,ht=t=>{const s=ce(t),r=t.reduce((x,g)=>x+Math.pow(g-s,2),0)/(t.length-1);return Math.sqrt(r)},xt=(t,s,r,x)=>{if(r<=1)return[NaN,NaN];const g=r-1,b=E.studentt.inv(1-x/2,g)*(s/Math.sqrt(r));return[t-b,t+b]},ut=(t,s,r)=>Math.abs(t-s)/r,gt=(t,s,r,x,g)=>{if(t.length===0)return[];const f=t.map((a,p)=>({value:a,originalIndex:p})).sort((a,p)=>a.value-p.value),b=new Array(t.length);let _=0;switch(s){case"bonferroni":for(let a=0;a<f.length;a++)b[f[a].originalIndex]=Math.min(f[a].value*t.length,1);break;case"holm":for(let a=0;a<f.length;a++){const p=f[a].value*(t.length-a);_=Math.max(_,p),b[f[a].originalIndex]=Math.min(_,1)}for(let a=f.length-2;a>=0;a--){const p=f[a].originalIndex,S=f[a+1].originalIndex;b[p]=Math.max(b[p],b[S])}break;case"tukey":for(let a=0;a<r.length;a++){const p=r[a],S=Math.abs(p.meanDiff)/(p.se/Math.sqrt(2)),V=1-E.tukey.cdf(S,x,g);b[a]=V}break;case"scheffe":for(let a=0;a<r.length;a++){const p=r[a],S=Math.pow(p.meanDiff/p.se,2),V=1-E.centralF.cdf(S/(x-1),x-1,g);b[a]=V}break;default:for(let a=0;a<t.length;a++)b[a]=t[a];break}return b},pt=()=>{if(!i||!T||!I){$("Please select a dataset, dependent variable, and independent variable.");return}re(!0),$(null);try{const t=i.columns.find(o=>o.id===T),s=i.columns.find(o=>o.id===I);if(!t||!s)throw new Error("Selected columns not found in dataset.");const r={};i.data.forEach(o=>{const m=String(o[s.name]??"Missing"),v=o[t.name];typeof v=="number"&&!isNaN(v)&&(r[m]||(r[m]=[]),r[m].push(v))});const x=_t(I,i);if(x.length<2)throw new Error("Need at least 2 groups for PostHoc analysis.");const g={};x.forEach(o=>{const m=r[o],v=ce(m),C=ht(m),[k,A]=xt(v,C,m.length,w);g[o]={n:m.length,mean:v,std:C,ci_lower:k,ci_upper:A}});const f=[];for(let o=0;o<x.length;o++)for(let m=o+1;m<x.length;m++){const v=x[o],C=x[m],k=r[v],A=r[C],Be=g[v].mean,Re=g[C].mean,ze=g[v].std,Ee=g[C].std,Y=Be-Re,De=Math.sqrt(((k.length-1)*ze*ze+(A.length-1)*Ee*Ee)/(k.length+A.length-2)),ue=De*Math.sqrt(1/k.length+1/A.length),Ct=Y/ue,ge=k.length+A.length-2,St=2*(1-E.studentt.cdf(Math.abs(Ct),ge)),kt=ut(Be,Re,De),Ne=E.studentt.inv(1-w/2,ge)*ue;f.push({group1:v,group2:C,meanDiff:Y,pValue:St,effectSize:kt,ciLower:Y-Ne,ciUpper:Y+Ne,degreesOfFreedom:ge,se:ue})}const b=f.map(o=>o.pValue),_=x.length,a=Object.values(r).flat(),p=a.length-x.length,S=gt(b,N,f,_,p),V=f.map((o,m)=>({...o,adjustedPValue:S[m],significant:S[m]<w})),jt=ce(a);let Me=0,Pe=0;x.forEach(o=>{const m=r[o],v=g[o].mean;Me+=m.length*Math.pow(v-jt,2),Pe+=m.reduce((C,k)=>C+Math.pow(k-v,2),0)});const Te=x.length-1,ft=Me/Te,bt=Pe/p,We=ft/bt,Ve=1-E.centralF.cdf(We,Te,p),vt=x.map(o=>({group:o,mean:g[o].mean,ciLower:g[o].ci_lower,ciUpper:g[o].ci_upper,n:g[o].n})),yt=V.map(o=>({group1:o.group1,group2:o.group2,significant:o.significant,pValue:o.adjustedPValue})),Ae={dependentVariable:t,independentVariable:s,testMethod:N,alpha:w,groupStats:g,pairwiseComparisons:V,overallTest:{fStatistic:We,pValue:Ve,significant:Ve<w},chartData:{meansChart:vt,comparisonMatrix:yt}};W(Ae),localStorage.setItem("posthoc_test_results",JSON.stringify(Ae)),re(!1),le("PostHoc analysis completed successfully!"),U(!0)}catch(t){$(`Error analyzing data: ${t instanceof Error?t.message:String(t)}`),re(!1)}},q=t=>{let s="";return s+=`Overall ANOVA: F = ${t.overallTest.fStatistic.toFixed(3)}, p = ${t.overallTest.pValue.toFixed(3)}
`,s+=t.overallTest.significant?`• Significant differences found between groups (p < 0.05)

`:`• No significant differences found between groups (p ≥ 0.05)

`,s+=`Pairwise Comparisons:
`,t.pairwiseComparisons.forEach(r=>{s+=`• ${r.group1} vs ${r.group2}: `,s+=`Mean difference = ${r.meanDiff.toFixed(3)}, `,s+=`p = ${r.adjustedPValue.toFixed(3)} `,s+=r.significant?"(Significant)":"(Not significant)",F&&(s+=`, Effect size (Cohen's d) = ${r.effectSize.toFixed(3)}`),s+=`
`}),s},mt=t=>({tukey:"Tukey's HSD",bonferroni:"Bonferroni",holm:"Holm",scheffe:"Scheffé's test",fisher:"Fisher's LSD"})[t]||t,de=()=>e.jsx(Ye,{component:z,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(Ke,{size:u?"small":"medium",children:[e.jsx(Xe,{children:e.jsxs(ee,{children:[e.jsx(h,{sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Group"}),e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"N"}),e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Mean"}),e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Std Dev"}),M&&e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"95% CI"})]})}),e.jsx(Je,{children:d&&Object.entries(d.groupStats).map(([t,s],r)=>e.jsxs(ee,{sx:{"&:nth-of-type(odd)":{backgroundColor:c.palette.action.hover}},children:[e.jsx(h,{sx:{fontWeight:500},children:e.jsx(Qe,{label:t,size:"small",variant:"outlined",color:"primary"})}),e.jsx(h,{align:"right",children:s.n}),e.jsx(h,{align:"right",children:s.mean.toFixed(3)}),e.jsx(h,{align:"right",children:s.std.toFixed(3)}),M&&e.jsxs(h,{align:"right",children:["[",s.ci_lower.toFixed(3),", ",s.ci_upper.toFixed(3),"]"]})]},t))})]})}),he=()=>e.jsx(Ye,{component:z,variant:"outlined",sx:{borderRadius:2,maxHeight:400},children:e.jsxs(Ke,{size:u?"small":"medium",stickyHeader:!0,children:[e.jsx(Xe,{children:e.jsxs(ee,{children:[e.jsx(h,{sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Comparison"}),e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Mean Difference"}),e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Adjusted p-value"}),e.jsx(h,{align:"center",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Significant"}),F&&e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"Effect Size"}),M&&e.jsx(h,{align:"right",sx:{fontWeight:600,backgroundColor:c.palette.grey[50]},children:"95% CI"})]})}),e.jsx(Je,{children:d==null?void 0:d.pairwiseComparisons.map((t,s)=>e.jsxs(ee,{sx:{"&:nth-of-type(odd)":{backgroundColor:c.palette.action.hover},backgroundColor:t.significant?c.palette.success.light+"20":void 0},children:[e.jsxs(h,{sx:{fontWeight:500},children:[t.group1," vs ",t.group2]}),e.jsx(h,{align:"right",children:t.meanDiff.toFixed(3)}),e.jsx(h,{align:"right",children:t.adjustedPValue.toFixed(3)}),e.jsx(h,{align:"center",children:e.jsx(Qe,{label:t.significant?"Yes":"No",color:t.significant?"success":"default",size:"small"})}),F&&e.jsx(h,{align:"right",children:t.effectSize.toFixed(3)}),M&&e.jsxs(h,{align:"right",children:["[",t.ciLower.toFixed(3),", ",t.ciUpper.toFixed(3),"]"]})]},`${t.group1}-${t.group2}`))})]})}),xe=()=>e.jsx(z,{variant:"outlined",sx:{p:2,height:u?300:400,borderRadius:2,backgroundColor:c.palette.grey[50]},children:e.jsx(Ut,{width:"100%",height:"100%",children:e.jsxs(qt,{data:d==null?void 0:d.chartData.meansChart,margin:{top:20,right:30,left:20,bottom:u?60:80},children:[e.jsx(Yt,{strokeDasharray:"3 3",stroke:c.palette.divider}),e.jsx(Kt,{dataKey:"group",angle:u?-45:0,textAnchor:u?"end":"middle",height:u?60:80,tick:{fontSize:u?10:12}}),e.jsx(Xt,{tick:{fontSize:u?10:12}}),e.jsx(Jt,{contentStyle:{backgroundColor:c.palette.background.paper,border:`1px solid ${c.palette.divider}`,borderRadius:8},formatter:(t,s)=>[typeof t=="number"?t.toFixed(3):t,s==="mean"?"Mean":s]}),e.jsx(Qt,{}),e.jsx(Zt,{dataKey:"mean",fill:c.palette.primary.main,name:"Group Mean",radius:[4,4,0,0],children:M&&e.jsx(es,{dataKey:"ciLower",width:4})})]})})});return e.jsx($t,{children:e.jsxs(l,{sx:{p:{xs:2,md:3},maxWidth:1400,mx:"auto"},children:[e.jsxs(l,{sx:{mb:4},children:[e.jsx(n,{variant:"h4",gutterBottom:!0,sx:{fontWeight:600,color:c.palette.primary.main},children:"PostHoc Tests"}),e.jsx(n,{variant:"body1",color:"text.secondary",children:"Perform multiple comparisons between groups after finding significant ANOVA results"})]}),e.jsxs(K,{elevation:2,sx:{mb:3},children:[e.jsx(He,{title:"Configuration",avatar:e.jsx(wt,{color:"primary"}),sx:{pb:1}}),e.jsxs(X,{children:[e.jsxs(y,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(y,{item:!0,xs:12,md:4,children:e.jsxs(J,{fullWidth:!0,children:[e.jsx(pe,{children:"Dataset"}),e.jsx(me,{value:Ze,label:"Dataset",onChange:rt,disabled:P.length===0,children:P.length===0?e.jsx(B,{value:"",disabled:!0,children:"No datasets available"}):P.map(t=>e.jsx(B,{value:t.id,children:e.jsxs(l,{children:[e.jsx(n,{variant:"body2",children:t.name}),e.jsxs(n,{variant:"caption",color:"text.secondary",children:[t.data.length," rows"]})]})},t.id))})]})}),e.jsx(y,{item:!0,xs:12,md:4,children:e.jsxs(J,{fullWidth:!0,children:[e.jsx(pe,{children:"Dependent Variable (Numeric)"}),e.jsx(me,{value:T,label:"Dependent Variable (Numeric)",onChange:it,disabled:ie.length===0,children:ie.length===0?e.jsx(B,{value:"",disabled:!0,children:"No numeric variables available"}):ie.map(t=>e.jsx(B,{value:t.id,children:e.jsx(n,{variant:"body2",children:t.name})},t.id))})]})}),e.jsx(y,{item:!0,xs:12,md:4,children:e.jsxs(J,{fullWidth:!0,children:[e.jsx(pe,{children:"Independent Variable (Groups)"}),e.jsx(me,{value:I,label:"Independent Variable (Groups)",onChange:ot,disabled:oe.length===0,children:oe.length===0?e.jsx(B,{value:"",disabled:!0,children:"No categorical variables available"}):oe.map(t=>e.jsx(B,{value:t.id,children:e.jsx(n,{variant:"body2",children:t.name})},t.id))})]})})]}),d&&e.jsxs(l,{sx:{mb:3},children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Display Mode"}),e.jsxs(Mt,{value:ne,exclusive:!0,onChange:lt,size:"small",sx:{mb:2},children:[e.jsxs(je,{value:"tabs","aria-label":"tabs view",children:[e.jsx(Pt,{sx:{mr:1}}),!u&&"Tabs"]}),e.jsxs(je,{value:"stacked","aria-label":"stacked view",children:[e.jsx(Tt,{sx:{mr:1}}),!u&&"Stacked"]}),!u&&e.jsxs(je,{value:"side-by-side","aria-label":"side by side view",children:[e.jsx(Wt,{sx:{mr:1}}),"Side by Side"]})]})]}),e.jsxs(Oe,{defaultExpanded:!1,children:[e.jsx(Ge,{expandIcon:e.jsx(_e,{}),children:e.jsx(n,{variant:"subtitle1",sx:{fontWeight:500},children:"Analysis Options"})}),e.jsx(Le,{children:e.jsxs(y,{container:!0,spacing:3,children:[e.jsx(y,{item:!0,xs:12,md:6,children:e.jsxs(J,{component:"fieldset",children:[e.jsx(Vt,{component:"legend",sx:{fontWeight:500,mb:1},children:"PostHoc Test Method"}),e.jsxs(At,{value:N,onChange:t=>{tt(t.target.value),G()},children:[e.jsx(R,{value:"tukey",control:e.jsx(Q,{}),label:e.jsxs(l,{children:[e.jsx(n,{variant:"body2",children:"Tukey's HSD"}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Controls family-wise error rate"})]})}),e.jsx(R,{value:"bonferroni",control:e.jsx(Q,{}),label:e.jsxs(l,{children:[e.jsx(n,{variant:"body2",children:"Bonferroni"}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Conservative adjustment"})]})}),e.jsx(R,{value:"holm",control:e.jsx(Q,{}),label:e.jsxs(l,{children:[e.jsx(n,{variant:"body2",children:"Holm"}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Step-down method"})]})}),e.jsx(R,{value:"scheffe",control:e.jsx(Q,{}),label:e.jsxs(l,{children:[e.jsx(n,{variant:"body2",children:"Scheffé"}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Most conservative"})]})})]})]})}),e.jsx(y,{item:!0,xs:12,md:6,children:e.jsxs(fe,{spacing:3,children:[e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Significance Level (α)"}),e.jsx(Bt,{value:w,onChange:(t,s)=>{st(s),G()},min:.01,max:.1,step:.01,marks:[{value:.01,label:"0.01"},{value:.05,label:"0.05"},{value:.1,label:"0.10"}],valueLabelDisplay:"auto",sx:{mt:2}})]}),e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:500},children:"Additional Options"}),e.jsxs(fe,{spacing:1,children:[e.jsx(R,{control:e.jsx($e,{checked:M,onChange:t=>{nt(t.target.checked),G()}}),label:"Include Confidence Intervals"}),e.jsx(R,{control:e.jsx($e,{checked:F,onChange:t=>{at(t.target.checked),G()}}),label:"Include Effect Sizes (Cohen's d)"})]})]})]})})]})})]}),e.jsxs(Oe,{defaultExpanded:!1,children:[e.jsx(Ge,{expandIcon:e.jsx(_e,{}),children:e.jsx(n,{variant:"subtitle1",sx:{fontWeight:500},children:"About PostHoc Tests"})}),e.jsxs(Le,{children:[e.jsx(n,{variant:"body2",color:"text.secondary",paragraph:!0,children:"PostHoc tests are used after finding a significant ANOVA result to determine which specific groups differ from each other:"}),e.jsxs(l,{component:"ul",sx:{pl:2},children:[e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Tukey's HSD:"})," Balanced approach controlling family-wise error rate"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Bonferroni:"})," Conservative method dividing α by number of comparisons"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Holm:"})," Step-down method more powerful than Bonferroni"]}),e.jsxs(n,{component:"li",variant:"body2",color:"text.secondary",children:[e.jsx("strong",{children:"Scheffé:"})," Most conservative, controls for all possible contrasts"]})]})]})]}),e.jsxs(l,{sx:{mt:3,display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(be,{variant:"contained",size:"large",startIcon:e.jsx(Ue,{}),onClick:pt,disabled:O||!T||!I,sx:{px:4,borderRadius:2,textTransform:"none",fontWeight:600},children:O?"Analyzing...":"Run PostHoc Tests"}),d&&e.jsx(be,{variant:"outlined",size:"large",onClick:G,sx:{px:3,borderRadius:2,textTransform:"none"},children:"Clear Results"})]})]})]}),O&&e.jsx(K,{elevation:2,children:e.jsx(X,{children:e.jsxs(l,{display:"flex",flexDirection:"column",alignItems:"center",py:4,children:[e.jsx(Rt,{size:60,sx:{mb:2}}),e.jsx(n,{variant:"h6",color:"text.secondary",children:"Running PostHoc Analysis..."}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Performing pairwise comparisons between groups"})]})})}),ke&&e.jsx(zt,{severity:"error",sx:{mb:3,borderRadius:2},action:e.jsx(be,{color:"inherit",size:"small",onClick:()=>$(null),children:"Dismiss"}),children:ke}),d&&!O&&e.jsxs(K,{elevation:2,children:[e.jsx(He,{title:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(qe,{color:"primary"}),e.jsxs(n,{variant:"h6",children:["PostHoc Analysis Results: ",d.dependentVariable.name," by ",d.independentVariable.name]})]}),subheader:e.jsxs(n,{variant:"body2",color:"text.secondary",children:[mt(d.testMethod)," • α = ",d.alpha]})}),e.jsx(X,{children:ne==="tabs"?e.jsxs(l,{children:[e.jsxs(Et,{value:H,onChange:(t,s)=>Se(s),sx:{borderBottom:1,borderColor:"divider",mb:2},children:[e.jsx(Z,{label:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Dt,{fontSize:"small"}),!u&&"Group Stats"]})}),e.jsx(Z,{label:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(qe,{fontSize:"small"}),!u&&"Comparisons"]})}),e.jsx(Z,{label:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Nt,{fontSize:"small"}),!u&&"Means Chart"]})}),e.jsx(Z,{label:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ft,{fontSize:"small"}),!u&&"Summary"]})})]}),e.jsxs(se,{value:H,index:0,children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Descriptive Statistics by Group"}),de()]}),e.jsxs(se,{value:H,index:1,children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Pairwise Comparisons"}),he()]}),e.jsxs(se,{value:H,index:2,children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Group Means with Confidence Intervals"}),xe()]}),e.jsx(se,{value:H,index:3,children:e.jsxs(z,{sx:{p:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500},children:"Analysis Summary"}),e.jsx(n,{variant:"body2",component:"pre",sx:{whiteSpace:"pre-line"},children:q(d)})]})})]}):ne==="stacked"?e.jsxs(fe,{spacing:4,children:[e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Descriptive Statistics by Group"}),de()]}),e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Pairwise Comparisons"}),he()]}),e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Group Means with Confidence Intervals"}),xe()]}),e.jsx(l,{children:e.jsxs(z,{sx:{p:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500},children:"Analysis Summary"}),e.jsx(n,{variant:"body2",component:"pre",sx:{whiteSpace:"pre-line"},children:q(d)})]})})]}):e.jsxs(y,{container:!0,spacing:3,children:[e.jsxs(y,{item:!0,xs:12,lg:6,children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Group Statistics"}),de(),e.jsxs(l,{sx:{mt:3},children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Analysis Summary"}),e.jsx(z,{sx:{p:3,bgcolor:"background.paper",borderRadius:2,border:1,borderColor:"divider"},children:e.jsx(n,{variant:"body2",component:"pre",sx:{whiteSpace:"pre-line"},children:q(d)})})]})]}),e.jsxs(y,{item:!0,xs:12,lg:6,children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Pairwise Comparisons"}),he(),e.jsxs(l,{sx:{mt:3},children:[e.jsx(n,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:500,mb:2},children:"Group Means Chart"}),xe()]})]})]})}),e.jsx(l,{sx:{display:"flex",justifyContent:"center",p:2,pt:0},children:e.jsx(Lt,{resultData:{title:`Post-Hoc Tests - ${N.toUpperCase()} (${i==null?void 0:i.name})`,type:"other",component:"PostHocTests",data:{dataset:i==null?void 0:i.name,dependentVariable:((Ie=i==null?void 0:i.columns.find(t=>t.id===T))==null?void 0:Ie.name)||"Unknown",independentVariable:((we=i==null?void 0:i.columns.find(t=>t.id===I))==null?void 0:we.name)||"Unknown",testMethod:N,alpha:w,includeCI:M,includeEffectSize:F,results:d,interpretation:d?q(d):"",timestamp:new Date().toISOString(),totalSampleSize:i==null?void 0:i.data.length}},onSuccess:()=>{le("Post-hoc test results successfully added to Results Manager!"),U(!0)},onError:t=>{le(`Error adding results to Results Manager: ${t}`),U(!0)},disabled:!d||!i})})]}),!O&&!d&&T&&I&&e.jsx(K,{elevation:1,children:e.jsx(X,{children:e.jsxs(l,{textAlign:"center",py:6,children:[e.jsx(Ue,{sx:{fontSize:80,color:c.palette.grey[400],mb:2}}),e.jsx(n,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"Ready to Analyze"}),e.jsx(n,{variant:"body2",color:"text.secondary",children:'Click "Run PostHoc Tests" to perform pairwise comparisons between groups'})]})})}),e.jsx(Ht,{open:ct,autoHideDuration:6e3,onClose:()=>U(!1),message:dt,anchorOrigin:{vertical:"bottom",horizontal:"center"}})]})})};export{hs as default};
