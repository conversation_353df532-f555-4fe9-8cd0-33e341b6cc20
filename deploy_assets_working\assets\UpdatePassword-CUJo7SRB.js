import{j as e,C as y,R as b,B as l,b8 as j,e as P,g as v,ah as p,f as x,ae as S}from"./mui-libs-CfwFIaTD.js";import{r as s}from"./react-libs-Cr2nE3UY.js";import{b as C,s as B}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const T=()=>{const{logoutGuest:u}=C(),[o,h]=s.useState(""),[d,f]=s.useState(""),[n,c]=s.useState(!1),[i,t]=s.useState(null),[w,m]=s.useState(!1);s.useEffect(()=>{u()},[u]);const g=async r=>{if(r.preventDefault(),t(null),m(!1),o!==d){t({type:"error",text:"Passwords do not match."});return}if(o.length<6){t({type:"error",text:"Password must be at least 6 characters long."});return}c(!0);try{const{error:a}=await B.auth.updateUser({password:o});if(a)throw a;t({type:"success",text:"Password updated successfully! You can now log in with your new password."}),m(!0)}catch(a){t({type:"error",text:a.message||"Failed to update password."}),console.error("Password update error:",a)}finally{c(!1)}};return e.jsx(y,{maxWidth:"sm",sx:{mt:8},children:e.jsxs(b,{elevation:3,sx:{p:4},children:[e.jsxs(l,{display:"flex",flexDirection:"column",alignItems:"center",mb:3,children:[e.jsx(j,{fontSize:"large",color:"primary",sx:{mb:1}}),e.jsx(P,{variant:"h5",component:"h1",gutterBottom:!0,children:"Update Your Password"})]}),i&&e.jsx(v,{severity:i.type,sx:{mb:3},children:i.text}),w?e.jsx(l,{sx:{textAlign:"center",mt:2},children:e.jsx(x,{variant:"contained",onClick:()=>window.location.hash="#/auth/login",children:"Go to Login"})}):e.jsxs(l,{component:"form",onSubmit:g,children:[e.jsx(p,{label:"New Password",type:"password",fullWidth:!0,margin:"normal",required:!0,value:o,onChange:r=>h(r.target.value),disabled:n,helperText:"Password must be at least 6 characters."}),e.jsx(p,{label:"Confirm New Password",type:"password",fullWidth:!0,margin:"normal",required:!0,value:d,onChange:r=>f(r.target.value),disabled:n}),e.jsx(x,{type:"submit",fullWidth:!0,variant:"contained",color:"primary",size:"large",sx:{mt:3,mb:2},disabled:n,children:n?e.jsx(S,{size:24}):"Update Password"})]})]})})};export{T as default};
