import{u as K,j as e,B as o,e as r,aW as Q,g as X,R as ee,a6 as te,a7 as v,aY as N,aZ as E,v as B,aV as ae,G as l,d as se,f as D,h as re,ah as h,k as u,l as L,a_ as ne,a$ as ie,aj as _,ak as w,b0 as oe,b1 as le,b2 as C,b3 as ce,b4 as de,D as me,L as he,m as O,n as R,b5 as ue,r as $,b6 as xe,ae as pe,b7 as fe}from"./mui-libs-CfwFIaTD.js";import{r as d}from"./react-libs-Cr2nE3UY.js";import{b as ye,c as ge,s as z,P as je}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const Te=()=>{var P,k,T,A,M,W;const n=K(),{user:s,accountType:m}=ye(),{themeMode:S,setThemeMode:f,appliedTheme:be}=ge(),[a,c]=d.useState({notifications_enabled:!0,email_notifications:!0,mobile_notifications:!1,theme_preference:"system",language_preference:"en",data_privacy:!0,data_retention:"standard",two_factor_auth:!1,analytics_consent:!0}),[I,x]=d.useState(!1),[y,g]=d.useState(null),[F,G]=d.useState(!1);d.useEffect(()=>{s&&(x(!0),U())},[s]);const U=async()=>{try{if(!s)return;const{data:t,error:i}=await z.from("user_settings").select("*").eq("user_id",s.id).single();if(i&&i.code!=="PGRST116")throw i;t?(c({notifications_enabled:t.notifications_enabled??!0,email_notifications:t.email_notifications??!0,mobile_notifications:t.mobile_notifications??!1,theme_preference:t.theme_preference??"system",language_preference:t.language_preference??"en",data_privacy:t.data_privacy??!0,data_retention:t.data_retention??"standard",two_factor_auth:t.two_factor_auth??!1,analytics_consent:t.analytics_consent??!0}),t.theme_preference&&f(t.theme_preference)):c(J=>({...J,theme_preference:S}))}catch(t){console.error("Error loading user settings:",t.message),c(i=>({...i,theme_preference:S}))}finally{x(!1)}},Y=async()=>{try{if(x(!0),g(null),!s)throw new Error("No user");const t={user_id:s.id,...a,updated_at:new Date().toISOString()};console.log("Attempting to save settings:",t);const{error:i}=await z.from("user_settings").upsert(t);if(i)throw console.error("Error saving settings:",i),i;console.log("Settings saved successfully."),g({type:"success",text:"Settings saved successfully!"})}catch(t){console.error("Caught error saving settings:",t),g({type:"error",text:t.message})}finally{x(!1)}},j=t=>{c({...a,[t.target.name]:t.target.checked})},q=t=>{const i=t.target.value;c({...a,[t.target.name]:i}),f(i)},b=t=>{c({...a,theme_preference:t}),f(t)},[p,V]=d.useState(0),Z=(t,i)=>{V(i)},H=()=>{G(!1)};return s?e.jsxs(o,{sx:{p:3,maxWidth:900,mx:"auto"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(Q,{sx:{mr:1},color:"primary"}),e.jsx(r,{variant:"h5",children:"Settings"})]}),y&&e.jsx(X,{severity:y.type,sx:{mb:3},children:y.text}),e.jsxs(ee,{elevation:3,sx:{p:3},children:[e.jsxs(te,{value:p,onChange:Z,variant:"scrollable",scrollButtons:"auto",sx:{mb:3,borderBottom:1,borderColor:"divider","& .MuiTab-root":{minWidth:120}},children:[e.jsx(v,{icon:e.jsx(N,{}),label:"Appearance",iconPosition:"start"}),e.jsx(v,{icon:e.jsx(E,{}),label:"Notifications",iconPosition:"start"}),e.jsx(v,{icon:e.jsx(B,{}),label:"Privacy & Security",iconPosition:"start"})]}),e.jsxs(o,{sx:{mb:4},children:[e.jsxs(r,{variant:"h6",sx:{mb:2,display:"flex",alignItems:"center"},children:[e.jsx(ae,{sx:{mr:1},color:"primary"}),"Account Information"]}),e.jsxs(l,{container:!0,spacing:2,children:[e.jsxs(l,{item:!0,xs:12,md:4,sx:{textAlign:"center"},children:[e.jsx(se,{src:((P=s.user_metadata)==null?void 0:P.avatar_url)||"/logo.png",alt:((k=s.user_metadata)==null?void 0:k.full_name)||s.email||"User",sx:{width:120,height:120,mx:"auto",mb:2}}),e.jsx(D,{variant:"outlined",size:"small",sx:{mb:1},children:"Change Avatar"}),e.jsx(r,{variant:"body1",fontWeight:"medium",children:s.email}),e.jsxs(r,{variant:"caption",color:"text.secondary",children:["Member since: ",s.created_at?new Date(s.created_at).toLocaleDateString():"N/A"]}),m&&e.jsx(re,{label:`Subscription Type: ${m.charAt(0).toUpperCase()+m.slice(1)}`,color:m==="pro"?"primary":m==="edu"?"secondary":"default",sx:{mt:1,fontWeight:"bold"}})]}),e.jsxs(l,{item:!0,xs:12,md:8,children:[e.jsx(h,{label:"Username",value:((T=s.user_metadata)==null?void 0:T.username)||"",fullWidth:!0,margin:"normal",InputProps:{readOnly:!0}}),e.jsx(h,{label:"Full Name",value:((A=s.user_metadata)==null?void 0:A.full_name)||"",fullWidth:!0,margin:"normal",InputProps:{readOnly:!0}}),e.jsx(h,{label:"Email",value:s.email||"",fullWidth:!0,margin:"normal",InputProps:{readOnly:!0},helperText:"Email cannot be changed"}),e.jsx(h,{label:"Institution",value:((M=s.user_metadata)==null?void 0:M.institution)||"",fullWidth:!0,margin:"normal",InputProps:{readOnly:!0}}),e.jsx(h,{label:"Country",value:((W=s.user_metadata)==null?void 0:W.country)||"",fullWidth:!0,margin:"normal",InputProps:{readOnly:!0}})]})]})]}),p===0&&e.jsxs(o,{children:[e.jsxs(r,{variant:"h6",sx:{mb:2,display:"flex",alignItems:"center"},children:[e.jsx(N,{sx:{mr:1},color:"primary"}),"Theme Preferences"]}),e.jsx(u,{variant:"outlined",sx:{mb:3},children:e.jsxs(L,{children:[e.jsx(r,{variant:"subtitle1",fontWeight:"medium",gutterBottom:!0,children:"Choose your preferred theme"}),e.jsx(ne,{name:"theme_preference",value:a.theme_preference,onChange:q,sx:{mt:2},children:e.jsxs(l,{container:!0,spacing:2,children:[e.jsx(l,{item:!0,xs:12,sm:4,children:e.jsxs(u,{variant:a.theme_preference==="light"?"elevation":"outlined",elevation:a.theme_preference==="light"?3:0,sx:{p:2,textAlign:"center",bgcolor:a.theme_preference==="light"?n.palette.action.hover:"transparent",border:a.theme_preference==="light"?`2px solid ${n.palette.primary.main}`:`1px solid ${n.palette.divider}`,cursor:"pointer",boxShadow:a.theme_preference==="light"?n.shadows[3]:n.shadows[1]},onClick:()=>b("light"),children:[e.jsx(o,{sx:{display:"flex",justifyContent:"center",mb:1},children:e.jsx(ie,{fontSize:"large",color:a.theme_preference==="light"?"primary":"inherit"})}),e.jsx(_,{value:"light",control:e.jsx(w,{}),label:"Light Mode",sx:{mx:"auto"}}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Bright interface with light colors"})]})}),e.jsx(l,{item:!0,xs:12,sm:4,children:e.jsxs(u,{variant:a.theme_preference==="dark"?"elevation":"outlined",elevation:a.theme_preference==="dark"?3:0,sx:{p:2,textAlign:"center",bgcolor:a.theme_preference==="dark"?n.palette.action.hover:"transparent",border:a.theme_preference==="dark"?`2px solid ${n.palette.primary.main}`:`1px solid ${n.palette.divider}`,cursor:"pointer",boxShadow:a.theme_preference==="dark"?n.shadows[3]:n.shadows[1]},onClick:()=>b("dark"),children:[e.jsx(o,{sx:{display:"flex",justifyContent:"center",mb:1},children:e.jsx(oe,{fontSize:"large",color:a.theme_preference==="dark"?"primary":"inherit"})}),e.jsx(_,{value:"dark",control:e.jsx(w,{}),label:"Dark Mode",sx:{mx:"auto"}}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Dark interface to reduce eye strain"})]})}),e.jsx(l,{item:!0,xs:12,sm:4,children:e.jsxs(u,{variant:a.theme_preference==="system"?"elevation":"outlined",elevation:a.theme_preference==="system"?3:0,sx:{p:2,textAlign:"center",bgcolor:a.theme_preference==="system"?n.palette.action.hover:"transparent",border:a.theme_preference==="system"?`2px solid ${n.palette.primary.main}`:`1px solid ${n.palette.divider}`,cursor:"pointer",boxShadow:a.theme_preference==="system"?n.shadows[3]:n.shadows[1]},onClick:()=>b("system"),children:[e.jsx(o,{sx:{display:"flex",justifyContent:"center",mb:1},children:e.jsx(le,{fontSize:"large",color:a.theme_preference==="system"?"primary":"inherit"})}),e.jsx(_,{value:"system",control:e.jsx(w,{}),label:"System Default",sx:{mx:"auto"}}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Follow your device settings"})]})})]})})]})})]}),p===1&&e.jsxs(o,{children:[e.jsxs(r,{variant:"h6",sx:{mb:2,display:"flex",alignItems:"center"},children:[e.jsx(E,{sx:{mr:1},color:"primary"}),"Notification Preferences"]}),e.jsx(u,{variant:"outlined",sx:{mb:3},children:e.jsxs(L,{children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2},children:[e.jsx(r,{variant:"subtitle1",fontWeight:"medium",children:"All Notifications"}),e.jsx(C,{checked:a.notifications_enabled,onChange:j,name:"notifications_enabled",color:"primary",icon:e.jsx(de,{}),checkedIcon:e.jsx(ce,{})})]}),e.jsx(me,{sx:{my:2}}),e.jsx(r,{variant:"subtitle2",gutterBottom:!0,children:"Notification Channels"}),e.jsxs(he,{disablePadding:!0,children:[e.jsxs(O,{disablePadding:!0,sx:{py:1},children:[e.jsx(R,{children:e.jsx(ue,{color:"action"})}),e.jsx($,{primary:"Email Notifications",secondary:"Receive updates via email",primaryTypographyProps:{fontWeight:"medium"}}),e.jsx(C,{edge:"end",checked:a.email_notifications,onChange:j,name:"email_notifications",color:"primary",disabled:!a.notifications_enabled})]}),e.jsxs(O,{disablePadding:!0,sx:{py:1},children:[e.jsx(R,{children:e.jsx(xe,{color:"action"})}),e.jsx($,{primary:"Mobile Notifications",secondary:"Receive push notifications on mobile devices",primaryTypographyProps:{fontWeight:"medium"}}),e.jsx(C,{edge:"end",checked:a.mobile_notifications,onChange:j,name:"mobile_notifications",color:"primary",disabled:!a.notifications_enabled})]})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mt:2},children:"You'll receive notifications about new features, updates, and activity related to your account."})]})})]}),p===2&&e.jsxs(o,{children:[e.jsxs(r,{variant:"h6",sx:{mb:2,display:"flex",alignItems:"center"},children:[e.jsx(B,{sx:{mr:1},color:"primary"}),"Privacy & Security Options"]}),e.jsx(r,{variant:"body1",sx:{mb:2},children:"Enjoy more peace of mind with data security"}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"At DataStatPro, data privacy is our top priority. By default, all data processing occurs directly on your device, ensuring that no data is sent to the cloud. Calculations are performed within your browser, and all results remain securely on your device."}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"For registered users, we offer the option to save up to two datasets on our server. This allows you to access your saved data from any device after a successful login. Rest assured, your data is handled with the utmost care and security."})]}),e.jsx(o,{sx:{mt:3,display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsx(D,{variant:"contained",color:"primary",startIcon:e.jsx(fe,{}),onClick:Y,disabled:I,children:I?e.jsx(pe,{size:24}):"Save Settings"})})]}),e.jsx(je,{open:F,onClose:H})]}):e.jsx(o,{sx:{p:3,textAlign:"center"},children:e.jsx(r,{children:"Please sign in to view your settings."})})};export{Te as default};
