import{u as De,j as e,B as i,e as n,G as $,R,a6 as ze,aE as T,a7 as ae,c7 as Ve,aW as Me,F as le,ai as C,b9 as z,ba as V,bb as u,a_ as re,aj as p,ak as I,f as Be,ae as ne,a8 as oe,I as se,b7 as Ee,bH as Ae,ah as Le,c8 as Oe,bc as ie,bN as H}from"./mui-libs-CfwFIaTD.js";import{r as h,b as q}from"./react-libs-Cr2nE3UY.js";import{a as Re,D as ce,h as Te,g as We}from"./index-Bpan7Tbe.js";import{b as _e}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{l as W,P as Ne}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./math-lib-BOZ-XUok.js";import"./charts-recharts-d3-BEF1Y_jn.js";const K={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],sequential:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],diverging:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"]},de={title:"Pie Chart",textInfo:"label+percent",hole:0,colorScheme:"default",legendPosition:{y:.5,x:1.1,xanchor:"left",yanchor:"middle"},sort:!0,maxSlices:10,labelPosition:"outside",pull:0},U="plotlyPieChartDiv",Ze=()=>{const{datasets:A,currentDataset:m}=Re(),o=De(),S=h.useRef(null),[M,Y]=h.useState((m==null?void 0:m.id)||""),[b,_]=h.useState(""),[w,N]=h.useState(""),[k,ue]=h.useState("sum"),[L,he]=h.useState("frequency"),[l,J]=h.useState(de),[j,F]=h.useState("variables"),[g,B]=h.useState(null),[E,Q]=h.useState(!1),[X,D]=h.useState(null),s=q.useMemo(()=>M&&A.find(t=>t.id===M)||null,[A,M]),Z=q.useMemo(()=>(s==null?void 0:s.columns.filter(t=>t.type===ce.CATEGORICAL))||[],[s]),ee=q.useMemo(()=>(s==null?void 0:s.columns.filter(t=>t.type===ce.NUMERIC))||[],[s]);h.useEffect(()=>{m!=null&&m.id&&m.id!==M&&(Y(m.id),_(""),N(""),B(null),D(null))},[m]),h.useEffect(()=>{if(g&&S.current){const t={responsive:!0,displayModeBar:!0,displaylogo:!1,modeBarButtonsToRemove:["pan2d","lasso2d","select2d","autoScale2d"]},a={...g.layout,height:500,width:void 0,autosize:!0};W.newPlot(U,g.data,a,t)}return()=>{S.current&&typeof Ne<"u"&&W.purge&&W.purge(S.current)}},[g]),h.useEffect(()=>{b&&s&&te()},[b,w,k,L,l,s]),h.useEffect(()=>{const t=a=>{a.altKey&&!a.ctrlKey&&!a.shiftKey&&(a.key==="1"?(a.preventDefault(),F("variables")):a.key==="2"&&(a.preventDefault(),F("settings")))};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)},[]);const xe=t=>{Y(t.target.value),_(""),N(""),B(null),D(null)},be=t=>{_(t.target.value)},ge=t=>{N(t.target.value)},me=t=>{ue(t.target.value)},fe=t=>{he(t.target.value)},f=(t,a)=>{t==="hole"&&typeof a=="boolean"&&(a=a?.4:0),t==="colorScheme"&&(a=a),t==="textInfo"&&(a=a),t==="labelPosition"&&(a=a),J(x=>({...x,[t]:a}))},te=()=>{if(!s||!b){D("Please select a dataset and a category variable.");return}Q(!0),D(null),B(null);try{const t=s.columns.find(r=>r.id===b);if(!t)throw new Error("Category column not found.");let a=null;if(w&&(a=s.columns.find(r=>r.id===w),!a))throw new Error("Selected value column not found.");let x=[],O=t.name;if(w){if(a){O=`${k.charAt(0).toUpperCase()+k.slice(1)} of ${a.name} by ${t.name}`;const r={};s.data.forEach(y=>{const c=String(y[t.name]);r[c]||(r[c]=[]);const d=y[a.name];typeof d=="number"&&!isNaN(d)&&r[c].push(d)}),x=We(b,s).map(y=>{const c=r[y]||[];let d=0;if(c.length>0)switch(k){case"sum":d=c.reduce((P,G)=>P+G,0);break;case"average":d=_e(c);break;case"count":d=c.length;break;case"max":d=Math.max(...c);break;case"min":d=Math.min(...c);break;default:d=c.reduce((P,G)=>P+G,0)}return{label:y,value:d}}),O=`${k.charAt(0).toUpperCase()+k.slice(1)} of ${a.name}`}}else{const r=Te(b,null,s,"count"),v=s.data.length;if(v===0)throw new Error("No data for category variable.");x=r.map(({category:y,value:c})=>({label:y,value:L==="percentage"?c/v*100:c})),O=L==="percentage"?"Percentage":"Frequency"}if(x=x.filter(r=>r.value>0&&!isNaN(r.value)),x.length===0)throw new Error("No positive data available to plot.");if(x.length>l.maxSlices){const r=[...x].sort((d,P)=>P.value-d.value),v=r.slice(0,l.maxSlices-1),c=r.slice(l.maxSlices-1).reduce((d,P)=>d+P.value,0);c>0&&v.push({label:"Other",value:c}),x=v}const Se=x.map(r=>r.label),we=x.map(r=>r.value),ke=ve(),Pe=[{type:"pie",labels:Se,values:we,name:O,hole:l.hole,textinfo:l.textInfo,textposition:l.labelPosition,marker:{colors:ke.slice(0,x.length),line:{color:o.palette.background.paper,width:1}},pull:l.pull>0?x.map((r,v)=>v===0?l.pull:0):0,sort:l.sort,hoverinfo:"label+percent",insidetextorientation:"radial"}],Ie={title:{text:l.title,font:{size:16}},margin:{t:50,b:50,l:50,r:50},paper_bgcolor:o.palette.mode==="dark"?"#333":"#fff",plot_bgcolor:o.palette.mode==="dark"?"#222":"#fff",font:{color:o.palette.text.primary},showlegend:!0,legend:{...l.legendPosition,bgcolor:o.palette.mode==="dark"?"rgba(51,51,51,0.8)":"rgba(255,255,255,0.8)",bordercolor:o.palette.divider,borderwidth:1,orientation:l.legendPosition.xanchor==="center"&&(l.legendPosition.y===-.2||l.legendPosition.y===1.2)?"h":"v"},autosize:!0};B({data:Pe,layout:Ie})}catch(t){D(`Error generating chart data: ${t instanceof Error?t.message:String(t)}`),B(null)}finally{Q(!1)}},pe=()=>{J(de)},je=()=>{if(S.current&&g){const t={format:"svg",filename:l.title.replace(/\s+/g,"_")||"piechart",width:S.current.offsetWidth||600,height:S.current.offsetHeight||500};W.downloadImage(U,t)}else D("Chart data not available for download.")},ve=()=>K[l.colorScheme]||K.default,ye=!b||E,Ce=!g||E;return e.jsxs(i,{p:3,children:[e.jsx(n,{variant:"h5",gutterBottom:!0,children:"Pie Chart Generator"}),e.jsxs($,{container:!0,spacing:2,children:[e.jsxs($,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx(R,{elevation:1,sx:{mb:1,backgroundColor:o.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(ze,{value:j,onChange:(t,a)=>F(a),variant:"fullWidth",sx:{minHeight:44,"& .MuiTab-root":{minHeight:44,fontSize:"0.875rem",fontWeight:500,color:o.palette.text.secondary,textTransform:"none",transition:"all 0.2s ease-in-out","&.Mui-selected":{color:o.palette.primary.main,backgroundColor:o.palette.mode==="dark"?"rgba(255, 255, 255, 0.08)":"rgba(25, 118, 210, 0.08)"},"&:hover":{backgroundColor:o.palette.mode==="dark"?"rgba(255, 255, 255, 0.04)":"rgba(0, 0, 0, 0.04)"}},"& .MuiTabs-indicator":{height:3,borderRadius:"3px 3px 0 0"}},children:[e.jsx(T,{title:"Variable Selection Panel",placement:"top",children:e.jsx(ae,{value:"variables",label:"Variables",icon:e.jsx(Ve,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(T,{title:"Chart Settings Panel",placement:"top",children:e.jsx(ae,{value:"settings",label:"Settings",icon:e.jsx(Me,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(le,{in:j==="variables",timeout:300,children:e.jsx(i,{sx:{display:j==="variables"?"block":"none"},children:e.jsxs(R,{elevation:2,sx:{p:2,height:"fit-content"},children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Variable Selection"}),e.jsxs(C,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(z,{id:"dataset-select-label",children:"Dataset"}),e.jsx(V,{labelId:"dataset-select-label",value:M,label:"Dataset",onChange:xe,disabled:A.length===0,children:A.map(t=>e.jsxs(u,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]}),e.jsxs(C,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(z,{id:"category-variable-label",children:"Category Variable"}),e.jsx(V,{labelId:"category-variable-label",value:b,label:"Category Variable",onChange:be,disabled:Z.length===0,children:Z.map(t=>e.jsx(u,{value:t.id,children:t.name},t.id))}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Variable defining the slices"})]}),e.jsxs(C,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(z,{id:"value-variable-label",children:"Value Variable (Optional)"}),e.jsxs(V,{labelId:"value-variable-label",value:w,label:"Value Variable (Optional)",onChange:ge,disabled:ee.length===0,children:[e.jsx(u,{value:"",children:e.jsx("em",{children:"None (Use Frequency/Percentage)"})}),ee.map(t=>e.jsx(u,{value:t.id,children:t.name},t.id))]}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Variable determining slice size (if selected)"})]}),w&&e.jsxs(C,{component:"fieldset",fullWidth:!0,margin:"normal",children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"Aggregation Method"}),e.jsxs(re,{value:k,onChange:me,row:!0,children:[e.jsx(p,{value:"sum",control:e.jsx(I,{size:"small"}),label:"Sum"}),e.jsx(p,{value:"average",control:e.jsx(I,{size:"small"}),label:"Average"}),e.jsx(p,{value:"count",control:e.jsx(I,{size:"small"}),label:"Count"}),e.jsx(p,{value:"max",control:e.jsx(I,{size:"small"}),label:"Max"}),e.jsx(p,{value:"min",control:e.jsx(I,{size:"small"}),label:"Min"})]})]}),!w&&b&&e.jsxs(C,{component:"fieldset",fullWidth:!0,margin:"normal",children:[e.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"Display Mode"}),e.jsxs(re,{value:L,onChange:fe,row:!0,children:[e.jsx(p,{value:"frequency",control:e.jsx(I,{size:"small"}),label:"Frequency"}),e.jsx(p,{value:"percentage",control:e.jsx(I,{size:"small"}),label:"Percentage"})]})]}),e.jsxs(i,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(Be,{variant:"contained",onClick:te,disabled:ye,startIcon:E?e.jsx(ne,{size:20}):e.jsx(oe,{}),fullWidth:!0,children:E?"Generating...":"Generate Pie Chart"}),e.jsxs(i,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(T,{title:"Download Chart",children:e.jsx(se,{onClick:je,disabled:Ce,children:e.jsx(Ee,{})})}),e.jsx(T,{title:"Reset Settings",children:e.jsx(se,{onClick:pe,children:e.jsx(Ae,{})})})]})]})]})})}),e.jsx(le,{in:j==="settings",timeout:300,children:e.jsx(i,{sx:{display:j==="settings"?"block":"none"},children:e.jsxs(R,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:o.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:2,mb:2},children:"Chart Settings"}),e.jsxs(i,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,sx:{mt:1},children:"Labels & Title"}),e.jsx(Le,{fullWidth:!0,label:"Chart Title",value:l.title,onChange:t=>f("title",t.target.value),margin:"normal",size:"small"}),e.jsxs(C,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(z,{id:"textinfo-label",children:"Show on Slices"}),e.jsxs(V,{labelId:"textinfo-label",value:l.textInfo,label:"Show on Slices",onChange:t=>f("textInfo",t.target.value),children:[e.jsx(u,{value:"label+percent",children:"Label + Percent"}),e.jsx(u,{value:"label+value",children:"Label + Value"}),e.jsx(u,{value:"label",children:"Label Only"}),e.jsx(u,{value:"percent",children:"Percent Only"}),e.jsx(u,{value:"value",children:"Value Only"}),e.jsx(u,{value:"none",children:"None"})]})]}),e.jsxs(C,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(z,{id:"label-position-label",children:"Label Position"}),e.jsxs(V,{labelId:"label-position-label",value:l.labelPosition,label:"Label Position",onChange:t=>f("labelPosition",t.target.value),children:[e.jsx(u,{value:"inside",children:"Inside"}),e.jsx(u,{value:"outside",children:"Outside"}),e.jsx(u,{value:"auto",children:"Auto"}),e.jsx(u,{value:"none",children:"None (Hides labels)"})]})]})]}),e.jsxs(i,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(C,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(z,{id:"color-scheme-label",children:"Color Scheme"}),e.jsx(V,{labelId:"color-scheme-label",value:l.colorScheme,label:"Color Scheme",onChange:t=>f("colorScheme",t.target.value),children:Object.keys(K).map(t=>e.jsx(u,{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})]}),e.jsxs(i,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(Oe,{children:[e.jsx(p,{control:e.jsx(ie,{checked:l.hole>0,onChange:t=>f("hole",t.target.checked),size:"small"}),label:"Donut Chart"}),e.jsx(p,{control:e.jsx(ie,{checked:l.sort,onChange:t=>f("sort",t.target.checked),size:"small"}),label:"Sort Slices (Descending)"})]})]}),e.jsxs(i,{mb:3,children:[e.jsx(n,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Size & Styling"}),l.hole>0&&e.jsxs(i,{sx:{mt:2},children:[e.jsx(n,{gutterBottom:!0,children:"Donut Hole Size"}),e.jsx(H,{value:l.hole,min:.1,max:.8,step:.05,onChange:(t,a)=>f("hole",a),valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(i,{sx:{mt:2},children:[e.jsx(n,{gutterBottom:!0,children:"Slice Separation (Pull)"}),e.jsx(H,{value:l.pull,min:0,max:.5,step:.05,onChange:(t,a)=>f("pull",a),valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(i,{sx:{mt:2},children:[e.jsx(n,{gutterBottom:!0,children:'Maximum Slices (incl. "Other")'}),e.jsx(H,{value:l.maxSlices,min:2,max:20,step:1,onChange:(t,a)=>f("maxSlices",a),valueLabelDisplay:"auto",size:"small"})]})]})]})})})]}),e.jsx($,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs(R,{elevation:2,sx:{p:2},children:[e.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(n,{variant:"h6",children:"Chart Preview"}),e.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(n,{variant:"body2",color:"text.secondary",children:["Active: ",j==="variables"?"Variables":"Settings"]}),e.jsx(i,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:j==="variables"?o.palette.primary.main:o.palette.warning.main,boxShadow:`0 0 0 2px ${j==="variables"?o.palette.primary.main+"20":o.palette.warning.main+"20"}`}})]})]}),X&&e.jsx(i,{sx:{mb:2,p:2,backgroundColor:o.palette.error.light+"20",borderRadius:1,border:`1px solid ${o.palette.error.light}`},children:e.jsx(n,{color:"error",children:X})}),e.jsx(i,{ref:S,id:U,sx:{height:500,width:"100%",display:g?"block":"flex",justifyContent:g?"flex-start":"center",alignItems:g?"flex-start":"center",border:`1px solid ${o.palette.divider}`,borderRadius:1,backgroundColor:o.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)",overflow:"hidden"},children:E?e.jsxs(i,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(ne,{}),e.jsx(n,{color:"text.secondary",children:"Generating chart..."})]}):g?e.jsx(e.Fragment,{}):e.jsxs(i,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,children:[e.jsx(oe,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(n,{color:"text.secondary",textAlign:"center",children:s?b?"Chart will appear here once generated":"Select a category variable to generate the pie chart":"Select a dataset to begin"}),s&&!b&&e.jsx(n,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"}),e.jsx(n,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Keyboard shortcuts: Alt+1 (Variables) | Alt+2 (Settings)"})]})})]})})]})]})};export{Ze as default};
