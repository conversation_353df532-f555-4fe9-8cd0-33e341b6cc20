import{u as Y,j as e,B as S,e as x,R as q,G as v,ao as U,ap as B,aq as z,ar as f,as as h,at as N,ah as b,f as M,bG as V,bH as k,D,i as w}from"./mui-libs-CfwFIaTD.js";import{r as E}from"./react-libs-Cr2nE3UY.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{S as j}from"./StatsCard-op8tGQ0a.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import{j as F}from"./other-utils-CR9xr_gI.js";import{c as G}from"./non-parametric-Cf6Ds91x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";class g{static prevalence(r,n,t){const c=(r+n)/t,s=1.96,o=1+s*s/t,p=c+s*s/(2*t),l=s*Math.sqrt(c*(1-c)/t+s*s/(4*t*t));return{prevalence:c,ci:[(p-l)/o,(p+l)/o]}}static prevalenceRatio(r,n,t,c){const s=r/(r+n),o=t/(t+c),p=s/o,l=Math.sqrt(1/r-1/(r+n)+(1/t-1/(t+c)));return{pr:p,ci:[Math.exp(Math.log(p)-1.96*l),Math.exp(Math.log(p)+1.96*l)]}}static prevalenceOddsRatio(r,n,t,c){const s=r*c/(n*t),o=Math.sqrt(1/r+1/n+1/t+1/c);return{por:s,ci:[Math.exp(Math.log(s)-1.96*o),Math.exp(Math.log(s)+1.96*o)]}}static uncorrectedChiSquareTest(r,n,t,c){return G([[r,n],[t,c]])}static chiSquareTestYates(r,n,t,c){const s=r+n+t+c,o=[(r+t)*(r+n)/s,(n+c)*(r+n)/s,(r+t)*(t+c)/s,(n+c)*(t+c)/s],p=Math.pow(Math.abs(r-o[0])-.5,2)/o[0]+Math.pow(Math.abs(n-o[1])-.5,2)/o[1]+Math.pow(Math.abs(t-o[2])-.5,2)/o[2]+Math.pow(Math.abs(c-o[3])-.5,2)/o[3];return{chi2:p,p:this.chiSquarePValue(p,1)}}static chiSquarePValue(r,n){return n<=0?null:1-F.chisquare.cdf(r,n)}static fisherExact(r,n,t,c){const s=u=>{if(u===0||u===1)return 1;let a=1;for(let i=2;i<=u;i++)a*=i;return a},o=(u,a,i,d)=>s(u+a)*s(i+d)*s(u+i)*s(a+d)/(s(u)*s(a)*s(i)*s(d)*s(u+a+i+d)),p=o(r,n,t,c);let l=0;for(let u=0;u<=Math.min(r+n,r+t);u++){const a=r+n-u,i=r+t-u,d=n+c-a;if(a>=0&&i>=0&&d>=0){const m=o(u,a,i,d);m<=p&&(l+=m)}}return l}}const ae=()=>{const y=Y(),[r,n]=E.useState({a:0,b:0,c:0,d:0}),[t,c]=E.useState({}),s=(a,i)=>{const d=i===""?0:parseInt(i,10);n(m=>({...m,[a]:isNaN(d)?0:d}))},o=()=>{const{a,b:i,c:d,d:m}=r,C=a+i+d+m;if(C!==0)try{const R=g.prevalence(a,d,C),P=g.prevalenceRatio(a,i,d,m),I=g.prevalenceOddsRatio(a,i,d,m),$=g.chiSquareTestYates(a,i,d,m),O=g.uncorrectedChiSquareTest(a,i,d,m);let T=null;C<100&&(T=g.fisherExact(a,i,d,m)),c({prevalence:R,prevalenceRatio:P,prevalenceOddsRatio:I,chiSquareYates:$,chiSquareUncorrected:O,fisherExact:T})}catch(R){console.error("Error calculating results:",R)}},p=()=>{n({a:0,b:0,c:0,d:0}),c({})},l=(a,i=3)=>a.toFixed(i),u=(a,i=3)=>`${l(a[0],i)} - ${l(a[1],i)}`;return e.jsxs(S,{children:[e.jsx(x,{variant:"h6",gutterBottom:!0,children:"Cross-Sectional Study Calculator"}),e.jsx(x,{variant:"body1",paragraph:!0,children:"Calculate measures of association for cross-sectional studies, including prevalence ratios and odds ratios."}),e.jsxs(q,{elevation:1,sx:{p:3,mb:3},children:[e.jsx(x,{variant:"subtitle1",gutterBottom:!0,children:"2×2 Contingency Table"}),e.jsx(x,{variant:"body2",paragraph:!0,children:"Enter the values for your 2×2 table to calculate epidemiological measures."}),e.jsxs(v,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(v,{item:!0,xs:12,md:7,children:e.jsx(U,{component:q,variant:"outlined",children:e.jsxs(B,{"aria-label":"2x2 contingency table",children:[e.jsx(z,{children:e.jsxs(f,{children:[e.jsx(h,{}),e.jsx(h,{align:"center",children:"Disease+"}),e.jsx(h,{align:"center",children:"Disease-"}),e.jsx(h,{align:"center",children:"Total"})]})}),e.jsxs(N,{children:[e.jsxs(f,{children:[e.jsx(h,{component:"th",scope:"row",children:"Exposed"}),e.jsx(h,{align:"center",children:e.jsx(b,{type:"number",value:r.a||"",onChange:a=>s("a",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(h,{align:"center",children:e.jsx(b,{type:"number",value:r.b||"",onChange:a=>s("b",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(h,{align:"center",children:e.jsx(x,{variant:"body2",children:r.a+r.b})})]}),e.jsxs(f,{children:[e.jsx(h,{component:"th",scope:"row",children:"Unexposed"}),e.jsx(h,{align:"center",children:e.jsx(b,{type:"number",value:r.c||"",onChange:a=>s("c",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(h,{align:"center",children:e.jsx(b,{type:"number",value:r.d||"",onChange:a=>s("d",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}})}),e.jsx(h,{align:"center",children:e.jsx(x,{variant:"body2",children:r.c+r.d})})]}),e.jsxs(f,{children:[e.jsx(h,{component:"th",scope:"row",children:"Total"}),e.jsx(h,{align:"center",children:e.jsx(x,{variant:"body2",children:r.a+r.c})}),e.jsx(h,{align:"center",children:e.jsx(x,{variant:"body2",children:r.b+r.d})}),e.jsx(h,{align:"center",children:e.jsx(x,{variant:"body2",children:r.a+r.b+r.c+r.d})})]})]})]})})}),e.jsxs(v,{item:!0,xs:12,md:4,sx:{display:"flex",flexDirection:"column",justifyContent:"center"},children:[e.jsxs(S,{sx:{display:"flex",gap:2,mb:2},children:[e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(V,{}),onClick:o,disabled:Object.values(r).every(a=>a===0),children:"Calculate"}),e.jsx(M,{variant:"outlined",startIcon:e.jsx(k,{}),onClick:p,children:"Reset"})]}),e.jsx(x,{variant:"body2",color:"text.secondary",children:"Enter the values in the 2×2 table and click Calculate to compute epidemiological measures."})]})]}),Object.keys(t).length>0&&e.jsxs(S,{sx:{mt:4},children:[e.jsx(D,{sx:{mb:3}}),e.jsx(x,{variant:"h6",gutterBottom:!0,children:"Results"}),e.jsxs(v,{container:!0,spacing:3,children:[t.prevalence&&e.jsx(v,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{title:"Prevalence",value:`${l(t.prevalence.prevalence*100)}%`,description:`95% CI: ${l(t.prevalence.ci[0]*100)}% - ${l(t.prevalence.ci[1]*100)}%`,color:"primary",variant:"outlined"})}),t.prevalenceRatio&&e.jsx(v,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{title:"Prevalence Ratio",value:l(t.prevalenceRatio.pr),description:`95% CI: ${u(t.prevalenceRatio.ci)}`,color:"secondary",variant:"outlined",tooltip:"Ratio of prevalence in exposed vs. unexposed groups"})}),t.prevalenceOddsRatio&&e.jsx(v,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{title:"Prevalence Odds Ratio",value:l(t.prevalenceOddsRatio.por),description:`95% CI: ${u(t.prevalenceOddsRatio.ci)}`,color:"info",variant:"outlined",tooltip:"Odds ratio of disease in exposed vs. unexposed groups"})}),t.chiSquareUncorrected&&e.jsx(v,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{title:"Chi-Square Test (Uncorrected)",value:l(t.chiSquareUncorrected.chiSquare),description:`p-value: ${l(t.chiSquareUncorrected.pValue)} (df=${t.chiSquareUncorrected.df})`,color:"warning",variant:"outlined",tooltip:"Uncorrected Chi-square test for 2×2 tables"})}),t.chiSquareYates&&e.jsx(v,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{title:"Chi-Square Test (Yates)",value:l(t.chiSquareYates.chi2),description:t.chiSquareYates.p!==null?`p-value: ${l(t.chiSquareYates.p)}`:"p-value: Not calculated",color:"warning",variant:"outlined",tooltip:"Chi-square test with Yates' correction for 2×2 tables"})}),t.fisherExact!==null&&t.fisherExact!==void 0&&e.jsx(v,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{title:"Fisher's Exact Test",value:`p = ${l(t.fisherExact)}`,description:`Significance at α = 0.05: ${t.fisherExact<.05?"Yes":"No"}`,color:"success",variant:"outlined",tooltip:"Fisher's exact test for small sample sizes"})})]}),e.jsxs(q,{variant:"outlined",sx:{mt:3,p:2,backgroundColor:w(y.palette.info.main,.05),borderColor:w(y.palette.info.main,.2)},children:[e.jsx(x,{variant:"subtitle2",gutterBottom:!0,children:"Interpretation Guidelines:"}),e.jsxs(x,{variant:"body2",children:["• ",e.jsx("strong",{children:"Prevalence Ratio = 1:"})," No association between exposure and disease",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Prevalence Ratio > 1:"})," Positive association (exposure may increase disease prevalence)",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Prevalence Ratio < 1:"})," Negative association (exposure may decrease disease prevalence)",e.jsx("br",{}),"• ",e.jsx("strong",{children:"Statistical significance:"})," If the 95% confidence interval does not include 1, or p-value < 0.05"]})]})]})]})]})};export{ae as default};
