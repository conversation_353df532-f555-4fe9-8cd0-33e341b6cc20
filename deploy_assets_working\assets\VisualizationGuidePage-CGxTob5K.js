import{j as e,B as c,G as d,R as h,e as n,L as a,a1 as u,r as s,D as y,m as r,ax as g,W as b,ay as f,az as v,ao as C,ap as B,aq as P,ar as l,as as t,at as T,N as w,aA as z,aB as D}from"./mui-libs-CfwFIaTD.js";import{r as I,b as R}from"./react-libs-Cr2nE3UY.js";import{H as k}from"./index-Bpan7Tbe.js";import{P as A}from"./PageTitle-DA3BXQ4x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const m=[{id:"define-story",title:"1. Define Your Data Story",icon:e.jsx(g,{}),content:e.jsxs(e.Fragment,{children:[e.jsx(n,{variant:"body1",paragraph:!0,children:"Ask: What message do you want to convey?"}),e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Compare values (e.g., sales across regions)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Show trends over time (e.g., monthly revenue growth)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Display distributions (e.g., age groups in a population)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Highlight proportions (e.g., market share by product)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Reveal relationships (e.g., correlation between variables)."})})]})]})},{id:"match-charts",title:"2. Match Chart Types to Goals",icon:e.jsx(b,{}),content:e.jsxs(e.Fragment,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"A. Comparisons"}),e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Bar/Column Charts: Compare discrete categories (e.g., product sales)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Grouped/Stacked Bars: Compare subgroups within categories."})}),e.jsx(r,{children:e.jsx(s,{primary:"Radar Charts: Compare multiple variables (e.g., skill assessments)."})})]}),e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{mt:1},children:"B. Trends Over Time"}),e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Line Charts: Track continuous data trends (e.g., stock prices)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Area Charts: Emphasize cumulative trends (e.g., total website visits)."})})]}),e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{mt:1},children:"C. Distributions"}),e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Histograms: Show frequency distribution of numerical data."})}),e.jsx(r,{children:e.jsx(s,{primary:"Box Plots: Visualize spread, outliers, and quartiles."})}),e.jsx(r,{children:e.jsx(s,{primary:"Violin Plots: Combine distribution and density (advanced)."})})]}),e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{mt:1},children:"D. Proportions"}),e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Pie/Doughnut Charts: Display parts of a whole (use ≤6 categories)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Treemaps: Show hierarchical proportions (e.g., budget allocation)."})})]}),e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{mt:1},children:"E. Relationships"}),e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Scatter Plots: Reveal correlations between two variables."})}),e.jsx(r,{children:e.jsx(s,{primary:"Bubble Charts: Add a third dimension (size) to scatter plots."})}),e.jsx(r,{children:e.jsx(s,{primary:"Heatmaps: Visualize matrix data (e.g., user activity by hour)."})})]})]})},{id:"optimize-clarity",title:"3. Optimize for Clarity",icon:e.jsx(f,{}),content:e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Avoid clutter: Simplify labels, limit colors, and remove redundant elements."})}),e.jsx(r,{children:e.jsx(s,{primary:"Prioritize accessibility: Use high-contrast colors and alt-text for digital charts."})}),e.jsx(r,{children:e.jsx(s,{primary:"Annotate key insights: Add brief notes to highlight trends or outliers."})})]})},{id:"common-pitfalls",title:"4. Common Pitfalls to Avoid",icon:e.jsx(v,{}),content:e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Misleading axes: Never truncate axes to exaggerate differences."})}),e.jsx(r,{children:e.jsx(s,{primary:"Overused 3D effects: They distort proportions (e.g., in pie charts)."})}),e.jsx(r,{children:e.jsx(s,{primary:"Inappropriate charts:",secondary:e.jsxs(e.Fragment,{children:["Don’t use pie charts for time-series data.",e.jsx("br",{}),"Avoid line charts for non-sequential categories."]})})})]})},{id:"quick-reference",title:"Quick Reference Table",icon:e.jsx(w,{}),content:e.jsxs(C,{component:h,sx:{my:0},children:[" ",e.jsxs(B,{size:"small",children:[e.jsx(P,{children:e.jsxs(l,{children:[e.jsx(t,{children:"Goal"}),e.jsx(t,{children:"Recommended Charts"})]})}),e.jsxs(T,{children:[e.jsxs(l,{children:[e.jsx(t,{children:"Compare categories"}),e.jsx(t,{children:"Bar, Column, Radar"})]}),e.jsxs(l,{children:[e.jsx(t,{children:"Show trends"}),e.jsx(t,{children:"Line, Area"})]}),e.jsxs(l,{children:[e.jsx(t,{children:"Visualize distributions"}),e.jsx(t,{children:"Histogram, Box Plot, Violin Plot"})]}),e.jsxs(l,{children:[e.jsx(t,{children:"Display proportions"}),e.jsx(t,{children:"Pie, Doughnut, Treemap"})]}),e.jsxs(l,{children:[e.jsx(t,{children:"Reveal relationships"}),e.jsx(t,{children:"Scatter Plot, Bubble Chart, Heatmap"})]})]})]})]})},{id:"tools-resources",title:"Tools & Resources",icon:e.jsx(z,{}),content:e.jsxs(a,{dense:!0,children:[e.jsx(r,{children:e.jsx(s,{primary:"Automated tools: Use AI-driven platforms (e.g., Tableau, Power BI) for smart chart suggestions."})}),e.jsx(r,{children:e.jsx(s,{primary:"Design frameworks: Follow Google’s Data Visualization Principles for clean, impactful visuals."})}),e.jsx(r,{children:e.jsx(s,{primary:"Interactive libraries: D3.js (advanced) or Plotly (user-friendly) for custom visualizations."})})]})},{id:"final-thoughts",title:"Final Thoughts",icon:e.jsx(D,{}),content:e.jsx(n,{variant:"body1",paragraph:!0,children:"Always test your visualization with a sample audience to ensure clarity and effectiveness. For complex datasets, combine multiple charts or use dashboards for deeper exploration."})}],M=()=>{const x=I.useRef([]),p=i=>{var o;(o=x.current[i])==null||o.scrollIntoView({behavior:"smooth",block:"start"})};return e.jsxs(e.Fragment,{children:[e.jsxs(k,{children:[e.jsx("title",{children:"Data Visualization Guide - DataStatPro"}),e.jsx("meta",{name:"description",content:"Learn how to choose the right chart type for your data story. Guide covers comparisons, trends, distributions, proportions, relationships, optimization tips, and common pitfalls."})]}),e.jsxs(c,{sx:{p:3},children:[e.jsx(A,{title:"Visualization Guide"}),e.jsxs(d,{container:!0,spacing:3,sx:{mt:2},children:[e.jsx(d,{item:!0,xs:12,md:3,children:e.jsxs(h,{sx:{p:2,position:"sticky",top:"80px"},children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Guide Sections"}),e.jsx(a,{component:"nav",dense:!0,children:m.map((i,o)=>e.jsxs(u,{onClick:()=>p(o),children:[i.icon&&e.jsx(c,{sx:{mr:1.5,display:"flex",alignItems:"center",color:"primary.main"},children:R.cloneElement(i.icon,{fontSize:"small"})}),e.jsx(s,{primary:i.title.replace(/^\d+\.\s*/,""),primaryTypographyProps:{variant:"body2"}})]},i.id))})]})}),e.jsx(d,{item:!0,xs:12,md:9,children:m.map((i,o)=>e.jsxs(h,{sx:{p:3,mb:3},ref:j=>x.current[o]=j,id:i.id,children:[e.jsxs(n,{variant:"h5",gutterBottom:!0,component:"div",sx:{display:"flex",alignItems:"center"},children:[i.icon&&e.jsx(c,{sx:{mr:1,display:"flex",alignItems:"center",color:"primary.main"},children:i.icon}),i.title]}),e.jsx(y,{sx:{mb:2}}),i.content]},i.id))})]})]})]})};export{M as default};
