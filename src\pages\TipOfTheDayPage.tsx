import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  IconButton,
  useTheme,
  alpha,
  Chip,
  Divider
} from '@mui/material';
import {
  TipsAndUpdates as TipsIcon,
  NavigateNext as NextIcon,
  NavigateBefore as PrevIcon,
  Lightbulb as LightbulbIcon,
  Star as StarIcon
} from '@mui/icons-material';
import PageTitle from '../components/UI/PageTitle';

interface Tip {
  id: number;
  title: string;
  content: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
}

const tips: Tip[] = [
  {
    id: 1,
    title: 'Use Keyboard Shortcuts for Faster Navigation',
    content: 'Press Ctrl+K to quickly search and navigate to any analysis tool. This saves time when working with multiple datasets.',
    category: 'Navigation',
    difficulty: 'Beginner'
  },
  {
    id: 2,
    title: 'Check Data Distribution Before Analysis',
    content: 'Always run descriptive statistics and normality tests before performing inferential statistics. This helps you choose the right statistical test.',
    category: 'Statistics',
    difficulty: 'Intermediate'
  },
  {
    id: 3,
    title: 'Save Your Work with Results Manager',
    content: 'Use the Results Manager to save and organize your analysis outputs. You can export them later as publication-ready reports.',
    category: 'Workflow',
    difficulty: 'Beginner'
  },
  {
    id: 4,
    title: 'Leverage AI Assistant for Test Selection',
    content: 'When unsure which statistical test to use, describe your research question to the AI Assistant for personalized recommendations.',
    category: 'AI Features',
    difficulty: 'Beginner'
  },
  {
    id: 5,
    title: 'Use Sample Size Calculators Before Data Collection',
    content: 'Calculate required sample sizes before starting your study to ensure adequate statistical power for your analyses.',
    category: 'Study Design',
    difficulty: 'Intermediate'
  },
  {
    id: 6,
    title: 'Create Custom Variable Groups',
    content: 'Group related variables together in the Variable Editor to streamline your analysis workflow and reduce selection time.',
    category: 'Data Management',
    difficulty: 'Advanced'
  },
  {
    id: 7,
    title: 'Export High-Quality Visualizations',
    content: 'Use the export options in charts to save publication-ready figures in PNG, SVG, or PDF formats with custom dimensions.',
    category: 'Visualization',
    difficulty: 'Intermediate'
  },
  {
    id: 8,
    title: 'Filter Data Non-Destructively',
    content: 'Use data filters to create focused analyses without modifying your original dataset. Filters can be saved and reapplied.',
    category: 'Data Management',
    difficulty: 'Intermediate'
  },
  {
    id: 9,
    title: 'Use Post-Hoc Tests After Significant ANOVA',
    content: 'When ANOVA shows significant differences, run post-hoc tests like Tukey HSD or Bonferroni to identify which specific groups differ from each other.',
    category: 'Statistics',
    difficulty: 'Advanced'
  },
  {
    id: 10,
    title: 'Choose the Right Chart for Your Data Type',
    content: 'Use bar charts for categorical comparisons, line charts for trends over time, histograms for distributions, and scatter plots for relationships between continuous variables.',
    category: 'Visualization',
    difficulty: 'Beginner'
  },
  {
    id: 11,
    title: 'Import CSV Files with Custom Delimiters',
    content: 'When importing CSV files, check the delimiter settings if data appears in a single column. DataStatPro supports semicolons, tabs, and custom separators.',
    category: 'Data Import',
    difficulty: 'Beginner'
  },
  {
    id: 12,
    title: 'Transform Variables Before Analysis',
    content: 'Use the Variable Editor to log-transform skewed data, create dummy variables from categorical data, or compute new variables from existing ones.',
    category: 'Data Management',
    difficulty: 'Intermediate'
  },
  {
    id: 13,
    title: 'Generate APA-Style Statistical Reports',
    content: 'Use the Publication Tools to automatically format your results in APA style with proper statistical notation, effect sizes, and confidence intervals.',
    category: 'Publication',
    difficulty: 'Intermediate'
  },
  {
    id: 14,
    title: 'Calculate Effect Sizes for Meaningful Results',
    content: 'Always report effect sizes (Cohen\'s d, eta-squared, r) alongside p-values. DataStatPro automatically calculates these for most statistical tests.',
    category: 'Statistics',
    difficulty: 'Intermediate'
  },
  {
    id: 15,
    title: 'Use Correlation Matrices for Variable Screening',
    content: 'Before running complex analyses, create correlation matrices to identify multicollinearity issues and understand relationships between variables.',
    category: 'Statistics',
    difficulty: 'Advanced'
  },
  {
    id: 16,
    title: 'Optimize Chart Colors for Accessibility',
    content: 'Choose colorblind-friendly palettes in the visualization settings. Avoid red-green combinations and use patterns or shapes for additional distinction.',
    category: 'Visualization',
    difficulty: 'Intermediate'
  },
  {
    id: 17,
    title: 'Export Data with Analysis-Ready Formatting',
    content: 'When exporting datasets, use the "Analysis Ready" format option to maintain variable types, labels, and missing value codes for future use.',
    category: 'Data Export',
    difficulty: 'Beginner'
  },
  {
    id: 18,
    title: 'Set Variable Labels for Clear Output',
    content: 'Add descriptive labels to your variables in the Variable Editor. These labels will appear in all analysis outputs, making results easier to interpret.',
    category: 'Data Management',
    difficulty: 'Beginner'
  },
  {
    id: 19,
    title: 'Use Power Analysis for Study Planning',
    content: 'Access the Sample Size Calculators to determine required sample sizes based on expected effect sizes, desired power (0.80), and significance level (0.05).',
    category: 'Study Design',
    difficulty: 'Advanced'
  },
  {
    id: 20,
    title: 'Ask AI Assistant About Assumption Violations',
    content: 'When statistical assumptions are violated, ask the AI Assistant for alternative non-parametric tests or data transformation suggestions.',
    category: 'AI Features',
    difficulty: 'Intermediate'
  },
  {
    id: 21,
    title: 'Organize Results by Research Questions',
    content: 'Create separate folders in Results Manager for each research question or hypothesis to keep your analysis outputs well-organized.',
    category: 'Workflow',
    difficulty: 'Intermediate'
  },
  {
    id: 22,
    title: 'Use Ctrl+Shift+S to Save Analysis State',
    content: 'Quickly save your current analysis setup including variable selections and settings with Ctrl+Shift+S, then reload later with Ctrl+Shift+O.',
    category: 'Productivity',
    difficulty: 'Advanced'
  },
  {
    id: 23,
    title: 'Check Regression Assumptions with Residual Plots',
    content: 'Always examine residual plots after regression analysis to verify linearity, homoscedasticity, and normality assumptions are met.',
    category: 'Statistics',
    difficulty: 'Advanced'
  },
  {
    id: 24,
    title: 'Create Box Plots to Identify Outliers',
    content: 'Use box plots to visually identify outliers before running statistical tests. Consider whether outliers should be removed or transformed.',
    category: 'Visualization',
    difficulty: 'Beginner'
  },
  {
    id: 25,
    title: 'Import Excel Files with Multiple Sheets',
    content: 'When importing Excel files, use the sheet selector to choose specific worksheets. DataStatPro can handle complex Excel formats with merged cells.',
    category: 'Data Import',
    difficulty: 'Intermediate'
  },
  {
    id: 26,
    title: 'Handle Missing Data Systematically',
    content: 'Define custom missing value codes in Variable Editor (e.g., -999, "N/A"). DataStatPro will automatically exclude these from calculations.',
    category: 'Data Management',
    difficulty: 'Intermediate'
  },
  {
    id: 27,
    title: 'Use Pivot Tables for Quick Cross-Tabulations',
    content: 'Access Pivot Analysis for interactive cross-tabulations and summary statistics. Drag variables to rows, columns, and values areas for instant insights.',
    category: 'Analysis',
    difficulty: 'Beginner'
  },
  {
    id: 28,
    title: 'Export Publication Tables as Word Documents',
    content: 'Use the Publication Tools to export formatted statistical tables directly to Word documents with proper APA formatting and table numbers.',
    category: 'Publication',
    difficulty: 'Beginner'
  },
  {
    id: 29,
    title: 'Troubleshoot Import Issues with Data Preview',
    content: 'If imported data looks incorrect, use the Data Preview feature to check encoding, delimiters, and header detection before finalizing the import.',
    category: 'Troubleshooting',
    difficulty: 'Beginner'
  },
  {
    id: 30,
    title: 'Use Repeated Measures ANOVA for Longitudinal Data',
    content: 'For data with multiple time points per participant, use Repeated Measures ANOVA instead of regular ANOVA to account for within-subject correlations.',
    category: 'Statistics',
    difficulty: 'Advanced'
  },
  {
    id: 31,
    title: 'Create Composite Scores from Multiple Variables',
    content: 'Use the Variable Editor\'s compute function to create composite scores by averaging or summing related items (e.g., scale scores from questionnaire items).',
    category: 'Data Management',
    difficulty: 'Advanced'
  },
  {
    id: 32,
    title: 'Use Alt+Tab to Switch Between Analysis Windows',
    content: 'Quickly switch between multiple open analysis windows using Alt+Tab, or use Ctrl+W to close the current analysis tab.',
    category: 'Productivity',
    difficulty: 'Beginner'
  },
  {
    id: 33,
    title: 'Validate Results with Alternative Methods',
    content: 'Cross-validate important findings using different statistical approaches (e.g., parametric vs. non-parametric tests) to ensure robust conclusions.',
    category: 'Statistics',
    difficulty: 'Advanced'
  }];

const TipOfTheDayPage: React.FC = () => {
  const theme = useTheme();
  const [currentTipIndex, setCurrentTipIndex] = useState(0);
  const [dailyTip, setDailyTip] = useState<Tip | null>(null);

  // Get today's tip based on date
  useEffect(() => {
    const today = new Date();
    const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / 86400000);
    const tipIndex = dayOfYear % tips.length;
    setDailyTip(tips[tipIndex]);
    setCurrentTipIndex(tipIndex);
  }, []);

  const nextTip = () => {
    setCurrentTipIndex((prev) => (prev + 1) % tips.length);
  };

  const prevTip = () => {
    setCurrentTipIndex((prev) => (prev - 1 + tips.length) % tips.length);
  };

  const currentTip = tips[currentTipIndex];
  const isDailyTip = dailyTip && currentTip.id === dailyTip.id;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return theme.palette.success.main;
      case 'Intermediate': return theme.palette.warning.main;
      case 'Advanced': return theme.palette.error.main;
      default: return theme.palette.primary.main;
    }
  };

  return (
    <>
      <Helmet>
        <title>Tip of the Day - DataStatPro</title>
        <meta name="description" content="Daily tips and tricks to improve your data analysis workflow in DataStatPro." />
      </Helmet>
      
      <Box sx={{ p: 3, maxWidth: 800, margin: '0 auto' }}>
        <PageTitle title="Tip of the Day" />
        
        {/* Today's Featured Tip */}
        {dailyTip && (
          <Paper
            sx={{
              p: 3,
              mb: 4,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
              border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              borderRadius: 2
            }}
            elevation={0}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <StarIcon sx={{ color: theme.palette.warning.main, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold" color="primary">
                Today's Featured Tip
              </Typography>
            </Box>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              {dailyTip.title}
            </Typography>
            <Typography variant="body1" paragraph>
              {dailyTip.content}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip 
                label={dailyTip.category} 
                size="small" 
                color="primary" 
                variant="outlined" 
              />
              <Chip 
                label={dailyTip.difficulty} 
                size="small" 
                sx={{ 
                  backgroundColor: alpha(getDifficultyColor(dailyTip.difficulty), 0.1),
                  color: getDifficultyColor(dailyTip.difficulty),
                  border: `1px solid ${alpha(getDifficultyColor(dailyTip.difficulty), 0.3)}`
                }}
              />
            </Box>
          </Paper>
        )}

        {/* Browse All Tips */}
        <Paper sx={{ p: 3, borderRadius: 2 }} elevation={0} variant="outlined">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LightbulbIcon sx={{ color: theme.palette.warning.main, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold">
                Browse All Tips ({currentTipIndex + 1} of {tips.length})
              </Typography>
            </Box>
            <Box>
              <IconButton onClick={prevTip} disabled={tips.length <= 1}>
                <PrevIcon />
              </IconButton>
              <IconButton onClick={nextTip} disabled={tips.length <= 1}>
                <NextIcon />
              </IconButton>
            </Box>
          </Box>

          <Card 
            sx={{ 
              mb: 2,
              ...(isDailyTip && {
                border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                backgroundColor: alpha(theme.palette.primary.main, 0.02)
              })
            }}
            elevation={isDailyTip ? 2 : 0}
            variant={isDailyTip ? "elevation" : "outlined"}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  {currentTip.title}
                </Typography>
                {isDailyTip && (
                  <Chip 
                    label="Today's Tip" 
                    size="small" 
                    color="primary" 
                    icon={<StarIcon />}
                  />
                )}
              </Box>
              <Typography variant="body1" paragraph>
                {currentTip.content}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip 
                  label={currentTip.category} 
                  size="small" 
                  color="primary" 
                  variant="outlined" 
                />
                <Chip 
                  label={currentTip.difficulty} 
                  size="small" 
                  sx={{ 
                    backgroundColor: alpha(getDifficultyColor(currentTip.difficulty), 0.1),
                    color: getDifficultyColor(currentTip.difficulty),
                    border: `1px solid ${alpha(getDifficultyColor(currentTip.difficulty), 0.3)}`
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          <Divider sx={{ my: 2 }} />
          
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary" paragraph>
              Tips are automatically rotated daily. Bookmark this page to get fresh insights every day!
            </Typography>
            <Button
              variant="outlined"
              startIcon={<TipsIcon />}
              onClick={() => window.location.reload()}
            >
              Refresh Tips
            </Button>
          </Box>
        </Paper>
      </Box>
    </>
  );
};

export default TipOfTheDayPage;
