import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  IconButton,
  useTheme,
  alpha,
  Chip,
  Divider
} from '@mui/material';
import {
  TipsAndUpdates as TipsIcon,
  NavigateNext as NextIcon,
  NavigateBefore as PrevIcon,
  Lightbulb as LightbulbIcon,
  Star as StarIcon
} from '@mui/icons-material';
import PageTitle from '../components/UI/PageTitle';

interface Tip {
  id: number;
  title: string;
  content: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
}

const tips: Tip[] = [
  {
    id: 1,
    title: 'Use Keyboard Shortcuts for Faster Navigation',
    content: 'Press Ctrl+K to quickly search and navigate to any analysis tool. This saves time when working with multiple datasets.',
    category: 'Navigation',
    difficulty: 'Beginner'
  },
  {
    id: 2,
    title: 'Check Data Distribution Before Analysis',
    content: 'Always run descriptive statistics and normality tests before performing inferential statistics. This helps you choose the right statistical test.',
    category: 'Statistics',
    difficulty: 'Intermediate'
  },
  {
    id: 3,
    title: 'Save Your Work with Results Manager',
    content: 'Use the Results Manager to save and organize your analysis outputs. You can export them later as publication-ready reports.',
    category: 'Workflow',
    difficulty: 'Beginner'
  },
  {
    id: 4,
    title: 'Leverage AI Assistant for Test Selection',
    content: 'When unsure which statistical test to use, describe your research question to the AI Assistant for personalized recommendations.',
    category: 'AI Features',
    difficulty: 'Beginner'
  },
  {
    id: 5,
    title: 'Use Sample Size Calculators Before Data Collection',
    content: 'Calculate required sample sizes before starting your study to ensure adequate statistical power for your analyses.',
    category: 'Study Design',
    difficulty: 'Intermediate'
  },
  {
    id: 6,
    title: 'Create Custom Variable Groups',
    content: 'Group related variables together in the Variable Editor to streamline your analysis workflow and reduce selection time.',
    category: 'Data Management',
    difficulty: 'Advanced'
  },
  {
    id: 7,
    title: 'Export High-Quality Visualizations',
    content: 'Use the export options in charts to save publication-ready figures in PNG, SVG, or PDF formats with custom dimensions.',
    category: 'Visualization',
    difficulty: 'Intermediate'
  },
  {
    id: 8,
    title: 'Filter Data Non-Destructively',
    content: 'Use data filters to create focused analyses without modifying your original dataset. Filters can be saved and reapplied.',
    category: 'Data Management',
    difficulty: 'Intermediate'
  }
];

const TipOfTheDayPage: React.FC = () => {
  const theme = useTheme();
  const [currentTipIndex, setCurrentTipIndex] = useState(0);
  const [dailyTip, setDailyTip] = useState<Tip | null>(null);

  // Get today's tip based on date
  useEffect(() => {
    const today = new Date();
    const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / 86400000);
    const tipIndex = dayOfYear % tips.length;
    setDailyTip(tips[tipIndex]);
    setCurrentTipIndex(tipIndex);
  }, []);

  const nextTip = () => {
    setCurrentTipIndex((prev) => (prev + 1) % tips.length);
  };

  const prevTip = () => {
    setCurrentTipIndex((prev) => (prev - 1 + tips.length) % tips.length);
  };

  const currentTip = tips[currentTipIndex];
  const isDailyTip = dailyTip && currentTip.id === dailyTip.id;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return theme.palette.success.main;
      case 'Intermediate': return theme.palette.warning.main;
      case 'Advanced': return theme.palette.error.main;
      default: return theme.palette.primary.main;
    }
  };

  return (
    <>
      <Helmet>
        <title>Tip of the Day - DataStatPro</title>
        <meta name="description" content="Daily tips and tricks to improve your data analysis workflow in DataStatPro." />
      </Helmet>
      
      <Box sx={{ p: 3, maxWidth: 800, margin: '0 auto' }}>
        <PageTitle title="Tip of the Day" />
        
        {/* Today's Featured Tip */}
        {dailyTip && (
          <Paper
            sx={{
              p: 3,
              mb: 4,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
              border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              borderRadius: 2
            }}
            elevation={0}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <StarIcon sx={{ color: theme.palette.warning.main, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold" color="primary">
                Today's Featured Tip
              </Typography>
            </Box>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              {dailyTip.title}
            </Typography>
            <Typography variant="body1" paragraph>
              {dailyTip.content}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip 
                label={dailyTip.category} 
                size="small" 
                color="primary" 
                variant="outlined" 
              />
              <Chip 
                label={dailyTip.difficulty} 
                size="small" 
                sx={{ 
                  backgroundColor: alpha(getDifficultyColor(dailyTip.difficulty), 0.1),
                  color: getDifficultyColor(dailyTip.difficulty),
                  border: `1px solid ${alpha(getDifficultyColor(dailyTip.difficulty), 0.3)}`
                }}
              />
            </Box>
          </Paper>
        )}

        {/* Browse All Tips */}
        <Paper sx={{ p: 3, borderRadius: 2 }} elevation={0} variant="outlined">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LightbulbIcon sx={{ color: theme.palette.warning.main, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold">
                Browse All Tips ({currentTipIndex + 1} of {tips.length})
              </Typography>
            </Box>
            <Box>
              <IconButton onClick={prevTip} disabled={tips.length <= 1}>
                <PrevIcon />
              </IconButton>
              <IconButton onClick={nextTip} disabled={tips.length <= 1}>
                <NextIcon />
              </IconButton>
            </Box>
          </Box>

          <Card 
            sx={{ 
              mb: 2,
              ...(isDailyTip && {
                border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                backgroundColor: alpha(theme.palette.primary.main, 0.02)
              })
            }}
            elevation={isDailyTip ? 2 : 0}
            variant={isDailyTip ? "elevation" : "outlined"}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  {currentTip.title}
                </Typography>
                {isDailyTip && (
                  <Chip 
                    label="Today's Tip" 
                    size="small" 
                    color="primary" 
                    icon={<StarIcon />}
                  />
                )}
              </Box>
              <Typography variant="body1" paragraph>
                {currentTip.content}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip 
                  label={currentTip.category} 
                  size="small" 
                  color="primary" 
                  variant="outlined" 
                />
                <Chip 
                  label={currentTip.difficulty} 
                  size="small" 
                  sx={{ 
                    backgroundColor: alpha(getDifficultyColor(currentTip.difficulty), 0.1),
                    color: getDifficultyColor(currentTip.difficulty),
                    border: `1px solid ${alpha(getDifficultyColor(currentTip.difficulty), 0.3)}`
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          <Divider sx={{ my: 2 }} />
          
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary" paragraph>
              Tips are automatically rotated daily. Bookmark this page to get fresh insights every day!
            </Typography>
            <Button
              variant="outlined"
              startIcon={<TipsIcon />}
              onClick={() => window.location.reload()}
            >
              Refresh Tips
            </Button>
          </Box>
        </Paper>
      </Box>
    </>
  );
};

export default TipOfTheDayPage;
