import{_ as a,l as o,K as p,d as s,L as n}from"./FlowDiagram-CHRbazj7.js";import{p as m}from"./radar-MK3ICKWK-BCMVXlQA.js";import"./mui-libs-CfwFIaTD.js";import"./react-libs-Cr2nE3UY.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./PublicationReadyGate-BGFbKbJc.js";import"./index-Bpan7Tbe.js";import"./other-utils-CR9xr_gI.js";import"./_baseUniq-DTckOzFM.js";import"./_basePickBy-CvIohq0t.js";import"./clone-B_Po1mvy.js";var g={parse:a(async r=>{const t=await m("info",r);o.debug(t)},"parse")},d={version:n.version},v=a(()=>d.version,"getVersion"),c={getVersion:v},l=a((r,t,i)=>{o.debug(`rendering info diagram
`+r);const e=p(t);s(e,100,400,!0),e.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${i}`)},"draw"),f={draw:l},A={parser:g,db:c,renderer:f};export{A as diagram};
