import{ck as Y,j as e,u as I,R as k,B as a,ce as ae,e as l,g as C,f as S,ah as y,ae as O,K as P,bR as K,I as J,du as ne,ai as oe,b9 as ie,ba as le,bb as U,be as ce,b8 as Q,dv as L,C as H,a6 as de,a7 as G,dw as ue,y as xe}from"./mui-libs-CfwFIaTD.js";import{r as s}from"./react-libs-Cr2nE3UY.js";import{l as V,b as D,u as X}from"./index-Bpan7Tbe.js";import{c as me}from"./countries-BGaMXFfG.js";import{F as he}from"./FeatureComparisonTable-D0Q0fTUa.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";var q={},ge=Y;Object.defineProperty(q,"__esModule",{value:!0});var B=q.default=void 0,pe=ge(V()),fe=e;B=q.default=(0,pe.default)((0,fe.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close");var T={},be=Y;Object.defineProperty(T,"__esModule",{value:!0});var F=T.default=void 0;ye(s);var je=be(V()),ve=e;function Z(t){if(typeof WeakMap!="function")return null;var o=new WeakMap,c=new WeakMap;return(Z=function(i){return i?c:o})(t)}function ye(t,o){if(t&&t.__esModule)return t;if(t===null||typeof t!="object"&&typeof t!="function")return{default:t};var c=Z(o);if(c&&c.has(t))return c.get(t);var i={__proto__:null},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in t)if(r!=="default"&&Object.prototype.hasOwnProperty.call(t,r)){var x=u?Object.getOwnPropertyDescriptor(t,r):null;x&&(x.get||x.set)?Object.defineProperty(i,r,x):i[r]=t[r]}return i.default=t,c&&c.set(t,i),i}F=T.default=(0,je.default)((0,ve.jsx)("path",{d:"M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"}),"Google");const Se=({onRegisterClick:t,onResetPasswordClick:o,onGuestLoginClick:c})=>{I();const{signIn:i,signInWithGoogle:u}=D(),[r,x]=s.useState(""),[d,m]=s.useState(""),[h,v]=s.useState(!1),[b,p]=s.useState(null),[W,_]=s.useState(!1);X();const f=async j=>{j.preventDefault(),p(null),v(!0);try{const{error:g}=await i(r,d);if(g)throw g;_(!0)}catch(g){p(g.message||"Failed to sign in")}finally{v(!1)}},w=(j,g)=>{g!=="clickaway"&&_(!1)};return e.jsxs(k,{elevation:3,sx:{p:4,maxWidth:400,mx:"auto",mt:4},children:[e.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",mb:3,children:[e.jsx(ae,{fontSize:"large",color:"primary",sx:{mb:1}}),e.jsx(l,{variant:"h5",component:"h1",gutterBottom:!0,children:"Sign in to DataStatPro"})]}),b&&e.jsx(C,{severity:"error",sx:{mb:3},children:b}),e.jsx(S,{fullWidth:!0,variant:"contained",color:"secondary",size:"large",sx:{mt:1,mb:2},onClick:u,disabled:h,startIcon:e.jsx(F,{}),children:"Sign In with Google"}),e.jsxs(a,{sx:{display:"flex",alignItems:"center",my:2},children:[e.jsx(a,{sx:{flexGrow:1,height:"1px",bgcolor:"divider"}}),e.jsx(l,{variant:"body2",sx:{mx:2,color:"text.secondary"},children:"OR"}),e.jsx(a,{sx:{flexGrow:1,height:"1px",bgcolor:"divider"}})]}),e.jsxs(a,{component:"form",onSubmit:f,children:[e.jsx(y,{label:"Email Address",type:"email",fullWidth:!0,margin:"normal",required:!0,value:r,onChange:j=>x(j.target.value),disabled:h}),e.jsx(y,{label:"Password",type:"password",fullWidth:!0,margin:"normal",required:!0,value:d,onChange:j=>m(j.target.value),disabled:h}),e.jsx(S,{type:"submit",fullWidth:!0,variant:"contained",color:"primary",size:"large",sx:{mt:3,mb:2},disabled:h,disableElevation:!0,children:h?e.jsx(O,{size:24}):"Sign In"}),e.jsx(a,{sx:{textAlign:"center",mt:2,mb:2},children:e.jsxs(l,{variant:"body2",children:["Don't want to create an account?",e.jsx(P,{component:"button",variant:"body2",onClick:c,underline:"hover",sx:{ml:1},children:"Continue as guest"})]})})]}),e.jsxs(a,{display:"flex",justifyContent:"space-between",mt:1,children:[e.jsx(P,{component:"button",variant:"body2",onClick:o,underline:"hover",children:"Forgot password?"}),e.jsx(P,{component:"button",variant:"body2",onClick:t,underline:"hover",children:"Don't have an account? Sign up"})]}),e.jsx(K,{open:W,autoHideDuration:4e3,onClose:w,message:"You are successfully logged in to DataStatPro app",action:e.jsx(J,{size:"small","aria-label":"close",color:"inherit",onClick:w,children:e.jsx(B,{fontSize:"small"})})})]})},Ce=({onLoginClick:t})=>{I();const{signUp:o,signInWithGoogle:c}=D(),[i,u]=s.useState(""),[r,x]=s.useState(""),[d,m]=s.useState(""),[h,v]=s.useState(""),[b,p]=s.useState(""),[W,_]=s.useState(""),[f,w]=s.useState(!1),[j,g]=s.useState(null),[M,A]=s.useState(!1),[te,N]=s.useState(!1),se=X(),re=async n=>{if(n.preventDefault(),g(null),r!==d){g("Passwords do not match");return}if(r.length<6){g("Password must be at least 6 characters");return}w(!0);const E={full_name:h,institution:b,country:W};try{const{error:R,user:z}=await o(i,r,{data:E});if(R)throw R;z&&(!z.identities||z.identities.length===0)?(g("An account with this email already exists"),A(!1)):(A(!0),N(!0),setTimeout(()=>{se("/dashboard")},2e3))}catch(R){g(R.message||"Failed to sign up"),A(!1)}finally{w(!1)}},$=(n,E)=>{E!=="clickaway"&&N(!1)};return e.jsxs(k,{elevation:3,sx:{p:4,maxWidth:400,mx:"auto",mt:4},children:[e.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",mb:3,children:[e.jsx(ne,{fontSize:"large",color:"primary",sx:{mb:1}}),e.jsx(l,{variant:"h5",component:"h1",gutterBottom:!0,children:"Create an Account"})]}),j&&e.jsx(C,{severity:"error",sx:{mb:3},children:j}),M&&e.jsx(C,{severity:"success",sx:{mb:3},children:"Registration successful! Please check your email to confirm your account."}),!M&&e.jsxs(e.Fragment,{children:[e.jsx(S,{fullWidth:!0,variant:"contained",color:"secondary",size:"large",sx:{mt:1,mb:2},onClick:c,disabled:f,startIcon:e.jsx(F,{}),children:"Sign Up with Google"}),e.jsxs(a,{sx:{display:"flex",alignItems:"center",my:2},children:[e.jsx(a,{sx:{flexGrow:1,height:"1px",bgcolor:"divider"}}),e.jsx(l,{variant:"body2",sx:{mx:2,color:"text.secondary"},children:"OR"}),e.jsx(a,{sx:{flexGrow:1,height:"1px",bgcolor:"divider"}})]}),e.jsxs(a,{component:"form",onSubmit:re,children:[e.jsx(y,{label:"Email Address",type:"email",fullWidth:!0,margin:"normal",required:!0,value:i,onChange:n=>u(n.target.value),disabled:f}),e.jsx(y,{label:"Full Name",type:"text",fullWidth:!0,margin:"normal",required:!0,value:h,onChange:n=>v(n.target.value),disabled:f}),e.jsx(y,{label:"Password",type:"password",fullWidth:!0,margin:"normal",required:!0,value:r,onChange:n=>x(n.target.value),disabled:f,helperText:"Password must be at least 6 characters"}),e.jsx(y,{label:"Confirm Password",type:"password",fullWidth:!0,margin:"normal",required:!0,value:d,onChange:n=>m(n.target.value),disabled:f}),e.jsx(y,{label:"Institution",type:"text",fullWidth:!0,margin:"normal",value:b,onChange:n=>p(n.target.value),disabled:f}),e.jsxs(oe,{fullWidth:!0,margin:"normal",children:[e.jsx(ie,{id:"country-select-label",children:"Country"}),e.jsxs(le,{labelId:"country-select-label",id:"country-select",value:W,label:"Country",onChange:n=>_(n.target.value),disabled:f,children:[e.jsx(U,{value:"",children:e.jsx("em",{children:"Select a country"})}),me.map(n=>e.jsx(U,{value:n,children:n},n))]}),e.jsx(ce,{children:"Select your country from the list"})]}),e.jsx(S,{type:"submit",fullWidth:!0,variant:"contained",color:"primary",size:"large",sx:{mt:3,mb:2},disabled:f,children:f?e.jsx(O,{size:24}):"Sign Up"})]})]}),e.jsxs(a,{sx:{mt:4,mb:3},children:[e.jsx(l,{variant:"h6",textAlign:"center",gutterBottom:!0,children:"What You Get With Your Account"}),e.jsx(he,{compact:!0,showDescriptions:!1})]}),e.jsx(a,{display:"flex",justifyContent:"center",mt:2,children:e.jsx(P,{component:"button",variant:"body2",onClick:t,underline:"hover",children:"Already have an account? Sign in"})}),e.jsx(K,{open:te,autoHideDuration:2e3,onClose:$,message:"Registration successful! Redirecting to dashboard...",action:e.jsx(J,{size:"small","aria-label":"close",color:"inherit",onClick:$,children:e.jsx(B,{fontSize:"small"})})})]})},we=({onLoginClick:t})=>{I();const{resetPassword:o}=D(),[c,i]=s.useState(""),[u,r]=s.useState(!1),[x,d]=s.useState(null),[m,h]=s.useState(!1),v=async b=>{b.preventDefault(),d(null),r(!0);try{const{error:p}=await o(c);if(p)throw p;h(!0)}catch(p){d(p.message||"Failed to send reset password email")}finally{r(!1)}};return e.jsxs(k,{elevation:3,sx:{p:4,maxWidth:400,mx:"auto",mt:4},children:[e.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",mb:3,children:[e.jsx(Q,{fontSize:"large",color:"primary",sx:{mb:1}}),e.jsx(l,{variant:"h5",component:"h1",gutterBottom:!0,children:"Reset Password"})]}),x&&e.jsx(C,{severity:"error",sx:{mb:3},children:x}),m?e.jsxs(a,{children:[e.jsx(C,{severity:"success",sx:{mb:3},children:"Password reset instructions have been sent to your email."}),e.jsx(S,{fullWidth:!0,variant:"contained",color:"primary",onClick:t,sx:{mt:2},children:"Back to Login"})]}):e.jsxs(a,{component:"form",onSubmit:v,children:[e.jsx(l,{variant:"body2",color:"textSecondary",paragraph:!0,children:"Enter your email address and we'll send you instructions to reset your password."}),e.jsx(y,{label:"Email Address",type:"email",fullWidth:!0,margin:"normal",required:!0,value:c,onChange:b=>i(b.target.value),disabled:u}),e.jsx(S,{type:"submit",fullWidth:!0,variant:"contained",color:"primary",size:"large",sx:{mt:3,mb:2},disabled:u,children:u?e.jsx(O,{size:24}):"Send Reset Instructions"}),e.jsx(a,{display:"flex",justifyContent:"center",mt:2,children:e.jsx(P,{component:"button",variant:"body2",onClick:t,underline:"hover",children:"Back to Login"})})]})]})},Pe=({onGuestLoginClick:t})=>{const o=I();return e.jsxs(k,{elevation:3,sx:{p:4,maxWidth:400,mx:"auto",mt:4},children:[e.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",mb:3,children:[e.jsx(L,{fontSize:"large",color:"secondary",sx:{mb:1}}),e.jsx(l,{variant:"h5",component:"h1",gutterBottom:!0,children:"Guest Access"})]}),e.jsxs(C,{severity:"info",sx:{mb:3,border:`1px solid ${o.palette.info.main}`,"& .MuiAlert-message":{width:"100%"}},children:[e.jsx(l,{variant:"subtitle1",fontWeight:"bold",gutterBottom:!0,children:"Guest Access Information"}),e.jsx(l,{variant:"body2",paragraph:!0,children:"Guest users may explore the application with all features but are limited to Sample Datasets for teaching and learning purposes."}),e.jsxs(l,{variant:"body2",component:"ul",sx:{pl:2,m:0},children:[e.jsx("li",{children:"Access to all statistical analysis features"}),e.jsx("li",{children:"Limited to pre-loaded sample datasets only"}),e.jsx("li",{children:"No data saving or custom data importing"}),e.jsx("li",{children:"Perfect for learning and evaluation"})]})]}),e.jsx(l,{variant:"body2",paragraph:!0,textAlign:"center",sx:{mb:3},children:"No registration required. Start exploring DataStatPro immediately."}),e.jsx(S,{fullWidth:!0,variant:"contained",color:"secondary",size:"large",startIcon:e.jsx(L,{}),onClick:t,sx:{py:1.5,fontSize:"1.1rem",fontWeight:"medium"},children:"Continue as Guest"})]})};var ee=(t=>(t.LOGIN="login",t.REGISTER="register",t.RESET_PASSWORD="reset_password",t.GUEST_ACCESS="guest_access",t))(ee||{});const Ie=({initialView:t="login",onAuthSuccess:o,onGuestLoginSuccess:c})=>{const i=I(),{user:u,isGuest:r,loginAsGuest:x}=D(),[d,m]=s.useState(t);s.useEffect(()=>{(u||r)&&o&&o()},[u,r,o]);const h=(b,p)=>{m(p)},v=()=>{x()};return u||r?e.jsx(H,{maxWidth:"sm",sx:{mt:4,mb:4,textAlign:"center"},children:e.jsx(l,{variant:"h6",children:"Redirecting..."})}):e.jsx(H,{maxWidth:"sm",sx:{mt:4,mb:4},children:e.jsxs(k,{elevation:3,sx:{borderRadius:2,overflow:"hidden",boxShadow:"0 4px 20px rgba(0,0,0,0.1)"},children:[e.jsxs(a,{sx:{p:2,bgcolor:i.palette.primary.main,color:"white",textAlign:"center"},children:[e.jsx(l,{variant:"h5",component:"h1",children:"DataStatPro"}),e.jsx(l,{variant:"subtitle2",children:"Statistical Analysis Platform"})]}),e.jsxs(de,{value:d,onChange:h,variant:"fullWidth",indicatorColor:"primary",textColor:"primary",sx:{borderBottom:1,borderColor:"divider"},children:[e.jsx(G,{icon:e.jsx(ue,{}),label:"Sign In",value:"login",iconPosition:"start"}),e.jsx(G,{icon:e.jsx(xe,{}),label:"Register",value:"register",iconPosition:"start"}),e.jsx(G,{icon:e.jsx(L,{}),label:"Guest Access",value:"guest_access",iconPosition:"start"}),e.jsx(G,{icon:e.jsx(Q,{}),label:"Reset",value:"reset_password",iconPosition:"start"})]}),e.jsxs(a,{sx:{p:3},children:[d==="login"&&e.jsx(Se,{onRegisterClick:()=>m("register"),onResetPasswordClick:()=>m("reset_password"),onGuestLoginClick:()=>m("guest_access")}),d==="register"&&e.jsx(Ce,{onLoginClick:()=>m("login")}),d==="guest_access"&&e.jsx(Pe,{onGuestLoginClick:v}),d==="reset_password"&&e.jsx(we,{onLoginClick:()=>m("login")})]})]})})},Oe=({initialView:t=ee.LOGIN,onAuthSuccess:o})=>e.jsx(Ie,{initialView:t,onAuthSuccess:o});export{ee as AuthView,Oe as default};
