import{u as A,a as I,j as e,B as n,e as o,k as z,l as E,I as P,aC as T,aD as S,a3 as W,G as j,i as l,ao as U,R as B,ap as _,aq as k,ar as g,as as d,aE as p,aF as R,at as D,o as G,q as L,h as M}from"./mui-libs-CfwFIaTD.js";import{r as q}from"./react-libs-Cr2nE3UY.js";const m=[{id:"guest",name:"Guest",color:"#4caf50"},{id:"standard",name:"Standard",color:"#2196f3",badge:"Free"},{id:"pro",name:"Pro",color:"#ff9800",badge:"Premium"},{id:"educational",name:"Educational",color:"#9c27b0",badge:"Free"},{id:"educational_pro",name:"Educational Pro",color:"#673ab7",badge:"Premium"}],y=[{name:"Sample Datasets",description:"Access to built-in sample datasets for learning and practice",features:{guest:{access:!0,note:"Full access to all sample datasets"},standard:{access:!0,note:"Full access to all sample datasets"},pro:{access:!0,note:"Full access to all sample datasets"},educational:{access:!0,note:"Full access to all sample datasets"},educational_pro:{access:!0,note:"Full access to all sample datasets"}}},{name:"Imported Datasets",description:"Upload and analyze your own data files (CSV, Excel, etc.)",features:{guest:{access:!1,note:"Cannot upload personal data"},standard:{access:!0,note:"Upload and analyze your own data"},pro:{access:!0,note:"Upload and analyze your own data"},educational:{access:!0,note:"Upload and analyze your own data"},educational_pro:{access:!0,note:"Upload and analyze your own data"}}},{name:"Cloud Datasets",description:"Store datasets in the cloud for access from any device",features:{guest:{access:!1,note:"No cloud storage available"},standard:{access:!1,note:"Local storage only"},pro:{access:!0,note:"Up to 2 datasets in cloud storage"},educational:{access:!1,note:"Local storage only"},educational_pro:{access:!0,note:"Up to 2 datasets in cloud storage"}}},{name:"Advanced Features",description:"Advanced statistical methods, ANOVA, regression, and more",features:{guest:{access:!0,note:"Preview only with sample data"},standard:{access:!1,note:"Basic analysis only"},pro:{access:!0,note:"Full access to all advanced features"},educational:{access:!0,note:"Free access for .edu users"},educational_pro:{access:!0,note:"Full access to all advanced features"}}},{name:"Publication Features",description:"APA tables, methods text generation, publication-ready outputs",features:{guest:{access:!0,note:"Preview only with sample data"},standard:{access:!1,note:"Not available"},pro:{access:!0,note:"Full publication-ready tools"},educational:{access:!1,note:"Upgrade to Educational Pro"},educational_pro:{access:!0,note:"Full publication-ready tools"}}}],H=({compact:i=!1,showDescriptions:u=!0,highlightTier:x})=>{const c=A(),C=I(c.breakpoints.down("md")),[h,F]=q.useState([]),v=a=>{F(s=>s.includes(a)?s.filter(t=>t!==a):[...s,a])},f=(a,s)=>a?e.jsx(p,{title:s||"Full access",children:e.jsx(G,{sx:{color:"success.main",fontSize:i?20:24}})}):e.jsx(p,{title:s||"No access",children:e.jsx(L,{sx:{color:"error.main",fontSize:i?20:24}})}),w=a=>e.jsxs(n,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:.5},children:[e.jsx(o,{variant:i?"body2":"subtitle1",sx:{fontWeight:"bold",color:a.color,textAlign:"center"},children:a.name}),a.badge&&e.jsx(M,{label:a.badge,size:"small",sx:{bgcolor:l(a.color,.1),color:a.color,fontSize:"0.7rem",height:20}})]});return C?e.jsxs(n,{sx:{width:"100%"},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,sx:{textAlign:"center",mb:3},children:"Feature Comparison"}),y.map(a=>e.jsx(z,{sx:{mb:2},children:e.jsxs(E,{sx:{p:2},children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",cursor:"pointer"},onClick:()=>v(a.name),children:[e.jsx(o,{variant:"subtitle1",sx:{fontWeight:"bold"},children:a.name}),e.jsx(P,{size:"small",children:h.includes(a.name)?e.jsx(T,{}):e.jsx(S,{})})]}),u&&e.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1,mb:2},children:a.description}),e.jsx(W,{in:h.includes(a.name),children:e.jsx(j,{container:!0,spacing:1,sx:{mt:1},children:m.map(s=>{var t,r;return e.jsx(j,{item:!0,xs:6,sm:4,children:e.jsxs(n,{sx:{p:1,border:1,borderColor:"divider",borderRadius:1,textAlign:"center",bgcolor:x===s.id?l(s.color,.05):"transparent"},children:[e.jsx(o,{variant:"caption",sx:{color:s.color,fontWeight:"bold"},children:s.name}),e.jsx(n,{sx:{mt:.5},children:f(((t=a.features[s.id])==null?void 0:t.access)||!1,(r=a.features[s.id])==null?void 0:r.note)})]})},s.id)})})})]})},a.name))]}):e.jsxs(n,{sx:{width:"100%",overflowX:"auto"},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,sx:{textAlign:"center",mb:3},children:"Feature Comparison"}),e.jsx(U,{component:B,elevation:2,children:e.jsxs(_,{sx:{minWidth:650},children:[e.jsx(k,{children:e.jsxs(g,{sx:{bgcolor:l(c.palette.primary.main,.05)},children:[e.jsxs(d,{sx:{fontWeight:"bold",minWidth:200},children:["Feature Category",u&&e.jsx(p,{title:"Click on feature names for detailed descriptions",children:e.jsx(R,{sx:{ml:1,fontSize:16,color:"text.secondary"}})})]}),m.map(a=>e.jsx(d,{align:"center",sx:{fontWeight:"bold",minWidth:120,bgcolor:x===a.id?l(a.color,.1):"transparent"},children:w(a)},a.id))]})}),e.jsx(D,{children:y.map((a,s)=>e.jsxs(g,{sx:{"&:nth-of-type(odd)":{bgcolor:l(c.palette.grey[100],.5)},"&:hover":{bgcolor:l(c.palette.primary.main,.02)}},children:[e.jsx(d,{children:e.jsxs(n,{children:[e.jsx(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:a.name}),u&&e.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:.5},children:a.description})]})}),m.map(t=>{var r,b;return e.jsx(d,{align:"center",sx:{bgcolor:x===t.id?l(t.color,.05):"transparent"},children:f(((r=a.features[t.id])==null?void 0:r.access)||!1,(b=a.features[t.id])==null?void 0:b.note)},t.id)})]},a.name))})]})})]})};export{H as F};
