import{u as Le,j as e,B as c,e as s,G as Q,R as $,a6 as Ee,aE as q,a7 as xe,c7 as Pe,aW as Oe,F as ge,ai as k,b9 as D,ba as L,bb as y,bc as Z,a_ as me,aj as S,ak as A,f as Re,ae as be,W as fe,I as pe,b7 as Te,bH as We,ah as ee,c8 as Ge,bN as je}from"./mui-libs-CfwFIaTD.js";import{r as g,b as ae}from"./react-libs-Cr2nE3UY.js";import{a as Fe,D as ve,h as _e,g as $e}from"./index-Bpan7Tbe.js";import{b as qe}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{l as H,P as He}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./math-lib-BOZ-XUok.js";import"./charts-recharts-d3-BEF1Y_jn.js";const te={default:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],pastel:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd"],bold:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],sequential:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],diverging:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"]},re={title:"Bar Chart",xAxisLabel:"Categories",yAxisLabel:"Values",showValuesOnBars:!0,barMode:"group",orientation:"v",colorScheme:"default",legendPosition:{y:-.2,x:.5,xanchor:"center",yanchor:"top"},barGap:.2,sortBars:"none",maxBars:15},le="plotlyBarChartDiv",ra=()=>{const{datasets:G,currentDataset:C}=Fe(),i=Le(),V=g.useRef(null),[E,ne]=g.useState((C==null?void 0:C.id)||""),[m,K]=g.useState(""),[h,N]=g.useState([]),[P,ye]=g.useState("sum"),[M,Ce]=g.useState("frequency"),[t,se]=g.useState(re),[B,U]=g.useState("variables"),[j,O]=g.useState(null),[R,oe]=g.useState(!1),[ie,z]=g.useState(null),d=ae.useMemo(()=>E&&G.find(a=>a.id===E)||null,[G,E]),de=ae.useMemo(()=>(d==null?void 0:d.columns.filter(a=>a.type===ve.CATEGORICAL))||[],[d]),F=ae.useMemo(()=>(d==null?void 0:d.columns.filter(a=>a.type===ve.NUMERIC))||[],[d]);g.useEffect(()=>{C!=null&&C.id&&C.id!==E&&(ne(C.id),K(""),N([]),O(null),z(null))},[C]),g.useEffect(()=>{if(j&&V.current){const a={responsive:!0,displayModeBar:!0,displaylogo:!1,modeBarButtonsToRemove:["pan2d","lasso2d","select2d","autoScale2d"]},r={...j.layout,height:500,width:void 0,autosize:!0};H.newPlot(le,j.data,r,a)}return()=>{V.current&&typeof He<"u"&&H.purge&&H.purge(V.current)}},[j]),g.useEffect(()=>{m&&d&&ce()},[m,h,P,M,t,d]),g.useEffect(()=>{const a=r=>{r.altKey&&!r.ctrlKey&&!r.shiftKey&&(r.key==="1"?(r.preventDefault(),U("variables")):r.key==="2"&&(r.preventDefault(),U("settings")))};return window.addEventListener("keydown",a),()=>window.removeEventListener("keydown",a)},[]);const Se=a=>{ne(a.target.value),K(""),N([]),O(null),z(null)},Be=a=>{K(a.target.value)},we=a=>{const r=a.target.value;N(typeof r=="string"?r.split(","):r)},ke=a=>{ye(a.target.value)},Ve=a=>{Ce(a.target.value)},v=(a,r)=>{a==="barMode"&&(r=r),a==="orientation"&&(r=r),a==="colorScheme"&&(r=r),a==="sortBars"&&(r=r),se(w=>({...w,[a]:r}))},ce=()=>{if(!d||!m){z("Please select a dataset and a category variable.");return}oe(!0),z(null),O(null);try{const a=d.columns.find(l=>l.id===m);if(!a)throw new Error("Category column not found.");let r=[],w={},b=[],I="",T=re.yAxisLabel;if(h.length===0){const l=_e(m,null,d,"count"),n=d.data.length;if(n===0)throw new Error("No data for category variable.");b=l.map(({category:f,value:o})=>({category:f,value:M==="percentage"?o/n*100:o})),T=M==="percentage"?"Percentage (%)":"Frequency",I="value",w.showlegend=!1}else{const l=h.map(o=>d.columns.find(u=>u.id===o)).filter(o=>!!o);if(l.length!==h.length)throw new Error("One or more value columns not found.");const n={};d.data.forEach(o=>{const u=String(o[a.name]);n[u]||(n[u]={},l.forEach(x=>{n[u][x.name]=[]})),l.forEach(x=>{const p=o[x.name];typeof p=="number"&&!isNaN(p)&&n[u][x.name].push(p)})}),b=$e(m,d).map(o=>{const u={category:o};return l.forEach(x=>{var he;const p=((he=n[o])==null?void 0:he[x.name])||[];if(p.length===0)u[x.name]=0;else switch(P){case"sum":u[x.name]=p.reduce((X,J)=>X+J,0);break;case"average":u[x.name]=qe(p);break;case"count":u[x.name]=p.length;break;case"max":u[x.name]=Math.max(...p);break;case"min":u[x.name]=Math.min(...p);break;default:u[x.name]=p.reduce((X,J)=>X+J,0)}}),u}),h.length===1?I=l[0].name:I="",T=h.length===1?`${P.charAt(0).toUpperCase()+P.slice(1)} of ${l[0].name}`:"Aggregated Value",w.showlegend=!0}t.sortBars!=="none"&&I&&b.sort((l,n)=>{const f=l[I]??0,o=n[I]??0;return t.sortBars==="ascending"?f-o:o-f}),b.length>t.maxBars&&(b=b.slice(0,t.maxBars));const _=b.map(l=>l.category),Y=ze(),ue=l=>{const n=Math.max(...l);return M==="percentage"?".1f":n<1?".2f":n<100?".1f":".0f"};if(h.length===0){const l=b.map(f=>f.value),n=ue(l);r.push({x:t.orientation==="v"?_:l,y:t.orientation==="v"?l:_,type:"bar",name:T,orientation:t.orientation,marker:{color:Y[0]},text:t.showValuesOnBars?l:void 0,texttemplate:t.showValuesOnBars?`%{text:${n}}${M==="percentage"?"%":""}`:void 0,textposition:"auto",hoverinfo:"x+y+name"})}else h.map(n=>F.find(f=>f.id===n)).filter(n=>!!n).forEach((n,f)=>{const o=b.map(x=>x[n.name]),u=ue(o);r.push({x:t.orientation==="v"?_:o,y:t.orientation==="v"?o:_,type:"bar",name:n.name,orientation:t.orientation,marker:{color:Y[f%Y.length]},text:t.showValuesOnBars?o:void 0,texttemplate:t.showValuesOnBars?`%{text:${u}}`:void 0,textposition:"auto",hoverinfo:"x+y+name"})});const W={title:{text:t.title,font:{size:16}},xaxis:{title:{text:t.orientation==="v"?t.xAxisLabel:T},automargin:!0},yaxis:{title:{text:t.orientation==="v"?T:t.xAxisLabel},automargin:!0},barmode:h.length>1?t.barMode:void 0,bargap:t.barGap,margin:{t:50,b:50,l:70,r:30},paper_bgcolor:i.palette.mode==="dark"?"#333":"#fff",plot_bgcolor:i.palette.mode==="dark"?"#222":"#fff",font:{color:i.palette.text.primary},legend:{...t.legendPosition,bgcolor:i.palette.mode==="dark"?"rgba(51,51,51,0.8)":"rgba(255,255,255,0.8)",bordercolor:i.palette.divider,borderwidth:1,orientation:"h"},autosize:!0,...w};t.orientation==="v"?W.xaxis={...W.xaxis,type:"category"}:W.yaxis={...W.yaxis,type:"category"},O({data:r,layout:W})}catch(a){z(`Error generating chart data: ${a instanceof Error?a.message:String(a)}`),O(null)}finally{oe(!1)}},Ae=()=>{se(re)},Me=()=>{if(V.current&&j){const a={format:"svg",filename:t.title.replace(/\s+/g,"_")||"barchart",width:V.current.offsetWidth||800,height:V.current.offsetHeight||500};H.downloadImage(le,a)}else z("Chart data not available for download.")},ze=()=>te[t.colorScheme]||te.default,Ie=!m||R,De=!j||R;return e.jsxs(c,{p:3,children:[e.jsx(s,{variant:"h5",gutterBottom:!0,children:"Bar Chart Generator"}),e.jsxs(Q,{container:!0,spacing:2,children:[e.jsxs(Q,{item:!0,xs:12,sm:12,md:3,lg:3,children:[e.jsx($,{elevation:1,sx:{mb:1,backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.02)"},children:e.jsxs(Ee,{value:B,onChange:(a,r)=>U(r),variant:"fullWidth",sx:{minHeight:44,"& .MuiTab-root":{minHeight:44,fontSize:"0.875rem",fontWeight:500,color:i.palette.text.secondary,textTransform:"none",transition:"all 0.2s ease-in-out","&.Mui-selected":{color:i.palette.primary.main,backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.08)":"rgba(25, 118, 210, 0.08)"},"&:hover":{backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.04)":"rgba(0, 0, 0, 0.04)"}},"& .MuiTabs-indicator":{height:3,borderRadius:"3px 3px 0 0"}},children:[e.jsx(q,{title:"Variable Selection Panel",placement:"top",children:e.jsx(xe,{value:"variables",label:"Variables",icon:e.jsx(Pe,{fontSize:"small"}),iconPosition:"start"})}),e.jsx(q,{title:"Chart Settings Panel",placement:"top",children:e.jsx(xe,{value:"settings",label:"Settings",icon:e.jsx(Oe,{fontSize:"small"}),iconPosition:"start"})})]})}),e.jsx(ge,{in:B==="variables",timeout:300,children:e.jsx(c,{sx:{display:B==="variables"?"block":"none"},children:e.jsxs($,{elevation:2,sx:{p:2,height:"fit-content"},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Variable Selection"}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(D,{id:"dataset-select-label",children:"Dataset"}),e.jsx(L,{labelId:"dataset-select-label",value:E,label:"Dataset",onChange:Se,disabled:G.length===0,children:G.map(a=>e.jsxs(y,{value:a.id,children:[a.name," (",a.data.length," rows)"]},a.id))})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(D,{id:"category-variable-label",children:"Category Variable"}),e.jsx(L,{labelId:"category-variable-label",value:m,label:"Category Variable",onChange:Be,disabled:de.length===0,children:de.map(a=>e.jsx(y,{value:a.id,children:a.name},a.id))}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Variable for the axis categories"})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(D,{id:"value-variables-label",children:"Value Variable(s) (Optional)"}),e.jsx(L,{labelId:"value-variables-label",multiple:!0,value:h,label:"Value Variable(s) (Optional)",onChange:we,disabled:F.length===0,renderValue:a=>a.map(r=>{var w;return(w=F.find(b=>b.id===r))==null?void 0:w.name}).join(", "),children:F.map(a=>e.jsxs(y,{value:a.id,children:[e.jsx(Z,{checked:h.indexOf(a.id)>-1,size:"small"}),e.jsx(s,{variant:"body2",children:a.name})]},a.id))}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"If empty, shows frequency/percentage."})]}),h.length>0&&e.jsxs(k,{component:"fieldset",fullWidth:!0,margin:"normal",children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Aggregation Method"}),e.jsxs(me,{value:P,onChange:ke,row:!0,children:[e.jsx(S,{value:"sum",control:e.jsx(A,{size:"small"}),label:"Sum"}),e.jsx(S,{value:"average",control:e.jsx(A,{size:"small"}),label:"Average"}),e.jsx(S,{value:"count",control:e.jsx(A,{size:"small"}),label:"Count"}),e.jsx(S,{value:"max",control:e.jsx(A,{size:"small"}),label:"Max"}),e.jsx(S,{value:"min",control:e.jsx(A,{size:"small"}),label:"Min"})]})]}),h.length===0&&m&&e.jsxs(k,{component:"fieldset",fullWidth:!0,margin:"normal",children:[e.jsx(s,{variant:"subtitle2",gutterBottom:!0,children:"Display Mode"}),e.jsxs(me,{value:M,onChange:Ve,row:!0,children:[e.jsx(S,{value:"frequency",control:e.jsx(A,{size:"small"}),label:"Frequency"}),e.jsx(S,{value:"percentage",control:e.jsx(A,{size:"small"}),label:"Percentage"})]})]}),e.jsxs(c,{mt:2,display:"flex",gap:1,flexDirection:"column",children:[e.jsx(Re,{variant:"contained",onClick:ce,disabled:Ie,startIcon:R?e.jsx(be,{size:20}):e.jsx(fe,{}),fullWidth:!0,children:R?"Generating...":"Generate Chart"}),e.jsxs(c,{display:"flex",gap:1,justifyContent:"center",children:[e.jsx(q,{title:"Download Chart",children:e.jsx(pe,{onClick:Me,disabled:De,children:e.jsx(Te,{})})}),e.jsx(q,{title:"Reset Settings",children:e.jsx(pe,{onClick:Ae,children:e.jsx(We,{})})})]})]})]})})}),e.jsx(ge,{in:B==="settings",timeout:300,children:e.jsx(c,{sx:{display:B==="settings"?"block":"none"},children:e.jsxs($,{elevation:2,sx:{p:2,height:"fit-content",maxHeight:"600px",overflowY:"auto",backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.02)":"rgba(0, 0, 0, 0.02)"},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,sx:{position:"sticky",top:0,backgroundColor:"inherit",zIndex:1,pb:2,mb:2},children:"Chart Settings"}),e.jsxs(c,{mb:3,children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,sx:{mt:1},children:"Labels & Title"}),e.jsx(ee,{fullWidth:!0,label:"Chart Title",value:t.title,onChange:a=>v("title",a.target.value),margin:"normal",size:"small"}),e.jsx(ee,{fullWidth:!0,label:"X-Axis Label",value:t.xAxisLabel,onChange:a=>v("xAxisLabel",a.target.value),margin:"normal",size:"small"}),e.jsx(ee,{fullWidth:!0,label:"Y-Axis Label",value:t.yAxisLabel,onChange:a=>v("yAxisLabel",a.target.value),margin:"normal",size:"small"})]}),e.jsxs(c,{mb:3,children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Appearance"}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(D,{id:"color-scheme-label",children:"Color Scheme"}),e.jsx(L,{labelId:"color-scheme-label",value:t.colorScheme,label:"Color Scheme",onChange:a=>v("colorScheme",a.target.value),children:Object.keys(te).map(a=>e.jsx(y,{value:a,children:a.charAt(0).toUpperCase()+a.slice(1)},a))})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(D,{id:"barmode-label",children:"Bar Mode (Multiple Vars)"}),e.jsxs(L,{labelId:"barmode-label",value:t.barMode,label:"Bar Mode (Multiple Vars)",onChange:a=>v("barMode",a.target.value),disabled:h.length<=1,children:[e.jsx(y,{value:"group",children:"Grouped"}),e.jsx(y,{value:"stack",children:"Stacked"}),e.jsx(y,{value:"relative",children:"Relative (Stacked %)"})]})]})]}),e.jsxs(c,{mb:3,children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Display Options"}),e.jsxs(Ge,{children:[e.jsx(S,{control:e.jsx(Z,{checked:t.showValuesOnBars,onChange:a=>v("showValuesOnBars",a.target.checked),size:"small"}),label:"Show Values on Bars"}),e.jsx(S,{control:e.jsx(Z,{checked:t.orientation==="h",onChange:a=>v("orientation",a.target.checked?"h":"v"),size:"small"}),label:"Horizontal Bars"})]}),e.jsxs(k,{fullWidth:!0,margin:"normal",size:"small",children:[e.jsx(D,{id:"sort-bars-label",children:"Sort Bars"}),e.jsxs(L,{labelId:"sort-bars-label",value:t.sortBars,label:"Sort Bars",onChange:a=>v("sortBars",a.target.value),disabled:h.length>1,children:[e.jsx(y,{value:"none",children:"None"}),e.jsx(y,{value:"ascending",children:"Ascending"}),e.jsx(y,{value:"descending",children:"Descending"})]}),e.jsx(s,{variant:"caption",color:"text.secondary",children:"Sorting enabled only for single value variable or frequency/percentage mode."})]})]}),e.jsxs(c,{mb:3,children:[e.jsx(s,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Size & Styling"}),e.jsxs(c,{sx:{mt:2},children:[e.jsx(s,{gutterBottom:!0,children:"Bar Gap (Between Categories)"}),e.jsx(je,{value:t.barGap,min:0,max:1,step:.05,onChange:(a,r)=>v("barGap",r),valueLabelDisplay:"auto",size:"small"})]}),e.jsxs(c,{sx:{mt:2},children:[e.jsx(s,{gutterBottom:!0,children:"Maximum Bars"}),e.jsx(je,{value:t.maxBars,min:5,max:50,step:1,onChange:(a,r)=>v("maxBars",r),valueLabelDisplay:"auto",size:"small"})]})]})]})})})]}),e.jsx(Q,{item:!0,xs:12,sm:12,md:9,lg:9,children:e.jsxs($,{elevation:2,sx:{p:2},children:[e.jsxs(c,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsx(s,{variant:"h6",children:"Chart Preview"}),e.jsxs(c,{display:"flex",alignItems:"center",gap:1,children:[e.jsxs(s,{variant:"body2",color:"text.secondary",children:["Active: ",B==="variables"?"Variables":"Settings"]}),e.jsx(c,{sx:{width:8,height:8,borderRadius:"50%",backgroundColor:B==="variables"?i.palette.primary.main:i.palette.warning.main,boxShadow:`0 0 0 2px ${B==="variables"?i.palette.primary.main+"20":i.palette.warning.main+"20"}`}})]})]}),ie&&e.jsx(c,{sx:{mb:2,p:2,backgroundColor:i.palette.error.light+"20",borderRadius:1,border:`1px solid ${i.palette.error.light}`},children:e.jsx(s,{color:"error",children:ie})}),e.jsx(c,{ref:V,id:le,sx:{height:500,width:"100%",display:j?"block":"flex",justifyContent:j?"flex-start":"center",alignItems:j?"flex-start":"center",border:`1px solid ${i.palette.divider}`,borderRadius:1,backgroundColor:i.palette.mode==="dark"?"rgba(255, 255, 255, 0.01)":"rgba(0, 0, 0, 0.01)",overflow:"hidden"},children:R?e.jsxs(c,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,children:[e.jsx(be,{}),e.jsx(s,{color:"text.secondary",children:"Generating chart..."})]}):j?e.jsx(e.Fragment,{}):e.jsxs(c,{display:"flex",flexDirection:"column",alignItems:"center",gap:2,p:4,children:[e.jsx(fe,{sx:{fontSize:48,color:"text.disabled"}}),e.jsx(s,{color:"text.secondary",textAlign:"center",children:d?m?"Chart will appear here once generated":"Select a category variable to generate the bar chart":"Select a dataset to begin"}),d&&!m&&e.jsx(s,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Switch to the Variables panel to select your data"}),e.jsx(s,{variant:"body2",color:"text.disabled",textAlign:"center",children:"Keyboard shortcuts: Alt+1 (Variables) | Alt+2 (Settings)"})]})})]})})]})]})};export{ra as default};
