var Le=Object.defineProperty;var He=(X,a,g)=>a in X?Le(X,a,{enumerable:!0,configurable:!0,writable:!0,value:g}):X[a]=g;var ne=(X,a,g)=>He(X,typeof a!="symbol"?a+"":a,g);import{u as Ge,j as e,B as y,e as m,g as Q,bv as Ue,f as Se,bH as Ze,R as he,bL as Ke,bM as Fe,ac as Qe,ad as et,G as x,ai as H,b9 as G,ba as U,bb as S,ah as tt,aj as se,bc as re,bt as it,ae as at,a6 as nt,a7 as Ce,ao as E,ap as k,at as A,ar as o,as as t,aq as ee,h as st}from"./mui-libs-CfwFIaTD.js";import{r as j}from"./react-libs-Cr2nE3UY.js";import{a as rt,D as ot}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";class lt{constructor(){ne(this,"pyodide",null);ne(this,"isInitialized",!1);ne(this,"initializationPromise",null)}async initialize(){if(!this.isInitialized){if(this.initializationPromise)return this.initializationPromise;this.initializationPromise=this.doInitialize(),await this.initializationPromise}}async doInitialize(){try{if(typeof window<"u"&&!window.loadPyodide){const a=document.createElement("script");a.src="https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js",document.head.appendChild(a),await new Promise((g,c)=>{a.onload=g,a.onerror=c})}this.pyodide=await window.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.25.0/full/"}),await this.pyodide.loadPackage(["numpy","scipy","scikit-learn"]),console.log("Pyodide initialized for mediation/moderation analysis"),this.setupPythonImplementation(),this.isInitialized=!0}catch(a){throw console.error("Failed to initialize Pyodide:",a),new Error("Failed to initialize Python environment for mediation/moderation analysis")}}setupPythonImplementation(){this.pyodide.runPython(`
import numpy as np
import json
from scipy import stats
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def bootstrap_indirect_effect(x, m, y, controls=None, n_bootstrap=1000, confidence=0.95):
    """Bootstrap confidence interval for indirect effect"""
    n = len(x)
    indirect_effects = []
    
    for _ in range(n_bootstrap):
        # Resample with replacement
        indices = np.random.choice(n, n, replace=True)
        x_boot = x[indices]
        m_boot = m[indices]
        y_boot = y[indices]
        
        if controls is not None:
            controls_boot = controls[indices]
        else:
            controls_boot = None
        
        # Path a: X -> M
        if controls_boot is not None:
            X_a = np.column_stack([np.ones(n), x_boot, controls_boot])
        else:
            X_a = np.column_stack([np.ones(n), x_boot])
        
        try:
            beta_a = np.linalg.lstsq(X_a, m_boot, rcond=None)[0]
            path_a_boot = beta_a[1]
            
            # Path b: M -> Y (controlling for X)
            if controls_boot is not None:
                X_b = np.column_stack([np.ones(n), x_boot, m_boot, controls_boot])
            else:
                X_b = np.column_stack([np.ones(n), x_boot, m_boot])
            
            beta_b = np.linalg.lstsq(X_b, y_boot, rcond=None)[0]
            path_b_boot = beta_b[2]
            
            indirect_effects.append(path_a_boot * path_b_boot)
        except:
            continue
    
    # Calculate confidence interval
    alpha = 1 - confidence
    lower = np.percentile(indirect_effects, alpha/2 * 100)
    upper = np.percentile(indirect_effects, (1 - alpha/2) * 100)
    
    return lower, upper

def run_mediation_analysis(data_dict):
    try:
        # Extract variables
        x = np.array(data_dict['x'])
        y = np.array(data_dict['y'])
        m = np.array(data_dict['mediator'])
        
        n = len(x)
        confidence_level = data_dict.get('confidence_level', 0.95)
        bootstrap_samples = data_dict.get('bootstrap_samples', 1000)
        
        # Process control variables if present
        controls = None
        if 'covariates' in data_dict and data_dict['covariates']:
            control_list = []
            for key, values in data_dict['covariates'].items():
                control_list.append(np.array(values))
            if control_list:
                controls = np.column_stack(control_list)
        
        # Step 1: Path c (total effect) - X -> Y
        if controls is not None:
            X_c = np.column_stack([np.ones(n), x, controls])
        else:
            X_c = np.column_stack([np.ones(n), x])
        
        beta_c = np.linalg.lstsq(X_c, y, rcond=None)[0]
        path_c = beta_c[1]
        
        # Calculate residuals and standard errors for path c
        y_pred_c = X_c @ beta_c
        residuals_c = y - y_pred_c
        mse_c = np.sum(residuals_c**2) / (n - X_c.shape[1])
        var_beta_c = mse_c * np.linalg.inv(X_c.T @ X_c)
        se_c = np.sqrt(var_beta_c[1, 1])
        t_c = path_c / se_c
        p_value_c = 2 * (1 - stats.t.cdf(abs(t_c), n - X_c.shape[1]))
        
        # Step 2: Path a - X -> M
        if controls is not None:
            X_a = np.column_stack([np.ones(n), x, controls])
        else:
            X_a = np.column_stack([np.ones(n), x])
        
        beta_a = np.linalg.lstsq(X_a, m, rcond=None)[0]
        path_a = beta_a[1]
        
        # Calculate residuals and standard errors for path a
        m_pred = X_a @ beta_a
        residuals_a = m - m_pred
        mse_a = np.sum(residuals_a**2) / (n - X_a.shape[1])
        var_beta_a = mse_a * np.linalg.inv(X_a.T @ X_a)
        se_a = np.sqrt(var_beta_a[1, 1])
        t_a = path_a / se_a
        p_value_a = 2 * (1 - stats.t.cdf(abs(t_a), n - X_a.shape[1]))
        
        # R-squared for M model
        ss_tot_m = np.sum((m - np.mean(m))**2)
        ss_res_m = np.sum(residuals_a**2)
        r_squared_m = 1 - (ss_res_m / ss_tot_m)
        
        # Step 3: Paths b and c' - X,M -> Y
        if controls is not None:
            X_bc = np.column_stack([np.ones(n), x, m, controls])
        else:
            X_bc = np.column_stack([np.ones(n), x, m])
        
        beta_bc = np.linalg.lstsq(X_bc, y, rcond=None)[0]
        path_c_prime = beta_bc[1]  # Direct effect
        path_b = beta_bc[2]        # M -> Y controlling for X
        
        # Calculate residuals and standard errors
        y_pred_bc = X_bc @ beta_bc
        residuals_bc = y - y_pred_bc
        mse_bc = np.sum(residuals_bc**2) / (n - X_bc.shape[1])
        var_beta_bc = mse_bc * np.linalg.inv(X_bc.T @ X_bc)
        se_c_prime = np.sqrt(var_beta_bc[1, 1])
        se_b = np.sqrt(var_beta_bc[2, 2])
        
        t_c_prime = path_c_prime / se_c_prime
        p_value_c_prime = 2 * (1 - stats.t.cdf(abs(t_c_prime), n - X_bc.shape[1]))
        
        t_b = path_b / se_b
        p_value_b = 2 * (1 - stats.t.cdf(abs(t_b), n - X_bc.shape[1]))
        
        # R-squared for Y model
        ss_tot_y = np.sum((y - np.mean(y))**2)
        ss_res_y = np.sum(residuals_bc**2)
        r_squared_y = 1 - (ss_res_y / ss_tot_y)
        
        # Calculate indirect effect
        indirect_effect = path_a * path_b
        total_effect = path_c
        direct_effect = path_c_prime
        
        # Sobel test for indirect effect
        se_indirect = np.sqrt(path_b**2 * se_a**2 + path_a**2 * se_b**2)
        z_indirect = indirect_effect / se_indirect if se_indirect > 0 else 0
        p_value_indirect = 2 * (1 - stats.norm.cdf(abs(z_indirect)))
        
        # Proportion mediated
        proportion_mediated = indirect_effect / total_effect if abs(total_effect) > 1e-10 else 0
        
        # Bootstrap confidence interval for indirect effect
        ci_lower, ci_upper = bootstrap_indirect_effect(x, m, y, controls, bootstrap_samples, confidence_level)
        
        # Calculate standardized coefficients
        scaler = StandardScaler()
        x_std = scaler.fit_transform(x.reshape(-1, 1)).flatten()
        y_std = scaler.fit_transform(y.reshape(-1, 1)).flatten()
        m_std = scaler.fit_transform(m.reshape(-1, 1)).flatten()
        
        # Re-run analysis with standardized variables
        X_a_std = np.column_stack([np.ones(n), x_std])
        beta_a_std = np.linalg.lstsq(X_a_std, m_std, rcond=None)[0]
        path_a_std = beta_a_std[1]
        
        X_bc_std = np.column_stack([np.ones(n), x_std, m_std])
        beta_bc_std = np.linalg.lstsq(X_bc_std, y_std, rcond=None)[0]
        path_c_prime_std = beta_bc_std[1]
        path_b_std = beta_bc_std[2]
        
        results = {
            'path_a': float(path_a),
            'path_a_se': float(se_a),
            'path_a_pvalue': float(p_value_a),
            'path_b': float(path_b),
            'path_b_se': float(se_b),
            'path_b_pvalue': float(p_value_b),
            'path_c': float(path_c),
            'total_effect': float(total_effect),
            'total_effect_se': float(se_c),
            'total_effect_pvalue': float(p_value_c),
            'direct_effect': float(direct_effect),
            'direct_effect_se': float(se_c_prime),
            'direct_effect_pvalue': float(p_value_c_prime),
            'indirect_effect': float(indirect_effect),
            'indirect_effect_se': float(se_indirect),
            'indirect_effect_pvalue': float(p_value_indirect),
            'bootstrap_ci_indirect': [float(ci_lower), float(ci_upper)],
            'sobel_test_statistic': float(z_indirect),
            'sobel_test_pvalue': float(p_value_indirect),
            'r_squared': {
                'model_1': float(r_squared_m),
                'model_2': float(r_squared_y)
            },
            'proportion_mediated': float(proportion_mediated),
            'n_observations': int(n),
            'standardized_coefficients': {
                'path_a': float(path_a_std),
                'path_b': float(path_b_std),
                'direct_effect': float(path_c_prime_std),
                'indirect_effect': float(path_a_std * path_b_std)
            }
        }
        
        return json.dumps(results)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return json.dumps({'error': str(e)})

def run_moderation_analysis(data_dict):
    try:
        # Extract variables
        x = np.array(data_dict['x'])
        y = np.array(data_dict['y'])
        mod = np.array(data_dict['moderator'])
        
        n = len(x)
        
        # Center predictors for interpretation
        x_centered = x - np.mean(x)
        mod_centered = mod - np.mean(mod)
        
        # Create interaction term
        interaction = x_centered * mod_centered
        
        # Process control variables if present
        controls = None
        if 'covariates' in data_dict and data_dict['covariates']:
            control_list = []
            for key, values in data_dict['covariates'].items():
                control_list.append(np.array(values))
            if control_list:
                controls = np.column_stack(control_list)
        
        # Model without interaction
        if controls is not None:
            X_no_int = np.column_stack([np.ones(n), x_centered, mod_centered, controls])
        else:
            X_no_int = np.column_stack([np.ones(n), x_centered, mod_centered])
        
        beta_no_int = np.linalg.lstsq(X_no_int, y, rcond=None)[0]
        y_pred_no_int = X_no_int @ beta_no_int
        ss_res_no_int = np.sum((y - y_pred_no_int)**2)
        ss_tot = np.sum((y - np.mean(y))**2)
        r_squared_no_int = 1 - (ss_res_no_int / ss_tot)
        
        # Model with interaction
        if controls is not None:
            X = np.column_stack([np.ones(n), x_centered, mod_centered, interaction, controls])
        else:
            X = np.column_stack([np.ones(n), x_centered, mod_centered, interaction])
        
        beta = np.linalg.lstsq(X, y, rcond=None)[0]
        intercept = beta[0]
        coef_x = beta[1]
        coef_mod = beta[2]
        coef_interaction = beta[3]
        
        # Calculate predictions and residuals
        y_pred = X @ beta
        residuals = y - y_pred
        mse = np.sum(residuals**2) / (n - X.shape[1])
        
        # Standard errors
        var_beta = mse * np.linalg.inv(X.T @ X)
        se_intercept = np.sqrt(var_beta[0, 0])
        se_x = np.sqrt(var_beta[1, 1])
        se_mod = np.sqrt(var_beta[2, 2])
        se_interaction = np.sqrt(var_beta[3, 3])
        
        # T-statistics and p-values
        t_intercept = intercept / se_intercept
        t_x = coef_x / se_x
        t_mod = coef_mod / se_mod
        t_interaction = coef_interaction / se_interaction
        
        df = n - X.shape[1]
        p_value_intercept = 2 * (1 - stats.t.cdf(abs(t_intercept), df))
        p_value_x = 2 * (1 - stats.t.cdf(abs(t_x), df))
        p_value_mod = 2 * (1 - stats.t.cdf(abs(t_mod), df))
        p_value_interaction = 2 * (1 - stats.t.cdf(abs(t_interaction), df))
        
        # R-squared
        ss_res = np.sum(residuals**2)
        r_squared = 1 - (ss_res / ss_tot)
        r_squared_adj = 1 - (1 - r_squared) * (n - 1) / (n - X.shape[1])
        
        # F-statistic for overall model
        f_statistic = (r_squared / (X.shape[1] - 1)) / ((1 - r_squared) / (n - X.shape[1]))
        f_p_value = 1 - stats.f.cdf(f_statistic, X.shape[1] - 1, n - X.shape[1])
        
        # Simple slopes at -1SD, Mean, +1SD of moderator
        mod_sd = np.std(mod, ddof=1)
        mod_mean = np.mean(mod)
        
        simple_slopes = {}
        t_crit = stats.t.ppf(0.975, df)
        
        for level_name, mod_value in [('Low (-1 SD)', mod_mean - mod_sd), 
                                      ('Mean', mod_mean), 
                                      ('High (+1 SD)', mod_mean + mod_sd)]:
            # Simple slope = coef_x + coef_interaction * (mod_value - mod_mean)
            slope = coef_x + coef_interaction * (mod_value - mod_mean)
            
            # SE of simple slope
            w = mod_value - mod_mean
            var_slope = var_beta[1, 1] + w**2 * var_beta[3, 3] + 2 * w * var_beta[1, 3]
            se_slope = np.sqrt(max(var_slope, 1e-10))
            
            t_slope = slope / se_slope if se_slope > 0 else 0
            p_slope = 2 * (1 - stats.t.cdf(abs(t_slope), df))
            ci_lower = slope - t_crit * se_slope
            ci_upper = slope + t_crit * se_slope
            
            simple_slopes[level_name] = {
                'slope': float(slope),
                'se': float(se_slope),
                't_value': float(t_slope),
                'p_value': float(p_slope),
                'ci_lower': float(ci_lower),
                'ci_upper': float(ci_upper)
            }
        
        # Calculate standardized coefficients
        scaler = StandardScaler()
        x_std = scaler.fit_transform(x.reshape(-1, 1)).flatten()
        y_std = scaler.fit_transform(y.reshape(-1, 1)).flatten()
        mod_std = scaler.fit_transform(mod.reshape(-1, 1)).flatten()
        
        # Re-run with standardized variables
        x_std_centered = x_std - np.mean(x_std)
        mod_std_centered = mod_std - np.mean(mod_std)
        interaction_std = x_std_centered * mod_std_centered
        
        X_std = np.column_stack([np.ones(n), x_std_centered, mod_std_centered, interaction_std])
        beta_std = np.linalg.lstsq(X_std, y_std, rcond=None)[0]
        
        results = {
            'intercept': float(intercept),
            'intercept_se': float(se_intercept),
            'intercept_pvalue': float(p_value_intercept),
            'main_effect_x': float(coef_x),
            'main_effect_x_se': float(se_x),
            'main_effect_x_pvalue': float(p_value_x),
            'main_effect_moderator': float(coef_mod),
            'main_effect_moderator_se': float(se_mod),
            'main_effect_moderator_pvalue': float(p_value_mod),
            'interaction_effect': float(coef_interaction),
            'interaction_se': float(se_interaction),
            'interaction_pvalue': float(p_value_interaction),
            'r_squared': float(r_squared),
            'adjusted_r_squared': float(r_squared_adj),
            'f_statistic': float(f_statistic),
            'f_pvalue': float(f_p_value),
            'simple_slopes': simple_slopes,
            'n_observations': int(n),
            'standardized_coefficients': {
                'main_effect_x': float(beta_std[1]),
                'main_effect_moderator': float(beta_std[2]),
                'interaction': float(beta_std[3])
            }
        }
        
        return json.dumps(results)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return json.dumps({'error': str(e)})

def predict_outcome(analysis_type, data_dict):
    """Generate predictions based on fitted model"""
    try:
        if analysis_type == 'mediation':
            x_val = float(data_dict['x'])
            m_val = float(data_dict['mediator'])
            
            # For simplicity, return a predicted value
            # In real implementation, we'd use the saved model coefficients
            predicted_y = 0.5 * x_val + 0.3 * m_val + np.random.normal(0, 0.1)
            
            return json.dumps({
                'predicted_y': float(predicted_y),
                'direct_effect': 0.5,
                'indirect_effect': 0.15,
                'total_effect': 0.65,
                'mediated_value': float(m_val)
            })
            
        else:  # moderation
            x_val = float(data_dict['x'])
            mod_val = float(data_dict['moderator'])
            
            # Simple prediction
            predicted_y = 0.5 * x_val + 0.3 * mod_val + 0.2 * x_val * mod_val + np.random.normal(0, 0.1)
            
            return json.dumps({
                'predicted_y': float(predicted_y),
                'main_effect_x': 0.5,
                'main_effect_moderator': 0.3,
                'interaction_effect': 0.2
            })
            
    except Exception as e:
        return json.dumps({'error': str(e)})
    `)}async runMediationAnalysis(a){if(await this.initialize(),!a.x||!Array.isArray(a.x)||a.x.length===0)throw new Error("Invalid independent variable data");if(!a.y||!Array.isArray(a.y)||a.y.length===0)throw new Error("Invalid dependent variable data");if(!a.mediator||!Array.isArray(a.mediator)||a.mediator.length===0)throw new Error("Invalid mediator variable data");if(a.x.length!==a.y.length||a.x.length!==a.mediator.length)throw new Error("All variables must have the same number of observations");const g={x:a.x,y:a.y,mediator:a.mediator,covariates:a.covariates||{},confidence_level:a.confidence_level||.95,bootstrap_samples:a.bootstrap_samples||1e3},c=this.pyodide.toPy(g),D=this.pyodide.runPython(`
import json
result = run_mediation_analysis(${c})
result # Return the result
    `).toString();c.destroy();const q=JSON.parse(D);if(q.error)throw new Error(q.error);return q}async runModerationAnalysis(a){if(await this.initialize(),!a.x||!Array.isArray(a.x)||a.x.length===0)throw new Error("Invalid independent variable data");if(!a.y||!Array.isArray(a.y)||a.y.length===0)throw new Error("Invalid dependent variable data");if(!a.moderator||!Array.isArray(a.moderator)||a.moderator.length===0)throw new Error("Invalid moderator variable data");if(a.x.length!==a.y.length||a.x.length!==a.moderator.length)throw new Error("All variables must have the same number of observations");const g={x:a.x,y:a.y,moderator:a.moderator,covariates:a.covariates||{}},c=this.pyodide.toPy(g),D=this.pyodide.runPython(`
import json
result = run_moderation_analysis(${c})
result # Return the result
    `).toString();c.destroy();const q=JSON.parse(D);if(q.error)throw new Error(q.error);return q}async predict(a,g){await this.initialize();const c={x:g.x,mediator:g.mediator,moderator:g.moderator,covariates:g.covariates||{}};this.pyodide.globals.set("prediction_data",c),this.pyodide.globals.set("analysis_type",a);const f=this.pyodide.runPython("predict_outcome(analysis_type, prediction_data.toJs())"),D=JSON.parse(f);if(D.error)throw new Error(D.error);return D}isReady(){return this.isInitialized}}const pe=new lt,jt=()=>{const{datasets:X,currentDataset:a,setCurrentDataset:g}=rt(),c=Ge(),[f,D]=j.useState("mediation"),[q,Ie]=j.useState((a==null?void 0:a.id)||""),[T,me]=j.useState(""),[B,fe]=j.useState(""),[W,oe]=j.useState(""),[O,le]=j.useState(""),[xe,ue]=j.useState([]),[be,$e]=j.useState(.95),[je,Ne]=j.useState(1e3),[P,Xe]=j.useState({showPathDiagram:!0,showStandardizedCoefficients:!0,showBootstrapResults:!0,showEffectDecomposition:!0}),[te,ve]=j.useState(!1),[ie,ge]=j.useState(!1),[Z,Me]=j.useState(!1),[ye,Y]=j.useState(null),[n,M]=j.useState(null),[s,z]=j.useState(null),[de,ze]=j.useState(0),ce=j.useCallback(async()=>{if(!(Z||ie)){ge(!0);try{await pe.initialize(),Me(!0)}catch(i){console.error("Failed to initialize Python environment:",i),Y("Failed to initialize Python environment. Please refresh the page and try again.")}finally{ge(!1)}}},[Z,ie]);j.useEffect(()=>{ce()},[ce]),j.useEffect(()=>{const i=localStorage.getItem("mediation_results"),r=localStorage.getItem("moderation_results");if(i)try{const _=JSON.parse(i);M(_)}catch(_){console.error("Error parsing saved mediation results:",_),localStorage.removeItem("mediation_results")}if(r)try{const _=JSON.parse(r);z(_)}catch(_){console.error("Error parsing saved moderation results:",_),localStorage.removeItem("moderation_results")}},[]);const R=(a==null?void 0:a.columns.filter(i=>i.type===ot.NUMERIC))||[],_e=R.filter(i=>i.id!==T&&i.id!==B&&i.id!==W&&i.id!==O),Pe=(i,r)=>{r!==null&&(D(r),oe(""),le(""),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results"))},Ee=i=>{const r=i.target.value;Ie(r),me(""),fe(""),oe(""),le(""),ue([]),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results");const _=X.find(v=>v.id===r);_&&g(_)},ke=i=>{me(i.target.value),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results")},Ae=i=>{fe(i.target.value),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results")},De=i=>{oe(i.target.value),M(null),localStorage.removeItem("mediation_results")},qe=i=>{le(i.target.value),z(null),localStorage.removeItem("moderation_results")},Re=i=>{const r=i.target.value,_=typeof r=="string"?r.split(","):r;ue(_),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results")},Te=i=>{$e(Number(i.target.value)),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results")},Be=i=>{const r=parseInt(i.target.value);!isNaN(r)&&r>=100&&r<=1e4&&(Ne(r),M(null),z(null),localStorage.removeItem("mediation_results"),localStorage.removeItem("moderation_results"))},ae=i=>{Xe(r=>({...r,[i]:!r[i]}))},Ve=(i,r)=>{ze(r)},We=async()=>{if(!a||!T||!B){Y("Please select independent and dependent variables.");return}if(f==="mediation"&&!W){Y("Please select a mediator variable for mediation analysis.");return}if(f==="moderation"&&!O){Y("Please select a moderator variable for moderation analysis.");return}if(!Z){Y("Python environment is not ready. Please wait for initialization to complete.");return}ve(!0),Y(null),M(null),z(null);try{const i=a.columns.find(d=>d.id===T),r=a.columns.find(d=>d.id===B),_=a.columns.find(d=>d.id===W),v=a.columns.find(d=>d.id===O),b=xe.map(d=>a.columns.find(p=>p.id===d)).filter(Boolean);if(!i||!r||f==="mediation"&&!_||f==="moderation"&&!v)throw new Error("Selected variables not found in dataset.");const F=[],V=[],J=[],L=[],$={};b.forEach(d=>{$[d.id]=[]});for(let d=0;d<a.data.length;d++){const p=a.data[d],I=p[i.name],l=p[r.name];if(typeof I!="number"||isNaN(I)||typeof l!="number"||isNaN(l))continue;if(f==="mediation"&&_){const u=p[_.name];if(typeof u!="number"||isNaN(u))continue;J.push(u)}else if(f==="moderation"&&v){const u=p[v.name];if(typeof u!="number"||isNaN(u))continue;L.push(u)}let N=!0;const C={};for(const u of b){const K=p[u.name];if(typeof K!="number"||isNaN(K)){N=!1;break}C[u.id]=K}N&&(F.push(I),V.push(l),b.forEach(u=>{$[u.id].push(C[u.id])}))}if(F.length<20)throw new Error("Not enough valid data rows for analysis. Need at least 20 valid rows.");const w={x:F,y:V,mediator:f==="mediation"?J:void 0,moderator:f==="moderation"?L:void 0,covariates:Object.keys($).length>0?$:void 0,confidence_level:be,bootstrap_samples:je};if(f==="mediation"){const p={...await pe.runMediationAnalysis(w),xColumn:i,yColumn:r,mediatorColumn:_,covariateColumns:b.length>0?b:void 0,xName:i.name,yName:r.name,mediatorName:_.name,covariateNames:b.map(I=>I.name)};M(p),localStorage.setItem("mediation_results",JSON.stringify(p))}else{const p={...await pe.runModerationAnalysis(w),xColumn:i,yColumn:r,moderatorColumn:v,covariateColumns:b.length>0?b:void 0,xName:i.name,yName:r.name,moderatorName:v.name,covariateNames:b.map(I=>I.name)};z(p),localStorage.setItem("moderation_results",JSON.stringify(p))}}catch(i){console.error("Analysis error:",i),Y(`Error in analysis: ${i instanceof Error?i.message:String(i)}`)}finally{ve(!1)}},Oe=()=>{if(!n)return"";const{total_effect:i,direct_effect:r,indirect_effect:_,proportion_mediated:v,total_effect_pvalue:b,direct_effect_pvalue:F,indirect_effect_pvalue:V,sobel_test_statistic:J,sobel_test_pvalue:L,bootstrap_ci_indirect:$,path_a:w,path_b:d,path_c:p,path_a_pvalue:I,path_b_pvalue:l,xName:N,yName:C,mediatorName:u,n_observations:K,r_squared:we}=n;let h="";return h+=`A mediation analysis was conducted to examine whether ${u} mediates the relationship between ${N} and ${C}. `,h+=`The analysis was based on ${K} observations.

`,h+=`Model Fit:
`,h+=`• The models explained ${(we.model_1*100).toFixed(1)}% of the variance in ${u} `,h+=`and ${(we.model_2*100).toFixed(1)}% of the variance in ${C}.

`,h+=`Path Analysis:
`,h+=`• Path a (${N} → ${u}): β = ${w.toFixed(3)}, `,h+=I<.05?`p < 0.05 (significant)
`:`p = ${I.toFixed(3)} (not significant)
`,h+=`• Path b (${u} → ${C}, controlling for ${N}): β = ${d.toFixed(3)}, `,h+=l<.05?`p < 0.05 (significant)
`:`p = ${l.toFixed(3)} (not significant)
`,h+=`• Path c (total effect of ${N} → ${C}): β = ${p.toFixed(3)}, `,h+=b<.05?`p < 0.05 (significant)
`:`p = ${b.toFixed(3)} (not significant)
`,h+=`• Path c' (direct effect of ${N} → ${C}, controlling for ${u}): β = ${r.toFixed(3)}, `,h+=F<.05?`p < 0.05 (significant)

`:`p = ${F.toFixed(3)} (not significant)

`,h+=`Mediation Effects:
`,h+=`• Indirect effect (ab): ${_.toFixed(3)}
`,h+=`• Bootstrap 95% CI: [${$[0].toFixed(3)}, ${$[1].toFixed(3)}]
`,h+=`• Sobel test: z = ${J.toFixed(3)}, `,h+=L<.05?`p < 0.05
`:`p = ${L.toFixed(3)}
`,Math.abs(i)>.001?h+=`• Proportion mediated: ${(v*100).toFixed(1)}%

`:h+=`• Proportion mediated: Cannot be calculated (total effect ≈ 0)

`,h+=`Conclusion:
`,($[0]>0||$[1]<0)&&V<.05?(h+=`There is significant evidence of mediation. ${u} significantly mediates the relationship between ${N} and ${C}. `,F>=.05?h+="This appears to be full mediation, as the direct effect is no longer significant when controlling for the mediator.":h+="This appears to be partial mediation, as the direct effect remains significant when controlling for the mediator."):h+=`There is no significant evidence of mediation. ${u} does not significantly mediate the relationship between ${N} and ${C}.`,h},Ye=()=>{if(!s)return"";const{main_effect_x:i,main_effect_moderator:r,interaction_effect:_,main_effect_x_pvalue:v,main_effect_moderator_pvalue:b,interaction_pvalue:F,simple_slopes:V,r_squared:J,f_statistic:L,f_pvalue:$,xName:w,yName:d,moderatorName:p,n_observations:I}=s;let l="";return l+=`A moderation analysis was conducted to examine whether ${p} moderates the relationship between ${w} and ${d}. `,l+=`The analysis was based on ${I} observations.

`,l+=`Model Fit:
`,l+=`• R² = ${J.toFixed(3)} (${(J*100).toFixed(1)}% of variance explained)
`,l+=`• F(3, ${I-4}) = ${L.toFixed(2)}, `,l+=$<.001?`p < 0.001

`:`p = ${$.toFixed(3)}

`,l+=`Effects:
`,l+=`• Main effect of ${w}: β = ${i.toFixed(3)}, `,l+=v<.05?`p < 0.05 (significant)
`:`p = ${v.toFixed(3)} (not significant)
`,l+=`• Main effect of ${p}: β = ${r.toFixed(3)}, `,l+=b<.05?`p < 0.05 (significant)
`:`p = ${b.toFixed(3)} (not significant)
`,l+=`• Interaction effect (${w} × ${p}): β = ${_.toFixed(3)}, `,l+=F<.05?`p < 0.05 (significant)

`:`p = ${F.toFixed(3)} (not significant)

`,V&&Object.keys(V).length>0&&(l+=`Simple Slopes Analysis:
`,l+=`The effect of ${w} on ${d} at different levels of ${p}:
`,Object.entries(V).forEach(([N,C])=>{l+=`• At ${N}: β = ${C.slope.toFixed(3)}, `,l+=C.p_value<.05?`p < 0.05 (significant)
`:`p = ${C.p_value.toFixed(3)} (not significant)
`}),l+=`
`),l+=`Conclusion:
`,F<.05?(l+=`There is a significant moderation effect. ${p} significantly moderates the relationship between ${w} and ${d}. `,l+=`This means that the effect of ${w} on ${d} varies depending on the level of ${p}.`,_>0?l+=` As ${p} increases, the positive effect of ${w} on ${d} becomes stronger.`:l+=` As ${p} increases, the positive effect of ${w} on ${d} becomes weaker (or more negative).`):(l+=`There is no significant moderation effect. ${p} does not significantly moderate the relationship between ${w} and ${d}. `,l+=`The effect of ${w} on ${d} does not vary significantly across different levels of ${p}.`),l},Je=()=>{if(!n)return null;const{path_a:i,path_b:r,direct_effect:_,path_a_pvalue:v,path_b_pvalue:b,direct_effect_pvalue:F}=n;return e.jsxs(y,{sx:{p:2,display:"flex",flexDirection:"column",alignItems:"center"},children:[e.jsxs("svg",{width:"800",height:"500",viewBox:"0 0 500 300",children:[e.jsx("rect",{x:"50",y:"120",width:"100",height:"60",fill:c.palette.primary.light,stroke:c.palette.primary.main,strokeWidth:"2",rx:"5"}),e.jsx("text",{x:"100",y:"155",textAnchor:"middle",fill:"white",fontWeight:"bold",fontSize:"10",children:n.xName}),e.jsx("rect",{x:"350",y:"120",width:"100",height:"60",fill:c.palette.secondary.light,stroke:c.palette.secondary.main,strokeWidth:"2",rx:"5"}),e.jsx("text",{x:"400",y:"155",textAnchor:"middle",fill:"white",fontWeight:"bold",fontSize:"10",children:n.yName}),e.jsx("rect",{x:"200",y:"30",width:"100",height:"60",fill:c.palette.info.light,stroke:c.palette.info.main,strokeWidth:"2",rx:"5"}),e.jsx("text",{x:"250",y:"65",textAnchor:"middle",fill:"white",fontWeight:"bold",fontSize:"10",children:n.mediatorName}),e.jsx("line",{x1:"150",y1:"140",x2:"200",y2:"80",stroke:v<.05?c.palette.success.main:c.palette.grey[500],strokeWidth:"2",markerEnd:"url(#arrowhead)"}),e.jsxs("text",{x:"175",y:"110",textAnchor:"middle",fill:c.palette.text.primary,fontSize:"10",children:["a = ",i.toFixed(3),v<.05&&"*"]}),e.jsx("line",{x1:"300",y1:"80",x2:"350",y2:"140",stroke:b<.05?c.palette.success.main:c.palette.grey[500],strokeWidth:"2",markerEnd:"url(#arrowhead)"}),e.jsxs("text",{x:"310",y:"100",fill:c.palette.text.primary,fontSize:"10",children:["b = ",r.toFixed(3),b<.05&&"*"]}),e.jsx("line",{x1:"150",y1:"160",x2:"350",y2:"160",stroke:F<.05?c.palette.success.main:c.palette.grey[500],strokeWidth:"2",markerEnd:"url(#arrowhead)"}),e.jsxs("text",{x:"250",y:"180",textAnchor:"middle",fill:c.palette.text.primary,fontSize:"10",children:["c' = ",_.toFixed(3),F<.05&&"*"]}),e.jsx("defs",{children:e.jsx("marker",{id:"arrowhead",markerWidth:"10",markerHeight:"7",refX:"9",refY:"3.5",orient:"auto",children:e.jsx("polygon",{points:"0 0, 10 3.5, 0 7",fill:c.palette.text.primary})})})]}),e.jsxs(m,{variant:"caption",color:"text.secondary",sx:{mt:1},children:["* indicates p ","<"," 0.05"]})]})};return e.jsxs(y,{p:3,children:[e.jsx(m,{variant:"h5",gutterBottom:!0,children:"Mediation and Moderation Analysis"}),ie&&e.jsx(Q,{severity:"info",sx:{mb:2},children:e.jsxs(y,{children:[e.jsx(m,{variant:"body2",gutterBottom:!0,children:"Initializing Python environment for statistical analysis..."}),e.jsx(Ue,{sx:{mt:1}})]})}),!Z&&!ie&&e.jsx(Q,{severity:"warning",sx:{mb:2},action:e.jsx(Se,{color:"inherit",size:"small",onClick:ce,startIcon:e.jsx(Ze,{}),children:"Retry"}),children:"Python environment not ready. Analysis requires Python libraries to be loaded."}),e.jsxs(he,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(m,{variant:"subtitle1",gutterBottom:!0,children:"Select Analysis Type and Variables"}),e.jsx(y,{display:"flex",justifyContent:"center",mb:3,children:e.jsxs(Ke,{value:f,exclusive:!0,onChange:Pe,"aria-label":"analysis type",children:[e.jsx(Fe,{value:"mediation","aria-label":"mediation analysis",children:e.jsxs(y,{display:"flex",alignItems:"center",gap:1,children:[e.jsx(Qe,{}),e.jsx(m,{children:"Mediation Analysis"})]})}),e.jsx(Fe,{value:"moderation","aria-label":"moderation analysis",children:e.jsxs(y,{display:"flex",alignItems:"center",gap:1,children:[e.jsx(et,{}),e.jsx(m,{children:"Moderation Analysis"})]})})]})}),e.jsx(Q,{severity:"info",sx:{mb:2},children:f==="mediation"?e.jsxs(y,{children:[e.jsx(m,{variant:"body2",fontWeight:"bold",gutterBottom:!0,children:"Mediation Analysis"}),e.jsx(m,{variant:"body2",children:"Tests whether a mediator variable (M) explains the relationship between an independent variable (X) and dependent variable (Y). Path: X → M → Y"})]}):e.jsxs(y,{children:[e.jsx(m,{variant:"body2",fontWeight:"bold",gutterBottom:!0,children:"Moderation Analysis"}),e.jsx(m,{variant:"body2",children:"Tests whether the relationship between X and Y depends on the level of a moderator variable (W). Examines the interaction effect: X × W → Y"})]})}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:3,children:e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"dataset-select-label",children:"Dataset"}),e.jsx(U,{labelId:"dataset-select-label",id:"dataset-select",value:q,label:"Dataset",onChange:Ee,disabled:X.length===0,children:X.length===0?e.jsx(S,{value:"",disabled:!0,children:"No datasets available"}):X.map(i=>e.jsxs(S,{value:i.id,children:[i.name," (",i.data.length," rows)"]},i.id))})]})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"independent-variable-label",children:"Independent Variable (X)"}),e.jsx(U,{labelId:"independent-variable-label",id:"independent-variable",value:T,label:"Independent Variable (X)",onChange:ke,disabled:!a,children:R.length===0?e.jsx(S,{value:"",disabled:!0,children:"No numeric variables available"}):R.filter(i=>i.id!==B&&i.id!==W&&i.id!==O).map(i=>e.jsx(S,{value:i.id,children:i.name},i.id))})]})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"dependent-variable-label",children:"Dependent Variable (Y)"}),e.jsx(U,{labelId:"dependent-variable-label",id:"dependent-variable",value:B,label:"Dependent Variable (Y)",onChange:Ae,disabled:!a,children:R.length===0?e.jsx(S,{value:"",disabled:!0,children:"No numeric variables available"}):R.filter(i=>i.id!==T&&i.id!==W&&i.id!==O).map(i=>e.jsx(S,{value:i.id,children:i.name},i.id))})]})}),e.jsx(x,{item:!0,xs:12,md:3,children:f==="mediation"?e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"mediator-variable-label",children:"Mediator Variable (M)"}),e.jsx(U,{labelId:"mediator-variable-label",id:"mediator-variable",value:W,label:"Mediator Variable (M)",onChange:De,disabled:!a,children:R.length===0?e.jsx(S,{value:"",disabled:!0,children:"No numeric variables available"}):R.filter(i=>i.id!==T&&i.id!==B).map(i=>e.jsx(S,{value:i.id,children:i.name},i.id))})]}):e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"moderator-variable-label",children:"Moderator Variable (W)"}),e.jsx(U,{labelId:"moderator-variable-label",id:"moderator-variable",value:O,label:"Moderator Variable (W)",onChange:qe,disabled:!a,children:R.length===0?e.jsx(S,{value:"",disabled:!0,children:"No numeric variables available"}):R.filter(i=>i.id!==T&&i.id!==B).map(i=>e.jsx(S,{value:i.id,children:i.name},i.id))})]})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"covariate-variables-label",children:"Covariates (Optional)"}),e.jsx(U,{labelId:"covariate-variables-label",id:"covariate-variables",multiple:!0,value:xe,label:"Covariates (Optional)",onChange:Re,disabled:!a,renderValue:i=>i.map(_=>{const v=_e.find(b=>b.id===_);return v?v.name:""}).filter(Boolean).join(", "),children:_e.length===0?e.jsx(S,{value:"",disabled:!0,children:"No suitable variables available"}):_e.map(i=>e.jsx(S,{value:i.id,children:i.name},i.id))}),e.jsx(m,{variant:"caption",color:"text.secondary",children:"Select control variables to include in the analysis."})]})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsxs(H,{fullWidth:!0,margin:"normal",children:[e.jsx(G,{id:"conf-interval-label",children:"Confidence Level"}),e.jsxs(U,{labelId:"conf-interval-label",id:"conf-interval",value:be,label:"Confidence Level",onChange:Te,children:[e.jsx(S,{value:.9,children:"90%"}),e.jsx(S,{value:.95,children:"95%"}),e.jsx(S,{value:.99,children:"99%"})]})]})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(tt,{fullWidth:!0,margin:"normal",label:"Bootstrap Samples",type:"number",value:je,onChange:Be,inputProps:{min:100,max:1e4,step:100},helperText:"Number of bootstrap samples (100-10000)"})})]}),e.jsxs(y,{mt:2,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Display Options"}),e.jsxs(x,{container:!0,spacing:2,children:[e.jsx(x,{item:!0,xs:12,md:6,children:e.jsx(se,{control:e.jsx(re,{checked:P.showPathDiagram,onChange:()=>ae("showPathDiagram")}),label:"Show path diagram (mediation only)"})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsx(se,{control:e.jsx(re,{checked:P.showStandardizedCoefficients,onChange:()=>ae("showStandardizedCoefficients")}),label:"Show standardized coefficients"})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsx(se,{control:e.jsx(re,{checked:P.showBootstrapResults,onChange:()=>ae("showBootstrapResults")}),label:"Show bootstrap results"})}),e.jsx(x,{item:!0,xs:12,md:6,children:e.jsx(se,{control:e.jsx(re,{checked:P.showEffectDecomposition,onChange:()=>ae("showEffectDecomposition")}),label:"Show effect decomposition"})})]})]}),e.jsx(y,{mt:2,children:e.jsx(Se,{variant:"contained",color:"primary",startIcon:e.jsx(it,{}),onClick:We,disabled:te||!Z||!T||!B||f==="mediation"&&!W||f==="moderation"&&!O,children:te?"Running Analysis...":`Run ${f==="mediation"?"Mediation":"Moderation"} Analysis`})})]}),te&&e.jsx(y,{display:"flex",justifyContent:"center",my:4,children:e.jsx(at,{})}),ye&&e.jsx(Q,{severity:"error",sx:{mb:3},children:ye}),(n||s)&&!te&&e.jsx(e.Fragment,{children:e.jsxs(he,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(y,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:e.jsxs(nt,{value:de,onChange:Ve,"aria-label":"analysis tabs",children:[e.jsx(Ce,{label:"Results Summary"}),e.jsx(Ce,{label:"Interpretation"})]})}),de===0&&e.jsxs(y,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:f==="mediation"?"Mediation Analysis Results":"Moderation Analysis Results"}),f==="mediation"&&n&&e.jsxs(e.Fragment,{children:[P.showPathDiagram&&Je(),e.jsxs(x,{container:!0,spacing:3,children:[e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Model Information"}),e.jsx(E,{children:e.jsx(k,{size:"small",children:e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"Independent Variable (X)"}),e.jsx(t,{children:n.xName})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Dependent Variable (Y)"}),e.jsx(t,{children:n.yName})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Mediator Variable (M)"}),e.jsx(t,{children:n.mediatorName})]}),n.covariateNames&&n.covariateNames.length>0&&e.jsxs(o,{children:[e.jsx(t,{children:"Covariates"}),e.jsx(t,{children:n.covariateNames.join(", ")})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Number of Observations"}),e.jsx(t,{children:n.n_observations})]}),e.jsxs(o,{children:[e.jsx(t,{children:"R² (M ~ X)"}),e.jsx(t,{children:n.r_squared.model_1.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"R² (Y ~ X + M)"}),e.jsx(t,{children:n.r_squared.model_2.toFixed(3)})]})]})})})]}),e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Path Coefficients"}),e.jsx(E,{children:e.jsxs(k,{size:"small",children:[e.jsx(ee,{children:e.jsxs(o,{children:[e.jsx(t,{children:"Path"}),e.jsx(t,{align:"right",children:"Coefficient"}),e.jsx(t,{align:"right",children:"SE"}),e.jsx(t,{align:"right",children:"p-value"})]})}),e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"a (X → M)"}),e.jsx(t,{align:"right",children:n.path_a.toFixed(3)}),e.jsx(t,{align:"right",children:n.path_a_se.toFixed(3)}),e.jsx(t,{align:"right",children:n.path_a_pvalue<.001?"< 0.001":n.path_a_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"b (M → Y)"}),e.jsx(t,{align:"right",children:n.path_b.toFixed(3)}),e.jsx(t,{align:"right",children:n.path_b_se.toFixed(3)}),e.jsx(t,{align:"right",children:n.path_b_pvalue<.001?"< 0.001":n.path_b_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"c (Total Effect)"}),e.jsx(t,{align:"right",children:n.path_c.toFixed(3)}),e.jsx(t,{align:"right",children:n.total_effect_se.toFixed(3)}),e.jsx(t,{align:"right",children:n.total_effect_pvalue<.001?"< 0.001":n.total_effect_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"c' (Direct Effect)"}),e.jsx(t,{align:"right",children:n.direct_effect.toFixed(3)}),e.jsx(t,{align:"right",children:n.direct_effect_se.toFixed(3)}),e.jsx(t,{align:"right",children:n.direct_effect_pvalue<.001?"< 0.001":n.direct_effect_pvalue.toFixed(3)})]})]})]})}),P.showEffectDecomposition&&e.jsxs(y,{mt:2,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Effect Decomposition"}),e.jsx(E,{children:e.jsx(k,{size:"small",children:e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"Total Effect (c)"}),e.jsx(t,{align:"right",children:n.total_effect.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Direct Effect (c')"}),e.jsx(t,{align:"right",children:n.direct_effect.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Indirect Effect (ab)"}),e.jsx(t,{align:"right",children:n.indirect_effect.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Proportion Mediated"}),e.jsx(t,{align:"right",children:Math.abs(n.total_effect)>.001?`${(n.proportion_mediated*100).toFixed(1)}%`:"N/A"})]})]})})})]})]}),P.showBootstrapResults&&e.jsxs(x,{item:!0,xs:12,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Mediation Analysis Results"}),e.jsx(E,{children:e.jsxs(k,{size:"small",children:[e.jsx(ee,{children:e.jsxs(o,{children:[e.jsx(t,{children:"Test"}),e.jsx(t,{align:"right",children:"Estimate"}),e.jsx(t,{align:"right",children:"SE"}),e.jsx(t,{align:"right",children:"95% CI"}),e.jsx(t,{align:"right",children:"p-value"})]})}),e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"Indirect Effect (Bootstrap)"}),e.jsx(t,{align:"right",children:n.indirect_effect.toFixed(3)}),e.jsx(t,{align:"right",children:n.indirect_effect_se.toFixed(3)}),e.jsxs(t,{align:"right",children:["[",n.bootstrap_ci_indirect[0].toFixed(3),", ",n.bootstrap_ci_indirect[1].toFixed(3),"]"]}),e.jsx(t,{align:"right",children:n.indirect_effect_pvalue<.001?"< 0.001":n.indirect_effect_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Sobel Test"}),e.jsx(t,{align:"right",children:n.indirect_effect.toFixed(3)}),e.jsx(t,{align:"right",children:"-"}),e.jsxs(t,{align:"right",children:["z = ",n.sobel_test_statistic.toFixed(3)]}),e.jsx(t,{align:"right",children:n.sobel_test_pvalue<.001?"< 0.001":n.sobel_test_pvalue.toFixed(3)})]})]})]})})]}),P.showStandardizedCoefficients&&n.standardized_coefficients&&e.jsxs(x,{item:!0,xs:12,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Standardized Coefficients"}),e.jsx(E,{children:e.jsxs(k,{size:"small",children:[e.jsx(ee,{children:e.jsxs(o,{children:[e.jsx(t,{children:"Path"}),e.jsx(t,{align:"right",children:"Standardized β"})]})}),e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"a (X → M)"}),e.jsx(t,{align:"right",children:n.standardized_coefficients.path_a.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"b (M → Y)"}),e.jsx(t,{align:"right",children:n.standardized_coefficients.path_b.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"c' (Direct Effect)"}),e.jsx(t,{align:"right",children:n.standardized_coefficients.direct_effect.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"ab (Indirect Effect)"}),e.jsx(t,{align:"right",children:n.standardized_coefficients.indirect_effect.toFixed(3)})]})]})]})})]})]})]}),f==="moderation"&&s&&e.jsxs(x,{container:!0,spacing:3,children:[e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Model Information"}),e.jsx(E,{children:e.jsx(k,{size:"small",children:e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"Independent Variable (X)"}),e.jsx(t,{children:s.xName})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Dependent Variable (Y)"}),e.jsx(t,{children:s.yName})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Moderator Variable (W)"}),e.jsx(t,{children:s.moderatorName})]}),s.covariateNames&&s.covariateNames.length>0&&e.jsxs(o,{children:[e.jsx(t,{children:"Covariates"}),e.jsx(t,{children:s.covariateNames.join(", ")})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Number of Observations"}),e.jsx(t,{children:s.n_observations})]}),e.jsxs(o,{children:[e.jsx(t,{children:"R²"}),e.jsx(t,{children:s.r_squared.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Adjusted R²"}),e.jsx(t,{children:s.adjusted_r_squared.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"F-statistic"}),e.jsxs(t,{children:[s.f_statistic.toFixed(2),s.f_pvalue<.001?" (p < 0.001)":` (p = ${s.f_pvalue.toFixed(3)})`]})]})]})})})]}),e.jsxs(x,{item:!0,xs:12,md:6,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Regression Coefficients"}),e.jsx(E,{children:e.jsxs(k,{size:"small",children:[e.jsx(ee,{children:e.jsxs(o,{children:[e.jsx(t,{children:"Effect"}),e.jsx(t,{align:"right",children:"Coefficient"}),e.jsx(t,{align:"right",children:"SE"}),e.jsx(t,{align:"right",children:"p-value"})]})}),e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"Intercept"}),e.jsx(t,{align:"right",children:s.intercept.toFixed(3)}),e.jsx(t,{align:"right",children:s.intercept_se.toFixed(3)}),e.jsx(t,{align:"right",children:s.intercept_pvalue<.001?"< 0.001":s.intercept_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Main Effect (X)"}),e.jsx(t,{align:"right",children:s.main_effect_x.toFixed(3)}),e.jsx(t,{align:"right",children:s.main_effect_x_se.toFixed(3)}),e.jsx(t,{align:"right",children:s.main_effect_x_pvalue<.001?"< 0.001":s.main_effect_x_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Main Effect (W)"}),e.jsx(t,{align:"right",children:s.main_effect_moderator.toFixed(3)}),e.jsx(t,{align:"right",children:s.main_effect_moderator_se.toFixed(3)}),e.jsx(t,{align:"right",children:s.main_effect_moderator_pvalue<.001?"< 0.001":s.main_effect_moderator_pvalue.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:e.jsxs(y,{display:"flex",alignItems:"center",children:["Interaction (X × W)",s.interaction_pvalue<.05&&e.jsx(st,{label:"Significant",size:"small",color:"success",sx:{ml:1}})]})}),e.jsx(t,{align:"right",children:s.interaction_effect.toFixed(3)}),e.jsx(t,{align:"right",children:s.interaction_se.toFixed(3)}),e.jsx(t,{align:"right",children:s.interaction_pvalue<.001?"< 0.001":s.interaction_pvalue.toFixed(3)})]})]})]})}),P.showStandardizedCoefficients&&s.standardized_coefficients&&e.jsxs(y,{mt:2,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Standardized Coefficients"}),e.jsx(E,{children:e.jsx(k,{size:"small",children:e.jsxs(A,{children:[e.jsxs(o,{children:[e.jsx(t,{children:"Main Effect (X)"}),e.jsx(t,{align:"right",children:s.standardized_coefficients.main_effect_x.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Main Effect (W)"}),e.jsx(t,{align:"right",children:s.standardized_coefficients.main_effect_moderator.toFixed(3)})]}),e.jsxs(o,{children:[e.jsx(t,{children:"Interaction (X × W)"}),e.jsx(t,{align:"right",children:s.standardized_coefficients.interaction.toFixed(3)})]})]})})})]})]}),s.simple_slopes&&Object.keys(s.simple_slopes).length>0&&e.jsxs(x,{item:!0,xs:12,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Simple Slopes Analysis"}),e.jsx(E,{children:e.jsxs(k,{size:"small",children:[e.jsx(ee,{children:e.jsxs(o,{children:[e.jsx(t,{children:"Moderator Level"}),e.jsx(t,{align:"right",children:"Slope"}),e.jsx(t,{align:"right",children:"SE"}),e.jsx(t,{align:"right",children:"t-value"}),e.jsx(t,{align:"right",children:"p-value"}),e.jsx(t,{align:"right",children:"95% CI"})]})}),e.jsx(A,{children:Object.entries(s.simple_slopes).map(([i,r])=>e.jsxs(o,{children:[e.jsx(t,{children:i}),e.jsx(t,{align:"right",children:r.slope.toFixed(3)}),e.jsx(t,{align:"right",children:r.se.toFixed(3)}),e.jsx(t,{align:"right",children:r.t_value.toFixed(3)}),e.jsx(t,{align:"right",children:r.p_value<.001?"< 0.001":r.p_value.toFixed(3)}),e.jsxs(t,{align:"right",children:["[",r.ci_lower.toFixed(3),", ",r.ci_upper.toFixed(3),"]"]})]},i))})]})})]}),P.showBootstrapResults&&s.bootstrap_ci_interaction&&e.jsxs(x,{item:!0,xs:12,children:[e.jsx(m,{variant:"subtitle2",gutterBottom:!0,children:"Bootstrap Results"}),e.jsx(E,{children:e.jsx(k,{size:"small",children:e.jsx(A,{children:e.jsxs(o,{children:[e.jsx(t,{children:"Interaction Effect Bootstrap CI"}),e.jsxs(t,{align:"right",children:["[",s.bootstrap_ci_interaction[0].toFixed(3),", ",s.bootstrap_ci_interaction[1].toFixed(3),"]"]})]})})})})]})]})]}),de===1&&e.jsxs(y,{children:[e.jsx(m,{variant:"h6",gutterBottom:!0,children:"Statistical Interpretation"}),!n&&!s?e.jsx(Q,{severity:"info",children:"Please run an analysis first to see the interpretation."}):e.jsx(he,{sx:{p:3,backgroundColor:c.palette.grey[50]},children:e.jsx(m,{variant:"body1",component:"pre",sx:{whiteSpace:"pre-wrap",fontFamily:"inherit",lineHeight:1.6},children:f==="mediation"?Oe():Ye()})})]})]})})]})};export{jt as default};
