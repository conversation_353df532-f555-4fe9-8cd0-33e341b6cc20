import{u as V,j as t,B as f,e as n,R as C,G as l,ao as z,ap as E,aq as q,ar as g,as as m,aE as N,I as T,aF as I,at as F,ah as b,f as P,bG as A,bH as _,D as H}from"./mui-libs-CfwFIaTD.js";import{r as R}from"./react-libs-Cr2nE3UY.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import{S as v}from"./StatsCard-op8tGQ0a.js";import"./index-Bpan7Tbe.js";import"./VariableSelector-CPdlCsJ2.js";import{j as S}from"./other-utils-CR9xr_gI.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";class M{static matchedOddsRatio(i,e){if(i===0&&e===0)return{or:void 0,lower:void 0,upper:void 0};const r=i===0?.5:i,s=e===0?.5:e,o=r/s,c=Math.log(o),u=Math.sqrt(1/r+1/s);return{or:o,lower:Math.exp(c-1.96*u),upper:Math.exp(c+1.96*u)}}static mcnemarTest(i,e){const r=i+e;if(r===0)return{chi2:void 0,pChi2:void 0,pExact:void 0};const s=Math.pow(Math.abs(i-e)-1,2)/r,o=this.chiSquarePValue(s,1),c=this.exactMcNemarPValue(i,e);return{chi2:s,pChi2:o,pExact:c}}static diagnosticMetrics(i,e,r,s){if(i+r===0||s+e===0||i+e===0||s+r===0)return{sensitivity:{estimate:void 0,lower:void 0,upper:void 0},specificity:{estimate:void 0,lower:void 0,upper:void 0},ppv:{estimate:void 0,lower:void 0,upper:void 0},npv:{estimate:void 0,lower:void 0,upper:void 0}};const o=i/(i+r),c=s/(s+e),u=i/(i+e),y=s/(s+r);return{sensitivity:this.withCI(o,i+r),specificity:this.withCI(c,s+e),ppv:this.withCI(u,i+e),npv:this.withCI(y,s+r)}}static exactMcNemarPValue(i,e){const r=i+e;if(r===0)return;if(i===e)return 1;if(r>20){const c=Math.abs(i-e)/Math.sqrt(r);return 2*(1-S.normal.cdf(c,0,1))}let s=0;const o=Math.min(i,e);for(let c=0;c<=o;c++)s+=this.binomialPMF(r,c,.5);return 2*s}static binomialPMF(i,e,r){return this.binomialCoefficient(i,e)*Math.pow(r,e)*Math.pow(1-r,i-e)}static binomialCoefficient(i,e){if(e<0||e>i)return 0;if(e===0||e===i)return 1;if(i>20){let s=0;for(let o=i-e+1;o<=i;o++)s+=Math.log(o);for(let o=1;o<=e;o++)s-=Math.log(o);return Math.exp(s)}let r=1;for(let s=1;s<=e;s++)r*=i-(e-s),r/=s;return r}static withCI(i,e){if(i===void 0||e===0)return{estimate:void 0,lower:void 0,upper:void 0};const r=1.96,s=r*r,o=i+s/(2*e),c=r*Math.sqrt((i*(1-i)+s/(4*e))/e),u=1+s/e;return{estimate:i,lower:Math.max(0,(o-c)/u),upper:Math.min(1,(o+c)/u)}}static chiSquarePValue(i,e){if(!(i<0||e<1))return 1-S.chisquare.cdf(i,e)}}const et=()=>{V();const[x,i]=R.useState({a:0,b:0,c:0,d:0}),[e,r]=R.useState({}),s=(a,d)=>{const p=d===""?0:parseInt(d,10);i(j=>({...j,[a]:isNaN(p)?0:p}))},o=()=>{try{const{a,b:d,c:p,d:j}=x;if(a+d+p+j===0)return;const B=M.matchedOddsRatio(d,p),O=M.mcnemarTest(d,p),$=M.diagnosticMetrics(a,d,p,j);r({matchedOR:B,mcnemar:O,diagnostics:$})}catch(a){console.error("Error calculating results:",a)}},c=()=>{i({a:0,b:0,c:0,d:0}),r({})},u=(a,d=3)=>a===void 0||isNaN(a)?"N/A":a.toFixed(d),y=(a,d,p=3)=>a===void 0||d===void 0||isNaN(a)||isNaN(d)?"N/A":`${u(a,p)} - ${u(d,p)}`,h=(a,d=1)=>a===void 0||isNaN(a)?"N/A":`${(a*100).toFixed(d)}%`,w=a=>a===void 0||isNaN(a)?"N/A":a<.001?"< 0.001":a.toFixed(4);return t.jsxs(f,{children:[t.jsx(n,{variant:"h6",gutterBottom:!0,children:"Matched Case-Control Study Calculator"}),t.jsx(n,{variant:"body1",paragraph:!0,children:"Calculate matched odds ratios and other measures for matched case-control studies."}),t.jsxs(C,{elevation:1,sx:{p:3,mb:3},children:[t.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"2×2 Contingency Table for Matched Pairs"}),t.jsx(n,{variant:"body2",paragraph:!0,children:"Enter the values for your matched pairs to calculate epidemiological measures."}),t.jsxs(l,{container:!0,spacing:3,sx:{mb:3},children:[t.jsx(l,{item:!0,xs:12,md:8,children:t.jsx(z,{component:C,variant:"outlined",children:t.jsxs(E,{"aria-label":"2x2 contingency table for matched pairs",children:[t.jsxs(q,{children:[t.jsxs(g,{children:[t.jsx(m,{}),t.jsxs(m,{align:"center",colSpan:2,children:["Control",t.jsx(N,{title:"The matched control subject in each pair",children:t.jsx(T,{size:"small",children:t.jsx(I,{fontSize:"small"})})})]})]}),t.jsxs(g,{children:[t.jsx(m,{}),t.jsx(m,{align:"center",children:"Exposed"}),t.jsx(m,{align:"center",children:"Unexposed"})]})]}),t.jsxs(F,{children:[t.jsxs(g,{children:[t.jsxs(m,{component:"th",scope:"row",rowSpan:2,children:["Case",t.jsx(N,{title:"The case subject in each matched pair",children:t.jsx(T,{size:"small",children:t.jsx(I,{fontSize:"small"})})})]}),t.jsxs(m,{align:"center",children:[t.jsx(b,{type:"number",value:x.a||"",onChange:a=>s("a",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}}),t.jsx(n,{variant:"caption",display:"block",sx:{mt:1},children:"Cell a: Both exposed"})]}),t.jsxs(m,{align:"center",children:[t.jsx(b,{type:"number",value:x.b||"",onChange:a=>s("b",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}}),t.jsx(n,{variant:"caption",display:"block",sx:{mt:1},children:"Cell b: Case exposed, Control unexposed"})]})]}),t.jsxs(g,{children:[t.jsxs(m,{align:"center",children:[t.jsx(b,{type:"number",value:x.c||"",onChange:a=>s("c",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}}),t.jsx(n,{variant:"caption",display:"block",sx:{mt:1},children:"Cell c: Case unexposed, Control exposed"})]}),t.jsxs(m,{align:"center",children:[t.jsx(b,{type:"number",value:x.d||"",onChange:a=>s("d",a.target.value),inputProps:{min:0},variant:"outlined",size:"small",sx:{width:"80px"}}),t.jsx(n,{variant:"caption",display:"block",sx:{mt:1},children:"Cell d: Both unexposed"})]})]})]})]})})}),t.jsxs(l,{item:!0,xs:12,md:4,sx:{display:"flex",flexDirection:"column",justifyContent:"center"},children:[t.jsxs(f,{sx:{display:"flex",gap:2,mb:2},children:[t.jsx(P,{variant:"contained",color:"primary",startIcon:t.jsx(A,{}),onClick:o,disabled:Object.values(x).every(a=>a===0),children:"Calculate"}),t.jsx(P,{variant:"outlined",startIcon:t.jsx(_,{}),onClick:c,children:"Reset"})]}),t.jsx(n,{variant:"body2",color:"text.secondary",children:"Enter the values in the 2×2 table and click Calculate to compute epidemiological measures."}),t.jsxs(n,{variant:"body2",sx:{mt:2,p:2,bgcolor:"background.default",borderRadius:1},children:[t.jsx("strong",{children:"Note:"})," For matched case-control studies, only the discordant pairs (cells b and c) are used to calculate the matched odds ratio and McNemar's test."]})]})]})]}),Object.keys(e).length>0&&t.jsxs(f,{sx:{mt:4},children:[t.jsx(H,{sx:{mb:3}}),t.jsx(n,{variant:"h6",gutterBottom:!0,children:"Results"}),t.jsxs(l,{container:!0,spacing:3,children:[e.matchedOR&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"Matched Odds Ratio",value:u(e.matchedOR.or),description:`95% CI: ${y(e.matchedOR.lower,e.matchedOR.upper)}`,color:"primary",variant:"outlined",tooltip:"The odds ratio for matched case-control data, calculated using discordant pairs"})}),e.mcnemar&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"McNemar's Test",value:`χ² = ${u(e.mcnemar.chi2,2)}`,description:`p-value: ${w(e.mcnemar.pChi2)}`,color:"info",variant:"outlined",tooltip:"McNemar's chi-square test for paired data with continuity correction"})}),e.mcnemar&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"Exact McNemar's Test",value:`p = ${w(e.mcnemar.pExact)}`,description:"Binomial exact test for paired data",color:"info",variant:"outlined",tooltip:"Exact binomial test for paired data, recommended for small sample sizes"})}),e.diagnostics&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"Sensitivity",value:h(e.diagnostics.sensitivity.estimate),description:`95% CI: ${h(e.diagnostics.sensitivity.lower)} - ${h(e.diagnostics.sensitivity.upper)}`,color:"success",variant:"outlined",tooltip:"The proportion of true positives that are correctly identified by the test"})}),e.diagnostics&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"Specificity",value:h(e.diagnostics.specificity.estimate),description:`95% CI: ${h(e.diagnostics.specificity.lower)} - ${h(e.diagnostics.specificity.upper)}`,color:"success",variant:"outlined",tooltip:"The proportion of true negatives that are correctly identified by the test"})}),e.diagnostics&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"Positive Predictive Value",value:h(e.diagnostics.ppv.estimate),description:`95% CI: ${h(e.diagnostics.ppv.lower)} - ${h(e.diagnostics.ppv.upper)}`,color:"warning",variant:"outlined",tooltip:"The proportion of positive test results that are true positives"})}),e.diagnostics&&t.jsx(l,{item:!0,xs:12,sm:6,md:4,children:t.jsx(v,{title:"Negative Predictive Value",value:h(e.diagnostics.npv.estimate),description:`95% CI: ${h(e.diagnostics.npv.lower)} - ${h(e.diagnostics.npv.upper)}`,color:"warning",variant:"outlined",tooltip:"The proportion of negative test results that are true negatives"})})]}),t.jsxs(C,{elevation:1,sx:{p:3,mt:3},children:[t.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Interpretation Guidelines"}),t.jsxs(l,{container:!0,spacing:2,children:[t.jsxs(l,{item:!0,xs:12,md:6,children:[t.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"Matched Odds Ratio"}),t.jsx(n,{variant:"body2",paragraph:!0,dangerouslySetInnerHTML:{__html:`
                  • OR = 1: No association between exposure and disease<br />
                  • OR > 1: Positive association (exposure may increase risk)<br />
                  • OR < 1: Negative association (exposure may decrease risk)<br />
                  • Check if the confidence interval includes 1 to assess statistical significance
                `}})]}),t.jsxs(l,{item:!0,xs:12,md:6,children:[t.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"McNemar's Test"}),t.jsx(n,{variant:"body2",paragraph:!0,dangerouslySetInnerHTML:{__html:`
                  • Tests the null hypothesis that the proportions of discordant pairs are equal<br />
                  • p < 0.05: Reject null hypothesis (significant difference between pairs)<br />
                  • Use the exact test when the number of discordant pairs is small (< 20)
                `}})]}),t.jsxs(l,{item:!0,xs:12,md:6,children:[t.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"Sensitivity & Specificity"}),t.jsx(n,{variant:"body2",paragraph:!0,dangerouslySetInnerHTML:{__html:`
                  • Sensitivity: Ability to correctly identify those with the disease<br />
                  • Specificity: Ability to correctly identify those without the disease<br />
                  • Higher values indicate better test performance
                `}})]}),t.jsxs(l,{item:!0,xs:12,md:6,children:[t.jsx(n,{variant:"subtitle2",gutterBottom:!0,children:"Predictive Values"}),t.jsx(n,{variant:"body2",paragraph:!0,dangerouslySetInnerHTML:{__html:`
                  • PPV: Probability that subjects with a positive test truly have the disease<br />
                  • NPV: Probability that subjects with a negative test truly don't have the disease<br />
                  • These values are affected by disease prevalence in the population
                `}})]})]})]})]})]})};export{et as default};
