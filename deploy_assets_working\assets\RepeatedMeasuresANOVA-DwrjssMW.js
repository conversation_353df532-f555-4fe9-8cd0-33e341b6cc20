import{u as _,j as e,B as A,R as r,br as $,X as w,e as n,g,aF as z,G as l,a5 as G,ao as R,ap as T,aq as N,ar as m,as as t,at as B,i as W}from"./mui-libs-CfwFIaTD.js";import{r as o}from"./react-libs-Cr2nE3UY.js";import{a as q,D as x}from"./index-Bpan7Tbe.js";import{B as D}from"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import"./StatsCard-op8tGQ0a.js";import{D as K}from"./DatasetSelector-G08QHuaN.js";import{V as O}from"./VariableSelector-CPdlCsJ2.js";import"./other-utils-CR9xr_gI.js";import{c as P}from"./repeatedMeasuresANOVA-B8bv3NGM.js";import{R as X,d as U,C as Y,X as H,Y as J,T as Q,L as Z,e as I}from"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";const je=()=>{const{datasets:M,currentDataset:a,setCurrentDataset:V}=q(),j=_(),[c,L]=o.useState([]),[b,k]=o.useState(null),[i,y]=o.useState(null),[f,p]=o.useState(!1),[v,d]=o.useState(null);a!=null&&a.columns.filter(s=>s.type===x.NUMERIC).map(s=>s.name),a!=null&&a.columns.filter(s=>s.type===x.CATEGORICAL).map(s=>s.name);const E=async()=>{if(!a||c.length<2){d("Please select a dataset and at least two within-subject factor levels (columns).");return}p(!0),d(null),y(null);try{if(!(a!=null&&a.data)){d("Dataset is not loaded or has no data."),p(!1);return}const s=await P(a.data,"__implicit_subject_id__",c,b);y(s)}catch(s){d(`Analysis failed: ${s.message}`),console.error(s)}finally{p(!1)}};return e.jsxs(A,{children:[e.jsxs(r,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:`1px solid ${j.palette.divider}`},children:[e.jsxs($,{direction:"row",alignItems:"center",spacing:2,mb:3,children:[e.jsx(w,{sx:{color:j.palette.primary.main,fontSize:28}}),e.jsx(n,{variant:"h6",fontWeight:"bold",children:"Repeated Measures ANOVA Configuration"})]}),e.jsx(g,{severity:"info",icon:e.jsx(z,{}),sx:{mb:3},children:e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Repeated Measures ANOVA:"})," This component performs repeated measures analysis of variance to analyze data where the same subjects are measured multiple times, accounting for within-subject correlations."]})}),e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Test Options"}),e.jsxs(l,{container:!0,spacing:3,children:[e.jsx(l,{item:!0,xs:12,children:e.jsx(K,{value:(a==null?void 0:a.id)??"",onChange:s=>{const u=M.find(h=>h.id===s)||null;V(u)}})}),e.jsx(l,{item:!0,xs:12,md:6,children:e.jsx(O,{label:"Within-Subjects Factor Levels (Columns)",value:c,onChange:s=>L(s),multiple:!0,datasetId:(a==null?void 0:a.id)||"",allowedTypes:[x.NUMERIC]})}),e.jsx(l,{item:!0,xs:12,md:6,children:e.jsx(O,{label:"Between-Subjects Factor (Optional, Categorical)",value:b??"",onChange:s=>k(s),datasetId:(a==null?void 0:a.id)||"",allowedTypes:[x.CATEGORICAL]})}),e.jsx(l,{item:!0,xs:12,children:e.jsx(A,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:e.jsx(D,{variant:"contained",color:"primary",startIcon:e.jsx(G,{}),onClick:E,disabled:f||!a||c.length<2,size:"large",children:f?"Running...":"Run Analysis"})})})]})]}),v&&e.jsxs(r,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:`1px solid ${j.palette.error.main}`},children:[e.jsx(n,{variant:"h6",color:"error",gutterBottom:!0,children:"Error"}),e.jsx(n,{variant:"body2",children:v})]}),i&&e.jsxs(r,{elevation:0,sx:{p:3,mb:3},children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Results"}),i.message&&e.jsx(g,{severity:"info",sx:{mb:2},children:i.message}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"ANOVA Summary Table"}),e.jsx(R,{component:r,sx:{mb:2},children:e.jsxs(T,{size:"small",children:[e.jsx(N,{children:e.jsxs(m,{children:[e.jsx(t,{children:"Source"}),e.jsx(t,{align:"right",children:"SS"}),e.jsx(t,{align:"right",children:"df"}),e.jsx(t,{align:"right",children:"MS"}),e.jsx(t,{align:"right",children:"F"}),e.jsx(t,{align:"right",children:"p"}),e.jsx(t,{align:"right",children:"η²"})]})}),e.jsx(B,{children:i.summary.map(s=>e.jsxs(m,{children:[e.jsx(t,{component:"th",scope:"row",children:s.source}),e.jsx(t,{align:"right",children:typeof s.SS=="number"?s.SS.toFixed(3):"-"}),e.jsx(t,{align:"right",children:s.df}),e.jsx(t,{align:"right",children:typeof s.MS=="number"&&!isNaN(s.MS)?s.MS.toFixed(3):"-"}),e.jsx(t,{align:"right",children:typeof s.F=="number"&&!isNaN(s.F)?s.F.toFixed(3):"-"}),e.jsx(t,{align:"right",children:typeof s.p=="number"&&!isNaN(s.p)?s.p<.001?"<0.001":s.p.toFixed(3):"-"}),e.jsx(t,{align:"right",children:typeof s.etaSquared=="number"&&!isNaN(s.etaSquared)?s.etaSquared.toFixed(3):"-"})]},s.source))})]})}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Descriptive Statistics"}),e.jsx(R,{component:r,sx:{mb:2},children:e.jsxs(T,{size:"small",children:[e.jsx(N,{children:e.jsxs(m,{children:[e.jsx(t,{children:"Condition"}),e.jsx(t,{align:"right",children:"N"}),e.jsx(t,{align:"right",children:"Mean"}),e.jsx(t,{align:"right",children:"SD"})]})}),e.jsx(B,{children:i.descriptives.map(s=>e.jsxs(m,{children:[e.jsx(t,{component:"th",scope:"row",children:s.condition}),e.jsx(t,{align:"right",children:s.n}),e.jsx(t,{align:"right",children:s.mean.toFixed(3)}),e.jsx(t,{align:"right",children:s.sd.toFixed(3)})]},s.condition))})]})}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Sphericity Test"}),e.jsxs(r,{sx:{p:2,mb:2,backgroundColor:s=>W(s.palette.info.light,.1)},children:[e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"Mauchly's W:"})," ",i.sphericity.mauchlyW.toFixed(3)]}),e.jsxs(n,{variant:"body2",children:[e.jsx("strong",{children:"p-value:"})," ",i.sphericity.pValue<.001?"<0.001":i.sphericity.pValue.toFixed(3)]}),e.jsx(n,{variant:"body2",sx:{mt:1},children:i.sphericity.message})]}),e.jsx(n,{variant:"subtitle1",gutterBottom:!0,children:"Means Plot"}),e.jsx(r,{sx:{p:2,mb:2,height:300},children:e.jsx(X,{width:"100%",height:"100%",children:e.jsxs(U,{margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Y,{strokeDasharray:"3 3"}),e.jsx(H,{dataKey:"name",allowDuplicatedCategory:!1,padding:{left:50,right:50}}),e.jsx(J,{}),e.jsx(Q,{}),e.jsx(Z,{}),i.parameters.betweenSubjectsFactorCol!=="N/A"&&i.means&&typeof Object.values(i.means)[0]=="object"?Object.keys(i.means[Object.keys(i.means)[0]]).map((s,u)=>e.jsx(I,{type:"monotone",dataKey:`mean_${s}`,name:`${i.parameters.betweenSubjectsFactorCol}: ${s}`,stroke:u%2===0?"#8884d8":"#82ca9d",activeDot:{r:8},data:i.parameters.withinFactorCols.map(h=>{const S=i.means[h],C={name:h};return Object.keys(S).forEach(F=>{C[`mean_${F}`]=S[F]}),C})},s)):e.jsx(I,{type:"monotone",dataKey:"mean",stroke:"#8884d8",activeDot:{r:8},data:i.descriptives.map(s=>({name:s.condition,mean:s.mean}))})]})})}),i.notes&&e.jsx(g,{severity:"warning",children:i.notes})]})]})};export{je as RepeatedMeasuresANOVA,je as default};
