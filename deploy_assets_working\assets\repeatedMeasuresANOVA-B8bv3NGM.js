import{j as x}from"./other-utils-CR9xr_gI.js";function T(c){const S=c.length;if(S===1)return c[0][0];if(S===2)return c[0][0]*c[1][1]-c[0][1]*c[1][0];let r=0;for(let u=0;u<S;u++){const N=c.slice(1).map(n=>n.filter((i,a)=>u!==a));r+=(u%2===0?1:-1)*c[0][u]*T(N)}return r}const ee=async(c,S,r,u)=>{if(!c||c.length===0)throw new Error("Data cannot be empty.");if(!r||r.length<2)throw new Error("At least two within-subject factor levels (columns) must be specified.");const N=c.length,n=r.length,i=r.map(e=>c.map(t=>parseFloat(t[e])).filter(t=>!isNaN(t))),a=i[0].length;if(!i.every(e=>e.length===a))throw new Error("Inconsistent number of valid data points across levels. Please check for missing values.");const I=r.map((e,t)=>{const s=i[t],o=s.length,f=o>0?s.reduce((p,l)=>p+l,0)/o:0,O=o>1?Math.sqrt(s.reduce((p,l)=>p+Math.pow(l-f,2),0)/(o-1)):0;return{condition:e,mean:f,sd:O,n:o}}),P={};I.forEach(e=>{P[e.condition]=e.mean});const M=[];for(let e=0;e<a;e++){const t=[];for(let s=0;s<n;s++)t.push(i[s][e]);M.push(t)}const L=M.flat(),w=L.reduce((e,t)=>e+t,0)/L.length,_=L.reduce((e,t)=>e+Math.pow(t-w,2),0),z=M.map(e=>e.reduce((t,s)=>t+s,0)/e.length).reduce((e,t)=>e+n*Math.pow(t-w,2),0),E=r.map((e,t)=>{const s=M.map(o=>o[t]);return s.reduce((o,f)=>o+f,0)/s.length}),H=_-z,m=E.reduce((e,t)=>e+a*Math.pow(t-w,2),0),F=H-m,K=a*(n-1),h=n-1,v=K-h,A=m/h,q=F/v,b=A/q,$=b>0?1-x.centralF.cdf(b,h,v):1,Q=m/(m+F),g=[];for(let e=0;e<n;e++){g[e]=[];for(let t=0;t<n;t++){const s=i[e],o=i[t],f=E[e],O=E[t];let p=0;for(let l=0;l<a;l++)p+=(s[l]-f)*(o[l]-O);p/=a-1,g[e][t]=p}}const U=T(g),X=g.reduce((e,t,s)=>e*t[s],1),W=U/Math.pow(X,1/n),k=-(a-1)*Math.log(W),B=n*(n-1)/2-1,D=k>0&&B>0?1-x.chisquare.cdf(k,B):1,d=D>=.05;let y=b,V=h,G=v,J=$,R="Sphericity assumed (Mauchly's test p > 0.05).",j=1;d||(j=Math.max(1/(n-1),.75),V=h*j,G=v*j,y=A/q,J=y>0?1-x.centralF.cdf(y,V,G):1,R=`Sphericity violated (Mauchly's W = ${W.toFixed(3)}, p = ${D.toFixed(3)}). Greenhouse-Geisser correction applied (ε = ${j.toFixed(3)}).`);const Y=[{source:"Within-Subjects Factor",SS:m,df:d?h:V.toFixed(2),MS:A,F:d?b:y,p:d?$:J,etaSquared:Q},{source:"Error (Within)",SS:F,df:d?v:G.toFixed(2),MS:q,F:NaN,p:NaN,etaSquared:NaN}];return{message:"Repeated Measures ANOVA completed successfully.",parameters:{subjectIdCol:S,withinFactorCols:r,betweenSubjectsFactorCol:u||"N/A",numberOfSubjects:N,numberOfLevels:n},summary:Y,means:P,descriptives:I,sphericity:{mauchlyW:W,pValue:D,assumed:d,message:R},notes:d?"":"Note: Greenhouse-Geisser correction applied due to violation of sphericity assumption."}};export{ee as c};
