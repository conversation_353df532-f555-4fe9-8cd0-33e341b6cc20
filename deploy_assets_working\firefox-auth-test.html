<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-good { color: #4CAF50; font-weight: bold; }
        .status-warning { color: #FF9800; font-weight: bold; }
        .status-error { color: #F44336; font-weight: bold; }
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover { background: #1565c0; }
        .test-button:disabled { background: #ccc; cursor: not-allowed; }
        .result-box {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        h1 { color: #1976d2; }
        h2 { color: #333; border-bottom: 2px solid #1976d2; padding-bottom: 5px; }
    </style>
</head>
<body>
    <h1>🦊 Firefox Authentication Test for Hostinger</h1>
    <p>This tool specifically tests Firefox authentication issues on Hostinger hosting.</p>

    <div class="test-section">
        <h2>🔍 Environment Detection</h2>
        <div id="environment-info"></div>
    </div>

    <div class="test-section">
        <h2>🔐 Supabase Authentication Test</h2>
        <div class="input-group">
            <label for="test-email">Test Email:</label>
            <input type="email" id="test-email" placeholder="<EMAIL>">
        </div>
        <div class="input-group">
            <label for="test-password">Test Password:</label>
            <input type="password" id="test-password" placeholder="your-test-password">
        </div>
        <button class="test-button" onclick="testSupabaseAuth()">Test Authentication</button>
        <button class="test-button" onclick="testSupabaseConnection()">Test Connection Only</button>
        <div id="auth-results"></div>
    </div>

    <div class="test-section">
        <h2>🌐 Network & WebSocket Tests</h2>
        <button class="test-button" onclick="testWebSocketConnection()">Test WebSocket</button>
        <button class="test-button" onclick="testFetchAPI()">Test Fetch API</button>
        <button class="test-button" onclick="testCORSPreflight()">Test CORS Preflight</button>
        <div id="network-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🔧 Firefox-Specific Debugging</h2>
        <button class="test-button" onclick="checkFirefoxQuirks()">Check Firefox Quirks</button>
        <button class="test-button" onclick="testContentBlocking()">Test Content Blocking</button>
        <button class="test-button" onclick="simulateAuthFlow()">Simulate Auth Flow</button>
        <div id="firefox-debug-results"></div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.OCeU38SWqhgeprIekN8qoaPdCzu04RaU-ktdNOci4rs';

        // Initialize environment detection
        document.addEventListener('DOMContentLoaded', function() {
            detectEnvironment();
        });

        function detectEnvironment() {
            const info = {
                userAgent: navigator.userAgent,
                isFirefox: navigator.userAgent.includes('Firefox'),
                firefoxVersion: navigator.userAgent.match(/Firefox\/(\d+)/)?.[1] || 'N/A',
                protocol: location.protocol,
                hostname: location.hostname,
                isHTTPS: location.protocol === 'https:',
                isSecureContext: window.isSecureContext,
                cookiesEnabled: navigator.cookieEnabled,
                localStorage: 'localStorage' in window,
                sessionStorage: 'sessionStorage' in window,
                indexedDB: 'indexedDB' in window,
                serviceWorker: 'serviceWorker' in navigator,
                webSocket: 'WebSocket' in window,
                fetch: 'fetch' in window,
                timestamp: new Date().toISOString()
            };

            const envEl = document.getElementById('environment-info');
            envEl.innerHTML = `
                <div class="result-box">
                    <strong>Environment Information:</strong>
                    ${JSON.stringify(info, null, 2)}
                </div>
            `;
        }

        async function testSupabaseConnection() {
            const resultsEl = document.getElementById('auth-results');
            resultsEl.innerHTML = '<div class="result-box">Testing Supabase connection...</div>';

            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    method: 'HEAD',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    ok: response.ok
                };

                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="${response.ok ? 'status-good' : 'status-error'}">
                            ${response.ok ? '✅' : '❌'} Supabase Connection Test
                        </span>
                        ${JSON.stringify(result, null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Supabase Connection Failed</span>
                        Error: ${error.message}
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }

        async function testSupabaseAuth() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const resultsEl = document.getElementById('auth-results');

            if (!email || !password) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-warning">⚠️ Please enter test email and password</span>
                    </div>
                `;
                return;
            }

            resultsEl.innerHTML = '<div class="result-box">Testing Supabase authentication...</div>';

            try {
                const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();
                
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="${response.ok ? 'status-good' : 'status-error'}">
                            ${response.ok ? '✅' : '❌'} Authentication Test
                        </span>
                        Status: ${response.status}
                        Response: ${JSON.stringify(data, null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ Authentication Failed</span>
                        Error: ${error.message}
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }

        async function testWebSocketConnection() {
            const resultsEl = document.getElementById('network-test-results');
            resultsEl.innerHTML = '<div class="result-box">Testing WebSocket connection...</div>';

            try {
                const wsUrl = `wss://${SUPABASE_URL.replace('https://', '')}/realtime/v1/websocket?apikey=${SUPABASE_ANON_KEY}&vsn=1.0.0`;
                const ws = new WebSocket(wsUrl);

                const timeout = setTimeout(() => {
                    ws.close();
                    resultsEl.innerHTML = `
                        <div class="result-box">
                            <span class="status-warning">⚠️ WebSocket Connection Timeout</span>
                            URL: ${wsUrl}
                            ReadyState: ${ws.readyState}
                        </div>
                    `;
                }, 5000);

                ws.onopen = function() {
                    clearTimeout(timeout);
                    ws.close();
                    resultsEl.innerHTML = `
                        <div class="result-box">
                            <span class="status-good">✅ WebSocket Connection Successful</span>
                            URL: ${wsUrl}
                        </div>
                    `;
                };

                ws.onerror = function(error) {
                    clearTimeout(timeout);
                    resultsEl.innerHTML = `
                        <div class="result-box">
                            <span class="status-error">❌ WebSocket Connection Failed</span>
                            URL: ${wsUrl}
                            Error: ${error.message || 'Unknown error'}
                            ReadyState: ${ws.readyState}
                        </div>
                    `;
                };

            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="result-box">
                        <span class="status-error">❌ WebSocket Test Failed</span>
                        Error: ${error.message}
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }

        async function testFetchAPI() {
            const resultsEl = document.getElementById('network-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                const testUrls = [
                    `${SUPABASE_URL}/rest/v1/`,
                    `${SUPABASE_URL}/auth/v1/settings`,
                    'https://httpbin.org/get'
                ];

                let results = '<div class="result-box"><strong>Fetch API Tests:</strong>\n';

                for (const url of testUrls) {
                    try {
                        const response = await fetch(url, {
                            method: 'GET',
                            headers: {
                                'apikey': SUPABASE_ANON_KEY
                            }
                        });
                        results += `✅ ${url}: ${response.status} ${response.statusText}\n`;
                    } catch (fetchError) {
                        results += `❌ ${url}: ${fetchError.message}\n`;
                    }
                }

                results += '</div>';
                resultsEl.innerHTML = currentContent + results;

            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ Fetch API Test Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        async function testCORSPreflight() {
            const resultsEl = document.getElementById('network-test-results');
            const currentContent = resultsEl.innerHTML;

            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    method: 'OPTIONS',
                    headers: {
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization, apikey'
                    }
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Max-Age': response.headers.get('Access-Control-Max-Age')
                };

                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="${response.ok ? 'status-good' : 'status-error'}">
                            ${response.ok ? '✅' : '❌'} CORS Preflight Test
                        </span>
                        Status: ${response.status}
                        CORS Headers: ${JSON.stringify(corsHeaders, null, 2)}
                    </div>
                `;
            } catch (error) {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <span class="status-error">❌ CORS Preflight Failed</span>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function checkFirefoxQuirks() {
            const resultsEl = document.getElementById('firefox-debug-results');
            const isFirefox = navigator.userAgent.includes('Firefox');

            const quirks = {
                'Is Firefox': isFirefox,
                'Enhanced Tracking Protection': 'unknown',
                'Strict Privacy Mode': 'unknown',
                'Content Blocking': 'unknown',
                'Third-party Cookies': navigator.cookieEnabled,
                'Local Storage': 'localStorage' in window,
                'Session Storage': 'sessionStorage' in window,
                'IndexedDB': 'indexedDB' in window,
                'Service Worker': 'serviceWorker' in navigator,
                'WebSocket': 'WebSocket' in window,
                'Fetch': 'fetch' in window,
                'Secure Context': window.isSecureContext
            };

            // Test for Firefox-specific features
            if (isFirefox) {
                try {
                    // Test if content blocking is active
                    const testImg = new Image();
                    testImg.src = 'https://www.google-analytics.com/analytics.js';
                    testImg.onerror = () => {
                        quirks['Content Blocking'] = 'Active (blocked analytics)';
                    };
                    testImg.onload = () => {
                        quirks['Content Blocking'] = 'Inactive (analytics loaded)';
                    };
                } catch (e) {
                    quirks['Content Blocking'] = 'Error testing';
                }
            }

            resultsEl.innerHTML = `
                <div class="result-box">
                    <strong>Firefox Quirks Detection:</strong>
                    ${JSON.stringify(quirks, null, 2)}
                </div>
            `;
        }

        function testContentBlocking() {
            const resultsEl = document.getElementById('firefox-debug-results');
            const currentContent = resultsEl.innerHTML;

            const tests = [];

            // Test 1: Try to access a commonly blocked resource
            try {
                fetch('https://www.google-analytics.com/analytics.js', { mode: 'no-cors' })
                    .then(() => tests.push('✅ Analytics not blocked'))
                    .catch(() => tests.push('❌ Analytics blocked'));
            } catch (e) {
                tests.push('❌ Analytics fetch failed');
            }

            // Test 2: Try WebSocket to a tracking domain
            try {
                const ws = new WebSocket('wss://echo.websocket.org');
                ws.onopen = () => {
                    tests.push('✅ WebSocket to external domain works');
                    ws.close();
                };
                ws.onerror = () => tests.push('❌ WebSocket to external domain blocked');
            } catch (e) {
                tests.push('❌ WebSocket creation failed');
            }

            // Test 3: Local storage access
            try {
                localStorage.setItem('firefox-test', 'test');
                const value = localStorage.getItem('firefox-test');
                localStorage.removeItem('firefox-test');
                tests.push(value === 'test' ? '✅ Local storage works' : '❌ Local storage blocked');
            } catch (e) {
                tests.push('❌ Local storage access failed');
            }

            setTimeout(() => {
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <strong>Content Blocking Tests:</strong>
                        ${tests.join('\n')}
                    </div>
                `;
            }, 2000);
        }

        async function simulateAuthFlow() {
            const resultsEl = document.getElementById('firefox-debug-results');
            const currentContent = resultsEl.innerHTML;

            resultsEl.innerHTML = currentContent + '<div class="result-box">Simulating authentication flow...</div>';

            const steps = [];

            try {
                // Step 1: Test initial connection
                steps.push('🔄 Step 1: Testing initial connection...');
                const initResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    method: 'HEAD',
                    headers: { 'apikey': SUPABASE_ANON_KEY }
                });
                steps.push(`✅ Step 1: Connection ${initResponse.ok ? 'successful' : 'failed'} (${initResponse.status})`);

                // Step 2: Test auth endpoint
                steps.push('🔄 Step 2: Testing auth endpoint...');
                const authResponse = await fetch(`${SUPABASE_URL}/auth/v1/settings`, {
                    headers: { 'apikey': SUPABASE_ANON_KEY }
                });
                steps.push(`✅ Step 2: Auth endpoint ${authResponse.ok ? 'accessible' : 'failed'} (${authResponse.status})`);

                // Step 3: Test WebSocket (if supported)
                if ('WebSocket' in window) {
                    steps.push('🔄 Step 3: Testing WebSocket connection...');
                    const wsPromise = new Promise((resolve) => {
                        const ws = new WebSocket(`wss://${SUPABASE_URL.replace('https://', '')}/realtime/v1/websocket?apikey=${SUPABASE_ANON_KEY}&vsn=1.0.0`);
                        const timeout = setTimeout(() => {
                            ws.close();
                            resolve('❌ Step 3: WebSocket timeout');
                        }, 3000);
                        
                        ws.onopen = () => {
                            clearTimeout(timeout);
                            ws.close();
                            resolve('✅ Step 3: WebSocket connection successful');
                        };
                        
                        ws.onerror = () => {
                            clearTimeout(timeout);
                            resolve('❌ Step 3: WebSocket connection failed');
                        };
                    });
                    
                    const wsResult = await wsPromise;
                    steps.push(wsResult);
                } else {
                    steps.push('❌ Step 3: WebSocket not supported');
                }

                // Step 4: Test storage APIs
                steps.push('🔄 Step 4: Testing storage APIs...');
                try {
                    localStorage.setItem('auth-test', 'test');
                    sessionStorage.setItem('auth-test', 'test');
                    localStorage.removeItem('auth-test');
                    sessionStorage.removeItem('auth-test');
                    steps.push('✅ Step 4: Storage APIs working');
                } catch (storageError) {
                    steps.push(`❌ Step 4: Storage APIs failed - ${storageError.message}`);
                }

                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <strong>Authentication Flow Simulation:</strong>
                        ${steps.join('\n')}
                        
                        <strong>Summary:</strong>
                        ${steps.filter(s => s.includes('✅')).length} steps passed
                        ${steps.filter(s => s.includes('❌')).length} steps failed
                    </div>
                `;

            } catch (error) {
                steps.push(`❌ Simulation failed: ${error.message}`);
                resultsEl.innerHTML = currentContent + `
                    <div class="result-box">
                        <strong>Authentication Flow Simulation Failed:</strong>
                        ${steps.join('\n')}
                        Error: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
