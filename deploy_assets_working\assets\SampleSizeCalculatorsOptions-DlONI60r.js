import{j as e,bI as p,bJ as d,a0 as m,bK as h,u,C as x,R as g,i,e as r,G as n,k as f,bz as j,aE as b,I as y,aF as C,B as l,d as v,l as w,f as z,bA as D}from"./mui-libs-CfwFIaTD.js";import{b as S}from"./react-libs-Cr2nE3UY.js";const o=[{name:"One Sample Calculator",shortDescription:"Calculate sample size for a single group",detailedDescription:"Determine the required sample size for studies involving a single population mean or proportion.",path:"samplesize/one-sample",icon:e.jsx(p,{}),category:"Means",color:"#2196F3"},{name:"Two Sample Calculator",shortDescription:"Calculate sample size for comparing two groups",detailedDescription:"Determine the required sample size for studies comparing the means or proportions of two independent groups.",path:"samplesize/two-sample",icon:e.jsx(d,{}),category:"Means",color:"#4CAF50"},{name:"Paired Sample Calculator",shortDescription:"Calculate sample size for paired or matched data",detailedDescription:"Determine the required sample size for studies involving paired observations or matched subjects, such as pre-post designs.",path:"samplesize/paired-sample",icon:e.jsx(m,{}),category:"Means",color:"#FF9800"},{name:"More Than Two Groups Calculator",shortDescription:"Calculate sample size for comparing multiple groups (ANOVA)",detailedDescription:"Determine the required sample size for studies comparing the means of more than two independent groups, typically used for ANOVA.",path:"samplesize/more-than-two-groups",icon:e.jsx(h,{}),category:"Means",color:"#9C27B0"}],M=({onNavigate:c})=>{const s=u(),[t,I]=S.useState("All");return t==="All"||o.filter(a=>a.category===t),e.jsxs(x,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(g,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${i(s.palette.primary.main,.1)} 0%, ${i(s.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[e.jsx(r,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Sample Size Calculators"}),e.jsx(r,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Determine the necessary sample size for your study"}),e.jsx(r,{variant:"body1",color:"text.secondary",children:"Use these calculators to estimate the minimum number of participants or observations required to detect a statistically significant effect with a given level of power."})]}),e.jsx(n,{container:!0,spacing:3,children:o.map(a=>e.jsx(n,{item:!0,xs:12,md:6,lg:4,children:e.jsxs(f,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:s.shadows[8],"& .launch-button":{backgroundColor:a.color,color:"white"}}},children:[e.jsx(j,{avatar:e.jsx(v,{sx:{bgcolor:a.color,width:48,height:48},children:a.icon}),title:e.jsx(r,{variant:"h6",fontWeight:"bold",children:a.name}),subheader:e.jsx(l,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"}}),action:e.jsx(b,{title:"More information",children:e.jsx(y,{size:"small",children:e.jsx(C,{})})})}),e.jsxs(w,{sx:{flexGrow:1,pt:0},children:[e.jsx(r,{variant:"body2",color:"text.secondary",paragraph:!0,children:a.shortDescription}),e.jsx(r,{variant:"body2",paragraph:!0,children:a.detailedDescription})]}),e.jsx(l,{sx:{p:2,pt:0},children:e.jsxs(z,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>c(a.path),endIcon:e.jsx(D,{}),sx:{borderColor:a.color,color:a.color,fontWeight:"bold","&:hover":{borderColor:a.color}},children:["Launch ",a.name]})})]})},a.name))})]})};export{M as default,o as sampleSizeCalculatorOptions};
