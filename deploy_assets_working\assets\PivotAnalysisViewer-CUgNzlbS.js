const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./PlotlyRenderers-px7c2MoL.js","./react-libs-Cr2nE3UY.js","./mui-libs-CfwFIaTD.js","./charts-plotly-BhN4fPIu.js","./charts-recharts-d3-BEF1Y_jn.js","./charts-plotly-CuCRB34y.css"])))=>i.map(i=>d[i]);
import{_ as qr}from"./supabase-lib-B3goak-P.js";import{ck as Cn,j as y,f as Gr,aH as Wr,bS as lo,bT as uo,B as tt,aE as ba,I as gn,bs as fo,cm as co,bU as po,e as et,ah as ho,aj as Kr,bc as Zr,g as wa,bV as go,ae as mo,p as pr,u as vo,a as Qn,k as ea,l as ta,br as yo,aU as bo,a3 as wo,G as Jr,R as So}from"./mui-libs-CfwFIaTD.js";import{r as Fe,g as Eo,a as _o,d as Do,e as xo}from"./react-libs-Cr2nE3UY.js";import{l as An,a as Sa,V as To,E as ra,D as it,Z as Oo}from"./index-Bpan7Tbe.js";import{P as Co}from"./PageTitle-DA3BXQ4x.js";import{D as Ao}from"./DatasetSelector-G08QHuaN.js";var Pn={},Po=Cn;Object.defineProperty(Pn,"__esModule",{value:!0});var Ea=Pn.default=void 0,Io=Po(An()),No=y;Ea=Pn.default=(0,Io.default)((0,No.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-6h2zm0-8h-2V7h2z"}),"Info");var In={},ko=Cn;Object.defineProperty(In,"__esModule",{value:!0});var _a=In.default=void 0,Mo=ko(An()),jo=y;_a=In.default=(0,Mo.default)((0,jo.jsx)("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"}),"ExpandMore");var Nn={},Fo=Cn;Object.defineProperty(Nn,"__esModule",{value:!0});var Da=Nn.default=void 0,Ro=Fo(An()),Lo=y;Da=Nn.default=(0,Ro.default)((0,Lo.jsx)("path",{d:"m12 8-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"}),"ExpandLess");const Uo=({variant:n="outlined",color:e="primary",size:r="medium"})=>{const{addDataset:t,setCurrentDataset:a}=Sa(),[o,i]=Fe.useState(!1),[s,u]=Fe.useState(""),[c,g]=Fe.useState(!1),[m,b]=Fe.useState(null),[O,D]=Fe.useState({hasHeader:!0,skipEmptyLines:!0,dynamicTyping:!0}),E=()=>i(!0),v=()=>{c||(i(!1),u(""),b(null))},S=F=>{const k=F.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);return k?k[1]:null},_=F=>{const k=F.filter(A=>A!==""&&A!==null&&A!==void 0);if(k.length===0)return it.TEXT;const J=["0","1"];if(k.every(A=>J.includes(A)))return it.NUMERIC;const q=["yes","no"];if(k.every(A=>q.includes(A.toLowerCase())))return it.CATEGORICAL;const Y=["true","false"];if(k.every(A=>Y.includes(A.toLowerCase())))return it.BOOLEAN;if(k.every(A=>!isNaN(Number(A))&&A.trim()!==""))return it.NUMERIC;const ae=/^\d{1,4}[\/\-]\d{1,2}[\/\-]\d{1,4}$|^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/;return k.some(A=>ae.test(A)||!isNaN(Date.parse(A)))?it.DATE:it.CATEGORICAL},w=async()=>{g(!0),b(null);try{const F=S(s);if(!F)throw new Error("Invalid Google Sheets URL. Please provide a valid URL.");const k=`https://docs.google.com/spreadsheets/d/${F}/export?format=csv`,J=await fetch(k);if(!J.ok)throw new Error('Failed to fetch data from Google Sheets. Make sure the sheet is publicly accessible ("Anyone with the link can view")');const Y=(await J.text()).split(`
`).map(ve=>ve.split(",").map(ie=>{const X=ie.trim();return X.startsWith('"')&&X.endsWith('"')?X.substring(1,X.length-1):X}));if(Y.length===0)throw new Error("The spreadsheet appears to be empty.");let Q=[],ae=[];O.hasHeader&&Y.length>0?(Q=Y[0],ae=Y.slice(1)):(Q=Y[0].map((ve,ie)=>`Column ${ie+1}`),ae=Y),O.skipEmptyLines&&(ae=ae.filter(ve=>ve.some(ie=>ie.trim()!=="")));const Ie=Q.map((ve,ie)=>{const X=ae.map(Ce=>Ce[ie]||""),Le=_(X);return{id:ra(),name:ve,type:Le,role:To.NONE}}),A=ae.map(ve=>{const ie={};return Q.forEach((X,Le)=>{const Ce=ve[Le]||"";if(O.dynamicTyping){const Qe=Ie[Le].type;if(Qe===it.NUMERIC&&Ce!=="")ie[X]=Number(Ce);else if(Qe===it.DATE&&Ce!=="")try{ie[X]=new Date(Ce)}catch{ie[X]=Ce}else ie[X]=Ce}else ie[X]=Ce}),ie}),Re={id:ra(),name:`Google Sheet Import ${new Date().toLocaleDateString()}`,description:`Imported from Google Sheets: ${s}`,dateCreated:new Date,dateModified:new Date,columns:Ie,data:A},Oe=await t(Re);g(!1),typeof Oe=="string"&&(Oe==="added"||Oe==="selected")&&(u(""),i(!1),a(Re))}catch(F){b(`Failed to import data: ${F instanceof Error?F.message:String(F)}`),g(!1)}},C=F=>{D(k=>({...k,[F]:!k[F]}))};return y.jsxs(y.Fragment,{children:[y.jsx(Gr,{variant:n,color:e,size:r,startIcon:y.jsx(Wr,{}),onClick:E,children:"Import from Google Sheets"}),y.jsxs(lo,{open:o,onClose:v,maxWidth:"sm",fullWidth:!0,children:[y.jsx(uo,{children:y.jsxs(tt,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[y.jsxs(tt,{display:"flex",alignItems:"center",children:[y.jsx(Wr,{sx:{mr:1,color:"primary.main"}}),"Import from Google Sheets",y.jsx(ba,{title:"The Google Sheet must be publicly accessible (set to 'Anyone with the link can view')",children:y.jsx(gn,{size:"small",sx:{ml:1},children:y.jsx(fo,{fontSize:"small"})})})]}),y.jsx(gn,{edge:"end",color:"inherit",onClick:v,"aria-label":"close",children:y.jsx(co,{})})]})}),y.jsxs(po,{children:[y.jsx(et,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Import data directly from a Google Sheets document. Paste the URL of your Google Sheet below."}),y.jsx(ho,{fullWidth:!0,label:"Google Sheets URL",placeholder:"https://docs.google.com/spreadsheets/d/...",value:s,onChange:F=>u(F.target.value),variant:"outlined",sx:{mb:2},disabled:c}),y.jsxs(tt,{sx:{mb:2},children:[y.jsx(et,{variant:"subtitle2",sx:{mb:1},children:"Import Options"}),y.jsx(Kr,{control:y.jsx(Zr,{checked:O.hasHeader,onChange:()=>C("hasHeader"),disabled:c}),label:"First row contains headers"}),y.jsx(Kr,{control:y.jsx(Zr,{checked:O.skipEmptyLines,onChange:()=>C("skipEmptyLines"),disabled:c}),label:"Skip empty rows"}),y.jsx(Kr,{control:y.jsx(Zr,{checked:O.dynamicTyping,onChange:()=>C("dynamicTyping"),disabled:c}),label:"Automatically detect data types"})]}),m&&y.jsx(wa,{severity:"error",sx:{mb:2},children:m})]}),y.jsxs(go,{children:[y.jsx(Gr,{onClick:v,disabled:c,children:"Cancel"}),y.jsx(Gr,{variant:"contained",color:"primary",onClick:w,disabled:!s.trim()||c,startIcon:c?y.jsx(mo,{size:20}):y.jsx(Wr,{}),children:c?"Importing...":"Import"})]})]})]})};var Ut={},Fr={exports:{}},Je=Oo,mn=Object.prototype.hasOwnProperty,$o=Array.prototype.splice,zo=Object.prototype.toString,Ot=function(n){return zo.call(n).slice(8,-1)},vn=Object.assign||function(e,r){return kn(r).forEach(function(t){mn.call(r,t)&&(e[t]=r[t])}),e},kn=typeof Object.getOwnPropertySymbols=="function"?function(n){return Object.keys(n).concat(Object.getOwnPropertySymbols(n))}:function(n){return Object.keys(n)};function dt(n){if(Array.isArray(n))return vn(n.constructor(n.length),n);if(Ot(n)==="Map")return new Map(n);if(Ot(n)==="Set")return new Set(n);if(n&&typeof n=="object"){var e=Object.getPrototypeOf(n);return vn(Object.create(e),n)}else return n}function xa(){var n=vn({},Bo);return e.extend=function(r,t){n[r]=t},e.isEquals=function(r,t){return r===t},e;function e(r,t){typeof t=="function"&&(t={$apply:t}),Array.isArray(r)&&Array.isArray(t)||Je(!Array.isArray(t),"update(): You provided an invalid spec to update(). The spec may not contain an array except as the value of $set, $push, $unshift, $splice or any custom command allowing an array value."),Je(typeof t=="object"&&t!==null,"update(): You provided an invalid spec to update(). The spec and every included key path must be plain objects containing one of the following commands: %s.",Object.keys(n).join(", "));var a=r;return kn(t).forEach(function(o){if(mn.call(n,o)){var i=r===a;a=n[o](t[o],a,t,r),i&&e.isEquals(a,r)&&(a=r)}else{var s=Ot(r)==="Map"?e(r.get(o),t[o]):e(r[o],t[o]),u=Ot(a)==="Map"?a.get(o):a[o];(!e.isEquals(s,u)||typeof s>"u"&&!mn.call(r,o))&&(a===r&&(a=dt(r)),Ot(a)==="Map"?a.set(o,s):a[o]=s)}}),a}}var Bo={$push:function(n,e,r){return na(e,r,"$push"),n.length?e.concat(n):e},$unshift:function(n,e,r){return na(e,r,"$unshift"),n.length?n.concat(e):e},$splice:function(n,e,r,t){return Vo(e,r),n.forEach(function(a){Oa(a),e===t&&a.length&&(e=dt(t)),$o.apply(e,a)}),e},$set:function(n,e,r){return Yo(r),n},$toggle:function(n,e){tr(n,"$toggle");var r=n.length?dt(e):e;return n.forEach(function(t){r[t]=!e[t]}),r},$unset:function(n,e,r,t){return tr(n,"$unset"),n.forEach(function(a){Object.hasOwnProperty.call(e,a)&&(e===t&&(e=dt(t)),delete e[a])}),e},$add:function(n,e,r,t){return aa(e,"$add"),tr(n,"$add"),Ot(e)==="Map"?n.forEach(function(a){var o=a[0],i=a[1];e===t&&e.get(o)!==i&&(e=dt(t)),e.set(o,i)}):n.forEach(function(a){e===t&&!e.has(a)&&(e=dt(t)),e.add(a)}),e},$remove:function(n,e,r,t){return aa(e,"$remove"),tr(n,"$remove"),n.forEach(function(a){e===t&&e.has(a)&&(e=dt(t)),e.delete(a)}),e},$merge:function(n,e,r,t){return Xo(e,n),kn(n).forEach(function(a){n[a]!==e[a]&&(e===t&&(e=dt(t)),e[a]=n[a])}),e},$apply:function(n,e){return Ho(n),n(e)}},Ta=xa();Fr.exports=Ta;Fr.exports.default=Ta;Fr.exports.newContext=xa;function na(n,e,r){Je(Array.isArray(n),"update(): expected target of %s to be an array; got %s.",r,n),tr(e[r],r)}function tr(n,e){Je(Array.isArray(n),"update(): expected spec of %s to be an array; got %s. Did you forget to wrap your parameter in an array?",e,n)}function Vo(n,e){Je(Array.isArray(n),"Expected $splice target to be an array; got %s",n),Oa(e.$splice)}function Oa(n){Je(Array.isArray(n),"update(): expected spec of $splice to be an array of arrays; got %s. Did you forget to wrap your parameters in an array?",n)}function Ho(n){Je(typeof n=="function","update(): expected spec of $apply to be a function; got %s.",n)}function Yo(n){Je(Object.keys(n).length===1,"Cannot have more than one key in an object with $set")}function Xo(n,e){Je(e&&typeof e=="object","update(): $merge expects a spec of type 'object'; got %s",e),Je(n&&typeof n=="object","update(): $merge expects a target of type 'object'; got %s",n)}function aa(n,e){var r=Ot(n);Je(r==="Map"||r==="Set","update(): %s expects a target of type Set or Map; got %s",e,r)}var qo=Fr.exports,be={};Object.defineProperty(be,"__esModule",{value:!0});be.PivotData=be.sortAs=be.getSort=be.numberFormat=be.naturalSort=be.locales=be.derivers=be.aggregators=be.aggregatorTemplates=void 0;var Go=function(){function n(e,r){for(var t=0;t<r.length;t++){var a=r[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(e,r,t){return r&&n(e.prototype,r),t&&n(e,t),e}}(),It=function(){function n(e,r){var t=[],a=!0,o=!1,i=void 0;try{for(var s=e[Symbol.iterator](),u;!(a=(u=s.next()).done)&&(t.push(u.value),!(r&&t.length===r));a=!0);}catch(c){o=!0,i=c}finally{try{!a&&s.return&&s.return()}finally{if(o)throw i}}return t}return function(e,r){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return n(e,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),Wo=pr,he=Ko(Wo);function Ko(n){return n&&n.__esModule?n:{default:n}}function Zo(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function Qr(n){if(Array.isArray(n)){for(var e=0,r=Array(n.length);e<n.length;e++)r[e]=n[e];return r}else return Array.from(n)}var Jo=function(e,r,t){for(var a=String(e).split("."),o=a[0],i=a.length>1?t+a[1]:"",s=/(\d+)(\d{3})/;s.test(o);)o=o.replace(s,"$1"+r+"$2");return o+i},Rr=function(e){var r={digitsAfterDecimal:2,scaler:1,thousandsSep:",",decimalSep:".",prefix:"",suffix:""},t=Object.assign({},r,e);return function(a){if(isNaN(a)||!isFinite(a))return"";var o=Jo((t.scaler*a).toFixed(t.digitsAfterDecimal),t.thousandsSep,t.decimalSep);return""+t.prefix+o+t.suffix}},oa=/(\d+)|(\D+)/g,br=/\d/,ia=/^0/,Tt=function(e,r){if(r!==null&&e===null)return-1;if(e!==null&&r===null)return 1;if(typeof e=="number"&&isNaN(e))return-1;if(typeof r=="number"&&isNaN(r))return 1;var t=Number(e),a=Number(r);if(t<a)return-1;if(t>a)return 1;if(typeof e=="number"&&typeof r!="number")return-1;if(typeof r=="number"&&typeof e!="number")return 1;if(typeof e=="number"&&typeof r=="number")return 0;if(isNaN(a)&&!isNaN(t))return-1;if(isNaN(t)&&!isNaN(a))return 1;var o=String(e),i=String(r);if(o===i)return 0;if(!br.test(o)||!br.test(i))return o>i?1:-1;for(o=o.match(oa),i=i.match(oa);o.length&&i.length;){var s=o.shift(),u=i.shift();if(s!==u)return br.test(s)&&br.test(u)?s.replace(ia,".0")-u.replace(ia,".0"):s>u?1:-1}return o.length-i.length},Qo=function(e){var r={},t={};for(var a in e){var o=e[a];r[o]=a,typeof o=="string"&&(t[o.toLowerCase()]=a)}return function(i,s){return i in r&&s in r?r[i]-r[s]:i in r?-1:s in r?1:i in t&&s in t?t[i]-t[s]:i in t?-1:s in t?1:Tt(i,s)}},Mn=function(e,r){if(e){if(typeof e=="function"){var t=e(r);if(typeof t=="function")return t}else if(r in e)return e[r]}return Tt},ze=Rr(),ir=Rr({digitsAfterDecimal:0}),Dt=Rr({digitsAfterDecimal:1,scaler:100,suffix:"%"}),Se={count:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ir;return function(){return function(){return{count:0,push:function(){this.count++},value:function(){return this.count},format:e}}}},uniques:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ir;return function(t){var a=It(t,1),o=a[0];return function(){return{uniq:[],push:function(s){Array.from(this.uniq).includes(s[o])||this.uniq.push(s[o])},value:function(){return e(this.uniq)},format:r,numInputs:typeof o<"u"?0:1}}}},sum:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ze;return function(r){var t=It(r,1),a=t[0];return function(){return{sum:0,push:function(i){isNaN(parseFloat(i[a]))||(this.sum+=parseFloat(i[a]))},value:function(){return this.sum},format:e,numInputs:typeof a<"u"?0:1}}}},extremes:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ze;return function(t){var a=It(t,1),o=a[0];return function(i){return{val:null,sorter:Mn(typeof i<"u"?i.sorters:null,o),push:function(u){var c=u[o];["min","max"].includes(e)&&(c=parseFloat(c),isNaN(c)||(this.val=Math[e](c,this.val!==null?this.val:c))),e==="first"&&this.sorter(c,this.val!==null?this.val:c)<=0&&(this.val=c),e==="last"&&this.sorter(c,this.val!==null?this.val:c)>=0&&(this.val=c)},value:function(){return this.val},format:function(u){return isNaN(u)?u:r(u)},numInputs:typeof o<"u"?0:1}}}},quantile:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ze;return function(t){var a=It(t,1),o=a[0];return function(){return{vals:[],push:function(s){var u=parseFloat(s[o]);isNaN(u)||this.vals.push(u)},value:function(){if(this.vals.length===0)return null;this.vals.sort(function(u,c){return u-c});var s=(this.vals.length-1)*e;return(this.vals[Math.floor(s)]+this.vals[Math.ceil(s)])/2},format:r,numInputs:typeof o<"u"?0:1}}}},runningStat:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"mean",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ze;return function(a){var o=It(a,1),i=o[0];return function(){return{n:0,m:0,s:0,push:function(u){var c=parseFloat(u[i]);if(!isNaN(c)){this.n+=1,this.n===1&&(this.m=c);var g=this.m+(c-this.m)/this.n;this.s=this.s+(c-this.m)*(c-g),this.m=g}},value:function(){if(e==="mean")return this.n===0?NaN:this.m;if(this.n<=r)return 0;switch(e){case"var":return this.s/(this.n-r);case"stdev":return Math.sqrt(this.s/(this.n-r));default:throw new Error("unknown mode for runningStat")}},format:t,numInputs:typeof i<"u"?0:1}}}},sumOverSum:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ze;return function(r){var t=It(r,2),a=t[0],o=t[1];return function(){return{sumNum:0,sumDenom:0,push:function(s){isNaN(parseFloat(s[a]))||(this.sumNum+=parseFloat(s[a])),isNaN(parseFloat(s[o]))||(this.sumDenom+=parseFloat(s[o]))},value:function(){return this.sumNum/this.sumDenom},format:e,numInputs:typeof a<"u"&&typeof o<"u"?0:2}}}},fractionOf:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"total",t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Dt;return function(){for(var a=arguments.length,o=Array(a),i=0;i<a;i++)o[i]=arguments[i];return function(s,u,c){return{selector:{total:[[],[]],row:[u,[]],col:[[],c]}[r],inner:e.apply(void 0,Qr(Array.from(o||[])))(s,u,c),push:function(m){this.inner.push(m)},format:t,value:function(){return this.inner.value()/s.getAggregator.apply(s,Qr(Array.from(this.selector||[]))).inner.value()},numInputs:e.apply(void 0,Qr(Array.from(o||[])))().numInputs}}}}};Se.countUnique=function(n){return Se.uniques(function(e){return e.length},n)};Se.listUnique=function(n){return Se.uniques(function(e){return e.join(n)},function(e){return e})};Se.max=function(n){return Se.extremes("max",n)};Se.min=function(n){return Se.extremes("min",n)};Se.first=function(n){return Se.extremes("first",n)};Se.last=function(n){return Se.extremes("last",n)};Se.median=function(n){return Se.quantile(.5,n)};Se.average=function(n){return Se.runningStat("mean",1,n)};Se.var=function(n,e){return Se.runningStat("var",n,e)};Se.stdev=function(n,e){return Se.runningStat("stdev",n,e)};var jn=function(n){return{Count:n.count(ir),"Count Unique Values":n.countUnique(ir),"List Unique Values":n.listUnique(", "),Sum:n.sum(ze),"Integer Sum":n.sum(ir),Average:n.average(ze),Median:n.median(ze),"Sample Variance":n.var(1,ze),"Sample Standard Deviation":n.stdev(1,ze),Minimum:n.min(ze),Maximum:n.max(ze),First:n.first(ze),Last:n.last(ze),"Sum over Sum":n.sumOverSum(ze),"Sum as Fraction of Total":n.fractionOf(n.sum(),"total",Dt),"Sum as Fraction of Rows":n.fractionOf(n.sum(),"row",Dt),"Sum as Fraction of Columns":n.fractionOf(n.sum(),"col",Dt),"Count as Fraction of Total":n.fractionOf(n.count(),"total",Dt),"Count as Fraction of Rows":n.fractionOf(n.count(),"row",Dt),"Count as Fraction of Columns":n.fractionOf(n.count(),"col",Dt)}}(Se),ei={en:{aggregators:jn,localeStrings:{renderError:"An error occurred rendering the PivotTable results.",computeError:"An error occurred computing the PivotTable results.",uiRenderError:"An error occurred rendering the PivotTable UI.",selectAll:"Select All",selectNone:"Select None",tooMany:"(too many to list)",filterResults:"Filter values",apply:"Apply",cancel:"Cancel",totals:"Totals",vs:"vs",by:"by"}}},ti=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],ri=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],Kt=function(e){return("0"+e).substr(-2,2)},ni={bin:function(e,r){return function(t){return t[e]-t[e]%r}},dateFormat:function(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:ti,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:ri,i=t?"UTC":"";return function(s){var u=new Date(Date.parse(s[e]));return isNaN(u)?"":r.replace(/%(.)/g,function(c,g){switch(g){case"y":return u["get"+i+"FullYear"]();case"m":return Kt(u["get"+i+"Month"]()+1);case"n":return a[u["get"+i+"Month"]()];case"d":return Kt(u["get"+i+"Date"]());case"w":return o[u["get"+i+"Day"]()];case"x":return u["get"+i+"Day"]();case"H":return Kt(u["get"+i+"Hours"]());case"M":return Kt(u["get"+i+"Minutes"]());case"S":return Kt(u["get"+i+"Seconds"]());default:return"%"+g}})}}},Lr=function(){function n(){var e=this,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Zo(this,n),this.props=Object.assign({},n.defaultProps,r),he.default.checkPropTypes(n.propTypes,this.props,"prop","PivotData"),this.aggregator=this.props.aggregators[this.props.aggregatorName](this.props.vals),this.tree={},this.rowKeys=[],this.colKeys=[],this.rowTotals={},this.colTotals={},this.allTotal=this.aggregator(this,[],[]),this.sorted=!1,n.forEachRecord(this.props.data,this.props.derivedAttributes,function(t){e.filter(t)&&e.processRecord(t)})}return Go(n,[{key:"filter",value:function(r){for(var t in this.props.valueFilter)if(r[t]in this.props.valueFilter[t])return!1;return!0}},{key:"forEachMatchingRecord",value:function(r,t){var a=this;return n.forEachRecord(this.props.data,this.props.derivedAttributes,function(o){if(a.filter(o)){for(var i in r){var s=r[i];if(s!==(i in o?o[i]:"null"))return}t(o)}})}},{key:"arrSort",value:function(r){var t=this,a=void 0,o=function(){var i=[],s=!0,u=!1,c=void 0;try{for(var g=Array.from(r)[Symbol.iterator](),m;!(s=(m=g.next()).done);s=!0)a=m.value,i.push(Mn(t.props.sorters,a))}catch(b){u=!0,c=b}finally{try{!s&&g.return&&g.return()}finally{if(u)throw c}}return i}();return function(i,s){var u=!0,c=!1,g=void 0;try{for(var m=Object.keys(o||{})[Symbol.iterator](),b;!(u=(b=m.next()).done);u=!0){var O=b.value,D=o[O],E=D(i[O],s[O]);if(E!==0)return E}}catch(v){c=!0,g=v}finally{try{!u&&m.return&&m.return()}finally{if(c)throw g}}return 0}}},{key:"sortKeys",value:function(){var r=this;if(!this.sorted){this.sorted=!0;var t=function(o,i){return r.getAggregator(o,i).value()};switch(this.props.rowOrder){case"value_a_to_z":this.rowKeys.sort(function(a,o){return Tt(t(a,[]),t(o,[]))});break;case"value_z_to_a":this.rowKeys.sort(function(a,o){return-Tt(t(a,[]),t(o,[]))});break;default:this.rowKeys.sort(this.arrSort(this.props.rows))}switch(this.props.colOrder){case"value_a_to_z":this.colKeys.sort(function(a,o){return Tt(t([],a),t([],o))});break;case"value_z_to_a":this.colKeys.sort(function(a,o){return-Tt(t([],a),t([],o))});break;default:this.colKeys.sort(this.arrSort(this.props.cols))}}}},{key:"getColKeys",value:function(){return this.sortKeys(),this.colKeys}},{key:"getRowKeys",value:function(){return this.sortKeys(),this.rowKeys}},{key:"processRecord",value:function(r){var t=[],a=[],o=!0,i=!1,s=void 0;try{for(var u=Array.from(this.props.cols)[Symbol.iterator](),c;!(o=(c=u.next()).done);o=!0){var g=c.value;t.push(g in r?r[g]:"null")}}catch(w){i=!0,s=w}finally{try{!o&&u.return&&u.return()}finally{if(i)throw s}}var m=!0,b=!1,O=void 0;try{for(var D=Array.from(this.props.rows)[Symbol.iterator](),E;!(m=(E=D.next()).done);m=!0){var v=E.value;a.push(v in r?r[v]:"null")}}catch(w){b=!0,O=w}finally{try{!m&&D.return&&D.return()}finally{if(b)throw O}}var S=a.join("\0"),_=t.join("\0");this.allTotal.push(r),a.length!==0&&(this.rowTotals[S]||(this.rowKeys.push(a),this.rowTotals[S]=this.aggregator(this,a,[])),this.rowTotals[S].push(r)),t.length!==0&&(this.colTotals[_]||(this.colKeys.push(t),this.colTotals[_]=this.aggregator(this,[],t)),this.colTotals[_].push(r)),t.length!==0&&a.length!==0&&(this.tree[S]||(this.tree[S]={}),this.tree[S][_]||(this.tree[S][_]=this.aggregator(this,a,t)),this.tree[S][_].push(r))}},{key:"getAggregator",value:function(r,t){var a=void 0,o=r.join("\0"),i=t.join("\0");return r.length===0&&t.length===0?a=this.allTotal:r.length===0?a=this.colTotals[i]:t.length===0?a=this.rowTotals[o]:a=this.tree[o][i],a||{value:function(){return null},format:function(){return""}}}}]),n}();Lr.forEachRecord=function(n,e,r){var t=void 0,a=void 0;if(Object.getOwnPropertyNames(e).length===0?t=r:t=function(i){for(var s in e){var u=e[s](i);u!==null&&(i[s]=u)}return r(i)},typeof n=="function")return n(t);if(Array.isArray(n))return Array.isArray(n[0])?function(){var o=[],i=!0,s=!1,u=void 0;try{for(var c=Object.keys(n||{})[Symbol.iterator](),g;!(i=(g=c.next()).done);i=!0){var m=g.value,b=n[m];if(m>0){a={};var O=!0,D=!1,E=void 0;try{for(var v=Object.keys(n[0]||{})[Symbol.iterator](),S;!(O=(S=v.next()).done);O=!0){var _=S.value,w=n[0][_];a[w]=b[_]}}catch(C){D=!0,E=C}finally{try{!O&&v.return&&v.return()}finally{if(D)throw E}}o.push(t(a))}}}catch(C){s=!0,u=C}finally{try{!i&&c.return&&c.return()}finally{if(s)throw u}}return o}():function(){var o=[],i=!0,s=!1,u=void 0;try{for(var c=Array.from(n)[Symbol.iterator](),g;!(i=(g=c.next()).done);i=!0)a=g.value,o.push(t(a))}catch(m){s=!0,u=m}finally{try{!i&&c.return&&c.return()}finally{if(s)throw u}}return o}();throw new Error("unknown input format")};Lr.defaultProps={aggregators:jn,cols:[],rows:[],vals:[],aggregatorName:"Count",sorters:{},valueFilter:{},rowOrder:"key_a_to_z",colOrder:"key_a_to_z",derivedAttributes:{}};Lr.propTypes={data:he.default.oneOfType([he.default.array,he.default.object,he.default.func]).isRequired,aggregatorName:he.default.string,cols:he.default.arrayOf(he.default.string),rows:he.default.arrayOf(he.default.string),vals:he.default.arrayOf(he.default.string),valueFilter:he.default.objectOf(he.default.objectOf(he.default.bool)),sorters:he.default.oneOfType([he.default.func,he.default.objectOf(he.default.func)]),derivedAttributes:he.default.objectOf(he.default.func),rowOrder:he.default.oneOf(["key_a_to_z","value_a_to_z","value_z_to_a"]),colOrder:he.default.oneOf(["key_a_to_z","value_a_to_z","value_z_to_a"])};be.aggregatorTemplates=Se;be.aggregators=jn;be.derivers=ni;be.locales=ei;be.naturalSort=Tt;be.numberFormat=Rr;be.getSort=Mn;be.sortAs=Qo;be.PivotData=Lr;var yn={exports:{}},bn={exports:{}};(function(n,e){Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function v(S,_){for(var w=0;w<_.length;w++){var C=_[w];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(S,C.key,C)}}return function(S,_,w){return _&&v(S.prototype,_),w&&v(S,w),S}}(),t=Fe,a=u(t),o=pr,i=u(o),s=be;function u(v){return v&&v.__esModule?v:{default:v}}function c(v,S){if(!(v instanceof S))throw new TypeError("Cannot call a class as a function")}function g(v,S){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:v}function m(v,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);v.prototype=Object.create(S&&S.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(v,S):v.__proto__=S)}var b=function(S,_,w){var C=void 0;if(_!==0){var F=void 0,k=void 0,J=!0;for(C=0,k=w,F=k>=0;F?C<=k:C>=k;F?C++:C--)S[_-1][C]!==S[_][C]&&(J=!1);if(J)return-1}for(var q=0;_+q<S.length;){var Y=void 0,Q=void 0,ae=!1;for(C=0,Q=w,Y=Q>=0;Y?C<=Q:C>=Q;Y?C++:C--)S[_][C]!==S[_+q][C]&&(ae=!0);if(ae)break;q++}return q};function O(v){var S=Math.min.apply(Math,v),_=Math.max.apply(Math,v);return function(w){var C=255-Math.round(255*(w-S)/(_-S));return{backgroundColor:"rgb(255,"+C+","+C+")"}}}function D(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},S=function(_){m(w,_);function w(){return c(this,w),g(this,(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments))}return r(w,[{key:"render",value:function(){var F=this,k=new s.PivotData(this.props),J=k.props.cols,q=k.props.rows,Y=k.getRowKeys(),Q=k.getColKeys(),ae=k.getAggregator([],[]),Ie=function(){},A=function(){},Re=function(){};if(v.heatmapMode){var Oe=this.props.tableColorScaleGenerator,ve=Q.map(function(G){return k.getAggregator([],G).value()});A=Oe(ve);var ie=Y.map(function(G){return k.getAggregator(G,[]).value()});if(Re=Oe(ie),v.heatmapMode==="full"){var X=[];Y.map(function(G){return Q.map(function(ee){return X.push(k.getAggregator(G,ee).value())})});var Le=Oe(X);Ie=function(ee,fe,Ee){return Le(Ee)}}else if(v.heatmapMode==="row"){var Ce={};Y.map(function(G){var ee=Q.map(function(fe){return k.getAggregator(G,fe).value()});Ce[G]=Oe(ee)}),Ie=function(ee,fe,Ee){return Ce[ee](Ee)}}else if(v.heatmapMode==="col"){var Qe={};Q.map(function(G){var ee=Y.map(function(fe){return k.getAggregator(fe,G).value()});Qe[G]=Oe(ee)}),Ie=function(ee,fe,Ee){return Qe[fe](Ee)}}}var Be=this.props.tableOptions&&this.props.tableOptions.clickCallback?function(G,ee,fe){var Ee={},Ne=!0,ke=!1,Bt=void 0;try{for(var mt=Object.keys(J||{})[Symbol.iterator](),Vt;!(Ne=(Vt=mt.next()).done);Ne=!0){var ut=Vt.value,Ht=J[ut];fe[ut]!==null&&(Ee[Ht]=fe[ut])}}catch(vt){ke=!0,Bt=vt}finally{try{!Ne&&mt.return&&mt.return()}finally{if(ke)throw Bt}}var At=!0,Yt=!1,at=void 0;try{for(var Xt=Object.keys(q||{})[Symbol.iterator](),mr;!(At=(mr=Xt.next()).done);At=!0){var qt=mr.value,Ht=q[qt];ee[qt]!==null&&(Ee[Ht]=ee[qt])}}catch(vt){Yt=!0,at=vt}finally{try{!At&&Xt.return&&Xt.return()}finally{if(Yt)throw at}}return function(vt){return F.props.tableOptions.clickCallback(vt,G,Ee,k)}}:null;return a.default.createElement("table",{className:"pvtTable"},a.default.createElement("thead",null,J.map(function(G,ee){return a.default.createElement("tr",{key:"colAttr"+ee},ee===0&&q.length!==0&&a.default.createElement("th",{colSpan:q.length,rowSpan:J.length}),a.default.createElement("th",{className:"pvtAxisLabel"},G),Q.map(function(fe,Ee){var Ne=b(Q,Ee,ee);return Ne===-1?null:a.default.createElement("th",{className:"pvtColLabel",key:"colKey"+Ee,colSpan:Ne,rowSpan:ee===J.length-1&&q.length!==0?2:1},fe[ee])}),ee===0&&a.default.createElement("th",{className:"pvtTotalLabel",rowSpan:J.length+(q.length===0?0:1)},"Totals"))}),q.length!==0&&a.default.createElement("tr",null,q.map(function(G,ee){return a.default.createElement("th",{className:"pvtAxisLabel",key:"rowAttr"+ee},G)}),a.default.createElement("th",{className:"pvtTotalLabel"},J.length===0?"Totals":null))),a.default.createElement("tbody",null,Y.map(function(G,ee){var fe=k.getAggregator(G,[]);return a.default.createElement("tr",{key:"rowKeyRow"+ee},G.map(function(Ee,Ne){var ke=b(Y,ee,Ne);return ke===-1?null:a.default.createElement("th",{key:"rowKeyLabel"+ee+"-"+Ne,className:"pvtRowLabel",rowSpan:ke,colSpan:Ne===q.length-1&&J.length!==0?2:1},Ee)}),Q.map(function(Ee,Ne){var ke=k.getAggregator(G,Ee);return a.default.createElement("td",{className:"pvtVal",key:"pvtVal"+ee+"-"+Ne,onClick:Be&&Be(ke.value(),G,Ee),style:Ie(G,Ee,ke.value())},ke.format(ke.value()))}),a.default.createElement("td",{className:"pvtTotal",onClick:Be&&Be(fe.value(),G,[null]),style:Re(fe.value())},fe.format(fe.value())))}),a.default.createElement("tr",null,a.default.createElement("th",{className:"pvtTotalLabel",colSpan:q.length+(J.length===0?0:1)},"Totals"),Q.map(function(G,ee){var fe=k.getAggregator([],G);return a.default.createElement("td",{className:"pvtTotal",key:"total"+ee,onClick:Be&&Be(fe.value(),[null],G),style:A(fe.value())},fe.format(fe.value()))}),a.default.createElement("td",{onClick:Be&&Be(ae.value(),[null],[null]),className:"pvtGrandTotal"},ae.format(ae.value())))))}}]),w}(a.default.PureComponent);return S.defaultProps=s.PivotData.defaultProps,S.propTypes=s.PivotData.propTypes,S.defaultProps.tableColorScaleGenerator=O,S.defaultProps.tableOptions={},S.propTypes.tableColorScaleGenerator=i.default.func,S.propTypes.tableOptions=i.default.object,S}var E=function(v){m(S,v);function S(){return c(this,S),g(this,(S.__proto__||Object.getPrototypeOf(S)).apply(this,arguments))}return r(S,[{key:"render",value:function(){var w=new s.PivotData(this.props),C=w.getRowKeys(),F=w.getColKeys();C.length===0&&C.push([]),F.length===0&&F.push([]);var k=w.props.rows.map(function(q){return q});F.length===1&&F[0].length===0?k.push(this.props.aggregatorName):F.map(function(q){return k.push(q.join("-"))});var J=C.map(function(q){var Y=q.map(function(Q){return Q});return F.map(function(Q){var ae=w.getAggregator(q,Q).value();Y.push(ae||"")}),Y});return J.unshift(k),a.default.createElement("textarea",{value:J.map(function(q){return q.join("	")}).join(`
`),style:{width:window.innerWidth/2,height:window.innerHeight/2},readOnly:!0})}}]),S}(a.default.PureComponent);E.defaultProps=s.PivotData.defaultProps,E.propTypes=s.PivotData.propTypes,e.default={Table:D(),"Table Heatmap":D({heatmapMode:"full"}),"Table Col Heatmap":D({heatmapMode:"col"}),"Table Row Heatmap":D({heatmapMode:"row"}),"Exportable TSV":E},n.exports=e.default})(bn,bn.exports);var Ca=bn.exports;const ai=Eo(Ca);(function(n,e){Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function E(v,S){for(var _=0;_<S.length;_++){var w=S[_];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(v,w.key,w)}}return function(v,S,_){return S&&E(v.prototype,S),_&&E(v,_),v}}(),t=Fe,a=g(t),o=pr,i=g(o),s=be,u=Ca,c=g(u);function g(E){return E&&E.__esModule?E:{default:E}}function m(E,v){if(!(E instanceof v))throw new TypeError("Cannot call a class as a function")}function b(E,v){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:E}function O(E,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);E.prototype=Object.create(v&&v.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(E,v):E.__proto__=v)}var D=function(E){O(v,E);function v(){return m(this,v),b(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return r(v,[{key:"render",value:function(){var _=this.props.renderers[this.props.rendererName in this.props.renderers?this.props.rendererName:Object.keys(this.props.renderers)[0]];return a.default.createElement(_,this.props)}}]),v}(a.default.PureComponent);D.propTypes=Object.assign({},s.PivotData.propTypes,{rendererName:i.default.string,renderers:i.default.objectOf(i.default.func)}),D.defaultProps=Object.assign({},s.PivotData.defaultProps,{rendererName:"Table",renderers:c.default}),e.default=D,n.exports=e.default})(yn,yn.exports);var oi=yn.exports,Ur={};/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function sa(n,e){var r=Object.keys(n);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(n);e&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),r.push.apply(r,t)}return r}function nt(n){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?sa(Object(r),!0).forEach(function(t){ii(n,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):sa(Object(r)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))})}return n}function Tr(n){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Tr=function(e){return typeof e}:Tr=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tr(n)}function ii(n,e,r){return e in n?Object.defineProperty(n,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[e]=r,n}function We(){return We=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(n[t]=r[t])}return n},We.apply(this,arguments)}function si(n,e){if(n==null)return{};var r={},t=Object.keys(n),a,o;for(o=0;o<t.length;o++)a=t[o],!(e.indexOf(a)>=0)&&(r[a]=n[a]);return r}function li(n,e){if(n==null)return{};var r=si(n,e),t,a;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(a=0;a<o.length;a++)t=o[a],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(n,t)&&(r[t]=n[t])}return r}function ui(n){return fi(n)||ci(n)||di(n)||pi()}function fi(n){if(Array.isArray(n))return wn(n)}function ci(n){if(typeof Symbol<"u"&&n[Symbol.iterator]!=null||n["@@iterator"]!=null)return Array.from(n)}function di(n,e){if(n){if(typeof n=="string")return wn(n,e);var r=Object.prototype.toString.call(n).slice(8,-1);if(r==="Object"&&n.constructor&&(r=n.constructor.name),r==="Map"||r==="Set")return Array.from(n);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wn(n,e)}}function wn(n,e){(e==null||e>n.length)&&(e=n.length);for(var r=0,t=new Array(e);r<e;r++)t[r]=n[r];return t}function pi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var hi="1.15.6";function st(n){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(n)}var lt=st(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),hr=st(/Edge/i),la=st(/firefox/i),sr=st(/safari/i)&&!st(/chrome/i)&&!st(/android/i),Fn=st(/iP(ad|od|hone)/i),Aa=st(/chrome/i)&&st(/android/i),Pa={capture:!1,passive:!1};function H(n,e,r){n.addEventListener(e,r,!lt&&Pa)}function V(n,e,r){n.removeEventListener(e,r,!lt&&Pa)}function Ir(n,e){if(e){if(e[0]===">"&&(e=e.substring(1)),n)try{if(n.matches)return n.matches(e);if(n.msMatchesSelector)return n.msMatchesSelector(e);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(e)}catch{return!1}return!1}}function Ia(n){return n.host&&n!==document&&n.host.nodeType?n.host:n.parentNode}function Ye(n,e,r,t){if(n){r=r||document;do{if(e!=null&&(e[0]===">"?n.parentNode===r&&Ir(n,e):Ir(n,e))||t&&n===r)return n;if(n===r)break}while(n=Ia(n))}return null}var ua=/\s+/g;function me(n,e,r){if(n&&e)if(n.classList)n.classList[r?"add":"remove"](e);else{var t=(" "+n.className+" ").replace(ua," ").replace(" "+e+" "," ");n.className=(t+(r?" "+e:"")).replace(ua," ")}}function I(n,e,r){var t=n&&n.style;if(t){if(r===void 0)return document.defaultView&&document.defaultView.getComputedStyle?r=document.defaultView.getComputedStyle(n,""):n.currentStyle&&(r=n.currentStyle),e===void 0?r:r[e];!(e in t)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),t[e]=r+(typeof r=="string"?"":"px")}}function Ct(n,e){var r="";if(typeof n=="string")r=n;else do{var t=I(n,"transform");t&&t!=="none"&&(r=t+" "+r)}while(!e&&(n=n.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(r)}function Na(n,e,r){if(n){var t=n.getElementsByTagName(e),a=0,o=t.length;if(r)for(;a<o;a++)r(t[a],a);return t}return[]}function rt(){var n=document.scrollingElement;return n||document.documentElement}function ce(n,e,r,t,a){if(!(!n.getBoundingClientRect&&n!==window)){var o,i,s,u,c,g,m;if(n!==window&&n.parentNode&&n!==rt()?(o=n.getBoundingClientRect(),i=o.top,s=o.left,u=o.bottom,c=o.right,g=o.height,m=o.width):(i=0,s=0,u=window.innerHeight,c=window.innerWidth,g=window.innerHeight,m=window.innerWidth),(e||r)&&n!==window&&(a=a||n.parentNode,!lt))do if(a&&a.getBoundingClientRect&&(I(a,"transform")!=="none"||r&&I(a,"position")!=="static")){var b=a.getBoundingClientRect();i-=b.top+parseInt(I(a,"border-top-width")),s-=b.left+parseInt(I(a,"border-left-width")),u=i+o.height,c=s+o.width;break}while(a=a.parentNode);if(t&&n!==window){var O=Ct(a||n),D=O&&O.a,E=O&&O.d;O&&(i/=E,s/=D,m/=D,g/=E,u=i+g,c=s+m)}return{top:i,left:s,bottom:u,right:c,width:m,height:g}}}function fa(n,e,r){for(var t=gt(n,!0),a=ce(n)[e];t;){var o=ce(t)[r],i=void 0;if(i=a>=o,!i)return t;if(t===rt())break;t=gt(t,!1)}return!1}function $t(n,e,r,t){for(var a=0,o=0,i=n.children;o<i.length;){if(i[o].style.display!=="none"&&i[o]!==U.ghost&&(t||i[o]!==U.dragged)&&Ye(i[o],r.draggable,n,!1)){if(a===e)return i[o];a++}o++}return null}function Rn(n,e){for(var r=n.lastElementChild;r&&(r===U.ghost||I(r,"display")==="none"||e&&!Ir(r,e));)r=r.previousElementSibling;return r||null}function we(n,e){var r=0;if(!n||!n.parentNode)return-1;for(;n=n.previousElementSibling;)n.nodeName.toUpperCase()!=="TEMPLATE"&&n!==U.clone&&(!e||Ir(n,e))&&r++;return r}function ca(n){var e=0,r=0,t=rt();if(n)do{var a=Ct(n),o=a.a,i=a.d;e+=n.scrollLeft*o,r+=n.scrollTop*i}while(n!==t&&(n=n.parentNode));return[e,r]}function gi(n,e){for(var r in n)if(n.hasOwnProperty(r)){for(var t in e)if(e.hasOwnProperty(t)&&e[t]===n[r][t])return Number(r)}return-1}function gt(n,e){if(!n||!n.getBoundingClientRect)return rt();var r=n,t=!1;do if(r.clientWidth<r.scrollWidth||r.clientHeight<r.scrollHeight){var a=I(r);if(r.clientWidth<r.scrollWidth&&(a.overflowX=="auto"||a.overflowX=="scroll")||r.clientHeight<r.scrollHeight&&(a.overflowY=="auto"||a.overflowY=="scroll")){if(!r.getBoundingClientRect||r===document.body)return rt();if(t||e)return r;t=!0}}while(r=r.parentNode);return rt()}function mi(n,e){if(n&&e)for(var r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);return n}function en(n,e){return Math.round(n.top)===Math.round(e.top)&&Math.round(n.left)===Math.round(e.left)&&Math.round(n.height)===Math.round(e.height)&&Math.round(n.width)===Math.round(e.width)}var lr;function ka(n,e){return function(){if(!lr){var r=arguments,t=this;r.length===1?n.call(t,r[0]):n.apply(t,r),lr=setTimeout(function(){lr=void 0},e)}}}function vi(){clearTimeout(lr),lr=void 0}function Ma(n,e,r){n.scrollLeft+=e,n.scrollTop+=r}function Ln(n){var e=window.Polymer,r=window.jQuery||window.Zepto;return e&&e.dom?e.dom(n).cloneNode(!0):r?r(n).clone(!0)[0]:n.cloneNode(!0)}function da(n,e){I(n,"position","absolute"),I(n,"top",e.top),I(n,"left",e.left),I(n,"width",e.width),I(n,"height",e.height)}function tn(n){I(n,"position",""),I(n,"top",""),I(n,"left",""),I(n,"width",""),I(n,"height","")}function ja(n,e,r){var t={};return Array.from(n.children).forEach(function(a){var o,i,s,u;if(!(!Ye(a,e.draggable,n,!1)||a.animated||a===r)){var c=ce(a);t.left=Math.min((o=t.left)!==null&&o!==void 0?o:1/0,c.left),t.top=Math.min((i=t.top)!==null&&i!==void 0?i:1/0,c.top),t.right=Math.max((s=t.right)!==null&&s!==void 0?s:-1/0,c.right),t.bottom=Math.max((u=t.bottom)!==null&&u!==void 0?u:-1/0,c.bottom)}}),t.width=t.right-t.left,t.height=t.bottom-t.top,t.x=t.left,t.y=t.top,t}var Pe="Sortable"+new Date().getTime();function yi(){var n=[],e;return{captureAnimationState:function(){if(n=[],!!this.options.animation){var t=[].slice.call(this.el.children);t.forEach(function(a){if(!(I(a,"display")==="none"||a===U.ghost)){n.push({target:a,rect:ce(a)});var o=nt({},n[n.length-1].rect);if(a.thisAnimationDuration){var i=Ct(a,!0);i&&(o.top-=i.f,o.left-=i.e)}a.fromRect=o}})}},addAnimationState:function(t){n.push(t)},removeAnimationState:function(t){n.splice(gi(n,{target:t}),1)},animateAll:function(t){var a=this;if(!this.options.animation){clearTimeout(e),typeof t=="function"&&t();return}var o=!1,i=0;n.forEach(function(s){var u=0,c=s.target,g=c.fromRect,m=ce(c),b=c.prevFromRect,O=c.prevToRect,D=s.rect,E=Ct(c,!0);E&&(m.top-=E.f,m.left-=E.e),c.toRect=m,c.thisAnimationDuration&&en(b,m)&&!en(g,m)&&(D.top-m.top)/(D.left-m.left)===(g.top-m.top)/(g.left-m.left)&&(u=wi(D,b,O,a.options)),en(m,g)||(c.prevFromRect=g,c.prevToRect=m,u||(u=a.options.animation),a.animate(c,D,m,u)),u&&(o=!0,i=Math.max(i,u),clearTimeout(c.animationResetTimer),c.animationResetTimer=setTimeout(function(){c.animationTime=0,c.prevFromRect=null,c.fromRect=null,c.prevToRect=null,c.thisAnimationDuration=null},u),c.thisAnimationDuration=u)}),clearTimeout(e),o?e=setTimeout(function(){typeof t=="function"&&t()},i):typeof t=="function"&&t(),n=[]},animate:function(t,a,o,i){if(i){I(t,"transition",""),I(t,"transform","");var s=Ct(this.el),u=s&&s.a,c=s&&s.d,g=(a.left-o.left)/(u||1),m=(a.top-o.top)/(c||1);t.animatingX=!!g,t.animatingY=!!m,I(t,"transform","translate3d("+g+"px,"+m+"px,0)"),this.forRepaintDummy=bi(t),I(t,"transition","transform "+i+"ms"+(this.options.easing?" "+this.options.easing:"")),I(t,"transform","translate3d(0,0,0)"),typeof t.animated=="number"&&clearTimeout(t.animated),t.animated=setTimeout(function(){I(t,"transition",""),I(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},i)}}}}function bi(n){return n.offsetWidth}function wi(n,e,r,t){return Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))/Math.sqrt(Math.pow(e.top-r.top,2)+Math.pow(e.left-r.left,2))*t.animation}var Nt=[],rn={initializeByDefault:!0},gr={mount:function(e){for(var r in rn)rn.hasOwnProperty(r)&&!(r in e)&&(e[r]=rn[r]);Nt.forEach(function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),Nt.push(e)},pluginEvent:function(e,r,t){var a=this;this.eventCanceled=!1,t.cancel=function(){a.eventCanceled=!0};var o=e+"Global";Nt.forEach(function(i){r[i.pluginName]&&(r[i.pluginName][o]&&r[i.pluginName][o](nt({sortable:r},t)),r.options[i.pluginName]&&r[i.pluginName][e]&&r[i.pluginName][e](nt({sortable:r},t)))})},initializePlugins:function(e,r,t,a){Nt.forEach(function(s){var u=s.pluginName;if(!(!e.options[u]&&!s.initializeByDefault)){var c=new s(e,r,e.options);c.sortable=e,c.options=e.options,e[u]=c,We(t,c.defaults)}});for(var o in e.options)if(e.options.hasOwnProperty(o)){var i=this.modifyOption(e,o,e.options[o]);typeof i<"u"&&(e.options[o]=i)}},getEventProperties:function(e,r){var t={};return Nt.forEach(function(a){typeof a.eventProperties=="function"&&We(t,a.eventProperties.call(r[a.pluginName],e))}),t},modifyOption:function(e,r,t){var a;return Nt.forEach(function(o){e[o.pluginName]&&o.optionListeners&&typeof o.optionListeners[r]=="function"&&(a=o.optionListeners[r].call(e[o.pluginName],t))}),a}};function rr(n){var e=n.sortable,r=n.rootEl,t=n.name,a=n.targetEl,o=n.cloneEl,i=n.toEl,s=n.fromEl,u=n.oldIndex,c=n.newIndex,g=n.oldDraggableIndex,m=n.newDraggableIndex,b=n.originalEvent,O=n.putSortable,D=n.extraEventProperties;if(e=e||r&&r[Pe],!!e){var E,v=e.options,S="on"+t.charAt(0).toUpperCase()+t.substr(1);window.CustomEvent&&!lt&&!hr?E=new CustomEvent(t,{bubbles:!0,cancelable:!0}):(E=document.createEvent("Event"),E.initEvent(t,!0,!0)),E.to=i||r,E.from=s||r,E.item=a||r,E.clone=o,E.oldIndex=u,E.newIndex=c,E.oldDraggableIndex=g,E.newDraggableIndex=m,E.originalEvent=b,E.pullMode=O?O.lastPutMode:void 0;var _=nt(nt({},D),gr.getEventProperties(t,e));for(var w in _)E[w]=_[w];r&&r.dispatchEvent(E),v[S]&&v[S].call(e,E)}}var Si=["evt"],Ve=function(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=t.evt,o=li(t,Si);gr.pluginEvent.bind(U)(e,r,nt({dragEl:x,parentEl:ye,ghostEl:B,rootEl:pe,nextEl:xt,lastDownEl:Or,cloneEl:ge,cloneHidden:ht,dragStarted:nr,putSortable:Ae,activeSortable:U.active,originalEvent:a,oldIndex:Lt,oldDraggableIndex:ur,newIndex:qe,newDraggableIndex:pt,hideGhostForTarget:Ua,unhideGhostForTarget:$a,cloneNowHidden:function(){ht=!0},cloneNowShown:function(){ht=!1},dispatchSortableEvent:function(s){$e({sortable:r,name:s,originalEvent:a})}},o))};function $e(n){rr(nt({putSortable:Ae,cloneEl:ge,targetEl:x,rootEl:pe,oldIndex:Lt,oldDraggableIndex:ur,newIndex:qe,newDraggableIndex:pt},n))}var x,ye,B,pe,xt,Or,ge,ht,Lt,qe,ur,pt,wr,Ae,Rt=!1,Nr=!1,kr=[],Et,Ke,nn,an,pa,ha,nr,kt,fr,cr=!1,Sr=!1,Cr,je,on=[],Sn=!1,Mr=[],$r=typeof document<"u",Er=Fn,ga=hr||lt?"cssFloat":"float",Ei=$r&&!Aa&&!Fn&&"draggable"in document.createElement("div"),Fa=function(){if($r){if(lt)return!1;var n=document.createElement("x");return n.style.cssText="pointer-events:auto",n.style.pointerEvents==="auto"}}(),Ra=function(e,r){var t=I(e),a=parseInt(t.width)-parseInt(t.paddingLeft)-parseInt(t.paddingRight)-parseInt(t.borderLeftWidth)-parseInt(t.borderRightWidth),o=$t(e,0,r),i=$t(e,1,r),s=o&&I(o),u=i&&I(i),c=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+ce(o).width,g=u&&parseInt(u.marginLeft)+parseInt(u.marginRight)+ce(i).width;if(t.display==="flex")return t.flexDirection==="column"||t.flexDirection==="column-reverse"?"vertical":"horizontal";if(t.display==="grid")return t.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&s.float&&s.float!=="none"){var m=s.float==="left"?"left":"right";return i&&(u.clear==="both"||u.clear===m)?"vertical":"horizontal"}return o&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||c>=a&&t[ga]==="none"||i&&t[ga]==="none"&&c+g>a)?"vertical":"horizontal"},_i=function(e,r,t){var a=t?e.left:e.top,o=t?e.right:e.bottom,i=t?e.width:e.height,s=t?r.left:r.top,u=t?r.right:r.bottom,c=t?r.width:r.height;return a===s||o===u||a+i/2===s+c/2},Di=function(e,r){var t;return kr.some(function(a){var o=a[Pe].options.emptyInsertThreshold;if(!(!o||Rn(a))){var i=ce(a),s=e>=i.left-o&&e<=i.right+o,u=r>=i.top-o&&r<=i.bottom+o;if(s&&u)return t=a}}),t},La=function(e){function r(o,i){return function(s,u,c,g){var m=s.options.group.name&&u.options.group.name&&s.options.group.name===u.options.group.name;if(o==null&&(i||m))return!0;if(o==null||o===!1)return!1;if(i&&o==="clone")return o;if(typeof o=="function")return r(o(s,u,c,g),i)(s,u,c,g);var b=(i?s:u).options.group.name;return o===!0||typeof o=="string"&&o===b||o.join&&o.indexOf(b)>-1}}var t={},a=e.group;(!a||Tr(a)!="object")&&(a={name:a}),t.name=a.name,t.checkPull=r(a.pull,!0),t.checkPut=r(a.put),t.revertClone=a.revertClone,e.group=t},Ua=function(){!Fa&&B&&I(B,"display","none")},$a=function(){!Fa&&B&&I(B,"display","")};$r&&!Aa&&document.addEventListener("click",function(n){if(Nr)return n.preventDefault(),n.stopPropagation&&n.stopPropagation(),n.stopImmediatePropagation&&n.stopImmediatePropagation(),Nr=!1,!1},!0);var _t=function(e){if(x){e=e.touches?e.touches[0]:e;var r=Di(e.clientX,e.clientY);if(r){var t={};for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.target=t.rootEl=r,t.preventDefault=void 0,t.stopPropagation=void 0,r[Pe]._onDragOver(t)}}},xi=function(e){x&&x.parentNode[Pe]._isOutsideThisEl(e.target)};function U(n,e){if(!(n&&n.nodeType&&n.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(n));this.el=n,this.options=e=We({},e),n[Pe]=this;var r={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(n.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Ra(n,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(i,s){i.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:U.supportPointer!==!1&&"PointerEvent"in window&&(!sr||Fn),emptyInsertThreshold:5};gr.initializePlugins(this,n,r);for(var t in r)!(t in e)&&(e[t]=r[t]);La(e);for(var a in this)a.charAt(0)==="_"&&typeof this[a]=="function"&&(this[a]=this[a].bind(this));this.nativeDraggable=e.forceFallback?!1:Ei,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?H(n,"pointerdown",this._onTapStart):(H(n,"mousedown",this._onTapStart),H(n,"touchstart",this._onTapStart)),this.nativeDraggable&&(H(n,"dragover",this),H(n,"dragenter",this)),kr.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),We(this,yi())}U.prototype={constructor:U,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(kt=null)},_getDirection:function(e,r){return typeof this.options.direction=="function"?this.options.direction.call(this,e,r,x):this.options.direction},_onTapStart:function(e){if(e.cancelable){var r=this,t=this.el,a=this.options,o=a.preventOnFilter,i=e.type,s=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,u=(s||e).target,c=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||u,g=a.filter;if(ki(t),!x&&!(/mousedown|pointerdown/.test(i)&&e.button!==0||a.disabled)&&!c.isContentEditable&&!(!this.nativeDraggable&&sr&&u&&u.tagName.toUpperCase()==="SELECT")&&(u=Ye(u,a.draggable,t,!1),!(u&&u.animated)&&Or!==u)){if(Lt=we(u),ur=we(u,a.draggable),typeof g=="function"){if(g.call(this,e,u,this)){$e({sortable:r,rootEl:c,name:"filter",targetEl:u,toEl:t,fromEl:t}),Ve("filter",r,{evt:e}),o&&e.preventDefault();return}}else if(g&&(g=g.split(",").some(function(m){if(m=Ye(c,m.trim(),t,!1),m)return $e({sortable:r,rootEl:m,name:"filter",targetEl:u,fromEl:t,toEl:t}),Ve("filter",r,{evt:e}),!0}),g)){o&&e.preventDefault();return}a.handle&&!Ye(c,a.handle,t,!1)||this._prepareDragStart(e,s,u)}}},_prepareDragStart:function(e,r,t){var a=this,o=a.el,i=a.options,s=o.ownerDocument,u;if(t&&!x&&t.parentNode===o){var c=ce(t);if(pe=o,x=t,ye=x.parentNode,xt=x.nextSibling,Or=t,wr=i.group,U.dragged=x,Et={target:x,clientX:(r||e).clientX,clientY:(r||e).clientY},pa=Et.clientX-c.left,ha=Et.clientY-c.top,this._lastX=(r||e).clientX,this._lastY=(r||e).clientY,x.style["will-change"]="all",u=function(){if(Ve("delayEnded",a,{evt:e}),U.eventCanceled){a._onDrop();return}a._disableDelayedDragEvents(),!la&&a.nativeDraggable&&(x.draggable=!0),a._triggerDragStart(e,r),$e({sortable:a,name:"choose",originalEvent:e}),me(x,i.chosenClass,!0)},i.ignore.split(",").forEach(function(g){Na(x,g.trim(),sn)}),H(s,"dragover",_t),H(s,"mousemove",_t),H(s,"touchmove",_t),i.supportPointer?(H(s,"pointerup",a._onDrop),!this.nativeDraggable&&H(s,"pointercancel",a._onDrop)):(H(s,"mouseup",a._onDrop),H(s,"touchend",a._onDrop),H(s,"touchcancel",a._onDrop)),la&&this.nativeDraggable&&(this.options.touchStartThreshold=4,x.draggable=!0),Ve("delayStart",this,{evt:e}),i.delay&&(!i.delayOnTouchOnly||r)&&(!this.nativeDraggable||!(hr||lt))){if(U.eventCanceled){this._onDrop();return}i.supportPointer?(H(s,"pointerup",a._disableDelayedDrag),H(s,"pointercancel",a._disableDelayedDrag)):(H(s,"mouseup",a._disableDelayedDrag),H(s,"touchend",a._disableDelayedDrag),H(s,"touchcancel",a._disableDelayedDrag)),H(s,"mousemove",a._delayedDragTouchMoveHandler),H(s,"touchmove",a._delayedDragTouchMoveHandler),i.supportPointer&&H(s,"pointermove",a._delayedDragTouchMoveHandler),a._dragStartTimer=setTimeout(u,i.delay)}else u()}},_delayedDragTouchMoveHandler:function(e){var r=e.touches?e.touches[0]:e;Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){x&&sn(x),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;V(e,"mouseup",this._disableDelayedDrag),V(e,"touchend",this._disableDelayedDrag),V(e,"touchcancel",this._disableDelayedDrag),V(e,"pointerup",this._disableDelayedDrag),V(e,"pointercancel",this._disableDelayedDrag),V(e,"mousemove",this._delayedDragTouchMoveHandler),V(e,"touchmove",this._delayedDragTouchMoveHandler),V(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,r){r=r||e.pointerType=="touch"&&e,!this.nativeDraggable||r?this.options.supportPointer?H(document,"pointermove",this._onTouchMove):r?H(document,"touchmove",this._onTouchMove):H(document,"mousemove",this._onTouchMove):(H(x,"dragend",this),H(pe,"dragstart",this._onDragStart));try{document.selection?Ar(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,r){if(Rt=!1,pe&&x){Ve("dragStarted",this,{evt:r}),this.nativeDraggable&&H(document,"dragover",xi);var t=this.options;!e&&me(x,t.dragClass,!1),me(x,t.ghostClass,!0),U.active=this,e&&this._appendGhost(),$e({sortable:this,name:"start",originalEvent:r})}else this._nulling()},_emulateDragOver:function(){if(Ke){this._lastX=Ke.clientX,this._lastY=Ke.clientY,Ua();for(var e=document.elementFromPoint(Ke.clientX,Ke.clientY),r=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Ke.clientX,Ke.clientY),e!==r);)r=e;if(x.parentNode[Pe]._isOutsideThisEl(e),r)do{if(r[Pe]){var t=void 0;if(t=r[Pe]._onDragOver({clientX:Ke.clientX,clientY:Ke.clientY,target:e,rootEl:r}),t&&!this.options.dragoverBubble)break}e=r}while(r=Ia(r));$a()}},_onTouchMove:function(e){if(Et){var r=this.options,t=r.fallbackTolerance,a=r.fallbackOffset,o=e.touches?e.touches[0]:e,i=B&&Ct(B,!0),s=B&&i&&i.a,u=B&&i&&i.d,c=Er&&je&&ca(je),g=(o.clientX-Et.clientX+a.x)/(s||1)+(c?c[0]-on[0]:0)/(s||1),m=(o.clientY-Et.clientY+a.y)/(u||1)+(c?c[1]-on[1]:0)/(u||1);if(!U.active&&!Rt){if(t&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<t)return;this._onDragStart(e,!0)}if(B){i?(i.e+=g-(nn||0),i.f+=m-(an||0)):i={a:1,b:0,c:0,d:1,e:g,f:m};var b="matrix(".concat(i.a,",").concat(i.b,",").concat(i.c,",").concat(i.d,",").concat(i.e,",").concat(i.f,")");I(B,"webkitTransform",b),I(B,"mozTransform",b),I(B,"msTransform",b),I(B,"transform",b),nn=g,an=m,Ke=o}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!B){var e=this.options.fallbackOnBody?document.body:pe,r=ce(x,!0,Er,!0,e),t=this.options;if(Er){for(je=e;I(je,"position")==="static"&&I(je,"transform")==="none"&&je!==document;)je=je.parentNode;je!==document.body&&je!==document.documentElement?(je===document&&(je=rt()),r.top+=je.scrollTop,r.left+=je.scrollLeft):je=rt(),on=ca(je)}B=x.cloneNode(!0),me(B,t.ghostClass,!1),me(B,t.fallbackClass,!0),me(B,t.dragClass,!0),I(B,"transition",""),I(B,"transform",""),I(B,"box-sizing","border-box"),I(B,"margin",0),I(B,"top",r.top),I(B,"left",r.left),I(B,"width",r.width),I(B,"height",r.height),I(B,"opacity","0.8"),I(B,"position",Er?"absolute":"fixed"),I(B,"zIndex","100000"),I(B,"pointerEvents","none"),U.ghost=B,e.appendChild(B),I(B,"transform-origin",pa/parseInt(B.style.width)*100+"% "+ha/parseInt(B.style.height)*100+"%")}},_onDragStart:function(e,r){var t=this,a=e.dataTransfer,o=t.options;if(Ve("dragStart",this,{evt:e}),U.eventCanceled){this._onDrop();return}Ve("setupClone",this),U.eventCanceled||(ge=Ln(x),ge.removeAttribute("id"),ge.draggable=!1,ge.style["will-change"]="",this._hideClone(),me(ge,this.options.chosenClass,!1),U.clone=ge),t.cloneId=Ar(function(){Ve("clone",t),!U.eventCanceled&&(t.options.removeCloneOnHide||pe.insertBefore(ge,x),t._hideClone(),$e({sortable:t,name:"clone"}))}),!r&&me(x,o.dragClass,!0),r?(Nr=!0,t._loopId=setInterval(t._emulateDragOver,50)):(V(document,"mouseup",t._onDrop),V(document,"touchend",t._onDrop),V(document,"touchcancel",t._onDrop),a&&(a.effectAllowed="move",o.setData&&o.setData.call(t,a,x)),H(document,"drop",t),I(x,"transform","translateZ(0)")),Rt=!0,t._dragStartId=Ar(t._dragStarted.bind(t,r,e)),H(document,"selectstart",t),nr=!0,window.getSelection().removeAllRanges(),sr&&I(document.body,"user-select","none")},_onDragOver:function(e){var r=this.el,t=e.target,a,o,i,s=this.options,u=s.group,c=U.active,g=wr===u,m=s.sort,b=Ae||c,O,D=this,E=!1;if(Sn)return;function v(X,Le){Ve(X,D,nt({evt:e,isOwner:g,axis:O?"vertical":"horizontal",revert:i,dragRect:a,targetRect:o,canSort:m,fromSortable:b,target:t,completed:_,onMove:function(Qe,Be){return _r(pe,r,x,a,Qe,ce(Qe),e,Be)},changed:w},Le))}function S(){v("dragOverAnimationCapture"),D.captureAnimationState(),D!==b&&b.captureAnimationState()}function _(X){return v("dragOverCompleted",{insertion:X}),X&&(g?c._hideClone():c._showClone(D),D!==b&&(me(x,Ae?Ae.options.ghostClass:c.options.ghostClass,!1),me(x,s.ghostClass,!0)),Ae!==D&&D!==U.active?Ae=D:D===U.active&&Ae&&(Ae=null),b===D&&(D._ignoreWhileAnimating=t),D.animateAll(function(){v("dragOverAnimationComplete"),D._ignoreWhileAnimating=null}),D!==b&&(b.animateAll(),b._ignoreWhileAnimating=null)),(t===x&&!x.animated||t===r&&!t.animated)&&(kt=null),!s.dragoverBubble&&!e.rootEl&&t!==document&&(x.parentNode[Pe]._isOutsideThisEl(e.target),!X&&_t(e)),!s.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),E=!0}function w(){qe=we(x),pt=we(x,s.draggable),$e({sortable:D,name:"change",toEl:r,newIndex:qe,newDraggableIndex:pt,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),t=Ye(t,s.draggable,r,!0),v("dragOver"),U.eventCanceled)return E;if(x.contains(e.target)||t.animated&&t.animatingX&&t.animatingY||D._ignoreWhileAnimating===t)return _(!1);if(Nr=!1,c&&!s.disabled&&(g?m||(i=ye!==pe):Ae===this||(this.lastPutMode=wr.checkPull(this,c,x,e))&&u.checkPut(this,c,x,e))){if(O=this._getDirection(e,t)==="vertical",a=ce(x),v("dragOverValid"),U.eventCanceled)return E;if(i)return ye=pe,S(),this._hideClone(),v("revert"),U.eventCanceled||(xt?pe.insertBefore(x,xt):pe.appendChild(x)),_(!0);var C=Rn(r,s.draggable);if(!C||Ai(e,O,this)&&!C.animated){if(C===x)return _(!1);if(C&&r===e.target&&(t=C),t&&(o=ce(t)),_r(pe,r,x,a,t,o,e,!!t)!==!1)return S(),C&&C.nextSibling?r.insertBefore(x,C.nextSibling):r.appendChild(x),ye=r,w(),_(!0)}else if(C&&Ci(e,O,this)){var F=$t(r,0,s,!0);if(F===x)return _(!1);if(t=F,o=ce(t),_r(pe,r,x,a,t,o,e,!1)!==!1)return S(),r.insertBefore(x,F),ye=r,w(),_(!0)}else if(t.parentNode===r){o=ce(t);var k=0,J,q=x.parentNode!==r,Y=!_i(x.animated&&x.toRect||a,t.animated&&t.toRect||o,O),Q=O?"top":"left",ae=fa(t,"top","top")||fa(x,"top","top"),Ie=ae?ae.scrollTop:void 0;kt!==t&&(J=o[Q],cr=!1,Sr=!Y&&s.invertSwap||q),k=Pi(e,t,o,O,Y?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,Sr,kt===t);var A;if(k!==0){var Re=we(x);do Re-=k,A=ye.children[Re];while(A&&(I(A,"display")==="none"||A===B))}if(k===0||A===t)return _(!1);kt=t,fr=k;var Oe=t.nextElementSibling,ve=!1;ve=k===1;var ie=_r(pe,r,x,a,t,o,e,ve);if(ie!==!1)return(ie===1||ie===-1)&&(ve=ie===1),Sn=!0,setTimeout(Oi,30),S(),ve&&!Oe?r.appendChild(x):t.parentNode.insertBefore(x,ve?Oe:t),ae&&Ma(ae,0,Ie-ae.scrollTop),ye=x.parentNode,J!==void 0&&!Sr&&(Cr=Math.abs(J-ce(t)[Q])),w(),_(!0)}if(r.contains(x))return _(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){V(document,"mousemove",this._onTouchMove),V(document,"touchmove",this._onTouchMove),V(document,"pointermove",this._onTouchMove),V(document,"dragover",_t),V(document,"mousemove",_t),V(document,"touchmove",_t)},_offUpEvents:function(){var e=this.el.ownerDocument;V(e,"mouseup",this._onDrop),V(e,"touchend",this._onDrop),V(e,"pointerup",this._onDrop),V(e,"pointercancel",this._onDrop),V(e,"touchcancel",this._onDrop),V(document,"selectstart",this)},_onDrop:function(e){var r=this.el,t=this.options;if(qe=we(x),pt=we(x,t.draggable),Ve("drop",this,{evt:e}),ye=x&&x.parentNode,qe=we(x),pt=we(x,t.draggable),U.eventCanceled){this._nulling();return}Rt=!1,Sr=!1,cr=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),En(this.cloneId),En(this._dragStartId),this.nativeDraggable&&(V(document,"drop",this),V(r,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),sr&&I(document.body,"user-select",""),I(x,"transform",""),e&&(nr&&(e.cancelable&&e.preventDefault(),!t.dropBubble&&e.stopPropagation()),B&&B.parentNode&&B.parentNode.removeChild(B),(pe===ye||Ae&&Ae.lastPutMode!=="clone")&&ge&&ge.parentNode&&ge.parentNode.removeChild(ge),x&&(this.nativeDraggable&&V(x,"dragend",this),sn(x),x.style["will-change"]="",nr&&!Rt&&me(x,Ae?Ae.options.ghostClass:this.options.ghostClass,!1),me(x,this.options.chosenClass,!1),$e({sortable:this,name:"unchoose",toEl:ye,newIndex:null,newDraggableIndex:null,originalEvent:e}),pe!==ye?(qe>=0&&($e({rootEl:ye,name:"add",toEl:ye,fromEl:pe,originalEvent:e}),$e({sortable:this,name:"remove",toEl:ye,originalEvent:e}),$e({rootEl:ye,name:"sort",toEl:ye,fromEl:pe,originalEvent:e}),$e({sortable:this,name:"sort",toEl:ye,originalEvent:e})),Ae&&Ae.save()):qe!==Lt&&qe>=0&&($e({sortable:this,name:"update",toEl:ye,originalEvent:e}),$e({sortable:this,name:"sort",toEl:ye,originalEvent:e})),U.active&&((qe==null||qe===-1)&&(qe=Lt,pt=ur),$e({sortable:this,name:"end",toEl:ye,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){Ve("nulling",this),pe=x=ye=B=xt=ge=Or=ht=Et=Ke=nr=qe=pt=Lt=ur=kt=fr=Ae=wr=U.dragged=U.ghost=U.clone=U.active=null,Mr.forEach(function(e){e.checked=!0}),Mr.length=nn=an=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":x&&(this._onDragOver(e),Ti(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],r,t=this.el.children,a=0,o=t.length,i=this.options;a<o;a++)r=t[a],Ye(r,i.draggable,this.el,!1)&&e.push(r.getAttribute(i.dataIdAttr)||Ni(r));return e},sort:function(e,r){var t={},a=this.el;this.toArray().forEach(function(o,i){var s=a.children[i];Ye(s,this.options.draggable,a,!1)&&(t[o]=s)},this),r&&this.captureAnimationState(),e.forEach(function(o){t[o]&&(a.removeChild(t[o]),a.appendChild(t[o]))}),r&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,r){return Ye(e,r||this.options.draggable,this.el,!1)},option:function(e,r){var t=this.options;if(r===void 0)return t[e];var a=gr.modifyOption(this,e,r);typeof a<"u"?t[e]=a:t[e]=r,e==="group"&&La(t)},destroy:function(){Ve("destroy",this);var e=this.el;e[Pe]=null,V(e,"mousedown",this._onTapStart),V(e,"touchstart",this._onTapStart),V(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(V(e,"dragover",this),V(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(r){r.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),kr.splice(kr.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!ht){if(Ve("hideClone",this),U.eventCanceled)return;I(ge,"display","none"),this.options.removeCloneOnHide&&ge.parentNode&&ge.parentNode.removeChild(ge),ht=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(ht){if(Ve("showClone",this),U.eventCanceled)return;x.parentNode==pe&&!this.options.group.revertClone?pe.insertBefore(ge,x):xt?pe.insertBefore(ge,xt):pe.appendChild(ge),this.options.group.revertClone&&this.animate(x,ge),I(ge,"display",""),ht=!1}}};function Ti(n){n.dataTransfer&&(n.dataTransfer.dropEffect="move"),n.cancelable&&n.preventDefault()}function _r(n,e,r,t,a,o,i,s){var u,c=n[Pe],g=c.options.onMove,m;return window.CustomEvent&&!lt&&!hr?u=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(u=document.createEvent("Event"),u.initEvent("move",!0,!0)),u.to=e,u.from=n,u.dragged=r,u.draggedRect=t,u.related=a||e,u.relatedRect=o||ce(e),u.willInsertAfter=s,u.originalEvent=i,n.dispatchEvent(u),g&&(m=g.call(c,u,i)),m}function sn(n){n.draggable=!1}function Oi(){Sn=!1}function Ci(n,e,r){var t=ce($t(r.el,0,r.options,!0)),a=ja(r.el,r.options,B),o=10;return e?n.clientX<a.left-o||n.clientY<t.top&&n.clientX<t.right:n.clientY<a.top-o||n.clientY<t.bottom&&n.clientX<t.left}function Ai(n,e,r){var t=ce(Rn(r.el,r.options.draggable)),a=ja(r.el,r.options,B),o=10;return e?n.clientX>a.right+o||n.clientY>t.bottom&&n.clientX>t.left:n.clientY>a.bottom+o||n.clientX>t.right&&n.clientY>t.top}function Pi(n,e,r,t,a,o,i,s){var u=t?n.clientY:n.clientX,c=t?r.height:r.width,g=t?r.top:r.left,m=t?r.bottom:r.right,b=!1;if(!i){if(s&&Cr<c*a){if(!cr&&(fr===1?u>g+c*o/2:u<m-c*o/2)&&(cr=!0),cr)b=!0;else if(fr===1?u<g+Cr:u>m-Cr)return-fr}else if(u>g+c*(1-a)/2&&u<m-c*(1-a)/2)return Ii(e)}return b=b||i,b&&(u<g+c*o/2||u>m-c*o/2)?u>g+c/2?1:-1:0}function Ii(n){return we(x)<we(n)?1:-1}function Ni(n){for(var e=n.tagName+n.className+n.src+n.href+n.textContent,r=e.length,t=0;r--;)t+=e.charCodeAt(r);return t.toString(36)}function ki(n){Mr.length=0;for(var e=n.getElementsByTagName("input"),r=e.length;r--;){var t=e[r];t.checked&&Mr.push(t)}}function Ar(n){return setTimeout(n,0)}function En(n){return clearTimeout(n)}$r&&H(document,"touchmove",function(n){(U.active||Rt)&&n.cancelable&&n.preventDefault()});U.utils={on:H,off:V,css:I,find:Na,is:function(e,r){return!!Ye(e,r,e,!1)},extend:mi,throttle:ka,closest:Ye,toggleClass:me,clone:Ln,index:we,nextTick:Ar,cancelNextTick:En,detectDirection:Ra,getChild:$t,expando:Pe};U.get=function(n){return n[Pe]};U.mount=function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];e[0].constructor===Array&&(e=e[0]),e.forEach(function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(U.utils=nt(nt({},U.utils),t.utils)),gr.mount(t)})};U.create=function(n,e){return new U(n,e)};U.version=hi;var xe=[],ar,_n,Dn=!1,ln,un,jr,or;function Mi(){function n(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return n.prototype={dragStarted:function(r){var t=r.originalEvent;this.sortable.nativeDraggable?H(document,"dragover",this._handleAutoScroll):this.options.supportPointer?H(document,"pointermove",this._handleFallbackAutoScroll):t.touches?H(document,"touchmove",this._handleFallbackAutoScroll):H(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(r){var t=r.originalEvent;!this.options.dragOverBubble&&!t.rootEl&&this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?V(document,"dragover",this._handleAutoScroll):(V(document,"pointermove",this._handleFallbackAutoScroll),V(document,"touchmove",this._handleFallbackAutoScroll),V(document,"mousemove",this._handleFallbackAutoScroll)),ma(),Pr(),vi()},nulling:function(){jr=_n=ar=Dn=or=ln=un=null,xe.length=0},_handleFallbackAutoScroll:function(r){this._handleAutoScroll(r,!0)},_handleAutoScroll:function(r,t){var a=this,o=(r.touches?r.touches[0]:r).clientX,i=(r.touches?r.touches[0]:r).clientY,s=document.elementFromPoint(o,i);if(jr=r,t||this.options.forceAutoScrollFallback||hr||lt||sr){fn(r,this.options,s,t);var u=gt(s,!0);Dn&&(!or||o!==ln||i!==un)&&(or&&ma(),or=setInterval(function(){var c=gt(document.elementFromPoint(o,i),!0);c!==u&&(u=c,Pr()),fn(r,a.options,c,t)},10),ln=o,un=i)}else{if(!this.options.bubbleScroll||gt(s,!0)===rt()){Pr();return}fn(r,this.options,gt(s,!1),!1)}}},We(n,{pluginName:"scroll",initializeByDefault:!0})}function Pr(){xe.forEach(function(n){clearInterval(n.pid)}),xe=[]}function ma(){clearInterval(or)}var fn=ka(function(n,e,r,t){if(e.scroll){var a=(n.touches?n.touches[0]:n).clientX,o=(n.touches?n.touches[0]:n).clientY,i=e.scrollSensitivity,s=e.scrollSpeed,u=rt(),c=!1,g;_n!==r&&(_n=r,Pr(),ar=e.scroll,g=e.scrollFn,ar===!0&&(ar=gt(r,!0)));var m=0,b=ar;do{var O=b,D=ce(O),E=D.top,v=D.bottom,S=D.left,_=D.right,w=D.width,C=D.height,F=void 0,k=void 0,J=O.scrollWidth,q=O.scrollHeight,Y=I(O),Q=O.scrollLeft,ae=O.scrollTop;O===u?(F=w<J&&(Y.overflowX==="auto"||Y.overflowX==="scroll"||Y.overflowX==="visible"),k=C<q&&(Y.overflowY==="auto"||Y.overflowY==="scroll"||Y.overflowY==="visible")):(F=w<J&&(Y.overflowX==="auto"||Y.overflowX==="scroll"),k=C<q&&(Y.overflowY==="auto"||Y.overflowY==="scroll"));var Ie=F&&(Math.abs(_-a)<=i&&Q+w<J)-(Math.abs(S-a)<=i&&!!Q),A=k&&(Math.abs(v-o)<=i&&ae+C<q)-(Math.abs(E-o)<=i&&!!ae);if(!xe[m])for(var Re=0;Re<=m;Re++)xe[Re]||(xe[Re]={});(xe[m].vx!=Ie||xe[m].vy!=A||xe[m].el!==O)&&(xe[m].el=O,xe[m].vx=Ie,xe[m].vy=A,clearInterval(xe[m].pid),(Ie!=0||A!=0)&&(c=!0,xe[m].pid=setInterval((function(){t&&this.layer===0&&U.active._onTouchMove(jr);var Oe=xe[this.layer].vy?xe[this.layer].vy*s:0,ve=xe[this.layer].vx?xe[this.layer].vx*s:0;typeof g=="function"&&g.call(U.dragged.parentNode[Pe],ve,Oe,n,jr,xe[this.layer].el)!=="continue"||Ma(xe[this.layer].el,ve,Oe)}).bind({layer:m}),24))),m++}while(e.bubbleScroll&&b!==u&&(b=gt(b,!1)));Dn=c}},30),za=function(e){var r=e.originalEvent,t=e.putSortable,a=e.dragEl,o=e.activeSortable,i=e.dispatchSortableEvent,s=e.hideGhostForTarget,u=e.unhideGhostForTarget;if(r){var c=t||o;s();var g=r.changedTouches&&r.changedTouches.length?r.changedTouches[0]:r,m=document.elementFromPoint(g.clientX,g.clientY);u(),c&&!c.el.contains(m)&&(i("spill"),this.onSpill({dragEl:a,putSortable:t}))}};function Un(){}Un.prototype={startIndex:null,dragStart:function(e){var r=e.oldDraggableIndex;this.startIndex=r},onSpill:function(e){var r=e.dragEl,t=e.putSortable;this.sortable.captureAnimationState(),t&&t.captureAnimationState();var a=$t(this.sortable.el,this.startIndex,this.options);a?this.sortable.el.insertBefore(r,a):this.sortable.el.appendChild(r),this.sortable.animateAll(),t&&t.animateAll()},drop:za};We(Un,{pluginName:"revertOnSpill"});function $n(){}$n.prototype={onSpill:function(e){var r=e.dragEl,t=e.putSortable,a=t||this.sortable;a.captureAnimationState(),r.parentNode&&r.parentNode.removeChild(r),a.animateAll()},drop:za};We($n,{pluginName:"removeOnSpill"});var Ge;function ji(){function n(){this.defaults={swapClass:"sortable-swap-highlight"}}return n.prototype={dragStart:function(r){var t=r.dragEl;Ge=t},dragOverValid:function(r){var t=r.completed,a=r.target,o=r.onMove,i=r.activeSortable,s=r.changed,u=r.cancel;if(i.options.swap){var c=this.sortable.el,g=this.options;if(a&&a!==c){var m=Ge;o(a)!==!1?(me(a,g.swapClass,!0),Ge=a):Ge=null,m&&m!==Ge&&me(m,g.swapClass,!1)}s(),t(!0),u()}},drop:function(r){var t=r.activeSortable,a=r.putSortable,o=r.dragEl,i=a||this.sortable,s=this.options;Ge&&me(Ge,s.swapClass,!1),Ge&&(s.swap||a&&a.options.swap)&&o!==Ge&&(i.captureAnimationState(),i!==t&&t.captureAnimationState(),Fi(o,Ge),i.animateAll(),i!==t&&t.animateAll())},nulling:function(){Ge=null}},We(n,{pluginName:"swap",eventProperties:function(){return{swapItem:Ge}}})}function Fi(n,e){var r=n.parentNode,t=e.parentNode,a,o;!r||!t||r.isEqualNode(e)||t.isEqualNode(n)||(a=we(n),o=we(e),r.isEqualNode(t)&&a<o&&o++,r.insertBefore(e,r.children[a]),t.insertBefore(n,t.children[o]))}var z=[],Xe=[],Zt,Ze,Jt=!1,He=!1,Mt=!1,ue,Qt,Dr;function Ri(){function n(e){for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));e.options.avoidImplicitDeselect||(e.options.supportPointer?H(document,"pointerup",this._deselectMultiDrag):(H(document,"mouseup",this._deselectMultiDrag),H(document,"touchend",this._deselectMultiDrag))),H(document,"keydown",this._checkKeyDown),H(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,avoidImplicitDeselect:!1,setData:function(a,o){var i="";z.length&&Ze===e?z.forEach(function(s,u){i+=(u?", ":"")+s.textContent}):i=o.textContent,a.setData("Text",i)}}}return n.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(r){var t=r.dragEl;ue=t},delayEnded:function(){this.isMultiDrag=~z.indexOf(ue)},setupClone:function(r){var t=r.sortable,a=r.cancel;if(this.isMultiDrag){for(var o=0;o<z.length;o++)Xe.push(Ln(z[o])),Xe[o].sortableIndex=z[o].sortableIndex,Xe[o].draggable=!1,Xe[o].style["will-change"]="",me(Xe[o],this.options.selectedClass,!1),z[o]===ue&&me(Xe[o],this.options.chosenClass,!1);t._hideClone(),a()}},clone:function(r){var t=r.sortable,a=r.rootEl,o=r.dispatchSortableEvent,i=r.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||z.length&&Ze===t&&(va(!0,a),o("clone"),i()))},showClone:function(r){var t=r.cloneNowShown,a=r.rootEl,o=r.cancel;this.isMultiDrag&&(va(!1,a),Xe.forEach(function(i){I(i,"display","")}),t(),Dr=!1,o())},hideClone:function(r){var t=this;r.sortable;var a=r.cloneNowHidden,o=r.cancel;this.isMultiDrag&&(Xe.forEach(function(i){I(i,"display","none"),t.options.removeCloneOnHide&&i.parentNode&&i.parentNode.removeChild(i)}),a(),Dr=!0,o())},dragStartGlobal:function(r){r.sortable,!this.isMultiDrag&&Ze&&Ze.multiDrag._deselectMultiDrag(),z.forEach(function(t){t.sortableIndex=we(t)}),z=z.sort(function(t,a){return t.sortableIndex-a.sortableIndex}),Mt=!0},dragStarted:function(r){var t=this,a=r.sortable;if(this.isMultiDrag){if(this.options.sort&&(a.captureAnimationState(),this.options.animation)){z.forEach(function(i){i!==ue&&I(i,"position","absolute")});var o=ce(ue,!1,!0,!0);z.forEach(function(i){i!==ue&&da(i,o)}),He=!0,Jt=!0}a.animateAll(function(){He=!1,Jt=!1,t.options.animation&&z.forEach(function(i){tn(i)}),t.options.sort&&xr()})}},dragOver:function(r){var t=r.target,a=r.completed,o=r.cancel;He&&~z.indexOf(t)&&(a(!1),o())},revert:function(r){var t=r.fromSortable,a=r.rootEl,o=r.sortable,i=r.dragRect;z.length>1&&(z.forEach(function(s){o.addAnimationState({target:s,rect:He?ce(s):i}),tn(s),s.fromRect=i,t.removeAnimationState(s)}),He=!1,Li(!this.options.removeCloneOnHide,a))},dragOverCompleted:function(r){var t=r.sortable,a=r.isOwner,o=r.insertion,i=r.activeSortable,s=r.parentEl,u=r.putSortable,c=this.options;if(o){if(a&&i._hideClone(),Jt=!1,c.animation&&z.length>1&&(He||!a&&!i.options.sort&&!u)){var g=ce(ue,!1,!0,!0);z.forEach(function(b){b!==ue&&(da(b,g),s.appendChild(b))}),He=!0}if(!a)if(He||xr(),z.length>1){var m=Dr;i._showClone(t),i.options.animation&&!Dr&&m&&Xe.forEach(function(b){i.addAnimationState({target:b,rect:Qt}),b.fromRect=Qt,b.thisAnimationDuration=null})}else i._showClone(t)}},dragOverAnimationCapture:function(r){var t=r.dragRect,a=r.isOwner,o=r.activeSortable;if(z.forEach(function(s){s.thisAnimationDuration=null}),o.options.animation&&!a&&o.multiDrag.isMultiDrag){Qt=We({},t);var i=Ct(ue,!0);Qt.top-=i.f,Qt.left-=i.e}},dragOverAnimationComplete:function(){He&&(He=!1,xr())},drop:function(r){var t=r.originalEvent,a=r.rootEl,o=r.parentEl,i=r.sortable,s=r.dispatchSortableEvent,u=r.oldIndex,c=r.putSortable,g=c||this.sortable;if(t){var m=this.options,b=o.children;if(!Mt)if(m.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),me(ue,m.selectedClass,!~z.indexOf(ue)),~z.indexOf(ue))z.splice(z.indexOf(ue),1),Zt=null,rr({sortable:i,rootEl:a,name:"deselect",targetEl:ue,originalEvent:t});else{if(z.push(ue),rr({sortable:i,rootEl:a,name:"select",targetEl:ue,originalEvent:t}),t.shiftKey&&Zt&&i.el.contains(Zt)){var O=we(Zt),D=we(ue);~O&&~D&&O!==D&&function(){var _,w;D>O?(w=O,_=D):(w=D,_=O+1);for(var C=m.filter;w<_;w++)if(!~z.indexOf(b[w])&&Ye(b[w],m.draggable,o,!1)){var F=C&&(typeof C=="function"?C.call(i,t,b[w],i):C.split(",").some(function(k){return Ye(b[w],k.trim(),o,!1)}));F||(me(b[w],m.selectedClass,!0),z.push(b[w]),rr({sortable:i,rootEl:a,name:"select",targetEl:b[w],originalEvent:t}))}}()}else Zt=ue;Ze=g}if(Mt&&this.isMultiDrag){if(He=!1,(o[Pe].options.sort||o!==a)&&z.length>1){var E=ce(ue),v=we(ue,":not(."+this.options.selectedClass+")");if(!Jt&&m.animation&&(ue.thisAnimationDuration=null),g.captureAnimationState(),!Jt&&(m.animation&&(ue.fromRect=E,z.forEach(function(_){if(_.thisAnimationDuration=null,_!==ue){var w=He?ce(_):E;_.fromRect=w,g.addAnimationState({target:_,rect:w})}})),xr(),z.forEach(function(_){b[v]?o.insertBefore(_,b[v]):o.appendChild(_),v++}),u===we(ue))){var S=!1;z.forEach(function(_){if(_.sortableIndex!==we(_)){S=!0;return}}),S&&(s("update"),s("sort"))}z.forEach(function(_){tn(_)}),g.animateAll()}Ze=g}(a===o||c&&c.lastPutMode!=="clone")&&Xe.forEach(function(_){_.parentNode&&_.parentNode.removeChild(_)})}},nullingGlobal:function(){this.isMultiDrag=Mt=!1,Xe.length=0},destroyGlobal:function(){this._deselectMultiDrag(),V(document,"pointerup",this._deselectMultiDrag),V(document,"mouseup",this._deselectMultiDrag),V(document,"touchend",this._deselectMultiDrag),V(document,"keydown",this._checkKeyDown),V(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(r){if(!(typeof Mt<"u"&&Mt)&&Ze===this.sortable&&!(r&&Ye(r.target,this.options.draggable,this.sortable.el,!1))&&!(r&&r.button!==0))for(;z.length;){var t=z[0];me(t,this.options.selectedClass,!1),z.shift(),rr({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:t,originalEvent:r})}},_checkKeyDown:function(r){r.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(r){r.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},We(n,{pluginName:"multiDrag",utils:{select:function(r){var t=r.parentNode[Pe];!t||!t.options.multiDrag||~z.indexOf(r)||(Ze&&Ze!==t&&(Ze.multiDrag._deselectMultiDrag(),Ze=t),me(r,t.options.selectedClass,!0),z.push(r))},deselect:function(r){var t=r.parentNode[Pe],a=z.indexOf(r);!t||!t.options.multiDrag||!~a||(me(r,t.options.selectedClass,!1),z.splice(a,1))}},eventProperties:function(){var r=this,t=[],a=[];return z.forEach(function(o){t.push({multiDragElement:o,index:o.sortableIndex});var i;He&&o!==ue?i=-1:He?i=we(o,":not(."+r.options.selectedClass+")"):i=we(o),a.push({multiDragElement:o,index:i})}),{items:ui(z),clones:[].concat(Xe),oldIndicies:t,newIndicies:a}},optionListeners:{multiDragKey:function(r){return r=r.toLowerCase(),r==="ctrl"?r="Control":r.length>1&&(r=r.charAt(0).toUpperCase()+r.substr(1)),r}}})}function Li(n,e){z.forEach(function(r,t){var a=e.children[r.sortableIndex+(n?Number(t):0)];a?e.insertBefore(r,a):e.appendChild(r)})}function va(n,e){Xe.forEach(function(r,t){var a=e.children[r.sortableIndex+(n?Number(t):0)];a?e.insertBefore(r,a):e.appendChild(r)})}function xr(){z.forEach(function(n){n!==ue&&n.parentNode&&n.parentNode.removeChild(n)})}U.mount(new Mi);U.mount($n,Un);const Ui=Object.freeze(Object.defineProperty({__proto__:null,MultiDrag:Ri,Sortable:U,Swap:ji,default:U},Symbol.toStringTag,{value:"Module"})),$i=_o(Ui);Object.defineProperty(Ur,"__esModule",{value:!0});Ur.default=void 0;var jt=Ba(pr),ya=Bi(Fe),zi=Ba($i);function Bi(n){if(n&&n.__esModule)return n;var e={};if(n!=null){for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var t=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(n,r):{};t.get||t.set?Object.defineProperty(e,r,t):e[r]=n[r]}}return e.default=n,e}function Ba(n){return n&&n.__esModule?n:{default:n}}function xn(){return xn=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(n[t]=r[t])}return n},xn.apply(this,arguments)}function Vi(n,e){if(n==null)return{};var r=Hi(n,e),t,a;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(a=0;a<o.length;a++)t=o[a],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(n,t)&&(r[t]=n[t])}return r}function Hi(n,e){if(n==null)return{};var r={},t=Object.keys(n),a,o;for(o=0;o<t.length;o++)a=t[o],!(e.indexOf(a)>=0)&&(r[a]=n[a]);return r}function dr(n){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?dr=function(r){return typeof r}:dr=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},dr(n)}function Yi(n){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},t=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(t=t.concat(Object.getOwnPropertySymbols(r).filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable}))),t.forEach(function(a){zr(n,a,r[a])})}return n}function Xi(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function qi(n,e){for(var r=0;r<e.length;r++){var t=e[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(n,t.key,t)}}function Gi(n,e,r){return e&&qi(n.prototype,e),n}function Wi(n,e){return e&&(dr(e)==="object"||typeof e=="function")?e:Va(n)}function Tn(n){return Tn=Object.setPrototypeOf?Object.getPrototypeOf:function(r){return r.__proto__||Object.getPrototypeOf(r)},Tn(n)}function Va(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function Ki(n,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),e&&On(n,e)}function On(n,e){return On=Object.setPrototypeOf||function(t,a){return t.__proto__=a,t},On(n,e)}function zr(n,e,r){return e in n?Object.defineProperty(n,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[e]=r,n}var Ft={nextSibling:null,activeComponent:null},zn=function(n){Ki(e,n);function e(){var r,t;Xi(this,e);for(var a=arguments.length,o=new Array(a),i=0;i<a;i++)o[i]=arguments[i];return t=Wi(this,(r=Tn(e)).call.apply(r,[this].concat(o))),zr(Va(t),"sortable",null),t}return Gi(e,[{key:"componentDidMount",value:function(){var t=this,a=Yi({},this.props.options);["onChoose","onStart","onEnd","onAdd","onUpdate","onSort","onRemove","onFilter","onMove","onClone"].forEach(function(o){var i=a[o];a[o]=function(){for(var s=arguments.length,u=new Array(s),c=0;c<s;c++)u[c]=arguments[c];var g=u[0];if(o==="onChoose")Ft.nextSibling=g.item.nextElementSibling,Ft.activeComponent=t;else if((o==="onAdd"||o==="onUpdate")&&t.props.onChange){var m=t.sortable.toArray(),b=Ft.activeComponent,O=b.sortable.toArray(),D=Ft.nextSibling&&Ft.nextSibling.parentNode!==null?Ft.nextSibling:null;if(g.from.insertBefore(g.item,D),b!==t){var E=b.props.options||{};dr(E.group)==="object"&&E.group.pull==="clone"&&g.item.parentNode.removeChild(g.item),b.props.onChange&&b.props.onChange(O,b.sortable,g)}t.props.onChange&&t.props.onChange(m,t.sortable,g)}if(g.type==="move"){var v=u[0],S=u[1],_=i?i(v,S):!0;return _}setTimeout(function(){i&&i(g)},0)}}),this.sortable=zi.default.create(this.node,a)}},{key:"shouldComponentUpdate",value:function(t){return!!t.onChange}},{key:"componentWillUnmount",value:function(){this.sortable&&(this.sortable.destroy(),this.sortable=null)}},{key:"render",value:function(){var t=this,a=this.props,o=a.tag;a.options,a.onChange;var i=Vi(a,["tag","options","onChange"]);return ya.default.createElement(o,xn({},i,{ref:function(u){t.node=u}}))}}]),e}(ya.Component);zr(zn,"propTypes",{options:jt.default.object,onChange:jt.default.func,tag:jt.default.oneOfType([jt.default.string,jt.default.func]),style:jt.default.object});zr(zn,"defaultProps",{options:{},tag:"div",style:{}});var Zi=zn;Ur.default=Zi;var Ji=Ur.default,Qi=Ji,Ha={exports:{}};(function(n,e){(function(r,t){n.exports=t(Do,Fe)})(xo,function(r,t){r=r&&r.hasOwnProperty("default")?r.default:r,t=t&&t.hasOwnProperty("default")?t.default:t;function a(l,f){return f={exports:{}},l(f,f.exports),f.exports}function o(l){return function(){return l}}var i=function(){};i.thatReturns=o,i.thatReturnsFalse=o(!1),i.thatReturnsTrue=o(!0),i.thatReturnsNull=o(null),i.thatReturnsThis=function(){return this},i.thatReturnsArgument=function(l){return l};var s=i,u=function(f){};u=function(f){if(f===void 0)throw new Error("invariant requires an error message argument")};function c(l,f,d,h,p,P,j,N){if(u(f),!l){var $;if(f===void 0)$=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var M=[d,h,p,P,j,N],W=0;$=new Error(f.replace(/%s/g,function(){return M[W++]})),$.name="Invariant Violation"}throw $.framesToPop=1,$}}var g=c,m=s;{var b=function(f){for(var d=arguments.length,h=Array(d>1?d-1:0),p=1;p<d;p++)h[p-1]=arguments[p];var P=0,j="Warning: "+f.replace(/%s/g,function(){return h[P++]});typeof console<"u"&&console.error(j);try{throw new Error(j)}catch{}};m=function(f,d){if(d===void 0)throw new Error("`warning(condition, format, ...args)` requires a warning message argument");if(d.indexOf("Failed Composite propType: ")!==0&&!f){for(var h=arguments.length,p=Array(h>2?h-2:0),P=2;P<h;P++)p[P-2]=arguments[P];b.apply(void 0,[d].concat(p))}}}var O=m;/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var D=Object.getOwnPropertySymbols,E=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;function S(l){if(l==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(l)}function _(){try{if(!Object.assign)return!1;var l=new String("abc");if(l[5]="de",Object.getOwnPropertyNames(l)[0]==="5")return!1;for(var f={},d=0;d<10;d++)f["_"+String.fromCharCode(d)]=d;var h=Object.getOwnPropertyNames(f).map(function(P){return f[P]});if(h.join("")!=="**********")return!1;var p={};return"abcdefghijklmnopqrst".split("").forEach(function(P){p[P]=P}),Object.keys(Object.assign({},p)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}var w=_()?Object.assign:function(l,f){for(var d,h=S(l),p,P=1;P<arguments.length;P++){d=Object(arguments[P]);for(var j in d)E.call(d,j)&&(h[j]=d[j]);if(D){p=D(d);for(var N=0;N<p.length;N++)v.call(d,p[N])&&(h[p[N]]=d[p[N]])}}return h},C="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",F=C,k=g,J=O,q=F,Y={};function Q(l,f,d,h,p){for(var P in l)if(l.hasOwnProperty(P)){var j;try{k(typeof l[P]=="function","%s: %s type `%s` is invalid; it must be a function, usually from the `prop-types` package, but received `%s`.",h||"React class",d,P,typeof l[P]),j=l[P](f,P,h,d,null,q)}catch($){j=$}if(J(!j||j instanceof Error,"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",h||"React class",d,P,typeof j),j instanceof Error&&!(j.message in Y)){Y[j.message]=!0;var N=p?p():"";J(!1,"Failed %s type: %s%s",d,j.message,N??"")}}}var ae=Q,Ie=function(l,f){var d=typeof Symbol=="function"&&Symbol.iterator,h="@@iterator";function p(T){var R=T&&(d&&T[d]||T[h]);if(typeof R=="function")return R}var P="<<anonymous>>",j={array:W("array"),bool:W("boolean"),func:W("function"),number:W("number"),object:W("object"),string:W("string"),symbol:W("symbol"),any:De(),arrayOf:Ue,element:Me(),instanceOf:_e,node:Xr(),objectOf:Pt,oneOf:ct,oneOfType:Gt,shape:vr,exact:Zn};function N(T,R){return T===R?T!==0||1/T===1/R:T!==T&&R!==R}function $(T){this.message=T,this.stack=""}$.prototype=Error.prototype;function M(T){var R={},oe=0;function te(se,re,Z,ne,le,de,St){if(ne=ne||P,de=de||Z,St!==F){if(f)g(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");else if(typeof console<"u"){var Wt=ne+":"+Z;!R[Wt]&&oe<3&&(O(!1,"You are manually calling a React.PropTypes validation function for the `%s` prop on `%s`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details.",de,ne),R[Wt]=!0,oe++)}}return re[Z]==null?se?re[Z]===null?new $("The "+le+" `"+de+"` is marked as required "+("in `"+ne+"`, but its value is `null`.")):new $("The "+le+" `"+de+"` is marked as required in "+("`"+ne+"`, but its value is `undefined`.")):null:T(re,Z,ne,le,de)}var K=te.bind(null,!1);return K.isRequired=te.bind(null,!0),K}function W(T){function R(oe,te,K,se,re,Z){var ne=oe[te],le=wt(ne);if(le!==T){var de=Jn(ne);return new $("Invalid "+se+" `"+re+"` of type "+("`"+de+"` supplied to `"+K+"`, expected ")+("`"+T+"`."))}return null}return M(R)}function De(){return M(s.thatReturnsNull)}function Ue(T){function R(oe,te,K,se,re){if(typeof T!="function")return new $("Property `"+re+"` of component `"+K+"` has invalid PropType notation inside arrayOf.");var Z=oe[te];if(!Array.isArray(Z)){var ne=wt(Z);return new $("Invalid "+se+" `"+re+"` of type "+("`"+ne+"` supplied to `"+K+"`, expected an array."))}for(var le=0;le<Z.length;le++){var de=T(Z,le,K,se,re+"["+le+"]",F);if(de instanceof Error)return de}return null}return M(R)}function Me(){function T(R,oe,te,K,se){var re=R[oe];if(!l(re)){var Z=wt(re);return new $("Invalid "+K+" `"+se+"` of type "+("`"+Z+"` supplied to `"+te+"`, expected a single ReactElement."))}return null}return M(T)}function _e(T){function R(oe,te,K,se,re){if(!(oe[te]instanceof T)){var Z=T.name||P,ne=so(oe[te]);return new $("Invalid "+se+" `"+re+"` of type "+("`"+ne+"` supplied to `"+K+"`, expected ")+("instance of `"+Z+"`."))}return null}return M(R)}function ct(T){if(!Array.isArray(T))return O(!1,"Invalid argument supplied to oneOf, expected an instance of array."),s.thatReturnsNull;function R(oe,te,K,se,re){for(var Z=oe[te],ne=0;ne<T.length;ne++)if(N(Z,T[ne]))return null;var le=JSON.stringify(T);return new $("Invalid "+se+" `"+re+"` of value `"+Z+"` "+("supplied to `"+K+"`, expected one of "+le+"."))}return M(R)}function Pt(T){function R(oe,te,K,se,re){if(typeof T!="function")return new $("Property `"+re+"` of component `"+K+"` has invalid PropType notation inside objectOf.");var Z=oe[te],ne=wt(Z);if(ne!=="object")return new $("Invalid "+se+" `"+re+"` of type "+("`"+ne+"` supplied to `"+K+"`, expected an object."));for(var le in Z)if(Z.hasOwnProperty(le)){var de=T(Z,le,K,se,re+"."+le,F);if(de instanceof Error)return de}return null}return M(R)}function Gt(T){if(!Array.isArray(T))return O(!1,"Invalid argument supplied to oneOfType, expected an instance of array."),s.thatReturnsNull;for(var R=0;R<T.length;R++){var oe=T[R];if(typeof oe!="function")return O(!1,"Invalid argument supplied to oneOfType. Expected an array of check functions, but received %s at index %s.",io(oe),R),s.thatReturnsNull}function te(K,se,re,Z,ne){for(var le=0;le<T.length;le++){var de=T[le];if(de(K,se,re,Z,ne,F)==null)return null}return new $("Invalid "+Z+" `"+ne+"` supplied to "+("`"+re+"`."))}return M(te)}function Xr(){function T(R,oe,te,K,se){return yr(R[oe])?null:new $("Invalid "+K+" `"+se+"` supplied to "+("`"+te+"`, expected a ReactNode."))}return M(T)}function vr(T){function R(oe,te,K,se,re){var Z=oe[te],ne=wt(Z);if(ne!=="object")return new $("Invalid "+se+" `"+re+"` of type `"+ne+"` "+("supplied to `"+K+"`, expected `object`."));for(var le in T){var de=T[le];if(de){var St=de(Z,le,K,se,re+"."+le,F);if(St)return St}}return null}return M(R)}function Zn(T){function R(oe,te,K,se,re){var Z=oe[te],ne=wt(Z);if(ne!=="object")return new $("Invalid "+se+" `"+re+"` of type `"+ne+"` "+("supplied to `"+K+"`, expected `object`."));var le=w({},oe[te],T);for(var de in le){var St=T[de];if(!St)return new $("Invalid "+se+" `"+re+"` key `"+de+"` supplied to `"+K+"`.\nBad object: "+JSON.stringify(oe[te],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(T),null,"  "));var Wt=St(Z,de,K,se,re+"."+de,F);if(Wt)return Wt}return null}return M(R)}function yr(T){switch(typeof T){case"number":case"string":case"undefined":return!0;case"boolean":return!T;case"object":if(Array.isArray(T))return T.every(yr);if(T===null||l(T))return!0;var R=p(T);if(R){var oe=R.call(T),te;if(R!==T.entries){for(;!(te=oe.next()).done;)if(!yr(te.value))return!1}else for(;!(te=oe.next()).done;){var K=te.value;if(K&&!yr(K[1]))return!1}}else return!1;return!0;default:return!1}}function oo(T,R){return T==="symbol"||R["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&R instanceof Symbol}function wt(T){var R=typeof T;return Array.isArray(T)?"array":T instanceof RegExp?"object":oo(R,T)?"symbol":R}function Jn(T){if(typeof T>"u"||T===null)return""+T;var R=wt(T);if(R==="object"){if(T instanceof Date)return"date";if(T instanceof RegExp)return"regexp"}return R}function io(T){var R=Jn(T);switch(R){case"array":case"object":return"an "+R;case"boolean":case"date":case"regexp":return"a "+R;default:return R}}function so(T){return!T.constructor||!T.constructor.name?P:T.constructor.name}return j.checkPropTypes=ae,j.PropTypes=j,j},A=a(function(l){{var f=typeof Symbol=="function"&&Symbol.for&&Symbol.for("react.element")||60103,d=function(p){return typeof p=="object"&&p!==null&&p.$$typeof===f},h=!0;l.exports=Ie(d,h)}}),Re=a(function(l){/*!
  Copyright (c) 2016 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/(function(){var f={}.hasOwnProperty;function d(){for(var h=[],p=0;p<arguments.length;p++){var P=arguments[p];if(P){var j=typeof P;if(j==="string"||j==="number")h.push(P);else if(Array.isArray(P))h.push(d.apply(null,P));else if(j==="object")for(var N in P)f.call(P,N)&&P[N]&&h.push(N)}}return h.join(" ")}l.exports?l.exports=d:window.classNames=d})()});function Oe(l,f){for(var d=0,h=l.length;d<h;d++)if(f.apply(f,[l[d],d,l]))return l[d]}function ve(l){return typeof l=="function"||Object.prototype.toString.call(l)==="[object Function]"}function ie(l){return typeof l=="number"&&!isNaN(l)}function X(l){return parseInt(l,10)}function Le(l,f,d){if(l[f])return new Error("Invalid prop "+f+" passed to "+d+" - do not set this, set it on the child.")}var Ce=["Moz","Webkit","O","ms"];function Qe(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"transform";if(typeof window>"u"||typeof window.document>"u")return"";var f=window.document.documentElement.style;if(l in f)return"";for(var d=0;d<Ce.length;d++)if(Be(l,Ce[d])in f)return Ce[d];return""}function Be(l,f){return f?""+f+G(l):l}function G(l){for(var f="",d=!0,h=0;h<l.length;h++)d?(f+=l[h].toUpperCase(),d=!1):l[h]==="-"?d=!0:f+=l[h];return f}var ee=Qe(),fe=function(l,f){if(!(l instanceof f))throw new TypeError("Cannot call a class as a function")},Ee=function(){function l(f,d){for(var h=0;h<d.length;h++){var p=d[h];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(f,p.key,p)}}return function(f,d,h){return d&&l(f.prototype,d),h&&l(f,h),f}}(),Ne=function(l,f,d){return f in l?Object.defineProperty(l,f,{value:d,enumerable:!0,configurable:!0,writable:!0}):l[f]=d,l},ke=Object.assign||function(l){for(var f=1;f<arguments.length;f++){var d=arguments[f];for(var h in d)Object.prototype.hasOwnProperty.call(d,h)&&(l[h]=d[h])}return l},Bt=function(l,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);l.prototype=Object.create(f&&f.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(l,f):l.__proto__=f)},mt=function(l,f){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:l},Vt=function(){function l(f,d){var h=[],p=!0,P=!1,j=void 0;try{for(var N=f[Symbol.iterator](),$;!(p=($=N.next()).done)&&(h.push($.value),!(d&&h.length===d));p=!0);}catch(M){P=!0,j=M}finally{try{!p&&N.return&&N.return()}finally{if(P)throw j}}return h}return function(f,d){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f))return l(f,d);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),ut="";function Ht(l,f){return ut||(ut=Oe(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(d){return ve(l[d])})),ve(l[ut])?l[ut](f):!1}function At(l,f,d){var h=l;do{if(Ht(h,f))return!0;if(h===d)return!1;h=h.parentNode}while(h);return!1}function Yt(l,f,d){l&&(l.attachEvent?l.attachEvent("on"+f,d):l.addEventListener?l.addEventListener(f,d,!0):l["on"+f]=d)}function at(l,f,d){l&&(l.detachEvent?l.detachEvent("on"+f,d):l.removeEventListener?l.removeEventListener(f,d,!0):l["on"+f]=null)}function Xt(l){var f=l.clientHeight,d=l.ownerDocument.defaultView.getComputedStyle(l);return f+=X(d.borderTopWidth),f+=X(d.borderBottomWidth),f}function mr(l){var f=l.clientWidth,d=l.ownerDocument.defaultView.getComputedStyle(l);return f+=X(d.borderLeftWidth),f+=X(d.borderRightWidth),f}function qt(l){var f=l.clientHeight,d=l.ownerDocument.defaultView.getComputedStyle(l);return f-=X(d.paddingTop),f-=X(d.paddingBottom),f}function vt(l){var f=l.clientWidth,d=l.ownerDocument.defaultView.getComputedStyle(l);return f-=X(d.paddingLeft),f-=X(d.paddingRight),f}function Ya(l,f){var d=f===f.ownerDocument.body,h=d?{left:0,top:0}:f.getBoundingClientRect(),p=l.clientX+f.scrollLeft-h.left,P=l.clientY+f.scrollTop-h.top;return{x:p,y:P}}function Xa(l,f){var d=Wn(l,f,"px");return Ne({},Be("transform",ee),d)}function qa(l,f){var d=Wn(l,f,"");return d}function Wn(l,f,d){var h=l.x,p=l.y,P="translate("+h+d+","+p+d+")";if(f){var j=""+(typeof f.x=="string"?f.x:f.x+d),N=""+(typeof f.y=="string"?f.y:f.y+d);P="translate("+j+", "+N+")"+P}return P}function Ga(l,f){return l.targetTouches&&Oe(l.targetTouches,function(d){return f===d.identifier})||l.changedTouches&&Oe(l.changedTouches,function(d){return f===d.identifier})}function Wa(l){if(l.targetTouches&&l.targetTouches[0])return l.targetTouches[0].identifier;if(l.changedTouches&&l.changedTouches[0])return l.changedTouches[0].identifier}function Ka(l){if(l){var f=l.getElementById("react-draggable-style-el");f||(f=l.createElement("style"),f.type="text/css",f.id="react-draggable-style-el",f.innerHTML=`.react-draggable-transparent-selection *::-moz-selection {all: inherit;}
`,f.innerHTML+=`.react-draggable-transparent-selection *::selection {all: inherit;}
`,l.getElementsByTagName("head")[0].appendChild(f)),l.body&&Ja(l.body,"react-draggable-transparent-selection")}}function Kn(l){try{l&&l.body&&Qa(l.body,"react-draggable-transparent-selection"),l.selection?l.selection.empty():window.getSelection().removeAllRanges()}catch{}}function Za(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return ke({touchAction:"none"},l)}function Ja(l,f){l.classList?l.classList.add(f):l.className.match(new RegExp("(?:^|\\s)"+f+"(?!\\S)"))||(l.className+=" "+f)}function Qa(l,f){l.classList?l.classList.remove(f):l.className=l.className.replace(new RegExp("(?:^|\\s)"+f+"(?!\\S)","g"),"")}function eo(l,f,d){if(!l.props.bounds)return[f,d];var h=l.props.bounds;h=typeof h=="string"?h:ao(h);var p=Yr(l);if(typeof h=="string"){var P=p.ownerDocument,j=P.defaultView,N=void 0;if(h==="parent"?N=p.parentNode:N=P.querySelector(h),!(N instanceof j.HTMLElement))throw new Error('Bounds selector "'+h+'" could not find an element.');var $=j.getComputedStyle(p),M=j.getComputedStyle(N);h={left:-p.offsetLeft+X(M.paddingLeft)+X($.marginLeft),top:-p.offsetTop+X(M.paddingTop)+X($.marginTop),right:vt(N)-mr(p)-p.offsetLeft+X(M.paddingRight)-X($.marginRight),bottom:qt(N)-Xt(p)-p.offsetTop+X(M.paddingBottom)-X($.marginBottom)}}return ie(h.right)&&(f=Math.min(f,h.right)),ie(h.bottom)&&(d=Math.min(d,h.bottom)),ie(h.left)&&(f=Math.max(f,h.left)),ie(h.top)&&(d=Math.max(d,h.top)),[f,d]}function to(l,f,d){var h=Math.round(f/l[0])*l[0],p=Math.round(d/l[1])*l[1];return[h,p]}function ro(l){return l.props.axis==="both"||l.props.axis==="x"}function no(l){return l.props.axis==="both"||l.props.axis==="y"}function Br(l,f,d){var h=typeof f=="number"?Ga(l,f):null;if(typeof f=="number"&&!h)return null;var p=Yr(d),P=d.props.offsetParent||p.offsetParent||p.ownerDocument.body;return Ya(h||l,P)}function Vr(l,f,d){var h=l.state,p=!ie(h.lastX),P=Yr(l);return p?{node:P,deltaX:0,deltaY:0,lastX:f,lastY:d,x:f,y:d}:{node:P,deltaX:f-h.lastX,deltaY:d-h.lastY,lastX:h.lastX,lastY:h.lastY,x:f,y:d}}function Hr(l,f){var d=l.props.scale;return{node:f.node,x:l.state.x+f.deltaX/d,y:l.state.y+f.deltaY/d,deltaX:f.deltaX/d,deltaY:f.deltaY/d,lastX:l.state.x,lastY:l.state.y}}function ao(l){return{left:l.left,top:l.top,right:l.right,bottom:l.bottom}}function Yr(l){var f=r.findDOMNode(l);if(!f)throw new Error("<DraggableCore>: Unmounted during event!");return f}function ps(){}var ot={touch:{start:"touchstart",move:"touchmove",stop:"touchend"},mouse:{start:"mousedown",move:"mousemove",stop:"mouseup"}},ft=ot.mouse,yt=function(l){Bt(f,l);function f(){var d,h,p,P;fe(this,f);for(var j=arguments.length,N=Array(j),$=0;$<j;$++)N[$]=arguments[$];return P=(h=(p=mt(this,(d=f.__proto__||Object.getPrototypeOf(f)).call.apply(d,[this].concat(N))),p),p.state={dragging:!1,lastX:NaN,lastY:NaN,touchIdentifier:null},p.handleDragStart=function(M){if(p.props.onMouseDown(M),!p.props.allowAnyClick&&typeof M.button=="number"&&M.button!==0)return!1;var W=r.findDOMNode(p);if(!W||!W.ownerDocument||!W.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");var De=W.ownerDocument;if(!(p.props.disabled||!(M.target instanceof De.defaultView.Node)||p.props.handle&&!At(M.target,p.props.handle,W)||p.props.cancel&&At(M.target,p.props.cancel,W))){var Ue=Wa(M);p.setState({touchIdentifier:Ue});var Me=Br(M,Ue,p);if(Me!=null){var _e=Me.x,ct=Me.y,Pt=Vr(p,_e,ct);p.props.onStart;var Gt=p.props.onStart(M,Pt);Gt!==!1&&(p.props.enableUserSelectHack&&Ka(De),p.setState({dragging:!0,lastX:_e,lastY:ct}),Yt(De,ft.move,p.handleDrag),Yt(De,ft.stop,p.handleDragStop))}}},p.handleDrag=function(M){M.type==="touchmove"&&M.preventDefault();var W=Br(M,p.state.touchIdentifier,p);if(W!=null){var De=W.x,Ue=W.y;if(Array.isArray(p.props.grid)){var Me=De-p.state.lastX,_e=Ue-p.state.lastY,ct=to(p.props.grid,Me,_e),Pt=Vt(ct,2);if(Me=Pt[0],_e=Pt[1],!Me&&!_e)return;De=p.state.lastX+Me,Ue=p.state.lastY+_e}var Gt=Vr(p,De,Ue),Xr=p.props.onDrag(M,Gt);if(Xr===!1){try{p.handleDragStop(new MouseEvent("mouseup"))}catch{var vr=document.createEvent("MouseEvents");vr.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),p.handleDragStop(vr)}return}p.setState({lastX:De,lastY:Ue})}},p.handleDragStop=function(M){if(p.state.dragging){var W=Br(M,p.state.touchIdentifier,p);if(W!=null){var De=W.x,Ue=W.y,Me=Vr(p,De,Ue),_e=r.findDOMNode(p);_e&&p.props.enableUserSelectHack&&Kn(_e.ownerDocument),p.setState({dragging:!1,lastX:NaN,lastY:NaN}),p.props.onStop(M,Me),_e&&(at(_e.ownerDocument,ft.move,p.handleDrag),at(_e.ownerDocument,ft.stop,p.handleDragStop))}}},p.onMouseDown=function(M){return ft=ot.mouse,p.handleDragStart(M)},p.onMouseUp=function(M){return ft=ot.mouse,p.handleDragStop(M)},p.onTouchStart=function(M){return ft=ot.touch,p.handleDragStart(M)},p.onTouchEnd=function(M){return ft=ot.touch,p.handleDragStop(M)},h),mt(p,P)}return Ee(f,[{key:"componentWillUnmount",value:function(){var h=r.findDOMNode(this);if(h){var p=h.ownerDocument;at(p,ot.mouse.move,this.handleDrag),at(p,ot.touch.move,this.handleDrag),at(p,ot.mouse.stop,this.handleDragStop),at(p,ot.touch.stop,this.handleDragStop),this.props.enableUserSelectHack&&Kn(p)}}},{key:"render",value:function(){return t.cloneElement(t.Children.only(this.props.children),{style:Za(this.props.children.props.style),onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}]),f}(t.Component);yt.displayName="DraggableCore",yt.propTypes={allowAnyClick:A.bool,disabled:A.bool,enableUserSelectHack:A.bool,offsetParent:function(f,d){if(f[d]&&f[d].nodeType!==1)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:A.arrayOf(A.number),scale:A.number,handle:A.string,cancel:A.string,onStart:A.func,onDrag:A.func,onStop:A.func,onMouseDown:A.func,className:Le,style:Le,transform:Le},yt.defaultProps={allowAnyClick:!1,cancel:null,disabled:!1,enableUserSelectHack:!0,offsetParent:null,handle:null,grid:null,transform:null,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){}};var bt=function(l){Bt(f,l);function f(d){fe(this,f);var h=mt(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,d));return h.onDragStart=function(p,P){var j=h.props.onStart(p,Hr(h,P));if(j===!1)return!1;h.setState({dragging:!0,dragged:!0})},h.onDrag=function(p,P){if(!h.state.dragging)return!1;var j=Hr(h,P),N={x:j.x,y:j.y};if(h.props.bounds){var $=N.x,M=N.y;N.x+=h.state.slackX,N.y+=h.state.slackY;var W=eo(h,N.x,N.y),De=Vt(W,2),Ue=De[0],Me=De[1];N.x=Ue,N.y=Me,N.slackX=h.state.slackX+($-N.x),N.slackY=h.state.slackY+(M-N.y),j.x=N.x,j.y=N.y,j.deltaX=N.x-h.state.x,j.deltaY=N.y-h.state.y}var _e=h.props.onDrag(p,j);if(_e===!1)return!1;h.setState(N)},h.onDragStop=function(p,P){if(!h.state.dragging)return!1;var j=h.props.onStop(p,Hr(h,P));if(j===!1)return!1;var N={dragging:!1,slackX:0,slackY:0},$=!!h.props.position;if($){var M=h.props.position,W=M.x,De=M.y;N.x=W,N.y=De}h.setState(N)},h.state={dragging:!1,dragged:!1,x:d.position?d.position.x:d.defaultPosition.x,y:d.position?d.position.y:d.defaultPosition.y,slackX:0,slackY:0,isElementSVG:!1},d.position&&!(d.onDrag||d.onStop)&&console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element."),h}return Ee(f,[{key:"componentDidMount",value:function(){typeof window.SVGElement<"u"&&r.findDOMNode(this)instanceof window.SVGElement&&this.setState({isElementSVG:!0})}},{key:"componentWillReceiveProps",value:function(h){h.position&&(!this.props.position||h.position.x!==this.props.position.x||h.position.y!==this.props.position.y)&&this.setState({x:h.position.x,y:h.position.y})}},{key:"componentWillUnmount",value:function(){this.setState({dragging:!1})}},{key:"render",value:function(){var h,p={},P=null,j=!!this.props.position,N=!j||this.state.dragging,$=this.props.position||this.props.defaultPosition,M={x:ro(this)&&N?this.state.x:$.x,y:no(this)&&N?this.state.y:$.y};this.state.isElementSVG?P=qa(M,this.props.positionOffset):p=Xa(M,this.props.positionOffset);var W=this.props,De=W.defaultClassName,Ue=W.defaultClassNameDragging,Me=W.defaultClassNameDragged,_e=t.Children.only(this.props.children),ct=Re(_e.props.className||"",De,(h={},Ne(h,Ue,this.state.dragging),Ne(h,Me,this.state.dragged),h));return t.createElement(yt,ke({},this.props,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),t.cloneElement(_e,{className:ct,style:ke({},_e.props.style,p),transform:P}))}}]),f}(t.Component);return bt.displayName="Draggable",bt.propTypes=ke({},yt.propTypes,{axis:A.oneOf(["both","x","y","none"]),bounds:A.oneOfType([A.shape({left:A.number,right:A.number,top:A.number,bottom:A.number}),A.string,A.oneOf([!1])]),defaultClassName:A.string,defaultClassNameDragging:A.string,defaultClassNameDragged:A.string,defaultPosition:A.shape({x:A.number,y:A.number}),positionOffset:A.shape({x:A.oneOfType([A.number,A.string]),y:A.oneOfType([A.number,A.string])}),position:A.shape({x:A.number,y:A.number}),className:Le,style:Le,transform:Le}),bt.defaultProps=ke({},yt.defaultProps,{axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},position:null,scale:1}),bt.default=bt,bt.DraggableCore=yt,bt})})(Ha);var es=Ha.exports;Object.defineProperty(Ut,"__esModule",{value:!0});Ut.Dropdown=Ut.DraggableAttribute=void 0;var Bn=function(){function n(e,r){for(var t=0;t<r.length;t++){var a=r[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(e,r,t){return r&&n(e.prototype,r),t&&n(e,t),e}}(),ts=Fe,L=zt(ts),rs=pr,Te=zt(rs),ns=qo,cn=zt(ns),dn=be,as=oi,Vn=zt(as),os=Qi,is=zt(os),ss=es,ls=zt(ss);function zt(n){return n&&n.__esModule?n:{default:n}}function er(n,e,r){return e in n?Object.defineProperty(n,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[e]=r,n}function Hn(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function Yn(n,e){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:n}function Xn(n,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(n,e):n.__proto__=e)}var qn=Ut.DraggableAttribute=function(n){Xn(e,n);function e(r){Hn(this,e);var t=Yn(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,r));return t.state={open:!1,filterText:""},t}return Bn(e,[{key:"toggleValue",value:function(t){t in this.props.valueFilter?this.props.removeValuesFromFilter(this.props.name,[t]):this.props.addValuesToFilter(this.props.name,[t])}},{key:"matchesFilter",value:function(t){return t.toLowerCase().trim().includes(this.state.filterText.toLowerCase().trim())}},{key:"selectOnly",value:function(t,a){t.stopPropagation(),this.props.setValuesInFilter(this.props.name,Object.keys(this.props.attrValues).filter(function(o){return o!==a}))}},{key:"getFilterBox",value:function(){var t=this,a=Object.keys(this.props.attrValues).length<this.props.menuLimit,o=Object.keys(this.props.attrValues),i=o.filter(this.matchesFilter.bind(this)).sort(this.props.sorter);return L.default.createElement(ls.default,{handle:".pvtDragHandle"},L.default.createElement("div",{className:"pvtFilterBox",style:{display:"block",cursor:"initial",zIndex:this.props.zIndex},onClick:function(){return t.props.moveFilterBoxToTop(t.props.name)}},L.default.createElement("a",{onClick:function(){return t.setState({open:!1})},className:"pvtCloseX"},"×"),L.default.createElement("span",{className:"pvtDragHandle"},"☰"),L.default.createElement("h4",null,this.props.name),a||L.default.createElement("p",null,"(too many values to show)"),a&&L.default.createElement("p",null,L.default.createElement("input",{type:"text",placeholder:"Filter values",className:"pvtSearch",value:this.state.filterText,onChange:function(u){return t.setState({filterText:u.target.value})}}),L.default.createElement("br",null),L.default.createElement("a",{role:"button",className:"pvtButton",onClick:function(){return t.props.removeValuesFromFilter(t.props.name,Object.keys(t.props.attrValues).filter(t.matchesFilter.bind(t)))}},"Select ",o.length===i.length?"All":i.length)," ",L.default.createElement("a",{role:"button",className:"pvtButton",onClick:function(){return t.props.addValuesToFilter(t.props.name,Object.keys(t.props.attrValues).filter(t.matchesFilter.bind(t)))}},"Deselect ",o.length===i.length?"All":i.length)),a&&L.default.createElement("div",{className:"pvtCheckContainer"},i.map(function(s){return L.default.createElement("p",{key:s,onClick:function(){return t.toggleValue(s)},className:s in t.props.valueFilter?"":"selected"},L.default.createElement("a",{className:"pvtOnly",onClick:function(c){return t.selectOnly(c,s)}},"only"),L.default.createElement("a",{className:"pvtOnlySpacer"}," "),s===""?L.default.createElement("em",null,"null"):s)}))))}},{key:"toggleFilterBox",value:function(){this.setState({open:!this.state.open}),this.props.moveFilterBoxToTop(this.props.name)}},{key:"render",value:function(){var t=Object.keys(this.props.valueFilter).length!==0?"pvtFilteredAttribute":"";return L.default.createElement("li",{"data-id":this.props.name},L.default.createElement("span",{className:"pvtAttr "+t},this.props.name,L.default.createElement("span",{className:"pvtTriangle",onClick:this.toggleFilterBox.bind(this)}," ","▾")),this.state.open?this.getFilterBox():null)}}]),e}(L.default.Component);qn.defaultProps={valueFilter:{}};qn.propTypes={name:Te.default.string.isRequired,addValuesToFilter:Te.default.func.isRequired,removeValuesFromFilter:Te.default.func.isRequired,attrValues:Te.default.objectOf(Te.default.number).isRequired,valueFilter:Te.default.objectOf(Te.default.bool),moveFilterBoxToTop:Te.default.func.isRequired,sorter:Te.default.func.isRequired,menuLimit:Te.default.number,zIndex:Te.default.number};var pn=Ut.Dropdown=function(n){Xn(e,n);function e(){return Hn(this,e),Yn(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return Bn(e,[{key:"render",value:function(){var t=this;return L.default.createElement("div",{className:"pvtDropdown",style:{zIndex:this.props.zIndex}},L.default.createElement("div",{onClick:function(o){o.stopPropagation(),t.props.toggle()},className:"pvtDropdownValue pvtDropdownCurrent "+(this.props.open?"pvtDropdownCurrentOpen":""),role:"button"},L.default.createElement("div",{className:"pvtDropdownIcon"},this.props.open?"×":"▾"),this.props.current||L.default.createElement("span",null," ")),this.props.open&&L.default.createElement("div",{className:"pvtDropdownMenu"},this.props.values.map(function(a){return L.default.createElement("div",{key:a,role:"button",onClick:function(i){i.stopPropagation(),t.props.current===a?t.props.toggle():t.props.setValue(a)},className:"pvtDropdownValue "+(a===t.props.current?"pvtDropdownActiveValue":"")},a)})))}}]),e}(L.default.PureComponent),Gn=function(n){Xn(e,n);function e(r){Hn(this,e);var t=Yn(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,r));return t.state={unusedOrder:[],zIndices:{},maxZIndex:1e3,openDropdown:!1,attrValues:{},materializedInput:[]},t}return Bn(e,[{key:"componentDidMount",value:function(){this.materializeInput(this.props.data)}},{key:"componentDidUpdate",value:function(){this.materializeInput(this.props.data)}},{key:"materializeInput",value:function(t){if(this.state.data!==t){var a={data:t,attrValues:{},materializedInput:[]},o=0;dn.PivotData.forEachRecord(a.data,this.props.derivedAttributes,function(i){a.materializedInput.push(i);var s=!0,u=!1,c=void 0;try{for(var g=Object.keys(i)[Symbol.iterator](),m;!(s=(m=g.next()).done);s=!0){var b=m.value;b in a.attrValues||(a.attrValues[b]={},o>0&&(a.attrValues[b].null=o))}}catch(E){u=!0,c=E}finally{try{!s&&g.return&&g.return()}finally{if(u)throw c}}for(var O in a.attrValues){var D=O in i?i[O]:"null";D in a.attrValues[O]||(a.attrValues[O][D]=0),a.attrValues[O][D]++}o++}),this.setState(a)}}},{key:"sendPropUpdate",value:function(t){this.props.onChange((0,cn.default)(this.props,t))}},{key:"propUpdater",value:function(t){var a=this;return function(o){return a.sendPropUpdate(er({},t,{$set:o}))}}},{key:"setValuesInFilter",value:function(t,a){this.sendPropUpdate({valueFilter:er({},t,{$set:a.reduce(function(o,i){return o[i]=!0,o},{})})})}},{key:"addValuesToFilter",value:function(t,a){t in this.props.valueFilter?this.sendPropUpdate({valueFilter:er({},t,a.reduce(function(o,i){return o[i]={$set:!0},o},{}))}):this.setValuesInFilter(t,a)}},{key:"removeValuesFromFilter",value:function(t,a){this.sendPropUpdate({valueFilter:er({},t,{$unset:a})})}},{key:"moveFilterBoxToTop",value:function(t){this.setState((0,cn.default)(this.state,{maxZIndex:{$set:this.state.maxZIndex+1},zIndices:er({},t,{$set:this.state.maxZIndex+1})}))}},{key:"isOpen",value:function(t){return this.state.openDropdown===t}},{key:"makeDnDCell",value:function(t,a,o){var i=this;return L.default.createElement(is.default,{options:{group:"shared",ghostClass:"pvtPlaceholder",filter:".pvtFilterBox",preventOnFilter:!1},tag:"td",className:o,onChange:a},t.map(function(s){return L.default.createElement(qn,{name:s,key:s,attrValues:i.state.attrValues[s],valueFilter:i.props.valueFilter[s]||{},sorter:(0,dn.getSort)(i.props.sorters,s),menuLimit:i.props.menuLimit,setValuesInFilter:i.setValuesInFilter.bind(i),addValuesToFilter:i.addValuesToFilter.bind(i),moveFilterBoxToTop:i.moveFilterBoxToTop.bind(i),removeValuesFromFilter:i.removeValuesFromFilter.bind(i),zIndex:i.state.zIndices[s]||i.state.maxZIndex})}))}},{key:"render",value:function(){var t=this,a=this.props.aggregators[this.props.aggregatorName]([])().numInputs||0,o=this.props.aggregators[this.props.aggregatorName]([])().outlet,i=this.props.rendererName in this.props.renderers?this.props.rendererName:Object.keys(this.props.renderers)[0],s=L.default.createElement("td",{className:"pvtRenderers"},L.default.createElement(pn,{current:i,values:Object.keys(this.props.renderers),open:this.isOpen("renderer"),zIndex:this.isOpen("renderer")?this.state.maxZIndex+1:1,toggle:function(){return t.setState({openDropdown:t.isOpen("renderer")?!1:"renderer"})},setValue:this.propUpdater("rendererName")})),u={key_a_to_z:{rowSymbol:"↕",colSymbol:"↔",next:"value_a_to_z"},value_a_to_z:{rowSymbol:"↓",colSymbol:"→",next:"value_z_to_a"},value_z_to_a:{rowSymbol:"↑",colSymbol:"←",next:"key_a_to_z"}},c=L.default.createElement("td",{className:"pvtVals"},L.default.createElement(pn,{current:this.props.aggregatorName,values:Object.keys(this.props.aggregators),open:this.isOpen("aggregators"),zIndex:this.isOpen("aggregators")?this.state.maxZIndex+1:1,toggle:function(){return t.setState({openDropdown:t.isOpen("aggregators")?!1:"aggregators"})},setValue:this.propUpdater("aggregatorName")}),L.default.createElement("a",{role:"button",className:"pvtRowOrder",onClick:function(){return t.propUpdater("rowOrder")(u[t.props.rowOrder].next)}},u[this.props.rowOrder].rowSymbol),L.default.createElement("a",{role:"button",className:"pvtColOrder",onClick:function(){return t.propUpdater("colOrder")(u[t.props.colOrder].next)}},u[this.props.colOrder].colSymbol),a>0&&L.default.createElement("br",null),new Array(a).fill().map(function(w,C){return[L.default.createElement(pn,{key:C,current:t.props.vals[C],values:Object.keys(t.state.attrValues).filter(function(F){return!t.props.hiddenAttributes.includes(F)&&!t.props.hiddenFromAggregators.includes(F)}),open:t.isOpen("val"+C),zIndex:t.isOpen("val"+C)?t.state.maxZIndex+1:1,toggle:function(){return t.setState({openDropdown:t.isOpen("val"+C)?!1:"val"+C})},setValue:function(k){return t.sendPropUpdate({vals:{$splice:[[C,1,k]]}})}}),C+1!==a?L.default.createElement("br",{key:"br"+C}):null]}),o&&o(this.props.data)),g=Object.keys(this.state.attrValues).filter(function(w){return!t.props.rows.includes(w)&&!t.props.cols.includes(w)&&!t.props.hiddenAttributes.includes(w)&&!t.props.hiddenFromDragDrop.includes(w)}).sort((0,dn.sortAs)(this.state.unusedOrder)),m=g.reduce(function(w,C){return w+C.length},0),b=m<this.props.unusedOrientationCutoff,O=this.makeDnDCell(g,function(w){return t.setState({unusedOrder:w})},"pvtAxisContainer pvtUnused "+(b?"pvtHorizList":"pvtVertList")),D=this.props.cols.filter(function(w){return!t.props.hiddenAttributes.includes(w)&&!t.props.hiddenFromDragDrop.includes(w)}),E=this.makeDnDCell(D,this.propUpdater("cols"),"pvtAxisContainer pvtHorizList pvtCols"),v=this.props.rows.filter(function(w){return!t.props.hiddenAttributes.includes(w)&&!t.props.hiddenFromDragDrop.includes(w)}),S=this.makeDnDCell(v,this.propUpdater("rows"),"pvtAxisContainer pvtVertList pvtRows"),_=L.default.createElement("td",{className:"pvtOutput"},L.default.createElement(Vn.default,(0,cn.default)(this.props,{data:{$set:this.state.materializedInput}})));return b?L.default.createElement("table",{className:"pvtUi"},L.default.createElement("tbody",{onClick:function(){return t.setState({openDropdown:!1})}},L.default.createElement("tr",null,s,O),L.default.createElement("tr",null,c,E),L.default.createElement("tr",null,S,_))):L.default.createElement("table",{className:"pvtUi"},L.default.createElement("tbody",{onClick:function(){return t.setState({openDropdown:!1})}},L.default.createElement("tr",null,s,c,E),L.default.createElement("tr",null,O,S,_)))}}]),e}(L.default.PureComponent);Gn.propTypes=Object.assign({},Vn.default.propTypes,{onChange:Te.default.func.isRequired,hiddenAttributes:Te.default.arrayOf(Te.default.string),hiddenFromAggregators:Te.default.arrayOf(Te.default.string),hiddenFromDragDrop:Te.default.arrayOf(Te.default.string),unusedOrientationCutoff:Te.default.number,menuLimit:Te.default.number});Gn.defaultProps=Object.assign({},Vn.default.defaultProps,{hiddenAttributes:[],hiddenFromAggregators:[],hiddenFromDragDrop:[],unusedOrientationCutoff:85,menuLimit:500});var us=Ut.default=Gn;const fs=async()=>{const[{default:n},e,{default:r}]=await Promise.all([qr(()=>import("./PlotlyRenderers-px7c2MoL.js").then(a=>a.P),__vite__mapDeps([0,1,2]),import.meta.url),qr(()=>import("./charts-plotly-BhN4fPIu.js").then(a=>a.P),__vite__mapDeps([3,1,2,4,5]),import.meta.url),qr(()=>import("./charts-plotly-BhN4fPIu.js").then(a=>a.f),__vite__mapDeps([3,1,2,4,5]),import.meta.url)]),t=r(e);return n(t)};let hn={...ai};const cs=bo(gn)(({theme:n})=>({marginLeft:n.spacing(1),padding:n.spacing(.5)})),ds=()=>{const{currentDataset:n,datasets:e,setCurrentDataset:r}=Sa(),[t,a]=Fe.useState({}),[o,i]=Fe.useState(!0),[s,u]=Fe.useState(!1),c=vo(),g=Qn(c.breakpoints.down("sm")),m=Qn(c.breakpoints.between("sm","md")),b=n?n.data:[];Fe.useEffect(()=>{a({rendererName:"Table"})},[n==null?void 0:n.id]),Fe.useEffect(()=>{t.rendererName&&!["Table","Table Barchart","Heatmap","Row Heatmap","Col Heatmap"].includes(t.rendererName)&&!s&&fs().then(E=>{hn={...hn,...E},u(!0)}).catch(E=>{console.error("Failed to load Plotly renderers:",E)})},[t.rendererName,s]),Fe.useEffect(()=>{if(t.rendererName&&t.rendererName!=="Table"){const D={width:g?300:m?500:700,height:g?300:m?400:500,responsive:!0};a(E=>({...E,plotlyOptions:D}))}},[g,m,t.rendererName]);const O={menuLimit:500,tableColorScaleGenerator:D=>E=>{const v=c.palette.mode==="dark"?["#1a237e","#283593","#3949ab","#5c6bc0","#7986cb"]:["#e3f2fd","#bbdefb","#90caf9","#64b5f6","#42a5f5"],S=D.indexOf(E)/D.length,_=Math.min(Math.floor(S*v.length),v.length-1);return{backgroundColor:v[_]}},unusedAttrsVertical:!1,showUI:!0,hiddenAttributes:[],hiddenFromAggregators:[],hiddenFromDragDrop:[],sorters:{},rowTotal:!0,colTotal:!0,autoSortUnusedAttrs:!1,onRefresh:D=>{a({...D})}};return y.jsxs(tt,{sx:{p:{xs:1,sm:2,md:3},width:"100%"},children:[y.jsx(Co,{title:"Pivot Analysis",description:"Interactively summarize, analyze, and visualize your data using pivot tables and charts."}),y.jsx(ea,{sx:{mb:{xs:2,md:3},mt:{xs:1,md:2}},children:y.jsxs(ta,{sx:{p:{xs:2,md:3}},children:[y.jsx(et,{variant:"h6",gutterBottom:!0,children:"1. Select Dataset"}),y.jsxs(yo,{direction:"row",spacing:2,alignItems:"center",sx:{mb:2},children:[y.jsx(tt,{sx:{flexGrow:1},children:y.jsx(Ao,{value:(n==null?void 0:n.id)||"",onChange:D=>{const E=e.find(v=>v.id===D);r(E||null)},variant:"default",showActions:!0})}),y.jsx(Uo,{size:"small"})]})]})}),n&&y.jsx(ea,{children:y.jsxs(ta,{sx:{p:{xs:2,md:3}},children:[y.jsx(et,{variant:"h6",gutterBottom:!0,children:"2. Configure Pivot Table & Visualization"}),y.jsxs(tt,{sx:{display:"flex",alignItems:"center",mb:1},children:[y.jsx(et,{variant:"subtitle2",children:"How to use the Pivot Table"}),y.jsx(cs,{onClick:()=>i(!o),size:"small","aria-expanded":o,"aria-label":"toggle instructions",children:o?y.jsx(Da,{fontSize:"small"}):y.jsx(_a,{fontSize:"small"})})]}),y.jsx(wo,{in:o,children:y.jsx(wa,{severity:"info",sx:{mb:3},children:y.jsxs(Jr,{container:!0,spacing:1,children:[y.jsx(Jr,{item:!0,xs:12,md:6,children:y.jsxs(et,{variant:"body2",component:"div",children:[y.jsx("strong",{children:"1. Define your analysis structure:"}),y.jsxs("ul",{style:{paddingLeft:"20px",marginTop:"4px",marginBottom:"4px"},children:[y.jsxs("li",{children:["Drag fields to ",y.jsx("strong",{children:"Rows"})," area to group data vertically"]}),y.jsxs("li",{children:["Drag fields to ",y.jsx("strong",{children:"Columns"})," area to group data horizontally"]}),y.jsxs("li",{children:["Drag fields to ",y.jsx("strong",{children:"Values"})," area to calculate metrics"]})]})]})}),y.jsx(Jr,{item:!0,xs:12,md:6,children:y.jsxs(et,{variant:"body2",component:"div",children:[y.jsx("strong",{children:"2. Customize your view:"}),y.jsxs("ul",{style:{paddingLeft:"20px",marginTop:"4px",marginBottom:"4px"},children:[y.jsxs("li",{children:["Select a ",y.jsx("strong",{children:"Renderer"})," to switch between tables and charts"]}),y.jsxs("li",{children:["Use ",y.jsx("strong",{children:"Aggregator"})," to change calculation method (sum, average, etc.)"]}),y.jsxs("li",{children:["Apply filters by dragging fields to the ",y.jsx("strong",{children:"Filters"})," area"]})]})]})})]})})}),b.length>0?y.jsx(tt,{sx:{overflowX:"auto",overflowY:"auto",maxWidth:"100%"},children:y.jsxs(tt,{sx:{position:"relative",mb:2},children:[y.jsx(ba,{title:y.jsxs(et,{variant:"body2",children:[y.jsx("strong",{children:"Tips:"}),y.jsxs("ul",{style:{paddingLeft:"15px",margin:"5px 0"},children:[y.jsx("li",{children:"Click and drag fields between areas"}),y.jsx("li",{children:"Click on values in the table to filter data"}),y.jsx("li",{children:"Right-click on attributes for more options"}),y.jsx("li",{children:"Double-click on a cell to see underlying data"})]})]}),placement:"top-start",arrow:!0,children:y.jsx(tt,{sx:{position:"absolute",top:"-5px",right:"5px",zIndex:10,bgcolor:"background.paper",borderRadius:"50%",p:.5,border:"1px solid",borderColor:"divider"},children:y.jsx(Ea,{color:"info",fontSize:"small"})})}),y.jsx(tt,{children:y.jsx(us,{data:b,onChange:D=>a(D),renderers:hn,...t,rendererName:t.rendererName||"Table",...O})})]})}):y.jsx(et,{sx:{mt:2,textAlign:"center",color:"text.secondary"},children:"The selected dataset is empty. Please choose a dataset with data to perform pivot analysis."})]})}),!n&&y.jsx(So,{sx:{p:{xs:2,md:3},mt:2,textAlign:"center"},children:y.jsx(et,{variant:"body1",color:"text.secondary",children:"Please select a dataset to begin pivot analysis."})})]})},ws=Object.freeze(Object.defineProperty({__proto__:null,default:ds},Symbol.toStringTag,{value:"Module"}));export{ws as P,be as U};
