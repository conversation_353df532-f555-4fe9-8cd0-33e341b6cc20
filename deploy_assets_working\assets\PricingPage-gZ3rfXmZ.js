import{u as S,a as P,j as e,B as o,A as C,T as k,I as D,b as I,d as W,e as a,f as u,H as z,C as E,F as m,g as $,h as x,i as r,G as l,S as T,k as y,l as F,D as R,L as q,m as B,n as M,o as V,q as G,r as H,s as L,v as O,w as Y,x as U,y as N,z as Q,E as J,J as K}from"./mui-libs-CfwFIaTD.js";import{r as h}from"./react-libs-Cr2nE3UY.js";import{F as X}from"./FeatureComparisonTable-D0Q0fTUa.js";import{u as Z}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const le=()=>{const n=S(),g=P(n.breakpoints.down("md")),i=Z(),[c,v]=h.useState(!1),[s,b]=h.useState("monthly");h.useEffect(()=>{const t=setTimeout(()=>v(!0),300);return()=>clearTimeout(t)},[]);const j=[{id:"guest",name:"Guest Access",price:"Free",period:"Forever",description:"Perfect for exploring and learning statistical analysis",icon:e.jsx(N,{sx:{fontSize:32}}),color:"#4caf50",buttonText:"Start Exploring",buttonVariant:"outlined",features:[{name:"Full app exploration",included:!0,description:"Access all features and interface"},{name:"Pro features preview",included:!0,description:"See advanced analysis capabilities"},{name:"Built-in sample datasets",included:!0,description:"Practice with curated datasets"},{name:"Teaching & demonstration",included:!0,description:"Perfect for educational use"},{name:"Personal data import",included:!1,description:"Cannot upload your own data"},{name:"Data persistence",included:!1,description:"Data resets on page refresh"},{name:"Cloud storage",included:!1},{name:"Pro analysis features",included:!1}]},{id:"standard",name:"Standard Account",price:"Free",period:"Currently",description:"Full local analysis capabilities with personal data",icon:e.jsx(Q,{sx:{fontSize:32}}),color:"#2196f3",buttonText:"Create Account",buttonVariant:"outlined",badge:"Most Popular",features:[{name:"All Guest Access features",included:!0},{name:"Personal data import",included:!0,description:"Upload CSV, Excel, and other formats"},{name:"Local data storage",included:!0,description:"Data saved in your browser"},{name:"Full analysis suite",included:!0,description:"Complete statistical toolkit"},{name:"Export capabilities",included:!0,description:"Save results and visualizations"},{name:"Pro analysis features",included:!1,description:"Advanced statistical methods"},{name:"Cloud synchronization",included:!1},{name:"Multi-device access",included:!1}]},{id:"pro",name:"Pro Account",price:s==="monthly"?"$10":"$96",period:s==="monthly"?"per month":"per year",monthlyPrice:"$10",annualPrice:"$96",annualSavings:"20%",billingOptions:["monthly","annual"],description:"Professional analysis with cloud features and advanced tools",icon:e.jsx(J,{sx:{fontSize:32}}),color:"#ff9800",buttonText:"Get Started",buttonVariant:"contained",highlighted:!0,features:[{name:"All Standard features",included:!0},{name:"Advanced Analysis",included:!0,description:"Advanced statistical methods"},{name:"Publication Ready",included:!0,description:"APA tables, methods text, figures"},{name:"Cloud data storage",included:!0,description:"Secure cloud backup"},{name:"Multi-device sync",included:!0,description:"Access from anywhere"},{name:"Priority support",included:!0,description:"Faster response times"},{name:"Collaboration tools",included:!0,description:"Share projects with team"},{name:"API access",included:!0,description:"Integrate with other tools"}]},{id:"edu",name:"Educational Account",price:"Free",period:"for .edu emails",emailRequirement:"Educational email required (.edu)",description:"Advanced Analysis included free for educational users",icon:e.jsx(K,{sx:{fontSize:32}}),color:"#9c27b0",buttonText:"Create Educational Account",buttonVariant:"outlined",badge:"Advanced Analysis Free",features:[{name:"All Standard Account features",included:!0},{name:"Advanced Analysis",included:!0,description:"FREE for .edu users"},{name:"Advanced statistical tests",included:!0,description:"ANOVA, Regression, etc."},{name:"Interactive visualizations",included:!0,description:"Professional charts"},{name:"Publication Ready",included:!1,description:"Upgrade to Pro for $10/month"},{name:"Cloud Storage",included:!1,description:"Upgrade to Pro for $10/month"},{name:"Multi-device sync",included:!1,description:"Upgrade to Pro for $10/month"}],upgradeOption:{name:"Educational Pro",price:"$10/month",description:"Same price as regular Pro - no educational discount",features:["Keep all current features","Add Publication Ready tools","Add Cloud Storage","Priority support"]}}],A=t=>{switch(t){case"guest":i("/app");break;case"standard":i("/app#/auth/login");break;case"pro":window.open("mailto:<EMAIL>?subject=Early Access Request - DataStatPro Pro Account&body=Hi DataStatPro Team,%0D%0A%0D%0AI would like to request early access to Pro account features during the development phase.%0D%0A%0D%0AMy use case:%0D%0A[Please describe how you plan to use DataStatPro]%0D%0A%0D%0ASpecific features I'm interested in:%0D%0A- Advanced statistical methods%0D%0A- Cloud data storage%0D%0A- Multi-device synchronization%0D%0A- [Add other features you're interested in]%0D%0A%0D%0AThank you for considering my request.%0D%0A%0D%0ABest regards");break;case"edu":i("/app#/auth/login");break;default:i("/app")}};return e.jsxs(o,{sx:{bgcolor:"background.default",minHeight:"100vh"},children:[e.jsx(C,{position:"fixed",sx:{zIndex:n.zIndex.drawer+1,boxShadow:"0 2px 10px rgba(0,0,0,0.08)",background:`linear-gradient(90deg, ${n.palette.primary.main} 0%, ${n.palette.primary.dark} 100%)`},elevation:0,children:e.jsxs(k,{sx:{minHeight:{xs:56,sm:64},px:{xs:1,sm:2}},children:[e.jsx(D,{color:"inherit","aria-label":"go back",edge:"start",onClick:()=>i(-1),sx:{mr:1},size:g?"small":"medium",children:e.jsx(I,{})}),e.jsxs(o,{onClick:()=>i("/"),sx:{display:"flex",alignItems:"center",mr:1,cursor:"pointer",textDecoration:"none",color:"inherit"},children:[e.jsx(W,{src:"/logo.png",alt:"DataStatPro",sx:{width:{xs:28,sm:32},height:{xs:28,sm:32},mr:1.5}}),e.jsx(a,{variant:g?"subtitle1":"h6",noWrap:!0,component:"div",sx:{fontWeight:"bold",letterSpacing:"0.5px"},children:"DataStatPro"})]}),e.jsx(o,{sx:{flexGrow:1}}),e.jsx(u,{color:"inherit",startIcon:e.jsx(z,{}),onClick:()=>i("/"),sx:{ml:1,display:{xs:"none",sm:"flex"}},children:"Home"}),e.jsx(u,{color:"inherit",onClick:()=>i("/app"),sx:{ml:1,bgcolor:"rgba(255, 255, 255, 0.1)","&:hover":{bgcolor:"rgba(255, 255, 255, 0.2)"}},children:"Launch App"})]})}),e.jsx(o,{sx:{pt:{xs:7,sm:8},pb:{xs:4,md:8}},children:e.jsxs(E,{maxWidth:"lg",children:[e.jsx(m,{in:c,timeout:800,children:e.jsxs(o,{textAlign:"center",mb:6,children:[e.jsx(a,{variant:"h2",component:"h1",fontWeight:"bold",sx:{mb:2,fontSize:{xs:"2.5rem",md:"3.5rem"},background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",backgroundClip:"text",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},children:"Choose Your Plan"}),e.jsx(a,{variant:"h5",color:"text.secondary",sx:{mb:4,maxWidth:"600px",mx:"auto",lineHeight:1.6},children:"Select the perfect plan for your statistical analysis needs"}),e.jsxs($,{severity:"info",sx:{maxWidth:"800px",mx:"auto",mb:3,borderRadius:2,"& .MuiAlert-message":{fontSize:"1rem"}},children:[e.jsx(a,{variant:"body1",fontWeight:"medium",children:"🚧 Development Phase Notice"}),e.jsx(a,{variant:"body2",sx:{mt:1},children:"DataStatPro is currently in active development. Payment processing is not yet available. All features are accessible for testing and evaluation purposes."})]}),e.jsx(o,{sx:{display:"flex",justifyContent:"center",mb:4},children:e.jsxs(o,{sx:{display:"flex",alignItems:"center",bgcolor:"background.paper",borderRadius:3,p:.5,border:1,borderColor:"divider",boxShadow:1},children:[e.jsx(u,{variant:s==="monthly"?"contained":"text",onClick:()=>b("monthly"),sx:{borderRadius:2,px:3,py:1,textTransform:"none",fontWeight:s==="monthly"?"bold":"normal"},children:"Monthly"}),e.jsxs(u,{variant:s==="annual"?"contained":"text",onClick:()=>b("annual"),sx:{borderRadius:2,px:3,py:1,textTransform:"none",fontWeight:s==="annual"?"bold":"normal",position:"relative"},children:["Annual",e.jsx(x,{label:"Save 20%",size:"small",color:"success",sx:{position:"absolute",top:-8,right:-8,fontSize:"0.7rem",height:18}})]})]})}),e.jsxs(o,{sx:{maxWidth:"800px",mx:"auto",mb:4,p:3,borderRadius:2,background:`linear-gradient(135deg, ${r(n.palette.primary.main,.08)} 0%, ${r(n.palette.primary.light,.12)} 100%)`,border:`1px solid ${r(n.palette.primary.main,.2)}`,color:"text.primary",textAlign:"center",position:"relative",overflow:"hidden"},children:[e.jsx(o,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:`url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23${n.palette.primary.main.replace("#","")}" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,opacity:.5}}),e.jsxs(o,{sx:{position:"relative",zIndex:1},children:[e.jsx(a,{variant:"h6",fontWeight:"bold",sx:{mb:2},children:"🚀 Want Early Access to Pro Features?"}),e.jsx(a,{variant:"body1",sx:{mb:2,color:"text.secondary"},children:"Interested in testing Pro or Educational features before our official launch? We're offering early access to qualified users during the development phase."}),e.jsxs(o,{sx:{display:"inline-flex",alignItems:"center",gap:1,bgcolor:n.palette.primary.main,px:3,py:1.5,borderRadius:2,boxShadow:`0 4px 12px ${r(n.palette.primary.main,.3)}`,cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-2px)",boxShadow:`0 6px 16px ${r(n.palette.primary.main,.4)}`}},onClick:()=>window.open("mailto:<EMAIL>?subject=Early Access Request - DataStatPro Pro Features"),children:[e.jsx(a,{variant:"body2",fontWeight:"medium",sx:{color:"white"},children:"Email us at:"}),e.jsx(a,{variant:"body1",fontWeight:"bold",sx:{color:"white",textDecoration:"underline"},children:"<EMAIL>"})]}),e.jsx(a,{variant:"body2",sx:{mt:2,color:"text.secondary",fontSize:"0.875rem"},children:"Include your use case and which features you'd like to evaluate"})]})]})]})}),e.jsx(l,{container:!0,spacing:4,justifyContent:"center",children:j.map((t,d)=>{var f;return e.jsx(l,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(T,{direction:"up",in:c,timeout:800+d*200,children:e.jsxs(y,{sx:{height:"100%",display:"flex",flexDirection:"column",position:"relative",borderRadius:3,transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",...t.highlighted&&{border:`2px solid ${t.color}`,transform:"scale(1.05)",boxShadow:`0 12px 40px ${r(t.color,.2)}`},"&:hover":{transform:t.highlighted?"scale(1.05)":"translateY(-8px)",boxShadow:`0 16px 50px ${r(t.color,.15)}`}},children:[t.badge&&e.jsx(x,{label:t.badge,sx:{position:"absolute",top:16,right:16,bgcolor:t.color,color:"white",fontWeight:"bold",fontSize:"0.75rem"}}),e.jsxs(F,{sx:{flexGrow:1,p:3},children:[e.jsxs(o,{textAlign:"center",mb:3,children:[e.jsx(o,{sx:{width:64,height:64,borderRadius:"50%",bgcolor:r(t.color,.1),color:t.color,display:"flex",alignItems:"center",justifyContent:"center",mx:"auto",mb:2},children:t.icon}),e.jsx(a,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:t.name}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{mb:2},children:t.description}),e.jsxs(o,{children:[e.jsx(a,{variant:"h3",component:"span",fontWeight:"bold",color:t.color,children:t.price}),e.jsx(a,{variant:"body2",color:"text.secondary",component:"span",children:t.period&&` ${t.period}`}),t.billingOptions&&s==="annual"&&t.annualSavings&&e.jsxs(o,{sx:{mt:1},children:[e.jsx(x,{label:`Save ${t.annualSavings}`,size:"small",color:"success",sx:{fontSize:"0.75rem"}}),e.jsxs(a,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:.5},children:["$",(parseFloat(((f=t.annualPrice)==null?void 0:f.replace("$",""))||"0")/12).toFixed(0),"/month when billed annually"]})]}),t.emailRequirement&&e.jsx(a,{variant:"caption",color:"warning.main",sx:{display:"block",mt:1,fontStyle:"italic"},children:t.emailRequirement})]})]}),e.jsx(R,{sx:{mb:2}}),e.jsx(q,{dense:!0,sx:{p:0},children:t.features.map((p,w)=>e.jsxs(B,{sx:{px:0,py:.5},children:[e.jsx(M,{sx:{minWidth:32},children:p.included?e.jsx(V,{sx:{color:"success.main",fontSize:20}}):e.jsx(G,{sx:{color:"text.disabled",fontSize:20}})}),e.jsx(H,{primary:p.name,secondary:p.description,primaryTypographyProps:{variant:"body2",color:p.included?"text.primary":"text.disabled"},secondaryTypographyProps:{variant:"caption",sx:{fontSize:"0.7rem"}}})]},w))})]}),e.jsx(L,{sx:{p:3,pt:0},children:e.jsx(u,{variant:t.buttonVariant,fullWidth:!0,size:"large",onClick:()=>A(t.id),sx:{py:1.5,fontWeight:"bold",borderRadius:2,...t.buttonVariant==="contained"&&{bgcolor:t.color,"&:hover":{bgcolor:r(t.color,.8)}},...t.buttonVariant==="outlined"&&{borderColor:t.color,color:t.color,"&:hover":{bgcolor:r(t.color,.1),borderColor:t.color}}},children:t.buttonText})})]})})},t.id)})}),e.jsx(m,{in:c,timeout:1e3,children:e.jsxs(o,{mt:8,children:[e.jsx(a,{variant:"h4",fontWeight:"bold",textAlign:"center",gutterBottom:!0,children:"Compare Features Across All Plans"}),e.jsx(a,{variant:"body1",textAlign:"center",color:"text.secondary",sx:{mb:4},children:"See exactly what's included with each account type"}),e.jsx(X,{showDescriptions:!0})]})}),e.jsx(m,{in:c,timeout:1200,children:e.jsxs(o,{mt:8,textAlign:"center",children:[e.jsx(a,{variant:"h4",fontWeight:"bold",gutterBottom:!0,children:"Why Choose DataStatPro?"}),e.jsx(l,{container:!0,spacing:4,sx:{mt:2},children:[{icon:e.jsx(O,{sx:{fontSize:40,color:"#4caf50"}}),title:"Secure & Private",description:"Your data is protected with enterprise-grade security"},{icon:e.jsx(Y,{sx:{fontSize:40,color:"#2196f3"}}),title:"Cross-Platform",description:"Works seamlessly across all devices and browsers"},{icon:e.jsx(U,{sx:{fontSize:40,color:"#ff9800"}}),title:"24/7 Support",description:"Get help whenever you need it from our expert team"}].map((t,d)=>e.jsx(l,{item:!0,xs:12,md:4,children:e.jsxs(o,{textAlign:"center",children:[t.icon,e.jsx(a,{variant:"h6",fontWeight:"bold",sx:{mt:2,mb:1},children:t.title}),e.jsx(a,{variant:"body2",color:"text.secondary",children:t.description})]})},d))})]})}),e.jsx(m,{in:c,timeout:1400,children:e.jsxs(o,{mt:8,children:[e.jsx(a,{variant:"h4",fontWeight:"bold",textAlign:"center",gutterBottom:!0,children:"Frequently Asked Questions"}),e.jsx(l,{container:!0,spacing:3,sx:{mt:2},children:[{question:"How can I get early access to Pro or Educational features?",answer:"During the development phase, you can request early access to Pro or Educational features <NAME_EMAIL>. Include details about your use case and which specific features you'd like to evaluate. We'll review your request and provide access for testing purposes."},{question:"When will payment processing be available?",answer:"Payment processing will be implemented in the next phase of development. All users can currently access the full application for testing and evaluation."},{question:"What happens to my data during development?",answer:"During the development phase, data is stored locally in your browser. Cloud storage will be available with Pro accounts once payment processing is implemented."},{question:"Can I upgrade or downgrade my plan later?",answer:"Yes, once payment processing is available, you can change your plan at any time. Changes will be reflected in your next billing cycle."},{question:"Is there a free trial for Pro features?",answer:"Currently, all features are accessible for testing. Once payment processing is implemented, we will offer a free trial period for Pro accounts."}].map((t,d)=>e.jsx(l,{item:!0,xs:12,md:6,children:e.jsxs(y,{sx:{height:"100%",p:2},children:[e.jsx(a,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:t.question}),e.jsx(a,{variant:"body2",color:"text.secondary",children:t.answer})]})},d))})]})})]})})]})};export{le as default};
