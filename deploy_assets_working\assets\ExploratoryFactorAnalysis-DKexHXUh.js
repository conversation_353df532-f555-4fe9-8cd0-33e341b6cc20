var Je=Object.defineProperty;var He=(y,l,p)=>l in y?Je(y,l,{enumerable:!0,configurable:!0,writable:!0,value:p}):y[l]=p;var J=(y,l,p)=>He(y,typeof l!="symbol"?l+"":l,p);import{u as Ye,j as e,B as u,e as i,g as oe,bv as Qe,f as q,bH as Ze,R as C,G as g,ai as B,b9 as H,ba as Y,bb as w,h as et,D as ne,c6 as je,a_ as tt,aj as v,ak as se,ah as ye,c8 as le,bc as F,ch as at,ae as rt,a6 as it,a7 as D,bt as ot,N as nt,ad as st,Z as lt,aF as ct,k as Ce,l as we,ao as P,ap as T,at as A,ar as x,as as n,ci as dt,bw as ce,aq as $,bS as Fe,bT as ke,bU as Se,bV as Me}from"./mui-libs-CfwFIaTD.js";import{r as h}from"./react-libs-Cr2nE3UY.js";import{a as mt,D as ut}from"./index-Bpan7Tbe.js";import{i as ht}from"./charts-plotly-BhN4fPIu.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./other-utils-CR9xr_gI.js";import"./charts-recharts-d3-BEF1Y_jn.js";class xt{constructor(){J(this,"pyodide",null);J(this,"isInitialized",!1);J(this,"initializationPromise",null)}async initialize(){if(!this.isInitialized){if(this.initializationPromise)return this.initializationPromise;this.initializationPromise=this.doInitialize(),await this.initializationPromise}}async doInitialize(){try{if(typeof window<"u"&&!window.loadPyodide){const l=document.createElement("script");l.src="https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js",document.head.appendChild(l),await new Promise((p,O)=>{l.onload=p,l.onerror=O})}this.pyodide=await window.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.25.0/full/"}),await this.pyodide.loadPackage(["numpy","scipy"]),console.log("Pyodide initialized for Factor Analysis"),this.setupFactorAnalysisImplementation(),this.isInitialized=!0}catch(l){throw console.error("Failed to initialize Pyodide:",l),new Error("Failed to initialize Python environment for factor analysis")}}setupFactorAnalysisImplementation(){this.pyodide.runPython(`
import numpy as np
import scipy.stats as stats
import scipy.linalg as linalg
from scipy.optimize import minimize
import json
import warnings
warnings.filterwarnings('ignore')

def calculate_correlation_matrix(data):
    """Calculate correlation matrix from data"""
    # Convert data to numpy array
    n_vars = len(data)
    n_obs = len(next(iter(data.values())))
    
    # Create data matrix
    X = np.array([data[key] for key in sorted(data.keys())]).T
    
    # Standardize data
    X_std = (X - np.mean(X, axis=0)) / np.std(X, axis=0, ddof=1)
    
    # Calculate correlation matrix
    corr_matrix = np.corrcoef(X_std.T)
    
    return corr_matrix, n_obs, X_std

def calculate_kmo_bartlett(corr_matrix, n_obs):
    """Calculate Kaiser-Meyer-Olkin (KMO) measure and Bartlett's test"""
    n_vars = corr_matrix.shape[0]
    
    # Calculate partial correlations
    try:
        inv_corr = np.linalg.inv(corr_matrix)
        partial_corr = -inv_corr / np.sqrt(np.outer(np.diag(inv_corr), np.diag(inv_corr)))
        np.fill_diagonal(partial_corr, 0)
    except:
        # If correlation matrix is singular, return poor KMO
        return 0.0, 0.0, 1.0
    
    # KMO calculation
    corr_sq = corr_matrix ** 2
    partial_corr_sq = partial_corr ** 2
    
    # Sum of squared correlations
    sum_corr_sq = np.sum(corr_sq) - np.trace(corr_sq)
    # Sum of squared partial correlations
    sum_partial_sq = np.sum(partial_corr_sq)
    
    # Overall KMO
    if sum_corr_sq + sum_partial_sq > 0:
        kmo = sum_corr_sq / (sum_corr_sq + sum_partial_sq)
    else:
        kmo = 0.0
    
    # Bartlett's test of sphericity
    det_corr = np.linalg.det(corr_matrix)
    if det_corr <= 0:
        bartlett_chi_square = 0.0
        bartlett_p_value = 1.0
    else:
        bartlett_chi_square = -(n_obs - 1 - (2 * n_vars + 5) / 6) * np.log(det_corr)
        df = n_vars * (n_vars - 1) / 2
        bartlett_p_value = 1 - stats.chi2.cdf(bartlett_chi_square, df)
    
    return kmo, bartlett_chi_square, bartlett_p_value

def extract_factors_pca(corr_matrix, n_factors):
    """Extract factors using Principal Component Analysis"""
    # Eigenvalue decomposition
    eigenvalues, eigenvectors = np.linalg.eigh(corr_matrix)
    
    # Sort in descending order
    idx = eigenvalues.argsort()[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]
    
    # Select first n_factors
    loadings = eigenvectors[:, :n_factors] @ np.diag(np.sqrt(eigenvalues[:n_factors]))
    
    return loadings, eigenvalues

def extract_factors_ml(corr_matrix, n_factors, max_iter=100):
    """Extract factors using Maximum Likelihood"""
    n_vars = corr_matrix.shape[0]
    
    # Initialize with PCA solution
    loadings_pca, _ = extract_factors_pca(corr_matrix, n_factors)
    
    # ML estimation using iterative approach
    uniqueness = 1 - np.sum(loadings_pca ** 2, axis=1)
    uniqueness = np.clip(uniqueness, 0.001, 0.999)
    
    for iteration in range(max_iter):
        # Update reduced correlation matrix
        psi = np.diag(uniqueness)
        reduced_corr = corr_matrix - psi
        
        # Extract factors
        try:
            eigenvalues, eigenvectors = np.linalg.eigh(reduced_corr)
            idx = eigenvalues.argsort()[::-1]
            eigenvalues = eigenvalues[idx]
            eigenvectors = eigenvectors[:, idx]
            
            # Keep only positive eigenvalues
            pos_idx = eigenvalues > 0
            if np.sum(pos_idx) < n_factors:
                break
                
            loadings = eigenvectors[:, :n_factors] @ np.diag(np.sqrt(eigenvalues[:n_factors]))
            
            # Update uniqueness
            new_uniqueness = 1 - np.sum(loadings ** 2, axis=1)
            new_uniqueness = np.clip(new_uniqueness, 0.001, 0.999)
            
            # Check convergence
            if np.max(np.abs(new_uniqueness - uniqueness)) < 0.001:
                break
                
            uniqueness = new_uniqueness
            
        except:
            break
    
    # Calculate eigenvalues for the final solution
    eigenvalues, _ = np.linalg.eigh(corr_matrix)
    eigenvalues = np.sort(eigenvalues)[::-1]
    
    return loadings, eigenvalues

def rotate_varimax(loadings, gamma=1.0, max_iter=100, tol=1e-6):
    """Varimax rotation (orthogonal)"""
    n_vars, n_factors = loadings.shape
    rotation_matrix = np.eye(n_factors)
    
    for _ in range(max_iter):
        old_rotation = rotation_matrix.copy()
        
        for i in range(n_factors - 1):
            for j in range(i + 1, n_factors):
                # Extract columns i and j
                u = loadings @ rotation_matrix[:, [i, j]]
                
                # Calculate rotation angle
                if gamma == 0:  # Quartimax
                    u_squared = u ** 2
                    numerator = 2 * np.sum(u[:, 0] * u[:, 1] * (u_squared[:, 0] - u_squared[:, 1]))
                    denominator = np.sum((u_squared[:, 0] - u_squared[:, 1]) ** 2)
                else:  # Varimax
                    u_squared = u ** 2
                    sum_u2 = np.sum(u_squared, axis=0)
                    numerator = np.sum(u[:, 0] * u[:, 1]) * n_vars - gamma * sum_u2[0] * sum_u2[1] / n_vars
                    denominator = (np.sum(u_squared[:, 0] ** 2) - gamma * sum_u2[0] ** 2 / n_vars - 
                                 np.sum(u_squared[:, 1] ** 2) + gamma * sum_u2[1] ** 2 / n_vars)
                
                # Calculate angle
                if abs(denominator) < 1e-10:
                    angle = 0
                else:
                    angle = 0.25 * np.arctan2(2 * numerator, denominator)
                
                # Apply rotation
                cos_angle = np.cos(angle)
                sin_angle = np.sin(angle)
                rotation_matrix[:, i], rotation_matrix[:, j] = (
                    cos_angle * rotation_matrix[:, i] + sin_angle * rotation_matrix[:, j],
                    -sin_angle * rotation_matrix[:, i] + cos_angle * rotation_matrix[:, j]
                )
        
        # Check convergence
        if np.max(np.abs(rotation_matrix - old_rotation)) < tol:
            break
    
    rotated_loadings = loadings @ rotation_matrix
    return rotated_loadings, rotation_matrix

def rotate_promax(loadings, power=4):
    """Promax rotation (oblique)"""
    # Start with varimax rotation
    varimax_loadings, _ = rotate_varimax(loadings)
    
    # Raise to power and normalize
    target = np.sign(varimax_loadings) * np.abs(varimax_loadings) ** power
    
    # Find transformation matrix
    try:
        transformation = np.linalg.lstsq(loadings, target, rcond=None)[0]
        inv_transformation = np.linalg.inv(transformation.T @ transformation)
        pattern_matrix = loadings @ transformation @ inv_transformation
        
        # Factor correlations
        factor_corr = inv_transformation @ transformation.T @ transformation @ inv_transformation
        
        # Normalize to get correlation matrix
        d = np.sqrt(np.diag(factor_corr))
        factor_corr = factor_corr / np.outer(d, d)
        
        return pattern_matrix, factor_corr
    except:
        # If transformation fails, return varimax solution
        return varimax_loadings, np.eye(loadings.shape[1])

def rotate_oblimin(loadings, delta=0):
    """Oblimin rotation (oblique)"""
    n_vars, n_factors = loadings.shape
    
    # Initialize with random rotation
    rotation_matrix = np.linalg.qr(np.random.randn(n_factors, n_factors))[0]
    
    max_iter = 100
    tol = 1e-6
    
    for _ in range(max_iter):
        old_rotation = rotation_matrix.copy()
        
        # Calculate gradient
        rotated = loadings @ rotation_matrix
        gradient = rotated.T @ (rotated ** 3) - delta * rotated.T @ rotated @ np.diag(np.sum(rotated ** 2, axis=0))
        
        # Update rotation matrix
        u, s, vt = np.linalg.svd(gradient)
        rotation_matrix = u @ vt
        
        # Check convergence
        if np.max(np.abs(rotation_matrix - old_rotation)) < tol:
            break
    
    # Calculate pattern matrix
    pattern_matrix = loadings @ rotation_matrix
    
    # Calculate factor correlations
    try:
        structure_matrix = loadings @ rotation_matrix
        factor_corr = np.corrcoef(structure_matrix.T)
    except:
        factor_corr = np.eye(n_factors)
    
    return pattern_matrix, factor_corr

def calculate_communalities(loadings):
    """Calculate communalities from loadings"""
    return np.sum(loadings ** 2, axis=1)

def determine_n_factors(eigenvalues, method='kaiser'):
    """Determine number of factors to extract"""
    if method == 'kaiser':
        return np.sum(eigenvalues > 1)
    else:  # scree
        # Simple scree test - look for elbow
        if len(eigenvalues) < 3:
            return 1
        
        # Calculate differences
        diffs = np.diff(eigenvalues)
        second_diffs = np.diff(diffs)
        
        # Find elbow point (where second derivative is maximum)
        elbow = np.argmax(second_diffs) + 2
        
        return min(elbow, len(eigenvalues) // 2)

def run_factor_analysis(data_dict):
    """Main factor analysis function"""
    try:
        # Extract parameters
        variables = data_dict.get('variables', {})
        variable_names = data_dict.get('variable_names', list(variables.keys()))
        n_factors = data_dict.get('n_factors', None)
        extraction_method = data_dict.get('extraction_method', 'pca')
        rotation_method = data_dict.get('rotation_method', 'varimax')
        factor_selection = data_dict.get('factor_selection', 'kaiser')
        min_loading = data_dict.get('min_loading', 0.0)
        
        print(f"Running factor analysis with {len(variables)} variables")
        
        # Calculate correlation matrix
        corr_matrix, n_obs, X_std = calculate_correlation_matrix(variables)
        
        # Calculate KMO and Bartlett's test
        kmo, bartlett_chi, bartlett_p = calculate_kmo_bartlett(corr_matrix, n_obs)
        
        # Extract factors
        if extraction_method == 'ml':
            loadings, eigenvalues = extract_factors_ml(corr_matrix, n_factors or 3)
        else:  # pca
            loadings, eigenvalues = extract_factors_pca(corr_matrix, n_factors or len(variables))
        
        # Determine number of factors if not specified
        if n_factors is None:
            n_factors = determine_n_factors(eigenvalues, factor_selection)
            loadings = loadings[:, :n_factors]
        
        # Apply rotation
        factor_correlations = None
        if n_factors > 1:
            if rotation_method == 'varimax':
                rotated_loadings, _ = rotate_varimax(loadings)
            elif rotation_method == 'promax':
                rotated_loadings, factor_correlations = rotate_promax(loadings)
            elif rotation_method == 'oblimin':
                rotated_loadings, factor_correlations = rotate_oblimin(loadings)
            else:
                rotated_loadings = loadings
        else:
            rotated_loadings = loadings
        
        # Apply minimum loading threshold
        if min_loading > 0:
            display_loadings = np.where(np.abs(rotated_loadings) < min_loading, 0, rotated_loadings)
        else:
            display_loadings = rotated_loadings
        
        # Calculate communalities
        communalities = calculate_communalities(rotated_loadings)
        communalities_dict = {name: float(comm) for name, comm in zip(variable_names, communalities)}
        
        # Calculate variance explained
        total_variance = np.sum(eigenvalues)
        variance_explained = eigenvalues / total_variance * 100
        cumulative_variance = np.cumsum(variance_explained)
        
        # Prepare results
        results = {
            'loadings': loadings.tolist(),
            'rotated_loadings': display_loadings.tolist(),
            'eigenvalues': eigenvalues.tolist(),
            'variance_explained': variance_explained.tolist(),
            'cumulative_variance': cumulative_variance.tolist(),
            'communalities': communalities_dict,
            'kmo': float(kmo),
            'bartlett_chi_square': float(bartlett_chi),
            'bartlett_p_value': float(bartlett_p),
            'n_factors': int(n_factors),
            'rotation_method': rotation_method,
            'extraction_method': extraction_method,
            'correlation_matrix': corr_matrix.tolist(),
            'n_observations': int(n_obs)
        }
        
        if factor_correlations is not None:
            results['factor_correlations'] = factor_correlations.tolist()
        
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'Factor analysis failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})

def calculate_factor_scores(factor_results_dict, data_dict):
    """Calculate factor scores using regression method"""
    try:
        # Extract data
        variables = data_dict.get('variables', {})
        rotated_loadings = np.array(factor_results_dict.get('rotated_loadings', []))
        corr_matrix = np.array(factor_results_dict.get('correlation_matrix', []))
        
        # Create data matrix
        X = np.array([variables[key] for key in sorted(variables.keys())]).T
        
        # Standardize data
        X_std = (X - np.mean(X, axis=0)) / np.std(X, axis=0, ddof=1)
        
        # Calculate factor score coefficients using regression method
        # F = (L'R^-1L)^-1 L'R^-1
        try:
            inv_corr = np.linalg.inv(corr_matrix)
            factor_score_coef = np.linalg.inv(rotated_loadings.T @ inv_corr @ rotated_loadings) @ rotated_loadings.T @ inv_corr
        except:
            # If matrix is singular, use pseudo-inverse
            factor_score_coef = np.linalg.pinv(rotated_loadings.T @ rotated_loadings) @ rotated_loadings.T
        
        # Calculate factor scores
        scores = X_std @ factor_score_coef.T
        
        results = {
            'scores': scores.tolist(),
            'method': 'regression'
        }
        
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'Factor score calculation failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})
    `)}async runFactorAnalysis(l){var K;await this.initialize();const p={variables:l.variables,variable_names:l.variable_names,n_factors:l.n_factors,extraction_method:l.extraction_method||"pca",rotation_method:l.rotation_method||"varimax",factor_selection:l.factor_selection||"kaiser",min_loading:l.min_loading||0};console.log("Running factor analysis with:",{nVariables:Object.keys(l.variables).length,nObservations:((K=l.variables[Object.keys(l.variables)[0]])==null?void 0:K.length)||0,extractionMethod:p.extraction_method,rotationMethod:p.rotation_method}),this.pyodide.globals.set("factor_data",this.pyodide.toPy(p));const O=this.pyodide.runPython("run_factor_analysis(factor_data)"),k=JSON.parse(O);if(k.error)throw new Error(`Factor analysis failed: ${k.error}`);return k}async calculateFactorScores(l,p){await this.initialize(),this.pyodide.globals.set("factor_results",this.pyodide.toPy(l)),this.pyodide.globals.set("score_data",this.pyodide.toPy(p));const O=this.pyodide.runPython("calculate_factor_scores(factor_results, score_data)"),k=JSON.parse(O);if(k.error)throw new Error(`Factor score calculation failed: ${k.error}`);return k}isReady(){return this.isInitialized}}const de=new xt,wt=()=>{var pe,be,ve;const{datasets:y,currentDataset:l,setCurrentDataset:p}=mt(),O=Ye(),[k,K]=h.useState((l==null?void 0:l.id)||""),[S,me]=h.useState([]),[ue,Ee]=h.useState("pca"),[Q,Oe]=h.useState("varimax"),[Z,he]=h.useState(""),[U,Re]=h.useState("kaiser"),[_,xe]=h.useState({showScreePlot:!0,showFactorMatrix:!0,showCommunalities:!0,showRotatedMatrix:!0,showCorrelationMatrix:!1,minimumLoading:.3}),[W,ge]=h.useState(!1),[X,_e]=h.useState(!1),[I,qe]=h.useState(!1),[fe,L]=h.useState(null),[a,M]=h.useState(null),[E,R]=h.useState(null),[z,Pe]=h.useState(0),[Te,ee]=h.useState(!1),[Ae,G]=h.useState(!1),[j,N]=h.useState({summary:!0,loadings:!0,communalities:!0,eigenvalues:!0,correlationMatrix:!1,factorCorrelations:!1}),te=h.useCallback(async()=>{if(!(I||X)){_e(!0);try{await de.initialize(),qe(!0)}catch(t){console.error("Failed to initialize Python environment:",t),L("Failed to initialize Python environment. Please refresh the page and try again.")}finally{_e(!1)}}},[I,X]);h.useEffect(()=>{te()},[te]),h.useEffect(()=>{const t=localStorage.getItem("factor_analysis_results");if(t)try{const r=JSON.parse(t);M(r)}catch(r){console.error("Error parsing saved factor analysis results:",r),localStorage.removeItem("factor_analysis_results")}},[]);const ae=(l==null?void 0:l.columns.filter(t=>t.type===ut.NUMERIC))||[],Ie=t=>{const r=t.target.value;K(r),me([]),M(null),R(null),localStorage.removeItem("factor_analysis_results");const s=y.find(c=>c.id===r);s&&p(s)},Le=t=>{const r=t.target.value,s=typeof r=="string"?r.split(","):r;me(s),M(null),R(null),localStorage.removeItem("factor_analysis_results")},ze=t=>{Ee(t.target.value),M(null),R(null),localStorage.removeItem("factor_analysis_results")},Ne=t=>{Oe(t.target.value),M(null),R(null),localStorage.removeItem("factor_analysis_results")},Be=t=>{Re(t.target.value),t.target.value!=="manual"&&he(""),M(null),R(null),localStorage.removeItem("factor_analysis_results")},$e=t=>{he(t.target.value),M(null),R(null),localStorage.removeItem("factor_analysis_results")},V=t=>r=>{if(t==="minimumLoading"){const s=parseFloat(r.target.value);!isNaN(s)&&s>=0&&s<1&&xe(c=>({...c,minimumLoading:s}))}else xe(s=>({...s,[t]:r.target.checked}))},Ve=(t,r)=>{Pe(r)},De=async()=>{if(!l||S.length<3){L("Please select at least 3 variables for factor analysis.");return}if(!I){L("Python environment is not ready. Please wait for initialization to complete.");return}ge(!0),L(null),M(null),R(null);try{const t=S.map(o=>l.columns.find(b=>b.id===o)).filter(Boolean),r=t.map(o=>o.name),s={};t.forEach((o,b)=>{const f=[];l.data.forEach(re=>{const ie=re[o.name];typeof ie=="number"&&!isNaN(ie)&&f.push(ie)}),s[`var_${b}`]=f});const c=Object.values(s).map(o=>o.length);if(new Set(c).size>1)throw new Error("Variables have different numbers of valid observations.");if(c[0]<10)throw new Error("Insufficient data for factor analysis. Need at least 10 valid observations.");const m={variables:s,variable_names:r,extraction_method:ue,rotation_method:Q,factor_selection:U,min_loading:_.minimumLoading};if(U==="manual"&&Z){const o=parseInt(Z);if(o>0&&o<=S.length)m.n_factors=o;else throw new Error(`Number of factors must be between 1 and ${S.length}`)}const d=await de.runFactorAnalysis(m);M(d),localStorage.setItem("factor_analysis_results",JSON.stringify(d))}catch(t){console.error("Factor analysis error:",t),L(`Error in factor analysis: ${t instanceof Error?t.message:String(t)}`)}finally{ge(!1)}},Ke=async()=>{if(!(!a||!l||!I))try{const t=S.map(m=>l.columns.find(d=>d.id===m)).filter(Boolean),r={};t.forEach((m,d)=>{const o=[];l.data.forEach(b=>{const f=b[m.name];typeof f=="number"&&!isNaN(f)&&o.push(f)}),r[`var_${d}`]=o});const s={variables:r,variable_names:t.map(m=>m.name)},c=await de.calculateFactorScores(a,s);R(c),ee(!0)}catch(t){console.error("Factor score calculation error:",t),L(`Error calculating factor scores: ${t instanceof Error?t.message:String(t)}`)}},Ue=()=>{if(!E)return;let t="Observation";for(let m=0;m<E.scores[0].length;m++)t+=`,Factor_${m+1}`;t+=`
`,E.scores.forEach((m,d)=>{t+=`${d+1}`,m.forEach(o=>{t+=`,${o.toFixed(6)}`}),t+=`
`});const r=new Blob([t],{type:"text/csv"}),s=URL.createObjectURL(r),c=document.createElement("a");c.href=s,c.download="factor_scores.csv",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(s)},We=()=>{var m;if(!a)return;let t=`EXPLORATORY FACTOR ANALYSIS RESULTS
`;if(t+=`====================================

`,j.summary&&(t+=`SUMMARY
`,t+=`-------
`,t+=`Extraction Method: ${a.extraction_method.toUpperCase()}
`,t+=`Rotation Method: ${a.rotation_method}
`,t+=`Number of Factors: ${a.n_factors}
`,t+=`Number of Variables: ${((m=a.rotated_loadings)==null?void 0:m.length)||0}
`,t+=`Number of Observations: ${a.n_observations}
`,t+=`KMO Measure: ${a.kmo.toFixed(3)}
`,t+=`Bartlett's Test: χ² = ${a.bartlett_chi_square.toFixed(2)}, p = ${a.bartlett_p_value.toFixed(4)}

`),j.eigenvalues&&(t+=`EIGENVALUES AND VARIANCE EXPLAINED
`,t+=`-----------------------------------
`,t+=`Factor	Eigenvalue	Variance %	Cumulative %
`,a.eigenvalues.forEach((d,o)=>{t+=`${o+1}	${d.toFixed(3)}	`,t+=`${a.variance_explained[o].toFixed(1)}	`,t+=`${a.cumulative_variance[o].toFixed(1)}
`}),t+=`
`),j.loadings&&a.rotated_loadings){t+=`ROTATED FACTOR LOADINGS (${a.rotation_method})
`,t+=`----------------------------------------
`,t+="Variable";for(let o=0;o<a.n_factors;o++)t+=`	Factor ${o+1}`;t+=`
`,Object.keys(a.communalities).forEach((o,b)=>{t+=o;for(let f=0;f<a.n_factors;f++){const re=a.rotated_loadings[b][f];t+=`	${re.toFixed(3)}`}t+=`
`}),t+=`
`}if(j.communalities&&(t+=`COMMUNALITIES
`,t+=`-------------
`,Object.entries(a.communalities).forEach(([d,o])=>{t+=`${d}: ${o.toFixed(3)}
`}),t+=`
`),j.correlationMatrix&&a.correlation_matrix){t+=`CORRELATION MATRIX
`,t+=`------------------
`;const d=Object.keys(a.communalities);t+="	"+d.join("	")+`
`,a.correlation_matrix.forEach((o,b)=>{t+=d[b]+"	",t+=o.map(f=>f.toFixed(3)).join("	")+`
`}),t+=`
`}if(j.factorCorrelations&&a.factor_correlations){t+=`FACTOR CORRELATIONS
`,t+=`-------------------
`;for(let d=0;d<a.n_factors;d++)t+=`	Factor ${d+1}`;t+=`
`,a.factor_correlations.forEach((d,o)=>{t+=`Factor ${o+1}`,d.forEach(b=>{t+=`	${b.toFixed(3)}`}),t+=`
`})}const r=new Blob([t],{type:"text/plain"}),s=URL.createObjectURL(r),c=document.createElement("a");c.href=s,c.download="factor_analysis_results.txt",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(s),G(!1)},Xe=()=>{var r;if(!a)return"";let t="";if(t+=`Kaiser-Meyer-Olkin (KMO) Measure: ${a.kmo.toFixed(3)}
`,a.kmo>=.9?t+=`The KMO value is marvelous, indicating the data is highly suitable for factor analysis.
`:a.kmo>=.8?t+=`The KMO value is meritorious, indicating the data is well-suited for factor analysis.
`:a.kmo>=.7?t+=`The KMO value is middling, indicating the data is adequately suited for factor analysis.
`:a.kmo>=.6?t+=`The KMO value is mediocre, indicating the data is marginally suitable for factor analysis.
`:t+=`The KMO value is miserable, suggesting the data may not be suitable for factor analysis.
`,t+=`
Bartlett's Test of Sphericity: χ² = ${a.bartlett_chi_square.toFixed(2)}, p = ${a.bartlett_p_value.toFixed(4)}
`,a.bartlett_p_value<.001?t+=`The test is highly significant (p < 0.001), confirming that correlations between variables are sufficient for factor analysis.
`:a.bartlett_p_value<.05?t+=`The test is significant (p < 0.05), indicating that correlations between variables are sufficient for factor analysis.
`:t+=`The test is not significant, suggesting the correlation matrix may be an identity matrix (variables are uncorrelated).
`,t+=`
Extraction Method: ${a.extraction_method==="pca"?"Principal Component Analysis":"Maximum Likelihood"}
`,t+=`Number of Factors Extracted: ${a.n_factors}
`,t+=`Total Variance Explained: ${(r=a.cumulative_variance[a.n_factors-1])==null?void 0:r.toFixed(1)}%
`,a.cumulative_variance[a.n_factors-1]>=60?t+=`The extracted factors explain a substantial portion of the total variance.
`:t+=`The extracted factors explain a moderate portion of the total variance. Consider extracting additional factors.
`,a.rotation_method!=="none"&&(t+=`
Rotation Method: ${a.rotation_method}
`,a.rotation_method==="varimax"?t+=`Varimax rotation was used to achieve simple structure while maintaining factor independence.
`:a.rotation_method==="promax"?t+=`Promax rotation was used, allowing factors to correlate, which may be more realistic for your data.
`:a.rotation_method==="oblimin"&&(t+=`Oblimin rotation was used, allowing oblique (correlated) factors.
`)),a.communalities){const s=Object.values(a.communalities),c=s.reduce((d,o)=>d+o,0)/s.length,m=Object.entries(a.communalities).filter(([d,o])=>o<.3).map(([d,o])=>d);t+=`
Average Communality: ${c.toFixed(3)}
`,m.length>0&&(t+=`Variables with low communalities (< 0.3): ${m.join(", ")}
`,t+=`These variables may not be well-represented by the extracted factors.
`)}return t},Ge=()=>a?{data:[{x:a.eigenvalues.map((t,r)=>r+1),y:a.eigenvalues,type:"scatter",mode:"lines+markers",name:"Eigenvalues",line:{color:O.palette.primary.main},marker:{size:8}},{x:[0,a.eigenvalues.length+1],y:[1,1],type:"scatter",mode:"lines",name:"Kaiser Criterion (λ = 1)",line:{color:O.palette.error.main,dash:"dash"}}],layout:{title:"Scree Plot",xaxis:{title:"Factor Number",dtick:1},yaxis:{title:"Eigenvalue"},hovermode:"closest",showlegend:!0,height:400}}:null;return e.jsxs(u,{p:3,children:[e.jsx(i,{variant:"h5",gutterBottom:!0,children:"Exploratory Factor Analysis"}),X&&e.jsx(oe,{severity:"info",sx:{mb:2},children:e.jsxs(u,{children:[e.jsx(i,{variant:"body2",gutterBottom:!0,children:"Initializing Python environment for statistical analysis..."}),e.jsx(Qe,{sx:{mt:1}})]})}),!I&&!X&&e.jsx(oe,{severity:"warning",sx:{mb:2},action:e.jsx(q,{color:"inherit",size:"small",onClick:te,startIcon:e.jsx(Ze,{}),children:"Retry"}),children:"Python environment not ready. Factor analysis requires Python libraries to be loaded."}),e.jsxs(C,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Select Variables"}),e.jsxs(g,{container:!0,spacing:2,children:[e.jsx(g,{item:!0,xs:12,md:4,children:e.jsxs(B,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"dataset-select-label",children:"Dataset"}),e.jsx(Y,{labelId:"dataset-select-label",id:"dataset-select",value:k,label:"Dataset",onChange:Ie,disabled:y.length===0,children:y.length===0?e.jsx(w,{value:"",disabled:!0,children:"No datasets available"}):y.map(t=>e.jsxs(w,{value:t.id,children:[t.name," (",t.data.length," rows)"]},t.id))})]})}),e.jsx(g,{item:!0,xs:12,md:8,children:e.jsxs(B,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"variables-label",children:"Variables (Select at least 3)"}),e.jsx(Y,{labelId:"variables-label",id:"variables",multiple:!0,value:S,label:"Variables (Select at least 3)",onChange:Le,disabled:!l,renderValue:t=>{const r=t.map(s=>{const c=ae.find(m=>m.id===s);return c?c.name:""}).filter(Boolean);return e.jsx(u,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:r.map(s=>e.jsx(et,{label:s,size:"small"},s))})},children:ae.length===0?e.jsx(w,{value:"",disabled:!0,children:"No numeric variables available"}):ae.map(t=>e.jsx(w,{value:t.id,children:t.name},t.id))}),e.jsx(i,{variant:"caption",color:"text.secondary",children:"Factor analysis requires numeric variables. Select the variables you want to analyze for underlying factors."})]})})]}),e.jsx(ne,{sx:{my:2}}),e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Analysis Configuration"}),e.jsxs(g,{container:!0,spacing:2,children:[e.jsx(g,{item:!0,xs:12,md:3,children:e.jsxs(B,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"extraction-method-label",children:"Extraction Method"}),e.jsxs(Y,{labelId:"extraction-method-label",id:"extraction-method",value:ue,label:"Extraction Method",onChange:ze,children:[e.jsx(w,{value:"pca",children:"Principal Component Analysis"}),e.jsx(w,{value:"ml",children:"Maximum Likelihood"})]})]})}),e.jsx(g,{item:!0,xs:12,md:3,children:e.jsxs(B,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"rotation-method-label",children:"Rotation Method"}),e.jsxs(Y,{labelId:"rotation-method-label",id:"rotation-method",value:Q,label:"Rotation Method",onChange:Ne,children:[e.jsx(w,{value:"none",children:"None"}),e.jsx(w,{value:"varimax",children:"Varimax (Orthogonal)"}),e.jsx(w,{value:"promax",children:"Promax (Oblique)"}),e.jsx(w,{value:"oblimin",children:"Oblimin (Oblique)"})]})]})}),e.jsxs(g,{item:!0,xs:12,md:6,children:[e.jsxs(B,{component:"fieldset",margin:"normal",children:[e.jsx(je,{component:"legend",children:"Factor Selection Method"}),e.jsxs(tt,{row:!0,value:U,onChange:Be,children:[e.jsx(v,{value:"kaiser",control:e.jsx(se,{}),label:"Kaiser Criterion (λ > 1)"}),e.jsx(v,{value:"scree",control:e.jsx(se,{}),label:"Scree Test"}),e.jsx(v,{value:"manual",control:e.jsx(se,{}),label:"Manual"})]})]}),U==="manual"&&e.jsx(ye,{label:"Number of Factors",type:"number",value:Z,onChange:$e,fullWidth:!0,margin:"normal",inputProps:{min:1,max:S.length},helperText:`Enter a value between 1 and ${S.length||"N"}`})]})]}),e.jsx(ne,{sx:{my:2}}),e.jsx(i,{variant:"subtitle1",gutterBottom:!0,children:"Display Options"}),e.jsxs(g,{container:!0,spacing:2,children:[e.jsx(g,{item:!0,xs:12,md:6,children:e.jsxs(le,{children:[e.jsx(v,{control:e.jsx(F,{checked:_.showScreePlot,onChange:V("showScreePlot")}),label:"Show Scree Plot"}),e.jsx(v,{control:e.jsx(F,{checked:_.showFactorMatrix,onChange:V("showFactorMatrix")}),label:"Show Unrotated Factor Matrix"}),e.jsx(v,{control:e.jsx(F,{checked:_.showRotatedMatrix,onChange:V("showRotatedMatrix")}),label:"Show Rotated Factor Matrix"})]})}),e.jsxs(g,{item:!0,xs:12,md:6,children:[e.jsx(le,{children:e.jsx(v,{control:e.jsx(F,{checked:_.showCorrelationMatrix,onChange:V("showCorrelationMatrix")}),label:"Show Correlation Matrix"})}),e.jsx(ye,{label:"Minimum Loading to Display",type:"number",value:_.minimumLoading,onChange:V("minimumLoading"),fullWidth:!0,margin:"normal",inputProps:{min:0,max:.99,step:.05},helperText:"Hide loadings below this threshold"})]})]}),e.jsx(u,{mt:2,children:e.jsx(q,{variant:"contained",color:"primary",startIcon:e.jsx(at,{}),onClick:De,disabled:W||!I||S.length<3,children:W?"Running Analysis...":"Run Factor Analysis"})})]}),W&&e.jsx(u,{display:"flex",justifyContent:"center",my:4,children:e.jsx(rt,{})}),fe&&e.jsx(oe,{severity:"error",sx:{mb:3},children:fe}),a&&!W&&e.jsx(e.Fragment,{children:e.jsxs(C,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(u,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:e.jsxs(it,{value:z,onChange:Ve,"aria-label":"factor analysis tabs",children:[e.jsx(D,{label:"Summary",icon:e.jsx(ot,{}),iconPosition:"start"}),e.jsx(D,{label:"Factor Loadings",icon:e.jsx(nt,{}),iconPosition:"start"}),e.jsx(D,{label:"Variance Explained",icon:e.jsx(st,{}),iconPosition:"start"}),e.jsx(D,{label:"Additional Results",icon:e.jsx(lt,{}),iconPosition:"start"}),e.jsx(D,{label:"Interpretation",icon:e.jsx(ct,{}),iconPosition:"start"})]})}),z===0&&e.jsxs(u,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Analysis Summary"}),e.jsxs(g,{container:!0,spacing:3,children:[e.jsx(g,{item:!0,xs:12,md:6,children:e.jsx(Ce,{variant:"outlined",children:e.jsxs(we,{children:[e.jsx(i,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Data Adequacy Tests"}),e.jsxs(u,{mb:2,children:[e.jsx(i,{variant:"body2",gutterBottom:!0,children:"Kaiser-Meyer-Olkin (KMO) Measure"}),e.jsx(i,{variant:"h5",children:a.kmo.toFixed(3)}),e.jsx(i,{variant:"body2",color:a.kmo>=.8?"success.main":a.kmo>=.6?"warning.main":"error.main",children:a.kmo>=.9?"Marvelous":a.kmo>=.8?"Meritorious":a.kmo>=.7?"Middling":a.kmo>=.6?"Mediocre":"Miserable"})]}),e.jsx(ne,{sx:{my:2}}),e.jsxs(u,{children:[e.jsx(i,{variant:"body2",gutterBottom:!0,children:"Bartlett's Test of Sphericity"}),e.jsxs(i,{variant:"body1",children:["χ² = ",a.bartlett_chi_square.toFixed(2)]}),e.jsxs(i,{variant:"body2",color:a.bartlett_p_value<.05?"success.main":"error.main",children:["p = ",a.bartlett_p_value<.001?"< 0.001":a.bartlett_p_value.toFixed(4)]})]})]})})}),e.jsx(g,{item:!0,xs:12,md:6,children:e.jsx(Ce,{variant:"outlined",children:e.jsxs(we,{children:[e.jsx(i,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Model Information"}),e.jsx(P,{children:e.jsx(T,{size:"small",children:e.jsxs(A,{children:[e.jsxs(x,{children:[e.jsx(n,{children:"Extraction Method"}),e.jsx(n,{children:a.extraction_method==="pca"?"Principal Component Analysis":"Maximum Likelihood"})]}),e.jsxs(x,{children:[e.jsx(n,{children:"Rotation Method"}),e.jsx(n,{children:a.rotation_method})]}),e.jsxs(x,{children:[e.jsx(n,{children:"Number of Factors"}),e.jsx(n,{children:a.n_factors})]}),e.jsxs(x,{children:[e.jsx(n,{children:"Number of Variables"}),e.jsx(n,{children:Object.keys(a.communalities).length})]}),e.jsxs(x,{children:[e.jsx(n,{children:"Number of Observations"}),e.jsx(n,{children:a.n_observations})]}),e.jsxs(x,{children:[e.jsx(n,{children:"Total Variance Explained"}),e.jsxs(n,{children:[(pe=a.cumulative_variance[a.n_factors-1])==null?void 0:pe.toFixed(1),"%"]})]})]})})})]})})})]}),e.jsxs(u,{mt:3,children:[e.jsx(q,{variant:"outlined",startIcon:e.jsx(dt,{}),onClick:Ke,sx:{mr:2},children:"Calculate Factor Scores"}),e.jsx(q,{variant:"outlined",startIcon:e.jsx(ce,{}),onClick:()=>G(!0),children:"Export Results"})]})]}),z===1&&e.jsxs(u,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Factor Loadings"}),_.showRotatedMatrix&&a.rotated_loadings&&e.jsxs(u,{mb:3,children:[e.jsxs(i,{variant:"subtitle2",gutterBottom:!0,children:["Rotated Factor Matrix (",a.rotation_method,")"]}),e.jsx(P,{component:C,variant:"outlined",children:e.jsxs(T,{size:"small",children:[e.jsx($,{children:e.jsxs(x,{children:[e.jsx(n,{children:"Variable"}),Array.from({length:a.n_factors},(t,r)=>e.jsxs(n,{align:"right",children:["Factor ",r+1]},r)),_.showCommunalities&&e.jsx(n,{align:"right",children:"Communality"})]})}),e.jsx(A,{children:Object.keys(a.communalities).map((t,r)=>e.jsxs(x,{children:[e.jsx(n,{children:t}),Array.from({length:a.n_factors},(s,c)=>{const m=a.loadings[r][c],d=Math.abs(m),o=d>=_.minimumLoading,b=d>=.7,f=d>=.4;return e.jsx(n,{align:"right",sx:{fontWeight:b?"bold":f?"medium":"normal",color:o?b?"primary.main":f?"text.primary":"text.secondary":"text.disabled"},children:o?m.toFixed(3):"-"},c)}),_.showCommunalities&&e.jsx(n,{align:"right",children:a.communalities[t].toFixed(3)})]},t))})]})}),e.jsx(u,{mt:1,children:e.jsxs(i,{variant:"caption",color:"text.secondary",children:["Loadings below ",_.minimumLoading," are suppressed. Strong loadings (≥ 0.7) are highlighted."]})})]}),_.showFactorMatrix&&a.loadings&&e.jsxs(u,{children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,children:"Unrotated Factor Matrix"}),e.jsx(P,{component:C,variant:"outlined",children:e.jsxs(T,{size:"small",children:[e.jsx($,{children:e.jsxs(x,{children:[e.jsx(n,{children:"Variable"}),Array.from({length:a.n_factors},(t,r)=>e.jsxs(n,{align:"right",children:["Factor ",r+1]},r))]})}),e.jsx(A,{children:Object.keys(a.communalities).map((t,r)=>e.jsxs(x,{children:[e.jsx(n,{children:t}),Array.from({length:a.n_factors},(s,c)=>{const m=a.loadings[r][c],o=Math.abs(m)>=_.minimumLoading;return e.jsx(n,{align:"right",sx:{color:o?"text.primary":"text.disabled"},children:o?m.toFixed(3):"-"},c)})]},t))})]})})]}),a.factor_correlations&&Q!=="varimax"&&e.jsxs(u,{mt:3,children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,children:"Factor Correlation Matrix"}),e.jsx(P,{component:C,variant:"outlined",sx:{maxWidth:600},children:e.jsxs(T,{size:"small",children:[e.jsx($,{children:e.jsxs(x,{children:[e.jsx(n,{children:"Factor"}),Array.from({length:a.n_factors},(t,r)=>e.jsx(n,{align:"right",children:r+1},r))]})}),e.jsx(A,{children:a.factor_correlations.map((t,r)=>e.jsxs(x,{children:[e.jsx(n,{children:r+1}),t.map((s,c)=>e.jsx(n,{align:"right",sx:{fontWeight:r===c?"bold":"normal",color:Math.abs(s)>.3&&r!==c?"warning.main":"text.primary"},children:s.toFixed(3)},c))]},r))})]})}),e.jsx(u,{mt:1,children:e.jsx(i,{variant:"caption",color:"text.secondary",children:"Correlations above 0.3 are highlighted as they may indicate factor overlap."})})]})]}),z===2&&e.jsxs(u,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Variance Explained"}),e.jsxs(g,{container:!0,spacing:3,children:[e.jsx(g,{item:!0,xs:12,md:6,children:e.jsx(P,{component:C,variant:"outlined",children:e.jsxs(T,{size:"small",children:[e.jsx($,{children:e.jsxs(x,{children:[e.jsx(n,{children:"Factor"}),e.jsx(n,{align:"right",children:"Eigenvalue"}),e.jsx(n,{align:"right",children:"Variance %"}),e.jsx(n,{align:"right",children:"Cumulative %"})]})}),e.jsx(A,{children:a.eigenvalues.map((t,r)=>e.jsxs(x,{sx:{backgroundColor:r<a.n_factors?"action.selected":"transparent"},children:[e.jsx(n,{children:r+1}),e.jsx(n,{align:"right",children:t.toFixed(3)}),e.jsxs(n,{align:"right",children:[a.variance_explained[r].toFixed(1),"%"]}),e.jsxs(n,{align:"right",children:[a.cumulative_variance[r].toFixed(1),"%"]})]},r))})]})})}),_.showScreePlot&&e.jsx(g,{item:!0,xs:12,md:6,children:e.jsx(ht,{...Ge(),config:{displayModeBar:!1},style:{width:"100%",height:"100%"}})})]})]}),z===3&&e.jsxs(u,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Additional Results"}),_.showCommunalities&&e.jsxs(u,{mb:3,children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,children:"Communalities"}),e.jsx(g,{container:!0,spacing:2,children:Object.entries(a.communalities).map(([t,r])=>e.jsx(g,{item:!0,xs:12,sm:6,md:4,children:e.jsxs(u,{sx:{p:2,border:1,borderColor:r<.3?"error.main":"divider",borderRadius:1,backgroundColor:r<.3?"error.lighter":"background.paper"},children:[e.jsx(i,{variant:"body2",color:"text.secondary",children:t}),e.jsx(i,{variant:"h6",color:r<.3?"error.main":"text.primary",children:r.toFixed(3)})]})},t))}),e.jsx(u,{mt:2,children:e.jsx(i,{variant:"body2",color:"text.secondary",children:"Communalities represent the proportion of variance in each variable explained by the extracted factors. Values below 0.3 (highlighted) suggest the variable may not fit well with the factor structure."})})]}),_.showCorrelationMatrix&&a.correlation_matrix&&e.jsxs(u,{children:[e.jsx(i,{variant:"subtitle2",gutterBottom:!0,children:"Correlation Matrix"}),e.jsx(P,{component:C,variant:"outlined",sx:{maxWidth:"100%",overflowX:"auto"},children:e.jsxs(T,{size:"small",children:[e.jsx($,{children:e.jsxs(x,{children:[e.jsx(n,{children:"Variable"}),Object.keys(a.communalities).map(t=>e.jsx(n,{align:"right",children:t},t))]})}),e.jsx(A,{children:Object.keys(a.communalities).map((t,r)=>e.jsxs(x,{children:[e.jsx(n,{children:t}),a.correlation_matrix[r].map((s,c)=>e.jsx(n,{align:"right",sx:{backgroundColor:r===c?"action.hover":Math.abs(s)>.7?"warning.lighter":Math.abs(s)>.5?"info.lighter":"transparent",fontWeight:r===c?"bold":"normal"},children:s.toFixed(3)},c))]},t))})]})}),e.jsx(u,{mt:1,children:e.jsx(i,{variant:"caption",color:"text.secondary",children:"Strong correlations (|r| > 0.7) are highlighted in orange, moderate correlations (|r| > 0.5) in blue."})})]})]}),z===4&&e.jsxs(u,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Interpretation Guide"}),e.jsx(C,{elevation:0,variant:"outlined",sx:{p:2,mb:3,bgcolor:"background.paper"},children:e.jsx(i,{variant:"body2",sx:{whiteSpace:"pre-line"},children:Xe()})}),e.jsx(i,{variant:"subtitle2",gutterBottom:!0,sx:{mt:3},children:"Understanding Factor Analysis"}),e.jsxs(C,{elevation:0,variant:"outlined",sx:{p:2,bgcolor:"background.paper"},children:[e.jsx(i,{variant:"body2",fontWeight:"bold",children:"KMO and Bartlett's Test"}),e.jsx(i,{variant:"body2",children:"KMO values > 0.8 are considered good, > 0.6 acceptable. Bartlett's test should be significant (p < 0.05) to proceed with factor analysis."}),e.jsx(i,{variant:"body2",fontWeight:"bold",sx:{mt:2},children:"Factor Extraction"}),e.jsx(i,{variant:"body2",children:'Factors are extracted based on eigenvalues. Common criteria include eigenvalues &gt 1 (Kaiser criterion) or examining the scree plot for the "elbow" point.'}),e.jsx(i,{variant:"body2",fontWeight:"bold",sx:{mt:2},children:"Factor Loadings"}),e.jsx(i,{variant:"body2",children:"Loadings represent correlations between variables and factors. Values &gt 0.4 are typically considered meaningful, &gt 0.7 are strong."}),e.jsx(i,{variant:"body2",fontWeight:"bold",sx:{mt:2},children:"Communalities"}),e.jsx(i,{variant:"body2",children:"Communalities show the proportion of variance in each variable explained by the factors. Values &lt 0.3 suggest the variable may not fit well."}),e.jsx(i,{variant:"body2",fontWeight:"bold",sx:{mt:2},children:"Rotation Methods"}),e.jsx(i,{variant:"body2",paragraph:!0,children:"• Varimax: Orthogonal rotation that maximizes variance of squared loadings (factors remain uncorrelated)"}),e.jsx(i,{variant:"body2",paragraph:!0,children:"• Promax: Oblique rotation allowing factors to correlate (more realistic for psychological constructs)"}),e.jsx(i,{variant:"body2",children:"• Oblimin: Another oblique rotation method with adjustable delta parameter"})]})]})]})}),e.jsxs(Fe,{open:Te,onClose:()=>ee(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(ke,{children:"Factor Scores"}),e.jsx(Se,{children:E&&e.jsxs(e.Fragment,{children:[e.jsx(i,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Factor scores represent each observation's position on the extracted factors."}),e.jsx(P,{component:C,variant:"outlined",sx:{mt:2,maxHeight:400},children:e.jsxs(T,{stickyHeader:!0,size:"small",children:[e.jsx($,{children:e.jsxs(x,{children:[e.jsx(n,{children:"Observation"}),Array.from({length:((be=E.scores[0])==null?void 0:be.length)||0},(t,r)=>e.jsxs(n,{align:"right",children:["Factor ",r+1]},r))]})}),e.jsxs(A,{children:[E.scores.slice(0,100).map((t,r)=>e.jsxs(x,{children:[e.jsx(n,{children:r+1}),t.map((s,c)=>e.jsx(n,{align:"right",children:s.toFixed(3)},c))]},r)),E.scores.length>100&&e.jsx(x,{children:e.jsxs(n,{colSpan:((ve=E.scores[0])==null?void 0:ve.length)+1,align:"center",children:["... and ",E.scores.length-100," more rows"]})})]})]})}),e.jsx(u,{mt:2,children:e.jsx(q,{variant:"outlined",onClick:Ue,startIcon:e.jsx(ce,{}),children:"Export Factor Scores as CSV"})})]})}),e.jsx(Me,{children:e.jsx(q,{onClick:()=>ee(!1),children:"Close"})})]}),e.jsxs(Fe,{open:Ae,onClose:()=>G(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(ke,{children:"Export Results"}),e.jsxs(Se,{children:[e.jsxs(B,{component:"fieldset",children:[e.jsx(je,{component:"legend",children:"Select items to export:"}),e.jsxs(le,{children:[e.jsx(v,{control:e.jsx(F,{checked:j.summary,onChange:t=>N(r=>({...r,summary:t.target.checked}))}),label:"Summary Statistics"}),e.jsx(v,{control:e.jsx(F,{checked:j.loadings,onChange:t=>N(r=>({...r,loadings:t.target.checked}))}),label:"Factor Loadings"}),e.jsx(v,{control:e.jsx(F,{checked:j.communalities,onChange:t=>N(r=>({...r,communalities:t.target.checked}))}),label:"Communalities"}),e.jsx(v,{control:e.jsx(F,{checked:j.eigenvalues,onChange:t=>N(r=>({...r,eigenvalues:t.target.checked}))}),label:"Eigenvalues"}),e.jsx(v,{control:e.jsx(F,{checked:j.correlationMatrix,onChange:t=>N(r=>({...r,correlationMatrix:t.target.checked}))}),label:"Show Correlation Matrix"}),e.jsx(v,{control:e.jsx(F,{checked:j.factorCorrelations,onChange:t=>N(r=>({...r,factorCorrelations:t.target.checked})),disabled:(a==null?void 0:a.rotation_method)==="varimax"}),label:"Factor Correlations (Oblique only)"})]})]}),e.jsx(u,{mt:2,children:e.jsx(i,{variant:"body2",color:"text.secondary",children:"Results will be exported as a formatted text file."})})]}),e.jsxs(Me,{children:[e.jsx(q,{onClick:()=>G(!1),children:"Cancel"}),e.jsx(q,{onClick:We,variant:"contained",startIcon:e.jsx(ce,{}),children:"Export"})]})]})]})};export{wt as default};
