import{u as d,j as g}from"./mui-libs-CfwFIaTD.js";import{r as c}from"./react-libs-Cr2nE3UY.js";import{j as m}from"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";const M=({columnId:u,boxPlotData:e,columnName:i,includeOutliers:n})=>{const s=d(),t=c.useRef(null);return c.useEffect(()=>{if(t.current){const p={x:[e.min,e.q1,e.median,e.q3,e.max,...e.outliers],type:"box",name:i,boxpoints:n?"outliers":!1,jitter:.3,pointpos:-1.8,marker:{color:s.palette.primary.main},orientation:"h",boxmean:!1,line:{width:2},fillcolor:"rgba(30, 144, 255, 0.5)"},r={margin:{t:10,r:50,b:20,l:50},height:180,width:t.current.clientWidth,hovermode:"closest",showlegend:!1,xaxis:{zeroline:!1,automargin:!0,fixedrange:!0},yaxis:{showticklabels:!1,fixedrange:!0},autosize:!0,plot_bgcolor:"transparent",boxmode:"group"},f={displayModeBar:!1,responsive:!0},a=t.current;if(a){const o=a.clientWidth;r.width=o;const h=Math.min(600,o*.9),l=Math.max(50,(o-h)/2);r.margin.l=l,r.margin.r=l}m.newPlot(t.current,[p],r,f)}return()=>{t.current&&m.purge(t.current)}},[e,i,n,s]),g.jsx("div",{id:`boxplot-${u}`,ref:t,style:{width:"100%",height:"100%"}})};export{M as default};
