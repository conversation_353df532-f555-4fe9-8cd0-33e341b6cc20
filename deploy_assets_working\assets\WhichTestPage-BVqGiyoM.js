import{j as e,B as c,G as d,R as x,e as l,L as o,a1 as u,r as i,D as y,m as t,am as g,an as b,ao as f,ap as v,aq as T,ar as n,as as s,at as A,au as C,av as I,aw as P}from"./mui-libs-CfwFIaTD.js";import{r as N,b as S}from"./react-libs-Cr2nE3UY.js";import{H as R}from"./index-Bpan7Tbe.js";import{P as V}from"./PageTitle-DA3BXQ4x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const h=[{id:"clarify-objective",title:"1. Clarify Research Objective",icon:e.jsx(g,{}),content:e.jsxs(o,{dense:!0,children:[e.jsx(t,{children:e.jsx(i,{primary:"Compare groups: Are you testing differences between groups (e.g., means, medians, or proportions)?"})}),e.jsx(t,{children:e.jsx(i,{primary:"Analyze relationships: Are you examining correlations or associations between variables?"})}),e.jsx(t,{children:e.jsx(i,{primary:"Predict outcomes: Are you modeling or forecasting trends?"})})]})},{id:"identify-data-types",title:"2. Identify Your Data Types",icon:e.jsx(b,{}),content:e.jsxs(e.Fragment,{children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"A. Variable Types"}),e.jsxs(o,{dense:!0,children:[e.jsx(t,{children:e.jsx(i,{primary:"Quantitative (Numerical): Continuous data (e.g., height, blood pressure).",secondary:"Tests: t-test, ANOVA, Pearson correlation, linear regression."})}),e.jsx(t,{children:e.jsx(i,{primary:"Categorical:",secondary:e.jsxs(e.Fragment,{children:["Nominal: Unordered categories (e.g., gender, race).",e.jsx("br",{}),"Ordinal: Ordered categories (e.g., survey ratings).",e.jsx("br",{}),"Tests: Chi-square, Fisher’s exact test, logistic regression."]})})})]}),e.jsx(l,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"B. Data Distribution"}),e.jsxs(o,{dense:!0,children:[e.jsx(t,{children:e.jsx(i,{primary:"Normal distribution: Use parametric tests (e.g., t-test, ANOVA)."})}),e.jsx(t,{children:e.jsx(i,{primary:"Non-normal/small sample: Use non-parametric tests (e.g., Mann-Whitney U, Kruskal-Wallis)."})})]})]})},{id:"select-tests",title:"3. Select Tests for Scenarios",icon:e.jsx(C,{}),content:e.jsx(f,{component:x,sx:{my:0},children:e.jsxs(v,{size:"small","aria-label":"select tests for common scenarios table",children:[e.jsx(T,{children:e.jsxs(n,{children:[e.jsx(s,{children:"Goal"}),e.jsx(s,{children:"Data Type"}),e.jsx(s,{children:"Groups"}),e.jsx(s,{children:"Recommended Test"})]})}),e.jsxs(A,{children:[e.jsxs(n,{children:[e.jsx(s,{children:"Compare means"}),e.jsx(s,{children:"Quantitative, normal"}),e.jsx(s,{children:"2"}),e.jsx(s,{children:"Independent t-test"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Compare means"}),e.jsx(s,{children:"Quantitative, non-normal"}),e.jsx(s,{children:"2"}),e.jsx(s,{children:"Mann-Whitney U test"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Compare means"}),e.jsx(s,{children:"Quantitative, normal"}),e.jsx(s,{children:"≥"}),e.jsx(s,{children:"ANOVA"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Compare medians"}),e.jsx(s,{children:"Quantitative, non-normal"}),e.jsx(s,{children:"≥"}),e.jsx(s,{children:"Kruskal-Wallis test"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Compare proportions"}),e.jsx(s,{children:"Categorical"}),e.jsx(s,{children:"2"}),e.jsx(s,{children:"Chi-square or Fisher’s exact test"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Measure correlation"}),e.jsx(s,{children:"Quantitative"}),e.jsx(s,{children:"2 variables"}),e.jsx(s,{children:"Pearson (linear) or Spearman (non-linear)"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Predict a continuous outcome"}),e.jsx(s,{children:"Mixed"}),e.jsx(s,{children:"Any"}),e.jsx(s,{children:"Linear regression"})]}),e.jsxs(n,{children:[e.jsx(s,{children:"Predict a binary outcome"}),e.jsx(s,{children:"Mixed"}),e.jsx(s,{children:"Any"}),e.jsx(s,{children:"Logistic regression"})]})]})]})})},{id:"adjust-study-design",title:"4. Adjust for Study Design",icon:e.jsx(I,{}),content:e.jsxs(o,{dense:!0,children:[e.jsx(t,{children:e.jsx(i,{primary:"Paired data (e.g., pre/post measurements):",secondary:e.jsxs(e.Fragment,{children:["Normal: Paired t-test.",e.jsx("br",{}),"Non-normal: Wilcoxon signed-rank test."]})})}),e.jsx(t,{children:e.jsx(i,{primary:"Repeated measures: Repeated-measures ANOVA or Friedman test."})})]})},{id:"verify-assumptions",title:"5. Verify Test Assumptions",icon:e.jsx(P,{}),content:e.jsxs(o,{dense:!0,children:[e.jsx(t,{children:e.jsx(i,{primary:"Parametric tests: Check normality, equal variance, and independence."})}),e.jsx(t,{children:e.jsx(i,{primary:"Non-parametric tests: Fewer assumptions but less statistical power."})})]})}],q=({onNavigate:B})=>{const m=N.useRef([]),j=r=>{var a;(a=m.current[r])==null||a.scrollIntoView({behavior:"smooth",block:"start"})};return e.jsxs(e.Fragment,{children:[e.jsxs(R,{children:[e.jsx("title",{children:"Which Test Should I Use? - DataStatPro Guide"}),e.jsx("meta",{name:"description",content:"Guide to selecting the appropriate statistical test based on your research question, data types, data distribution, and study design. Covers t-tests, ANOVA, chi-square, and more."})]}),e.jsxs(c,{sx:{p:3},children:[e.jsx(V,{title:"Which Test Should I Use?"}),e.jsxs(d,{container:!0,spacing:3,sx:{mt:2},children:[e.jsx(d,{item:!0,xs:12,md:3,children:e.jsxs(x,{sx:{p:2,position:"sticky",top:"80px"},children:[e.jsx(l,{variant:"h6",gutterBottom:!0,children:"Guide Sections"}),e.jsx(o,{component:"nav",dense:!0,children:h.map((r,a)=>e.jsxs(u,{onClick:()=>j(a),children:[r.icon&&e.jsx(c,{sx:{mr:1.5,display:"flex",alignItems:"center",color:"primary.main"},children:S.cloneElement(r.icon,{fontSize:"small"})}),e.jsx(i,{primary:r.title.replace(/^\d+\.\s*/,""),primaryTypographyProps:{variant:"body2"}})]},r.id))})]})}),e.jsx(d,{item:!0,xs:12,md:9,children:h.map((r,a)=>e.jsxs(x,{sx:{p:3,mb:3},ref:p=>m.current[a]=p,id:r.id,children:[e.jsxs(l,{variant:"h5",gutterBottom:!0,component:"div",sx:{display:"flex",alignItems:"center"},children:[r.icon&&e.jsx(c,{sx:{mr:1,display:"flex",alignItems:"center",color:"primary.main"},children:r.icon}),r.title]}),e.jsx(y,{sx:{mb:2}}),r.content]},r.id))})]})]})]})};export{q as default};
