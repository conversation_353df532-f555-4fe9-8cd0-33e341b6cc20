import{j as e,f as y,ae as k,g as I,u as T,a as z,B as o,A as R,T as D,cj as F,e as i,b as W,C as j,I as B,H as M,F as v,G as S,k as L,i as f,h as P,l as V,D as U,L as q,m as O,n as G,o as H,q as J,r as N,s as Y,y as K,z as Q,E as X,J as Z}from"./mui-libs-CfwFIaTD.js";import{r as d}from"./react-libs-Cr2nE3UY.js";import{b as _,u as $,k as ee}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const C={enableStripeDev:!1,stripe:{priceIds:{proMonthly:"",proAnnual:"",eduMonthly:"",eduAnnual:""}}},A=()=>C.enableStripeDev,te=(r,n)=>{const a=`${r}${n.charAt(0).toUpperCase()+n.slice(1)}`;return C.stripe.priceIds[a]},ne=r=>[/\.edu$/i,/\.ac\.edu$/i,/\.edu\.au$/i,/\.ac\.uk$/i,/\.edu\.[a-z]{2,}$/i,/\.ac\.[a-z]{2,}$/i,/\.university\.[a-z]{2,}$/i,/\.college\.[a-z]{2,}$/i].some(a=>a.test(r)),ie=({priceId:r,planName:n,billingCycle:a,requiresEducationalEmail:u=!1,disabled:b=!1,children:c})=>{const{user:m}=_(),g=$(),[h,t]=d.useState(!1),[l,s]=d.useState(null),p=async()=>{if(!m){sessionStorage.setItem("subscriptionIntent",JSON.stringify({priceId:r,planName:n,billingCycle:a,requiresEducationalEmail:u,timestamp:Date.now()})),g("/app#/auth/login?returnTo=subscription&plan="+n.toLowerCase().replace(" ","-"));return}if(u&&!ne(m.email||"")){s("Educational email address (.edu domain) is required for this plan");return}t(!0),s(null);try{const x=`Subscription Request - ${n} (${a})`,E=`Hi DataStatPro Team,

I would like to subscribe to the ${n} plan with ${a} billing.

Account Details:
- Email: ${m.email}
- Plan: ${n}
- Billing: ${a}
- Price ID: ${r}

${u?`Educational Email: ${m.email} (verified)`:""}

Please process my subscription and provide payment instructions.

Thank you!`,w=`mailto:<EMAIL>?subject=${encodeURIComponent(x)}&body=${encodeURIComponent(E)}`;window.open(w)}catch(x){s(x.message||"Failed to start checkout process")}finally{t(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(y,{variant:"contained",color:"primary",fullWidth:!0,onClick:p,disabled:b||h,sx:{mt:2},children:h?e.jsx(k,{size:24,color:"inherit"}):c||`Subscribe to ${n}`}),l&&e.jsx(I,{severity:"error",sx:{mt:2},children:l})]})},pe=()=>{const r=T();z(r.breakpoints.down("md"));const n=$(),a=ee(),[u,b]=d.useState(!1),[c,m]=d.useState("monthly");d.useEffect(()=>{const t=setTimeout(()=>b(!0),300);return()=>clearTimeout(t)},[]),d.useEffect(()=>{n("/pricing")},[n]),d.useEffect(()=>{if(new URLSearchParams(a.search).get("returnTo")==="subscription"){const l=sessionStorage.getItem("subscriptionIntent");if(l)try{const s=JSON.parse(l);Date.now()-s.timestamp<10*60*1e3&&(sessionStorage.removeItem("subscriptionIntent"),console.log("Resuming subscription for:",s.planName))}catch(s){console.error("Error parsing subscription intent:",s),sessionStorage.removeItem("subscriptionIntent")}}},[a]);const g=[{id:"guest",name:"Guest Access",price:"Free",period:"Forever",description:"Perfect for exploring and learning statistical analysis",icon:e.jsx(K,{sx:{fontSize:32}}),color:"#4caf50",buttonText:"Start Exploring",buttonVariant:"outlined",stripeEnabled:!1,features:[{name:"Full app exploration",included:!0,description:"Access all features and interface"},{name:"Pro features preview",included:!0,description:"See advanced analysis capabilities"},{name:"Built-in sample datasets",included:!0,description:"Practice with curated datasets"},{name:"Teaching & demonstration",included:!0,description:"Perfect for educational use"},{name:"Personal data import",included:!1},{name:"Cloud synchronization",included:!1},{name:"Multi-device access",included:!1}]},{id:"standard",name:"Standard Account",price:"Free",period:"Currently",description:"Full local analysis capabilities with personal data",icon:e.jsx(Q,{sx:{fontSize:32}}),color:"#2196f3",buttonText:"Create Account",buttonVariant:"outlined",badge:"Most Popular",stripeEnabled:!1,features:[{name:"All Guest Access features",included:!0},{name:"Personal data import",included:!0,description:"Upload CSV, Excel, and other formats"},{name:"Local data storage",included:!0,description:"Data saved in your browser"},{name:"Full analysis suite",included:!0,description:"Complete statistical toolkit"},{name:"Export capabilities",included:!0,description:"Save results and visualizations"},{name:"Pro analysis features",included:!1,description:"Advanced statistical methods"},{name:"Cloud synchronization",included:!1},{name:"Multi-device access",included:!1}]},{id:"pro",name:"Pro Account",price:c==="monthly"?"$10":"$96",period:c==="monthly"?"per month":"per year",monthlyPrice:"$10",annualPrice:"$96",annualSavings:"20%",billingOptions:["monthly","annual"],description:"Professional analysis with cloud features and advanced tools",icon:e.jsx(X,{sx:{fontSize:32}}),color:"#ff9800",buttonText:"Request Early Access",buttonVariant:"contained",highlighted:!0,stripeEnabled:A(),features:[{name:"All Standard features",included:!0},{name:"Advanced Analysis",included:!0,description:"Advanced statistical methods"},{name:"Publication Ready",included:!0,description:"APA tables, methods text, figures"},{name:"Cloud data storage",included:!0,description:"Secure cloud backup"},{name:"Multi-device sync",included:!0,description:"Access from anywhere"},{name:"Priority support",included:!0,description:"Faster response times"},{name:"Collaboration tools",included:!0,description:"Share projects with team"},{name:"API access",included:!0,description:"Integrate with other tools"}]},{id:"edu",name:"Educational Account",price:"Free",period:"for .edu emails",emailRequirement:"Educational email required (.edu)",description:"Advanced Analysis included free for educational users",icon:e.jsx(Z,{sx:{fontSize:32}}),color:"#9c27b0",buttonText:"Create Educational Account",buttonVariant:"outlined",badge:"Advanced Analysis Free",stripeEnabled:!1,features:[{name:"All Standard Account features",included:!0},{name:"Advanced Analysis",included:!0,description:"FREE for .edu users"},{name:"Advanced statistical tests",included:!0,description:"ANOVA, Regression, etc."},{name:"Interactive visualizations",included:!0,description:"Professional charts"},{name:"Publication Ready",included:!1,description:"Upgrade to Pro for $10/month"},{name:"Cloud Storage",included:!1,description:"Upgrade to Pro for $10/month"},{name:"Multi-device sync",included:!1,description:"Upgrade to Pro for $10/month"}],upgradeOption:{name:"Educational Pro",price:"$10/month",description:"Same price as regular Pro - no educational discount",features:["Keep all current features","Add Publication Ready tools","Add Cloud Storage","Priority support"]}}],h=t=>{switch(t){case"guest":n("/app");break;case"standard":n("/app#/auth/login");break}};return e.jsxs(o,{sx:{bgcolor:"background.default",minHeight:"100vh"},children:[e.jsx(R,{position:"static",sx:{bgcolor:"warning.main"},children:e.jsxs(D,{children:[e.jsx(F,{sx:{mr:1}}),e.jsx(i,{variant:"h6",sx:{flexGrow:1,color:"warning.contrastText"},children:"DEVELOPMENT MODE - Stripe Integration Testing"}),e.jsx(y,{color:"inherit",startIcon:e.jsx(W,{}),onClick:()=>n("/pricing"),children:"Back to Production Pricing"})]})}),e.jsx(o,{sx:{bgcolor:"primary.main",color:"white",py:1},children:e.jsx(j,{maxWidth:"lg",children:e.jsxs(o,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(B,{color:"inherit",onClick:()=>n("/"),sx:{p:1},children:e.jsx(M,{})}),e.jsx(i,{variant:"h6",fontWeight:"bold",children:"DataStatPro"})]}),e.jsx(i,{variant:"body2",sx:{opacity:.9},children:"Development Pricing Page"})]})})}),e.jsxs(j,{maxWidth:"lg",sx:{py:8},children:[e.jsx(v,{in:u,timeout:600,children:e.jsxs(o,{textAlign:"center",mb:6,children:[e.jsx(i,{variant:"h2",component:"h1",fontWeight:"bold",sx:{mb:2,fontSize:{xs:"2.5rem",md:"3.5rem"},background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",backgroundClip:"text",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},children:"Choose Your Plan"}),e.jsx(i,{variant:"h5",color:"text.secondary",sx:{mb:4,maxWidth:"600px",mx:"auto",lineHeight:1.6},children:"Select the perfect plan for your statistical analysis needs"}),e.jsxs(I,{severity:"info",sx:{maxWidth:"800px",mx:"auto",mb:3,borderRadius:2,"& .MuiAlert-message":{fontSize:"1rem"}},children:[e.jsx(i,{variant:"body1",fontWeight:"medium",children:"🚧 Development Mode - Email Integration"}),e.jsx(i,{variant:"body2",sx:{mt:1},children:"Stripe integration is disabled. Subscription requests will use email contact."})]}),A()]})}),e.jsx(S,{container:!0,spacing:4,justifyContent:"center",children:g.map((t,l)=>{var s;return e.jsx(S,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(v,{in:u,timeout:800+l*200,children:e.jsxs(L,{sx:{height:"100%",display:"flex",flexDirection:"column",position:"relative",borderRadius:3,transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",...t.highlighted&&{border:`2px solid ${t.color}`,transform:"scale(1.05)",boxShadow:`0 12px 40px ${f(t.color,.2)}`},"&:hover":{transform:t.highlighted?"scale(1.05)":"translateY(-8px)",boxShadow:`0 16px 50px ${f(t.color,.15)}`}},children:[t.badge&&e.jsx(P,{label:t.badge,sx:{position:"absolute",top:16,right:16,bgcolor:t.color,color:"white",fontWeight:"bold",zIndex:1}}),e.jsxs(V,{sx:{flexGrow:1,p:3},children:[e.jsxs(o,{textAlign:"center",mb:3,children:[e.jsx(o,{sx:{width:64,height:64,borderRadius:"50%",bgcolor:f(t.color,.1),color:t.color,display:"flex",alignItems:"center",justifyContent:"center",mx:"auto",mb:2},children:t.icon}),e.jsx(i,{variant:"h5",fontWeight:"bold",gutterBottom:!0,children:t.name}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:t.description}),e.jsxs(o,{children:[e.jsx(i,{variant:"h3",component:"span",fontWeight:"bold",color:t.color,children:t.price}),e.jsx(i,{variant:"body2",color:"text.secondary",component:"span",children:t.period&&` ${t.period}`}),t.billingOptions&&c==="annual"&&t.annualSavings&&e.jsxs(o,{sx:{mt:1},children:[e.jsx(P,{label:`Save ${t.annualSavings}`,size:"small",color:"success",sx:{fontSize:"0.75rem"}}),e.jsxs(i,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:.5},children:["$",(parseFloat(((s=t.annualPrice)==null?void 0:s.replace("$",""))||"0")/12).toFixed(0),"/month when billed annually"]})]}),t.emailRequirement&&e.jsx(i,{variant:"caption",color:"warning.main",sx:{display:"block",mt:1,fontStyle:"italic"},children:t.emailRequirement})]})]}),e.jsx(U,{sx:{mb:2}}),e.jsx(q,{dense:!0,sx:{p:0},children:t.features.map((p,x)=>e.jsxs(O,{sx:{px:0,py:.5},children:[e.jsx(G,{sx:{minWidth:32},children:p.included?e.jsx(H,{sx:{color:"success.main",fontSize:20}}):e.jsx(J,{sx:{color:"text.disabled",fontSize:20}})}),e.jsx(N,{primary:p.name,secondary:p.description,primaryTypographyProps:{variant:"body2",color:p.included?"text.primary":"text.disabled"},secondaryTypographyProps:{variant:"caption",sx:{fontSize:"0.7rem"}}})]},x))})]}),e.jsx(Y,{sx:{p:3,pt:0},children:t.stripeEnabled&&(t.id==="pro"||t.id==="edu")?e.jsx(ie,{priceId:te(t.id,c),planName:t.name,billingCycle:c,requiresEducationalEmail:t.id==="edu",children:t.buttonText}):e.jsx(y,{variant:t.buttonVariant,color:"primary",fullWidth:!0,size:"large",onClick:()=>h(t.id),sx:{py:1.5,fontWeight:"bold",textTransform:"none",borderRadius:2,...t.buttonVariant==="contained"&&{bgcolor:t.color,"&:hover":{bgcolor:t.color,filter:"brightness(0.9)"}}},children:t.buttonText})})]})})},t.id)})})]})]})};export{pe as default};
