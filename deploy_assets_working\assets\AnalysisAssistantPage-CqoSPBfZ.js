import{u as Qe,j as e,B as b,bt as xe,e as u,h as T,k as ie,i as U,l as be,cn as pt,bv as Ee,bW as Z,bX as _,cQ as ut,aD as L,bY as Q,g as k,o as K,L as H,m as q,n as W,r as B,f as ee,aC as Ke,cR as ht,cS as gt,G as P,R as te,U as ne,D as ve,cj as yt,aX as Pe,aN as re,ao as ue,ap as he,aq as ge,ar as J,as as I,at as ye,bu as qe,aF as Oe,by as ft,bR as xt,bL as bt,bM as $e,aS as le,cT as vt,ah as wt,cU as Fe,I as jt,b_ as At,ae as Ge,bs as Ct,M as St,bf as Nt,d as It,cV as Tt,cW as zt,E as Le,N as Be,aW as Dt,W as Vt,aP as Ue,X as We,J as Rt,a2 as Ze}from"./mui-libs-CfwFIaTD.js";import{r as D,b as kt}from"./react-libs-Cr2nE3UY.js";import{m as He,D as C,n as Mt,i as Et,j as Pt,o as Ye,e as qt,p as we,V as fe,q as Ot,a as $t}from"./index-Bpan7Tbe.js";import{b as Xe,a as ce,c as Je,m as Ft,n as Gt}from"./descriptive-Djo0s6H4.js";import"./other-utils-CR9xr_gI.js";import"./math-setup-BTRs7Kau.js";import{i as et}from"./normality-CwHD6Rjl.js";import{i as Lt,t as Bt}from"./trainingDataInitializer-DhlysJr9.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./math-lib-BOZ-XUok.js";const tt=t=>{if(!t||!t.data||t.data.length===0)return{hasData:!1,totalRows:0,totalColumns:0,variableTypes:{numeric:0,categorical:0,ordinal:0,date:0,text:0,boolean:0},dataQuality:{totalMissingValues:0,missingValuesByColumn:{},columnsWithMissingData:[],missingDataPercentage:0},variableAnalysis:[],recommendations:[]};const d=t.data.length,r=t.columns.length,a={numeric:0,categorical:0,ordinal:0,date:0,text:0,boolean:0};t.columns.forEach(h=>{a[h.type]++});const i=He(t.data),l={totalMissingValues:i.total,missingValuesByColumn:i.byColumn,columnsWithMissingData:Object.keys(i.byColumn).filter(h=>i.byColumn[h]>0),missingDataPercentage:i.total/(d*r)*100},m=t.columns.map(h=>it(h,t.data)),n=Qt(t,m,l),o=na(t);return{hasData:!0,totalRows:d,totalColumns:r,variableTypes:a,dataQuality:l,variableAnalysis:m,recommendations:n,qualityAssessment:o}},Ut=t=>{const d={};t.forEach(a=>{const i=a.columnName.toLowerCase(),l=[/^([a-z]+)_\d+$/,/^([a-z]+)_scale_\d+$/,/^([a-z]+)_item_?\d+$/,/^([a-z]+)\d+$/,/^(item|question|q)\d+$/];for(const m of l){const n=i.match(m);if(n){const o=n[1];d[o]||(d[o]=[]),d[o].push(a.columnName);break}}});const r=Object.entries(d).filter(([a,i])=>i.length>=3).map(([a,i])=>({prefix:a,variables:i,count:i.length}));return{scaleGroups:r,hasScales:r.length>0}},Wt=t=>{const d=["survival","time","duration","follow","followup","follow_up","recurrence","event","status","death","mortality","censored","endpoint","outcome","relapse","progression"],r=["time","duration","follow","followup","follow_up","survival"],a=["event","status","death","mortality","censored","outcome"],i=[];let l,m;return t.forEach(n=>{const o=n.columnName.toLowerCase();d.some(x=>o.includes(x))&&(i.push(n.columnName),!l&&r.some(x=>o.includes(x))&&(l=n.columnName),!m&&a.some(x=>o.includes(x))&&(m=n.columnName))}),{survivalVariables:i,hasSurvivalData:i.length>0,timeVariable:l,eventVariable:m}},Zt=(t,d)=>{const r=t.name.toLowerCase(),i=[/\bid\b/i,/\bkey\b/i,/identifier/i,/\bnumber\b/i,/\bcode\b/i,/\bref\b/i,/\bseq\b/i,/\bindex\b/i,/\buuid\b/i,/\bguid\b/i,/\bserial\b/i,/\brecord\b/i,/\brow\b/i,/\bpatient\b.*\bid\b/i,/\bstudent\b.*\bid\b/i,/\buser\b.*\bid\b/i,/\bcustomer\b.*\bid\b/i,/\bemployee\b.*\bid\b/i,/\bsubject\b.*\bid\b/i,/\bcase\b.*\bid\b/i,/\bparticipant\b.*\bid\b/i,/^id_/i,/_id$/i,/^.*_?id$/i,/^.*_?key$/i,/^.*_?num$/i,/^.*_?no$/i].some(g=>g.test(r)),l=d.map(g=>g[t.name]).filter(g=>g!=null&&String(g).trim()!=="");if(l.length===0)return!1;const n=new Set(l.map(g=>String(g))).size/l.length*100,o=n>=95,h=at(l),x=st(l);return!!(i&&o||o&&l.length>10&&(h||x)||i&&n>=80||i&&l.length>5&&n>=70||n>=98&&l.length>20||h&&n>=90||x&&n>=85||n===100&&l.length>10)},at=t=>{if(t.length<3)return!1;const d=t.map(a=>Number(a)).filter(a=>!isNaN(a)&&Number.isInteger(a)).sort((a,i)=>a-i);if(d.length<t.length*.8)return!1;let r=0;for(let a=1;a<d.length;a++)d[a]-d[a-1]===1&&r++;return r/(d.length-1)>.7},st=t=>{if(t.length<3)return!1;const d=t.map(i=>String(i)),r=[/^[A-Z]{2,4}\d{3,}$/,/^\d{3,}-[A-Z]{2,}$/,/^[A-Z]\d{3,}$/,/^\d{8,}$/,/^[A-Z0-9]{8,}$/,/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i];return d.filter(i=>r.some(l=>l.test(i))).length/d.length>.8},_t=(t,d)=>{const r=d.map(g=>g[t.name]),a=r.filter(g=>g!=null&&String(g).trim()!==""),i=new Set(a.map(g=>String(g))).size,l=i/a.length*100,m=a.length/r.length*100,n=a.length-i,o=at(a),h=st(a),x=[];return m<100&&x.push(`${(100-m).toFixed(1)}% of IDs are missing - ensure all records have identifiers`),n>0&&x.push(`${n} duplicate IDs found - identifiers should be unique`),l<95&&x.push("Low uniqueness for identifier variable - verify this is intended to be an ID"),x.length===0&&x.push("Identifier variable appears well-formed with good completeness and uniqueness"),{uniquenessPercentage:l,hasSequentialPattern:o,hasAlphanumericPattern:h,completenessPercentage:m,duplicateCount:n,recommendations:x}},it=(t,d)=>{const r=Mt(d,t),a=d.map(g=>g[t.name]).filter(g=>!Et(g,t).isMissing),i=new Set(a).size,l=r.missingCount,m=r.missingPercentage,n=[];let o={};if(t.type===C.NUMERIC){const g=Pt(d,t);if(g.length>0){const s=Xe(g),p=ce(g),f=Je(g);if(o.mean=s,o.median=p,o.standardDeviation=f,g.length>=3){const S=et(g);o.isNormal=S.isNormal,S.isNormal?n.push("Data appears normally distributed"):n.push("Data is not normally distributed")}try{const S=Ye(d,t.name);o.hasOutliers=S.outliers.length>0,o.hasOutliers&&n.push(`${S.outliers.length} potential outliers detected`)}catch{}if(f>0){const S=f/Math.abs(s)*100;S>30?n.push("High variability in data"):S<10&&n.push("Low variability in data")}}}else if(t.type===C.CATEGORICAL||t.type===C.ORDINAL){const g=qt(d,t),s=we(g),p=Object.keys(s),f=p.length;o.categories=p,o.categoryCount=f;const S=Object.entries(s).reduce((V,O)=>s[V[0]]>s[O[0]]?V:O);o.mostFrequentCategory=S[0],f<=2?n.push("Binary variable - suitable for chi-square or t-tests"):f<=5?n.push("Few categories - good for ANOVA or chi-square tests"):f>10&&n.push("Many categories - consider grouping for analysis");const y=g.length,v=Object.values(s).map(V=>V/y);Math.max(...v)>.8&&n.push("Highly imbalanced categories")}m>20?n.push("High percentage of missing values - consider data cleaning"):m>5&&n.push("Some missing values present"),i===d.length&&l===0&&n.push("All values are unique - potential identifier variable");const h=Zt(t,d);let x;return h&&(x=_t(t,d),n.push("Identified as identifier variable")),{columnId:t.id,columnName:t.name,type:t.type,role:t.role,uniqueValues:i,missingCount:l,missingPercentage:m,isIdentifier:h,insights:n,identifierAnalysis:x,...o}},Qt=(t,d,r)=>{const a=[],i=d.filter(s=>s.type===C.NUMERIC),l=d.filter(s=>s.type===C.CATEGORICAL||s.type===C.ORDINAL),m=l.filter(s=>s.categoryCount===2);if(r.missingDataPercentage>10&&a.push({id:"data-cleaning",title:"Data Cleaning Required",description:"Address missing values before analysis",reason:`${r.missingDataPercentage.toFixed(1)}% of your data contains missing values. Consider imputation or removal strategies.`,analysisType:"data-preparation",category:"Data Management",priority:"high",variables:r.columnsWithMissingData,path:"data-management"}),a.push({id:"descriptive-stats",title:"Explore Your Data with Descriptive Statistics",description:"Get summary statistics and understand your data distribution",reason:`Start by exploring your ${t.data.length} observations across ${t.columns.length} variables.`,analysisType:"descriptive",category:"Descriptive Statistics",priority:"high",variables:d.map(s=>s.columnName),path:"stats/descriptives"}),i.length>=2&&a.push({id:"correlation-analysis",title:"Correlation Analysis",description:"Examine relationships between numeric variables",reason:`You have ${i.length} numeric variables: ${i.map(s=>s.columnName).join(", ")}. Explore their relationships.`,analysisType:"correlation",category:"Correlation Analysis",priority:"medium",variables:i.map(s=>s.columnName),path:"correlation-analysis/pearson"}),i.length>=1&&m.length>=1){const s=i[0],p=m[0];a.push({id:"independent-t-test",title:"Independent Samples T-Test",description:"Compare means between two groups",reason:`Compare ${s.columnName} between the two groups in ${p.columnName}.`,analysisType:"t-test",category:"Inferential Statistics",priority:"medium",variables:[s.columnName,p.columnName],path:"inference/ttest"})}if(i.length>=1&&l.length>=1){const s=i[0],p=l.find(f=>f.categoryCount&&f.categoryCount>2);p&&a.push({id:"one-way-anova",title:"One-Way ANOVA",description:"Compare means across multiple groups",reason:`Compare ${s.columnName} across the ${p.categoryCount} groups in ${p.columnName}.`,analysisType:"anova",category:"Inferential Statistics",priority:"medium",variables:[s.columnName,p.columnName],path:"inference/anova"})}if(l.length>=2){const s=l[0],p=l[1];a.push({id:"chi-square-test",title:"Chi-Square Test of Independence",description:"Test association between categorical variables",reason:`Examine the relationship between ${s.columnName} and ${p.columnName}.`,analysisType:"chi-square",category:"Inferential Statistics",priority:"medium",variables:[s.columnName,p.columnName],path:"stats/crosstabs"})}if(i.length>=2){const s=i.find(f=>f.role===fe.DEPENDENT)||i[0],p=i.filter(f=>f.columnId!==s.columnId);p.length>0&&a.push({id:"linear-regression",title:"Linear Regression Analysis",description:"Model relationships and make predictions",reason:`Use ${p.map(f=>f.columnName).join(", ")} to predict ${s.columnName}.`,analysisType:"regression",category:"Advanced Analysis",priority:"medium",variables:[s.columnName,...p.map(f=>f.columnName)],path:"correlation-analysis/regression"})}const n=i.filter(s=>s.isNormal===!1);n.length>0&&l.length>0&&a.push({id:"non-parametric-tests",title:"Non-Parametric Tests",description:"Alternative tests for non-normal data",reason:`Some variables (${n.map(s=>s.columnName).join(", ")}) are not normally distributed. Consider non-parametric alternatives.`,analysisType:"non-parametric",category:"Inferential Statistics",priority:"low",variables:n.map(s=>s.columnName),path:"inference/nonparametric"});const o=d.filter(s=>s.role===fe.DEPENDENT),h=d.filter(s=>s.role===fe.INDEPENDENT);if(o.length>0&&h.length>0){const s=o[0];s.type===C.NUMERIC?a.push({id:"linear-regression-role-based",title:"Linear Regression Analysis",description:"Model your dependent variable with independent predictors",reason:`${s.columnName} is marked as dependent variable. Use ${h.map(p=>p.columnName).join(", ")} as predictors.`,analysisType:"regression",category:"Advanced Analysis",priority:"high",variables:[s.columnName,...h.map(p=>p.columnName)],path:"correlation/linear"}):s.type===C.CATEGORICAL&&a.push({id:"logistic-regression-role-based",title:"Logistic Regression Analysis",description:"Model categorical outcomes with your predictors",reason:`${s.columnName} is a categorical dependent variable. Use ${h.map(p=>p.columnName).join(", ")} as predictors.`,analysisType:"regression",category:"Advanced Analysis",priority:"high",variables:[s.columnName,...h.map(p=>p.columnName)],path:"correlation/logistic"})}const x=Ut(d);x.hasScales&&(x.scaleGroups.forEach(s=>{a.push({id:`efa-${s.prefix}`,title:"Exploratory Factor Analysis (EFA)",description:`Explore underlying factors in ${s.prefix} scale`,reason:`Detected ${s.count} variables with similar naming pattern (${s.prefix}). These may represent a psychological scale or construct.`,analysisType:"factor-analysis",category:"Advanced Analysis",priority:"medium",variables:s.variables,path:"advanced-analysis/efa"}),a.push({id:`reliability-${s.prefix}`,title:"Reliability Analysis",description:`Assess internal consistency of ${s.prefix} scale`,reason:`Check Cronbach's alpha and item-total correlations for the ${s.prefix} scale items.`,analysisType:"reliability",category:"Advanced Analysis",priority:"medium",variables:s.variables,path:"advanced-analysis/reliability"})}),x.scaleGroups.length>1&&a.push({id:"cfa-multiple-scales",title:"Confirmatory Factor Analysis (CFA)",description:"Test your measurement model with multiple scales",reason:`Multiple scales detected (${x.scaleGroups.map(s=>s.prefix).join(", ")}). CFA can test the factor structure.`,analysisType:"factor-analysis",category:"Advanced Analysis",priority:"low",variables:x.scaleGroups.flatMap(s=>s.variables),path:"advanced-analysis/cfa"}));const g=Wt(d);return g.hasSurvivalData&&a.push({id:"survival-analysis",title:"Survival Analysis",description:"Analyze time-to-event data",reason:`Detected survival-related variables: ${g.survivalVariables.join(", ")}. ${g.timeVariable?`Time variable: ${g.timeVariable}. `:""}${g.eventVariable?`Event variable: ${g.eventVariable}.`:""}`,analysisType:"survival",category:"Advanced Analysis",priority:"high",variables:g.survivalVariables,path:"advanced-analysis/survival"}),a.push({id:"data-visualization",title:"Create Visualizations",description:"Visualize your data patterns and relationships",reason:"Visual exploration helps identify patterns, outliers, and relationships in your data.",analysisType:"visualization",category:"Data Visualization",priority:"medium",variables:d.map(s=>s.columnName),path:"charts"}),a.sort((s,p)=>{const f={high:3,medium:2,low:1};return f[p.priority]-f[s.priority]})},_e=(t,d)=>{const r=tt(t),a=[];if(!r.hasData)return[];const i=d?Kt(d,r.variableAnalysis):null;return i?a.push(...Ht(i,r)):a.push(...r.recommendations),a},Kt=(t,d)=>{const r=t.toLowerCase(),a=d.filter(o=>r.includes(o.columnName.toLowerCase())),i={correlation:/correlat|relationship|associat|connect/i,comparison:/compar|differ|between|versus|vs/i,prediction:/predict|forecast|model|estimate/i,description:/describ|summar|explor|understand/i,test:/test|significant|p.?value|hypothesis/i};let l="general",m=0;for(const[o,h]of Object.entries(i))if(h.test(t)){l=o,m=.8;break}const n=r.split(/\s+/).filter(o=>o.length>3);return{analysisType:l,variables:a.map(o=>o.columnName),keywords:n,confidence:m}},Ht=(t,d)=>{const r=[],a=d.variableAnalysis.filter(i=>t.variables.includes(i.columnName));switch(t.analysisType){case"correlation":if(a.length>=2){const i=a.filter(l=>l.type===C.NUMERIC);i.length>=2&&r.push({id:"intent-correlation",title:"Correlation Analysis",description:"Examine relationships between your specified variables",reason:`Based on your query, analyze correlation between ${i.map(l=>l.columnName).join(" and ")}.`,analysisType:"correlation",category:"Correlation Analysis",priority:"high",variables:i.map(l=>l.columnName),path:"correlation-analysis/pearson"})}break;case"comparison":if(a.length>=2){const i=a.find(m=>m.type===C.NUMERIC),l=a.find(m=>m.type===C.CATEGORICAL||m.type===C.ORDINAL);if(i&&l){const m=l.categoryCount===2?"t-test":"anova",n=l.categoryCount===2?"T-Test":"ANOVA";r.push({id:"intent-comparison",title:`${n} Analysis`,description:`Compare ${i.columnName} across groups`,reason:`Compare ${i.columnName} between groups in ${l.columnName}.`,analysisType:m,category:"Inferential Statistics",priority:"high",variables:[i.columnName,l.columnName],path:m==="t-test"?"inference/ttest":"inference/anova"})}}break;case"prediction":if(a.length>=2){const i=a[0],l=a.slice(1);r.push({id:"intent-prediction",title:"Regression Analysis",description:"Build predictive model",reason:`Create a model to predict ${i.columnName} using ${l.map(m=>m.columnName).join(", ")}.`,analysisType:"regression",category:"Advanced Analysis",priority:"high",variables:a.map(m=>m.columnName),path:"correlation-analysis/regression"})}break;case"description":r.push({id:"intent-descriptive",title:"Descriptive Statistics",description:"Explore and summarize your data",reason:a.length>0?`Get detailed statistics for ${a.map(i=>i.columnName).join(", ")}.`:"Explore your dataset with comprehensive descriptive statistics.",analysisType:"descriptive",category:"Descriptive Statistics",priority:"high",variables:a.length>0?a.map(i=>i.columnName):d.variableAnalysis.map(i=>i.columnName),path:"stats/descriptives"});break}return r},Yt=t=>{const d=[],r=t.filter(n=>n.type===C.NUMERIC),a=t.filter(n=>n.type===C.CATEGORICAL||n.type===C.ORDINAL);r.length>a.length?d.push(`Your dataset is primarily numeric (${r.length} numeric vs ${a.length} categorical variables). Consider correlation and regression analyses.`):a.length>r.length?d.push(`Your dataset is primarily categorical (${a.length} categorical vs ${r.length} numeric variables). Consider chi-square tests and cross-tabulations.`):d.push("Your dataset has a balanced mix of numeric and categorical variables. This allows for diverse analysis approaches.");const i=t.filter(n=>n.missingPercentage>20);i.length>0&&d.push(`Variables with high missing data: ${i.map(n=>n.columnName).join(", ")}. Consider data cleaning before analysis.`);const l=r.filter(n=>n.isNormal===!1);l.length>0&&d.push(`Non-normal distributions detected in: ${l.map(n=>n.columnName).join(", ")}. Consider non-parametric tests or data transformation.`);const m=r.filter(n=>n.hasOutliers);return m.length>0&&d.push(`Potential outliers detected in: ${m.map(n=>n.columnName).join(", ")}. Review these values before analysis.`),d},Xt=(t,d)=>{const r=t.data,a=r.map((h,x)=>({row:h,index:x})).filter(({row:h})=>h[d]===null||h[d]===void 0||String(h[d]).trim()==="").map(({index:h})=>h);if(a.length===0)return{type:"MCAR",confidence:1,description:"No missing values detected",recommendation:"No action needed for missing data"};const i=r.length,l=a.length/i*100,m=Jt(r,d,a),n=ea(t,d,a);let o;return l<5?o={type:"MCAR",confidence:.8,description:`Low missing data rate (${l.toFixed(1)}%) with no obvious patterns`,recommendation:"Consider listwise deletion or simple imputation methods"}:m?o={type:"MNAR",confidence:.7,description:"Missing data appears to follow systematic patterns, possibly related to the variable itself",recommendation:"Consider domain knowledge for imputation or model-based approaches"}:n?o={type:"MAR",confidence:.6,description:"Missing data appears related to other observed variables",recommendation:"Use multiple imputation or regression-based imputation methods"}:o={type:"MCAR",confidence:.5,description:"Missing data appears random, but further testing recommended",recommendation:"Perform Little's MCAR test for confirmation, consider multiple imputation"},o},Jt=(t,d,r)=>{if(r.length<3)return!1;const a=[...r].sort((o,h)=>o-h),i=t.length*.25,l=t.length*.75,m=a.filter(o=>o<i).length/r.length>.6,n=a.filter(o=>o>l).length/r.length>.6;return m||n},ea=(t,d,r)=>{const a=t.data,i=t.columns.filter(l=>l.name!==d&&l.type===C.CATEGORICAL).slice(0,3);for(const l of i){const m={},n={};a.forEach((g,s)=>{const p=String(g[l.name]||"Unknown");n[p]=(n[p]||0)+1,r.includes(s)&&(m[p]=(m[p]||0)+1)});const o=Object.keys(n).map(g=>({category:g,rate:(m[g]||0)/n[g]})),h=Math.max(...o.map(g=>g.rate)),x=Math.min(...o.map(g=>g.rate));if(h-x>.2)return!0}return!1},ta=(t,d,r=["IQR","Z_SCORE","MODIFIED_Z_SCORE"])=>{const a=t.map((m,n)=>({value:m[d],index:n})).filter(({value:m})=>typeof m=="number"&&!isNaN(m));if(a.length<4)return[];const i=[],l=a.map(m=>m.value);if(r.includes("IQR")){const m=Ye(t,d,1.5);i.push({method:"IQR",outliers:m.outliers.map(n=>({rowIndex:n.rowIndex,value:n.value,severity:Math.abs(n.value-ce(l))>2*(m.q3-m.q1)?"extreme":"moderate"})),threshold:1.5,recommendation:m.outliers.length>0?"Investigate outliers - they may represent data entry errors or genuine extreme values":"No outliers detected using IQR method"})}if(r.includes("Z_SCORE")){const m=Xe(l),n=Je(l),o=a.map(({value:h,index:x})=>({rowIndex:x,value:h,zScore:Math.abs((h-m)/n),severity:Math.abs((h-m)/n)>3?"extreme":Math.abs((h-m)/n)>2?"moderate":"mild"})).filter(({zScore:h})=>h>2);i.push({method:"Z_SCORE",outliers:o,threshold:2,recommendation:o.length>0?"Consider investigating values with |z-score| > 2, especially those > 3":"No outliers detected using z-score method"})}if(r.includes("MODIFIED_Z_SCORE")){const m=ce(l),n=l.map(x=>Math.abs(x-m)),o=ce(n),h=a.map(({value:x,index:g})=>({rowIndex:g,value:x,modifiedZScore:o===0?0:.6745*(x-m)/o,severity:o===0?"mild":Math.abs(.6745*(x-m)/o)>3.5?"extreme":Math.abs(.6745*(x-m)/o)>2.5?"moderate":"mild"})).filter(({modifiedZScore:x})=>Math.abs(x)>2.5);i.push({method:"MODIFIED_Z_SCORE",outliers:h,threshold:2.5,recommendation:h.length>0?"Modified z-score method detected outliers - more robust to extreme values than standard z-score":"No outliers detected using modified z-score method"})}return i},aa=(t,d)=>{const r=t.map(a=>a[d.name]).filter(a=>a!=null&&String(a).trim()!=="");if(d.type===C.NUMERIC){const a=r.map(h=>Number(h)).filter(h=>!isNaN(h));if(a.length<3)return{normality:{isNormal:!1,pValue:NaN,statistic:NaN,skewness:NaN,kurtosis:NaN,interpretation:"Insufficient data for distribution analysis"},transformationSuggestions:[]};const i=et(a),l=Ft(a),m=Gt(a);let n="";const o=[];return i.isNormal?n="Data appears normally distributed - suitable for parametric tests":(n=`Data is not normally distributed (p=${i.pValue.toFixed(4)})`,Math.abs(l)>1?l>1?(n+=" - highly right-skewed",o.push("Consider log transformation for right skew")):(n+=" - highly left-skewed",o.push("Consider square or exponential transformation for left skew")):Math.abs(l)>.5&&(n+=l>0?" - moderately right-skewed":" - moderately left-skewed",o.push("Consider Box-Cox transformation")),Math.abs(m)>1&&(n+=m>0?" with heavy tails":" with light tails"),i.isNormal||o.push("Consider non-parametric tests as alternative")),{normality:{isNormal:i.isNormal,pValue:i.pValue,statistic:i.statistic,skewness:l,kurtosis:m,interpretation:n},transformationSuggestions:o}}else if(d.type===C.CATEGORICAL){const a=we(t.map(v=>v[d.name])),i=Object.values(a).reduce((v,z)=>v+z,0),l=Object.keys(a);if(l.length<2){const v=l[0]||"Unknown",z=a[v]||0;return{normality:{isNormal:!1,pValue:NaN,statistic:NaN,skewness:NaN,kurtosis:NaN,interpretation:"Single category - no variation"},balance:{isBalanced:!1,imbalanceRatio:1/0,minorityClassPercentage:0,majorityClassPercentage:100,recommendation:"Variable has no variation - consider removing",categoryDetails:{mostFrequent:{category:v,count:z,percentage:100},leastFrequent:{category:v,count:z,percentage:100},allCategories:[{category:v,count:z,percentage:100}]}},transformationSuggestions:["Remove variable - no variation"]}}const m=Object.values(a),n=Math.max(...m),o=Math.min(...m),h=n/o,x=o/i*100,g=n/i*100,s=Object.entries(a).map(([v,z])=>({category:v,count:z,percentage:z/i*100}));s.sort((v,z)=>z.count-v.count);const p=s[0],f=s[s.length-1];let S=!0,y="";return h>10?(S=!1,y=`Severe class imbalance detected. Leading category: '${p.category}' (${p.percentage.toFixed(1)}% of data), Least frequent: '${f.category}' (${f.percentage.toFixed(1)}% of data). Consider resampling techniques or combining rare categories.`):h>3?(S=!1,y=`Moderate class imbalance detected. Leading category: '${p.category}' (${p.percentage.toFixed(1)}% of data), Least frequent: '${f.category}' (${f.percentage.toFixed(1)}% of data). Monitor for potential bias in analyses.`):y=`Classes are reasonably balanced. Most frequent: '${p.category}' (${p.percentage.toFixed(1)}%), Least frequent: '${f.category}' (${f.percentage.toFixed(1)}%).`,{normality:{isNormal:!1,pValue:NaN,statistic:NaN,skewness:NaN,kurtosis:NaN,interpretation:"Categorical variable - normality not applicable"},balance:{isBalanced:S,imbalanceRatio:h,minorityClassPercentage:x,majorityClassPercentage:g,recommendation:y,categoryDetails:{mostFrequent:p,leastFrequent:f,allCategories:s}},transformationSuggestions:S?[]:["Consider stratified sampling to balance categories","Use appropriate statistical methods for imbalanced data",`Consider combining rare categories (${s.filter(v=>v.percentage<5).map(v=>v.category).join(", ")}) if meaningful`,"Apply class weights in machine learning models"]}}return{normality:{isNormal:!1,pValue:NaN,statistic:NaN,skewness:NaN,kurtosis:NaN,interpretation:"Distribution analysis not applicable for this data type"},transformationSuggestions:[]}},sa=t=>{const d=[],r=t.data;return t.columns.forEach(a=>{const i={type:"LOGICAL",variable:a.name,issues:[],recommendation:""},l=a.name.toLowerCase();if(a.type===C.NUMERIC&&r.forEach((m,n)=>{const o=m[a.name];typeof o=="number"&&!isNaN(o)&&(l.includes("age")&&(o<0||o>150)&&i.issues.push({rowIndex:n,value:o,description:`Unrealistic age value: ${o}`,severity:o<0||o>120?"high":"medium"}),(l.includes("percent")||l.includes("rate")||l.includes("ratio"))&&(o<0||o>100)&&i.issues.push({rowIndex:n,value:o,description:`Percentage/rate outside 0-100 range: ${o}`,severity:"medium"}),(l.includes("score")||l.includes("grade"))&&(o<0||o>100)&&i.issues.push({rowIndex:n,value:o,description:`Score outside expected range: ${o}`,severity:"low"}),(l.includes("count")||l.includes("number"))&&o<0&&i.issues.push({rowIndex:n,value:o,description:`Negative count value: ${o}`,severity:"high"}))}),(a.type===C.DATE||l.includes("date"))&&r.forEach((m,n)=>{const o=m[a.name];if(o&&typeof o!="boolean"){const h=new Date(o),x=new Date,g=new Date("1900-01-01");isNaN(h.getTime())?i.issues.push({rowIndex:n,value:o,description:`Invalid date format: ${o}`,severity:"high"}):h>x?i.issues.push({rowIndex:n,value:o,description:`Future date: ${o}`,severity:"medium"}):h<g&&i.issues.push({rowIndex:n,value:o,description:`Very old date (before 1900): ${o}`,severity:"low"})}}),i.issues.length>0){const m=i.issues.filter(o=>o.severity==="high").length,n=i.issues.filter(o=>o.severity==="medium").length;m>0?i.recommendation=`Critical: ${m} high-severity issues found. Review and correct these values.`:n>0?i.recommendation=`${n} medium-severity issues found. Verify these values are correct.`:i.recommendation=`${i.issues.length} minor issues found. Consider reviewing for data quality.`,d.push(i)}}),d},ia=t=>{const d=t.data.length,r=t.columns.length;let a=30;const i=[];if(r>10&&(a=Math.max(a,r*10),i.push("For multiple regression: aim for 10-15 observations per predictor variable")),r>=5){const n=r*5;a=Math.max(a,n),i.push("For factor analysis: minimum 5 observations per variable, preferably 10+")}t.columns.filter(n=>n.type===C.CATEGORICAL).forEach(n=>{const o=we(t.data.map(x=>x[n.name]));Object.keys(o).length>2&&Math.min(...Object.values(o))<5&&i.push(`Variable "${n.name}" has categories with <5 observations - consider combining categories`)});const m=d>=a;return m?i.push("Sample size appears adequate for planned analyses"):(i.push(`Current sample size (${d}) is below recommended minimum (${a})`),i.push("Consider collecting more data or using simpler analytical approaches")),{isAdequate:m,currentSize:d,recommendedMinimum:a,recommendations:i}},ra=t=>{const d=[];return t.columns.forEach(r=>{const a=t.data.map(l=>l[r.name]).filter(l=>l!=null&&String(l).trim()!=="");if(a.length===0)return;const i=Ot(a);if(i!==r.type){let l=.7,m="",n=!0;const o=r.name.toLowerCase();if(r.type===C.NUMERIC&&i===C.DATE&&(["age","score","count","number","amount","value","price","cost","weight","height","length","width","depth","size","quantity","percent","rate","ratio","index","level","grade","rank"].some(x=>o.includes(x))||a.every(g=>{const s=Number(g);return Number.isInteger(s)&&s>=0&&s<=200})?n=!1:(l=.4,m="Values could be timestamps or date codes - verify if this represents dates")),r.type===C.TEXT&&i===C.CATEGORICAL){const h=new Set(a).size;h<=10&&h<a.length*.5&&(l=.9,m=`Only ${h} unique values out of ${a.length} observations - likely categorical`)}else r.type===C.CATEGORICAL&&i===C.NUMERIC?a.every(x=>!isNaN(Number(x)))&&(l=.8,m="All values are numeric - consider if this should be a numeric variable"):r.type===C.TEXT&&i===C.BOOLEAN?(l=.95,m="Values appear to be boolean (true/false, yes/no, 0/1)"):r.type===C.TEXT&&i===C.DATE&&(a.some(x=>{const g=String(x);return g.includes("/")||g.includes("-")||/\d{4}/.test(g)})?(l=.8,m="Values appear to contain date patterns"):n=!1);n&&d.push({variable:r.name,currentType:r.type,suggestedType:i,confidence:l,reason:m||`Detected type (${i}) differs from current type (${r.type})`})}}),d},na=t=>{let d=100;const r=[],a=t.columns.map(y=>it(y,t.data)),i=a.filter(y=>y.isIdentifier);a.filter(y=>!y.isIdentifier);const l=i.map(y=>({variable:y.columnName,analysis:y.identifierAnalysis}));i.forEach(y=>{const v=y.identifierAnalysis;v.duplicateCount>0&&(r.push({priority:"high",category:"identifiers",title:`Duplicate IDs in ${y.columnName}`,description:`${v.duplicateCount} duplicate identifiers found`,actionSteps:["Review data collection process for duplicate entries","Implement unique constraints for identifier fields","Consider adding sequence numbers to distinguish duplicates"],impact:"Duplicate IDs can cause data integrity issues and analysis errors"}),d-=15),v.completenessPercentage<95&&(r.push({priority:"medium",category:"identifiers",title:`Missing IDs in ${y.columnName}`,description:`${(100-v.completenessPercentage).toFixed(1)}% of identifiers are missing`,actionSteps:["Ensure all records have unique identifiers","Implement data validation rules for required ID fields","Review data entry procedures"],impact:"Missing identifiers can make data tracking and linking difficult"}),d-=10)});const m={totalMissing:0,missingPercentage:0,patternsByVariable:{},overallPattern:{type:"MCAR",confidence:1,description:"No missing data detected",recommendation:"No action needed"},excludedIdentifiers:i.map(y=>y.columnName)},n=t.columns.filter(y=>!i.some(v=>v.columnName===y.name)),o=He(t.data,n.map(y=>y.name));m.totalMissing=o.total,m.missingPercentage=o.total/(t.data.length*n.length)*100,n.forEach(y=>{o.byColumn[y.name]>0&&(m.patternsByVariable[y.name]=Xt(t,y.name))}),m.missingPercentage>20?(d-=30,r.push({priority:"critical",category:"missing_data",title:"High Missing Data Rate",description:`${m.missingPercentage.toFixed(1)}% of data is missing`,actionSteps:["Investigate reasons for missing data","Consider multiple imputation methods","Evaluate if data collection can be improved"],impact:"Severely limits analysis validity and statistical power"})):m.missingPercentage>5&&(d-=15,r.push({priority:"high",category:"missing_data",title:"Moderate Missing Data",description:`${m.missingPercentage.toFixed(1)}% of data is missing`,actionSteps:["Analyze missing data patterns","Consider appropriate imputation methods","Document missing data handling in analysis"],impact:"May affect analysis results and reduce statistical power"}));const h={};t.columns.filter(y=>y.type===C.NUMERIC).filter(y=>!i.some(v=>v.columnName===y.name)).forEach(y=>{h[y.name]=ta(t.data,y.name);const z=h[y.name].reduce((V,O)=>V+O.outliers.length,0)/t.data.length*100;z>10&&(d-=10,r.push({priority:"medium",category:"outliers",title:`High Outlier Rate in ${y.name}`,description:`${z.toFixed(1)}% of values are outliers`,actionSteps:["Investigate outlier values for data entry errors","Consider outlier treatment methods","Document outlier handling decisions"],impact:"May skew results and affect statistical assumptions"}))});const x={};t.columns.filter(y=>!i.some(v=>v.columnName===y.name)).forEach(y=>{x[y.name]=aa(t.data,y);const v=x[y.name];if(y.type===C.NUMERIC&&!v.normality.isNormal&&v.transformationSuggestions.length>0&&r.push({priority:"medium",category:"distribution",title:`Non-normal Distribution: ${y.name}`,description:v.normality.interpretation,actionSteps:v.transformationSuggestions,impact:"May violate assumptions of parametric statistical tests"}),v.balance&&!v.balance.isBalanced){const z=v.balance.imbalanceRatio>10?"high":"medium",V=v.balance.categoryDetails.mostFrequent,O=v.balance.categoryDetails.leastFrequent;r.push({priority:z,category:"distribution",title:`Class Imbalance: ${y.name}`,description:`Leading category: '${V.category}' (${V.percentage.toFixed(1)}% of data), Least frequent: '${O.category}' (${O.percentage.toFixed(1)}% of data). Imbalance ratio: ${v.balance.imbalanceRatio.toFixed(1)}:1`,actionSteps:[`Consider combining rare categories: ${v.balance.categoryDetails.allCategories.filter($=>$.percentage<5).map($=>`'${$.category}'`).join(", ")}`,"Apply stratified sampling to balance categories","Use class weights in statistical models","Consider oversampling minority classes or undersampling majority class"],impact:`May bias analysis results toward majority class '${V.category}' and reduce statistical power for rare categories`}),z==="high"?d-=15:d-=8}});const g=sa(t).filter(y=>!i.some(v=>v.columnName===y.variable));g.forEach(y=>{const v=y.issues.filter(V=>V.severity==="high").length,z=y.issues.filter(V=>V.severity==="medium").length;v>0?(d-=20,r.push({priority:"critical",category:"consistency",title:`Data Consistency Issues: ${y.variable}`,description:`${v} critical consistency issues found`,actionSteps:["Review and correct invalid values","Implement data validation rules","Check data entry procedures"],impact:"Invalid data can lead to incorrect analysis results"})):z>0&&(d-=10,r.push({priority:"high",category:"consistency",title:`Potential Data Issues: ${y.variable}`,description:`${z} potential issues found`,actionSteps:["Verify questionable values","Document any intentional unusual values"],impact:"May indicate data quality problems"}))});const s=ra(t).filter(y=>!i.some(v=>v.columnName===y.variable));s.forEach(y=>{y.confidence>.8&&(r.push({priority:"medium",category:"data_types",title:`Data Type Mismatch: ${y.variable}`,description:y.reason,actionSteps:[`Consider changing from ${y.currentType} to ${y.suggestedType}`,"Verify the intended data type for analysis"],impact:"Incorrect data types can limit analysis options"}),d-=5)});const p=ia(t);p.isAdequate||(d-=25,r.push({priority:"high",category:"sample_size",title:"Insufficient Sample Size",description:`Current: ${p.currentSize}, Recommended: ${p.recommendedMinimum}`,actionSteps:p.recommendations,impact:"Reduces statistical power and generalizability of results"}));let f;d>=90?f="excellent":d>=75?f="good":d>=60?f="fair":f="poor";const S={critical:0,high:1,medium:2,low:3};return r.sort((y,v)=>S[y.priority]-S[v.priority]),{overallScore:Math.max(0,d),overallGrade:f,identifierVariables:l,missingDataAnalysis:m,outlierAnalysis:h,distributionAnalysis:x,consistencyChecks:g,dataTypeValidation:s,sampleSizeAssessment:p,prioritizedRecommendations:r}},oa=({assessment:t,datasetName:d})=>{const r=Qe(),[a,i]=D.useState(["overview","recommendations"]),[l,m]=D.useState(!1),n=s=>{i(p=>p.includes(s)?p.filter(f=>f!==s):[...p,s])},o=s=>{switch(s){case"critical":return r.palette.error.main;case"high":return r.palette.warning.main;case"medium":return r.palette.info.main;case"low":return r.palette.success.main;default:return r.palette.grey[500]}},h=s=>{switch(s){case"critical":return e.jsx(qe,{});case"high":return e.jsx(ft,{});case"medium":return e.jsx(Oe,{});case"low":return e.jsx(K,{});default:return e.jsx(Oe,{})}},x=s=>{switch(s){case"excellent":return r.palette.success.main;case"good":return r.palette.info.main;case"fair":return r.palette.warning.main;case"poor":return r.palette.error.main;default:return r.palette.grey[500]}},g=l?t.prioritizedRecommendations:t.prioritizedRecommendations.slice(0,5);return e.jsxs(b,{sx:{width:"100%"},children:[e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(xe,{color:"primary",sx:{mr:1,fontSize:28}}),e.jsx(u,{variant:"h6",children:"Data Quality Assessment"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:`${t.overallGrade.toUpperCase()} (${t.overallScore}/100)`,color:t.overallGrade==="excellent"?"success":t.overallGrade==="good"?"info":t.overallGrade==="fair"?"warning":"error",sx:{fontWeight:"bold"}})]}),e.jsx(ie,{sx:{mb:3,bgcolor:U(x(t.overallGrade),.05)},children:e.jsxs(be,{children:[e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(pt,{sx:{mr:1,color:x(t.overallGrade)}}),e.jsxs(u,{variant:"h6",children:["Overall Data Quality: ",t.overallGrade.charAt(0).toUpperCase()+t.overallGrade.slice(1)]})]}),e.jsxs(b,{sx:{mb:2},children:[e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(u,{variant:"body2",color:"text.secondary",children:"Quality Score"}),e.jsxs(u,{variant:"body2",fontWeight:"bold",children:[t.overallScore,"/100"]})]}),e.jsx(Ee,{variant:"determinate",value:t.overallScore,sx:{height:8,borderRadius:4,bgcolor:U(r.palette.grey[300],.3),"& .MuiLinearProgress-bar":{bgcolor:x(t.overallGrade),borderRadius:4}}})]}),e.jsxs(u,{variant:"body2",color:"text.secondary",children:["Dataset: ",e.jsx("strong",{children:d})," •",t.prioritizedRecommendations.length," recommendations •",t.prioritizedRecommendations.filter(s=>s.priority==="critical"||s.priority==="high").length," high priority issues"]})]})}),e.jsxs(Z,{expanded:a.includes("recommendations"),onChange:()=>n("recommendations"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(ut,{sx:{mr:1,color:r.palette.primary.main}}),e.jsx(u,{variant:"h6",children:"Priority Recommendations"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:t.prioritizedRecommendations.length,size:"small",color:"primary",sx:{mr:1}})]})}),e.jsx(Q,{children:g.length===0?e.jsxs(k,{severity:"success",icon:e.jsx(K,{}),children:[e.jsx(u,{variant:"subtitle2",children:"Excellent Data Quality!"}),e.jsx(u,{variant:"body2",children:"No significant data quality issues detected. Your dataset appears to be well-prepared for analysis."})]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{sx:{width:"100%"},children:g.map((s,p)=>e.jsxs(q,{sx:{mb:2,bgcolor:U(o(s.priority),.05),borderRadius:2,border:`1px solid ${U(o(s.priority),.2)}`,flexDirection:"column",alignItems:"flex-start"},children:[e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%",mb:1},children:[e.jsx(W,{sx:{minWidth:40},children:kt.cloneElement(h(s.priority),{sx:{color:o(s.priority)}})}),e.jsxs(b,{sx:{flexGrow:1},children:[e.jsx(u,{variant:"subtitle1",fontWeight:"bold",children:s.title}),e.jsx(u,{variant:"body2",color:"text.secondary",children:s.description})]}),e.jsx(T,{label:s.priority.toUpperCase(),size:"small",sx:{bgcolor:o(s.priority),color:"white",fontWeight:"bold"}})]}),e.jsxs(b,{sx:{width:"100%",pl:5},children:[e.jsxs(u,{variant:"body2",sx:{mb:1,fontStyle:"italic"},children:[e.jsx("strong",{children:"Impact:"})," ",s.impact]}),e.jsx(u,{variant:"body2",sx:{mb:1,fontWeight:"bold"},children:"Recommended Actions:"}),e.jsx(H,{dense:!0,sx:{pl:2},children:s.actionSteps.map((f,S)=>e.jsx(q,{sx:{py:.5,pl:0},children:e.jsx(B,{primary:`${S+1}. ${f}`,primaryTypographyProps:{variant:"body2"}})},S))})]})]},p))}),t.prioritizedRecommendations.length>5&&e.jsx(b,{sx:{textAlign:"center",mt:2},children:e.jsx(ee,{variant:"outlined",onClick:()=>m(!l),endIcon:l?e.jsx(Ke,{}):e.jsx(L,{}),children:l?"Show Less":`Show ${t.prioritizedRecommendations.length-5} More Recommendations`})})]})})]}),t.identifierVariables.length>0&&e.jsxs(Z,{expanded:a.includes("identifiers"),onChange:()=>n("identifiers"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(ht,{sx:{mr:1,color:r.palette.secondary.main}}),e.jsx(u,{variant:"h6",children:"Identifier Variables"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:`${t.identifierVariables.length} identifiers`,size:"small",color:"secondary"})]})}),e.jsxs(Q,{children:[e.jsx(k,{severity:"info",sx:{mb:2},children:e.jsx(u,{variant:"body2",children:"Identifier variables are automatically excluded from statistical validation (outlier detection, normality tests, etc.) as they serve identification purposes rather than analytical ones."})}),t.identifierVariables.map((s,p)=>e.jsxs(b,{sx:{mb:3},children:[e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(gt,{sx:{mr:1,color:r.palette.secondary.main}}),e.jsx(u,{variant:"subtitle1",fontWeight:"bold",children:s.variable})]}),e.jsxs(P,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(P,{item:!0,xs:12,md:6,children:e.jsxs(te,{variant:"outlined",sx:{p:2},children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Quality Metrics"}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(u,{variant:"body2",children:"Completeness:"}),e.jsxs(u,{variant:"body2",fontWeight:"bold",children:[s.analysis.completenessPercentage.toFixed(1),"%"]})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(u,{variant:"body2",children:"Uniqueness:"}),e.jsxs(u,{variant:"body2",fontWeight:"bold",children:[s.analysis.uniquenessPercentage.toFixed(1),"%"]})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(u,{variant:"body2",children:"Duplicates:"}),e.jsx(u,{variant:"body2",fontWeight:"bold",color:s.analysis.duplicateCount>0?"error":"success",children:s.analysis.duplicateCount})]})]})}),e.jsx(P,{item:!0,xs:12,md:6,children:e.jsxs(te,{variant:"outlined",sx:{p:2},children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Pattern Analysis"}),e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(u,{variant:"body2",sx:{mr:1},children:"Sequential Pattern:"}),e.jsx(T,{label:s.analysis.hasSequentialPattern?"Yes":"No",size:"small",color:s.analysis.hasSequentialPattern?"success":"default"})]}),e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(u,{variant:"body2",sx:{mr:1},children:"Alphanumeric Pattern:"}),e.jsx(T,{label:s.analysis.hasAlphanumericPattern?"Yes":"No",size:"small",color:s.analysis.hasAlphanumericPattern?"success":"default"})]})]})})]}),e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Recommendations"}),e.jsx(H,{dense:!0,children:s.analysis.recommendations.map((f,S)=>e.jsxs(q,{sx:{py:.5,pl:0},children:[e.jsx(W,{sx:{minWidth:30},children:e.jsx(ne,{sx:{fontSize:16,color:r.palette.secondary.main}})}),e.jsx(B,{primary:f,primaryTypographyProps:{variant:"body2"}})]},S))}),p<t.identifierVariables.length-1&&e.jsx(ve,{sx:{mt:2}})]},p))]})]}),e.jsxs(Z,{expanded:a.includes("missing"),onChange:()=>n("missing"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(yt,{sx:{mr:1}}),e.jsx(u,{variant:"h6",children:"Missing Data Analysis"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:`${t.missingDataAnalysis.missingPercentage.toFixed(1)}% missing`,size:"small",color:t.missingDataAnalysis.missingPercentage>10?"error":t.missingDataAnalysis.missingPercentage>5?"warning":"success"})]})}),e.jsx(Q,{children:e.jsxs(P,{container:!0,spacing:2,children:[e.jsxs(P,{item:!0,xs:12,md:6,children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Overall Pattern"}),e.jsx(k,{severity:t.missingDataAnalysis.overallPattern.type==="MCAR"?"info":"warning",sx:{mb:2},children:e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:t.missingDataAnalysis.overallPattern.type})," - ",t.missingDataAnalysis.overallPattern.description]})}),e.jsx(u,{variant:"body2",color:"text.secondary",children:t.missingDataAnalysis.overallPattern.recommendation})]}),e.jsxs(P,{item:!0,xs:12,md:6,children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Missing Data Summary"}),e.jsxs(b,{sx:{bgcolor:U(r.palette.info.main,.05),p:2,borderRadius:1},children:[e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Total Missing:"})," ",t.missingDataAnalysis.totalMissing," values"]}),e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Percentage:"})," ",t.missingDataAnalysis.missingPercentage.toFixed(2),"%"]}),e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Variables Affected:"})," ",Object.keys(t.missingDataAnalysis.patternsByVariable).length]}),t.missingDataAnalysis.excludedIdentifiers.length>0&&e.jsxs(u,{variant:"body2",sx:{mt:1,fontStyle:"italic"},children:[e.jsx("strong",{children:"Note:"})," Excluded ",t.missingDataAnalysis.excludedIdentifiers.length," identifier variable(s) from analysis"]})]})]})]})})]}),e.jsxs(Z,{expanded:a.includes("sample"),onChange:()=>n("sample"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(Pe,{sx:{mr:1}}),e.jsx(u,{variant:"h6",children:"Sample Size Assessment"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:t.sampleSizeAssessment.isAdequate?"Adequate":"Insufficient",size:"small",color:t.sampleSizeAssessment.isAdequate?"success":"warning"})]})}),e.jsx(Q,{children:e.jsxs(P,{container:!0,spacing:2,children:[e.jsx(P,{item:!0,xs:12,md:6,children:e.jsxs(b,{sx:{bgcolor:U(r.palette.info.main,.05),p:2,borderRadius:1,mb:2},children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Sample Size Details"}),e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Current Size:"})," ",t.sampleSizeAssessment.currentSize]}),e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Recommended Minimum:"})," ",t.sampleSizeAssessment.recommendedMinimum]}),e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Status:"})," ",t.sampleSizeAssessment.isAdequate?"Adequate":"Needs more data"]})]})}),e.jsxs(P,{item:!0,xs:12,md:6,children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Recommendations"}),e.jsx(H,{dense:!0,children:t.sampleSizeAssessment.recommendations.map((s,p)=>e.jsxs(q,{sx:{py:.5,pl:0},children:[e.jsx(W,{sx:{minWidth:30},children:e.jsx(ne,{sx:{fontSize:16,color:r.palette.warning.main}})}),e.jsx(B,{primary:s,primaryTypographyProps:{variant:"body2"}})]},p))})]})]})})]}),t.dataTypeValidation.length>0&&e.jsxs(Z,{expanded:a.includes("types"),onChange:()=>n("types"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(re,{sx:{mr:1}}),e.jsx(u,{variant:"h6",children:"Data Type Validation"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:`${t.dataTypeValidation.length} suggestions`,size:"small",color:"info"})]})}),e.jsx(Q,{children:e.jsx(ue,{component:te,variant:"outlined",children:e.jsxs(he,{size:"small",children:[e.jsx(ge,{children:e.jsxs(J,{children:[e.jsx(I,{children:e.jsx("strong",{children:"Variable"})}),e.jsx(I,{children:e.jsx("strong",{children:"Current Type"})}),e.jsx(I,{children:e.jsx("strong",{children:"Suggested Type"})}),e.jsx(I,{children:e.jsx("strong",{children:"Confidence"})}),e.jsx(I,{children:e.jsx("strong",{children:"Reason"})})]})}),e.jsx(ye,{children:t.dataTypeValidation.map((s,p)=>e.jsxs(J,{children:[e.jsx(I,{children:s.variable}),e.jsx(I,{children:e.jsx(T,{label:s.currentType,size:"small",variant:"outlined"})}),e.jsx(I,{children:e.jsx(T,{label:s.suggestedType,size:"small",color:"primary",variant:"outlined"})}),e.jsx(I,{children:e.jsxs(b,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(Ee,{variant:"determinate",value:s.confidence*100,sx:{width:60,mr:1,height:6}}),e.jsxs(u,{variant:"caption",children:[(s.confidence*100).toFixed(0),"%"]})]})}),e.jsx(I,{children:e.jsx(u,{variant:"body2",sx:{maxWidth:200},children:s.reason})})]},p))})]})})})]}),t.consistencyChecks.length>0&&e.jsxs(Z,{expanded:a.includes("consistency"),onChange:()=>n("consistency"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(qe,{sx:{mr:1}}),e.jsx(u,{variant:"h6",children:"Consistency Issues"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:`${t.consistencyChecks.reduce((s,p)=>s+p.issues.length,0)} issues`,size:"small",color:"error"})]})}),e.jsx(Q,{children:t.consistencyChecks.map((s,p)=>e.jsxs(b,{sx:{mb:3},children:[e.jsx(u,{variant:"subtitle1",gutterBottom:!0,children:s.variable}),e.jsx(u,{variant:"body2",color:"text.secondary",sx:{mb:2},children:s.recommendation}),e.jsx(ue,{component:te,variant:"outlined",sx:{mb:2},children:e.jsxs(he,{size:"small",children:[e.jsx(ge,{children:e.jsxs(J,{children:[e.jsx(I,{children:e.jsx("strong",{children:"Row"})}),e.jsx(I,{children:e.jsx("strong",{children:"Value"})}),e.jsx(I,{children:e.jsx("strong",{children:"Issue"})}),e.jsx(I,{children:e.jsx("strong",{children:"Severity"})})]})}),e.jsx(ye,{children:s.issues.slice(0,10).map((f,S)=>e.jsxs(J,{children:[e.jsx(I,{children:f.rowIndex+1}),e.jsx(I,{children:e.jsx("code",{children:String(f.value)})}),e.jsx(I,{children:f.description}),e.jsx(I,{children:e.jsx(T,{label:f.severity.toUpperCase(),size:"small",color:f.severity==="high"?"error":f.severity==="medium"?"warning":"info"})})]},S))})]})}),s.issues.length>10&&e.jsxs(u,{variant:"caption",color:"text.secondary",children:["Showing first 10 of ",s.issues.length," issues"]})]},p))})]}),e.jsxs(Z,{expanded:a.includes("distribution"),onChange:()=>n("distribution"),sx:{mb:2},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.jsx(Pe,{sx:{mr:1}}),e.jsx(u,{variant:"h6",children:"Distribution Analysis"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(T,{label:`${Object.keys(t.distributionAnalysis).length} analytical variables`,size:"small",color:"info"})]})}),e.jsxs(Q,{children:[t.identifierVariables.length>0&&e.jsx(k,{severity:"info",sx:{mb:2},children:e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Note:"})," ",t.identifierVariables.length," identifier variable(s) (",t.identifierVariables.map(s=>s.variable).join(", "),") are excluded from distribution analysis as statistical measures don't apply to identifier variables."]})}),Object.keys(t.distributionAnalysis).length===0?e.jsx(k,{severity:"info",children:e.jsx(u,{variant:"body2",children:"No analytical variables found for distribution analysis. All variables appear to be identifiers."})}):Object.entries(t.distributionAnalysis).map(([s,p])=>e.jsxs(b,{sx:{mb:3},children:[e.jsx(u,{variant:"subtitle1",gutterBottom:!0,children:s}),p.normality.interpretation!=="Categorical variable - normality not applicable"&&p.normality.interpretation!=="Distribution analysis not applicable for this data type"&&e.jsx(b,{sx:{mb:2},children:e.jsxs(k,{severity:p.normality.isNormal?"success":"warning",sx:{mb:1},children:[e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Normality:"})," ",p.normality.interpretation]}),!isNaN(p.normality.pValue)&&e.jsxs(u,{variant:"body2",children:["p-value: ",p.normality.pValue.toFixed(4),", Skewness: ",p.normality.skewness.toFixed(3),", Kurtosis: ",p.normality.kurtosis.toFixed(3)]})]})}),p.balance&&e.jsxs(b,{sx:{mb:2},children:[e.jsxs(k,{severity:p.balance.isBalanced?"success":"warning",sx:{mb:2},children:[e.jsxs(u,{variant:"body2",sx:{mb:1},children:[e.jsx("strong",{children:"Class Balance:"})," ",p.balance.recommendation]}),e.jsxs(u,{variant:"body2",children:[e.jsx("strong",{children:"Imbalance Ratio:"})," ",p.balance.imbalanceRatio.toFixed(1),":1"]})]}),e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Category Distribution"}),e.jsx(ue,{component:te,variant:"outlined",sx:{mb:2},children:e.jsxs(he,{size:"small",children:[e.jsx(ge,{children:e.jsxs(J,{children:[e.jsx(I,{children:e.jsx("strong",{children:"Category"})}),e.jsx(I,{align:"right",children:e.jsx("strong",{children:"Count"})}),e.jsx(I,{align:"right",children:e.jsx("strong",{children:"Percentage"})}),e.jsx(I,{children:e.jsx("strong",{children:"Status"})})]})}),e.jsx(ye,{children:p.balance.categoryDetails.allCategories.map((f,S)=>e.jsxs(J,{children:[e.jsx(I,{children:f.category}),e.jsx(I,{align:"right",children:f.count}),e.jsxs(I,{align:"right",children:[f.percentage.toFixed(1),"%"]}),e.jsxs(I,{children:[f.category===p.balance.categoryDetails.mostFrequent.category&&e.jsx(T,{label:"Most Frequent",size:"small",color:"primary"}),f.category===p.balance.categoryDetails.leastFrequent.category&&e.jsx(T,{label:"Least Frequent",size:"small",color:"warning"}),f.percentage<5&&f.category!==p.balance.categoryDetails.leastFrequent.category&&e.jsx(T,{label:"Rare (<5%)",size:"small",color:"error"})]})]},S))})]})})]}),p.transformationSuggestions.length>0&&e.jsxs(b,{children:[e.jsx(u,{variant:"subtitle2",gutterBottom:!0,children:"Recommended Transformations"}),e.jsx(H,{dense:!0,children:p.transformationSuggestions.map((f,S)=>e.jsxs(q,{sx:{py:.5,pl:0},children:[e.jsx(W,{sx:{minWidth:30},children:e.jsx(ne,{sx:{fontSize:16,color:r.palette.info.main}})}),e.jsx(B,{primary:f,primaryTypographyProps:{variant:"body2"}})]},S))})]}),Object.keys(t.distributionAnalysis).indexOf(s)<Object.keys(t.distributionAnalysis).length-1&&e.jsx(ve,{sx:{mt:2}})]},s))]})]})]})},la=({onNavigate:t})=>{var Re,ke;const d=Qe(),{currentDataset:r}=$t(),[a,i]=D.useState(""),[l,m]=D.useState("assistant"),[n,o]=D.useState([]),[h,x]=D.useState([]),[g,s]=D.useState([]),[p,f]=D.useState({open:!1,message:""}),[S,y]=D.useState(!1),[v,z]=D.useState(!0),[V,O]=D.useState(!1),[$,je]=D.useState([]),[Y,Ae]=D.useState(!1),[w,Ce]=D.useState(null),[Se,de]=D.useState([]),[me,pe]=D.useState([]);D.useEffect(()=>{if(Lt(),r){const c=tt(r);if(Ce(c),c.hasData){const N=_e(r);de(N);const j=Yt(c.variableAnalysis);pe(j)}else de([]),pe([])}else Ce(null),de([]),pe([])},[r]);const X=[{id:"DESC1",text:"Descriptive Analysis",description:"Calculate mean, median, mode, SD, variance, range, quartiles, skewness, kurtosis",type:"descriptive",category:"Descriptive Analysis",path:"stats/summary",tags:["summary","statistics","mean","median","standard deviation","basic","describe","central tendency","variability"]},{id:"DESC2",text:"Frequency Tables",description:"Generate frequency and percentage tables for categorical variables",type:"descriptive",category:"Descriptive Analysis",path:"stats/frequency",tags:["frequency","categorical","count","percentage","basic","nominal","ordinal"]},{id:"DESC3",text:"Cross Tabulation",description:"Create two-way tables showing joint frequency distribution",type:"descriptive",category:"Descriptive Analysis",path:"stats/crosstabulation",tags:["crosstab","contingency","two-way","association","joint distribution"]},{id:"DESC4",text:"Normality Test",description:"Test if data follows normal distribution (Kolmogorov-Smirnov, Shapiro-Wilk)",type:"assumption",category:"Descriptive Analysis",path:"inference/assumptions/normality",tags:["normal","distribution","assumption","K-S test","shapiro","gaussian","parametric assumptions"]},{id:"TTEST1",text:"One-Sample T-Test",description:"Compare sample mean to a hypothesized population mean",type:"test",category:"T-Tests",path:"inference/ttest/onesample",tags:["t-test","one sample","mean comparison","hypothesis","parametric","population mean"]},{id:"TTEST2",text:"Independent Samples T-Test",description:"Compare means of two independent groups",type:"test",category:"T-Tests",path:"inference/ttest/independent",tags:["t-test","two groups","independent","between subjects","parametric","unpaired","two sample"]},{id:"TTEST3",text:"Paired Samples T-Test",description:"Compare means of two related samples or repeated measurements",type:"test",category:"T-Tests",path:"inference/ttest/paired/matched-case-control",tags:["t-test","paired","related","within subjects","pre-post","parametric","repeated","matched","before after"]},{id:"NONPAR1",text:"Mann-Whitney U Test",description:"Non-parametric alternative to independent t-test",type:"test",category:"Non-parametric Tests",path:"inference/nonparametric/mann_whitney",tags:["mann-whitney","wilcoxon rank-sum","non-parametric","two groups","independent","rank"]},{id:"NONPAR2",text:"Wilcoxon Signed-Rank Test",description:"Non-parametric alternative to paired t-test",type:"test",category:"Non-parametric Tests",path:"inference/nonparametric/wilcoxon",tags:["wilcoxon","signed-rank","non-parametric","paired","related","matched","pre-post","Before After"]},{id:"NONPAR3",text:"Kruskal-Wallis Test",description:"Non-parametric alternative to one-way ANOVA",type:"test",category:"Non-parametric Tests",path:"inference/nonparametric/kruskal_wallis",tags:["kruskal-wallis","non-parametric","multiple groups","ranks","three groups","Three or more groups","H-test"]},{id:"NONPAR4",text:"Friedman Test",description:"Non-parametric alternative to repeated measures ANOVA",type:"test",category:"Non-parametric Tests",path:"inference/nonparametric/friedman",tags:["friedman","non-parametric","repeated measures","ranks","within subjects"]},{id:"ANOVA1",text:"One-Way ANOVA",description:"Compare means of three or more independent groups",type:"test",category:"ANOVA",path:"inference/anova/oneway",tags:["anova","analysis of variance","multiple groups","f-test","parametric","three groups","omnibus"]},{id:"ANOVA2",text:"Two-Way ANOVA",description:"Examine effects of two factors and their interaction",type:"test",category:"ANOVA",path:"inference/anova/twoway",tags:["anova","two-way","factorial","interaction","parametric","main effects"]},{id:"ANOVA3",text:"Repeated Measures ANOVA",description:"Compare means across multiple time points or conditions",type:"test",category:"ANOVA",path:"inference/anova/repeated",tags:["anova","repeated measures","within subjects","longitudinal","parametric","time points"]},{id:"CAT1",text:"Chi-Square Test",description:"Test association between two categorical variables",type:"categorical",category:"Categorical Tests",path:"inference/categorical/chisquare",tags:["chi-square","χ²","association","independence","categorical","contingency","nominal"]},{id:"CORR1",text:"Correlation Matrix",description:"Calculate Pearson, Spearman, or Kendall correlations between variables",type:"correlation",category:"Correlation & Regression",path:"correlation/matrix",tags:["correlation","pearson","spearman","relationship","association","linear","monotonic"]},{id:"REG1",text:"Linear Regression",description:"Model linear relationships between dependent and independent variables",type:"regression",category:"Correlation & Regression",path:"correlation/linear",tags:["regression","linear","prediction","model","least squares","continuous outcome","predictor"]},{id:"REG2",text:"Logistic Regression",description:"Model binary outcomes and calculate odds ratios",type:"regression",category:"Correlation & Regression",path:"correlation/logistic",tags:["logistic","binary","classification","odds ratio","probability","dichotomous","yes no"]},{id:"ADV1",text:"Exploratory Factor Analysis",description:"Identify underlying factors in a set of variables",type:"advanced",category:"Advanced Methods",path:"advanced/efa",tags:["factor analysis","EFA","latent variables","dimension reduction","construct"]},{id:"ADV2",text:"Mediation and Moderation",description:"Analyze indirect effects and conditional relationships",type:"advanced",category:"Advanced Methods",path:"advanced/mediation",tags:["mediation","moderation","indirect effect","interaction","conditional"]},{id:"ADV3",text:"Reliability Analysis",description:"Assess internal consistency with Cronbach's alpha",type:"advanced",category:"Advanced Methods",path:"advanced/reliability",tags:["reliability","cronbach alpha","internal consistency","scale","questionnaire"]},{id:"ADV4",text:"Survival Analysis",description:"Analyze time-to-event data with Kaplan-Meier and Cox regression",type:"advanced",category:"Advanced Methods",path:"advanced/survival",tags:["survival","time-to-event","kaplan-meier","cox","hazard","censored"]},{id:"ADV5",text:"Cluster Analysis",description:"Group similar data points using K-means, hierarchical clustering",type:"advanced",category:"Advanced Methods",path:"advanced/cluster",tags:["cluster","grouping","k-means","hierarchical","segmentation","similarity"]},{id:"ADV6",text:"Meta Analysis",description:"Synthesize findings from multiple studies",type:"advanced",category:"Advanced Methods",path:"advanced/meta",tags:["meta-analysis","systematic review","effect size","forest plot","heterogeneity"]},{id:"EPI1",text:"Case-Control Calculator",description:"Calculate odds ratios and confidence intervals",type:"epidemiology",category:"Epidemiology",path:"epicalc/case_control",tags:["case-control","odds ratio","epidemiology","retrospective","disease"]},{id:"EPI2",text:"Cohort Calculator",description:"Calculate risk ratios and rate ratios",type:"epidemiology",category:"Epidemiology",path:"epicalc/cohort",tags:["cohort","risk ratio","prospective","incidence","follow-up"]},{id:"EPI3",text:"Cross-Sectional Calculator",description:"Calculate prevalence ratios and odds ratios",type:"epidemiology",category:"Epidemiology",path:"epicalc/cross_sectional",tags:["cross-sectional","prevalence","snapshot","survey","point in time"]},{id:"EPI4",text:"Matched Case-Control Calculator",description:"Calculate matched odds ratios for paired data",type:"epidemiology",category:"Epidemiology",path:"epicalc/matched",tags:["matched","paired","case-control","McNemar","conditional"]},{id:"SS1",text:"One Sample Calculator",description:"Calculate sample size for single group studies",type:"sample_size",category:"Sample Size",path:"samplesize/one",tags:["sample size","power","one sample","single group","planning"]},{id:"SS2",text:"Two Sample Calculator",description:"Calculate sample size for comparing two groups",type:"sample_size",category:"Sample Size",path:"samplesize/two",tags:["sample size","power","two groups","comparison","planning"]},{id:"SS3",text:"Paired Sample Calculator",description:"Calculate sample size for paired or matched designs",type:"sample_size",category:"Sample Size",path:"samplesize/paired",tags:["sample size","power","paired","matched","repeated","planning"]},{id:"SS4",text:"ANOVA Sample Size Calculator",description:"Calculate sample size for comparing multiple groups",type:"sample_size",category:"Sample Size",path:"samplesize/anova",tags:["sample size","power","anova","multiple groups","planning"]},{id:"VIZ1",text:"Bar Chart",description:"Compare values across categories",type:"visualization",category:"Visualizations",path:"charts/bar",tags:["bar chart","categorical","comparison","frequency","counts"]},{id:"VIZ2",text:"Box Plot",description:"Show distribution, median, quartiles, and outliers",type:"visualization",category:"Visualizations",path:"charts/box",tags:["box plot","distribution","quartiles","outliers","whiskers","five number summary"]},{id:"VIZ3",text:"Histogram",description:"Visualize frequency distribution of numerical data",type:"visualization",category:"Visualizations",path:"charts/histogram",tags:["histogram","frequency","distribution","bins","continuous","shape"]},{id:"VIZ4",text:"Scatter Plot",description:"Show relationship between two numerical variables",type:"visualization",category:"Visualizations",path:"charts/scatter",tags:["scatter","correlation","relationship","x-y plot","regression","bivariate"]},{id:"VIZ5",text:"RainCloud Plot",description:"Combine box plot, scatter plot, and density visualization",type:"visualization",category:"Visualizations",path:"charts/raincloud",tags:["raincloud","distribution","density","hybrid","violin","raw data"]},{id:"VIZ6",text:"Pie Chart",description:"Show proportions of categorical data",type:"visualization",category:"Visualizations",path:"charts/pie",tags:["pie chart","proportions","percentage","parts of whole","composition"]},{id:"DATA1",text:"Data Import",description:"Import data from CSV, Excel, or Google Sheets",type:"data",category:"Data Management",path:"data/import",tags:["import","load","csv","excel","upload","file"]},{id:"DATA2",text:"Data Editor",description:"View and edit your datasets directly",type:"data",category:"Data Management",path:"data/editor",tags:["edit","modify","clean","transform","spreadsheet"]},{id:"DATA3",text:"Variable Editor",description:"Modify variable names, types, and properties",type:"data",category:"Data Management",path:"data/variables",tags:["variables","rename","recode","type","label","metadata"]},{id:"DATA4",text:"Data Transform",description:"Create new variables, handle missing values",type:"data",category:"Data Management",path:"data/transform",tags:["transform","compute","missing","recode","derive","calculate"]},{id:"PUB1",text:"Table 1",description:"Generate baseline characteristics table",type:"publication",category:"Publication Tools",path:"tables/table1",tags:["table 1","baseline","characteristics","demographics","descriptive"]},{id:"PUB2",text:"Table 2",description:"Create outcome comparison table",type:"publication",category:"Publication Tools",path:"tables/table2",tags:["table 2","outcomes","results","comparison","primary endpoint"]},{id:"PUB3",text:"SMD Table",description:"Calculate standardized mean differences",type:"publication",category:"Publication Tools",path:"tables/smd",tags:["SMD","standardized","effect size","Cohen's d","balance"]},{id:"PUB4",text:"Regression Table",description:"Format regression results for publication",type:"publication",category:"Publication Tools",path:"tables/regression",tags:["regression table","coefficients","odds ratios","confidence intervals","formatted"]},{id:"PUB5",text:"Flow Diagram",description:"Create CONSORT/STROBE/PRISMA flow diagrams",type:"publication",category:"Publication Tools",path:"diagrams/flow",tags:["flow diagram","CONSORT","STROBE","PRISMA","participant flow","enrollment"]}],rt=[{name:"Comparing Two Independent Groups",keywords:["compare means of two independent groups","difference in test scores between male and female students","compare average height of two distinct populations","is there a significant difference between two unrelated samples","test if two groups are statistically different on a continuous variable","compare the average income of two different cities","compare group a and group b on a numerical outcome","is there a difference in means for two separate samples","compare two independent groups","difference between two groups","group a vs group b","control experimental two groups","compare means between two groups","compare two groups","two independent groups","difference between two groups","group a vs group b","control experimental two"],patterns:[/compar\w*\s+two\s+independent\s+groups?/i,/difference\s+between\s+two\s+unrelated\s+samples/i,/group\s+a\s+vs\s+group\s+b/i],suggestions:["TTEST2","NONPAR1","VIZ2","DESC1","PUB1"],contextualAdvice:"For comparing two independent groups, use Independent T-Test if data is normally distributed, or Mann-Whitney U Test if not."},{name:"Linear Regression",keywords:["is there a relationship between hours studied and exam performance","predict house prices based on square footage","effect of advertising spend on sales","model the relationship between a dependent variable and one or more independent variables","forecast future sales based on past marketing efforts","predict a continuous variable","predict a number","simple linear regression","multiple linear regression","predict blood pressure","how do i predict blood pressure","predict continuous variable","predict a number","linear regression","check for linearity and homoscedasticity","validate regression model assumptions with Residual Plots"],patterns:[/linear\s+regression/i,/predict\s+(house\s+prices|sales|blood\s+pressure|continuous\s+variable|number)/i,/relationship\s+between\s+hours\s+studied\s+and\s+exam\s+performance/i],suggestions:["REG1","CORR1","VIZ4","PUB4"],contextualAdvice:"Use Linear Regression to predict continuous outcomes. Check assumptions: linearity, normality of residuals, homoscedasticity, and independence."},{name:"Paired Samples T-Test",keywords:["pre and post treatment measurements for the same group","compare blood pressure before and after medication in the same patients","evaluate the impact of a training program on employee productivity","test for a difference between two related measurements","assess the change in a variable over time for the same subjects","pre-post analysis","related groups comparison","before after study","pre-post","pre post","related groups","paired measurements","before after","pre/post"],patterns:[/pre\s*and\s*post\s+treatment/i,/before\s*and\s*after\s+medication/i,/paired\s+samples?\s+t-test/i,/change\s+in\s+a\s+variable\s+over\s+time/i],suggestions:["TTEST3","NONPAR2","ANOVA3","VIZ4","VIZ2"],contextualAdvice:"For paired or repeated measurements, use Paired T-Test for two time points or Repeated Measures ANOVA for multiple time points."},{name:"One-Way ANOVA",keywords:["compare average across three different plant species","difference in customer satisfaction across multiple product versions","impact of different teaching methods on student engagement","test for differences among means of three or more independent groups","determine if there's a significant effect of a single categorical factor on a continuous outcome","compare three groups","compare multiple groups","anova for independent groups","compare over two groups","multiple group comparison anova","comparing more than two groups","anova test","3 groups","4 groups","multiple independent groups","anova for multiple groups","perform post-hoc tests after ANOVA","compare means of more than two groups","anova","more than two groups"],patterns:[/one-way\s+anova/i,/compare\s+(three|multiple|more\s+than\s+two)\s+groups?/i,/impact\s+of\s+different\s+teaching\s+methods/i],suggestions:["ANOVA1","NONPAR3","VIZ2","PUB1","ANOVA2"],contextualAdvice:"For comparing multiple groups, use One-Way ANOVA if data is normally distributed, or Kruskal-Wallis Test if not. Consider post-hoc tests if significant."},{name:"Logistic Regression",keywords:["determine if a categorical variable influences a binary outcome","predict customer churn based on demographic data","likelihood of loan default given credit score","model the probability of a binary event occurring","classify emails as spam or not spam based on content","predict a binary outcome","predict disease status","predict categorical variable","predict a category","how do i predict disease status","predict categorical variable","predict a category","logistic regression","predict binary outcome","evaluate binary classification model performance","roc curve","odds ratio"],patterns:[/logistic\s+regression/i,/predict\s+(customer\s+churn|loan\s+default|binary\s+outcome|disease\s+status|categorical\s+variable|category)/i,/classify\s+emails\s+as\s+spam/i],suggestions:["REG2","PUB4","EPI1"],contextualAdvice:"Use Logistic Regression for binary outcomes (yes/no, disease/healthy). Results are expressed as odds ratios with confidence intervals."},{name:"Correlation Analysis",keywords:["relationship between two continuous variables","strength and direction of association between two variables","explore the link between exercise and cholesterol levels","measure how two quantitative variables move together","investigate the association between temperature and ice cream sales","correlation between two numerical variables","how related are two variables","pearson correlation","spearman correlation","correlation","relationship between two variables","association numerical","how related are","visualize correlations between multiple variables"],patterns:[/correlat/i,/relationship\s+between/i,/associat\w*\s+between/i,/how\s+related\s+are/i],suggestions:["CORR1","REG1","VIZ4","REG2"],contextualAdvice:"Use Pearson correlation for linear relationships between continuous variables, or Spearman correlation for monotonic relationships or ordinal data."},{name:"One-Sample T-Test",keywords:["compare a sample mean to a known population mean","is the average weight of a new product batch different from the standard","test if a drug's effect differs from a placebo's known effect","determine if a sample mean is significantly different from a hypothesized population mean","compare a sample to a population standard"],patterns:[/one-sample\s+t-test/i,/compare\s+a\s+sample\s+mean\s+to\s+a\s+known\s+population\s+mean/i],suggestions:["TTEST1","DESC1"],contextualAdvice:"Use a One-Sample T-Test to compare a sample mean to a known or hypothesized population mean."},{name:"Kruskal-Wallis Test",keywords:["compare more than two independent groups, non-parametric","difference in rankings among several independent samples","non-parametric alternative to one-way ANOVA","test if there are significant differences in medians across multiple independent groups","compare three or more groups non-normal data","non-parametric comparison of more than two independent groups"],patterns:[/kruskal-wallis\s+test/i,/compare\s+(three|more\s+than\s+two)\s+groups\s+non-parametric/i],suggestions:["NONPAR3","VIZ2"],contextualAdvice:"Kruskal-Wallis is the non-parametric alternative to One-Way ANOVA for comparing three or more independent groups when data is not normally distributed."},{name:"Mann-Whitney U Test",keywords:["compare two independent groups, non-parametric","difference in distributions between two independent samples","non-parametric alternative to independent samples t-test","test if two independent samples come from the same distribution","compare two groups non-normal data","compare two independent groups non-parametric"],patterns:[/mann-whitney\s+u\s+test/i,/compare\s+two\s+independent\s+groups\s+non-parametric/i],suggestions:["NONPAR1","VIZ2"],contextualAdvice:"Mann-Whitney U Test is the non-parametric alternative to the Independent Samples T-Test for comparing two independent groups when data is not normally distributed."},{name:"Wilcoxon Signed-Rank Test",keywords:["compare two related samples, non-parametric","difference in paired observations, non-parametric","non-parametric alternative to paired samples t-test","test for differences between two related samples when data is not normally distributed","pre-post non-normal data","compare population mean non-parametric","compare one group to a median value","compare two related groups non-parametric"],patterns:[/wilcoxon\s+signed-rank\s+test/i,/compare\s+two\s+related\s+samples\s+non-parametric/i,/pre-post\s+non-normal\s+data/i],suggestions:["NONPAR2","VIZ2"],contextualAdvice:"Wilcoxon Signed-Rank Test is the non-parametric alternative to the Paired Samples T-Test for comparing two related samples when data is not normally distributed."},{name:"Two-Way ANOVA",keywords:["analyze the effect of two categorical independent variables on a continuous dependent variable","investigate interaction effects between two factors","compare means across groups defined by two factors","examine the main effects and interaction effects of two independent variables","two factors continuous outcome","interaction effects in two-way ANOVA","analyze effects and interaction of two categorical variables"],patterns:[/two-way\s+anova/i,/effect\s+of\s+two\s+categorical\s+independent\s+variables/i,/interaction\s+effects\s+between\s+two\s+factors/i],suggestions:["ANOVA2","VIZ2"],contextualAdvice:"Two-Way ANOVA examines the main effects of two independent categorical variables and their interaction effect on a continuous dependent variable."},{name:"Repeated Measures ANOVA",keywords:["compare means of three or more related groups","effect of time on a dependent variable within the same subjects","assess changes in performance over multiple trials","test for differences in means across multiple time points for the same subjects","analyze repeated measurements over time","changes over time in the same subjects"],patterns:[/repeated\s+measures\s+anova/i,/effect\s+of\s+time\s+on\s+a\s+dependent\s+variable\s+within\s+the\s+same\s+subjects/i],suggestions:["ANOVA3","VIZ4"],contextualAdvice:"Repeated Measures ANOVA is used to compare means across multiple time points or conditions for the same subjects."},{name:"Friedman Test",keywords:["test for differences among three or more related samples, non-parametric","compare multiple treatments applied to the same subjects, non-parametric","non-parametric alternative to repeated measures ANOVA","assess differences among repeated measurements on the same subjects when data is not normal","compare three or more related groups non-parametric"],patterns:[/friedman\s+test/i,/compare\s+(three|more\s+than\s+two)\s+related\s+samples\s+non-parametric/i],suggestions:["NONPAR4","VIZ2"],contextualAdvice:"Friedman Test is the non-parametric alternative to Repeated Measures ANOVA for comparing three or more related samples when data is not normally distributed."},{name:"Descriptive Statistics",keywords:["summarize central tendency and spread of data","calculate mean, median, mode, standard deviation","understand the distribution of a single variable","get summary statistics for a dataset","explore data patterns","data summary","identify outliers","data transformation","grouped statistics","summarize my data","pivot table"],patterns:[/summarize\s+data/i,/descriptive\s+statistics/i,/calculate\s+(mean|median|mode|standard\s+deviation)/i,/explore\s+data\s+patterns/i],suggestions:["DESC1","DESC2","DESC3","VIZ3","VIZ2","CORR1"],contextualAdvice:"Start with descriptive statistics and visualizations to understand your data distribution, identify outliers, and check for missing values."},{name:"Normality Test",keywords:["check if data follows a normal distribution","assess the gaussian nature of a dataset","determine if a dataset is normally distributed","is my data normal","test normality","test for equal variances","test for normal distribution","check for linearity and homoscedasticity","what to do if my data is not normally distributed","data not normal","non-normal data","nonparametric tests","non-parametric","normal distribution","normally distributed","check normality"],patterns:[/normal\w*\s+distribut/i,/test\w*\s+normal/i,/check\w*\s+normal/i,/is\s+my\s+data\s+normal/i,/non-normal\s+data/i],suggestions:["DESC4","VIZ3","VIZ2","DESC1"],contextualAdvice:"Check normality using statistical tests (Shapiro-Wilk, K-S test) and visual methods (Histogram, Q-Q plot). Consider sample size when interpreting."},{name:"Histogram",keywords:["visualize data distribution","show frequency of data points in bins","display the shape of a continuous variable's distribution","visualize distribution of a single variable"],patterns:[/histogram/i,/visualize\s+data\s+distribution/i,/show\s+frequency\s+of\s+data\s+points/i],suggestions:["VIZ3","DESC1"],contextualAdvice:"Histograms are ideal for visualizing the frequency distribution and shape of a single continuous variable."},{name:"Bar Chart",keywords:["display categories and their counts","compare values across different categories","show the frequency of categorical data","visualize categorical frequency","clustered bar chart","grouped bar chart","show relationships stratified by a third variable","compare counts of categories"],patterns:[/bar\s+chart/i,/display\s+categories\s+and\s+their\s+counts/i,/compare\s+values\s+across\s+different\s+categories/i],suggestions:["VIZ1","DESC2"],contextualAdvice:"Bar charts are best for displaying and comparing counts or values across different categories."},{name:"Pie Chart",keywords:["show parts of a whole","represent proportions of a total","visualize the composition of a single categorical variable","visualize categorical proportions","show proportions of a whole"],patterns:[/pie\s+chart/i,/show\s+parts\s+of\s+a\s+whole/i,/represent\s+proportions\s+of\s+a\s+total/i],suggestions:["VIZ6","DESC2"],contextualAdvice:"Pie charts are used to visualize the composition of a single categorical variable, showing proportions of a total."},{name:"Scatter Plot",keywords:["display relationship between two continuous variables","identify trends or clusters in bivariate data","show the relationship between two quantitative variables","visualize linear relationship with trendline","faceted scatter plot","show relationships stratified by a third variable","plot relationship between two continuous variables"],patterns:[/scatter\s+plot/i,/display\s+relationship\s+between\s+two\s+continuous\s+variables/i,/identify\s+trends\s+or\s+clusters/i],suggestions:["VIZ4","CORR1","REG1"],contextualAdvice:"Scatter plots are excellent for visualizing the relationship between two continuous variables and identifying trends or clusters."},{name:"Box Plot",keywords:["show distribution and outliers for a single variable","compare distributions across different groups","visualize the five-number summary of a dataset","visually compare group variances","stratified box plot","show distributions stratified by a third variable","visualize data spread and outliers"],patterns:[/box\s+plot/i,/show\s+distribution\s+and\s+outliers/i,/compare\s+distributions\s+across\s+different\s+groups/i],suggestions:["VIZ2","DESC1","DESC4"],contextualAdvice:"Box plots summarize the distribution of a variable, showing median, quartiles, and potential outliers, and are useful for comparing distributions across groups."},{name:"Cross-Tabulation",keywords:["examine relationships between two categorical variables","create a contingency table","analyze the relationship between two nominal variables","cross tabulation of categorical data","test association between two categorical variables","measure strength of association for categorical variables","chi-square","contingency table","association categorical","relationship categorical","what test should i use for categorical data","test for categorical data","categorical data test","test categorical","analyze categorical","contingency table analysis"],patterns:[/cross-tabulation/i,/contingency\s+table/i,/relationship\s+between\s+two\s+categorical\s+variables/i,/chi-square/i],suggestions:["DESC3","CAT1","VIZ1"],contextualAdvice:"Cross-tabulation (contingency tables) is used to examine the relationship between two categorical variables, often followed by a Chi-Square test."},{name:"Frequency Tables",keywords:["count occurrences of each category in a variable","summarize qualitative data","get counts and percentages for categorical variables","count frequencies of categories"],patterns:[/frequency\s+tables?/i,/count\s+occurrences\s+of\s+each\s+category/i,/summarize\s+qualitative\s+data/i],suggestions:["DESC2","VIZ1"],contextualAdvice:"Frequency tables provide counts and percentages for each category of a categorical variable."},{name:"Sample Size and Power Estimation",keywords:["how many subjects do I need for my study","calculate required sample size","determine sample size for an experiment","what is the power of my study","calculate statistical power","power estimation for t-test","sample size calculator","sample size","power analysis","how many participants","study planning","calculate n","determine sample size for a t-test","calculate power for an ANOVA"],patterns:[/sample\s+size/i,/power\s+analys/i,/how\s+many\s+(subjects|participants|patients)/i,/calculate\s+required\s+sample\s+size/i],suggestions:["SS1","SS2","SS3","SS4"],contextualAdvice:"Calculate sample size based on your study design, expected effect size, desired power (usually 80%), and significance level (usually 0.05)."},{name:"Epidemiological Calculators",keywords:["epidemiological calculator","2x2 table analysis","calculate odds ratio from a 2x2 table","calculate risk ratio from a 2x2 table","epidemiology","risk ratio","odds ratio","case control","cohort","prevalence","odds ratio","odds ratio for case-control study","risk ratio calculation","relative risk calculation","risk ratio for cohort study","adjusted odds ratio","calculate adjusted OR","control for confounding variables odds ratio","adjusted risk ratio","calculate adjusted RR","control for confounding variables risk ratio","multivariable logistic regression for adjusted odds ratio"],patterns:[/epi-?calculator/i,/2x2\s+table\s+analys/i,/(calculate|what\s+is|interpret)\s+(odds|risk)\s+ratio/i,/adjusted\s+(odds|risk)\s+ratio/i],suggestions:["EPI1","EPI2","EPI3","EPI4","REG2"],contextualAdvice:"Choose the appropriate epidemiological calculator based on your study design (case-control, cohort, cross-sectional) or the measure of association you need (Odds Ratio, Risk Ratio, Adjusted OR/RR)."},{name:"Cross-Sectional Study",keywords:["study at a single point in time","prevalence study","what is a cross-sectional study","study design for disease prevalence"],patterns:[/cross-sectional\s+study/i,/single\s+point\s+in\s+time/i,/prevalence\s+study/i],suggestions:["EPI3","DESC1","DESC2"],contextualAdvice:"Cross-sectional studies assess prevalence at a single point in time. Use the Cross-Sectional Calculator for analysis."},{name:"Cohort Study",keywords:["follow a group over time","prospective study design","retrospective cohort study","incidence study","longitudinal study","longitudinal vs cohort","study design for disease incidence"],patterns:[/cohort\s+study/i,/follow\s+a\s+group\s+over\s+time/i,/prospective\s+study\s+design/i,/incidence\s+study/i],suggestions:["EPI2","ANOVA3","ADV4"],contextualAdvice:"Cohort studies follow groups over time to assess incidence and risk. Use the Cohort Calculator for analysis."},{name:"Case-Control Study",keywords:["compare people with and without a disease","study to find risk factors for a condition","retrospective study design","odds ratio","odds ratio for case-control study","study design for risk factors for a rare disease"],patterns:[/case-control\s+study/i,/compare\s+people\s+with\s+and\s+without\s+a\s+disease/i,/risk\s+factors\s+for\s+a\s+condition/i],suggestions:["EPI1","REG2"],contextualAdvice:"Case-control studies are retrospective and compare cases (with disease) to controls (without disease) to identify risk factors. Use the Case-Control Calculator for odds ratios."},{name:"Matched Case-Control Study",keywords:["matched case-control study analysis","analysis for matched pairs","conditional logistic regression","odds ratio for matched pairs"],patterns:[/matched\s+case-control\s+study/i,/analysis\s+for\s+matched\s+pairs/i,/conditional\s+logistic\s+regression/i],suggestions:["EPI4","TTEST3"],contextualAdvice:"Matched case-control studies involve pairing cases and controls. Use the Matched Case-Control Calculator for analysis, often involving McNemar's test or conditional logistic regression."},{name:"Survival Analysis",keywords:["analyze time to event data","Kaplan-Meier curve","Cox proportional hazards model","predict survival time","compare survival rates between groups","hazard ratio calculation","time to relapse analysis","event history analysis","survival","time to event","kaplan meier","cox regression","censored data"],patterns:[/survival\s+analys/i,/kaplan-meier/i,/cox\s+regression/i,/time\s+to\s+event\s+data/i],suggestions:["ADV4","VIZ4"],contextualAdvice:"Survival analysis handles time-to-event data with censoring. Use Kaplan-Meier for descriptive analysis and Cox regression for modeling."},{name:"Mediation Analysis",keywords:["test for mediation effect","indirect effect analysis","does a third variable explain the relationship"],patterns:[/mediation\s+effect/i,/indirect\s+effect/i,/third\s+variable\s+explain/i],suggestions:["ADV2","REG1"],contextualAdvice:"Mediation analysis examines if the relationship between two variables is explained by a third, mediating variable."},{name:"Moderation Analysis",keywords:["test for moderation effect","does a third variable change the relationship","interaction effect analysis","conditional effect analysis"],patterns:[/moderation\s+effect/i,/third\s+variable\s+change/i,/interaction\s+effect/i],suggestions:["ADV2","ANOVA2"],contextualAdvice:"Moderation analysis investigates if the relationship between two variables changes depending on the level of a third, moderating variable."},{name:"Exploratory Factor Analysis",keywords:["reduce dimensionality of data","identify underlying constructs","factor analysis","group correlated variables"],patterns:[/exploratory\s+factor\s+analys/i,/reduce\s+dimensionality/i,/underlying\s+constructs/i],suggestions:["ADV1"],contextualAdvice:"Exploratory Factor Analysis (EFA) is used to identify underlying factors or constructs that explain the correlations among a set of observed variables."},{name:"Meta-Analysis",keywords:["combine results from multiple studies","synthesize research findings","calculate pooled effect size","forest plot"],patterns:[/meta-analys/i,/combine\s+results\s+from\s+multiple\s+studies/i,/synthesize\s+research\s+findings/i],suggestions:["ADV6"],contextualAdvice:"Meta-analysis systematically combines results from multiple independent studies to derive a single pooled estimate of effect."},{name:"Cluster Analysis",keywords:["group similar observations","segment customers","identify natural groupings in data","k-means clustering","hierarchical clustering"],patterns:[/cluster\s+analys/i,/group\s+similar\s+observations/i,/identify\s+natural\s+groupings/i],suggestions:["ADV5"],contextualAdvice:"Cluster analysis groups similar data points together, identifying natural groupings or segments within a dataset."},{name:"Reliability Analysis",keywords:["assess internal consistency of a scale","Cronbach's Alpha","test questionnaire reliability","reliability","cronbach","internal consistency","scale validation","questionnaire"],patterns:[/reliabilit\w*\s+analys/i,/cronbach's\s+alpha/i,/internal\s+consistency/i],suggestions:["ADV3","CORR1"],contextualAdvice:"Reliability analysis, often using Cronbach's alpha, assesses the internal consistency of a scale or questionnaire."},{name:"Table 1",keywords:["create a table of baseline characteristics","summarize demographic data","descriptive table for study participants","table 1","publication","manuscript","baseline characteristics","demographics table"],patterns:[/table\s+1/i,/baseline\s+characteristics/i,/summarize\s+demographic\s+data/i],suggestions:["PUB1","DESC1","DESC2"],contextualAdvice:"Table 1 typically presents the baseline characteristics of study participants, summarizing demographic and clinical data."},{name:"Table 2",keywords:["create a comparative table","compare variables between groups in a table","show differences in outcomes by exposure","table 2","outcomes","results","comparison","primary endpoint"],patterns:[/table\s+2/i,/compare\s+variables\s+between\s+groups\s+in\s+a\s+table/i],suggestions:["PUB2","TTEST2","ANOVA1"],contextualAdvice:"Table 2 often presents comparative outcomes or results between different groups, such as treatment vs. control."},{name:"Regression Interpretation",keywords:["interpret regression coefficients","explain regression output","understand p-values in regression"],patterns:[/interpret\s+regression\s+coefficients/i,/explain\s+regression\s+output/i],suggestions:["PUB4","REG1","REG2"],contextualAdvice:"Regression interpretation involves understanding the meaning of coefficients, p-values, and model fit statistics."},{name:"Flow Diagram",keywords:["create a study flow diagram","visualize participant flow","CONSORT diagram"],patterns:[/flow\s+diagram/i,/study\s+flow\s+diagram/i,/consort\s+diagram/i],suggestions:["PUB5"],contextualAdvice:"A flow diagram, like a CONSORT diagram, visually represents the flow of participants through a study."},{name:"SMD Table",keywords:["calculate standardized mean differences","create a table of SMDs","compare effect sizes between groups"],patterns:[/smd\s+table/i,/standardized\s+mean\s+differences/i],suggestions:["PUB3"],contextualAdvice:"An SMD table presents standardized mean differences, useful for comparing effect sizes across different studies or groups."}];D.useMemo(()=>Array.from(new Set(X.map(c=>c.category))).sort(),[]),D.useMemo(()=>Array.from(new Set(X.map(c=>c.type))).sort(),[]);const Ne=c=>{const N=Bt.findMatchingSuggestions(c);if(N.length>0){const j=[];return N.forEach(E=>{E.suggestions.forEach(A=>{const R=X.find(M=>M.id===A.analysisId);R&&j.push({...R,relevanceScore:Math.round(A.confidence*100),reason:A.reason,matchDetails:{scenarioMatch:!0,patternMatch:!0,keywordMatches:E.keywords.length,directMatches:0}})})}),j.reduce((E,A)=>{const R=E.find(M=>M.id===A.id);return!R||(A.relevanceScore||0)>(R.relevanceScore||0)?[...E.filter(M=>M.id!==A.id),A]:E},[]).sort((E,A)=>(A.relevanceScore||0)-(E.relevanceScore||0)).slice(0,8)}return nt(c)},nt=c=>{const N=c.toLowerCase().trim(),j=new Map;rt.forEach(A=>{var ae;let R=0,M=0,F=!1;A.keywords.forEach(G=>{N.includes(G)&&(R+=3,M++)}),(ae=A.patterns)==null||ae.forEach(G=>{G.test(c)&&(R+=5,F=!0)}),R>0&&A.suggestions.forEach(G=>{const se=X.find(oe=>oe.id===G);if(se){const oe=j.get(G);(!oe||oe.score<R)&&j.set(G,{...se,score:R,matchDetails:{scenarioMatch:!0,patternMatch:F,keywordMatches:M,directMatches:0},reason:A.contextualAdvice})}})});const Me=N.split(/\s+/).filter(A=>A.length>1);X.forEach(A=>{let R=0,M=0;if(Me.forEach(F=>{var ae,G;A.text.toLowerCase().includes(F)&&(R+=3,M++),(ae=A.description)!=null&&ae.toLowerCase().includes(F)&&(R+=2,M++),(G=A.tags)!=null&&G.some(se=>se.toLowerCase()===F||se.toLowerCase().includes(F))&&(R+=4,M++)}),R>0){const F=j.get(A.id);(!F||F.score<R)&&j.set(A.id,{...A,score:R,matchDetails:{scenarioMatch:!1,patternMatch:!1,keywordMatches:0,directMatches:M}})}});const E=Math.max(...Array.from(j.values()).map(A=>A.score));return Array.from(j.values()).map(A=>({...A,relevanceScore:E>0?Math.round(A.score/E*100):0})).filter(A=>A.relevanceScore>=50).sort((A,R)=>R.score-A.score).slice(0,10)},Ie=D.useMemo(()=>{let c=[...X];return n.length>0&&(c=c.filter(N=>n.includes(N.category))),h.length>0&&(c=c.filter(N=>h.includes(N.type))),c},[n,h,S,r]),ot=D.useMemo(()=>{const c={};return(Y?Ie:$).forEach(j=>{c[j.category]||(c[j.category]=[]),c[j.category].push(j)}),c},[Ie,$,Y]),Te=()=>{a.trim()&&(O(!0),Ae(!1),setTimeout(()=>{let c=[];r&&(w!=null&&w.hasData)?(c=_e(r,a).map(j=>({id:j.id,text:j.title,description:j.description,type:j.analysisType,category:j.category,path:j.path,reason:j.reason,relevanceScore:j.priority==="high"?90:j.priority==="medium"?70:50})),c.length===0&&(c=Ne(a))):c=Ne(a),je(c),O(!1),c.length===0&&f({open:!0,message:r?"No specific matches found for your dataset. Try browsing all analyses or rephrasing your question.":"No specific matches found. Try browsing all analyses or rephrasing your question."})},800))},lt=["How do I compare means between two groups?","Is my data normally distributed?","What test for categorical data?","Compare pre and post measurements","Calculate sample size for my study","Create publication-ready tables","Analyze survival data","Test correlation between variables"],ze=c=>{switch(c){case"Descriptive Analysis":return e.jsx(xe,{});case"T-Tests":return e.jsx(Ue,{});case"Non-parametric Tests":return e.jsx(We,{});case"ANOVA":return e.jsx(Ze,{});case"Categorical Tests":return e.jsx(Be,{});case"Correlation & Regression":return e.jsx(Ze,{});case"Advanced Methods":return e.jsx(Rt,{});case"Epidemiology":return e.jsx(We,{});case"Sample Size":return e.jsx(Ue,{});case"Visualizations":return e.jsx(Vt,{});case"Data Management":return e.jsx(Dt,{});case"Publication Tools":return e.jsx(Be,{});default:return e.jsx(xe,{})}},De=c=>{switch(c){case"test":return"primary";case"descriptive":return"success";case"visualization":return"warning";case"regression":return"secondary";case"categorical":return"info";case"assumption":return"default";case"advanced":return"error";case"epidemiology":return"primary";case"sample_size":return"secondary";case"data":return"default";case"publication":return"info";case"correlation":return"secondary";default:return"default"}},Ve=c=>{t&&c.path?t(c.path):f({open:!0,message:`Selected: ${c.text}`})},ct=c=>{s(N=>N.includes(c)?N.filter(j=>j!==c):[...N,c])},dt=c=>c>=90?5:c>=70?4:c>=50?3:c>=30?2:1,mt=c=>e.jsx(ie,{variant:"outlined",sx:{mb:2,transition:"all 0.2s",borderColor:c.relevanceScore&&c.relevanceScore>70?d.palette.primary.main:d.palette.divider,"&:hover":{transform:"translateY(-2px)",boxShadow:2}},children:e.jsx(Nt,{onClick:()=>Ve(c),children:e.jsxs(be,{children:[e.jsxs(b,{sx:{display:"flex",alignItems:"flex-start",mb:1},children:[e.jsx(It,{sx:{bgcolor:U(d.palette.primary.main,.1),width:36,height:36,mr:2},children:ze(c.category)}),e.jsxs(b,{sx:{flexGrow:1},children:[e.jsx(u,{variant:"h6",sx:{fontSize:"1.1rem",mb:.5},children:c.text}),e.jsx(u,{variant:"body2",color:"text.secondary",children:c.description}),c.reason&&e.jsx(k,{severity:"info",sx:{mt:1,py:.5,"& .MuiAlert-message":{fontSize:"0.875rem"}},icon:e.jsx(Tt,{fontSize:"small"}),children:c.reason})]}),c.relevanceScore!==void 0&&e.jsxs(b,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[e.jsx(zt,{value:dt(c.relevanceScore),max:5,readOnly:!0,size:"small",icon:e.jsx(Le,{fontSize:"inherit"}),emptyIcon:e.jsx(Le,{fontSize:"inherit",sx:{opacity:.3}})}),e.jsxs(u,{variant:"caption",color:"text.secondary",sx:{mt:.5},children:[c.relevanceScore,"% match"]}),c.matchDetails&&e.jsxs(u,{variant:"caption",color:"text.secondary",sx:{fontSize:"0.7rem"},children:[c.matchDetails.scenarioMatch&&"✓ Scenario",c.matchDetails.patternMatch&&" • Pattern",c.matchDetails.keywordMatches>0&&` • ${c.matchDetails.keywordMatches} keyword${c.matchDetails.keywordMatches>1?"s":""}`,c.matchDetails.directMatches>0&&` • ${c.matchDetails.directMatches} direct`]})]})]}),e.jsxs(b,{sx:{display:"flex",gap:.5,mt:2},children:[e.jsx(T,{label:c.type,size:"small",color:De(c.type),variant:"outlined"}),e.jsx(T,{label:c.category,size:"small",sx:{bgcolor:U(d.palette.primary.main,.1)}})]})]})})},c.id);return e.jsxs(b,{sx:{position:"relative",minHeight:"600px"},children:[e.jsx(xt,{open:p.open,autoHideDuration:6e3,onClose:()=>f({...p,open:!1}),message:p.message,anchorOrigin:{vertical:"bottom",horizontal:"center"}}),e.jsxs(te,{elevation:0,sx:{p:3,mb:3},children:[e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:3},children:[e.jsx(re,{color:"primary",sx:{mr:1,fontSize:28}}),e.jsx(u,{variant:"h6",children:"Analysis Assistant"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsxs(bt,{value:l,exclusive:!0,onChange:(c,N)=>N&&m(N),size:"small",children:[e.jsxs($e,{value:"assistant",children:[e.jsx(le,{sx:{mr:.5}}),"AI Analysis Assistant"]}),e.jsxs($e,{value:"quality",children:[e.jsx(re,{sx:{mr:.5}}),"AI Data Quality Assistant",r&&(w==null?void 0:w.qualityAssessment)&&e.jsx(vt,{badgeContent:"AI",color:"primary",sx:{ml:1,"& .MuiBadge-badge":{fontSize:"0.6rem",height:"16px",minWidth:"16px"}}})]})]})]}),l==="assistant"?e.jsxs(e.Fragment,{children:[e.jsx(u,{variant:"body2",color:"text.secondary",paragraph:!0,children:r&&(w!=null&&w.hasData)?`Describe your research question about "${r.name}" and I'll suggest the most appropriate analyses based on your actual data.`:"Describe your research question or analysis needs in plain language, and I'll suggest the most appropriate statistical tests and tools."}),e.jsxs(k,{severity:"info",icon:e.jsx(ne,{}),sx:{mb:3},children:[e.jsxs(u,{variant:"subtitle2",sx:{mb:1},children:["💡 ",r?"Ask about your data":"Pro tip: Be specific about your data and goals"]}),e.jsx(u,{variant:"body2",children:r&&(w!=null&&w.hasData)?e.jsxs(e.Fragment,{children:['Try asking: "Compare ',((Re=w.variableAnalysis.find(c=>c.type==="numeric"))==null?void 0:Re.columnName)||"numeric variables",` between groups" or "What's the relationship between `,w.variableAnalysis.slice(0,2).map(c=>c.columnName).join(" and "),'?"']}):e.jsx(e.Fragment,{children:'For example: "I want to compare blood pressure between treatment and control groups" or "How do I test if age is correlated with test scores?"'})})]}),e.jsxs(b,{sx:{display:"flex",mb:3},children:[e.jsx(wt,{fullWidth:!0,variant:"outlined",placeholder:"Describe what you want to analyze...",value:a,onChange:c=>i(c.target.value),onKeyPress:c=>{c.key==="Enter"&&Te()},disabled:V,InputProps:{startAdornment:e.jsx(Fe,{position:"start",children:e.jsx(re,{color:"primary"})}),endAdornment:a&&e.jsx(Fe,{position:"end",children:e.jsx(jt,{size:"small",onClick:()=>{i(""),je([])},edge:"end",children:e.jsx(At,{})})})}}),e.jsx(ee,{variant:"contained",color:"primary",onClick:Te,disabled:V||!a.trim(),sx:{ml:1,minWidth:120},startIcon:V?e.jsx(Ge,{size:20,color:"inherit"}):e.jsx(le,{}),children:V?"Analyzing...":"Analyze"})]}),!$.length&&!V&&e.jsxs(b,{sx:{mb:4},children:[e.jsx(u,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Try asking:"}),e.jsx(b,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:lt.map((c,N)=>e.jsx(T,{label:c,onClick:()=>i(c),variant:"outlined",clickable:!0,icon:e.jsx(Ct,{})},N))})]}),$.length>0&&e.jsxs(b,{children:[e.jsx(ve,{sx:{mb:3}}),e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(le,{color:"primary",sx:{mr:1}}),e.jsx(u,{variant:"h6",children:"Recommended Analyses"}),e.jsx(b,{sx:{flexGrow:1}}),e.jsx(ee,{size:"small",onClick:()=>Ae(!Y),endIcon:Y?e.jsx(Ke,{}):e.jsx(L,{}),children:Y?"Show Less":"Show All"})]}),Y?Object.entries(ot).map(([c,N])=>e.jsxs(Z,{expanded:g.includes(c),onChange:()=>ct(c),sx:{mb:1},children:[e.jsx(_,{expandIcon:e.jsx(L,{}),children:e.jsxs(b,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[ze(c),e.jsx(u,{sx:{ml:1,fontWeight:500},children:c}),e.jsx(T,{label:N.length,size:"small",sx:{ml:2}})]})}),e.jsx(Q,{children:e.jsx(H,{sx:{pt:0},children:N.map(j=>e.jsxs(q,{button:!0,onClick:()=>Ve(j),sx:{mb:1,borderRadius:1,"&:hover":{backgroundColor:U(d.palette.primary.main,.05)}},children:[e.jsx(B,{primary:j.text,secondary:j.description,primaryTypographyProps:{fontSize:"0.9rem"},secondaryTypographyProps:{fontSize:"0.8rem"}}),e.jsx(T,{label:j.type,size:"small",color:De(j.type),variant:"outlined"})]},j.id))})})]},c)):$.map(mt)]})]}):l==="quality"?e.jsx(e.Fragment,{children:r&&(w!=null&&w.qualityAssessment)?e.jsx(oa,{assessment:w.qualityAssessment,datasetName:r.name}):e.jsxs(b,{sx:{textAlign:"center",py:6},children:[e.jsx(re,{sx:{fontSize:80,color:"primary.main",mb:2,opacity:.7}}),e.jsx(u,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"AI Data Quality Assistant"}),e.jsx(u,{variant:"body1",color:"text.secondary",sx:{mb:3,maxWidth:600,mx:"auto"},children:"Get intelligent insights about your data quality with AI-powered analysis"}),r?e.jsxs(ie,{sx:{maxWidth:500,mx:"auto",p:3,bgcolor:"info.50"},children:[e.jsxs(u,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"center"},children:[e.jsx(Ge,{size:20,sx:{mr:1}}),"Analyzing Your Data"]}),e.jsxs(u,{variant:"body2",color:"text.secondary",children:['The AI is processing "',r.name,'" to generate comprehensive data quality insights. This may take a moment for large datasets.']})]}):e.jsxs(ie,{sx:{maxWidth:500,mx:"auto",p:3,bgcolor:"grey.50"},children:[e.jsxs(u,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"center"},children:[e.jsx(ne,{sx:{mr:1,color:"warning.main"}}),"Load a Dataset to Get Started"]}),e.jsx(u,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"To use the AI Data Quality Assistant, you need to load a dataset first. Once loaded, the AI will analyze:"}),e.jsxs(H,{dense:!0,sx:{textAlign:"left"},children:[e.jsxs(q,{children:[e.jsx(W,{children:e.jsx(K,{color:"primary",fontSize:"small"})}),e.jsx(B,{primary:"Missing data patterns and recommendations"})]}),e.jsxs(q,{children:[e.jsx(W,{children:e.jsx(K,{color:"primary",fontSize:"small"})}),e.jsx(B,{primary:"Outlier detection and handling suggestions"})]}),e.jsxs(q,{children:[e.jsx(W,{children:e.jsx(K,{color:"primary",fontSize:"small"})}),e.jsx(B,{primary:"Data distribution assessment"})]}),e.jsxs(q,{children:[e.jsx(W,{children:e.jsx(K,{color:"primary",fontSize:"small"})}),e.jsx(B,{primary:"Variable type validation"})]}),e.jsxs(q,{children:[e.jsx(W,{children:e.jsx(K,{color:"primary",fontSize:"small"})}),e.jsx(B,{primary:"Data improvement recommendations"})]})]}),e.jsx(ee,{variant:"contained",size:"large",onClick:()=>t==null?void 0:t("data-management/datasets"),sx:{mt:2},startIcon:e.jsx(St,{}),children:"Go to Datasets"})]})]})}):null,r&&(w!=null&&w.hasData)?e.jsxs(b,{sx:{mt:3},children:[e.jsxs(k,{severity:"success",sx:{mb:2},children:[e.jsxs(u,{variant:"subtitle2",sx:{mb:1},children:["📊 Dataset Loaded: ",r.name]}),e.jsxs(u,{variant:"body2",children:[w.totalRows," observations • ",w.totalColumns," variables",w.variableTypes.numeric>0&&` • ${w.variableTypes.numeric} numeric`,w.variableTypes.categorical>0&&` • ${w.variableTypes.categorical} categorical`,w.qualityAssessment&&e.jsxs(e.Fragment,{children:[" • ",e.jsx(T,{label:`Quality: ${w.qualityAssessment.overallGrade.toUpperCase()}`,size:"small",color:w.qualityAssessment.overallGrade==="excellent"?"success":w.qualityAssessment.overallGrade==="good"?"info":w.qualityAssessment.overallGrade==="fair"?"warning":"error",sx:{ml:1}})]})]})]}),w.qualityAssessment&&w.qualityAssessment.prioritizedRecommendations.some(c=>c.priority==="critical")&&e.jsxs(k,{severity:"error",sx:{mb:2},children:[e.jsx(u,{variant:"subtitle2",sx:{mb:1},children:"🚨 Critical Data Quality Issues"}),e.jsxs(u,{variant:"body2",children:[w.qualityAssessment.prioritizedRecommendations.filter(c=>c.priority==="critical").length," critical issues detected.",e.jsx(ee,{size:"small",onClick:()=>m("quality"),sx:{ml:1},children:"View Details"})]})]}),w.dataQuality.missingDataPercentage>5&&!((ke=w.qualityAssessment)!=null&&ke.prioritizedRecommendations.some(c=>c.priority==="critical"))&&e.jsxs(k,{severity:"warning",sx:{mb:2},children:[e.jsx(u,{variant:"subtitle2",sx:{mb:1},children:"⚠️ Data Quality Notice"}),e.jsxs(u,{variant:"body2",children:[w.dataQuality.missingDataPercentage.toFixed(1),"% missing data detected. Consider data cleaning before analysis.",w.qualityAssessment&&e.jsx(ee,{size:"small",onClick:()=>m("quality"),sx:{ml:1},children:"View Assessment"})]})]}),me.length>0&&e.jsxs(k,{severity:"info",sx:{mb:2},children:[e.jsx(u,{variant:"subtitle2",sx:{mb:1},children:"💡 Data Insights"}),me.slice(0,2).map((c,N)=>e.jsxs(u,{variant:"body2",sx:{mb:N<me.slice(0,2).length-1?1:0},children:["• ",c]},N))]}),Se.length>0&&l==="assistant"&&!a&&!$.length&&e.jsxs(b,{sx:{mt:2},children:[e.jsxs(u,{variant:"subtitle1",sx:{mb:2,display:"flex",alignItems:"center"},children:[e.jsx(le,{sx:{mr:1}}),"Recommended for Your Data"]}),e.jsx(P,{container:!0,spacing:2,children:Se.slice(0,4).map(c=>e.jsx(P,{item:!0,xs:12,sm:6,children:e.jsx(ie,{variant:"outlined",sx:{height:"100%",cursor:"pointer",transition:"all 0.2s",borderColor:c.priority==="high"?d.palette.primary.main:d.palette.divider,"&:hover":{transform:"translateY(-2px)",boxShadow:2}},onClick:()=>c.path&&(t==null?void 0:t(c.path)),children:e.jsxs(be,{children:[e.jsxs(b,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(T,{label:c.priority.toUpperCase(),size:"small",color:c.priority==="high"?"primary":c.priority==="medium"?"secondary":"default",sx:{mr:1}}),e.jsx(u,{variant:"caption",color:"text.secondary",children:c.category})]}),e.jsx(u,{variant:"h6",sx:{fontSize:"1rem",mb:1},children:c.title}),e.jsx(u,{variant:"body2",color:"text.secondary",sx:{mb:1},children:c.description}),e.jsx(u,{variant:"caption",color:"primary",children:c.reason})]})})},c.id))})]})]}):e.jsx(k,{severity:"info",sx:{mt:3},children:"No dataset loaded. You can still explore available analyses and plan your study."})]})]})},ja=({onNavigate:t})=>e.jsx(b,{sx:{p:3},children:e.jsx(la,{onNavigate:t})});export{ja as default};
