import{e as Gn,g as Vn}from"./react-libs-Cr2nE3UY.js";let Ne;const Wi=new Uint8Array(16);function Yi(){if(!Ne&&(Ne=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Ne))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Ne(Wi)}const gA=[];for(let t=0;t<256;++t)gA.push((t+256).toString(16).slice(1));function Zi(t,e=0){return gA[t[e+0]]+gA[t[e+1]]+gA[t[e+2]]+gA[t[e+3]]+"-"+gA[t[e+4]]+gA[t[e+5]]+"-"+gA[t[e+6]]+gA[t[e+7]]+"-"+gA[t[e+8]]+gA[t[e+9]]+"-"+gA[t[e+10]]+gA[t[e+11]]+gA[t[e+12]]+gA[t[e+13]]+gA[t[e+14]]+gA[t[e+15]]}const qi=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),_t={randomUUID:qi};function yf(t,e,n){if(_t.randomUUID&&!t)return _t.randomUUID();t=t||{};const A=t.random||(t.rng||Yi)();return A[6]=A[6]&15|64,A[8]=A[8]&63|128,Zi(A)}var Nn={exports:{}};/* @license
Papa Parse
v5.5.2
https://github.com/mholt/PapaParse
License: MIT
*/(function(t,e){((n,A)=>{t.exports=A()})(Gn,function n(){var A=typeof self<"u"?self:typeof window<"u"?window:A!==void 0?A:{},i,u=!A.document&&!!A.postMessage,f=A.IS_PAPA_WORKER||!1,l={},c=0,o={};function r(F){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(p){var m=v(p);m.chunkSize=parseInt(m.chunkSize),p.step||p.chunk||(m.chunkSize=null),this._handle=new w(m),(this._handle.streamer=this)._config=m}).call(this,F),this.parseChunk=function(p,m){var I=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&0<I){let K=this._config.newline;K||(y=this._config.quoteChar||'"',K=this._handle.guessLineEndings(p,y)),p=[...p.split(K).slice(I)].join(K)}this.isFirstChunk&&H(this._config.beforeFirstChunk)&&(y=this._config.beforeFirstChunk(p))!==void 0&&(p=y),this.isFirstChunk=!1,this._halted=!1;var I=this._partialLine+p,y=(this._partialLine="",this._handle.parse(I,this._baseIndex,!this._finished));if(!this._handle.paused()&&!this._handle.aborted()){if(p=y.meta.cursor,I=(this._finished||(this._partialLine=I.substring(p-this._baseIndex),this._baseIndex=p),y&&y.data&&(this._rowCount+=y.data.length),this._finished||this._config.preview&&this._rowCount>=this._config.preview),f)A.postMessage({results:y,workerId:o.WORKER_ID,finished:I});else if(H(this._config.chunk)&&!m){if(this._config.chunk(y,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);this._completeResults=y=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(y.data),this._completeResults.errors=this._completeResults.errors.concat(y.errors),this._completeResults.meta=y.meta),this._completed||!I||!H(this._config.complete)||y&&y.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),I||y&&y.meta.paused||this._nextChunk(),y}this._halted=!0},this._sendError=function(p){H(this._config.error)?this._config.error(p):f&&this._config.error&&A.postMessage({workerId:o.WORKER_ID,error:p,finished:!1})}}function s(F){var p;(F=F||{}).chunkSize||(F.chunkSize=o.RemoteChunkSize),r.call(this,F),this._nextChunk=u?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(m){this._input=m,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(p=new XMLHttpRequest,this._config.withCredentials&&(p.withCredentials=this._config.withCredentials),u||(p.onload=E(this._chunkLoaded,this),p.onerror=E(this._chunkError,this)),p.open(this._config.downloadRequestBody?"POST":"GET",this._input,!u),this._config.downloadRequestHeaders){var m,I=this._config.downloadRequestHeaders;for(m in I)p.setRequestHeader(m,I[m])}var y;this._config.chunkSize&&(y=this._start+this._config.chunkSize-1,p.setRequestHeader("Range","bytes="+this._start+"-"+y));try{p.send(this._config.downloadRequestBody)}catch(K){this._chunkError(K.message)}u&&p.status===0&&this._chunkError()}},this._chunkLoaded=function(){p.readyState===4&&(p.status<200||400<=p.status?this._chunkError():(this._start+=this._config.chunkSize||p.responseText.length,this._finished=!this._config.chunkSize||this._start>=(m=>(m=m.getResponseHeader("Content-Range"))!==null?parseInt(m.substring(m.lastIndexOf("/")+1)):-1)(p),this.parseChunk(p.responseText)))},this._chunkError=function(m){m=p.statusText||m,this._sendError(new Error(m))}}function B(F){(F=F||{}).chunkSize||(F.chunkSize=o.LocalChunkSize),r.call(this,F);var p,m,I=typeof FileReader<"u";this.stream=function(y){this._input=y,m=y.slice||y.webkitSlice||y.mozSlice,I?((p=new FileReader).onload=E(this._chunkLoaded,this),p.onerror=E(this._chunkError,this)):p=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var y=this._input,K=(this._config.chunkSize&&(K=Math.min(this._start+this._config.chunkSize,this._input.size),y=m.call(y,this._start,K)),p.readAsText(y,this._config.encoding));I||this._chunkLoaded({target:{result:K}})},this._chunkLoaded=function(y){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(y.target.result)},this._chunkError=function(){this._sendError(p.error)}}function a(F){var p;r.call(this,F=F||{}),this.stream=function(m){return p=m,this._nextChunk()},this._nextChunk=function(){var m,I;if(!this._finished)return m=this._config.chunkSize,p=m?(I=p.substring(0,m),p.substring(m)):(I=p,""),this._finished=!p,this.parseChunk(I)}}function g(F){r.call(this,F=F||{});var p=[],m=!0,I=!1;this.pause=function(){r.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){r.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(y){this._input=y,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){I&&p.length===1&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),p.length?this.parseChunk(p.shift()):m=!0},this._streamData=E(function(y){try{p.push(typeof y=="string"?y:y.toString(this._config.encoding)),m&&(m=!1,this._checkIsFinished(),this.parseChunk(p.shift()))}catch(K){this._streamError(K)}},this),this._streamError=E(function(y){this._streamCleanUp(),this._sendError(y)},this),this._streamEnd=E(function(){this._streamCleanUp(),I=!0,this._streamData("")},this),this._streamCleanUp=E(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function w(F){var p,m,I,y,K=Math.pow(2,53),_=-K,$=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,W=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,D=this,N=0,T=0,k=!1,O=!1,R=[],L={data:[],errors:[],meta:{}};function M(J){return F.skipEmptyLines==="greedy"?J.join("").trim()==="":J.length===1&&J[0].length===0}function P(){if(L&&I&&(rA("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+o.DefaultDelimiter+"'"),I=!1),F.skipEmptyLines&&(L.data=L.data.filter(function(G){return!M(G)})),q()){let G=function(sA,uA){H(F.transformHeader)&&(sA=F.transformHeader(sA,uA)),R.push(sA)};if(L)if(Array.isArray(L.data[0])){for(var J=0;q()&&J<L.data.length;J++)L.data[J].forEach(G);L.data.splice(0,1)}else L.data.forEach(G)}function Y(G,sA){for(var uA=F.header?{}:[],j=0;j<G.length;j++){var AA=j,X=G[j],X=((yA,V)=>(nA=>(F.dynamicTypingFunction&&F.dynamicTyping[nA]===void 0&&(F.dynamicTyping[nA]=F.dynamicTypingFunction(nA)),(F.dynamicTyping[nA]||F.dynamicTyping)===!0))(yA)?V==="true"||V==="TRUE"||V!=="false"&&V!=="FALSE"&&((nA=>{if($.test(nA)&&(nA=parseFloat(nA),_<nA&&nA<K))return 1})(V)?parseFloat(V):W.test(V)?new Date(V):V===""?null:V):V)(AA=F.header?j>=R.length?"__parsed_extra":R[j]:AA,X=F.transform?F.transform(X,AA):X);AA==="__parsed_extra"?(uA[AA]=uA[AA]||[],uA[AA].push(X)):uA[AA]=X}return F.header&&(j>R.length?rA("FieldMismatch","TooManyFields","Too many fields: expected "+R.length+" fields but parsed "+j,T+sA):j<R.length&&rA("FieldMismatch","TooFewFields","Too few fields: expected "+R.length+" fields but parsed "+j,T+sA)),uA}var iA;L&&(F.header||F.dynamicTyping||F.transform)&&(iA=1,!L.data.length||Array.isArray(L.data[0])?(L.data=L.data.map(Y),iA=L.data.length):L.data=Y(L.data,0),F.header&&L.meta&&(L.meta.fields=R),T+=iA)}function q(){return F.header&&R.length===0}function rA(J,Y,iA,G){J={type:J,code:Y,message:iA},G!==void 0&&(J.row=G),L.errors.push(J)}H(F.step)&&(y=F.step,F.step=function(J){L=J,q()?P():(P(),L.data.length!==0&&(N+=J.data.length,F.preview&&N>F.preview?m.abort():(L.data=L.data[0],y(L,D))))}),this.parse=function(J,Y,iA){var G=F.quoteChar||'"',G=(F.newline||(F.newline=this.guessLineEndings(J,G)),I=!1,F.delimiter?H(F.delimiter)&&(F.delimiter=F.delimiter(J),L.meta.delimiter=F.delimiter):((G=((sA,uA,j,AA,X)=>{var yA,V,nA,PA;X=X||[",","	","|",";",o.RECORD_SEP,o.UNIT_SEP];for(var te=0;te<X.length;te++){for(var bA,Ce=X[te],CA=0,xA=0,cA=0,FA=(nA=void 0,new U({comments:AA,delimiter:Ce,newline:uA,preview:10}).parse(sA)),_A=0;_A<FA.data.length;_A++)j&&M(FA.data[_A])?cA++:(bA=FA.data[_A].length,xA+=bA,nA===void 0?nA=bA:0<bA&&(CA+=Math.abs(bA-nA),nA=bA));0<FA.data.length&&(xA/=FA.data.length-cA),(V===void 0||CA<=V)&&(PA===void 0||PA<xA)&&1.99<xA&&(V=CA,yA=Ce,PA=xA)}return{successful:!!(F.delimiter=yA),bestDelimiter:yA}})(J,F.newline,F.skipEmptyLines,F.comments,F.delimitersToGuess)).successful?F.delimiter=G.bestDelimiter:(I=!0,F.delimiter=o.DefaultDelimiter),L.meta.delimiter=F.delimiter),v(F));return F.preview&&F.header&&G.preview++,p=J,m=new U(G),L=m.parse(p,Y,iA),P(),k?{meta:{paused:!0}}:L||{meta:{paused:!1}}},this.paused=function(){return k},this.pause=function(){k=!0,m.abort(),p=H(F.chunk)?"":p.substring(m.getCharIndex())},this.resume=function(){D.streamer._halted?(k=!1,D.streamer.parseChunk(p,!0)):setTimeout(D.resume,3)},this.aborted=function(){return O},this.abort=function(){O=!0,m.abort(),L.meta.aborted=!0,H(F.complete)&&F.complete(L),p=""},this.guessLineEndings=function(sA,G){sA=sA.substring(0,1048576);var G=new RegExp(Q(G)+"([^]*?)"+Q(G),"gm"),iA=(sA=sA.replace(G,"")).split("\r"),G=sA.split(`
`),sA=1<G.length&&G[0].length<iA[0].length;if(iA.length===1||sA)return`
`;for(var uA=0,j=0;j<iA.length;j++)iA[j][0]===`
`&&uA++;return uA>=iA.length/2?`\r
`:"\r"}}function Q(F){return F.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function U(F){var p=(F=F||{}).delimiter,m=F.newline,I=F.comments,y=F.step,K=F.preview,_=F.fastMode,$=null,W=!1,D=F.quoteChar==null?'"':F.quoteChar,N=D;if(F.escapeChar!==void 0&&(N=F.escapeChar),(typeof p!="string"||-1<o.BAD_DELIMITERS.indexOf(p))&&(p=","),I===p)throw new Error("Comment character same as delimiter");I===!0?I="#":(typeof I!="string"||-1<o.BAD_DELIMITERS.indexOf(I))&&(I=!1),m!==`
`&&m!=="\r"&&m!==`\r
`&&(m=`
`);var T=0,k=!1;this.parse=function(O,R,L){if(typeof O!="string")throw new Error("Input must be a string");var M=O.length,P=p.length,q=m.length,rA=I.length,J=H(y),Y=[],iA=[],G=[],sA=T=0;if(!O)return CA();if(_||_!==!1&&O.indexOf(D)===-1){for(var uA=O.split(m),j=0;j<uA.length;j++){if(G=uA[j],T+=G.length,j!==uA.length-1)T+=m.length;else if(L)return CA();if(!I||G.substring(0,rA)!==I){if(J){if(Y=[],PA(G.split(p)),xA(),k)return CA()}else PA(G.split(p));if(K&&K<=j)return Y=Y.slice(0,K),CA(!0)}}return CA()}for(var AA=O.indexOf(p,T),X=O.indexOf(m,T),yA=new RegExp(Q(N)+Q(D),"g"),V=O.indexOf(D,T);;)if(O[T]===D)for(V=T,T++;;){if((V=O.indexOf(D,V+1))===-1)return L||iA.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:Y.length,index:T}),bA();if(V===M-1)return bA(O.substring(T,V).replace(yA,D));if(D===N&&O[V+1]===N)V++;else if(D===N||V===0||O[V-1]!==N){AA!==-1&&AA<V+1&&(AA=O.indexOf(p,V+1));var nA=te((X=X!==-1&&X<V+1?O.indexOf(m,V+1):X)===-1?AA:Math.min(AA,X));if(O.substr(V+1+nA,P)===p){G.push(O.substring(T,V).replace(yA,D)),O[T=V+1+nA+P]!==D&&(V=O.indexOf(D,T)),AA=O.indexOf(p,T),X=O.indexOf(m,T);break}if(nA=te(X),O.substring(V+1+nA,V+1+nA+q)===m){if(G.push(O.substring(T,V).replace(yA,D)),Ce(V+1+nA+q),AA=O.indexOf(p,T),V=O.indexOf(D,T),J&&(xA(),k))return CA();if(K&&Y.length>=K)return CA(!0);break}iA.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:Y.length,index:T}),V++}}else if(I&&G.length===0&&O.substring(T,T+rA)===I){if(X===-1)return CA();T=X+q,X=O.indexOf(m,T),AA=O.indexOf(p,T)}else if(AA!==-1&&(AA<X||X===-1))G.push(O.substring(T,AA)),T=AA+P,AA=O.indexOf(p,T);else{if(X===-1)break;if(G.push(O.substring(T,X)),Ce(X+q),J&&(xA(),k))return CA();if(K&&Y.length>=K)return CA(!0)}return bA();function PA(cA){Y.push(cA),sA=T}function te(cA){var FA=0;return FA=cA!==-1&&(cA=O.substring(V+1,cA))&&cA.trim()===""?cA.length:FA}function bA(cA){return L||(cA===void 0&&(cA=O.substring(T)),G.push(cA),T=M,PA(G),J&&xA()),CA()}function Ce(cA){T=cA,PA(G),G=[],X=O.indexOf(m,T)}function CA(cA){if(F.header&&!R&&Y.length&&!W){var FA=Y[0],_A={},Tr=new Set(FA);let Ot=!1;for(let ne=0;ne<FA.length;ne++){let DA=FA[ne];if(_A[DA=H(F.transformHeader)?F.transformHeader(DA,ne):DA]){let Ue,Rt=_A[DA];for(;Ue=DA+"_"+Rt,Rt++,Tr.has(Ue););Tr.add(Ue),FA[ne]=Ue,_A[DA]++,Ot=!0,($=$===null?{}:$)[Ue]=DA}else _A[DA]=1,FA[ne]=DA;Tr.add(DA)}Ot&&console.warn("Duplicate headers found and renamed."),W=!0}return{data:Y,errors:iA,meta:{delimiter:p,linebreak:m,aborted:k,truncated:!!cA,cursor:sA+(R||0),renamedHeaders:$}}}function xA(){y(CA()),Y=[],iA=[]}},this.abort=function(){k=!0},this.getCharIndex=function(){return T}}function h(F){var p=F.data,m=l[p.workerId],I=!1;if(p.error)m.userError(p.error,p.file);else if(p.results&&p.results.data){var y={abort:function(){I=!0,C(p.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:d,resume:d};if(H(m.userStep)){for(var K=0;K<p.results.data.length&&(m.userStep({data:p.results.data[K],errors:p.results.errors,meta:p.results.meta},y),!I);K++);delete p.results}else H(m.userChunk)&&(m.userChunk(p.results,y,p.file),delete p.results)}p.finished&&!I&&C(p.workerId,p.results)}function C(F,p){var m=l[F];H(m.userComplete)&&m.userComplete(p),m.terminate(),delete l[F]}function d(){throw new Error("Not implemented.")}function v(F){if(typeof F!="object"||F===null)return F;var p,m=Array.isArray(F)?[]:{};for(p in F)m[p]=v(F[p]);return m}function E(F,p){return function(){F.apply(p,arguments)}}function H(F){return typeof F=="function"}return o.parse=function(F,p){var m=(p=p||{}).dynamicTyping||!1;if(H(m)&&(p.dynamicTypingFunction=m,m={}),p.dynamicTyping=m,p.transform=!!H(p.transform)&&p.transform,!p.worker||!o.WORKERS_SUPPORTED)return m=null,o.NODE_STREAM_INPUT,typeof F=="string"?(F=(I=>I.charCodeAt(0)!==65279?I:I.slice(1))(F),m=new(p.download?s:a)(p)):F.readable===!0&&H(F.read)&&H(F.on)?m=new g(p):(A.File&&F instanceof File||F instanceof Object)&&(m=new B(p)),m.stream(F);(m=(()=>{var I;return!!o.WORKERS_SUPPORTED&&(I=(()=>{var y=A.URL||A.webkitURL||null,K=n.toString();return o.BLOB_URL||(o.BLOB_URL=y.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",K,")();"],{type:"text/javascript"})))})(),(I=new A.Worker(I)).onmessage=h,I.id=c++,l[I.id]=I)})()).userStep=p.step,m.userChunk=p.chunk,m.userComplete=p.complete,m.userError=p.error,p.step=H(p.step),p.chunk=H(p.chunk),p.complete=H(p.complete),p.error=H(p.error),delete p.worker,m.postMessage({input:F,config:p,workerId:m.id})},o.unparse=function(F,p){var m=!1,I=!0,y=",",K=`\r
`,_='"',$=_+_,W=!1,D=null,N=!1,T=((()=>{if(typeof p=="object"){if(typeof p.delimiter!="string"||o.BAD_DELIMITERS.filter(function(R){return p.delimiter.indexOf(R)!==-1}).length||(y=p.delimiter),typeof p.quotes!="boolean"&&typeof p.quotes!="function"&&!Array.isArray(p.quotes)||(m=p.quotes),typeof p.skipEmptyLines!="boolean"&&typeof p.skipEmptyLines!="string"||(W=p.skipEmptyLines),typeof p.newline=="string"&&(K=p.newline),typeof p.quoteChar=="string"&&(_=p.quoteChar),typeof p.header=="boolean"&&(I=p.header),Array.isArray(p.columns)){if(p.columns.length===0)throw new Error("Option columns is empty");D=p.columns}p.escapeChar!==void 0&&($=p.escapeChar+_),p.escapeFormulae instanceof RegExp?N=p.escapeFormulae:typeof p.escapeFormulae=="boolean"&&p.escapeFormulae&&(N=/^[=+\-@\t\r].*$/)}})(),new RegExp(Q(_),"g"));if(typeof F=="string"&&(F=JSON.parse(F)),Array.isArray(F)){if(!F.length||Array.isArray(F[0]))return k(null,F,W);if(typeof F[0]=="object")return k(D||Object.keys(F[0]),F,W)}else if(typeof F=="object")return typeof F.data=="string"&&(F.data=JSON.parse(F.data)),Array.isArray(F.data)&&(F.fields||(F.fields=F.meta&&F.meta.fields||D),F.fields||(F.fields=Array.isArray(F.data[0])?F.fields:typeof F.data[0]=="object"?Object.keys(F.data[0]):[]),Array.isArray(F.data[0])||typeof F.data[0]=="object"||(F.data=[F.data])),k(F.fields||[],F.data||[],W);throw new Error("Unable to serialize unrecognized input");function k(R,L,M){var P="",q=(typeof R=="string"&&(R=JSON.parse(R)),typeof L=="string"&&(L=JSON.parse(L)),Array.isArray(R)&&0<R.length),rA=!Array.isArray(L[0]);if(q&&I){for(var J=0;J<R.length;J++)0<J&&(P+=y),P+=O(R[J],J);0<L.length&&(P+=K)}for(var Y=0;Y<L.length;Y++){var iA=(q?R:L[Y]).length,G=!1,sA=q?Object.keys(L[Y]).length===0:L[Y].length===0;if(M&&!q&&(G=M==="greedy"?L[Y].join("").trim()==="":L[Y].length===1&&L[Y][0].length===0),M==="greedy"&&q){for(var uA=[],j=0;j<iA;j++){var AA=rA?R[j]:j;uA.push(L[Y][AA])}G=uA.join("").trim()===""}if(!G){for(var X=0;X<iA;X++){0<X&&!sA&&(P+=y);var yA=q&&rA?R[X]:X;P+=O(L[Y][yA],X)}Y<L.length-1&&(!M||0<iA&&!sA)&&(P+=K)}}return P}function O(R,L){var M,P;return R==null?"":R.constructor===Date?JSON.stringify(R).slice(1,25):(P=!1,N&&typeof R=="string"&&N.test(R)&&(R="'"+R,P=!0),M=R.toString().replace(T,$),(P=P||m===!0||typeof m=="function"&&m(R,L)||Array.isArray(m)&&m[L]||((q,rA)=>{for(var J=0;J<rA.length;J++)if(-1<q.indexOf(rA[J]))return!0;return!1})(M,o.BAD_DELIMITERS)||-1<M.indexOf(y)||M.charAt(0)===" "||M.charAt(M.length-1)===" ")?_+M+_:M)}},o.RECORD_SEP="",o.UNIT_SEP="",o.BYTE_ORDER_MARK="\uFEFF",o.BAD_DELIMITERS=["\r",`
`,'"',o.BYTE_ORDER_MARK],o.WORKERS_SUPPORTED=!u&&!!A.Worker,o.NODE_STREAM_INPUT=1,o.LocalChunkSize=10485760,o.RemoteChunkSize=5242880,o.DefaultDelimiter=",",o.Parser=U,o.ParserHandle=w,o.NetworkStreamer=s,o.FileStreamer=B,o.StringStreamer=a,o.ReadableStreamStreamer=g,A.jQuery&&((i=A.jQuery).fn.parse=function(F){var p=F.config||{},m=[];return this.each(function(K){if(!(i(this).prop("tagName").toUpperCase()==="INPUT"&&i(this).attr("type").toLowerCase()==="file"&&A.FileReader)||!this.files||this.files.length===0)return!0;for(var _=0;_<this.files.length;_++)m.push({file:this.files[_],inputElem:this,instanceConfig:i.extend({},p)})}),I(),this;function I(){if(m.length===0)H(F.complete)&&F.complete();else{var K,_,$,W,D=m[0];if(H(F.before)){var N=F.before(D.file,D.inputElem);if(typeof N=="object"){if(N.action==="abort")return K="AbortError",_=D.file,$=D.inputElem,W=N.reason,void(H(F.error)&&F.error({name:K},_,$,W));if(N.action==="skip")return void y();typeof N.config=="object"&&(D.instanceConfig=i.extend(D.instanceConfig,N.config))}else if(N==="skip")return void y()}var T=D.instanceConfig.complete;D.instanceConfig.complete=function(k){H(T)&&T(k,D.file,D.inputElem),y()},o.parse(D.file,D.instanceConfig)}}function y(){m.splice(0,1),I()}}),f&&(A.onmessage=function(F){F=F.data,o.WORKER_ID===void 0&&F&&(o.WORKER_ID=F.workerId),typeof F.input=="string"?A.postMessage({workerId:o.WORKER_ID,results:o.parse(F.input,F.config),finished:!0}):(A.File&&F.input instanceof File||F.input instanceof Object)&&(F=o.parse(F.input,F.config))&&A.postMessage({workerId:o.WORKER_ID,results:F,finished:!0})}),(s.prototype=Object.create(r.prototype)).constructor=s,(B.prototype=Object.create(r.prototype)).constructor=B,(a.prototype=Object.create(a.prototype)).constructor=a,(g.prototype=Object.create(r.prototype)).constructor=g,o})})(Nn);var zi=Nn.exports;const Kf=Vn(zi);/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var tt=function(t,e){return tt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,A){n.__proto__=A}||function(n,A){for(var i in A)Object.prototype.hasOwnProperty.call(A,i)&&(n[i]=A[i])},tt(t,e)};function LA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");tt(t,e);function n(){this.constructor=t}t.prototype=e===null?Object.create(e):(n.prototype=e.prototype,new n)}var nt=function(){return nt=Object.assign||function(e){for(var n,A=1,i=arguments.length;A<i;A++){n=arguments[A];for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&(e[u]=n[u])}return e},nt.apply(this,arguments)};function dA(t,e,n,A){function i(u){return u instanceof n?u:new n(function(f){f(u)})}return new(n||(n=Promise))(function(u,f){function l(r){try{o(A.next(r))}catch(s){f(s)}}function c(r){try{o(A.throw(r))}catch(s){f(s)}}function o(r){r.done?u(r.value):i(r.value).then(l,c)}o((A=A.apply(t,[])).next())})}function UA(t,e){var n={label:0,sent:function(){if(u[0]&1)throw u[1];return u[1]},trys:[],ops:[]},A,i,u,f;return f={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(f[Symbol.iterator]=function(){return this}),f;function l(o){return function(r){return c([o,r])}}function c(o){if(A)throw new TypeError("Generator is already executing.");for(;n;)try{if(A=1,i&&(u=o[0]&2?i.return:o[0]?i.throw||((u=i.return)&&u.call(i),0):i.next)&&!(u=u.call(i,o[1])).done)return u;switch(i=0,u&&(o=[o[0]&2,u.value]),o[0]){case 0:case 1:u=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(u=n.trys,!(u=u.length>0&&u[u.length-1])&&(o[0]===6||o[0]===2)){n=0;continue}if(o[0]===3&&(!u||o[1]>u[0]&&o[1]<u[3])){n.label=o[1];break}if(o[0]===6&&n.label<u[1]){n.label=u[1],u=o;break}if(u&&n.label<u[2]){n.label=u[2],n.ops.push(o);break}u[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(r){o=[6,r],i=0}finally{A=u=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}}function ke(t,e,n){if(arguments.length===2)for(var A=0,i=e.length,u;A<i;A++)(u||!(A in e))&&(u||(u=Array.prototype.slice.call(e,0,A)),u[A]=e[A]);return t.concat(u||e)}var kA=function(){function t(e,n,A,i){this.left=e,this.top=n,this.width=A,this.height=i}return t.prototype.add=function(e,n,A,i){return new t(this.left+e,this.top+n,this.width+A,this.height+i)},t.fromClientRect=function(e,n){return new t(n.left+e.windowBounds.left,n.top+e.windowBounds.top,n.width,n.height)},t.fromDOMRectList=function(e,n){var A=Array.from(n).find(function(i){return i.width!==0});return A?new t(A.left+e.windowBounds.left,A.top+e.windowBounds.top,A.width,A.height):t.EMPTY},t.EMPTY=new t(0,0,0,0),t}(),Er=function(t,e){return kA.fromClientRect(t,e.getBoundingClientRect())},$i=function(t){var e=t.body,n=t.documentElement;if(!e||!n)throw new Error("Unable to get document size");var A=Math.max(Math.max(e.scrollWidth,n.scrollWidth),Math.max(e.offsetWidth,n.offsetWidth),Math.max(e.clientWidth,n.clientWidth)),i=Math.max(Math.max(e.scrollHeight,n.scrollHeight),Math.max(e.offsetHeight,n.offsetHeight),Math.max(e.clientHeight,n.clientHeight));return new kA(0,0,A,i)},mr=function(t){for(var e=[],n=0,A=t.length;n<A;){var i=t.charCodeAt(n++);if(i>=55296&&i<=56319&&n<A){var u=t.charCodeAt(n++);(u&64512)===56320?e.push(((i&1023)<<10)+(u&1023)+65536):(e.push(i),n--)}else e.push(i)}return e},BA=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,t);var n=t.length;if(!n)return"";for(var A=[],i=-1,u="";++i<n;){var f=t[i];f<=65535?A.push(f):(f-=65536,A.push((f>>10)+55296,f%1024+56320)),(i+1===n||A.length>16384)&&(u+=String.fromCharCode.apply(String,A),A.length=0)}return u},Gt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ji=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Pe=0;Pe<Gt.length;Pe++)ji[Gt.charCodeAt(Pe)]=Pe;var Vt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ve=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Se=0;Se<Vt.length;Se++)ve[Vt.charCodeAt(Se)]=Se;var As=function(t){var e=t.length*.75,n=t.length,A,i=0,u,f,l,c;t[t.length-1]==="="&&(e--,t[t.length-2]==="="&&e--);var o=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),r=Array.isArray(o)?o:new Uint8Array(o);for(A=0;A<n;A+=4)u=ve[t.charCodeAt(A)],f=ve[t.charCodeAt(A+1)],l=ve[t.charCodeAt(A+2)],c=ve[t.charCodeAt(A+3)],r[i++]=u<<2|f>>4,r[i++]=(f&15)<<4|l>>2,r[i++]=(l&3)<<6|c&63;return o},es=function(t){for(var e=t.length,n=[],A=0;A<e;A+=2)n.push(t[A+1]<<8|t[A]);return n},rs=function(t){for(var e=t.length,n=[],A=0;A<e;A+=4)n.push(t[A+3]<<24|t[A+2]<<16|t[A+1]<<8|t[A]);return n},ee=5,It=11,Or=2,ts=It-ee,kn=65536>>ee,ns=1<<ee,Rr=ns-1,is=1024>>ee,ss=kn+is,os=ss,Bs=32,as=os+Bs,us=65536>>It,fs=1<<ts,cs=fs-1,Nt=function(t,e,n){return t.slice?t.slice(e,n):new Uint16Array(Array.prototype.slice.call(t,e,n))},ls=function(t,e,n){return t.slice?t.slice(e,n):new Uint32Array(Array.prototype.slice.call(t,e,n))},gs=function(t,e){var n=As(t),A=Array.isArray(n)?rs(n):new Uint32Array(n),i=Array.isArray(n)?es(n):new Uint16Array(n),u=24,f=Nt(i,u/2,A[4]/2),l=A[5]===2?Nt(i,(u+A[4])/2):ls(A,Math.ceil((u+A[4])/4));return new ws(A[0],A[1],A[2],A[3],f,l)},ws=function(){function t(e,n,A,i,u,f){this.initialValue=e,this.errorValue=n,this.highStart=A,this.highValueIndex=i,this.index=u,this.data=f}return t.prototype.get=function(e){var n;if(e>=0){if(e<55296||e>56319&&e<=65535)return n=this.index[e>>ee],n=(n<<Or)+(e&Rr),this.data[n];if(e<=65535)return n=this.index[kn+(e-55296>>ee)],n=(n<<Or)+(e&Rr),this.data[n];if(e<this.highStart)return n=as-us+(e>>It),n=this.index[n],n+=e>>ee&cs,n=this.index[n],n=(n<<Or)+(e&Rr),this.data[n];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},t}(),kt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Qs=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Me=0;Me<kt.length;Me++)Qs[kt.charCodeAt(Me)]=Me;var Cs="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",Pt=50,Us=1,Pn=2,Sn=3,hs=4,ds=5,St=7,Mn=8,Mt=9,XA=10,it=11,Jt=12,st=13,Fs=14,Ee=15,ot=16,Je=17,he=18,ps=19,Xt=20,Bt=21,de=22,_r=23,ie=24,vA=25,me=26,He=27,se=28,vs=29,jA=30,Es=31,Xe=32,We=33,at=34,ut=35,ft=36,Oe=37,ct=38,ur=39,fr=40,Gr=41,Jn=42,ms=43,Hs=[9001,65288],Xn="!",S="×",Ye="÷",lt=gs(Cs),GA=[jA,ft],gt=[Us,Pn,Sn,ds],Wn=[XA,Mn],Wt=[He,me],Is=gt.concat(Wn),Yt=[ct,ur,fr,at,ut],ys=[Ee,st],Ks=function(t,e){e===void 0&&(e="strict");var n=[],A=[],i=[];return t.forEach(function(u,f){var l=lt.get(u);if(l>Pt?(i.push(!0),l-=Pt):i.push(!1),["normal","auto","loose"].indexOf(e)!==-1&&[8208,8211,12316,12448].indexOf(u)!==-1)return A.push(f),n.push(ot);if(l===hs||l===it){if(f===0)return A.push(f),n.push(jA);var c=n[f-1];return Is.indexOf(c)===-1?(A.push(A[f-1]),n.push(c)):(A.push(f),n.push(jA))}if(A.push(f),l===Es)return n.push(e==="strict"?Bt:Oe);if(l===Jn||l===vs)return n.push(jA);if(l===ms)return u>=131072&&u<=196605||u>=196608&&u<=262141?n.push(Oe):n.push(jA);n.push(l)}),[A,n,i]},Vr=function(t,e,n,A){var i=A[n];if(Array.isArray(t)?t.indexOf(i)!==-1:t===i)for(var u=n;u<=A.length;){u++;var f=A[u];if(f===e)return!0;if(f!==XA)break}if(i===XA)for(var u=n;u>0;){u--;var l=A[u];if(Array.isArray(t)?t.indexOf(l)!==-1:t===l)for(var c=n;c<=A.length;){c++;var f=A[c];if(f===e)return!0;if(f!==XA)break}if(l!==XA)break}return!1},Zt=function(t,e){for(var n=t;n>=0;){var A=e[n];if(A===XA)n--;else return A}return 0},Ls=function(t,e,n,A,i){if(n[A]===0)return S;var u=A-1;if(Array.isArray(i)&&i[u]===!0)return S;var f=u-1,l=u+1,c=e[u],o=f>=0?e[f]:0,r=e[l];if(c===Pn&&r===Sn)return S;if(gt.indexOf(c)!==-1)return Xn;if(gt.indexOf(r)!==-1||Wn.indexOf(r)!==-1)return S;if(Zt(u,e)===Mn)return Ye;if(lt.get(t[u])===it||(c===Xe||c===We)&&lt.get(t[l])===it||c===St||r===St||c===Mt||[XA,st,Ee].indexOf(c)===-1&&r===Mt||[Je,he,ps,ie,se].indexOf(r)!==-1||Zt(u,e)===de||Vr(_r,de,u,e)||Vr([Je,he],Bt,u,e)||Vr(Jt,Jt,u,e))return S;if(c===XA)return Ye;if(c===_r||r===_r)return S;if(r===ot||c===ot)return Ye;if([st,Ee,Bt].indexOf(r)!==-1||c===Fs||o===ft&&ys.indexOf(c)!==-1||c===se&&r===ft||r===Xt||GA.indexOf(r)!==-1&&c===vA||GA.indexOf(c)!==-1&&r===vA||c===He&&[Oe,Xe,We].indexOf(r)!==-1||[Oe,Xe,We].indexOf(c)!==-1&&r===me||GA.indexOf(c)!==-1&&Wt.indexOf(r)!==-1||Wt.indexOf(c)!==-1&&GA.indexOf(r)!==-1||[He,me].indexOf(c)!==-1&&(r===vA||[de,Ee].indexOf(r)!==-1&&e[l+1]===vA)||[de,Ee].indexOf(c)!==-1&&r===vA||c===vA&&[vA,se,ie].indexOf(r)!==-1)return S;if([vA,se,ie,Je,he].indexOf(r)!==-1)for(var s=u;s>=0;){var B=e[s];if(B===vA)return S;if([se,ie].indexOf(B)!==-1)s--;else break}if([He,me].indexOf(r)!==-1)for(var s=[Je,he].indexOf(c)!==-1?f:u;s>=0;){var B=e[s];if(B===vA)return S;if([se,ie].indexOf(B)!==-1)s--;else break}if(ct===c&&[ct,ur,at,ut].indexOf(r)!==-1||[ur,at].indexOf(c)!==-1&&[ur,fr].indexOf(r)!==-1||[fr,ut].indexOf(c)!==-1&&r===fr||Yt.indexOf(c)!==-1&&[Xt,me].indexOf(r)!==-1||Yt.indexOf(r)!==-1&&c===He||GA.indexOf(c)!==-1&&GA.indexOf(r)!==-1||c===ie&&GA.indexOf(r)!==-1||GA.concat(vA).indexOf(c)!==-1&&r===de&&Hs.indexOf(t[l])===-1||GA.concat(vA).indexOf(r)!==-1&&c===he)return S;if(c===Gr&&r===Gr){for(var a=n[u],g=1;a>0&&(a--,e[a]===Gr);)g++;if(g%2!==0)return S}return c===Xe&&r===We?S:Ye},bs=function(t,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var n=Ks(t,e.lineBreak),A=n[0],i=n[1],u=n[2];(e.wordBreak==="break-all"||e.wordBreak==="break-word")&&(i=i.map(function(l){return[vA,jA,Jn].indexOf(l)!==-1?Oe:l}));var f=e.wordBreak==="keep-all"?u.map(function(l,c){return l&&t[c]>=19968&&t[c]<=40959}):void 0;return[A,i,f]},xs=function(){function t(e,n,A,i){this.codePoints=e,this.required=n===Xn,this.start=A,this.end=i}return t.prototype.slice=function(){return BA.apply(void 0,this.codePoints.slice(this.start,this.end))},t}(),Ds=function(t,e){var n=mr(t),A=bs(n,e),i=A[0],u=A[1],f=A[2],l=n.length,c=0,o=0;return{next:function(){if(o>=l)return{done:!0,value:null};for(var r=S;o<l&&(r=Ls(n,u,i,++o,f))===S;);if(r!==S||o===l){var s=new xs(n,r,c,o);return c=o,{value:s,done:!1}}return{done:!0,value:null}}}},Ts=1,Os=2,Ge=4,qt=8,gr=10,zt=47,Le=92,Rs=9,_s=32,Ze=34,Fe=61,Gs=35,Vs=36,Ns=37,qe=39,ze=40,pe=41,ks=95,pA=45,Ps=33,Ss=60,Ms=62,Js=64,Xs=91,Ws=93,Ys=61,Zs=123,$e=63,qs=125,$t=124,zs=126,$s=128,jt=65533,Nr=42,Ae=43,js=44,Ao=58,eo=59,Re=46,ro=0,to=8,no=11,io=14,so=31,oo=127,TA=-1,Yn=48,Zn=97,qn=101,Bo=102,ao=117,uo=122,zn=65,$n=69,jn=70,fo=85,co=90,hA=function(t){return t>=Yn&&t<=57},lo=function(t){return t>=55296&&t<=57343},oe=function(t){return hA(t)||t>=zn&&t<=jn||t>=Zn&&t<=Bo},go=function(t){return t>=Zn&&t<=uo},wo=function(t){return t>=zn&&t<=co},Qo=function(t){return go(t)||wo(t)},Co=function(t){return t>=$s},je=function(t){return t===gr||t===Rs||t===_s},wr=function(t){return Qo(t)||Co(t)||t===ks},An=function(t){return wr(t)||hA(t)||t===pA},Uo=function(t){return t>=ro&&t<=to||t===no||t>=io&&t<=so||t===oo},JA=function(t,e){return t!==Le?!1:e!==gr},Ar=function(t,e,n){return t===pA?wr(e)||JA(e,n):wr(t)?!0:!!(t===Le&&JA(t,e))},kr=function(t,e,n){return t===Ae||t===pA?hA(e)?!0:e===Re&&hA(n):hA(t===Re?e:t)},ho=function(t){var e=0,n=1;(t[e]===Ae||t[e]===pA)&&(t[e]===pA&&(n=-1),e++);for(var A=[];hA(t[e]);)A.push(t[e++]);var i=A.length?parseInt(BA.apply(void 0,A),10):0;t[e]===Re&&e++;for(var u=[];hA(t[e]);)u.push(t[e++]);var f=u.length,l=f?parseInt(BA.apply(void 0,u),10):0;(t[e]===$n||t[e]===qn)&&e++;var c=1;(t[e]===Ae||t[e]===pA)&&(t[e]===pA&&(c=-1),e++);for(var o=[];hA(t[e]);)o.push(t[e++]);var r=o.length?parseInt(BA.apply(void 0,o),10):0;return n*(i+l*Math.pow(10,-f))*Math.pow(10,c*r)},Fo={type:2},po={type:3},vo={type:4},Eo={type:13},mo={type:8},Ho={type:21},Io={type:9},yo={type:10},Ko={type:11},Lo={type:12},bo={type:14},er={type:23},xo={type:1},Do={type:25},To={type:24},Oo={type:26},Ro={type:27},_o={type:28},Go={type:29},Vo={type:31},wt={type:32},Ai=function(){function t(){this._value=[]}return t.prototype.write=function(e){this._value=this._value.concat(mr(e))},t.prototype.read=function(){for(var e=[],n=this.consumeToken();n!==wt;)e.push(n),n=this.consumeToken();return e},t.prototype.consumeToken=function(){var e=this.consumeCodePoint();switch(e){case Ze:return this.consumeStringToken(Ze);case Gs:var n=this.peekCodePoint(0),A=this.peekCodePoint(1),i=this.peekCodePoint(2);if(An(n)||JA(A,i)){var u=Ar(n,A,i)?Os:Ts,f=this.consumeName();return{type:5,value:f,flags:u}}break;case Vs:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Eo;break;case qe:return this.consumeStringToken(qe);case ze:return Fo;case pe:return po;case Nr:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),bo;break;case Ae:if(kr(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case js:return vo;case pA:var l=e,c=this.peekCodePoint(0),o=this.peekCodePoint(1);if(kr(l,c,o))return this.reconsumeCodePoint(e),this.consumeNumericToken();if(Ar(l,c,o))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();if(c===pA&&o===Ms)return this.consumeCodePoint(),this.consumeCodePoint(),To;break;case Re:if(kr(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case zt:if(this.peekCodePoint(0)===Nr)for(this.consumeCodePoint();;){var r=this.consumeCodePoint();if(r===Nr&&(r=this.consumeCodePoint(),r===zt))return this.consumeToken();if(r===TA)return this.consumeToken()}break;case Ao:return Oo;case eo:return Ro;case Ss:if(this.peekCodePoint(0)===Ps&&this.peekCodePoint(1)===pA&&this.peekCodePoint(2)===pA)return this.consumeCodePoint(),this.consumeCodePoint(),Do;break;case Js:var s=this.peekCodePoint(0),B=this.peekCodePoint(1),a=this.peekCodePoint(2);if(Ar(s,B,a)){var f=this.consumeName();return{type:7,value:f}}break;case Xs:return _o;case Le:if(JA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();break;case Ws:return Go;case Ys:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),mo;break;case Zs:return Ko;case qs:return Lo;case ao:case fo:var g=this.peekCodePoint(0),w=this.peekCodePoint(1);return g===Ae&&(oe(w)||w===$e)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(e),this.consumeIdentLikeToken();case $t:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Io;if(this.peekCodePoint(0)===$t)return this.consumeCodePoint(),Ho;break;case zs:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),yo;break;case TA:return wt}return je(e)?(this.consumeWhiteSpace(),Vo):hA(e)?(this.reconsumeCodePoint(e),this.consumeNumericToken()):wr(e)?(this.reconsumeCodePoint(e),this.consumeIdentLikeToken()):{type:6,value:BA(e)}},t.prototype.consumeCodePoint=function(){var e=this._value.shift();return typeof e>"u"?-1:e},t.prototype.reconsumeCodePoint=function(e){this._value.unshift(e)},t.prototype.peekCodePoint=function(e){return e>=this._value.length?-1:this._value[e]},t.prototype.consumeUnicodeRangeToken=function(){for(var e=[],n=this.consumeCodePoint();oe(n)&&e.length<6;)e.push(n),n=this.consumeCodePoint();for(var A=!1;n===$e&&e.length<6;)e.push(n),n=this.consumeCodePoint(),A=!0;if(A){var i=parseInt(BA.apply(void 0,e.map(function(c){return c===$e?Yn:c})),16),u=parseInt(BA.apply(void 0,e.map(function(c){return c===$e?jn:c})),16);return{type:30,start:i,end:u}}var f=parseInt(BA.apply(void 0,e),16);if(this.peekCodePoint(0)===pA&&oe(this.peekCodePoint(1))){this.consumeCodePoint(),n=this.consumeCodePoint();for(var l=[];oe(n)&&l.length<6;)l.push(n),n=this.consumeCodePoint();var u=parseInt(BA.apply(void 0,l),16);return{type:30,start:f,end:u}}else return{type:30,start:f,end:f}},t.prototype.consumeIdentLikeToken=function(){var e=this.consumeName();return e.toLowerCase()==="url"&&this.peekCodePoint(0)===ze?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===ze?(this.consumeCodePoint(),{type:19,value:e}):{type:20,value:e}},t.prototype.consumeUrlToken=function(){var e=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===TA)return{type:22,value:""};var n=this.peekCodePoint(0);if(n===qe||n===Ze){var A=this.consumeStringToken(this.consumeCodePoint());return A.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===TA||this.peekCodePoint(0)===pe)?(this.consumeCodePoint(),{type:22,value:A.value}):(this.consumeBadUrlRemnants(),er)}for(;;){var i=this.consumeCodePoint();if(i===TA||i===pe)return{type:22,value:BA.apply(void 0,e)};if(je(i))return this.consumeWhiteSpace(),this.peekCodePoint(0)===TA||this.peekCodePoint(0)===pe?(this.consumeCodePoint(),{type:22,value:BA.apply(void 0,e)}):(this.consumeBadUrlRemnants(),er);if(i===Ze||i===qe||i===ze||Uo(i))return this.consumeBadUrlRemnants(),er;if(i===Le)if(JA(i,this.peekCodePoint(0)))e.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),er;else e.push(i)}},t.prototype.consumeWhiteSpace=function(){for(;je(this.peekCodePoint(0));)this.consumeCodePoint()},t.prototype.consumeBadUrlRemnants=function(){for(;;){var e=this.consumeCodePoint();if(e===pe||e===TA)return;JA(e,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},t.prototype.consumeStringSlice=function(e){for(var n=5e4,A="";e>0;){var i=Math.min(n,e);A+=BA.apply(void 0,this._value.splice(0,i)),e-=i}return this._value.shift(),A},t.prototype.consumeStringToken=function(e){var n="",A=0;do{var i=this._value[A];if(i===TA||i===void 0||i===e)return n+=this.consumeStringSlice(A),{type:0,value:n};if(i===gr)return this._value.splice(0,A),xo;if(i===Le){var u=this._value[A+1];u!==TA&&u!==void 0&&(u===gr?(n+=this.consumeStringSlice(A),A=-1,this._value.shift()):JA(i,u)&&(n+=this.consumeStringSlice(A),n+=BA(this.consumeEscapedCodePoint()),A=-1))}A++}while(!0)},t.prototype.consumeNumber=function(){var e=[],n=Ge,A=this.peekCodePoint(0);for((A===Ae||A===pA)&&e.push(this.consumeCodePoint());hA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());A=this.peekCodePoint(0);var i=this.peekCodePoint(1);if(A===Re&&hA(i))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),n=qt;hA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());A=this.peekCodePoint(0),i=this.peekCodePoint(1);var u=this.peekCodePoint(2);if((A===$n||A===qn)&&((i===Ae||i===pA)&&hA(u)||hA(i)))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),n=qt;hA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());return[ho(e),n]},t.prototype.consumeNumericToken=function(){var e=this.consumeNumber(),n=e[0],A=e[1],i=this.peekCodePoint(0),u=this.peekCodePoint(1),f=this.peekCodePoint(2);if(Ar(i,u,f)){var l=this.consumeName();return{type:15,number:n,flags:A,unit:l}}return i===Ns?(this.consumeCodePoint(),{type:16,number:n,flags:A}):{type:17,number:n,flags:A}},t.prototype.consumeEscapedCodePoint=function(){var e=this.consumeCodePoint();if(oe(e)){for(var n=BA(e);oe(this.peekCodePoint(0))&&n.length<6;)n+=BA(this.consumeCodePoint());je(this.peekCodePoint(0))&&this.consumeCodePoint();var A=parseInt(n,16);return A===0||lo(A)||A>1114111?jt:A}return e===TA?jt:e},t.prototype.consumeName=function(){for(var e="";;){var n=this.consumeCodePoint();if(An(n))e+=BA(n);else if(JA(n,this.peekCodePoint(0)))e+=BA(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(n),e}},t}(),ei=function(){function t(e){this._tokens=e}return t.create=function(e){var n=new Ai;return n.write(e),new t(n.read())},t.parseValue=function(e){return t.create(e).parseComponentValue()},t.parseValues=function(e){return t.create(e).parseComponentValues()},t.prototype.parseComponentValue=function(){for(var e=this.consumeToken();e.type===31;)e=this.consumeToken();if(e.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(e);var n=this.consumeComponentValue();do e=this.consumeToken();while(e.type===31);if(e.type===32)return n;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},t.prototype.parseComponentValues=function(){for(var e=[];;){var n=this.consumeComponentValue();if(n.type===32)return e;e.push(n),e.push()}},t.prototype.consumeComponentValue=function(){var e=this.consumeToken();switch(e.type){case 11:case 28:case 2:return this.consumeSimpleBlock(e.type);case 19:return this.consumeFunction(e)}return e},t.prototype.consumeSimpleBlock=function(e){for(var n={type:e,values:[]},A=this.consumeToken();;){if(A.type===32||ko(A,e))return n;this.reconsumeToken(A),n.values.push(this.consumeComponentValue()),A=this.consumeToken()}},t.prototype.consumeFunction=function(e){for(var n={name:e.value,values:[],type:18};;){var A=this.consumeToken();if(A.type===32||A.type===3)return n;this.reconsumeToken(A),n.values.push(this.consumeComponentValue())}},t.prototype.consumeToken=function(){var e=this._tokens.shift();return typeof e>"u"?wt:e},t.prototype.reconsumeToken=function(e){this._tokens.unshift(e)},t}(),Ve=function(t){return t.type===15},Qe=function(t){return t.type===17},z=function(t){return t.type===20},No=function(t){return t.type===0},Qt=function(t,e){return z(t)&&t.value===e},ri=function(t){return t.type!==31},we=function(t){return t.type!==31&&t.type!==4},OA=function(t){var e=[],n=[];return t.forEach(function(A){if(A.type===4){if(n.length===0)throw new Error("Error parsing function args, zero tokens for arg");e.push(n),n=[];return}A.type!==31&&n.push(A)}),n.length&&e.push(n),e},ko=function(t,e){return e===11&&t.type===12||e===28&&t.type===29?!0:e===2&&t.type===3},zA=function(t){return t.type===17||t.type===15},aA=function(t){return t.type===16||zA(t)},ti=function(t){return t.length>1?[t[0],t[1]]:[t[0]]},QA={type:17,number:0,flags:Ge},yt={type:16,number:50,flags:Ge},WA={type:16,number:100,flags:Ge},Ie=function(t,e,n){var A=t[0],i=t[1];return[eA(A,e),eA(typeof i<"u"?i:A,n)]},eA=function(t,e){if(t.type===16)return t.number/100*e;if(Ve(t))switch(t.unit){case"rem":case"em":return 16*t.number;case"px":default:return t.number}return t.number},ni="deg",ii="grad",si="rad",oi="turn",Hr={name:"angle",parse:function(t,e){if(e.type===15)switch(e.unit){case ni:return Math.PI*e.number/180;case ii:return Math.PI/200*e.number;case si:return e.number;case oi:return Math.PI*2*e.number}throw new Error("Unsupported angle type")}},Bi=function(t){return t.type===15&&(t.unit===ni||t.unit===ii||t.unit===si||t.unit===oi)},ai=function(t){var e=t.filter(z).map(function(n){return n.value}).join(" ");switch(e){case"to bottom right":case"to right bottom":case"left top":case"top left":return[QA,QA];case"to top":case"bottom":return HA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[QA,WA];case"to right":case"left":return HA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[WA,WA];case"to bottom":case"top":return HA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[WA,QA];case"to left":case"right":return HA(270)}return 0},HA=function(t){return Math.PI*t/180},ZA={name:"color",parse:function(t,e){if(e.type===18){var n=Po[e.name];if(typeof n>"u")throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return n(t,e.values)}if(e.type===5){if(e.value.length===3){var A=e.value.substring(0,1),i=e.value.substring(1,2),u=e.value.substring(2,3);return YA(parseInt(A+A,16),parseInt(i+i,16),parseInt(u+u,16),1)}if(e.value.length===4){var A=e.value.substring(0,1),i=e.value.substring(1,2),u=e.value.substring(2,3),f=e.value.substring(3,4);return YA(parseInt(A+A,16),parseInt(i+i,16),parseInt(u+u,16),parseInt(f+f,16)/255)}if(e.value.length===6){var A=e.value.substring(0,2),i=e.value.substring(2,4),u=e.value.substring(4,6);return YA(parseInt(A,16),parseInt(i,16),parseInt(u,16),1)}if(e.value.length===8){var A=e.value.substring(0,2),i=e.value.substring(2,4),u=e.value.substring(4,6),f=e.value.substring(6,8);return YA(parseInt(A,16),parseInt(i,16),parseInt(u,16),parseInt(f,16)/255)}}if(e.type===20){var l=NA[e.value.toUpperCase()];if(typeof l<"u")return l}return NA.TRANSPARENT}},qA=function(t){return(255&t)===0},lA=function(t){var e=255&t,n=255&t>>8,A=255&t>>16,i=255&t>>24;return e<255?"rgba("+i+","+A+","+n+","+e/255+")":"rgb("+i+","+A+","+n+")"},YA=function(t,e,n,A){return(t<<24|e<<16|n<<8|Math.round(A*255)<<0)>>>0},en=function(t,e){if(t.type===17)return t.number;if(t.type===16){var n=e===3?1:255;return e===3?t.number/100*n:Math.round(t.number/100*n)}return 0},rn=function(t,e){var n=e.filter(we);if(n.length===3){var A=n.map(en),i=A[0],u=A[1],f=A[2];return YA(i,u,f,1)}if(n.length===4){var l=n.map(en),i=l[0],u=l[1],f=l[2],c=l[3];return YA(i,u,f,c)}return 0};function Pr(t,e,n){return n<0&&(n+=1),n>=1&&(n-=1),n<1/6?(e-t)*n*6+t:n<1/2?e:n<2/3?(e-t)*6*(2/3-n)+t:t}var tn=function(t,e){var n=e.filter(we),A=n[0],i=n[1],u=n[2],f=n[3],l=(A.type===17?HA(A.number):Hr.parse(t,A))/(Math.PI*2),c=aA(i)?i.number/100:0,o=aA(u)?u.number/100:0,r=typeof f<"u"&&aA(f)?eA(f,1):1;if(c===0)return YA(o*255,o*255,o*255,1);var s=o<=.5?o*(c+1):o+c-o*c,B=o*2-s,a=Pr(B,s,l+1/3),g=Pr(B,s,l),w=Pr(B,s,l-1/3);return YA(a*255,g*255,w*255,r)},Po={hsl:tn,hsla:tn,rgb:rn,rgba:rn},be=function(t,e){return ZA.parse(t,ei.create(e).parseComponentValue())},NA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},So={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(t,e){return e.map(function(n){if(z(n))switch(n.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Mo={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ir=function(t,e){var n=ZA.parse(t,e[0]),A=e[1];return A&&aA(A)?{color:n,stop:A}:{color:n,stop:null}},nn=function(t,e){var n=t[0],A=t[t.length-1];n.stop===null&&(n.stop=QA),A.stop===null&&(A.stop=WA);for(var i=[],u=0,f=0;f<t.length;f++){var l=t[f].stop;if(l!==null){var c=eA(l,e);c>u?i.push(c):i.push(u),u=c}else i.push(null)}for(var o=null,f=0;f<i.length;f++){var r=i[f];if(r===null)o===null&&(o=f);else if(o!==null){for(var s=f-o,B=i[o-1],a=(r-B)/(s+1),g=1;g<=s;g++)i[o+g-1]=a*g;o=null}}return t.map(function(w,Q){var U=w.color;return{color:U,stop:Math.max(Math.min(1,i[Q]/e),0)}})},Jo=function(t,e,n){var A=e/2,i=n/2,u=eA(t[0],e)-A,f=i-eA(t[1],n);return(Math.atan2(f,u)+Math.PI*2)%(Math.PI*2)},Xo=function(t,e,n){var A=typeof t=="number"?t:Jo(t,e,n),i=Math.abs(e*Math.sin(A))+Math.abs(n*Math.cos(A)),u=e/2,f=n/2,l=i/2,c=Math.sin(A-Math.PI/2)*l,o=Math.cos(A-Math.PI/2)*l;return[i,u-o,u+o,f-c,f+c]},KA=function(t,e){return Math.sqrt(t*t+e*e)},sn=function(t,e,n,A,i){var u=[[0,0],[0,e],[t,0],[t,e]];return u.reduce(function(f,l){var c=l[0],o=l[1],r=KA(n-c,A-o);return(i?r<f.optimumDistance:r>f.optimumDistance)?{optimumCorner:l,optimumDistance:r}:f},{optimumDistance:i?1/0:-1/0,optimumCorner:null}).optimumCorner},Wo=function(t,e,n,A,i){var u=0,f=0;switch(t.size){case 0:t.shape===0?u=f=Math.min(Math.abs(e),Math.abs(e-A),Math.abs(n),Math.abs(n-i)):t.shape===1&&(u=Math.min(Math.abs(e),Math.abs(e-A)),f=Math.min(Math.abs(n),Math.abs(n-i)));break;case 2:if(t.shape===0)u=f=Math.min(KA(e,n),KA(e,n-i),KA(e-A,n),KA(e-A,n-i));else if(t.shape===1){var l=Math.min(Math.abs(n),Math.abs(n-i))/Math.min(Math.abs(e),Math.abs(e-A)),c=sn(A,i,e,n,!0),o=c[0],r=c[1];u=KA(o-e,(r-n)/l),f=l*u}break;case 1:t.shape===0?u=f=Math.max(Math.abs(e),Math.abs(e-A),Math.abs(n),Math.abs(n-i)):t.shape===1&&(u=Math.max(Math.abs(e),Math.abs(e-A)),f=Math.max(Math.abs(n),Math.abs(n-i)));break;case 3:if(t.shape===0)u=f=Math.max(KA(e,n),KA(e,n-i),KA(e-A,n),KA(e-A,n-i));else if(t.shape===1){var l=Math.max(Math.abs(n),Math.abs(n-i))/Math.max(Math.abs(e),Math.abs(e-A)),s=sn(A,i,e,n,!1),o=s[0],r=s[1];u=KA(o-e,(r-n)/l),f=l*u}break}return Array.isArray(t.size)&&(u=eA(t.size[0],A),f=t.size.length===2?eA(t.size[1],i):u),[u,f]},Yo=function(t,e){var n=HA(180),A=[];return OA(e).forEach(function(i,u){if(u===0){var f=i[0];if(f.type===20&&f.value==="to"){n=ai(i);return}else if(Bi(f)){n=Hr.parse(t,f);return}}var l=Ir(t,i);A.push(l)}),{angle:n,stops:A,type:1}},rr=function(t,e){var n=HA(180),A=[];return OA(e).forEach(function(i,u){if(u===0){var f=i[0];if(f.type===20&&["top","left","right","bottom"].indexOf(f.value)!==-1){n=ai(i);return}else if(Bi(f)){n=(Hr.parse(t,f)+HA(270))%HA(360);return}}var l=Ir(t,i);A.push(l)}),{angle:n,stops:A,type:1}},Zo=function(t,e){var n=HA(180),A=[],i=1,u=0,f=3,l=[];return OA(e).forEach(function(c,o){var r=c[0];if(o===0){if(z(r)&&r.value==="linear"){i=1;return}else if(z(r)&&r.value==="radial"){i=2;return}}if(r.type===18){if(r.name==="from"){var s=ZA.parse(t,r.values[0]);A.push({stop:QA,color:s})}else if(r.name==="to"){var s=ZA.parse(t,r.values[0]);A.push({stop:WA,color:s})}else if(r.name==="color-stop"){var B=r.values.filter(we);if(B.length===2){var s=ZA.parse(t,B[1]),a=B[0];Qe(a)&&A.push({stop:{type:16,number:a.number*100,flags:a.flags},color:s})}}}}),i===1?{angle:(n+HA(180))%HA(360),stops:A,type:i}:{size:f,shape:u,stops:A,position:l,type:i}},ui="closest-side",fi="farthest-side",ci="closest-corner",li="farthest-corner",gi="circle",wi="ellipse",Qi="cover",Ci="contain",qo=function(t,e){var n=0,A=3,i=[],u=[];return OA(e).forEach(function(f,l){var c=!0;if(l===0){var o=!1;c=f.reduce(function(s,B){if(o)if(z(B))switch(B.value){case"center":return u.push(yt),s;case"top":case"left":return u.push(QA),s;case"right":case"bottom":return u.push(WA),s}else(aA(B)||zA(B))&&u.push(B);else if(z(B))switch(B.value){case gi:return n=0,!1;case wi:return n=1,!1;case"at":return o=!0,!1;case ui:return A=0,!1;case Qi:case fi:return A=1,!1;case Ci:case ci:return A=2,!1;case li:return A=3,!1}else if(zA(B)||aA(B))return Array.isArray(A)||(A=[]),A.push(B),!1;return s},c)}if(c){var r=Ir(t,f);i.push(r)}}),{size:A,shape:n,stops:i,position:u,type:2}},tr=function(t,e){var n=0,A=3,i=[],u=[];return OA(e).forEach(function(f,l){var c=!0;if(l===0?c=f.reduce(function(r,s){if(z(s))switch(s.value){case"center":return u.push(yt),!1;case"top":case"left":return u.push(QA),!1;case"right":case"bottom":return u.push(WA),!1}else if(aA(s)||zA(s))return u.push(s),!1;return r},c):l===1&&(c=f.reduce(function(r,s){if(z(s))switch(s.value){case gi:return n=0,!1;case wi:return n=1,!1;case Ci:case ui:return A=0,!1;case fi:return A=1,!1;case ci:return A=2,!1;case Qi:case li:return A=3,!1}else if(zA(s)||aA(s))return Array.isArray(A)||(A=[]),A.push(s),!1;return r},c)),c){var o=Ir(t,f);i.push(o)}}),{size:A,shape:n,stops:i,position:u,type:2}},zo=function(t){return t.type===1},$o=function(t){return t.type===2},Kt={name:"image",parse:function(t,e){if(e.type===22){var n={url:e.value,type:0};return t.cache.addImage(e.value),n}if(e.type===18){var A=Ui[e.name];if(typeof A>"u")throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return A(t,e.values)}throw new Error("Unsupported image type "+e.type)}};function jo(t){return!(t.type===20&&t.value==="none")&&(t.type!==18||!!Ui[t.name])}var Ui={"linear-gradient":Yo,"-moz-linear-gradient":rr,"-ms-linear-gradient":rr,"-o-linear-gradient":rr,"-webkit-linear-gradient":rr,"radial-gradient":qo,"-moz-radial-gradient":tr,"-ms-radial-gradient":tr,"-o-radial-gradient":tr,"-webkit-radial-gradient":tr,"-webkit-gradient":Zo},AB={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(t,e){if(e.length===0)return[];var n=e[0];return n.type===20&&n.value==="none"?[]:e.filter(function(A){return we(A)&&jo(A)}).map(function(A){return Kt.parse(t,A)})}},eB={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(t,e){return e.map(function(n){if(z(n))switch(n.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},rB={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(t,e){return OA(e).map(function(n){return n.filter(aA)}).map(ti)}},tB={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(t,e){return OA(e).map(function(n){return n.filter(z).map(function(A){return A.value}).join(" ")}).map(nB)}},nB=function(t){switch(t){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},ge;(function(t){t.AUTO="auto",t.CONTAIN="contain",t.COVER="cover"})(ge||(ge={}));var iB={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(t,e){return OA(e).map(function(n){return n.filter(sB)})}},sB=function(t){return z(t)||aA(t)},yr=function(t){return{name:"border-"+t+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},oB=yr("top"),BB=yr("right"),aB=yr("bottom"),uB=yr("left"),Kr=function(t){return{name:"border-radius-"+t,initialValue:"0 0",prefix:!1,type:1,parse:function(e,n){return ti(n.filter(aA))}}},fB=Kr("top-left"),cB=Kr("top-right"),lB=Kr("bottom-right"),gB=Kr("bottom-left"),Lr=function(t){return{name:"border-"+t+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(e,n){switch(n){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},wB=Lr("top"),QB=Lr("right"),CB=Lr("bottom"),UB=Lr("left"),br=function(t){return{name:"border-"+t+"-width",initialValue:"0",type:0,prefix:!1,parse:function(e,n){return Ve(n)?n.number:0}}},hB=br("top"),dB=br("right"),FB=br("bottom"),pB=br("left"),vB={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},EB={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(t,e){switch(e){case"rtl":return 1;case"ltr":default:return 0}}},mB={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(t,e){return e.filter(z).reduce(function(n,A){return n|HB(A.value)},0)}},HB=function(t){switch(t){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},IB={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(t,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},yB={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(t,e){return e.type===20&&e.value==="normal"?0:e.type===17||e.type===15?e.number:0}},Qr;(function(t){t.NORMAL="normal",t.STRICT="strict"})(Qr||(Qr={}));var KB={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(t,e){switch(e){case"strict":return Qr.STRICT;case"normal":default:return Qr.NORMAL}}},LB={name:"line-height",initialValue:"normal",prefix:!1,type:4},on=function(t,e){return z(t)&&t.value==="normal"?1.2*e:t.type===17?e*t.number:aA(t)?eA(t,e):e},bB={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(t,e){return e.type===20&&e.value==="none"?null:Kt.parse(t,e)}},xB={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(t,e){switch(e){case"inside":return 0;case"outside":default:return 1}}},Ct={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(t,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},xr=function(t){return{name:"margin-"+t,initialValue:"0",prefix:!1,type:4}},DB=xr("top"),TB=xr("right"),OB=xr("bottom"),RB=xr("left"),_B={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(t,e){return e.filter(z).map(function(n){switch(n.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},GB={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(t,e){switch(e){case"break-word":return"break-word";case"normal":default:return"normal"}}},Dr=function(t){return{name:"padding-"+t,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},VB=Dr("top"),NB=Dr("right"),kB=Dr("bottom"),PB=Dr("left"),SB={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(t,e){switch(e){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},MB={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(t,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},JB={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(t,e){return e.length===1&&Qt(e[0],"none")?[]:OA(e).map(function(n){for(var A={color:NA.TRANSPARENT,offsetX:QA,offsetY:QA,blur:QA},i=0,u=0;u<n.length;u++){var f=n[u];zA(f)?(i===0?A.offsetX=f:i===1?A.offsetY=f:A.blur=f,i++):A.color=ZA.parse(t,f)}return A})}},XB={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(t,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},WB={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(t,e){if(e.type===20&&e.value==="none")return null;if(e.type===18){var n=qB[e.name];if(typeof n>"u")throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return n(e.values)}return null}},YB=function(t){var e=t.filter(function(n){return n.type===17}).map(function(n){return n.number});return e.length===6?e:null},ZB=function(t){var e=t.filter(function(c){return c.type===17}).map(function(c){return c.number}),n=e[0],A=e[1];e[2],e[3];var i=e[4],u=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var f=e[12],l=e[13];return e[14],e[15],e.length===16?[n,A,i,u,f,l]:null},qB={matrix:YB,matrix3d:ZB},Bn={type:16,number:50,flags:Ge},zB=[Bn,Bn],$B={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(t,e){var n=e.filter(aA);return n.length!==2?zB:[n[0],n[1]]}},jB={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(t,e){switch(e){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},xe;(function(t){t.NORMAL="normal",t.BREAK_ALL="break-all",t.KEEP_ALL="keep-all"})(xe||(xe={}));var Aa={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(t,e){switch(e){case"break-all":return xe.BREAK_ALL;case"keep-all":return xe.KEEP_ALL;case"normal":default:return xe.NORMAL}}},ea={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(t,e){if(e.type===20)return{auto:!0,order:0};if(Qe(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},hi={name:"time",parse:function(t,e){if(e.type===15)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")}},ra={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(t,e){return Qe(e)?e.number:1}},ta={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},na={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(t,e){return e.filter(z).map(function(n){switch(n.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(n){return n!==0})}},ia={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(t,e){var n=[],A=[];return e.forEach(function(i){switch(i.type){case 20:case 0:n.push(i.value);break;case 17:n.push(i.number.toString());break;case 4:A.push(n.join(" ")),n.length=0;break}}),n.length&&A.push(n.join(" ")),A.map(function(i){return i.indexOf(" ")===-1?i:"'"+i+"'"})}},sa={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},oa={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(t,e){if(Qe(e))return e.number;if(z(e))switch(e.value){case"bold":return 700;case"normal":default:return 400}return 400}},Ba={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(t,e){return e.filter(z).map(function(n){return n.value})}},aa={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(t,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},fA=function(t,e){return(t&e)!==0},ua={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(t,e){if(e.length===0)return[];var n=e[0];return n.type===20&&n.value==="none"?[]:e}},fa={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(t,e){if(e.length===0)return null;var n=e[0];if(n.type===20&&n.value==="none")return null;for(var A=[],i=e.filter(ri),u=0;u<i.length;u++){var f=i[u],l=i[u+1];if(f.type===20){var c=l&&Qe(l)?l.number:1;A.push({counter:f.value,increment:c})}}return A}},ca={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(t,e){if(e.length===0)return[];for(var n=[],A=e.filter(ri),i=0;i<A.length;i++){var u=A[i],f=A[i+1];if(z(u)&&u.value!=="none"){var l=f&&Qe(f)?f.number:0;n.push({counter:u.value,reset:l})}}return n}},la={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(t,e){return e.filter(Ve).map(function(n){return hi.parse(t,n)})}},ga={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(t,e){if(e.length===0)return null;var n=e[0];if(n.type===20&&n.value==="none")return null;var A=[],i=e.filter(No);if(i.length%2!==0)return null;for(var u=0;u<i.length;u+=2){var f=i[u].value,l=i[u+1].value;A.push({open:f,close:l})}return A}},an=function(t,e,n){if(!t)return"";var A=t[Math.min(e,t.length-1)];return A?n?A.open:A.close:""},wa={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(t,e){return e.length===1&&Qt(e[0],"none")?[]:OA(e).map(function(n){for(var A={color:255,offsetX:QA,offsetY:QA,blur:QA,spread:QA,inset:!1},i=0,u=0;u<n.length;u++){var f=n[u];Qt(f,"inset")?A.inset=!0:zA(f)?(i===0?A.offsetX=f:i===1?A.offsetY=f:i===2?A.blur=f:A.spread=f,i++):A.color=ZA.parse(t,f)}return A})}},Qa={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(t,e){var n=[0,1,2],A=[];return e.filter(z).forEach(function(i){switch(i.value){case"stroke":A.push(1);break;case"fill":A.push(0);break;case"markers":A.push(2);break}}),n.forEach(function(i){A.indexOf(i)===-1&&A.push(i)}),A}},Ca={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Ua={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(t,e){return Ve(e)?e.number:0}},ha=function(){function t(e,n){var A,i;this.animationDuration=x(e,la,n.animationDuration),this.backgroundClip=x(e,So,n.backgroundClip),this.backgroundColor=x(e,Mo,n.backgroundColor),this.backgroundImage=x(e,AB,n.backgroundImage),this.backgroundOrigin=x(e,eB,n.backgroundOrigin),this.backgroundPosition=x(e,rB,n.backgroundPosition),this.backgroundRepeat=x(e,tB,n.backgroundRepeat),this.backgroundSize=x(e,iB,n.backgroundSize),this.borderTopColor=x(e,oB,n.borderTopColor),this.borderRightColor=x(e,BB,n.borderRightColor),this.borderBottomColor=x(e,aB,n.borderBottomColor),this.borderLeftColor=x(e,uB,n.borderLeftColor),this.borderTopLeftRadius=x(e,fB,n.borderTopLeftRadius),this.borderTopRightRadius=x(e,cB,n.borderTopRightRadius),this.borderBottomRightRadius=x(e,lB,n.borderBottomRightRadius),this.borderBottomLeftRadius=x(e,gB,n.borderBottomLeftRadius),this.borderTopStyle=x(e,wB,n.borderTopStyle),this.borderRightStyle=x(e,QB,n.borderRightStyle),this.borderBottomStyle=x(e,CB,n.borderBottomStyle),this.borderLeftStyle=x(e,UB,n.borderLeftStyle),this.borderTopWidth=x(e,hB,n.borderTopWidth),this.borderRightWidth=x(e,dB,n.borderRightWidth),this.borderBottomWidth=x(e,FB,n.borderBottomWidth),this.borderLeftWidth=x(e,pB,n.borderLeftWidth),this.boxShadow=x(e,wa,n.boxShadow),this.color=x(e,vB,n.color),this.direction=x(e,EB,n.direction),this.display=x(e,mB,n.display),this.float=x(e,IB,n.cssFloat),this.fontFamily=x(e,ia,n.fontFamily),this.fontSize=x(e,sa,n.fontSize),this.fontStyle=x(e,aa,n.fontStyle),this.fontVariant=x(e,Ba,n.fontVariant),this.fontWeight=x(e,oa,n.fontWeight),this.letterSpacing=x(e,yB,n.letterSpacing),this.lineBreak=x(e,KB,n.lineBreak),this.lineHeight=x(e,LB,n.lineHeight),this.listStyleImage=x(e,bB,n.listStyleImage),this.listStylePosition=x(e,xB,n.listStylePosition),this.listStyleType=x(e,Ct,n.listStyleType),this.marginTop=x(e,DB,n.marginTop),this.marginRight=x(e,TB,n.marginRight),this.marginBottom=x(e,OB,n.marginBottom),this.marginLeft=x(e,RB,n.marginLeft),this.opacity=x(e,ra,n.opacity);var u=x(e,_B,n.overflow);this.overflowX=u[0],this.overflowY=u[u.length>1?1:0],this.overflowWrap=x(e,GB,n.overflowWrap),this.paddingTop=x(e,VB,n.paddingTop),this.paddingRight=x(e,NB,n.paddingRight),this.paddingBottom=x(e,kB,n.paddingBottom),this.paddingLeft=x(e,PB,n.paddingLeft),this.paintOrder=x(e,Qa,n.paintOrder),this.position=x(e,MB,n.position),this.textAlign=x(e,SB,n.textAlign),this.textDecorationColor=x(e,ta,(A=n.textDecorationColor)!==null&&A!==void 0?A:n.color),this.textDecorationLine=x(e,na,(i=n.textDecorationLine)!==null&&i!==void 0?i:n.textDecoration),this.textShadow=x(e,JB,n.textShadow),this.textTransform=x(e,XB,n.textTransform),this.transform=x(e,WB,n.transform),this.transformOrigin=x(e,$B,n.transformOrigin),this.visibility=x(e,jB,n.visibility),this.webkitTextStrokeColor=x(e,Ca,n.webkitTextStrokeColor),this.webkitTextStrokeWidth=x(e,Ua,n.webkitTextStrokeWidth),this.wordBreak=x(e,Aa,n.wordBreak),this.zIndex=x(e,ea,n.zIndex)}return t.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},t.prototype.isTransparent=function(){return qA(this.backgroundColor)},t.prototype.isTransformed=function(){return this.transform!==null},t.prototype.isPositioned=function(){return this.position!==0},t.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},t.prototype.isFloating=function(){return this.float!==0},t.prototype.isInlineLevel=function(){return fA(this.display,4)||fA(this.display,33554432)||fA(this.display,268435456)||fA(this.display,536870912)||fA(this.display,67108864)||fA(this.display,134217728)},t}(),da=function(){function t(e,n){this.content=x(e,ua,n.content),this.quotes=x(e,ga,n.quotes)}return t}(),un=function(){function t(e,n){this.counterIncrement=x(e,fa,n.counterIncrement),this.counterReset=x(e,ca,n.counterReset)}return t}(),x=function(t,e,n){var A=new Ai,i=n!==null&&typeof n<"u"?n.toString():e.initialValue;A.write(i);var u=new ei(A.read());switch(e.type){case 2:var f=u.parseComponentValue();return e.parse(t,z(f)?f.value:e.initialValue);case 0:return e.parse(t,u.parseComponentValue());case 1:return e.parse(t,u.parseComponentValues());case 4:return u.parseComponentValue();case 3:switch(e.format){case"angle":return Hr.parse(t,u.parseComponentValue());case"color":return ZA.parse(t,u.parseComponentValue());case"image":return Kt.parse(t,u.parseComponentValue());case"length":var l=u.parseComponentValue();return zA(l)?l:QA;case"length-percentage":var c=u.parseComponentValue();return aA(c)?c:QA;case"time":return hi.parse(t,u.parseComponentValue())}break}},Fa="data-html2canvas-debug",pa=function(t){var e=t.getAttribute(Fa);switch(e){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Ut=function(t,e){var n=pa(t);return n===1||e===n},RA=function(){function t(e,n){if(this.context=e,this.textNodes=[],this.elements=[],this.flags=0,Ut(n,3))debugger;this.styles=new ha(e,window.getComputedStyle(n,null)),Ft(n)&&(this.styles.animationDuration.some(function(A){return A>0})&&(n.style.animationDuration="0s"),this.styles.transform!==null&&(n.style.transform="none")),this.bounds=Er(this.context,n),Ut(n,4)&&(this.flags|=16)}return t}(),va="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",fn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ye=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var nr=0;nr<fn.length;nr++)ye[fn.charCodeAt(nr)]=nr;var Ea=function(t){var e=t.length*.75,n=t.length,A,i=0,u,f,l,c;t[t.length-1]==="="&&(e--,t[t.length-2]==="="&&e--);var o=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),r=Array.isArray(o)?o:new Uint8Array(o);for(A=0;A<n;A+=4)u=ye[t.charCodeAt(A)],f=ye[t.charCodeAt(A+1)],l=ye[t.charCodeAt(A+2)],c=ye[t.charCodeAt(A+3)],r[i++]=u<<2|f>>4,r[i++]=(f&15)<<4|l>>2,r[i++]=(l&3)<<6|c&63;return o},ma=function(t){for(var e=t.length,n=[],A=0;A<e;A+=2)n.push(t[A+1]<<8|t[A]);return n},Ha=function(t){for(var e=t.length,n=[],A=0;A<e;A+=4)n.push(t[A+3]<<24|t[A+2]<<16|t[A+1]<<8|t[A]);return n},re=5,Lt=11,Sr=2,Ia=Lt-re,di=65536>>re,ya=1<<re,Mr=ya-1,Ka=1024>>re,La=di+Ka,ba=La,xa=32,Da=ba+xa,Ta=65536>>Lt,Oa=1<<Ia,Ra=Oa-1,cn=function(t,e,n){return t.slice?t.slice(e,n):new Uint16Array(Array.prototype.slice.call(t,e,n))},_a=function(t,e,n){return t.slice?t.slice(e,n):new Uint32Array(Array.prototype.slice.call(t,e,n))},Ga=function(t,e){var n=Ea(t),A=Array.isArray(n)?Ha(n):new Uint32Array(n),i=Array.isArray(n)?ma(n):new Uint16Array(n),u=24,f=cn(i,u/2,A[4]/2),l=A[5]===2?cn(i,(u+A[4])/2):_a(A,Math.ceil((u+A[4])/4));return new Va(A[0],A[1],A[2],A[3],f,l)},Va=function(){function t(e,n,A,i,u,f){this.initialValue=e,this.errorValue=n,this.highStart=A,this.highValueIndex=i,this.index=u,this.data=f}return t.prototype.get=function(e){var n;if(e>=0){if(e<55296||e>56319&&e<=65535)return n=this.index[e>>re],n=(n<<Sr)+(e&Mr),this.data[n];if(e<=65535)return n=this.index[di+(e-55296>>re)],n=(n<<Sr)+(e&Mr),this.data[n];if(e<this.highStart)return n=Da-Ta+(e>>Lt),n=this.index[n],n+=e>>re&Ra,n=this.index[n],n=(n<<Sr)+(e&Mr),this.data[n];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},t}(),ln="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Na=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var ir=0;ir<ln.length;ir++)Na[ln.charCodeAt(ir)]=ir;var ka=1,Jr=2,Xr=3,gn=4,wn=5,Pa=7,Qn=8,Wr=9,Yr=10,Cn=11,Un=12,hn=13,dn=14,Zr=15,Sa=function(t){for(var e=[],n=0,A=t.length;n<A;){var i=t.charCodeAt(n++);if(i>=55296&&i<=56319&&n<A){var u=t.charCodeAt(n++);(u&64512)===56320?e.push(((i&1023)<<10)+(u&1023)+65536):(e.push(i),n--)}else e.push(i)}return e},Ma=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,t);var n=t.length;if(!n)return"";for(var A=[],i=-1,u="";++i<n;){var f=t[i];f<=65535?A.push(f):(f-=65536,A.push((f>>10)+55296,f%1024+56320)),(i+1===n||A.length>16384)&&(u+=String.fromCharCode.apply(String,A),A.length=0)}return u},Ja=Ga(va),EA="×",qr="÷",Xa=function(t){return Ja.get(t)},Wa=function(t,e,n){var A=n-2,i=e[A],u=e[n-1],f=e[n];if(u===Jr&&f===Xr)return EA;if(u===Jr||u===Xr||u===gn||f===Jr||f===Xr||f===gn)return qr;if(u===Qn&&[Qn,Wr,Cn,Un].indexOf(f)!==-1||(u===Cn||u===Wr)&&(f===Wr||f===Yr)||(u===Un||u===Yr)&&f===Yr||f===hn||f===wn||f===Pa||u===ka)return EA;if(u===hn&&f===dn){for(;i===wn;)i=e[--A];if(i===dn)return EA}if(u===Zr&&f===Zr){for(var l=0;i===Zr;)l++,i=e[--A];if(l%2===0)return EA}return qr},Ya=function(t){var e=Sa(t),n=e.length,A=0,i=0,u=e.map(Xa);return{next:function(){if(A>=n)return{done:!0,value:null};for(var f=EA;A<n&&(f=Wa(e,u,++A))===EA;);if(f!==EA||A===n){var l=Ma.apply(null,e.slice(i,A));return i=A,{value:l,done:!1}}return{done:!0,value:null}}}},Za=function(t){for(var e=Ya(t),n=[],A;!(A=e.next()).done;)A.value&&n.push(A.value.slice());return n},qa=function(t){var e=123;if(t.createRange){var n=t.createRange();if(n.getBoundingClientRect){var A=t.createElement("boundtest");A.style.height=e+"px",A.style.display="block",t.body.appendChild(A),n.selectNode(A);var i=n.getBoundingClientRect(),u=Math.round(i.height);if(t.body.removeChild(A),u===e)return!0}}return!1},za=function(t){var e=t.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",t.body.appendChild(e);var n=t.createRange();e.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var A=e.firstChild,i=mr(A.data).map(function(c){return BA(c)}),u=0,f={},l=i.every(function(c,o){n.setStart(A,u),n.setEnd(A,u+c.length);var r=n.getBoundingClientRect();u+=c.length;var s=r.x>f.x||r.y>f.y;return f=r,o===0?!0:s});return t.body.removeChild(e),l},$a=function(){return typeof new Image().crossOrigin<"u"},ja=function(){return typeof new XMLHttpRequest().responseType=="string"},Au=function(t){var e=new Image,n=t.createElement("canvas"),A=n.getContext("2d");if(!A)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{A.drawImage(e,0,0),n.toDataURL()}catch{return!1}return!0},Fn=function(t){return t[0]===0&&t[1]===255&&t[2]===0&&t[3]===255},eu=function(t){var e=t.createElement("canvas"),n=100;e.width=n,e.height=n;var A=e.getContext("2d");if(!A)return Promise.reject(!1);A.fillStyle="rgb(0, 255, 0)",A.fillRect(0,0,n,n);var i=new Image,u=e.toDataURL();i.src=u;var f=ht(n,n,0,0,i);return A.fillStyle="red",A.fillRect(0,0,n,n),pn(f).then(function(l){A.drawImage(l,0,0);var c=A.getImageData(0,0,n,n).data;A.fillStyle="red",A.fillRect(0,0,n,n);var o=t.createElement("div");return o.style.backgroundImage="url("+u+")",o.style.height=n+"px",Fn(c)?pn(ht(n,n,0,0,o)):Promise.reject(!1)}).then(function(l){return A.drawImage(l,0,0),Fn(A.getImageData(0,0,n,n).data)}).catch(function(){return!1})},ht=function(t,e,n,A,i){var u="http://www.w3.org/2000/svg",f=document.createElementNS(u,"svg"),l=document.createElementNS(u,"foreignObject");return f.setAttributeNS(null,"width",t.toString()),f.setAttributeNS(null,"height",e.toString()),l.setAttributeNS(null,"width","100%"),l.setAttributeNS(null,"height","100%"),l.setAttributeNS(null,"x",n.toString()),l.setAttributeNS(null,"y",A.toString()),l.setAttributeNS(null,"externalResourcesRequired","true"),f.appendChild(l),l.appendChild(i),f},pn=function(t){return new Promise(function(e,n){var A=new Image;A.onload=function(){return e(A)},A.onerror=n,A.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(t))})},wA={get SUPPORT_RANGE_BOUNDS(){var t=qa(document);return Object.defineProperty(wA,"SUPPORT_RANGE_BOUNDS",{value:t}),t},get SUPPORT_WORD_BREAKING(){var t=wA.SUPPORT_RANGE_BOUNDS&&za(document);return Object.defineProperty(wA,"SUPPORT_WORD_BREAKING",{value:t}),t},get SUPPORT_SVG_DRAWING(){var t=Au(document);return Object.defineProperty(wA,"SUPPORT_SVG_DRAWING",{value:t}),t},get SUPPORT_FOREIGNOBJECT_DRAWING(){var t=typeof Array.from=="function"&&typeof window.fetch=="function"?eu(document):Promise.resolve(!1);return Object.defineProperty(wA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:t}),t},get SUPPORT_CORS_IMAGES(){var t=$a();return Object.defineProperty(wA,"SUPPORT_CORS_IMAGES",{value:t}),t},get SUPPORT_RESPONSE_TYPE(){var t=ja();return Object.defineProperty(wA,"SUPPORT_RESPONSE_TYPE",{value:t}),t},get SUPPORT_CORS_XHR(){var t="withCredentials"in new XMLHttpRequest;return Object.defineProperty(wA,"SUPPORT_CORS_XHR",{value:t}),t},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var t=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(wA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:t}),t}},De=function(){function t(e,n){this.text=e,this.bounds=n}return t}(),ru=function(t,e,n,A){var i=iu(e,n),u=[],f=0;return i.forEach(function(l){if(n.textDecorationLine.length||l.trim().length>0)if(wA.SUPPORT_RANGE_BOUNDS){var c=vn(A,f,l.length).getClientRects();if(c.length>1){var o=bt(l),r=0;o.forEach(function(B){u.push(new De(B,kA.fromDOMRectList(t,vn(A,r+f,B.length).getClientRects()))),r+=B.length})}else u.push(new De(l,kA.fromDOMRectList(t,c)))}else{var s=A.splitText(l.length);u.push(new De(l,tu(t,A))),A=s}else wA.SUPPORT_RANGE_BOUNDS||(A=A.splitText(l.length));f+=l.length}),u},tu=function(t,e){var n=e.ownerDocument;if(n){var A=n.createElement("html2canvaswrapper");A.appendChild(e.cloneNode(!0));var i=e.parentNode;if(i){i.replaceChild(A,e);var u=Er(t,A);return A.firstChild&&i.replaceChild(A.firstChild,A),u}}return kA.EMPTY},vn=function(t,e,n){var A=t.ownerDocument;if(!A)throw new Error("Node has no owner document");var i=A.createRange();return i.setStart(t,e),i.setEnd(t,e+n),i},bt=function(t){if(wA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(t)).map(function(n){return n.segment})}return Za(t)},nu=function(t,e){if(wA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var n=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(n.segment(t)).map(function(A){return A.segment})}return ou(t,e)},iu=function(t,e){return e.letterSpacing!==0?bt(t):nu(t,e)},su=[32,160,4961,65792,65793,4153,4241],ou=function(t,e){for(var n=Ds(t,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap==="break-word"?"break-word":e.wordBreak}),A=[],i,u=function(){if(i.value){var f=i.value.slice(),l=mr(f),c="";l.forEach(function(o){su.indexOf(o)===-1?c+=BA(o):(c.length&&A.push(c),A.push(BA(o)),c="")}),c.length&&A.push(c)}};!(i=n.next()).done;)u();return A},Bu=function(){function t(e,n,A){this.text=au(n.data,A.textTransform),this.textBounds=ru(e,this.text,A,n)}return t}(),au=function(t,e){switch(e){case 1:return t.toLowerCase();case 3:return t.replace(uu,fu);case 2:return t.toUpperCase();default:return t}},uu=/(^|\s|:|-|\(|\))([a-z])/g,fu=function(t,e,n){return t.length>0?e+n.toUpperCase():t},Fi=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i.src=A.currentSrc||A.src,i.intrinsicWidth=A.naturalWidth,i.intrinsicHeight=A.naturalHeight,i.context.cache.addImage(i.src),i}return e}(RA),pi=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i.canvas=A,i.intrinsicWidth=A.width,i.intrinsicHeight=A.height,i}return e}(RA),vi=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this,u=new XMLSerializer,f=Er(n,A);return A.setAttribute("width",f.width+"px"),A.setAttribute("height",f.height+"px"),i.svg="data:image/svg+xml,"+encodeURIComponent(u.serializeToString(A)),i.intrinsicWidth=A.width.baseVal.value,i.intrinsicHeight=A.height.baseVal.value,i.context.cache.addImage(i.svg),i}return e}(RA),Ei=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i.value=A.value,i}return e}(RA),dt=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i.start=A.start,i.reversed=typeof A.reversed=="boolean"&&A.reversed===!0,i}return e}(RA),cu=[{type:15,flags:0,unit:"px",number:3}],lu=[{type:16,flags:0,number:50}],gu=function(t){return t.width>t.height?new kA(t.left+(t.width-t.height)/2,t.top,t.height,t.height):t.width<t.height?new kA(t.left,t.top+(t.height-t.width)/2,t.width,t.width):t},wu=function(t){var e=t.type===Qu?new Array(t.value.length+1).join("•"):t.value;return e.length===0?t.placeholder||"":e},Cr="checkbox",Ur="radio",Qu="password",En=707406591,xt=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;switch(i.type=A.type.toLowerCase(),i.checked=A.checked,i.value=wu(A),(i.type===Cr||i.type===Ur)&&(i.styles.backgroundColor=3739148031,i.styles.borderTopColor=i.styles.borderRightColor=i.styles.borderBottomColor=i.styles.borderLeftColor=2779096575,i.styles.borderTopWidth=i.styles.borderRightWidth=i.styles.borderBottomWidth=i.styles.borderLeftWidth=1,i.styles.borderTopStyle=i.styles.borderRightStyle=i.styles.borderBottomStyle=i.styles.borderLeftStyle=1,i.styles.backgroundClip=[0],i.styles.backgroundOrigin=[0],i.bounds=gu(i.bounds)),i.type){case Cr:i.styles.borderTopRightRadius=i.styles.borderTopLeftRadius=i.styles.borderBottomRightRadius=i.styles.borderBottomLeftRadius=cu;break;case Ur:i.styles.borderTopRightRadius=i.styles.borderTopLeftRadius=i.styles.borderBottomRightRadius=i.styles.borderBottomLeftRadius=lu;break}return i}return e}(RA),mi=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this,u=A.options[A.selectedIndex||0];return i.value=u&&u.text||"",i}return e}(RA),Hi=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i.value=A.value,i}return e}(RA),Ii=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;i.src=A.src,i.width=parseInt(A.width,10)||0,i.height=parseInt(A.height,10)||0,i.backgroundColor=i.styles.backgroundColor;try{if(A.contentWindow&&A.contentWindow.document&&A.contentWindow.document.documentElement){i.tree=Ki(n,A.contentWindow.document.documentElement);var u=A.contentWindow.document.documentElement?be(n,getComputedStyle(A.contentWindow.document.documentElement).backgroundColor):NA.TRANSPARENT,f=A.contentWindow.document.body?be(n,getComputedStyle(A.contentWindow.document.body).backgroundColor):NA.TRANSPARENT;i.backgroundColor=qA(u)?qA(f)?i.styles.backgroundColor:f:u}}catch{}return i}return e}(RA),Cu=["OL","UL","MENU"],cr=function(t,e,n,A){for(var i=e.firstChild,u=void 0;i;i=u)if(u=i.nextSibling,Li(i)&&i.data.trim().length>0)n.textNodes.push(new Bu(t,i,n.styles));else if(le(i))if(Ti(i)&&i.assignedNodes)i.assignedNodes().forEach(function(l){return cr(t,l,n,A)});else{var f=yi(t,i);f.styles.isVisible()&&(Uu(i,f,A)?f.flags|=4:hu(f.styles)&&(f.flags|=2),Cu.indexOf(i.tagName)!==-1&&(f.flags|=8),n.elements.push(f),i.slot,i.shadowRoot?cr(t,i.shadowRoot,f,A):!hr(i)&&!bi(i)&&!dr(i)&&cr(t,i,f,A))}},yi=function(t,e){return pt(e)?new Fi(t,e):xi(e)?new pi(t,e):bi(e)?new vi(t,e):du(e)?new Ei(t,e):Fu(e)?new dt(t,e):pu(e)?new xt(t,e):dr(e)?new mi(t,e):hr(e)?new Hi(t,e):Di(e)?new Ii(t,e):new RA(t,e)},Ki=function(t,e){var n=yi(t,e);return n.flags|=4,cr(t,e,n,n),n},Uu=function(t,e,n){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||Dt(t)&&n.styles.isTransparent()},hu=function(t){return t.isPositioned()||t.isFloating()},Li=function(t){return t.nodeType===Node.TEXT_NODE},le=function(t){return t.nodeType===Node.ELEMENT_NODE},Ft=function(t){return le(t)&&typeof t.style<"u"&&!lr(t)},lr=function(t){return typeof t.className=="object"},du=function(t){return t.tagName==="LI"},Fu=function(t){return t.tagName==="OL"},pu=function(t){return t.tagName==="INPUT"},vu=function(t){return t.tagName==="HTML"},bi=function(t){return t.tagName==="svg"},Dt=function(t){return t.tagName==="BODY"},xi=function(t){return t.tagName==="CANVAS"},mn=function(t){return t.tagName==="VIDEO"},pt=function(t){return t.tagName==="IMG"},Di=function(t){return t.tagName==="IFRAME"},Hn=function(t){return t.tagName==="STYLE"},Eu=function(t){return t.tagName==="SCRIPT"},hr=function(t){return t.tagName==="TEXTAREA"},dr=function(t){return t.tagName==="SELECT"},Ti=function(t){return t.tagName==="SLOT"},In=function(t){return t.tagName.indexOf("-")>0},mu=function(){function t(){this.counters={}}return t.prototype.getCounterValue=function(e){var n=this.counters[e];return n&&n.length?n[n.length-1]:1},t.prototype.getCounterValues=function(e){var n=this.counters[e];return n||[]},t.prototype.pop=function(e){var n=this;e.forEach(function(A){return n.counters[A].pop()})},t.prototype.parse=function(e){var n=this,A=e.counterIncrement,i=e.counterReset,u=!0;A!==null&&A.forEach(function(l){var c=n.counters[l.counter];c&&l.increment!==0&&(u=!1,c.length||c.push(1),c[Math.max(0,c.length-1)]+=l.increment)});var f=[];return u&&i.forEach(function(l){var c=n.counters[l.counter];f.push(l.counter),c||(c=n.counters[l.counter]=[]),c.push(l.reset)}),f},t}(),yn={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Kn={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Hu={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Iu={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},Be=function(t,e,n,A,i,u){return t<e||t>n?_e(t,i,u.length>0):A.integers.reduce(function(f,l,c){for(;t>=l;)t-=l,f+=A.values[c];return f},"")+u},Oi=function(t,e,n,A){var i="";do n||t--,i=A(t)+i,t/=e;while(t*e>=e);return i},oA=function(t,e,n,A,i){var u=n-e+1;return(t<0?"-":"")+(Oi(Math.abs(t),u,A,function(f){return BA(Math.floor(f%u)+e)})+i)},$A=function(t,e,n){n===void 0&&(n=". ");var A=e.length;return Oi(Math.abs(t),A,!1,function(i){return e[Math.floor(i%A)]})+n},fe=1,SA=2,MA=4,Ke=8,VA=function(t,e,n,A,i,u){if(t<-9999||t>9999)return _e(t,4,i.length>0);var f=Math.abs(t),l=i;if(f===0)return e[0]+l;for(var c=0;f>0&&c<=4;c++){var o=f%10;o===0&&fA(u,fe)&&l!==""?l=e[o]+l:o>1||o===1&&c===0||o===1&&c===1&&fA(u,SA)||o===1&&c===1&&fA(u,MA)&&t>100||o===1&&c>1&&fA(u,Ke)?l=e[o]+(c>0?n[c-1]:"")+l:o===1&&c>0&&(l=n[c-1]+l),f=Math.floor(f/10)}return(t<0?A:"")+l},Ln="十百千萬",bn="拾佰仟萬",xn="マイナス",zr="마이너스",_e=function(t,e,n){var A=n?". ":"",i=n?"、":"",u=n?", ":"",f=n?" ":"";switch(e){case 0:return"•"+f;case 1:return"◦"+f;case 2:return"◾"+f;case 5:var l=oA(t,48,57,!0,A);return l.length<4?"0"+l:l;case 4:return $A(t,"〇一二三四五六七八九",i);case 6:return Be(t,1,3999,yn,3,A).toLowerCase();case 7:return Be(t,1,3999,yn,3,A);case 8:return oA(t,945,969,!1,A);case 9:return oA(t,97,122,!1,A);case 10:return oA(t,65,90,!1,A);case 11:return oA(t,1632,1641,!0,A);case 12:case 49:return Be(t,1,9999,Kn,3,A);case 35:return Be(t,1,9999,Kn,3,A).toLowerCase();case 13:return oA(t,2534,2543,!0,A);case 14:case 30:return oA(t,6112,6121,!0,A);case 15:return $A(t,"子丑寅卯辰巳午未申酉戌亥",i);case 16:return $A(t,"甲乙丙丁戊己庚辛壬癸",i);case 17:case 48:return VA(t,"零一二三四五六七八九",Ln,"負",i,SA|MA|Ke);case 47:return VA(t,"零壹貳參肆伍陸柒捌玖",bn,"負",i,fe|SA|MA|Ke);case 42:return VA(t,"零一二三四五六七八九",Ln,"负",i,SA|MA|Ke);case 41:return VA(t,"零壹贰叁肆伍陆柒捌玖",bn,"负",i,fe|SA|MA|Ke);case 26:return VA(t,"〇一二三四五六七八九","十百千万",xn,i,0);case 25:return VA(t,"零壱弐参四伍六七八九","拾百千万",xn,i,fe|SA|MA);case 31:return VA(t,"영일이삼사오육칠팔구","십백천만",zr,u,fe|SA|MA);case 33:return VA(t,"零一二三四五六七八九","十百千萬",zr,u,0);case 32:return VA(t,"零壹貳參四五六七八九","拾百千",zr,u,fe|SA|MA);case 18:return oA(t,2406,2415,!0,A);case 20:return Be(t,1,19999,Iu,3,A);case 21:return oA(t,2790,2799,!0,A);case 22:return oA(t,2662,2671,!0,A);case 22:return Be(t,1,10999,Hu,3,A);case 23:return $A(t,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return $A(t,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return oA(t,3302,3311,!0,A);case 28:return $A(t,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",i);case 29:return $A(t,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",i);case 34:return oA(t,3792,3801,!0,A);case 37:return oA(t,6160,6169,!0,A);case 38:return oA(t,4160,4169,!0,A);case 39:return oA(t,2918,2927,!0,A);case 40:return oA(t,1776,1785,!0,A);case 43:return oA(t,3046,3055,!0,A);case 44:return oA(t,3174,3183,!0,A);case 45:return oA(t,3664,3673,!0,A);case 46:return oA(t,3872,3881,!0,A);case 3:default:return oA(t,48,57,!0,A)}},Ri="data-html2canvas-ignore",Dn=function(){function t(e,n,A){if(this.context=e,this.options=A,this.scrolledElements=[],this.referenceElement=n,this.counters=new mu,this.quoteDepth=0,!n.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(n.ownerDocument.documentElement,!1)}return t.prototype.toIFrame=function(e,n){var A=this,i=yu(e,n);if(!i.contentWindow)return Promise.reject("Unable to find iframe window");var u=e.defaultView.pageXOffset,f=e.defaultView.pageYOffset,l=i.contentWindow,c=l.document,o=bu(i).then(function(){return dA(A,void 0,void 0,function(){var r,s;return UA(this,function(B){switch(B.label){case 0:return this.scrolledElements.forEach(Ou),l&&(l.scrollTo(n.left,n.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(l.scrollY!==n.top||l.scrollX!==n.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(l.scrollX-n.left,l.scrollY-n.top,0,0))),r=this.options.onclone,s=this.clonedReferenceElement,typeof s>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:c.fonts&&c.fonts.ready?[4,c.fonts.ready]:[3,2];case 1:B.sent(),B.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Lu(c)]:[3,4];case 3:B.sent(),B.label=4;case 4:return typeof r=="function"?[2,Promise.resolve().then(function(){return r(c,s)}).then(function(){return i})]:[2,i]}})})});return c.open(),c.write(Du(document.doctype)+"<html></html>"),Tu(this.referenceElement.ownerDocument,u,f),c.replaceChild(c.adoptNode(this.documentElement),c.documentElement),c.close(),o},t.prototype.createElementClone=function(e){if(Ut(e,2))debugger;if(xi(e))return this.createCanvasClone(e);if(mn(e))return this.createVideoClone(e);if(Hn(e))return this.createStyleClone(e);var n=e.cloneNode(!1);return pt(n)&&(pt(e)&&e.currentSrc&&e.currentSrc!==e.src&&(n.src=e.currentSrc,n.srcset=""),n.loading==="lazy"&&(n.loading="eager")),In(n)?this.createCustomElementClone(n):n},t.prototype.createCustomElementClone=function(e){var n=document.createElement("html2canvascustomelement");return $r(e.style,n),n},t.prototype.createStyleClone=function(e){try{var n=e.sheet;if(n&&n.cssRules){var A=[].slice.call(n.cssRules,0).reduce(function(u,f){return f&&typeof f.cssText=="string"?u+f.cssText:u},""),i=e.cloneNode(!1);return i.textContent=A,i}}catch(u){if(this.context.logger.error("Unable to access cssRules property",u),u.name!=="SecurityError")throw u}return e.cloneNode(!1)},t.prototype.createCanvasClone=function(e){var n;if(this.options.inlineImages&&e.ownerDocument){var A=e.ownerDocument.createElement("img");try{return A.src=e.toDataURL(),A}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",e)}}var i=e.cloneNode(!1);try{i.width=e.width,i.height=e.height;var u=e.getContext("2d"),f=i.getContext("2d");if(f)if(!this.options.allowTaint&&u)f.putImageData(u.getImageData(0,0,e.width,e.height),0,0);else{var l=(n=e.getContext("webgl2"))!==null&&n!==void 0?n:e.getContext("webgl");if(l){var c=l.getContextAttributes();(c==null?void 0:c.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",e)}f.drawImage(e,0,0)}return i}catch{this.context.logger.info("Unable to clone canvas as it is tainted",e)}return i},t.prototype.createVideoClone=function(e){var n=e.ownerDocument.createElement("canvas");n.width=e.offsetWidth,n.height=e.offsetHeight;var A=n.getContext("2d");try{return A&&(A.drawImage(e,0,0,n.width,n.height),this.options.allowTaint||A.getImageData(0,0,n.width,n.height)),n}catch{this.context.logger.info("Unable to clone video as it is tainted",e)}var i=e.ownerDocument.createElement("canvas");return i.width=e.offsetWidth,i.height=e.offsetHeight,i},t.prototype.appendChildNode=function(e,n,A){(!le(n)||!Eu(n)&&!n.hasAttribute(Ri)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(n)))&&(!this.options.copyStyles||!le(n)||!Hn(n))&&e.appendChild(this.cloneNode(n,A))},t.prototype.cloneChildNodes=function(e,n,A){for(var i=this,u=e.shadowRoot?e.shadowRoot.firstChild:e.firstChild;u;u=u.nextSibling)if(le(u)&&Ti(u)&&typeof u.assignedNodes=="function"){var f=u.assignedNodes();f.length&&f.forEach(function(l){return i.appendChildNode(n,l,A)})}else this.appendChildNode(n,u,A)},t.prototype.cloneNode=function(e,n){if(Li(e))return document.createTextNode(e.data);if(!e.ownerDocument)return e.cloneNode(!1);var A=e.ownerDocument.defaultView;if(A&&le(e)&&(Ft(e)||lr(e))){var i=this.createElementClone(e);i.style.transitionProperty="none";var u=A.getComputedStyle(e),f=A.getComputedStyle(e,":before"),l=A.getComputedStyle(e,":after");this.referenceElement===e&&Ft(i)&&(this.clonedReferenceElement=i),Dt(i)&&Gu(i);var c=this.counters.parse(new un(this.context,u)),o=this.resolvePseudoContent(e,i,f,Te.BEFORE);In(e)&&(n=!0),mn(e)||this.cloneChildNodes(e,i,n),o&&i.insertBefore(o,i.firstChild);var r=this.resolvePseudoContent(e,i,l,Te.AFTER);return r&&i.appendChild(r),this.counters.pop(c),(u&&(this.options.copyStyles||lr(e))&&!Di(e)||n)&&$r(u,i),(e.scrollTop!==0||e.scrollLeft!==0)&&this.scrolledElements.push([i,e.scrollLeft,e.scrollTop]),(hr(e)||dr(e))&&(hr(i)||dr(i))&&(i.value=e.value),i}return e.cloneNode(!1)},t.prototype.resolvePseudoContent=function(e,n,A,i){var u=this;if(A){var f=A.content,l=n.ownerDocument;if(!(!l||!f||f==="none"||f==="-moz-alt-content"||A.display==="none")){this.counters.parse(new un(this.context,A));var c=new da(this.context,A),o=l.createElement("html2canvaspseudoelement");$r(A,o),c.content.forEach(function(s){if(s.type===0)o.appendChild(l.createTextNode(s.value));else if(s.type===22){var B=l.createElement("img");B.src=s.value,B.style.opacity="1",o.appendChild(B)}else if(s.type===18){if(s.name==="attr"){var a=s.values.filter(z);a.length&&o.appendChild(l.createTextNode(e.getAttribute(a[0].value)||""))}else if(s.name==="counter"){var g=s.values.filter(we),w=g[0],Q=g[1];if(w&&z(w)){var U=u.counters.getCounterValue(w.value),h=Q&&z(Q)?Ct.parse(u.context,Q.value):3;o.appendChild(l.createTextNode(_e(U,h,!1)))}}else if(s.name==="counters"){var C=s.values.filter(we),w=C[0],d=C[1],Q=C[2];if(w&&z(w)){var v=u.counters.getCounterValues(w.value),E=Q&&z(Q)?Ct.parse(u.context,Q.value):3,H=d&&d.type===0?d.value:"",F=v.map(function(I){return _e(I,E,!1)}).join(H);o.appendChild(l.createTextNode(F))}}}else if(s.type===20)switch(s.value){case"open-quote":o.appendChild(l.createTextNode(an(c.quotes,u.quoteDepth++,!0)));break;case"close-quote":o.appendChild(l.createTextNode(an(c.quotes,--u.quoteDepth,!1)));break;default:o.appendChild(l.createTextNode(s.value))}}),o.className=vt+" "+Et;var r=i===Te.BEFORE?" "+vt:" "+Et;return lr(n)?n.className.baseValue+=r:n.className+=r,o}}},t.destroy=function(e){return e.parentNode?(e.parentNode.removeChild(e),!0):!1},t}(),Te;(function(t){t[t.BEFORE=0]="BEFORE",t[t.AFTER=1]="AFTER"})(Te||(Te={}));var yu=function(t,e){var n=t.createElement("iframe");return n.className="html2canvas-container",n.style.visibility="hidden",n.style.position="fixed",n.style.left="-10000px",n.style.top="0px",n.style.border="0",n.width=e.width.toString(),n.height=e.height.toString(),n.scrolling="no",n.setAttribute(Ri,"true"),t.body.appendChild(n),n},Ku=function(t){return new Promise(function(e){if(t.complete){e();return}if(!t.src){e();return}t.onload=e,t.onerror=e})},Lu=function(t){return Promise.all([].slice.call(t.images,0).map(Ku))},bu=function(t){return new Promise(function(e,n){var A=t.contentWindow;if(!A)return n("No window assigned for iframe");var i=A.document;A.onload=t.onload=function(){A.onload=t.onload=null;var u=setInterval(function(){i.body.childNodes.length>0&&i.readyState==="complete"&&(clearInterval(u),e(t))},50)}})},xu=["all","d","content"],$r=function(t,e){for(var n=t.length-1;n>=0;n--){var A=t.item(n);xu.indexOf(A)===-1&&e.style.setProperty(A,t.getPropertyValue(A))}return e},Du=function(t){var e="";return t&&(e+="<!DOCTYPE ",t.name&&(e+=t.name),t.internalSubset&&(e+=t.internalSubset),t.publicId&&(e+='"'+t.publicId+'"'),t.systemId&&(e+='"'+t.systemId+'"'),e+=">"),e},Tu=function(t,e,n){t&&t.defaultView&&(e!==t.defaultView.pageXOffset||n!==t.defaultView.pageYOffset)&&t.defaultView.scrollTo(e,n)},Ou=function(t){var e=t[0],n=t[1],A=t[2];e.scrollLeft=n,e.scrollTop=A},Ru=":before",_u=":after",vt="___html2canvas___pseudoelement_before",Et="___html2canvas___pseudoelement_after",Tn=`{
    content: "" !important;
    display: none !important;
}`,Gu=function(t){Vu(t,"."+vt+Ru+Tn+`
         .`+Et+_u+Tn)},Vu=function(t,e){var n=t.ownerDocument;if(n){var A=n.createElement("style");A.textContent=e,t.appendChild(A)}},_i=function(){function t(){}return t.getOrigin=function(e){var n=t._link;return n?(n.href=e,n.href=n.href,n.protocol+n.hostname+n.port):"about:blank"},t.isSameOrigin=function(e){return t.getOrigin(e)===t._origin},t.setContext=function(e){t._link=e.document.createElement("a"),t._origin=t.getOrigin(e.location.href)},t._origin="about:blank",t}(),Nu=function(){function t(e,n){this.context=e,this._options=n,this._cache={}}return t.prototype.addImage=function(e){var n=Promise.resolve();return this.has(e)||(At(e)||Mu(e))&&(this._cache[e]=this.loadImage(e)).catch(function(){}),n},t.prototype.match=function(e){return this._cache[e]},t.prototype.loadImage=function(e){return dA(this,void 0,void 0,function(){var n,A,i,u,f=this;return UA(this,function(l){switch(l.label){case 0:return n=_i.isSameOrigin(e),A=!jr(e)&&this._options.useCORS===!0&&wA.SUPPORT_CORS_IMAGES&&!n,i=!jr(e)&&!n&&!At(e)&&typeof this._options.proxy=="string"&&wA.SUPPORT_CORS_XHR&&!A,!n&&this._options.allowTaint===!1&&!jr(e)&&!At(e)&&!i&&!A?[2]:(u=e,i?[4,this.proxy(u)]:[3,2]);case 1:u=l.sent(),l.label=2;case 2:return this.context.logger.debug("Added image "+e.substring(0,256)),[4,new Promise(function(c,o){var r=new Image;r.onload=function(){return c(r)},r.onerror=o,(Ju(u)||A)&&(r.crossOrigin="anonymous"),r.src=u,r.complete===!0&&setTimeout(function(){return c(r)},500),f._options.imageTimeout>0&&setTimeout(function(){return o("Timed out ("+f._options.imageTimeout+"ms) loading image")},f._options.imageTimeout)})];case 3:return[2,l.sent()]}})})},t.prototype.has=function(e){return typeof this._cache[e]<"u"},t.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},t.prototype.proxy=function(e){var n=this,A=this._options.proxy;if(!A)throw new Error("No proxy defined");var i=e.substring(0,256);return new Promise(function(u,f){var l=wA.SUPPORT_RESPONSE_TYPE?"blob":"text",c=new XMLHttpRequest;c.onload=function(){if(c.status===200)if(l==="text")u(c.response);else{var s=new FileReader;s.addEventListener("load",function(){return u(s.result)},!1),s.addEventListener("error",function(B){return f(B)},!1),s.readAsDataURL(c.response)}else f("Failed to proxy resource "+i+" with status code "+c.status)},c.onerror=f;var o=A.indexOf("?")>-1?"&":"?";if(c.open("GET",""+A+o+"url="+encodeURIComponent(e)+"&responseType="+l),l!=="text"&&c instanceof XMLHttpRequest&&(c.responseType=l),n._options.imageTimeout){var r=n._options.imageTimeout;c.timeout=r,c.ontimeout=function(){return f("Timed out ("+r+"ms) proxying "+i)}}c.send()})},t}(),ku=/^data:image\/svg\+xml/i,Pu=/^data:image\/.*;base64,/i,Su=/^data:image\/.*/i,Mu=function(t){return wA.SUPPORT_SVG_DRAWING||!Xu(t)},jr=function(t){return Su.test(t)},Ju=function(t){return Pu.test(t)},At=function(t){return t.substr(0,4)==="blob"},Xu=function(t){return t.substr(-3).toLowerCase()==="svg"||ku.test(t)},b=function(){function t(e,n){this.type=0,this.x=e,this.y=n}return t.prototype.add=function(e,n){return new t(this.x+e,this.y+n)},t}(),ae=function(t,e,n){return new b(t.x+(e.x-t.x)*n,t.y+(e.y-t.y)*n)},sr=function(){function t(e,n,A,i){this.type=1,this.start=e,this.startControl=n,this.endControl=A,this.end=i}return t.prototype.subdivide=function(e,n){var A=ae(this.start,this.startControl,e),i=ae(this.startControl,this.endControl,e),u=ae(this.endControl,this.end,e),f=ae(A,i,e),l=ae(i,u,e),c=ae(f,l,e);return n?new t(this.start,A,f,c):new t(c,l,u,this.end)},t.prototype.add=function(e,n){return new t(this.start.add(e,n),this.startControl.add(e,n),this.endControl.add(e,n),this.end.add(e,n))},t.prototype.reverse=function(){return new t(this.end,this.endControl,this.startControl,this.start)},t}(),mA=function(t){return t.type===1},Wu=function(){function t(e){var n=e.styles,A=e.bounds,i=Ie(n.borderTopLeftRadius,A.width,A.height),u=i[0],f=i[1],l=Ie(n.borderTopRightRadius,A.width,A.height),c=l[0],o=l[1],r=Ie(n.borderBottomRightRadius,A.width,A.height),s=r[0],B=r[1],a=Ie(n.borderBottomLeftRadius,A.width,A.height),g=a[0],w=a[1],Q=[];Q.push((u+c)/A.width),Q.push((g+s)/A.width),Q.push((f+w)/A.height),Q.push((o+B)/A.height);var U=Math.max.apply(Math,Q);U>1&&(u/=U,f/=U,c/=U,o/=U,s/=U,B/=U,g/=U,w/=U);var h=A.width-c,C=A.height-B,d=A.width-s,v=A.height-w,E=n.borderTopWidth,H=n.borderRightWidth,F=n.borderBottomWidth,p=n.borderLeftWidth,m=eA(n.paddingTop,e.bounds.width),I=eA(n.paddingRight,e.bounds.width),y=eA(n.paddingBottom,e.bounds.width),K=eA(n.paddingLeft,e.bounds.width);this.topLeftBorderDoubleOuterBox=u>0||f>0?tA(A.left+p/3,A.top+E/3,u-p/3,f-E/3,Z.TOP_LEFT):new b(A.left+p/3,A.top+E/3),this.topRightBorderDoubleOuterBox=u>0||f>0?tA(A.left+h,A.top+E/3,c-H/3,o-E/3,Z.TOP_RIGHT):new b(A.left+A.width-H/3,A.top+E/3),this.bottomRightBorderDoubleOuterBox=s>0||B>0?tA(A.left+d,A.top+C,s-H/3,B-F/3,Z.BOTTOM_RIGHT):new b(A.left+A.width-H/3,A.top+A.height-F/3),this.bottomLeftBorderDoubleOuterBox=g>0||w>0?tA(A.left+p/3,A.top+v,g-p/3,w-F/3,Z.BOTTOM_LEFT):new b(A.left+p/3,A.top+A.height-F/3),this.topLeftBorderDoubleInnerBox=u>0||f>0?tA(A.left+p*2/3,A.top+E*2/3,u-p*2/3,f-E*2/3,Z.TOP_LEFT):new b(A.left+p*2/3,A.top+E*2/3),this.topRightBorderDoubleInnerBox=u>0||f>0?tA(A.left+h,A.top+E*2/3,c-H*2/3,o-E*2/3,Z.TOP_RIGHT):new b(A.left+A.width-H*2/3,A.top+E*2/3),this.bottomRightBorderDoubleInnerBox=s>0||B>0?tA(A.left+d,A.top+C,s-H*2/3,B-F*2/3,Z.BOTTOM_RIGHT):new b(A.left+A.width-H*2/3,A.top+A.height-F*2/3),this.bottomLeftBorderDoubleInnerBox=g>0||w>0?tA(A.left+p*2/3,A.top+v,g-p*2/3,w-F*2/3,Z.BOTTOM_LEFT):new b(A.left+p*2/3,A.top+A.height-F*2/3),this.topLeftBorderStroke=u>0||f>0?tA(A.left+p/2,A.top+E/2,u-p/2,f-E/2,Z.TOP_LEFT):new b(A.left+p/2,A.top+E/2),this.topRightBorderStroke=u>0||f>0?tA(A.left+h,A.top+E/2,c-H/2,o-E/2,Z.TOP_RIGHT):new b(A.left+A.width-H/2,A.top+E/2),this.bottomRightBorderStroke=s>0||B>0?tA(A.left+d,A.top+C,s-H/2,B-F/2,Z.BOTTOM_RIGHT):new b(A.left+A.width-H/2,A.top+A.height-F/2),this.bottomLeftBorderStroke=g>0||w>0?tA(A.left+p/2,A.top+v,g-p/2,w-F/2,Z.BOTTOM_LEFT):new b(A.left+p/2,A.top+A.height-F/2),this.topLeftBorderBox=u>0||f>0?tA(A.left,A.top,u,f,Z.TOP_LEFT):new b(A.left,A.top),this.topRightBorderBox=c>0||o>0?tA(A.left+h,A.top,c,o,Z.TOP_RIGHT):new b(A.left+A.width,A.top),this.bottomRightBorderBox=s>0||B>0?tA(A.left+d,A.top+C,s,B,Z.BOTTOM_RIGHT):new b(A.left+A.width,A.top+A.height),this.bottomLeftBorderBox=g>0||w>0?tA(A.left,A.top+v,g,w,Z.BOTTOM_LEFT):new b(A.left,A.top+A.height),this.topLeftPaddingBox=u>0||f>0?tA(A.left+p,A.top+E,Math.max(0,u-p),Math.max(0,f-E),Z.TOP_LEFT):new b(A.left+p,A.top+E),this.topRightPaddingBox=c>0||o>0?tA(A.left+Math.min(h,A.width-H),A.top+E,h>A.width+H?0:Math.max(0,c-H),Math.max(0,o-E),Z.TOP_RIGHT):new b(A.left+A.width-H,A.top+E),this.bottomRightPaddingBox=s>0||B>0?tA(A.left+Math.min(d,A.width-p),A.top+Math.min(C,A.height-F),Math.max(0,s-H),Math.max(0,B-F),Z.BOTTOM_RIGHT):new b(A.left+A.width-H,A.top+A.height-F),this.bottomLeftPaddingBox=g>0||w>0?tA(A.left+p,A.top+Math.min(v,A.height-F),Math.max(0,g-p),Math.max(0,w-F),Z.BOTTOM_LEFT):new b(A.left+p,A.top+A.height-F),this.topLeftContentBox=u>0||f>0?tA(A.left+p+K,A.top+E+m,Math.max(0,u-(p+K)),Math.max(0,f-(E+m)),Z.TOP_LEFT):new b(A.left+p+K,A.top+E+m),this.topRightContentBox=c>0||o>0?tA(A.left+Math.min(h,A.width+p+K),A.top+E+m,h>A.width+p+K?0:c-p+K,o-(E+m),Z.TOP_RIGHT):new b(A.left+A.width-(H+I),A.top+E+m),this.bottomRightContentBox=s>0||B>0?tA(A.left+Math.min(d,A.width-(p+K)),A.top+Math.min(C,A.height+E+m),Math.max(0,s-(H+I)),B-(F+y),Z.BOTTOM_RIGHT):new b(A.left+A.width-(H+I),A.top+A.height-(F+y)),this.bottomLeftContentBox=g>0||w>0?tA(A.left+p+K,A.top+v,Math.max(0,g-(p+K)),w-(F+y),Z.BOTTOM_LEFT):new b(A.left+p+K,A.top+A.height-(F+y))}return t}(),Z;(function(t){t[t.TOP_LEFT=0]="TOP_LEFT",t[t.TOP_RIGHT=1]="TOP_RIGHT",t[t.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",t[t.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(Z||(Z={}));var tA=function(t,e,n,A,i){var u=4*((Math.sqrt(2)-1)/3),f=n*u,l=A*u,c=t+n,o=e+A;switch(i){case Z.TOP_LEFT:return new sr(new b(t,o),new b(t,o-l),new b(c-f,e),new b(c,e));case Z.TOP_RIGHT:return new sr(new b(t,e),new b(t+f,e),new b(c,o-l),new b(c,o));case Z.BOTTOM_RIGHT:return new sr(new b(c,e),new b(c,e+l),new b(t+f,o),new b(t,o));case Z.BOTTOM_LEFT:default:return new sr(new b(c,o),new b(c-f,o),new b(t,e+l),new b(t,e))}},Fr=function(t){return[t.topLeftBorderBox,t.topRightBorderBox,t.bottomRightBorderBox,t.bottomLeftBorderBox]},Yu=function(t){return[t.topLeftContentBox,t.topRightContentBox,t.bottomRightContentBox,t.bottomLeftContentBox]},pr=function(t){return[t.topLeftPaddingBox,t.topRightPaddingBox,t.bottomRightPaddingBox,t.bottomLeftPaddingBox]},Zu=function(){function t(e,n,A){this.offsetX=e,this.offsetY=n,this.matrix=A,this.type=0,this.target=6}return t}(),or=function(){function t(e,n){this.path=e,this.target=n,this.type=1}return t}(),qu=function(){function t(e){this.opacity=e,this.type=2,this.target=6}return t}(),zu=function(t){return t.type===0},Gi=function(t){return t.type===1},$u=function(t){return t.type===2},On=function(t,e){return t.length===e.length?t.some(function(n,A){return n===e[A]}):!1},ju=function(t,e,n,A,i){return t.map(function(u,f){switch(f){case 0:return u.add(e,n);case 1:return u.add(e+A,n);case 2:return u.add(e+A,n+i);case 3:return u.add(e,n+i)}return u})},Vi=function(){function t(e){this.element=e,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return t}(),Ni=function(){function t(e,n){if(this.container=e,this.parent=n,this.effects=[],this.curves=new Wu(this.container),this.container.styles.opacity<1&&this.effects.push(new qu(this.container.styles.opacity)),this.container.styles.transform!==null){var A=this.container.bounds.left+this.container.styles.transformOrigin[0].number,i=this.container.bounds.top+this.container.styles.transformOrigin[1].number,u=this.container.styles.transform;this.effects.push(new Zu(A,i,u))}if(this.container.styles.overflowX!==0){var f=Fr(this.curves),l=pr(this.curves);On(f,l)?this.effects.push(new or(f,6)):(this.effects.push(new or(f,2)),this.effects.push(new or(l,4)))}}return t.prototype.getEffects=function(e){for(var n=[2,3].indexOf(this.container.styles.position)===-1,A=this.parent,i=this.effects.slice(0);A;){var u=A.effects.filter(function(c){return!Gi(c)});if(n||A.container.styles.position!==0||!A.parent){if(i.unshift.apply(i,u),n=[2,3].indexOf(A.container.styles.position)===-1,A.container.styles.overflowX!==0){var f=Fr(A.curves),l=pr(A.curves);On(f,l)||i.unshift(new or(l,6))}}else i.unshift.apply(i,u);A=A.parent}return i.filter(function(c){return fA(c.target,e)})},t}(),mt=function(t,e,n,A){t.container.elements.forEach(function(i){var u=fA(i.flags,4),f=fA(i.flags,2),l=new Ni(i,t);fA(i.styles.display,2048)&&A.push(l);var c=fA(i.flags,8)?[]:A;if(u||f){var o=u||i.styles.isPositioned()?n:e,r=new Vi(l);if(i.styles.isPositioned()||i.styles.opacity<1||i.styles.isTransformed()){var s=i.styles.zIndex.order;if(s<0){var B=0;o.negativeZIndex.some(function(g,w){return s>g.element.container.styles.zIndex.order?(B=w,!1):B>0}),o.negativeZIndex.splice(B,0,r)}else if(s>0){var a=0;o.positiveZIndex.some(function(g,w){return s>=g.element.container.styles.zIndex.order?(a=w+1,!1):a>0}),o.positiveZIndex.splice(a,0,r)}else o.zeroOrAutoZIndexOrTransformedOrOpacity.push(r)}else i.styles.isFloating()?o.nonPositionedFloats.push(r):o.nonPositionedInlineLevel.push(r);mt(l,r,u?r:n,c)}else i.styles.isInlineLevel()?e.inlineLevel.push(l):e.nonInlineLevel.push(l),mt(l,e,n,c);fA(i.flags,8)&&ki(i,c)})},ki=function(t,e){for(var n=t instanceof dt?t.start:1,A=t instanceof dt?t.reversed:!1,i=0;i<e.length;i++){var u=e[i];u.container instanceof Ei&&typeof u.container.value=="number"&&u.container.value!==0&&(n=u.container.value),u.listValue=_e(n,u.container.styles.listStyleType,!0),n+=A?-1:1}},Af=function(t){var e=new Ni(t,null),n=new Vi(e),A=[];return mt(e,n,n,A),ki(e.container,A),n},Rn=function(t,e){switch(e){case 0:return IA(t.topLeftBorderBox,t.topLeftPaddingBox,t.topRightBorderBox,t.topRightPaddingBox);case 1:return IA(t.topRightBorderBox,t.topRightPaddingBox,t.bottomRightBorderBox,t.bottomRightPaddingBox);case 2:return IA(t.bottomRightBorderBox,t.bottomRightPaddingBox,t.bottomLeftBorderBox,t.bottomLeftPaddingBox);case 3:default:return IA(t.bottomLeftBorderBox,t.bottomLeftPaddingBox,t.topLeftBorderBox,t.topLeftPaddingBox)}},ef=function(t,e){switch(e){case 0:return IA(t.topLeftBorderBox,t.topLeftBorderDoubleOuterBox,t.topRightBorderBox,t.topRightBorderDoubleOuterBox);case 1:return IA(t.topRightBorderBox,t.topRightBorderDoubleOuterBox,t.bottomRightBorderBox,t.bottomRightBorderDoubleOuterBox);case 2:return IA(t.bottomRightBorderBox,t.bottomRightBorderDoubleOuterBox,t.bottomLeftBorderBox,t.bottomLeftBorderDoubleOuterBox);case 3:default:return IA(t.bottomLeftBorderBox,t.bottomLeftBorderDoubleOuterBox,t.topLeftBorderBox,t.topLeftBorderDoubleOuterBox)}},rf=function(t,e){switch(e){case 0:return IA(t.topLeftBorderDoubleInnerBox,t.topLeftPaddingBox,t.topRightBorderDoubleInnerBox,t.topRightPaddingBox);case 1:return IA(t.topRightBorderDoubleInnerBox,t.topRightPaddingBox,t.bottomRightBorderDoubleInnerBox,t.bottomRightPaddingBox);case 2:return IA(t.bottomRightBorderDoubleInnerBox,t.bottomRightPaddingBox,t.bottomLeftBorderDoubleInnerBox,t.bottomLeftPaddingBox);case 3:default:return IA(t.bottomLeftBorderDoubleInnerBox,t.bottomLeftPaddingBox,t.topLeftBorderDoubleInnerBox,t.topLeftPaddingBox)}},tf=function(t,e){switch(e){case 0:return Br(t.topLeftBorderStroke,t.topRightBorderStroke);case 1:return Br(t.topRightBorderStroke,t.bottomRightBorderStroke);case 2:return Br(t.bottomRightBorderStroke,t.bottomLeftBorderStroke);case 3:default:return Br(t.bottomLeftBorderStroke,t.topLeftBorderStroke)}},Br=function(t,e){var n=[];return mA(t)?n.push(t.subdivide(.5,!1)):n.push(t),mA(e)?n.push(e.subdivide(.5,!0)):n.push(e),n},IA=function(t,e,n,A){var i=[];return mA(t)?i.push(t.subdivide(.5,!1)):i.push(t),mA(n)?i.push(n.subdivide(.5,!0)):i.push(n),mA(A)?i.push(A.subdivide(.5,!0).reverse()):i.push(A),mA(e)?i.push(e.subdivide(.5,!1).reverse()):i.push(e),i},Pi=function(t){var e=t.bounds,n=t.styles;return e.add(n.borderLeftWidth,n.borderTopWidth,-(n.borderRightWidth+n.borderLeftWidth),-(n.borderTopWidth+n.borderBottomWidth))},vr=function(t){var e=t.styles,n=t.bounds,A=eA(e.paddingLeft,n.width),i=eA(e.paddingRight,n.width),u=eA(e.paddingTop,n.width),f=eA(e.paddingBottom,n.width);return n.add(A+e.borderLeftWidth,u+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+A+i),-(e.borderTopWidth+e.borderBottomWidth+u+f))},nf=function(t,e){return t===0?e.bounds:t===2?vr(e):Pi(e)},sf=function(t,e){return t===0?e.bounds:t===2?vr(e):Pi(e)},et=function(t,e,n){var A=nf(ce(t.styles.backgroundOrigin,e),t),i=sf(ce(t.styles.backgroundClip,e),t),u=of(ce(t.styles.backgroundSize,e),n,A),f=u[0],l=u[1],c=Ie(ce(t.styles.backgroundPosition,e),A.width-f,A.height-l),o=Bf(ce(t.styles.backgroundRepeat,e),c,u,A,i),r=Math.round(A.left+c[0]),s=Math.round(A.top+c[1]);return[o,r,s,f,l]},ue=function(t){return z(t)&&t.value===ge.AUTO},ar=function(t){return typeof t=="number"},of=function(t,e,n){var A=e[0],i=e[1],u=e[2],f=t[0],l=t[1];if(!f)return[0,0];if(aA(f)&&l&&aA(l))return[eA(f,n.width),eA(l,n.height)];var c=ar(u);if(z(f)&&(f.value===ge.CONTAIN||f.value===ge.COVER)){if(ar(u)){var o=n.width/n.height;return o<u!=(f.value===ge.COVER)?[n.width,n.width/u]:[n.height*u,n.height]}return[n.width,n.height]}var r=ar(A),s=ar(i),B=r||s;if(ue(f)&&(!l||ue(l))){if(r&&s)return[A,i];if(!c&&!B)return[n.width,n.height];if(B&&c){var a=r?A:i*u,g=s?i:A/u;return[a,g]}var w=r?A:n.width,Q=s?i:n.height;return[w,Q]}if(c){var U=0,h=0;return aA(f)?U=eA(f,n.width):aA(l)&&(h=eA(l,n.height)),ue(f)?U=h*u:(!l||ue(l))&&(h=U/u),[U,h]}var C=null,d=null;if(aA(f)?C=eA(f,n.width):l&&aA(l)&&(d=eA(l,n.height)),C!==null&&(!l||ue(l))&&(d=r&&s?C/A*i:n.height),d!==null&&ue(f)&&(C=r&&s?d/i*A:n.width),C!==null&&d!==null)return[C,d];throw new Error("Unable to calculate background-size for element")},ce=function(t,e){var n=t[e];return typeof n>"u"?t[0]:n},Bf=function(t,e,n,A,i){var u=e[0],f=e[1],l=n[0],c=n[1];switch(t){case 2:return[new b(Math.round(A.left),Math.round(A.top+f)),new b(Math.round(A.left+A.width),Math.round(A.top+f)),new b(Math.round(A.left+A.width),Math.round(c+A.top+f)),new b(Math.round(A.left),Math.round(c+A.top+f))];case 3:return[new b(Math.round(A.left+u),Math.round(A.top)),new b(Math.round(A.left+u+l),Math.round(A.top)),new b(Math.round(A.left+u+l),Math.round(A.height+A.top)),new b(Math.round(A.left+u),Math.round(A.height+A.top))];case 1:return[new b(Math.round(A.left+u),Math.round(A.top+f)),new b(Math.round(A.left+u+l),Math.round(A.top+f)),new b(Math.round(A.left+u+l),Math.round(A.top+f+c)),new b(Math.round(A.left+u),Math.round(A.top+f+c))];default:return[new b(Math.round(i.left),Math.round(i.top)),new b(Math.round(i.left+i.width),Math.round(i.top)),new b(Math.round(i.left+i.width),Math.round(i.height+i.top)),new b(Math.round(i.left),Math.round(i.height+i.top))]}},af="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",_n="Hidden Text",uf=function(){function t(e){this._data={},this._document=e}return t.prototype.parseMetrics=function(e,n){var A=this._document.createElement("div"),i=this._document.createElement("img"),u=this._document.createElement("span"),f=this._document.body;A.style.visibility="hidden",A.style.fontFamily=e,A.style.fontSize=n,A.style.margin="0",A.style.padding="0",A.style.whiteSpace="nowrap",f.appendChild(A),i.src=af,i.width=1,i.height=1,i.style.margin="0",i.style.padding="0",i.style.verticalAlign="baseline",u.style.fontFamily=e,u.style.fontSize=n,u.style.margin="0",u.style.padding="0",u.appendChild(this._document.createTextNode(_n)),A.appendChild(u),A.appendChild(i);var l=i.offsetTop-u.offsetTop+2;A.removeChild(u),A.appendChild(this._document.createTextNode(_n)),A.style.lineHeight="normal",i.style.verticalAlign="super";var c=i.offsetTop-A.offsetTop+2;return f.removeChild(A),{baseline:l,middle:c}},t.prototype.getMetrics=function(e,n){var A=e+" "+n;return typeof this._data[A]>"u"&&(this._data[A]=this.parseMetrics(e,n)),this._data[A]},t}(),Si=function(){function t(e,n){this.context=e,this.options=n}return t}(),ff=1e4,cf=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i._activeEffects=[],i.canvas=A.canvas?A.canvas:document.createElement("canvas"),i.ctx=i.canvas.getContext("2d"),A.canvas||(i.canvas.width=Math.floor(A.width*A.scale),i.canvas.height=Math.floor(A.height*A.scale),i.canvas.style.width=A.width+"px",i.canvas.style.height=A.height+"px"),i.fontMetrics=new uf(document),i.ctx.scale(i.options.scale,i.options.scale),i.ctx.translate(-A.x,-A.y),i.ctx.textBaseline="bottom",i._activeEffects=[],i.context.logger.debug("Canvas renderer initialized ("+A.width+"x"+A.height+") with scale "+A.scale),i}return e.prototype.applyEffects=function(n){for(var A=this;this._activeEffects.length;)this.popEffect();n.forEach(function(i){return A.applyEffect(i)})},e.prototype.applyEffect=function(n){this.ctx.save(),$u(n)&&(this.ctx.globalAlpha=n.opacity),zu(n)&&(this.ctx.translate(n.offsetX,n.offsetY),this.ctx.transform(n.matrix[0],n.matrix[1],n.matrix[2],n.matrix[3],n.matrix[4],n.matrix[5]),this.ctx.translate(-n.offsetX,-n.offsetY)),Gi(n)&&(this.path(n.path),this.ctx.clip()),this._activeEffects.push(n)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(n){return dA(this,void 0,void 0,function(){var A;return UA(this,function(i){switch(i.label){case 0:return A=n.element.container.styles,A.isVisible()?[4,this.renderStackContent(n)]:[3,2];case 1:i.sent(),i.label=2;case 2:return[2]}})})},e.prototype.renderNode=function(n){return dA(this,void 0,void 0,function(){return UA(this,function(A){switch(A.label){case 0:if(fA(n.container.flags,16))debugger;return n.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(n)]:[3,3];case 1:return A.sent(),[4,this.renderNodeContent(n)];case 2:A.sent(),A.label=3;case 3:return[2]}})})},e.prototype.renderTextWithLetterSpacing=function(n,A,i){var u=this;if(A===0)this.ctx.fillText(n.text,n.bounds.left,n.bounds.top+i);else{var f=bt(n.text);f.reduce(function(l,c){return u.ctx.fillText(c,l,n.bounds.top+i),l+u.ctx.measureText(c).width},n.bounds.left)}},e.prototype.createFontStyle=function(n){var A=n.fontVariant.filter(function(f){return f==="normal"||f==="small-caps"}).join(""),i=Cf(n.fontFamily).join(", "),u=Ve(n.fontSize)?""+n.fontSize.number+n.fontSize.unit:n.fontSize.number+"px";return[[n.fontStyle,A,n.fontWeight,u,i].join(" "),i,u]},e.prototype.renderTextNode=function(n,A){return dA(this,void 0,void 0,function(){var i,u,f,l,c,o,r,s,B=this;return UA(this,function(a){return i=this.createFontStyle(A),u=i[0],f=i[1],l=i[2],this.ctx.font=u,this.ctx.direction=A.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",c=this.fontMetrics.getMetrics(f,l),o=c.baseline,r=c.middle,s=A.paintOrder,n.textBounds.forEach(function(g){s.forEach(function(w){switch(w){case 0:B.ctx.fillStyle=lA(A.color),B.renderTextWithLetterSpacing(g,A.letterSpacing,o);var Q=A.textShadow;Q.length&&g.text.trim().length&&(Q.slice(0).reverse().forEach(function(U){B.ctx.shadowColor=lA(U.color),B.ctx.shadowOffsetX=U.offsetX.number*B.options.scale,B.ctx.shadowOffsetY=U.offsetY.number*B.options.scale,B.ctx.shadowBlur=U.blur.number,B.renderTextWithLetterSpacing(g,A.letterSpacing,o)}),B.ctx.shadowColor="",B.ctx.shadowOffsetX=0,B.ctx.shadowOffsetY=0,B.ctx.shadowBlur=0),A.textDecorationLine.length&&(B.ctx.fillStyle=lA(A.textDecorationColor||A.color),A.textDecorationLine.forEach(function(U){switch(U){case 1:B.ctx.fillRect(g.bounds.left,Math.round(g.bounds.top+o),g.bounds.width,1);break;case 2:B.ctx.fillRect(g.bounds.left,Math.round(g.bounds.top),g.bounds.width,1);break;case 3:B.ctx.fillRect(g.bounds.left,Math.ceil(g.bounds.top+r),g.bounds.width,1);break}}));break;case 1:A.webkitTextStrokeWidth&&g.text.trim().length&&(B.ctx.strokeStyle=lA(A.webkitTextStrokeColor),B.ctx.lineWidth=A.webkitTextStrokeWidth,B.ctx.lineJoin=window.chrome?"miter":"round",B.ctx.strokeText(g.text,g.bounds.left,g.bounds.top+o)),B.ctx.strokeStyle="",B.ctx.lineWidth=0,B.ctx.lineJoin="miter";break}})}),[2]})})},e.prototype.renderReplacedElement=function(n,A,i){if(i&&n.intrinsicWidth>0&&n.intrinsicHeight>0){var u=vr(n),f=pr(A);this.path(f),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(i,0,0,n.intrinsicWidth,n.intrinsicHeight,u.left,u.top,u.width,u.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(n){return dA(this,void 0,void 0,function(){var A,i,u,f,l,c,h,h,o,r,s,B,d,a,g,v,w,Q,U,h,C,d,v;return UA(this,function(E){switch(E.label){case 0:this.applyEffects(n.getEffects(4)),A=n.container,i=n.curves,u=A.styles,f=0,l=A.textNodes,E.label=1;case 1:return f<l.length?(c=l[f],[4,this.renderTextNode(c,u)]):[3,4];case 2:E.sent(),E.label=3;case 3:return f++,[3,1];case 4:if(!(A instanceof Fi))return[3,8];E.label=5;case 5:return E.trys.push([5,7,,8]),[4,this.context.cache.match(A.src)];case 6:return h=E.sent(),this.renderReplacedElement(A,i,h),[3,8];case 7:return E.sent(),this.context.logger.error("Error loading image "+A.src),[3,8];case 8:if(A instanceof pi&&this.renderReplacedElement(A,i,A.canvas),!(A instanceof vi))return[3,12];E.label=9;case 9:return E.trys.push([9,11,,12]),[4,this.context.cache.match(A.svg)];case 10:return h=E.sent(),this.renderReplacedElement(A,i,h),[3,12];case 11:return E.sent(),this.context.logger.error("Error loading svg "+A.svg.substring(0,255)),[3,12];case 12:return A instanceof Ii&&A.tree?(o=new e(this.context,{scale:this.options.scale,backgroundColor:A.backgroundColor,x:0,y:0,width:A.width,height:A.height}),[4,o.render(A.tree)]):[3,14];case 13:r=E.sent(),A.width&&A.height&&this.ctx.drawImage(r,0,0,A.width,A.height,A.bounds.left,A.bounds.top,A.bounds.width,A.bounds.height),E.label=14;case 14:if(A instanceof xt&&(s=Math.min(A.bounds.width,A.bounds.height),A.type===Cr?A.checked&&(this.ctx.save(),this.path([new b(A.bounds.left+s*.39363,A.bounds.top+s*.79),new b(A.bounds.left+s*.16,A.bounds.top+s*.5549),new b(A.bounds.left+s*.27347,A.bounds.top+s*.44071),new b(A.bounds.left+s*.39694,A.bounds.top+s*.5649),new b(A.bounds.left+s*.72983,A.bounds.top+s*.23),new b(A.bounds.left+s*.84,A.bounds.top+s*.34085),new b(A.bounds.left+s*.39363,A.bounds.top+s*.79)]),this.ctx.fillStyle=lA(En),this.ctx.fill(),this.ctx.restore()):A.type===Ur&&A.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(A.bounds.left+s/2,A.bounds.top+s/2,s/4,0,Math.PI*2,!0),this.ctx.fillStyle=lA(En),this.ctx.fill(),this.ctx.restore())),lf(A)&&A.value.length){switch(B=this.createFontStyle(u),d=B[0],a=B[1],g=this.fontMetrics.getMetrics(d,a).baseline,this.ctx.font=d,this.ctx.fillStyle=lA(u.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=wf(A.styles.textAlign),v=vr(A),w=0,A.styles.textAlign){case 1:w+=v.width/2;break;case 2:w+=v.width;break}Q=v.add(w,0,0,-v.height/2+1),this.ctx.save(),this.path([new b(v.left,v.top),new b(v.left+v.width,v.top),new b(v.left+v.width,v.top+v.height),new b(v.left,v.top+v.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new De(A.value,Q),u.letterSpacing,g),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!fA(A.styles.display,2048))return[3,20];if(A.styles.listStyleImage===null)return[3,19];if(U=A.styles.listStyleImage,U.type!==0)return[3,18];h=void 0,C=U.url,E.label=15;case 15:return E.trys.push([15,17,,18]),[4,this.context.cache.match(C)];case 16:return h=E.sent(),this.ctx.drawImage(h,A.bounds.left-(h.width+10),A.bounds.top),[3,18];case 17:return E.sent(),this.context.logger.error("Error loading list-style-image "+C),[3,18];case 18:return[3,20];case 19:n.listValue&&A.styles.listStyleType!==-1&&(d=this.createFontStyle(u)[0],this.ctx.font=d,this.ctx.fillStyle=lA(u.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",v=new kA(A.bounds.left,A.bounds.top+eA(A.styles.paddingTop,A.bounds.width),A.bounds.width,on(u.lineHeight,u.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new De(n.listValue,v),u.letterSpacing,on(u.lineHeight,u.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),E.label=20;case 20:return[2]}})})},e.prototype.renderStackContent=function(n){return dA(this,void 0,void 0,function(){var A,i,U,u,f,U,l,c,U,o,r,U,s,B,U,a,g,U,w,Q,U;return UA(this,function(h){switch(h.label){case 0:if(fA(n.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(n.element)];case 1:h.sent(),A=0,i=n.negativeZIndex,h.label=2;case 2:return A<i.length?(U=i[A],[4,this.renderStack(U)]):[3,5];case 3:h.sent(),h.label=4;case 4:return A++,[3,2];case 5:return[4,this.renderNodeContent(n.element)];case 6:h.sent(),u=0,f=n.nonInlineLevel,h.label=7;case 7:return u<f.length?(U=f[u],[4,this.renderNode(U)]):[3,10];case 8:h.sent(),h.label=9;case 9:return u++,[3,7];case 10:l=0,c=n.nonPositionedFloats,h.label=11;case 11:return l<c.length?(U=c[l],[4,this.renderStack(U)]):[3,14];case 12:h.sent(),h.label=13;case 13:return l++,[3,11];case 14:o=0,r=n.nonPositionedInlineLevel,h.label=15;case 15:return o<r.length?(U=r[o],[4,this.renderStack(U)]):[3,18];case 16:h.sent(),h.label=17;case 17:return o++,[3,15];case 18:s=0,B=n.inlineLevel,h.label=19;case 19:return s<B.length?(U=B[s],[4,this.renderNode(U)]):[3,22];case 20:h.sent(),h.label=21;case 21:return s++,[3,19];case 22:a=0,g=n.zeroOrAutoZIndexOrTransformedOrOpacity,h.label=23;case 23:return a<g.length?(U=g[a],[4,this.renderStack(U)]):[3,26];case 24:h.sent(),h.label=25;case 25:return a++,[3,23];case 26:w=0,Q=n.positiveZIndex,h.label=27;case 27:return w<Q.length?(U=Q[w],[4,this.renderStack(U)]):[3,30];case 28:h.sent(),h.label=29;case 29:return w++,[3,27];case 30:return[2]}})})},e.prototype.mask=function(n){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(n.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(n){this.ctx.beginPath(),this.formatPath(n),this.ctx.closePath()},e.prototype.formatPath=function(n){var A=this;n.forEach(function(i,u){var f=mA(i)?i.start:i;u===0?A.ctx.moveTo(f.x,f.y):A.ctx.lineTo(f.x,f.y),mA(i)&&A.ctx.bezierCurveTo(i.startControl.x,i.startControl.y,i.endControl.x,i.endControl.y,i.end.x,i.end.y)})},e.prototype.renderRepeat=function(n,A,i,u){this.path(n),this.ctx.fillStyle=A,this.ctx.translate(i,u),this.ctx.fill(),this.ctx.translate(-i,-u)},e.prototype.resizeImage=function(n,A,i){var u;if(n.width===A&&n.height===i)return n;var f=(u=this.canvas.ownerDocument)!==null&&u!==void 0?u:document,l=f.createElement("canvas");l.width=Math.max(1,A),l.height=Math.max(1,i);var c=l.getContext("2d");return c.drawImage(n,0,0,n.width,n.height,0,0,A,i),l},e.prototype.renderBackgroundImage=function(n){return dA(this,void 0,void 0,function(){var A,i,u,f,l,c;return UA(this,function(o){switch(o.label){case 0:A=n.styles.backgroundImage.length-1,i=function(r){var s,B,a,m,W,D,K,_,F,g,m,W,D,K,_,w,Q,U,h,C,d,v,E,H,F,p,m,I,y,K,_,$,W,D,N,T,k,O,R,L,M,P;return UA(this,function(q){switch(q.label){case 0:if(r.type!==0)return[3,5];s=void 0,B=r.url,q.label=1;case 1:return q.trys.push([1,3,,4]),[4,u.context.cache.match(B)];case 2:return s=q.sent(),[3,4];case 3:return q.sent(),u.context.logger.error("Error loading background-image "+B),[3,4];case 4:return s&&(a=et(n,A,[s.width,s.height,s.width/s.height]),m=a[0],W=a[1],D=a[2],K=a[3],_=a[4],F=u.ctx.createPattern(u.resizeImage(s,K,_),"repeat"),u.renderRepeat(m,F,W,D)),[3,6];case 5:zo(r)?(g=et(n,A,[null,null,null]),m=g[0],W=g[1],D=g[2],K=g[3],_=g[4],w=Xo(r.angle,K,_),Q=w[0],U=w[1],h=w[2],C=w[3],d=w[4],v=document.createElement("canvas"),v.width=K,v.height=_,E=v.getContext("2d"),H=E.createLinearGradient(U,C,h,d),nn(r.stops,Q).forEach(function(rA){return H.addColorStop(rA.stop,lA(rA.color))}),E.fillStyle=H,E.fillRect(0,0,K,_),K>0&&_>0&&(F=u.ctx.createPattern(v,"repeat"),u.renderRepeat(m,F,W,D))):$o(r)&&(p=et(n,A,[null,null,null]),m=p[0],I=p[1],y=p[2],K=p[3],_=p[4],$=r.position.length===0?[yt]:r.position,W=eA($[0],K),D=eA($[$.length-1],_),N=Wo(r,W,D,K,_),T=N[0],k=N[1],T>0&&k>0&&(O=u.ctx.createRadialGradient(I+W,y+D,0,I+W,y+D,T),nn(r.stops,T*2).forEach(function(rA){return O.addColorStop(rA.stop,lA(rA.color))}),u.path(m),u.ctx.fillStyle=O,T!==k?(R=n.bounds.left+.5*n.bounds.width,L=n.bounds.top+.5*n.bounds.height,M=k/T,P=1/M,u.ctx.save(),u.ctx.translate(R,L),u.ctx.transform(1,0,0,M,0,0),u.ctx.translate(-R,-L),u.ctx.fillRect(I,P*(y-L)+L,K,_*P),u.ctx.restore()):u.ctx.fill())),q.label=6;case 6:return A--,[2]}})},u=this,f=0,l=n.styles.backgroundImage.slice(0).reverse(),o.label=1;case 1:return f<l.length?(c=l[f],[5,i(c)]):[3,4];case 2:o.sent(),o.label=3;case 3:return f++,[3,1];case 4:return[2]}})})},e.prototype.renderSolidBorder=function(n,A,i){return dA(this,void 0,void 0,function(){return UA(this,function(u){return this.path(Rn(i,A)),this.ctx.fillStyle=lA(n),this.ctx.fill(),[2]})})},e.prototype.renderDoubleBorder=function(n,A,i,u){return dA(this,void 0,void 0,function(){var f,l;return UA(this,function(c){switch(c.label){case 0:return A<3?[4,this.renderSolidBorder(n,i,u)]:[3,2];case 1:return c.sent(),[2];case 2:return f=ef(u,i),this.path(f),this.ctx.fillStyle=lA(n),this.ctx.fill(),l=rf(u,i),this.path(l),this.ctx.fill(),[2]}})})},e.prototype.renderNodeBackgroundAndBorders=function(n){return dA(this,void 0,void 0,function(){var A,i,u,f,l,c,o,r,s=this;return UA(this,function(B){switch(B.label){case 0:return this.applyEffects(n.getEffects(2)),A=n.container.styles,i=!qA(A.backgroundColor)||A.backgroundImage.length,u=[{style:A.borderTopStyle,color:A.borderTopColor,width:A.borderTopWidth},{style:A.borderRightStyle,color:A.borderRightColor,width:A.borderRightWidth},{style:A.borderBottomStyle,color:A.borderBottomColor,width:A.borderBottomWidth},{style:A.borderLeftStyle,color:A.borderLeftColor,width:A.borderLeftWidth}],f=gf(ce(A.backgroundClip,0),n.curves),i||A.boxShadow.length?(this.ctx.save(),this.path(f),this.ctx.clip(),qA(A.backgroundColor)||(this.ctx.fillStyle=lA(A.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(n.container)]):[3,2];case 1:B.sent(),this.ctx.restore(),A.boxShadow.slice(0).reverse().forEach(function(a){s.ctx.save();var g=Fr(n.curves),w=a.inset?0:ff,Q=ju(g,-w+(a.inset?1:-1)*a.spread.number,(a.inset?1:-1)*a.spread.number,a.spread.number*(a.inset?-2:2),a.spread.number*(a.inset?-2:2));a.inset?(s.path(g),s.ctx.clip(),s.mask(Q)):(s.mask(g),s.ctx.clip(),s.path(Q)),s.ctx.shadowOffsetX=a.offsetX.number+w,s.ctx.shadowOffsetY=a.offsetY.number,s.ctx.shadowColor=lA(a.color),s.ctx.shadowBlur=a.blur.number,s.ctx.fillStyle=a.inset?lA(a.color):"rgba(0,0,0,1)",s.ctx.fill(),s.ctx.restore()}),B.label=2;case 2:l=0,c=0,o=u,B.label=3;case 3:return c<o.length?(r=o[c],r.style!==0&&!qA(r.color)&&r.width>0?r.style!==2?[3,5]:[4,this.renderDashedDottedBorder(r.color,r.width,l,n.curves,2)]:[3,11]):[3,13];case 4:return B.sent(),[3,11];case 5:return r.style!==3?[3,7]:[4,this.renderDashedDottedBorder(r.color,r.width,l,n.curves,3)];case 6:return B.sent(),[3,11];case 7:return r.style!==4?[3,9]:[4,this.renderDoubleBorder(r.color,r.width,l,n.curves)];case 8:return B.sent(),[3,11];case 9:return[4,this.renderSolidBorder(r.color,l,n.curves)];case 10:B.sent(),B.label=11;case 11:l++,B.label=12;case 12:return c++,[3,3];case 13:return[2]}})})},e.prototype.renderDashedDottedBorder=function(n,A,i,u,f){return dA(this,void 0,void 0,function(){var l,c,o,r,s,B,a,g,w,Q,U,h,C,d,v,E,v,E;return UA(this,function(H){return this.ctx.save(),l=tf(u,i),c=Rn(u,i),f===2&&(this.path(c),this.ctx.clip()),mA(c[0])?(o=c[0].start.x,r=c[0].start.y):(o=c[0].x,r=c[0].y),mA(c[1])?(s=c[1].end.x,B=c[1].end.y):(s=c[1].x,B=c[1].y),i===0||i===2?a=Math.abs(o-s):a=Math.abs(r-B),this.ctx.beginPath(),f===3?this.formatPath(l):this.formatPath(c.slice(0,2)),g=A<3?A*3:A*2,w=A<3?A*2:A,f===3&&(g=A,w=A),Q=!0,a<=g*2?Q=!1:a<=g*2+w?(U=a/(2*g+w),g*=U,w*=U):(h=Math.floor((a+w)/(g+w)),C=(a-h*g)/(h-1),d=(a-(h+1)*g)/h,w=d<=0||Math.abs(w-C)<Math.abs(w-d)?C:d),Q&&(f===3?this.ctx.setLineDash([0,g+w]):this.ctx.setLineDash([g,w])),f===3?(this.ctx.lineCap="round",this.ctx.lineWidth=A):this.ctx.lineWidth=A*2+1.1,this.ctx.strokeStyle=lA(n),this.ctx.stroke(),this.ctx.setLineDash([]),f===2&&(mA(c[0])&&(v=c[3],E=c[0],this.ctx.beginPath(),this.formatPath([new b(v.end.x,v.end.y),new b(E.start.x,E.start.y)]),this.ctx.stroke()),mA(c[1])&&(v=c[1],E=c[2],this.ctx.beginPath(),this.formatPath([new b(v.end.x,v.end.y),new b(E.start.x,E.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},e.prototype.render=function(n){return dA(this,void 0,void 0,function(){var A;return UA(this,function(i){switch(i.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=lA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),A=Af(n),[4,this.renderStack(A)];case 1:return i.sent(),this.applyEffects([]),[2,this.canvas]}})})},e}(Si),lf=function(t){return t instanceof Hi||t instanceof mi?!0:t instanceof xt&&t.type!==Ur&&t.type!==Cr},gf=function(t,e){switch(t){case 0:return Fr(e);case 2:return Yu(e);case 1:default:return pr(e)}},wf=function(t){switch(t){case 1:return"center";case 2:return"right";case 0:default:return"left"}},Qf=["-apple-system","system-ui"],Cf=function(t){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?t.filter(function(e){return Qf.indexOf(e)===-1}):t},Uf=function(t){LA(e,t);function e(n,A){var i=t.call(this,n,A)||this;return i.canvas=A.canvas?A.canvas:document.createElement("canvas"),i.ctx=i.canvas.getContext("2d"),i.options=A,i.canvas.width=Math.floor(A.width*A.scale),i.canvas.height=Math.floor(A.height*A.scale),i.canvas.style.width=A.width+"px",i.canvas.style.height=A.height+"px",i.ctx.scale(i.options.scale,i.options.scale),i.ctx.translate(-A.x,-A.y),i.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+A.width+"x"+A.height+" at "+A.x+","+A.y+") with scale "+A.scale),i}return e.prototype.render=function(n){return dA(this,void 0,void 0,function(){var A,i;return UA(this,function(u){switch(u.label){case 0:return A=ht(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,n),[4,hf(A)];case 1:return i=u.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=lA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(i,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},e}(Si),hf=function(t){return new Promise(function(e,n){var A=new Image;A.onload=function(){e(A)},A.onerror=n,A.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(t))})},df=function(){function t(e){var n=e.id,A=e.enabled;this.id=n,this.enabled=A,this.start=Date.now()}return t.prototype.debug=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,ke([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},t.prototype.getTime=function(){return Date.now()-this.start},t.prototype.info=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,ke([this.id,this.getTime()+"ms"],e))},t.prototype.warn=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,ke([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},t.prototype.error=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,ke([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},t.instances={},t}(),Ff=function(){function t(e,n){var A;this.windowBounds=n,this.instanceName="#"+t.instanceCount++,this.logger=new df({id:this.instanceName,enabled:e.logging}),this.cache=(A=e.cache)!==null&&A!==void 0?A:new Nu(this,e)}return t.instanceCount=1,t}(),Lf=function(t,e){return e===void 0&&(e={}),pf(t,e)};typeof window<"u"&&_i.setContext(window);var pf=function(t,e){return dA(void 0,void 0,void 0,function(){var n,A,i,u,f,l,c,o,r,s,B,a,g,w,Q,U,h,C,d,v,H,E,H,F,p,m,I,y,K,_,$,W,D,N,T,k,O,R,L,M;return UA(this,function(P){switch(P.label){case 0:if(!t||typeof t!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(n=t.ownerDocument,!n)throw new Error("Element is not attached to a Document");if(A=n.defaultView,!A)throw new Error("Document is not attached to a Window");return i={allowTaint:(F=e.allowTaint)!==null&&F!==void 0?F:!1,imageTimeout:(p=e.imageTimeout)!==null&&p!==void 0?p:15e3,proxy:e.proxy,useCORS:(m=e.useCORS)!==null&&m!==void 0?m:!1},u=nt({logging:(I=e.logging)!==null&&I!==void 0?I:!0,cache:e.cache},i),f={windowWidth:(y=e.windowWidth)!==null&&y!==void 0?y:A.innerWidth,windowHeight:(K=e.windowHeight)!==null&&K!==void 0?K:A.innerHeight,scrollX:(_=e.scrollX)!==null&&_!==void 0?_:A.pageXOffset,scrollY:($=e.scrollY)!==null&&$!==void 0?$:A.pageYOffset},l=new kA(f.scrollX,f.scrollY,f.windowWidth,f.windowHeight),c=new Ff(u,l),o=(W=e.foreignObjectRendering)!==null&&W!==void 0?W:!1,r={allowTaint:(D=e.allowTaint)!==null&&D!==void 0?D:!1,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:o,copyStyles:o},c.logger.debug("Starting document clone with size "+l.width+"x"+l.height+" scrolled to "+-l.left+","+-l.top),s=new Dn(c,t,r),B=s.clonedReferenceElement,B?[4,s.toIFrame(n,l)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return a=P.sent(),g=Dt(B)||vu(B)?$i(B.ownerDocument):Er(c,B),w=g.width,Q=g.height,U=g.left,h=g.top,C=vf(c,B,e.backgroundColor),d={canvas:e.canvas,backgroundColor:C,scale:(T=(N=e.scale)!==null&&N!==void 0?N:A.devicePixelRatio)!==null&&T!==void 0?T:1,x:((k=e.x)!==null&&k!==void 0?k:0)+U,y:((O=e.y)!==null&&O!==void 0?O:0)+h,width:(R=e.width)!==null&&R!==void 0?R:Math.ceil(w),height:(L=e.height)!==null&&L!==void 0?L:Math.ceil(Q)},o?(c.logger.debug("Document cloned, using foreign object rendering"),H=new Uf(c,d),[4,H.render(B)]):[3,3];case 2:return v=P.sent(),[3,5];case 3:return c.logger.debug("Document cloned, element located at "+U+","+h+" with size "+w+"x"+Q+" using computed rendering"),c.logger.debug("Starting DOM parsing"),E=Ki(c,B),C===E.styles.backgroundColor&&(E.styles.backgroundColor=NA.TRANSPARENT),c.logger.debug("Starting renderer for element at "+d.x+","+d.y+" with size "+d.width+"x"+d.height),H=new cf(c,d),[4,H.render(E)];case 4:v=P.sent(),P.label=5;case 5:return(!((M=e.removeContainer)!==null&&M!==void 0)||M)&&(Dn.destroy(a)||c.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),c.logger.debug("Finished rendering"),[2,v]}})})},vf=function(t,e,n){var A=e.ownerDocument,i=A.documentElement?be(t,getComputedStyle(A.documentElement).backgroundColor):NA.TRANSPARENT,u=A.body?be(t,getComputedStyle(A.body).backgroundColor):NA.TRANSPARENT,f=typeof n=="string"?be(t,n):n===null?NA.TRANSPARENT:4294967295;return e===A.documentElement?qA(i)?qA(u)?f:u:i:f},Mi={exports:{}};(function(t,e){(function(n,A){t.exports=A()})(Gn,function(){var n=function(A,i){var u=Array.prototype.concat,f=Array.prototype.slice,l=Object.prototype.toString;function c(h,C){var d=h>C?h:C;return A.pow(10,17-~~(A.log(d>0?d:-d)*A.LOG10E))}var o=Array.isArray||function(C){return l.call(C)==="[object Array]"};function r(h){return l.call(h)==="[object Function]"}function s(h){return typeof h=="number"?h-h===0:!1}function B(h){return u.apply([],h)}function a(){return new a._init(arguments)}a.fn=a.prototype,a._init=function(C){if(o(C[0]))if(o(C[0][0])){r(C[1])&&(C[0]=a.map(C[0],C[1]));for(var d=0;d<C[0].length;d++)this[d]=C[0][d];this.length=C[0].length}else this[0]=r(C[1])?a.map(C[0],C[1]):C[0],this.length=1;else if(s(C[0]))this[0]=a.seq.apply(null,C),this.length=1;else{if(C[0]instanceof a)return a(C[0].toArray());this[0]=[],this.length=1}return this},a._init.prototype=a.prototype,a._init.constructor=a,a.utils={calcRdx:c,isArray:o,isFunction:r,isNumber:s,toVector:B},a._random_fn=A.random,a.setRandom=function(C){if(typeof C!="function")throw new TypeError("fn is not a function");a._random_fn=C},a.extend=function(C){var d,v;if(arguments.length===1){for(v in C)a[v]=C[v];return this}for(d=1;d<arguments.length;d++)for(v in arguments[d])C[v]=arguments[d][v];return C},a.rows=function(C){return C.length||1},a.cols=function(C){return C[0].length||1},a.dimensions=function(C){return{rows:a.rows(C),cols:a.cols(C)}},a.row=function(C,d){return o(d)?d.map(function(v){return a.row(C,v)}):C[d]},a.rowa=function(C,d){return a.row(C,d)},a.col=function(C,d){if(o(d)){var v=a.arange(C.length).map(function(){return new Array(d.length)});return d.forEach(function(F,p){a.arange(C.length).forEach(function(m){v[m][p]=C[m][F]})}),v}for(var E=new Array(C.length),H=0;H<C.length;H++)E[H]=[C[H][d]];return E},a.cola=function(C,d){return a.col(C,d).map(function(v){return v[0]})},a.diag=function(C){for(var d=a.rows(C),v=new Array(d),E=0;E<d;E++)v[E]=[C[E][E]];return v},a.antidiag=function(C){for(var d=a.rows(C)-1,v=new Array(d),E=0;d>=0;d--,E++)v[E]=[C[E][d]];return v},a.transpose=function(C){var d=[],v,E,H,F,p;for(o(C[0])||(C=[C]),E=C.length,H=C[0].length,p=0;p<H;p++){for(v=new Array(E),F=0;F<E;F++)v[F]=C[F][p];d.push(v)}return d.length===1?d[0]:d},a.map=function(C,d,v){var E,H,F,p,m;for(o(C[0])||(C=[C]),H=C.length,F=C[0].length,p=v?C:new Array(H),E=0;E<H;E++)for(p[E]||(p[E]=new Array(F)),m=0;m<F;m++)p[E][m]=d(C[E][m],E,m);return p.length===1?p[0]:p},a.cumreduce=function(C,d,v){var E,H,F,p,m;for(o(C[0])||(C=[C]),H=C.length,F=C[0].length,p=v?C:new Array(H),E=0;E<H;E++)for(p[E]||(p[E]=new Array(F)),F>0&&(p[E][0]=C[E][0]),m=1;m<F;m++)p[E][m]=d(p[E][m-1],C[E][m]);return p.length===1?p[0]:p},a.alter=function(C,d){return a.map(C,d,!0)},a.create=function(C,d,v){var E=new Array(C),H,F;for(r(d)&&(v=d,d=C),H=0;H<C;H++)for(E[H]=new Array(d),F=0;F<d;F++)E[H][F]=v(H,F);return E};function g(){return 0}a.zeros=function(C,d){return s(d)||(d=C),a.create(C,d,g)};function w(){return 1}a.ones=function(C,d){return s(d)||(d=C),a.create(C,d,w)},a.rand=function(C,d){return s(d)||(d=C),a.create(C,d,a._random_fn)};function Q(h,C){return h===C?1:0}a.identity=function(C,d){return s(d)||(d=C),a.create(C,d,Q)},a.symmetric=function(C){var d=C.length,v,E;if(C.length!==C[0].length)return!1;for(v=0;v<d;v++)for(E=0;E<d;E++)if(C[E][v]!==C[v][E])return!1;return!0},a.clear=function(C){return a.alter(C,g)},a.seq=function(C,d,v,E){r(E)||(E=!1);var H=[],F=c(C,d),p=(d*F-C*F)/((v-1)*F),m=C,I;for(I=0;m<=d&&I<v;I++,m=(C*F+p*F*I)/F)H.push(E?E(m,I):m);return H},a.arange=function(C,d,v){var E=[],H;if(v=v||1,d===i&&(d=C,C=0),C===d||v===0)return[];if(C<d&&v<0)return[];if(C>d&&v>0)return[];if(v>0)for(H=C;H<d;H+=v)E.push(H);else for(H=C;H>d;H+=v)E.push(H);return E},a.slice=function(){function h(d,v,E,H){var F,p=[],m=d.length;if(v===i&&E===i&&H===i)return a.copy(d);if(v=v||0,E=E||d.length,v=v>=0?v:m+v,E=E>=0?E:m+E,H=H||1,v===E||H===0)return[];if(v<E&&H<0)return[];if(v>E&&H>0)return[];if(H>0)for(F=v;F<E;F+=H)p.push(d[F]);else for(F=v;F>E;F+=H)p.push(d[F]);return p}function C(d,v){var E,H;if(v=v||{},s(v.row)){if(s(v.col))return d[v.row][v.col];var F=a.rowa(d,v.row);return E=v.col||{},h(F,E.start,E.end,E.step)}if(s(v.col)){var p=a.cola(d,v.col);return H=v.row||{},h(p,H.start,H.end,H.step)}H=v.row||{},E=v.col||{};var m=h(d,H.start,H.end,H.step);return m.map(function(I){return h(I,E.start,E.end,E.step)})}return C}(),a.sliceAssign=function(C,d,v){var E,H;if(s(d.row)){if(s(d.col))return C[d.row][d.col]=v;d.col=d.col||{},d.col.start=d.col.start||0,d.col.end=d.col.end||C[0].length,d.col.step=d.col.step||1,E=a.arange(d.col.start,A.min(C.length,d.col.end),d.col.step);var F=d.row;return E.forEach(function(m,I){C[F][m]=v[I]}),C}if(s(d.col)){d.row=d.row||{},d.row.start=d.row.start||0,d.row.end=d.row.end||C.length,d.row.step=d.row.step||1,H=a.arange(d.row.start,A.min(C[0].length,d.row.end),d.row.step);var p=d.col;return H.forEach(function(m,I){C[m][p]=v[I]}),C}return v[0].length===i&&(v=[v]),d.row.start=d.row.start||0,d.row.end=d.row.end||C.length,d.row.step=d.row.step||1,d.col.start=d.col.start||0,d.col.end=d.col.end||C[0].length,d.col.step=d.col.step||1,H=a.arange(d.row.start,A.min(C.length,d.row.end),d.row.step),E=a.arange(d.col.start,A.min(C[0].length,d.col.end),d.col.step),H.forEach(function(m,I){E.forEach(function(y,K){C[m][y]=v[I][K]})}),C},a.diagonal=function(C){var d=a.zeros(C.length,C.length);return C.forEach(function(v,E){d[E][E]=v}),d},a.copy=function(C){return C.map(function(d){return s(d)?d:d.map(function(v){return v})})};var U=a.prototype;return U.length=0,U.push=Array.prototype.push,U.sort=Array.prototype.sort,U.splice=Array.prototype.splice,U.slice=Array.prototype.slice,U.toArray=function(){return this.length>1?f.call(this):f.call(this)[0]},U.map=function(C,d){return a(a.map(this,C,d))},U.cumreduce=function(C,d){return a(a.cumreduce(this,C,d))},U.alter=function(C){return a.alter(this,C),this},function(h){for(var C=0;C<h.length;C++)(function(d){U[d]=function(v){var E=this,H;return v?(setTimeout(function(){v.call(E,U[d].call(E))}),this):(H=a[d](this),o(H)?a(H):H)}})(h[C])}("transpose clear symmetric rows cols dimensions diag antidiag".split(" ")),function(h){for(var C=0;C<h.length;C++)(function(d){U[d]=function(v,E){var H=this;return E?(setTimeout(function(){E.call(H,U[d].call(H,v))}),this):a(a[d](this,v))}})(h[C])}("row col".split(" ")),function(h){for(var C=0;C<h.length;C++)(function(d){U[d]=function(){return a(a[d].apply(null,arguments))}})(h[C])}("create zeros ones rand identity".split(" ")),a}(Math);return function(A,i){var u=A.utils.isFunction;function f(o,r){return o-r}function l(o,r,s){return i.max(r,i.min(o,s))}A.sum=function(r){for(var s=0,B=r.length;--B>=0;)s+=r[B];return s},A.sumsqrd=function(r){for(var s=0,B=r.length;--B>=0;)s+=r[B]*r[B];return s},A.sumsqerr=function(r){for(var s=A.mean(r),B=0,a=r.length,g;--a>=0;)g=r[a]-s,B+=g*g;return B},A.sumrow=function(r){for(var s=0,B=r.length;--B>=0;)s+=r[B];return s},A.product=function(r){for(var s=1,B=r.length;--B>=0;)s*=r[B];return s},A.min=function(r){for(var s=r[0],B=0;++B<r.length;)r[B]<s&&(s=r[B]);return s},A.max=function(r){for(var s=r[0],B=0;++B<r.length;)r[B]>s&&(s=r[B]);return s},A.unique=function(r){for(var s={},B=[],a=0;a<r.length;a++)s[r[a]]||(s[r[a]]=!0,B.push(r[a]));return B},A.mean=function(r){return A.sum(r)/r.length},A.meansqerr=function(r){return A.sumsqerr(r)/r.length},A.geomean=function(r){var s=r.map(i.log),B=A.mean(s);return i.exp(B)},A.median=function(r){var s=r.length,B=r.slice().sort(f);return s&1?B[s/2|0]:(B[s/2-1]+B[s/2])/2},A.cumsum=function(r){return A.cumreduce(r,function(s,B){return s+B})},A.cumprod=function(r){return A.cumreduce(r,function(s,B){return s*B})},A.diff=function(r){var s=[],B=r.length,a;for(a=1;a<B;a++)s.push(r[a]-r[a-1]);return s},A.rank=function(o){var r,s=[],B={};for(r=0;r<o.length;r++){var a=o[r];B[a]?B[a]++:(B[a]=1,s.push(a))}var g=s.sort(f),w={},Q=1;for(r=0;r<g.length;r++){var a=g[r],U=B[a],h=Q,C=Q+U-1,d=(h+C)/2;w[a]=d,Q+=U}return o.map(function(v){return w[v]})},A.mode=function(r){var s=r.length,B=r.slice().sort(f),a=1,g=0,w=0,Q=[],U;for(U=0;U<s;U++)B[U]===B[U+1]?a++:(a>g?(Q=[B[U]],g=a,w=0):a===g&&(Q.push(B[U]),w++),a=1);return w===0?Q[0]:Q},A.range=function(r){return A.max(r)-A.min(r)},A.variance=function(r,s){return A.sumsqerr(r)/(r.length-(s?1:0))},A.pooledvariance=function(r){var s=r.reduce(function(a,g){return a+A.sumsqerr(g)},0),B=r.reduce(function(a,g){return a+g.length},0);return s/(B-r.length)},A.deviation=function(o){for(var r=A.mean(o),s=o.length,B=new Array(s),a=0;a<s;a++)B[a]=o[a]-r;return B},A.stdev=function(r,s){return i.sqrt(A.variance(r,s))},A.pooledstdev=function(r){return i.sqrt(A.pooledvariance(r))},A.meandev=function(r){for(var s=A.mean(r),B=[],a=r.length-1;a>=0;a--)B.push(i.abs(r[a]-s));return A.mean(B)},A.meddev=function(r){for(var s=A.median(r),B=[],a=r.length-1;a>=0;a--)B.push(i.abs(r[a]-s));return A.median(B)},A.coeffvar=function(r){return A.stdev(r)/A.mean(r)},A.quartiles=function(r){var s=r.length,B=r.slice().sort(f);return[B[i.round(s/4)-1],B[i.round(s/2)-1],B[i.round(s*3/4)-1]]},A.quantiles=function(r,s,B,a){var g=r.slice().sort(f),w=[s.length],Q=r.length,U,h,C,d,v,E;for(typeof B>"u"&&(B=3/8),typeof a>"u"&&(a=3/8),U=0;U<s.length;U++)h=s[U],C=B+h*(1-B-a),d=Q*h+C,v=i.floor(l(d,1,Q-1)),E=l(d-v,0,1),w[U]=(1-E)*g[v-1]+E*g[v];return w},A.percentile=function(r,s,B){var a=r.slice().sort(f),g=s*(a.length+(B?1:-1))+(B?0:1),w=parseInt(g),Q=g-w;return w+1<a.length?a[w-1]+Q*(a[w]-a[w-1]):a[w-1]},A.percentileOfScore=function(r,s,B){var a=0,g=r.length,w=!1,Q,U;for(B==="strict"&&(w=!0),U=0;U<g;U++)Q=r[U],(w&&Q<s||!w&&Q<=s)&&a++;return a/g},A.histogram=function(r,s){s=s||4;var B=A.min(r),a=(A.max(r)-B)/s,g=r.length,w=[],Q;for(Q=0;Q<s;Q++)w[Q]=0;for(Q=0;Q<g;Q++)w[i.min(i.floor((r[Q]-B)/a),s-1)]+=1;return w},A.covariance=function(r,s){var B=A.mean(r),a=A.mean(s),g=r.length,w=new Array(g),Q;for(Q=0;Q<g;Q++)w[Q]=(r[Q]-B)*(s[Q]-a);return A.sum(w)/(g-1)},A.corrcoeff=function(r,s){return A.covariance(r,s)/A.stdev(r,1)/A.stdev(s,1)},A.spearmancoeff=function(o,r){return o=A.rank(o),r=A.rank(r),A.corrcoeff(o,r)},A.stanMoment=function(r,s){for(var B=A.mean(r),a=A.stdev(r),g=r.length,w=0,Q=0;Q<g;Q++)w+=i.pow((r[Q]-B)/a,s);return w/r.length},A.skewness=function(r){return A.stanMoment(r,3)},A.kurtosis=function(r){return A.stanMoment(r,4)-3};var c=A.prototype;(function(o){for(var r=0;r<o.length;r++)(function(s){c[s]=function(B,a){var g=[],w=0,Q=this;if(u(B)&&(a=B,B=!1),a)return setTimeout(function(){a.call(Q,c[s].call(Q,B))}),this;if(this.length>1){for(Q=B===!0?this:this.transpose();w<Q.length;w++)g[w]=A[s](Q[w]);return g}return A[s](this[0],B)}})(o[r])})("cumsum cumprod".split(" ")),function(o){for(var r=0;r<o.length;r++)(function(s){c[s]=function(B,a){var g=[],w=0,Q=this;if(u(B)&&(a=B,B=!1),a)return setTimeout(function(){a.call(Q,c[s].call(Q,B))}),this;if(this.length>1){for(s!=="sumrow"&&(Q=B===!0?this:this.transpose());w<Q.length;w++)g[w]=A[s](Q[w]);return B===!0?A[s](A.utils.toVector(g)):g}return A[s](this[0],B)}})(o[r])}("sum sumsqrd sumsqerr sumrow product min max unique mean meansqerr geomean median diff rank mode range variance deviation stdev meandev meddev coeffvar quartiles histogram skewness kurtosis".split(" ")),function(o){for(var r=0;r<o.length;r++)(function(s){c[s]=function(){var B=[],a=0,g=this,w=Array.prototype.slice.call(arguments),Q;if(u(w[w.length-1])){Q=w[w.length-1];var U=w.slice(0,w.length-1);return setTimeout(function(){Q.call(g,c[s].apply(g,U))}),this}else{Q=void 0;var h=function(d){return A[s].apply(g,[d].concat(w))}}if(this.length>1){for(g=g.transpose();a<g.length;a++)B[a]=h(g[a]);return B}return h(this[0])}})(o[r])}("quantiles percentileOfScore".split(" "))}(n,Math),function(A,i){A.gammaln=function(f){var l=0,c=[76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],o=1.000000000190015,r,s,B;for(B=(s=r=f)+5.5,B-=(r+.5)*i.log(B);l<6;l++)o+=c[l]/++s;return i.log(2.5066282746310007*o/r)-B},A.loggam=function(f){var l,c,o,r,s,B,a,g=[.08333333333333333,-.002777777777777778,.0007936507936507937,-.0005952380952380952,.0008417508417508418,-.001917526917526918,.00641025641025641,-.02955065359477124,.1796443723688307,-1.3924322169059];if(l=f,a=0,f==1||f==2)return 0;for(f<=7&&(a=i.floor(7-f),l=f+a),c=1/(l*l),o=2*i.PI,s=g[9],B=8;B>=0;B--)s*=c,s+=g[B];if(r=s/l+.5*i.log(o)+(l-.5)*i.log(l)-l,f<=7)for(B=1;B<=a;B++)r-=i.log(l-1),l-=1;return r},A.gammafn=function(f){var l=[-1.716185138865495,24.76565080557592,-379.80425647094563,629.3311553128184,866.9662027904133,-31451.272968848367,-36144.413418691176,66456.14382024054],c=[-30.8402300119739,315.35062697960416,-1015.1563674902192,-3107.771671572311,22538.11842098015,4755.846277527881,-134659.9598649693,-115132.2596755535],o=!1,r=0,s=0,B=0,a=f,g,w,Q,U;if(f>171.6243769536076)return 1/0;if(a<=0)if(U=a%1+36e-17,U)o=(a&1?-1:1)*i.PI/i.sin(i.PI*U),a=1-a;else return 1/0;for(Q=a,a<1?w=a++:w=(a-=r=(a|0)-1)-1,g=0;g<8;++g)B=(B+l[g])*w,s=s*w+c[g];if(U=B/s+1,Q<a)U/=Q;else if(Q>a)for(g=0;g<r;++g)U*=a,a++;return o&&(U=o/U),U},A.gammap=function(f,l){return A.lowRegGamma(f,l)*A.gammafn(f)},A.lowRegGamma=function(f,l){var c=A.gammaln(f),o=f,r=1/f,s=r,B=l+1-f,a=1/1e-30,g=1/B,w=g,Q=1,U=-~(i.log(f>=1?f:1/f)*8.5+f*.4+17),h;if(l<0||f<=0)return NaN;if(l<f+1){for(;Q<=U;Q++)r+=s*=l/++o;return r*i.exp(-l+f*i.log(l)-c)}for(;Q<=U;Q++)h=-Q*(Q-f),B+=2,g=h*g+B,a=B+h/a,g=1/g,w*=g*a;return 1-w*i.exp(-l+f*i.log(l)-c)},A.factorialln=function(f){return f<0?NaN:A.gammaln(f+1)},A.factorial=function(f){return f<0?NaN:A.gammafn(f+1)},A.combination=function(f,l){return f>170||l>170?i.exp(A.combinationln(f,l)):A.factorial(f)/A.factorial(l)/A.factorial(f-l)},A.combinationln=function(f,l){return A.factorialln(f)-A.factorialln(l)-A.factorialln(f-l)},A.permutation=function(f,l){return A.factorial(f)/A.factorial(f-l)},A.betafn=function(f,l){if(!(f<=0||l<=0))return f+l>170?i.exp(A.betaln(f,l)):A.gammafn(f)*A.gammafn(l)/A.gammafn(f+l)},A.betaln=function(f,l){return A.gammaln(f)+A.gammaln(l)-A.gammaln(f+l)},A.betacf=function(f,l,c){var o=1e-30,r=1,s=l+c,B=l+1,a=l-1,g=1,w=1-s*f/B,Q,U,h,C;for(i.abs(w)<o&&(w=o),w=1/w,C=w;r<=100&&(Q=2*r,U=r*(c-r)*f/((a+Q)*(l+Q)),w=1+U*w,i.abs(w)<o&&(w=o),g=1+U/g,i.abs(g)<o&&(g=o),w=1/w,C*=w*g,U=-(l+r)*(s+r)*f/((l+Q)*(B+Q)),w=1+U*w,i.abs(w)<o&&(w=o),g=1+U/g,i.abs(g)<o&&(g=o),w=1/w,h=w*g,C*=h,!(i.abs(h-1)<3e-7));r++);return C},A.gammapinv=function(f,l){var c=0,o=l-1,r=1e-8,s=A.gammaln(l),B,a,g,w,Q,U,h;if(f>=1)return i.max(100,l+100*i.sqrt(l));if(f<=0)return 0;for(l>1?(U=i.log(o),h=i.exp(o*(U-1)-s),Q=f<.5?f:1-f,g=i.sqrt(-2*i.log(Q)),B=(2.30753+g*.27061)/(1+g*(.99229+g*.04481))-g,f<.5&&(B=-B),B=i.max(.001,l*i.pow(1-1/(9*l)-B/(3*i.sqrt(l)),3))):(g=1-l*(.253+l*.12),f<g?B=i.pow(f/g,1/l):B=1-i.log(1-(f-g)/(1-g)));c<12;c++){if(B<=0)return 0;if(a=A.lowRegGamma(l,B)-f,l>1?g=h*i.exp(-(B-o)+o*(i.log(B)-U)):g=i.exp(-B+o*i.log(B)-s),w=a/g,B-=g=w/(1-.5*i.min(1,w*((l-1)/B-1))),B<=0&&(B=.5*(B+g)),i.abs(g)<r*B)break}return B},A.erf=function(f){var l=[-1.3026537197817094,.6419697923564902,.019476473204185836,-.00956151478680863,-.000946595344482036,.000366839497852761,42523324806907e-18,-20278578112534e-18,-1624290004647e-18,130365583558e-17,15626441722e-18,-85238095915e-18,6529054439e-18,5059343495e-18,-991364156e-18,-227365122e-18,96467911e-18,2394038e-18,-6886027e-18,894487e-18,313092e-18,-112708e-18,381e-18,7106e-18,-1523e-18,-94e-18,121e-18,-28e-18],c=l.length-1,o=!1,r=0,s=0,B,a,g,w;for(f<0&&(f=-f,o=!0),B=2/(2+f),a=4*B-2;c>0;c--)g=r,r=a*r-s+l[c],s=g;return w=B*i.exp(-f*f+.5*(l[0]+a*r)-s),o?w-1:1-w},A.erfc=function(f){return 1-A.erf(f)},A.erfcinv=function(f){var l=0,c,o,r,s;if(f>=2)return-100;if(f<=0)return 100;for(s=f<1?f:2-f,r=i.sqrt(-2*i.log(s/2)),c=-.70711*((2.30753+r*.27061)/(1+r*(.99229+r*.04481))-r);l<2;l++)o=A.erfc(c)-s,c+=o/(1.1283791670955126*i.exp(-c*c)-c*o);return f<1?c:-c},A.ibetainv=function(f,l,c){var o=1e-8,r=l-1,s=c-1,B=0,a,g,w,Q,U,h,C,d,v,E,H;if(f<=0)return 0;if(f>=1)return 1;for(l>=1&&c>=1?(w=f<.5?f:1-f,Q=i.sqrt(-2*i.log(w)),C=(2.30753+Q*.27061)/(1+Q*(.99229+Q*.04481))-Q,f<.5&&(C=-C),d=(C*C-3)/6,v=2/(1/(2*l-1)+1/(2*c-1)),E=C*i.sqrt(d+v)/v-(1/(2*c-1)-1/(2*l-1))*(d+5/6-2/(3*v)),C=l/(l+c*i.exp(2*E))):(a=i.log(l/(l+c)),g=i.log(c/(l+c)),Q=i.exp(l*a)/l,U=i.exp(c*g)/c,E=Q+U,f<Q/E?C=i.pow(l*E*f,1/l):C=1-i.pow(c*E*(1-f),1/c)),H=-A.gammaln(l)-A.gammaln(c)+A.gammaln(l+c);B<10;B++){if(C===0||C===1)return C;if(h=A.ibeta(C,l,c)-f,Q=i.exp(r*i.log(C)+s*i.log(1-C)+H),U=h/Q,C-=Q=U/(1-.5*i.min(1,U*(r/C-s/(1-C)))),C<=0&&(C=.5*(C+Q)),C>=1&&(C=.5*(C+Q+1)),i.abs(Q)<o*C&&B>0)break}return C},A.ibeta=function(f,l,c){var o=f===0||f===1?0:i.exp(A.gammaln(l+c)-A.gammaln(l)-A.gammaln(c)+l*i.log(f)+c*i.log(1-f));return f<0||f>1?!1:f<(l+1)/(l+c+2)?o*A.betacf(f,l,c)/l:1-o*A.betacf(1-f,c,l)/c},A.randn=function(f,l){var c,o,r,s,B;if(l||(l=f),f)return A.create(f,l,function(){return A.randn()});do c=A._random_fn(),o=1.7156*(A._random_fn()-.5),r=c-.449871,s=i.abs(o)+.386595,B=r*r+s*(.196*s-.25472*r);while(B>.27597&&(B>.27846||o*o>-4*i.log(c)*c*c));return o/c},A.randg=function(f,l,c){var o=f,r,s,B,a,g,w;if(c||(c=l),f||(f=1),l)return w=A.zeros(l,c),w.alter(function(){return A.randg(f)}),w;f<1&&(f+=1),r=f-1/3,s=1/i.sqrt(9*r);do{do g=A.randn(),a=1+s*g;while(a<=0);a=a*a*a,B=A._random_fn()}while(B>1-.331*i.pow(g,4)&&i.log(B)>.5*g*g+r*(1-a+i.log(a)));if(f==o)return r*a;do B=A._random_fn();while(B===0);return i.pow(B,1/o)*r*a},function(u){for(var f=0;f<u.length;f++)(function(l){A.fn[l]=function(){return A(A.map(this,function(c){return A[l](c)}))}})(u[f])}("gammaln gammafn factorial factorialln".split(" ")),function(u){for(var f=0;f<u.length;f++)(function(l){A.fn[l]=function(){return A(A[l].apply(null,arguments))}})(u[f])}("randn".split(" "))}(n,Math),function(A,i){(function(o){for(var r=0;r<o.length;r++)(function(s){A[s]=function B(a,g,w){return this instanceof B?(this._a=a,this._b=g,this._c=w,this):new B(a,g,w)},A.fn[s]=function(B,a,g){var w=A[s](B,a,g);return w.data=this,w},A[s].prototype.sample=function(B){var a=this._a,g=this._b,w=this._c;return B?A.alter(B,function(){return A[s].sample(a,g,w)}):A[s].sample(a,g,w)},function(B){for(var a=0;a<B.length;a++)(function(g){A[s].prototype[g]=function(w){var Q=this._a,U=this._b,h=this._c;return!w&&w!==0&&(w=this.data),typeof w!="number"?A.fn.map.call(w,function(C){return A[s][g](C,Q,U,h)}):A[s][g](w,Q,U,h)}})(B[a])}("pdf cdf inv".split(" ")),function(B){for(var a=0;a<B.length;a++)(function(g){A[s].prototype[g]=function(){return A[s][g](this._a,this._b,this._c)}})(B[a])}("mean median mode variance".split(" "))})(o[r])})("beta centralF cauchy chisquare exponential gamma invgamma kumaraswamy laplace lognormal noncentralt normal pareto studentt weibull uniform binomial negbin hypgeom poisson triangular tukey arcsine".split(" ")),A.extend(A.beta,{pdf:function(r,s,B){return r>1||r<0?0:s==1&&B==1?1:s<512&&B<512?i.pow(r,s-1)*i.pow(1-r,B-1)/A.betafn(s,B):i.exp((s-1)*i.log(r)+(B-1)*i.log(1-r)-A.betaln(s,B))},cdf:function(r,s,B){return r>1||r<0?(r>1)*1:A.ibeta(r,s,B)},inv:function(r,s,B){return A.ibetainv(r,s,B)},mean:function(r,s){return r/(r+s)},median:function(r,s){return A.ibetainv(.5,r,s)},mode:function(r,s){return(r-1)/(r+s-2)},sample:function(r,s){var B=A.randg(r);return B/(B+A.randg(s))},variance:function(r,s){return r*s/(i.pow(r+s,2)*(r+s+1))}}),A.extend(A.centralF,{pdf:function(r,s,B){var a,g,w;return r<0?0:s<=2?r===0&&s<2?1/0:r===0&&s===2?1:1/A.betafn(s/2,B/2)*i.pow(s/B,s/2)*i.pow(r,s/2-1)*i.pow(1+s/B*r,-(s+B)/2):(a=s*r/(B+r*s),g=B/(B+r*s),w=s*g/2,w*A.binomial.pdf((s-2)/2,(s+B-2)/2,a))},cdf:function(r,s,B){return r<0?0:A.ibeta(s*r/(s*r+B),s/2,B/2)},inv:function(r,s,B){return B/(s*(1/A.ibetainv(r,s/2,B/2)-1))},mean:function(r,s){return s>2?s/(s-2):void 0},mode:function(r,s){return r>2?s*(r-2)/(r*(s+2)):void 0},sample:function(r,s){var B=A.randg(r/2)*2,a=A.randg(s/2)*2;return B/r/(a/s)},variance:function(r,s){if(!(s<=4))return 2*s*s*(r+s-2)/(r*(s-2)*(s-2)*(s-4))}}),A.extend(A.cauchy,{pdf:function(r,s,B){return B<0?0:B/(i.pow(r-s,2)+i.pow(B,2))/i.PI},cdf:function(r,s,B){return i.atan((r-s)/B)/i.PI+.5},inv:function(o,r,s){return r+s*i.tan(i.PI*(o-.5))},median:function(r){return r},mode:function(r){return r},sample:function(r,s){return A.randn()*i.sqrt(1/(2*A.randg(.5)))*s+r}}),A.extend(A.chisquare,{pdf:function(r,s){return r<0?0:r===0&&s===2?.5:i.exp((s/2-1)*i.log(r)-r/2-s/2*i.log(2)-A.gammaln(s/2))},cdf:function(r,s){return r<0?0:A.lowRegGamma(s/2,r/2)},inv:function(o,r){return 2*A.gammapinv(o,.5*r)},mean:function(o){return o},median:function(r){return r*i.pow(1-2/(9*r),3)},mode:function(r){return r-2>0?r-2:0},sample:function(r){return A.randg(r/2)*2},variance:function(r){return 2*r}}),A.extend(A.exponential,{pdf:function(r,s){return r<0?0:s*i.exp(-s*r)},cdf:function(r,s){return r<0?0:1-i.exp(-s*r)},inv:function(o,r){return-i.log(1-o)/r},mean:function(o){return 1/o},median:function(o){return 1/o*i.log(2)},mode:function(){return 0},sample:function(r){return-1/r*i.log(A._random_fn())},variance:function(o){return i.pow(o,-2)}}),A.extend(A.gamma,{pdf:function(r,s,B){return r<0?0:r===0&&s===1?1/B:i.exp((s-1)*i.log(r)-r/B-A.gammaln(s)-s*i.log(B))},cdf:function(r,s,B){return r<0?0:A.lowRegGamma(s,r/B)},inv:function(o,r,s){return A.gammapinv(o,r)*s},mean:function(o,r){return o*r},mode:function(r,s){if(r>1)return(r-1)*s},sample:function(r,s){return A.randg(r)*s},variance:function(r,s){return r*s*s}}),A.extend(A.invgamma,{pdf:function(r,s,B){return r<=0?0:i.exp(-(s+1)*i.log(r)-B/r-A.gammaln(s)+s*i.log(B))},cdf:function(r,s,B){return r<=0?0:1-A.lowRegGamma(s,B/r)},inv:function(o,r,s){return s/A.gammapinv(1-o,r)},mean:function(o,r){return o>1?r/(o-1):void 0},mode:function(r,s){return s/(r+1)},sample:function(r,s){return s/A.randg(r)},variance:function(r,s){if(!(r<=2))return s*s/((r-1)*(r-1)*(r-2))}}),A.extend(A.kumaraswamy,{pdf:function(r,s,B){return r===0&&s===1?B:r===1&&B===1?s:i.exp(i.log(s)+i.log(B)+(s-1)*i.log(r)+(B-1)*i.log(1-i.pow(r,s)))},cdf:function(r,s,B){return r<0?0:r>1?1:1-i.pow(1-i.pow(r,s),B)},inv:function(r,s,B){return i.pow(1-i.pow(1-r,1/B),1/s)},mean:function(o,r){return r*A.gammafn(1+1/o)*A.gammafn(r)/A.gammafn(1+1/o+r)},median:function(r,s){return i.pow(1-i.pow(2,-1/s),1/r)},mode:function(r,s){if(r>=1&&s>=1&&r!==1&&s!==1)return i.pow((r-1)/(r*s-1),1/r)},variance:function(){throw new Error("variance not yet implemented")}}),A.extend(A.lognormal,{pdf:function(r,s,B){return r<=0?0:i.exp(-i.log(r)-.5*i.log(2*i.PI)-i.log(B)-i.pow(i.log(r)-s,2)/(2*B*B))},cdf:function(r,s,B){return r<0?0:.5+.5*A.erf((i.log(r)-s)/i.sqrt(2*B*B))},inv:function(o,r,s){return i.exp(-1.4142135623730951*s*A.erfcinv(2*o)+r)},mean:function(r,s){return i.exp(r+s*s/2)},median:function(r){return i.exp(r)},mode:function(r,s){return i.exp(r-s*s)},sample:function(r,s){return i.exp(A.randn()*s+r)},variance:function(r,s){return(i.exp(s*s)-1)*i.exp(2*r+s*s)}}),A.extend(A.noncentralt,{pdf:function(r,s,B){var a=1e-14;return i.abs(B)<a?A.studentt.pdf(r,s):i.abs(r)<a?i.exp(A.gammaln((s+1)/2)-B*B/2-.5*i.log(i.PI*s)-A.gammaln(s/2)):s/r*(A.noncentralt.cdf(r*i.sqrt(1+2/s),s+2,B)-A.noncentralt.cdf(r,s,B))},cdf:function(r,s,B){var a=1e-14,g=200;if(i.abs(B)<a)return A.studentt.cdf(r,s);var w=!1;r<0&&(w=!0,B=-B);for(var Q=A.normal.cdf(-B,0,1),U=a+1,h=U,C=r*r/(r*r+s),d=0,v=i.exp(-B*B/2),E=i.exp(-B*B/2-.5*i.log(2)-A.gammaln(3/2))*B;d<g||h>a||U>a;)h=U,d>0&&(v*=B*B/(2*d),E*=B*B/(2*(d+1/2))),U=v*A.beta.cdf(C,d+.5,s/2)+E*A.beta.cdf(C,d+1,s/2),Q+=.5*U,d++;return w?1-Q:Q}}),A.extend(A.normal,{pdf:function(r,s,B){return i.exp(-.5*i.log(2*i.PI)-i.log(B)-i.pow(r-s,2)/(2*B*B))},cdf:function(r,s,B){return .5*(1+A.erf((r-s)/i.sqrt(2*B*B)))},inv:function(o,r,s){return-1.4142135623730951*s*A.erfcinv(2*o)+r},mean:function(o){return o},median:function(r){return r},mode:function(o){return o},sample:function(r,s){return A.randn()*s+r},variance:function(o,r){return r*r}}),A.extend(A.pareto,{pdf:function(r,s,B){return r<s?0:B*i.pow(s,B)/i.pow(r,B+1)},cdf:function(r,s,B){return r<s?0:1-i.pow(s/r,B)},inv:function(r,s,B){return s/i.pow(1-r,1/B)},mean:function(r,s){if(!(s<=1))return s*i.pow(r,s)/(s-1)},median:function(r,s){return r*(s*i.SQRT2)},mode:function(r){return r},variance:function(o,r){if(!(r<=2))return o*o*r/(i.pow(r-1,2)*(r-2))}}),A.extend(A.studentt,{pdf:function(r,s){return s=s>1e100?1e100:s,1/(i.sqrt(s)*A.betafn(.5,s/2))*i.pow(1+r*r/s,-((s+1)/2))},cdf:function(r,s){var B=s/2;return A.ibeta((r+i.sqrt(r*r+s))/(2*i.sqrt(r*r+s)),B,B)},inv:function(o,r){var s=A.ibetainv(2*i.min(o,1-o),.5*r,.5);return s=i.sqrt(r*(1-s)/s),o>.5?s:-s},mean:function(r){return r>1?0:void 0},median:function(){return 0},mode:function(){return 0},sample:function(r){return A.randn()*i.sqrt(r/(2*A.randg(r/2)))},variance:function(r){return r>2?r/(r-2):r>1?1/0:void 0}}),A.extend(A.weibull,{pdf:function(r,s,B){return r<0||s<0||B<0?0:B/s*i.pow(r/s,B-1)*i.exp(-i.pow(r/s,B))},cdf:function(r,s,B){return r<0?0:1-i.exp(-i.pow(r/s,B))},inv:function(o,r,s){return r*i.pow(-i.log(1-o),1/s)},mean:function(o,r){return o*A.gammafn(1+1/r)},median:function(r,s){return r*i.pow(i.log(2),1/s)},mode:function(r,s){return s<=1?0:r*i.pow((s-1)/s,1/s)},sample:function(r,s){return r*i.pow(-i.log(A._random_fn()),1/s)},variance:function(r,s){return r*r*A.gammafn(1+2/s)-i.pow(A.weibull.mean(r,s),2)}}),A.extend(A.uniform,{pdf:function(r,s,B){return r<s||r>B?0:1/(B-s)},cdf:function(r,s,B){return r<s?0:r<B?(r-s)/(B-s):1},inv:function(o,r,s){return r+o*(s-r)},mean:function(r,s){return .5*(r+s)},median:function(r,s){return A.mean(r,s)},mode:function(){throw new Error("mode is not yet implemented")},sample:function(r,s){return r/2+s/2+(s/2-r/2)*(2*A._random_fn()-1)},variance:function(r,s){return i.pow(s-r,2)/12}});function u(o,r,s,B){for(var a=0,g=1,w=1,Q=1,U=0,h=0,C;i.abs((w-h)/w)>B;)h=w,C=-(r+U)*(r+s+U)*o/(r+2*U)/(r+2*U+1),a=w+C*a,g=Q+C*g,U=U+1,C=U*(s-U)*o/(r+2*U-1)/(r+2*U),w=a+C*w,Q=g+C*Q,a=a/Q,g=g/Q,w=w/Q,Q=1;return w/r}A.extend(A.binomial,{pdf:function(r,s,B){return B===0||B===1?s*B===r?1:0:A.combination(s,r)*i.pow(B,r)*i.pow(1-B,s-r)},cdf:function(r,s,B){var a,g=1e-10;if(r<0)return 0;if(r>=s)return 1;if(B<0||B>1||s<=0)return NaN;r=i.floor(r);var w=B,Q=r+1,U=s-r,h=Q+U,C=i.exp(A.gammaln(h)-A.gammaln(U)-A.gammaln(Q)+Q*i.log(w)+U*i.log(1-w));return w<(Q+1)/(h+2)?a=C*u(w,Q,U,g):a=1-C*u(1-w,U,Q,g),i.round((1-a)*(1/g))/(1/g)}}),A.extend(A.negbin,{pdf:function(r,s,B){return r!==r>>>0?!1:r<0?0:A.combination(r+s-1,s-1)*i.pow(1-B,r)*i.pow(B,s)},cdf:function(r,s,B){var a=0,g=0;if(r<0)return 0;for(;g<=r;g++)a+=A.negbin.pdf(g,s,B);return a}}),A.extend(A.hypgeom,{pdf:function(r,s,B,a){if(r!==r|0)return!1;if(r<0||r<B-(s-a))return 0;if(r>a||r>B)return 0;if(B*2>s)return a*2>s?A.hypgeom.pdf(s-B-a+r,s,s-B,s-a):A.hypgeom.pdf(a-r,s,s-B,a);if(a*2>s)return A.hypgeom.pdf(B-r,s,B,s-a);if(B<a)return A.hypgeom.pdf(r,s,a,B);for(var g=1,w=0,Q=0;Q<r;Q++){for(;g>1&&w<a;)g*=1-B/(s-w),w++;g*=(a-Q)*(B-Q)/((Q+1)*(s-B-a+Q+1))}for(;w<a;w++)g*=1-B/(s-w);return i.min(1,i.max(0,g))},cdf:function(r,s,B,a){if(r<0||r<B-(s-a))return 0;if(r>=a||r>=B)return 1;if(B*2>s)return a*2>s?A.hypgeom.cdf(s-B-a+r,s,s-B,s-a):1-A.hypgeom.cdf(a-r-1,s,s-B,a);if(a*2>s)return 1-A.hypgeom.cdf(B-r-1,s,B,s-a);if(B<a)return A.hypgeom.cdf(r,s,a,B);for(var g=1,w=1,Q=0,U=0;U<r;U++){for(;g>1&&Q<a;){var h=1-B/(s-Q);w*=h,g*=h,Q++}w*=(a-U)*(B-U)/((U+1)*(s-B-a+U+1)),g+=w}for(;Q<a;Q++)g*=1-B/(s-Q);return i.min(1,i.max(0,g))}}),A.extend(A.poisson,{pdf:function(r,s){return s<0||r%1!==0||r<0?0:i.pow(s,r)*i.exp(-s)/A.factorial(r)},cdf:function(r,s){var B=[],a=0;if(r<0)return 0;for(;a<=r;a++)B.push(A.poisson.pdf(a,s));return A.sum(B)},mean:function(o){return o},variance:function(o){return o},sampleSmall:function(r){var s=1,B=0,a=i.exp(-r);do B++,s*=A._random_fn();while(s>a);return B-1},sampleLarge:function(r){var s=r,B,a,g,w,Q,U,h,C,d,v;for(w=i.sqrt(s),Q=i.log(s),h=.931+2.53*w,U=-.059+.02483*h,C=1.1239+1.1328/(h-3.4),d=.9277-3.6224/(h-2);;){if(a=i.random()-.5,g=i.random(),v=.5-i.abs(a),B=i.floor((2*U/v+h)*a+s+.43),v>=.07&&g<=d)return B;if(!(B<0||v<.013&&g>v)&&i.log(g)+i.log(C)-i.log(U/(v*v)+h)<=-s+B*Q-A.loggam(B+1))return B}},sample:function(r){return r<10?this.sampleSmall(r):this.sampleLarge(r)}}),A.extend(A.triangular,{pdf:function(r,s,B,a){return B<=s||a<s||a>B?NaN:r<s||r>B?0:r<a?2*(r-s)/((B-s)*(a-s)):r===a?2/(B-s):2*(B-r)/((B-s)*(B-a))},cdf:function(r,s,B,a){return B<=s||a<s||a>B?NaN:r<=s?0:r>=B?1:r<=a?i.pow(r-s,2)/((B-s)*(a-s)):1-i.pow(B-r,2)/((B-s)*(B-a))},inv:function(r,s,B,a){return B<=s||a<s||a>B?NaN:r<=(a-s)/(B-s)?s+(B-s)*i.sqrt(r*((a-s)/(B-s))):s+(B-s)*(1-i.sqrt((1-r)*(1-(a-s)/(B-s))))},mean:function(r,s,B){return(r+s+B)/3},median:function(r,s,B){if(B<=(r+s)/2)return s-i.sqrt((s-r)*(s-B))/i.sqrt(2);if(B>(r+s)/2)return r+i.sqrt((s-r)*(B-r))/i.sqrt(2)},mode:function(r,s,B){return B},sample:function(r,s,B){var a=A._random_fn();return a<(B-r)/(s-r)?r+i.sqrt(a*(s-r)*(B-r)):s-i.sqrt((1-a)*(s-r)*(s-B))},variance:function(r,s,B){return(r*r+s*s+B*B-r*s-r*B-s*B)/18}}),A.extend(A.arcsine,{pdf:function(r,s,B){return B<=s?NaN:r<=s||r>=B?0:2/i.PI*i.pow(i.pow(B-s,2)-i.pow(2*r-s-B,2),-.5)},cdf:function(r,s,B){return r<s?0:r<B?2/i.PI*i.asin(i.sqrt((r-s)/(B-s))):1},inv:function(o,r,s){return r+(.5-.5*i.cos(i.PI*o))*(s-r)},mean:function(r,s){return s<=r?NaN:(r+s)/2},median:function(r,s){return s<=r?NaN:(r+s)/2},mode:function(){throw new Error("mode is not yet implemented")},sample:function(r,s){return(r+s)/2+(s-r)/2*i.sin(2*i.PI*A.uniform.sample(0,1))},variance:function(r,s){return s<=r?NaN:i.pow(s-r,2)/8}});function f(o){return o/i.abs(o)}A.extend(A.laplace,{pdf:function(r,s,B){return B<=0?0:i.exp(-i.abs(r-s)/B)/(2*B)},cdf:function(r,s,B){return B<=0?0:r<s?.5*i.exp((r-s)/B):1-.5*i.exp(-(r-s)/B)},mean:function(o){return o},median:function(o){return o},mode:function(o){return o},variance:function(o,r){return 2*r*r},sample:function(r,s){var B=A._random_fn()-.5;return r-s*f(B)*i.log(1-2*i.abs(B))}});function l(o,r,s){var B=12,a=6,g=-30,w=-50,Q=60,U=8,h=3,C=2,d=3,v=[.9815606342467192,.9041172563704749,.7699026741943047,.5873179542866175,.3678314989981802,.1252334085114689],E=[.04717533638651183,.10693932599531843,.16007832854334622,.20316742672306592,.2334925365383548,.24914704581340277],H=o*.5;if(H>=U)return 1;var F=2*A.normal.cdf(H,0,1,1,0)-1;F>=i.exp(w/s)?F=i.pow(F,s):F=0;var p;o>h?p=C:p=d;for(var m=H,I=(U-H)/p,y=m+I,K=0,_=s-1,$=1;$<=p;$++){for(var W=0,D=.5*(y+m),N=.5*(y-m),T=1;T<=B;T++){var k,O;a<T?(k=B-T+1,O=v[k-1]):(k=T,O=-v[k-1]);var R=N*O,L=D+R,M=L*L;if(M>Q)break;var P=2*A.normal.cdf(L,0,1,1,0),q=2*A.normal.cdf(L,o,1,1,0),rA=P*.5-q*.5;rA>=i.exp(g/_)&&(rA=E[k-1]*i.exp(-(.5*M))*i.pow(rA,_),W+=rA)}W*=2*N*s/i.sqrt(2*i.PI),K+=W,m=y,y+=I}return F+=K,F<=i.exp(g/r)?0:(F=i.pow(F,r),F>=1?1:F)}function c(o,r,s){var B=.322232421088,a=.099348462606,g=-1,w=.588581570495,Q=-.342242088547,U=.531103462366,h=-.204231210125,C=.10353775285,d=-453642210148e-16,v=.0038560700634,E=.8832,H=.2368,F=1.208,p=1.4142,m=120,I=.5-.5*o,y=i.sqrt(i.log(1/(I*I))),K=y+((((y*d+h)*y+Q)*y+g)*y+B)/((((y*v+C)*y+U)*y+w)*y+a);s<m&&(K+=(K*K*K+K)/s/4);var _=E-H*K;return s<m&&(_+=-1.214/s+F*K/s),K*(_*i.log(r-1)+p)}A.extend(A.tukey,{cdf:function(r,s,B){var a=1,g=s,w=16,Q=8,U=-30,h=1e-14,C=100,d=800,v=5e3,E=25e3,H=1,F=.5,p=.25,m=.125,I=[.9894009349916499,.9445750230732326,.8656312023878318,.755404408355003,.6178762444026438,.45801677765722737,.2816035507792589,.09501250983763744],y=[.027152459411754096,.062253523938647894,.09515851168249279,.12462897125553388,.14959598881657674,.16915651939500254,.18260341504492358,.1894506104550685];if(r<=0)return 0;if(B<2||a<1||g<2)return NaN;if(!Number.isFinite(r))return 1;if(B>E)return l(r,a,g);var K=B*.5,_=K*i.log(B)-B*i.log(2)-A.gammaln(K),$=K-1,W=B*.25,D;B<=C?D=H:B<=d?D=F:B<=v?D=p:D=m,_+=i.log(D);for(var N=0,T=1;T<=50;T++){for(var k=0,O=(2*T-1)*D,R=1;R<=w;R++){var L,M;Q<R?(L=R-Q-1,M=_+$*i.log(O+I[L]*D)-(I[L]*D+O)*W):(L=R-1,M=_+$*i.log(O-I[L]*D)+(I[L]*D-O)*W);var P;if(M>=U){Q<R?P=r*i.sqrt((I[L]*D+O)*.5):P=r*i.sqrt((-(I[L]*D)+O)*.5);var q=l(P,a,g),rA=q*y[L]*i.exp(M);k+=rA}}if(T*D>=1&&k<=h)break;N+=k}if(k>h)throw new Error("tukey.cdf failed to converge");return N>1&&(N=1),N},inv:function(o,r,s){var B=1,a=r,g=1e-4,w=50;if(s<2||B<1||a<2)return NaN;if(o<0||o>1)return NaN;if(o===0)return 0;if(o===1)return 1/0;var Q=c(o,a,s),U=A.tukey.cdf(Q,r,s)-o,h;U>0?h=i.max(0,Q-1):h=Q+1;for(var C=A.tukey.cdf(h,r,s)-o,d,v=1;v<w;v++){d=h-C*(h-Q)/(C-U),U=C,Q=h,d<0&&(d=0,C=-o),C=A.tukey.cdf(d,r,s)-o,h=d;var E=i.abs(h-Q);if(E<g)return d}throw new Error("tukey.inv failed to converge")}})}(n,Math),function(A,i){var u=Array.prototype.push,f=A.utils.isArray;function l(c){return f(c)||c instanceof A}A.extend({add:function(o,r){return l(r)?(l(r[0])||(r=[r]),A.map(o,function(s,B,a){return s+r[B][a]})):A.map(o,function(s){return s+r})},subtract:function(o,r){return l(r)?(l(r[0])||(r=[r]),A.map(o,function(s,B,a){return s-r[B][a]||0})):A.map(o,function(s){return s-r})},divide:function(o,r){return l(r)?(l(r[0])||(r=[r]),A.multiply(o,A.inv(r))):A.map(o,function(s){return s/r})},multiply:function(o,r){var s,B,a,g,w,Q,U,h;if(o.length===void 0&&r.length===void 0)return o*r;if(w=o.length,Q=o[0].length,U=A.zeros(w,a=l(r)?r[0].length:Q),h=0,l(r)){for(;h<a;h++)for(s=0;s<w;s++){for(g=0,B=0;B<Q;B++)g+=o[s][B]*r[B][h];U[s][h]=g}return w===1&&h===1?U[0][0]:U}return A.map(o,function(C){return C*r})},outer:function(o,r){return A.multiply(o.map(function(s){return[s]}),[r])},dot:function(o,r){l(o[0])||(o=[o]),l(r[0])||(r=[r]);for(var s=o[0].length===1&&o.length!==1?A.transpose(o):o,B=r[0].length===1&&r.length!==1?A.transpose(r):r,a=[],g=0,w=s.length,Q=s[0].length,U,h;g<w;g++){for(a[g]=[],U=0,h=0;h<Q;h++)U+=s[g][h]*B[g][h];a[g]=U}return a.length===1?a[0]:a},pow:function(o,r){return A.map(o,function(s){return i.pow(s,r)})},exp:function(o){return A.map(o,function(r){return i.exp(r)})},log:function(o){return A.map(o,function(r){return i.log(r)})},abs:function(o){return A.map(o,function(r){return i.abs(r)})},norm:function(o,r){var s=0,B=0;for(isNaN(r)&&(r=2),l(o[0])&&(o=o[0]);B<o.length;B++)s+=i.pow(i.abs(o[B]),r);return i.pow(s,1/r)},angle:function(o,r){return i.acos(A.dot(o,r)/(A.norm(o)*A.norm(r)))},aug:function(o,r){var s=[],B;for(B=0;B<o.length;B++)s.push(o[B].slice());for(B=0;B<s.length;B++)u.apply(s[B],r[B]);return s},inv:function(o){for(var r=o.length,s=o[0].length,B=A.identity(r,s),a=A.gauss_jordan(o,B),g=[],w=0,Q;w<r;w++)for(g[w]=[],Q=s;Q<a[0].length;Q++)g[w][Q-s]=a[w][Q];return g},det:function c(o){if(o.length===2)return o[0][0]*o[1][1]-o[0][1]*o[1][0];for(var r=0,s=0;s<o.length;s++){for(var B=[],a=1;a<o.length;a++){B[a-1]=[];for(var g=0;g<o.length;g++)g<s?B[a-1][g]=o[a][g]:g>s&&(B[a-1][g-1]=o[a][g])}var w=s%2?-1:1;r+=c(B)*o[0][s]*w}return r},gauss_elimination:function(o,r){var s=0,B=0,a=o.length,g=o[0].length,w=1,Q=0,U=[],h,C,d,v;for(o=A.aug(o,r),h=o[0].length,s=0;s<a;s++){for(C=o[s][s],B=s,v=s+1;v<g;v++)C<i.abs(o[v][s])&&(C=o[v][s],B=v);if(B!=s)for(v=0;v<h;v++)d=o[s][v],o[s][v]=o[B][v],o[B][v]=d;for(B=s+1;B<a;B++)for(w=o[B][s]/o[s][s],v=s;v<h;v++)o[B][v]=o[B][v]-w*o[s][v]}for(s=a-1;s>=0;s--){for(Q=0,B=s+1;B<=a-1;B++)Q=Q+U[B]*o[s][B];U[s]=(o[s][h-1]-Q)/o[s][s]}return U},gauss_jordan:function(o,r){var s=A.aug(o,r),B=s.length,a=s[0].length,g=0,w,Q,U;for(Q=0;Q<B;Q++){var h=Q;for(U=Q+1;U<B;U++)i.abs(s[U][Q])>i.abs(s[h][Q])&&(h=U);var C=s[Q];for(s[Q]=s[h],s[h]=C,U=Q+1;U<B;U++)for(g=s[U][Q]/s[Q][Q],w=Q;w<a;w++)s[U][w]-=s[Q][w]*g}for(Q=B-1;Q>=0;Q--){for(g=s[Q][Q],U=0;U<Q;U++)for(w=a-1;w>Q-1;w--)s[U][w]-=s[Q][w]*s[U][Q]/g;for(s[Q][Q]/=g,w=B;w<a;w++)s[Q][w]/=g}return s},triaUpSolve:function(o,r){var s=o[0].length,B=A.zeros(1,s)[0],a,g=!1;return r[0].length!=null&&(r=r.map(function(w){return w[0]}),g=!0),A.arange(s-1,-1,-1).forEach(function(w){a=A.arange(w+1,s).map(function(Q){return B[Q]*o[w][Q]}),B[w]=(r[w]-A.sum(a))/o[w][w]}),g?B.map(function(w){return[w]}):B},triaLowSolve:function(o,r){var s=o[0].length,B=A.zeros(1,s)[0],a,g=!1;return r[0].length!=null&&(r=r.map(function(w){return w[0]}),g=!0),A.arange(s).forEach(function(w){a=A.arange(w).map(function(Q){return o[w][Q]*B[Q]}),B[w]=(r[w]-A.sum(a))/o[w][w]}),g?B.map(function(w){return[w]}):B},lu:function(o){var r=o.length,s=A.identity(r),B=A.zeros(o.length,o[0].length),a;return A.arange(r).forEach(function(g){B[0][g]=o[0][g]}),A.arange(1,r).forEach(function(g){A.arange(g).forEach(function(w){a=A.arange(w).map(function(Q){return s[g][Q]*B[Q][w]}),s[g][w]=(o[g][w]-A.sum(a))/B[w][w]}),A.arange(g,r).forEach(function(w){a=A.arange(g).map(function(Q){return s[g][Q]*B[Q][w]}),B[g][w]=o[a.length][w]-A.sum(a)})}),[s,B]},cholesky:function(o){var r=o.length,s=A.zeros(o.length,o[0].length),B;return A.arange(r).forEach(function(a){B=A.arange(a).map(function(g){return i.pow(s[a][g],2)}),s[a][a]=i.sqrt(o[a][a]-A.sum(B)),A.arange(a+1,r).forEach(function(g){B=A.arange(a).map(function(w){return s[a][w]*s[g][w]}),s[g][a]=(o[a][g]-A.sum(B))/s[a][a]})}),s},gauss_jacobi:function(o,r,s,B){for(var a=0,g=0,w=o.length,Q=[],U=[],h=[],C,d,v,E;a<w;a++)for(Q[a]=[],U[a]=[],h[a]=[],g=0;g<w;g++)a>g?(Q[a][g]=o[a][g],U[a][g]=h[a][g]=0):a<g?(U[a][g]=o[a][g],Q[a][g]=h[a][g]=0):(h[a][g]=o[a][g],Q[a][g]=U[a][g]=0);for(v=A.multiply(A.multiply(A.inv(h),A.add(Q,U)),-1),d=A.multiply(A.inv(h),r),C=s,E=A.add(A.multiply(v,s),d),a=2;i.abs(A.norm(A.subtract(E,C)))>B;)C=E,E=A.add(A.multiply(v,C),d),a++;return E},gauss_seidel:function(o,r,s,B){for(var a=0,g=o.length,w=[],Q=[],U=[],h,C,d,v,E;a<g;a++)for(w[a]=[],Q[a]=[],U[a]=[],h=0;h<g;h++)a>h?(w[a][h]=o[a][h],Q[a][h]=U[a][h]=0):a<h?(Q[a][h]=o[a][h],w[a][h]=U[a][h]=0):(U[a][h]=o[a][h],w[a][h]=Q[a][h]=0);for(v=A.multiply(A.multiply(A.inv(A.add(U,w)),Q),-1),d=A.multiply(A.inv(A.add(U,w)),r),C=s,E=A.add(A.multiply(v,s),d),a=2;i.abs(A.norm(A.subtract(E,C)))>B;)C=E,E=A.add(A.multiply(v,C),d),a=a+1;return E},SOR:function(o,r,s,B,a){for(var g=0,w=o.length,Q=[],U=[],h=[],C,d,v,E,H;g<w;g++)for(Q[g]=[],U[g]=[],h[g]=[],C=0;C<w;C++)g>C?(Q[g][C]=o[g][C],U[g][C]=h[g][C]=0):g<C?(U[g][C]=o[g][C],Q[g][C]=h[g][C]=0):(h[g][C]=o[g][C],Q[g][C]=U[g][C]=0);for(E=A.multiply(A.inv(A.add(h,A.multiply(Q,a))),A.subtract(A.multiply(h,1-a),A.multiply(U,a))),v=A.multiply(A.multiply(A.inv(A.add(h,A.multiply(Q,a))),r),a),d=s,H=A.add(A.multiply(E,s),v),g=2;i.abs(A.norm(A.subtract(H,d)))>B;)d=H,H=A.add(A.multiply(E,d),v),g++;return H},householder:function(o){for(var r=o.length,s=o[0].length,B=0,a=[],g=[],w,Q,U,h,C;B<r-1;B++){for(w=0,h=B+1;h<s;h++)w+=o[h][B]*o[h][B];for(C=o[B+1][B]>0?-1:1,w=C*i.sqrt(w),Q=i.sqrt((w*w-o[B+1][B]*w)/2),a=A.zeros(r,1),a[B+1][0]=(o[B+1][B]-w)/(2*Q),U=B+2;U<r;U++)a[U][0]=o[U][B]/(2*Q);g=A.subtract(A.identity(r,s),A.multiply(A.multiply(a,A.transpose(a)),2)),o=A.multiply(g,A.multiply(o,g))}return o},QR:function(){var c=A.sum,o=A.arange;function r(s){var B=s.length,a=s[0].length,g=A.zeros(a,a);s=A.copy(s);var w,Q,U;for(Q=0;Q<a;Q++){for(g[Q][Q]=i.sqrt(c(o(B).map(function(h){return s[h][Q]*s[h][Q]}))),w=0;w<B;w++)s[w][Q]=s[w][Q]/g[Q][Q];for(U=Q+1;U<a;U++)for(g[Q][U]=c(o(B).map(function(h){return s[h][Q]*s[h][U]})),w=0;w<B;w++)s[w][U]=s[w][U]-s[w][Q]*g[Q][U]}return[s,g]}return r}(),lstsq:function(){function c(r){r=A.copy(r);var s=r.length,B=A.identity(s);return A.arange(s-1,-1,-1).forEach(function(a){A.sliceAssign(B,{row:a},A.divide(A.slice(B,{row:a}),r[a][a])),A.sliceAssign(r,{row:a},A.divide(A.slice(r,{row:a}),r[a][a])),A.arange(a).forEach(function(g){var w=A.multiply(r[g][a],-1),Q=A.slice(r,{row:g}),U=A.multiply(A.slice(r,{row:a}),w);A.sliceAssign(r,{row:g},A.add(Q,U));var h=A.slice(B,{row:g}),C=A.multiply(A.slice(B,{row:a}),w);A.sliceAssign(B,{row:g},A.add(h,C))})}),B}function o(r,s){var B=!1;s[0].length===void 0&&(s=s.map(function(E){return[E]}),B=!0);var a=A.QR(r),g=a[0],w=a[1],Q=r[0].length,U=A.slice(g,{col:{end:Q}}),h=A.slice(w,{row:{end:Q}}),C=c(h),d=A.transpose(U);d[0].length===void 0&&(d=[d]);var v=A.multiply(A.multiply(C,d),s);return v.length===void 0&&(v=[[v]]),B?v.map(function(E){return E[0]}):v}return o}(),jacobi:function(o){for(var r=1,s=o.length,B=A.identity(s,s),a=[],g,w,Q,U,h,C,d,v;r===1;){for(C=o[0][1],U=0,h=1,w=0;w<s;w++)for(Q=0;Q<s;Q++)w!=Q&&C<i.abs(o[w][Q])&&(C=i.abs(o[w][Q]),U=w,h=Q);for(o[U][U]===o[h][h]?d=o[U][h]>0?i.PI/4:-i.PI/4:d=i.atan(2*o[U][h]/(o[U][U]-o[h][h]))/2,v=A.identity(s,s),v[U][U]=i.cos(d),v[U][h]=-i.sin(d),v[h][U]=i.sin(d),v[h][h]=i.cos(d),B=A.multiply(B,v),g=A.multiply(A.multiply(A.inv(v),o),v),o=g,r=0,w=1;w<s;w++)for(Q=1;Q<s;Q++)w!=Q&&i.abs(o[w][Q])>.001&&(r=1)}for(w=0;w<s;w++)a.push(o[w][w]);return[B,a]},rungekutta:function(o,r,s,B,a,g){var w,Q,U,h,C;if(g===2)for(;B<=s;)w=r*o(B,a),Q=r*o(B+r,a+w),U=a+(w+Q)/2,a=U,B=B+r;if(g===4)for(;B<=s;)w=r*o(B,a),Q=r*o(B+r/2,a+w/2),h=r*o(B+r/2,a+Q/2),C=r*o(B+r,a+h),U=a+(w+2*Q+2*h+C)/6,a=U,B=B+r;return a},romberg:function(o,r,s,B){for(var a=0,g=(s-r)/2,w=[],Q=[],U=[],h,C,d,v,E;a<B/2;){for(E=o(r),d=r,v=0;d<=s;d=d+g,v++)w[v]=d;for(h=w.length,d=1;d<h-1;d++)E+=(d%2!==0?4:2)*o(w[d]);E=g/3*(E+o(s)),U[a]=E,g/=2,a++}for(C=U.length,h=1;C!==1;){for(d=0;d<C-1;d++)Q[d]=(i.pow(4,h)*U[d+1]-U[d])/(i.pow(4,h)-1);C=Q.length,U=Q,Q=[],h++}return U},richardson:function(o,r,s,B){function a(H,F){for(var p=0,m=H.length,I;p<m;p++)H[p]===F&&(I=p);return I}for(var g=i.abs(s-o[a(o,s)+1]),w=0,Q=[],U=[],h,C,d,v,E;B>=g;)h=a(o,s+B),C=a(o,s),Q[w]=(r[h]-2*r[C]+r[2*C-h])/(B*B),B/=2,w++;for(v=Q.length,d=1;v!=1;){for(E=0;E<v-1;E++)U[E]=(i.pow(4,d)*Q[E+1]-Q[E])/(i.pow(4,d)-1);v=U.length,Q=U,U=[],d++}return Q},simpson:function(o,r,s,B){for(var a=(s-r)/B,g=o(r),w=[],Q=r,U=0,h=1,C;Q<=s;Q=Q+a,U++)w[U]=Q;for(C=w.length;h<C-1;h++)g+=(h%2!==0?4:2)*o(w[h]);return a/3*(g+o(s))},hermite:function(o,r,s,B){for(var a=o.length,g=0,w=0,Q=[],U=[],h=[],C=[],d;w<a;w++){for(Q[w]=1,d=0;d<a;d++)w!=d&&(Q[w]*=(B-o[d])/(o[w]-o[d]));for(U[w]=0,d=0;d<a;d++)w!=d&&(U[w]+=1/(o[w]-o[d]));h[w]=(1-2*(B-o[w])*U[w])*(Q[w]*Q[w]),C[w]=(B-o[w])*(Q[w]*Q[w]),g+=h[w]*r[w]+C[w]*s[w]}return g},lagrange:function(o,r,s){for(var B=0,a=0,g,w,Q=o.length;a<Q;a++){for(w=r[a],g=0;g<Q;g++)a!=g&&(w*=(s-o[g])/(o[a]-o[g]));B+=w}return B},cubic_spline:function(o,r,s){for(var B=o.length,a=0,g,w=[],Q=[],U=[],h=[],C=[],d=[],v=[];a<B-1;a++)C[a]=o[a+1]-o[a];for(U[0]=0,a=1;a<B-1;a++)U[a]=3/C[a]*(r[a+1]-r[a])-3/C[a-1]*(r[a]-r[a-1]);for(a=1;a<B-1;a++)w[a]=[],Q[a]=[],w[a][a-1]=C[a-1],w[a][a]=2*(C[a-1]+C[a]),w[a][a+1]=C[a],Q[a][0]=U[a];for(h=A.multiply(A.inv(w),Q),g=0;g<B-1;g++)d[g]=(r[g+1]-r[g])/C[g]-C[g]*(h[g+1][0]+2*h[g][0])/3,v[g]=(h[g+1][0]-h[g][0])/(3*C[g]);for(g=0;g<B&&!(o[g]>s);g++);return g-=1,r[g]+(s-o[g])*d[g]+A.sq(s-o[g])*h[g]+(s-o[g])*A.sq(s-o[g])*v[g]},gauss_quadrature:function(){throw new Error("gauss_quadrature not yet implemented")},PCA:function(o){var r=o.length,s=o[0].length,B=0,a,g,w=[],Q=[],U=[],h=[],C=[],d=[],v=[],E=[],H=[],F=[];for(B=0;B<r;B++)w[B]=A.sum(o[B])/s;for(B=0;B<s;B++)for(v[B]=[],a=0;a<r;a++)v[B][a]=o[a][B]-w[a];for(v=A.transpose(v),B=0;B<r;B++)for(E[B]=[],a=0;a<r;a++)E[B][a]=A.dot([v[B]],[v[a]])/(s-1);for(U=A.jacobi(E),H=U[0],Q=U[1],F=A.transpose(H),B=0;B<Q.length;B++)for(a=B;a<Q.length;a++)Q[B]<Q[a]&&(g=Q[B],Q[B]=Q[a],Q[a]=g,h=F[B],F[B]=F[a],F[a]=h);for(d=A.transpose(v),B=0;B<r;B++)for(C[B]=[],a=0;a<d.length;a++)C[B][a]=A.dot([F[B]],[d[a]]);return[o,Q,F,C]}}),function(c){for(var o=0;o<c.length;o++)(function(r){A.fn[r]=function(s,B){var a=this;return B?(setTimeout(function(){B.call(a,A.fn[r].call(a,s))},15),this):typeof A[r](this,s)=="number"?A[r](this,s):A(A[r](this,s))}})(c[o])}("add divide multiply subtract dot pow exp log abs norm angle".split(" "))}(n,Math),function(A,i){var u=[].slice,f=A.utils.isNumber,l=A.utils.isArray;A.extend({zscore:function(){var r=u.call(arguments);return f(r[1])?(r[0]-r[1])/r[2]:(r[0]-A.mean(r[1]))/A.stdev(r[1],r[2])},ztest:function(){var r=u.call(arguments),s;return l(r[1])?(s=A.zscore(r[0],r[1],r[3]),r[2]===1?A.normal.cdf(-i.abs(s),0,1):A.normal.cdf(-i.abs(s),0,1)*2):r.length>2?(s=A.zscore(r[0],r[1],r[2]),r[3]===1?A.normal.cdf(-i.abs(s),0,1):A.normal.cdf(-i.abs(s),0,1)*2):(s=r[0],r[1]===1?A.normal.cdf(-i.abs(s),0,1):A.normal.cdf(-i.abs(s),0,1)*2)}}),A.extend(A.fn,{zscore:function(r,s){return(r-this.mean())/this.stdev(s)},ztest:function(r,s,B){var a=i.abs(this.zscore(r,B));return s===1?A.normal.cdf(-a,0,1):A.normal.cdf(-a,0,1)*2}}),A.extend({tscore:function(){var r=u.call(arguments);return r.length===4?(r[0]-r[1])/(r[2]/i.sqrt(r[3])):(r[0]-A.mean(r[1]))/(A.stdev(r[1],!0)/i.sqrt(r[1].length))},ttest:function(){var r=u.call(arguments),s;return r.length===5?(s=i.abs(A.tscore(r[0],r[1],r[2],r[3])),r[4]===1?A.studentt.cdf(-s,r[3]-1):A.studentt.cdf(-s,r[3]-1)*2):f(r[1])?(s=i.abs(r[0]),r[2]==1?A.studentt.cdf(-s,r[1]-1):A.studentt.cdf(-s,r[1]-1)*2):(s=i.abs(A.tscore(r[0],r[1])),r[2]==1?A.studentt.cdf(-s,r[1].length-1):A.studentt.cdf(-s,r[1].length-1)*2)}}),A.extend(A.fn,{tscore:function(r){return(r-this.mean())/(this.stdev(!0)/i.sqrt(this.cols()))},ttest:function(r,s){return s===1?1-A.studentt.cdf(i.abs(this.tscore(r)),this.cols()-1):A.studentt.cdf(-i.abs(this.tscore(r)),this.cols()-1)*2}}),A.extend({anovafscore:function(){var r=u.call(arguments),s,B,a,g,w,Q,U,h;if(r.length===1){for(w=new Array(r[0].length),U=0;U<r[0].length;U++)w[U]=r[0][U];r=w}for(B=new Array,U=0;U<r.length;U++)B=B.concat(r[U]);for(a=A.mean(B),s=0,U=0;U<r.length;U++)s=s+r[U].length*i.pow(A.mean(r[U])-a,2);for(s/=r.length-1,Q=0,U=0;U<r.length;U++)for(g=A.mean(r[U]),h=0;h<r[U].length;h++)Q+=i.pow(r[U][h]-g,2);return Q/=B.length-r.length,s/Q},anovaftest:function(){var r=u.call(arguments),s,B,a,g;if(f(r[0]))return 1-A.centralF.cdf(r[0],r[1],r[2]);var w=A.anovafscore(r);for(s=r.length-1,a=0,g=0;g<r.length;g++)a=a+r[g].length;return B=a-s-1,1-A.centralF.cdf(w,s,B)},ftest:function(r,s,B){return 1-A.centralF.cdf(r,s,B)}}),A.extend(A.fn,{anovafscore:function(){return A.anovafscore(this.toArray())},anovaftes:function(){var r=0,s;for(s=0;s<this.length;s++)r=r+this[s].length;return A.ftest(this.anovafscore(),this.length-1,r-this.length)}}),A.extend({qscore:function(){var r=u.call(arguments),s,B,a,g,w;return f(r[0])?(s=r[0],B=r[1],a=r[2],g=r[3],w=r[4]):(s=A.mean(r[0]),B=A.mean(r[1]),a=r[0].length,g=r[1].length,w=r[2]),i.abs(s-B)/(w*i.sqrt((1/a+1/g)/2))},qtest:function(){var r=u.call(arguments),s;r.length===3?(s=r[0],r=r.slice(1)):r.length===7?(s=A.qscore(r[0],r[1],r[2],r[3],r[4]),r=r.slice(5)):(s=A.qscore(r[0],r[1],r[2]),r=r.slice(3));var B=r[0],a=r[1];return 1-A.tukey.cdf(s,a,B-a)},tukeyhsd:function(r){for(var s=A.pooledstdev(r),B=r.map(function(h){return A.mean(h)}),a=r.reduce(function(h,C){return h+C.length},0),g=[],w=0;w<r.length;++w)for(var Q=w+1;Q<r.length;++Q){var U=A.qtest(B[w],B[Q],r[w].length,r[Q].length,s,a,r.length);g.push([[w,Q],U])}return g}}),A.extend({normalci:function(){var r=u.call(arguments),s=new Array(2),B;return r.length===4?B=i.abs(A.normal.inv(r[1]/2,0,1)*r[2]/i.sqrt(r[3])):B=i.abs(A.normal.inv(r[1]/2,0,1)*A.stdev(r[2])/i.sqrt(r[2].length)),s[0]=r[0]-B,s[1]=r[0]+B,s},tci:function(){var r=u.call(arguments),s=new Array(2),B;return r.length===4?B=i.abs(A.studentt.inv(r[1]/2,r[3]-1)*r[2]/i.sqrt(r[3])):B=i.abs(A.studentt.inv(r[1]/2,r[2].length-1)*A.stdev(r[2],!0)/i.sqrt(r[2].length)),s[0]=r[0]-B,s[1]=r[0]+B,s},significant:function(r,s){return r<s}}),A.extend(A.fn,{normalci:function(r,s){return A.normalci(r,s,this.toArray())},tci:function(r,s){return A.tci(r,s,this.toArray())}});function c(o,r,s,B){if(o>1||s>1||o<=0||s<=0)throw new Error("Proportions should be greater than 0 and less than 1");var a=(o*r+s*B)/(r+B),g=i.sqrt(a*(1-a)*(1/r+1/B));return(o-s)/g}A.extend(A.fn,{oneSidedDifferenceOfProportions:function(r,s,B,a){var g=c(r,s,B,a);return A.ztest(g,1)},twoSidedDifferenceOfProportions:function(r,s,B,a){var g=c(r,s,B,a);return A.ztest(g,2)}})}(n,Math),n.models=function(){function A(c){var o=c[0].length,r=n.arange(o).map(function(s){var B=n.arange(o).filter(function(a){return a!==s});return i(n.col(c,s).map(function(a){return a[0]}),n.col(c,B))});return r}function i(c,o){var r=c.length,s=o[0].length-1,B=r-s-1,a=n.lstsq(o,c),g=n.multiply(o,a.map(function(v){return[v]})).map(function(v){return v[0]}),w=n.subtract(c,g),Q=n.mean(c),U=n.sum(g.map(function(v){return Math.pow(v-Q,2)})),h=n.sum(c.map(function(v,E){return Math.pow(v-g[E],2)})),C=U+h,d=U/C;return{exog:o,endog:c,nobs:r,df_model:s,df_resid:B,coef:a,predict:g,resid:w,ybar:Q,SST:C,SSE:U,SSR:h,R2:d}}function u(c){var o=A(c.exog),r=Math.sqrt(c.SSR/c.df_resid),s=o.map(function(Q){var U=Q.SST,h=Q.R2;return r/Math.sqrt(U*(1-h))}),B=c.coef.map(function(Q,U){return(Q-0)/s[U]}),a=B.map(function(Q){var U=n.studentt.cdf(Q,c.df_resid);return(U>.5?1-U:U)*2}),g=n.studentt.inv(.975,c.df_resid),w=c.coef.map(function(Q,U){var h=g*s[U];return[Q-h,Q+h]});return{se:s,t:B,p:a,sigmaHat:r,interval95:w}}function f(c){var o=c.R2/c.df_model/((1-c.R2)/c.df_resid),r=function(B,a,g){return n.beta.cdf(B/(g/a+B),a/2,g/2)},s=1-r(o,c.df_model,c.df_resid);return{F_statistic:o,pvalue:s}}function l(c,o){var r=i(c,o),s=u(r),B=f(r),a=1-(1-r.R2)*((r.nobs-1)/r.df_resid);return r.t=s,r.f=B,r.adjust_R2=a,r}return{ols:l}}(),n.extend({buildxmatrix:function(){for(var i=new Array(arguments.length),u=0;u<arguments.length;u++){var f=[1];i[u]=f.concat(arguments[u])}return n(i)},builddxmatrix:function(){for(var i=new Array(arguments[0].length),u=0;u<arguments[0].length;u++){var f=[1];i[u]=f.concat(arguments[0][u])}return n(i)},buildjxmatrix:function(i){for(var u=new Array(i.length),f=0;f<i.length;f++)u[f]=i[f];return n.builddxmatrix(u)},buildymatrix:function(i){return n(i).transpose()},buildjymatrix:function(i){return i.transpose()},matrixmult:function(i,u){var f,l,c,o,r;if(i.cols()==u.rows()){if(u.rows()>1){for(o=[],f=0;f<i.rows();f++)for(o[f]=[],l=0;l<u.cols();l++){for(r=0,c=0;c<i.cols();c++)r+=i.toArray()[f][c]*u.toArray()[c][l];o[f][l]=r}return n(o)}for(o=[],f=0;f<i.rows();f++)for(o[f]=[],l=0;l<u.cols();l++){for(r=0,c=0;c<i.cols();c++)r+=i.toArray()[f][c]*u.toArray()[l];o[f][l]=r}return n(o)}},regress:function(i,u){var f=n.xtranspxinv(i),l=i.transpose(),c=n.matrixmult(n(f),l);return n.matrixmult(c,u)},regresst:function(i,u,f){var l=n.regress(i,u),c={};c.anova={};var o=n.jMatYBar(i,l);c.yBar=o;var r=u.mean();c.anova.residuals=n.residuals(u,o),c.anova.ssr=n.ssr(o,r),c.anova.msr=c.anova.ssr/(i[0].length-1),c.anova.sse=n.sse(u,o),c.anova.mse=c.anova.sse/(u.length-(i[0].length-1)-1),c.anova.sst=n.sst(u,r),c.anova.mst=c.anova.sst/(u.length-1),c.anova.r2=1-c.anova.sse/c.anova.sst,c.anova.r2<0&&(c.anova.r2=0),c.anova.fratio=c.anova.msr/c.anova.mse,c.anova.pvalue=n.anovaftest(c.anova.fratio,i[0].length-1,u.length-(i[0].length-1)-1),c.anova.rmse=Math.sqrt(c.anova.mse),c.anova.r2adj=1-c.anova.mse/c.anova.mst,c.anova.r2adj<0&&(c.anova.r2adj=0),c.stats=new Array(i[0].length);for(var s=n.xtranspxinv(i),B,a,g,w=0;w<l.length;w++)B=Math.sqrt(c.anova.mse*Math.abs(s[w][w])),a=Math.abs(l[w]/B),g=n.ttest(a,u.length-i[0].length-1,f),c.stats[w]=[l[w],B,a,g];return c.regress=l,c},xtranspx:function(i){return n.matrixmult(i.transpose(),i)},xtranspxinv:function(i){var u=n.matrixmult(i.transpose(),i),f=n.inv(u);return f},jMatYBar:function(i,u){var f=n.matrixmult(i,u);return new n(f)},residuals:function(i,u){return n.matrixsubtract(i,u)},ssr:function(i,u){for(var f=0,l=0;l<i.length;l++)f+=Math.pow(i[l]-u,2);return f},sse:function(i,u){for(var f=0,l=0;l<i.length;l++)f+=Math.pow(i[l]-u[l],2);return f},sst:function(i,u){for(var f=0,l=0;l<i.length;l++)f+=Math.pow(i[l]-u,2);return f},matrixsubtract:function(i,u){for(var f=new Array(i.length),l=0;l<i.length;l++){f[l]=new Array(i[l].length);for(var c=0;c<i[l].length;c++)f[l][c]=i[l][c]-u[l][c]}return n(f)}}),n.jStat=n,n})})(Mi);var Ef=Mi.exports;const bf=Vn(Ef),mf=Object.prototype.toString;function Ht(t){const e=mf.call(t);return e.endsWith("Array]")&&!e.includes("Big")}function Ji(t,e){if(!Ht(t)||!Ht(e))throw new TypeError("x and y must be arrays");if(t.length!==e.length)throw new RangeError("x and y arrays must have the same length")}class Tt{constructor(){if(new.target===Tt)throw new Error("BaseRegression must be subclassed")}predict(e){if(typeof e=="number")return this._predict(e);if(Ht(e)){const n=[];for(const A of e)n.push(this._predict(A));return n}else throw new TypeError("x must be a number or array")}_predict(e){throw new Error("_predict must be implemented")}train(){}toString(e){return""}toLaTeX(e){return""}score(e,n){Ji(e,n);const A=e.length,i=new Array(A);for(let a=0;a<A;a++)i[a]=this._predict(e[a]);let u=0,f=0,l=0,c=0,o=0,r=0,s=0;for(let a=0;a<A;a++)u+=i[a],f+=n[a],o+=i[a]*i[a],r+=n[a]*n[a],s+=i[a]*n[a],n[a]!==0&&(l+=(n[a]-i[a])*(n[a]-i[a])/n[a]),c+=(n[a]-i[a])*(n[a]-i[a]);const B=(A*s-u*f)/Math.sqrt((A*o-u*u)*(A*r-f*f));return{r:B,r2:B*B,chi2:l,rmsd:Math.sqrt(c/A)}}}function rt(t,e){return t<0?(t=0-t,typeof e=="number"?`- ${t.toPrecision(e)}`:`- ${t.toString()}`):typeof e=="number"?t.toPrecision(e):t.toString()}class Xi extends Tt{constructor(e,n){if(super(),e===!0){const A=n;this.slope=A.slope,this.intercept=A.intercept,this.coefficients=[A.intercept,A.slope]}else{Ji(e,n);const A=Hf(e,n);this.slope=A.slope,this.intercept=A.intercept,this.coefficients=[A.intercept,A.slope]}}toJSON(){return{name:"simpleLinearRegression",slope:this.slope,intercept:this.intercept}}_predict(e){return this.slope*e+this.intercept}computeX(e){return(e-this.intercept)/this.slope}toString(e){let n="f(x) = ";if(this.slope!==0){const A=rt(this.slope,e);if(n+=`${A==="1"?"":`${A} * `}x`,this.intercept!==0){const i=Math.abs(this.intercept),u=i===this.intercept?"+":"-";n+=` ${u} ${rt(i,e)}`}}else n+=rt(this.intercept,e);return n}toLaTeX(e){return this.toString(e)}static load(e){if(e.name!=="simpleLinearRegression")throw new TypeError("not a SLR model");return new Xi(!0,e)}}function Hf(t,e){const n=t.length;let A=0,i=0,u=0,f=0;for(let o=0;o<n;o++)A+=t[o],i+=e[o],u+=t[o]*t[o],f+=t[o]*e[o];const c=(n*f-A*i)/(n*u-A*A);return{slope:c,intercept:1/n*i-c*(1/n)*A}}export{Kf as P,Xi as S,Ef as a,Lf as h,bf as j,yf as v};
