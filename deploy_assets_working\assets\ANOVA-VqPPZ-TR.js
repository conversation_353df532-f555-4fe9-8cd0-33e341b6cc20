import{j as r,B as t,e as p,R as m,a6 as x,a7 as o}from"./mui-libs-CfwFIaTD.js";import{r as n}from"./react-libs-Cr2nE3UY.js";import l from"./OneWayANOVA-ai9Q6gnX.js";import A from"./TwoWayANOVA-CiVErBBN.js";import{RepeatedMeasuresANOVA as d}from"./RepeatedMeasuresANOVA-DwrjssMW.js";import"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";import"./StatsCard-op8tGQ0a.js";import"./descriptive-Djo0s6H4.js";import"./anova-DbTY6dHK.js";import"./math-setup-BTRs7Kau.js";import"./math-lib-BOZ-XUok.js";import"./non-parametric-Cf6Ds91x.js";import"./normality-CwHD6Rjl.js";import"./twoWayANOVA-CIsn6bwj.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import"./DatasetSelector-G08QHuaN.js";import"./VariableSelector-CPdlCsJ2.js";import"./repeatedMeasuresANOVA-B8bv3NGM.js";const D=()=>{const[e,s]=n.useState(0),a=(j,i)=>{s(i)};return r.jsxs(t,{p:3,children:[r.jsx(p,{variant:"h5",gutterBottom:!0,children:"ANOVA Tests"}),r.jsxs(m,{elevation:0,sx:{mb:3},children:[r.jsx(t,{sx:{borderBottom:1,borderColor:"divider"},children:r.jsxs(x,{value:e,onChange:a,"aria-label":"ANOVA type tabs",children:[r.jsx(o,{label:"One-Way ANOVA"}),r.jsx(o,{label:"Two-Way ANOVA"}),r.jsx(o,{label:"Repeated Measures ANOVA"})]})}),e===0&&r.jsx(t,{sx:{pt:2},children:r.jsx(l,{})}),e===1&&r.jsx(t,{sx:{pt:2},children:r.jsx(A,{})}),e===2&&r.jsx(t,{sx:{pt:2},children:r.jsx(d,{})})]})]})};export{D as default};
