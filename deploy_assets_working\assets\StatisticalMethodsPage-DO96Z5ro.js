import{j as e,B as n,G as o,R as p,e as s,L as l,a1 as u,r as t,D as j,Z as m,X as c,m as a,a0 as y,a2 as f,W as v}from"./mui-libs-CfwFIaTD.js";import{r as b,b as w}from"./react-libs-Cr2nE3UY.js";import{H as P}from"./index-Bpan7Tbe.js";import{P as A}from"./PageTitle-DA3BXQ4x.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const h=[{id:"desc-numerical",title:"Descriptive Analysis of Numerical Data",icon:e.jsx(m,{}),content:e.jsx(s,{variant:"body1",paragraph:!0,children:"Descriptive analysis summarizes numerical data using measures of central tendency (mean, median, mode) and spread (range, variance, standard deviation, IQR). It helps identify patterns, outliers, and data distribution shapes (e.g., normal, skewed). Tools like histograms, box plots, and density plots visualize distributions, while summary tables provide key metrics. For example, calculating the average income and standard deviation in a population reveals income inequality. Use this analysis to explore data before advanced modeling."})},{id:"desc-categorical",title:"Descriptive Analysis of Categorical Data",icon:e.jsx(m,{}),content:e.jsx(s,{variant:"body1",paragraph:!0,children:'For categorical variables (e.g., gender, product categories), summarize data using frequency tables, proportions, or percentages. Bar charts and pie charts display category distributions, while mode identifies the most frequent value. For ordinal data (e.g., survey ratings), median or percentile rankings add depth. For instance, a frequency table showing "70% of customers prefer Product A" highlights dominant trends. This analysis sets the stage for inferential tests like chi-square.'})},{id:"t-tests",title:"t-Tests",icon:e.jsx(c,{}),content:e.jsxs(e.Fragment,{children:[e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"One-sample t-test:"})," Compares a sample mean to a known population mean (e.g., testing if average exam scores differ from 75)."]}),e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Paired t-test:"})," Measures mean differences in related groups (e.g., pre- vs. post-treatment results)."]}),e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Independent samples t-test:"})," Compares means between two unrelated groups (e.g., male vs. female salaries)."]}),e.jsx(s,{variant:"body1",paragraph:!0,children:"Assumptions: Normality, equal variance (use Welch’s t-test if variances differ)."})]})},{id:"non-parametric",title:"Non-Parametric Tests",icon:e.jsx(c,{}),content:e.jsxs(e.Fragment,{children:[e.jsx(s,{variant:"body1",paragraph:!0,children:"Use when data violates normality or is ordinal:"}),e.jsxs(l,{dense:!0,children:[e.jsx(a,{children:e.jsx(t,{primary:"Mann-Whitney U test: Non-parametric alternative to the independent t-test."})}),e.jsx(a,{children:e.jsx(t,{primary:"Wilcoxon signed-rank test: Replaces paired t-test for non-normal data."})}),e.jsx(a,{children:e.jsx(t,{primary:"Kruskal-Wallis test: Compares medians across ≥3 groups (alternative to ANOVA)."})})]})]})},{id:"anova",title:"ANOVA",icon:e.jsx(c,{}),content:e.jsxs(e.Fragment,{children:[e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"One-Way ANOVA:"})," Tests mean differences across ≥3 independent groups (e.g., comparing crop yields for three fertilizers). Post-hoc tests (Tukey, Bonferroni) identify which groups differ."]}),e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Two-Way ANOVA:"})," Examines the effect of two independent variables (factors) and their interaction (e.g., testing how both diet and exercise affect weight loss)."]})]})},{id:"correlation-chi-square",title:"Correlation & Chi-Square Test",icon:e.jsx(y,{}),content:e.jsxs(e.Fragment,{children:[e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Correlation (Pearson/Spearman):"})," Quantifies linear (Pearson) or monotonic (Spearman) relationships between two numerical variables (e.g., height vs. weight)."]}),e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Chi-Square Test of Association:"})," Tests independence between categorical variables (e.g., checking if education level and job type are related)."]})]})},{id:"regression",title:"Regression Models",icon:e.jsx(f,{}),content:e.jsxs(e.Fragment,{children:[e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Linear Regression:"})," Predicts a continuous outcome using one/more predictors (e.g., forecasting sales based on ad spend)."]}),e.jsxs(s,{variant:"body1",paragraph:!0,children:[e.jsx("strong",{children:"Logistic Regression:"})," Predicts binary outcomes (e.g., yes/no) using predictors (e.g., likelihood of loan default based on income/credit score)."]})]})},{id:"essential-charts",title:"Essential Charts",icon:e.jsx(v,{}),content:e.jsxs(l,{dense:!0,children:[e.jsx(a,{children:e.jsx(t,{primary:"Bar Chart: Compares categories (e.g., sales by region)."})}),e.jsx(a,{children:e.jsx(t,{primary:"Pie Chart: Shows proportions (limit to ≤6 categories)."})}),e.jsx(a,{children:e.jsx(t,{primary:"Scatter Plot: Reveals relationships/correlations between variables."})}),e.jsx(a,{children:e.jsx(t,{primary:"Histogram: Displays numerical data distribution (e.g., age frequencies)."})}),e.jsx(a,{children:e.jsx(t,{primary:"Box Plot: Summarizes spread, median, and outliers (e.g., income distribution by education)."})})]})}],D=()=>{const d=b.useRef([]),g=r=>{var i;(i=d.current[r])==null||i.scrollIntoView({behavior:"smooth",block:"start"})};return e.jsxs(e.Fragment,{children:[e.jsxs(P,{children:[e.jsx("title",{children:"Statistical Methods Guide - DataStatPro"}),e.jsx("meta",{name:"description",content:"Overview of common statistical methods including descriptive analysis, t-tests, non-parametric tests, ANOVA, correlation, chi-square, regression models, and essential charts."})]}),e.jsxs(n,{sx:{p:3},children:[e.jsx(A,{title:"Statistical Methods"}),e.jsxs(o,{container:!0,spacing:3,sx:{mt:2},children:[e.jsx(o,{item:!0,xs:12,md:3,children:e.jsxs(p,{sx:{p:2,position:"sticky",top:"80px"},children:[e.jsx(s,{variant:"h6",gutterBottom:!0,children:"Methods Overview"}),e.jsx(l,{component:"nav",dense:!0,children:h.map((r,i)=>e.jsxs(u,{onClick:()=>g(i),children:[r.icon&&e.jsx(n,{sx:{mr:1.5,display:"flex",alignItems:"center",color:"primary.main"},children:w.cloneElement(r.icon,{fontSize:"small"})}),e.jsx(t,{primary:r.title,primaryTypographyProps:{variant:"body2"}})]},r.id))})]})}),e.jsx(o,{item:!0,xs:12,md:9,children:h.map((r,i)=>e.jsxs(p,{sx:{p:3,mb:3},ref:x=>d.current[i]=x,id:r.id,children:[e.jsxs(s,{variant:"h5",gutterBottom:!0,component:"div",sx:{display:"flex",alignItems:"center"},children:[r.icon&&e.jsx(n,{sx:{mr:1,display:"flex",alignItems:"center",color:"primary.main"},children:r.icon}),r.title]}),e.jsx(j,{sx:{mb:2}}),r.content]},r.id))})]})]})]})};export{D as default};
