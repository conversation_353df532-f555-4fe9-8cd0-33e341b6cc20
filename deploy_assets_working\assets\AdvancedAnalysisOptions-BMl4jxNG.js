import{j as a,u as w,C as F,R as c,i as n,e as s,B as t,h as d,G as h,k as D,bz as I,aE as T,I as E,aF as u,d as B,l as k,f as z,bA as S,aN as l,c0 as p,a0 as o,aB as x,a2 as m,c1 as g,ac as v}from"./mui-libs-CfwFIaTD.js";import{r as M}from"./react-libs-Cr2nE3UY.js";const y=[{name:"Exploratory Factor Analysis (EFA)",shortDescription:"Identify underlying factors in a set of variables",detailedDescription:"Perform Exploratory Factor Analysis to uncover the latent structure among a set of observed variables. This technique is used to reduce a large number of variables into a smaller, more manageable set of factors for use in other analyses.",path:"advanced-analysis/efa",icon:a.jsx(l,{}),category:"Factor Analysis",color:"#FF5722"},{name:"Confirmatory Factor Analysis (CFA)",shortDescription:"Test a hypothesized factor structure",detailedDescription:"Conduct Confirmatory Factor Analysis to evaluate how well a measured set of variables represents a smaller number of latent constructs. This method is used to confirm or reject measurement models.",path:"advanced-analysis/cfa",icon:a.jsx(p,{}),category:"Confirmatory Factor Analysis",color:"#4CAF50"},{name:"Mediation and Moderation",shortDescription:"Analyze indirect and conditional effects",detailedDescription:"Examine how a third variable (mediator) explains the relationship between two other variables, or how the relationship between two variables depends on a third variable (moderator).",path:"advanced-analysis/mediation",icon:a.jsx(o,{}),category:"Regression",color:"#673AB7"},{name:"Reliability Analysis",shortDescription:"Assess the consistency of measurements",detailedDescription:"Evaluate the internal consistency of a scale or test using methods like Cronbach's alpha. Essential for validating questionnaires and psychometric instruments.",path:"advanced-analysis/reliability",icon:a.jsx(x,{}),category:"Reliability",color:"#00BCD4"},{name:"Survival Analysis",shortDescription:"Analyze time-to-event data",detailedDescription:"Study the time until an event occurs, such as death, disease onset, or equipment failure. Includes methods like Kaplan-Meier curves and Cox proportional hazards regression.",path:"advanced-analysis/survival",icon:a.jsx(m,{}),category:"Survival",color:"#8BC34A"},{name:"Cluster Analysis",shortDescription:"Group similar data points into clusters",detailedDescription:"Identify natural groupings in your data using algorithms like K-means, hierarchical clustering, and DBSCAN. Useful for customer segmentation, pattern recognition, and data exploration.",path:"advanced-analysis/cluster",icon:a.jsx(g,{}),category:"Cluster Analysis",color:"#9C27B0"},{name:"Meta Analysis",shortDescription:"Synthesize findings from multiple studies",detailedDescription:"Combine results from independent studies to produce a single estimate of a treatment effect or association. Essential for evidence-based practice and systematic reviews.",path:"advanced-analysis/meta-analysis",icon:a.jsx(o,{}),category:"Meta Analysis",color:"#2196F3"},{name:"Variable Tree Analysis",shortDescription:"Create hierarchical tree visualizations of variable relationships",detailedDescription:"Build custom tree structures to explore how 2-4 variables relate to each other at different hierarchical levels. Visualize data relationships with interactive tree diagrams showing statistical summaries at each node.",path:"advanced-analysis/variable-tree",icon:a.jsx(v,{}),category:"Variable Tree Analysis",color:"#795548"}],q=({onNavigate:f})=>{const r=w(),[i,b]=M.useState("All"),j=["All","Factor Analysis","Confirmatory Factor Analysis","Regression","Reliability","Survival","Cluster Analysis","Meta Analysis","Variable Tree Analysis"],A=i==="All"?y:y.filter(e=>e.category===i),C=e=>{switch(e){case"Factor Analysis":return a.jsx(l,{});case"Regression":return a.jsx(o,{});case"Reliability":return a.jsx(x,{});case"Survival":return a.jsx(m,{});case"Cluster Analysis":return a.jsx(g,{});case"Meta Analysis":return a.jsx(o,{});case"Confirmatory Factor Analysis":return a.jsx(p,{});case"Variable Tree Analysis":return a.jsx(v,{});default:return a.jsx(l,{})}};return a.jsxs(F,{maxWidth:"lg",sx:{py:4},children:[a.jsxs(c,{elevation:0,sx:{p:4,mb:4,background:`linear-gradient(135deg, ${n(r.palette.primary.main,.1)} 0%, ${n(r.palette.secondary.main,.1)} 100%)`,borderRadius:2},children:[a.jsx(s,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Advanced Analysis Tools"}),a.jsx(s,{variant:"h6",color:"text.secondary",paragraph:!0,children:"Explore advanced statistical techniques for deeper insights"}),a.jsx(s,{variant:"body1",color:"text.secondary",children:"Access a suite of powerful tools for complex statistical modeling and analysis, including factor analysis, mediation/moderation, reliability assessment, and survival analysis."})]}),a.jsxs(t,{sx:{mb:4},children:[a.jsx(s,{variant:"h6",gutterBottom:!0,children:"Filter by Category"}),a.jsx(t,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:j.map(e=>a.jsx(d,{label:e,onClick:()=>b(e),variant:i===e?"filled":"outlined",color:i===e?"primary":"default",icon:e!=="All"?C(e):void 0,sx:{"&:hover":{backgroundColor:i===e?r.palette.primary.dark:n(r.palette.primary.main,.1)}}},e))})]}),a.jsx(h,{container:!0,spacing:3,children:A.map((e,R)=>a.jsx(h,{item:!0,xs:12,md:6,lg:4,children:a.jsxs(D,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:r.shadows[8],"& .launch-button":{backgroundColor:e.color,color:"white"}}},children:[a.jsx(I,{avatar:a.jsx(B,{sx:{bgcolor:e.color,width:48,height:48},children:e.icon}),title:a.jsx(s,{variant:"h6",fontWeight:"bold",children:e.name}),subheader:a.jsx(t,{sx:{display:"flex",gap:1,mt:1,flexWrap:"wrap"},children:a.jsx(d,{label:e.category,size:"small",variant:"outlined",color:"primary"})}),action:a.jsx(T,{title:"More information",children:a.jsx(E,{size:"small",children:a.jsx(u,{})})})}),a.jsxs(k,{sx:{flexGrow:1,pt:0},children:[a.jsx(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:e.shortDescription}),a.jsx(s,{variant:"body2",paragraph:!0,children:e.detailedDescription})]}),a.jsx(t,{sx:{p:2,pt:0},children:a.jsxs(z,{className:"launch-button",variant:"outlined",fullWidth:!0,onClick:()=>f(e.path),endIcon:a.jsx(S,{}),sx:{borderColor:e.color,color:e.color,fontWeight:"bold","&:hover":{borderColor:e.color}},children:["Launch ",e.name]})})]})},e.name))}),a.jsx(c,{elevation:1,sx:{p:3,mt:4,backgroundColor:n(r.palette.info.main,.05),border:`1px solid ${n(r.palette.info.main,.2)}`},children:a.jsxs(t,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[a.jsx(u,{color:"info"}),a.jsxs(t,{children:[a.jsx(s,{variant:"h6",gutterBottom:!0,color:"info.main",children:"Need Help Choosing?"}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Exploring data structure?"})," Try Exploratory Factor Analysis (EFA)"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Understanding relationships?"})," Use Mediation and Moderation"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Validating scales?"})," Assess with Reliability Analysis"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Analyzing time-to-event?"})," Survival Analysis is the tool for you"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Looking for natural groupings in data?"})," Try Cluster Analysis"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Confirming a measurement model?"})," Use Confirmatory Factor Analysis (CFA)"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",paragraph:!0,children:["• ",a.jsx("strong",{children:"Synthesizing research findings?"})," Meta Analysis can help"]}),a.jsxs(s,{variant:"body2",color:"text.secondary",children:["• ",a.jsx("strong",{children:"Exploring variable hierarchies?"})," Variable Tree Analysis provides visual insights"]})]})]})})]})};export{q as A,y as a};
