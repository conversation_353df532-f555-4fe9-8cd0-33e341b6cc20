var Ue=Object.defineProperty;var De=(i,s,c)=>s in i?Ue(i,s,{enumerable:!0,configurable:!0,writable:!0,value:c}):i[s]=c;var ye=(i,s,c)=>De(i,typeof s!="symbol"?s+"":s,c);import{u as V,j as e,B as n,ae as Z,e as t,g as C,f as T,bH as te,G as h,bq as se,aX as we,v as K,O as Se,bt as Ae,dh as Re,k as D,l as R,bv as M,i as L,h as z,ah as O,cU as ze,am as Pe,ao as re,R as Y,ap as ie,aq as ae,ar as P,as as o,at as ne,aE as Ie,I as H,aI as Ce,di as Fe,bS as ue,bT as me,bU as je,ai as de,b9 as xe,ba as he,bb as E,aj as Be,b2 as Le,bV as ge,J as ve,E as Ne,y as Ee,a2 as Me,d3 as $e,Q as ke,dj as Oe,ay as He,bj as We,D as Ye,aF as Ge,L as be,m as F,n as le,aW as Te,r as B,by as qe,br as fe,bu as Ke,C as ce,a6 as Ve,a7 as Ze,d1 as Xe,dk as Je,dl as Qe,dm as es,W as ss,aZ as ts}from"./mui-libs-CfwFIaTD.js";import{r as l}from"./react-libs-Cr2nE3UY.js";import{s as W,b as pe,N as _e,C as Q,u as rs}from"./index-Bpan7Tbe.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./other-utils-CR9xr_gI.js";const $=({title:i,value:s,icon:c,color:u,subtitle:p,trend:j})=>{const y=V();return e.jsx(D,{elevation:0,variant:"outlined",sx:{height:"100%",borderRadius:2,transition:"all 0.3s ease","&:hover":{transform:"translateY(-2px)",boxShadow:y.shadows[4]}},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(n,{sx:{display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:2},children:[e.jsx(n,{sx:{p:1.5,borderRadius:2,backgroundColor:L(u,.1),color:u},children:c}),j&&e.jsx(z,{size:"small",label:j.label,color:j.positive?"success":"error",variant:"outlined"})]}),e.jsx(t,{variant:"h4",component:"div",fontWeight:"bold",color:u,gutterBottom:!0,children:typeof s=="number"?s.toLocaleString():s}),e.jsx(t,{variant:"h6",color:"text.primary",gutterBottom:!0,children:i}),p&&e.jsx(t,{variant:"body2",color:"text.secondary",children:p})]})})},is=()=>{const i=V(),[s,c]=l.useState(null),[u,p]=l.useState(!0),[j,y]=l.useState(null);l.useEffect(()=>{b()},[]);const b=async(v=0)=>{var I,x;try{v===0&&p(!0),y(null);const{data:f,error:w}=await W.rpc("get_user_statistics");if(w)throw w;c(f),console.log("✅ System statistics loaded successfully")}catch(f){if(console.error("Error fetching system statistics:",f),v<2&&((I=f.message)!=null&&I.includes("network")||(x=f.message)!=null&&x.includes("connection"))){console.log(`🔄 Retrying system statistics fetch (attempt ${v+1})`),setTimeout(()=>b(v+1),1e3*(v+1));return}y(f.message||"Failed to load system statistics. Please try refreshing the page.")}finally{v===0&&p(!1)}};if(u)return e.jsx(n,{sx:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:300},children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsx(Z,{size:48}),e.jsx(t,{variant:"h6",sx:{mt:2},children:"Loading system overview..."})]})});if(j)return e.jsxs(C,{severity:"error",sx:{mb:3},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Error Loading System Statistics"}),e.jsx(t,{children:j})]});if(!s)return e.jsx(C,{severity:"warning",children:e.jsx(t,{children:"No system statistics available."})});const S=s.users_last_30_days>0?(s.users_last_7_days/s.users_last_30_days*100).toFixed(1):"0",A=s.total_users>0?(s.active_users_last_7_days/s.total_users*100).toFixed(1):"0",r=s.total_users>0?(s.users_with_datasets/s.total_users*100).toFixed(1):"0";return e.jsxs(n,{sx:{width:"100%"},children:[e.jsxs(n,{sx:{mb:4,display:"flex",flexDirection:{xs:"column",sm:"row"},justifyContent:"space-between",alignItems:{xs:"flex-start",sm:"center"},gap:2},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",gutterBottom:!0,sx:{fontSize:{xs:"1.5rem",sm:"2rem"}},children:"System Overview"}),e.jsx(t,{variant:"body1",color:"text.secondary",sx:{fontSize:{xs:"0.875rem",sm:"1rem"}},children:"Real-time statistics and key metrics for DataStatPro"})]}),e.jsx(T,{variant:"outlined",startIcon:e.jsx(te,{}),onClick:()=>b(),disabled:u,size:"small",children:"Refresh"})]}),e.jsxs(h,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(h,{item:!0,xs:12,sm:6,md:4,children:e.jsx($,{title:"Total Users",value:s.total_users,icon:e.jsx(se,{}),color:i.palette.primary.main,subtitle:"Registered accounts",trend:{value:s.users_last_7_days,label:`+${s.users_last_7_days} this week`,positive:!0}})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,children:e.jsx($,{title:"Active Users",value:s.active_users_last_7_days,icon:e.jsx(we,{}),color:i.palette.success.main,subtitle:`${A}% of total users`})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,children:e.jsx($,{title:"Admin Users",value:s.admin_users,icon:e.jsx(K,{}),color:i.palette.error.main,subtitle:"System administrators"})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,children:e.jsx($,{title:"Total Datasets",value:s.total_datasets,icon:e.jsx(Se,{}),color:i.palette.info.main,subtitle:`${s.users_with_datasets} users with data`})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,children:e.jsx($,{title:"Pro Users",value:s.pro_users+s.edu_pro_users,icon:e.jsx(Ae,{}),color:i.palette.warning.main,subtitle:"Paid subscriptions"})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,children:e.jsx($,{title:"New Users (30d)",value:s.users_last_30_days,icon:e.jsx(Re,{}),color:i.palette.secondary.main,subtitle:`Growth rate: ${S}%`})})]}),e.jsxs(h,{container:!0,spacing:3,children:[e.jsx(h,{item:!0,xs:12,md:6,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Account Type Distribution"}),e.jsxs(n,{sx:{mt:3},children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(t,{variant:"body2",children:"Standard"}),e.jsxs(t,{variant:"body2",fontWeight:"bold",children:[s.standard_users," (",(s.standard_users/s.total_users*100).toFixed(1),"%)"]})]}),e.jsx(M,{variant:"determinate",value:s.standard_users/s.total_users*100,sx:{mb:2,height:8,borderRadius:4}}),e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(t,{variant:"body2",children:"Pro"}),e.jsxs(t,{variant:"body2",fontWeight:"bold",children:[s.pro_users," (",(s.pro_users/s.total_users*100).toFixed(1),"%)"]})]}),e.jsx(M,{variant:"determinate",value:s.pro_users/s.total_users*100,color:"warning",sx:{mb:2,height:8,borderRadius:4}}),e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(t,{variant:"body2",children:"Educational"}),e.jsxs(t,{variant:"body2",fontWeight:"bold",children:[s.edu_users," (",(s.edu_users/s.total_users*100).toFixed(1),"%)"]})]}),e.jsx(M,{variant:"determinate",value:s.edu_users/s.total_users*100,color:"info",sx:{mb:2,height:8,borderRadius:4}}),e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(t,{variant:"body2",children:"Educational Pro"}),e.jsxs(t,{variant:"body2",fontWeight:"bold",children:[s.edu_pro_users," (",(s.edu_pro_users/s.total_users*100).toFixed(1),"%)"]})]}),e.jsx(M,{variant:"determinate",value:s.edu_pro_users/s.total_users*100,color:"secondary",sx:{height:8,borderRadius:4}})]})]})})}),e.jsx(h,{item:!0,xs:12,md:6,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"User Engagement"}),e.jsxs(n,{sx:{mt:3},children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[e.jsx(t,{variant:"body2",children:"Data Usage Rate"}),e.jsxs(t,{variant:"body2",fontWeight:"bold",children:[r,"%"]})]}),e.jsx(M,{variant:"determinate",value:parseFloat(r),color:"success",sx:{mb:3,height:8,borderRadius:4}}),e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[e.jsx(t,{variant:"body2",children:"Weekly Active Rate"}),e.jsxs(t,{variant:"body2",fontWeight:"bold",children:[A,"%"]})]}),e.jsx(M,{variant:"determinate",value:parseFloat(A),color:"primary",sx:{height:8,borderRadius:4}})]})]})})})]})]})},as=({open:i,user:s,onClose:c,onSave:u})=>{const[p,j]=l.useState("standard"),[y,b]=l.useState(!1),[S,A]=l.useState(!1);l.useEffect(()=>{s&&(j(s.accounttype||"standard"),b(s.is_admin||!1))},[s]);const r=async()=>{if(s){A(!0);try{await u(s.id,{accounttype:p,is_admin:y}),c()}catch(v){console.error("Error saving user:",v)}finally{A(!1)}}};return e.jsxs(ue,{open:i,onClose:c,maxWidth:"sm",fullWidth:!0,children:[e.jsxs(me,{children:["Edit User: ",(s==null?void 0:s.full_name)||(s==null?void 0:s.email)]}),e.jsx(je,{children:e.jsxs(n,{sx:{pt:2},children:[e.jsxs(de,{fullWidth:!0,sx:{mb:3},children:[e.jsx(xe,{children:"Account Type"}),e.jsxs(he,{value:p,label:"Account Type",onChange:v=>j(v.target.value),children:[e.jsx(E,{value:"standard",children:"Standard"}),e.jsx(E,{value:"pro",children:"Pro"}),e.jsx(E,{value:"edu",children:"Educational"}),e.jsx(E,{value:"edu_pro",children:"Educational Pro"})]})]}),e.jsx(Be,{control:e.jsx(Le,{checked:y,onChange:v=>b(v.target.checked),color:"error"}),label:"Admin Privileges",sx:{mb:2}}),y&&e.jsx(C,{severity:"warning",sx:{mb:2},children:e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Warning:"})," Admin privileges grant full system access. Only assign to trusted users."]})})]})}),e.jsxs(ge,{children:[e.jsx(T,{onClick:c,disabled:S,children:"Cancel"}),e.jsx(T,{onClick:r,variant:"contained",disabled:S,startIcon:S?e.jsx(Z,{size:16}):void 0,children:S?"Saving...":"Save Changes"})]})]})},ns=()=>{const[i,s]=l.useState([]),[c,u]=l.useState(!0),[p,j]=l.useState(null),[y,b]=l.useState(""),[S,A]=l.useState(1),[r,v]=l.useState(1),[I,x]=l.useState(!1),[f,w]=l.useState(null),_=25;l.useEffect(()=>{g()},[S,y]);const g=async(d=0)=>{var a,m;try{d===0&&u(!0),j(null);const U=(S-1)*_,{data:N,error:J}=await W.rpc("get_all_users",{page_size:_,page_offset:U,search_term:y||null});if(J)throw J;s(N||[]),v(Math.max(1,Math.ceil(((N==null?void 0:N.length)||0)/_))),console.log("✅ Users loaded successfully")}catch(U){if(console.error("Error fetching users:",U),d<2&&((a=U.message)!=null&&a.includes("network")||(m=U.message)!=null&&m.includes("connection"))){console.log(`🔄 Retrying users fetch (attempt ${d+1})`),setTimeout(()=>g(d+1),1e3*(d+1));return}j(U.message||"Failed to load users. Please try refreshing the page.")}finally{d===0&&u(!1)}},k=d=>{w(d),x(!0)},oe=async(d,a)=>{try{const{error:m}=await W.rpc("update_user_account_type",{target_user_id:d,new_account_type:a.accounttype});if(m)throw m;const{error:U}=await W.rpc("update_user_admin_status",{target_user_id:d,new_admin_status:a.is_admin});if(U)throw U;await g()}catch(m){throw console.error("Error updating user:",m),m}},X=d=>{const a=d||"standard",m={standard:{label:"Standard",color:"default",icon:e.jsx(Ee,{})},pro:{label:"Pro",color:"warning",icon:e.jsx(Ne,{})},edu:{label:"Educational",color:"info",icon:e.jsx(ve,{})},edu_pro:{label:"Edu Pro",color:"secondary",icon:e.jsx(ve,{})}},{label:U,color:N,icon:J}=m[a]||m.standard;return e.jsx(z,{label:U,color:N,size:"small",icon:J,variant:"outlined"})},G=d=>d?new Date(d).toLocaleDateString():"Not available";return c&&i.length===0?e.jsx(n,{sx:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:300},children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsx(Z,{size:48}),e.jsx(t,{variant:"h6",sx:{mt:2},children:"Loading users..."})]})}):e.jsxs(n,{sx:{width:"100%",height:"100%"},children:[e.jsxs(n,{sx:{mb:3,display:"flex",flexDirection:{xs:"column",sm:"row"},justifyContent:"space-between",alignItems:{xs:"flex-start",sm:"center"},gap:2},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",gutterBottom:!0,sx:{fontSize:{xs:"1.5rem",sm:"2rem"}},children:"User Management"}),e.jsx(t,{variant:"body1",color:"text.secondary",sx:{fontSize:{xs:"0.875rem",sm:"1rem"}},children:"Manage user accounts, permissions, and account types"})]}),e.jsx(T,{variant:"outlined",startIcon:e.jsx(te,{}),onClick:g,disabled:c,children:"Refresh"})]}),p&&e.jsx(C,{severity:"error",sx:{mb:3},children:p}),e.jsx(n,{sx:{mb:3},children:e.jsx(O,{fullWidth:!0,placeholder:"Search users by name, email, or institution...",value:y,onChange:d=>b(d.target.value),InputProps:{startAdornment:e.jsx(ze,{position:"start",children:e.jsx(Pe,{})})},sx:{maxWidth:{xs:"100%",sm:500}},size:"small"})}),e.jsx(n,{sx:{width:"100%",overflow:"auto"},children:e.jsx(re,{component:Y,variant:"outlined",sx:{borderRadius:2,maxHeight:{xs:"60vh",lg:"70vh"},minWidth:800,transition:"max-height 0.3s ease-in-out"},children:e.jsxs(ie,{stickyHeader:!0,children:[e.jsx(ae,{children:e.jsxs(P,{children:[e.jsx(o,{sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"User"}),e.jsx(o,{sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"Account Type"}),e.jsx(o,{sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"Admin"}),e.jsx(o,{sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"Institution"}),e.jsx(o,{sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"Created"}),e.jsx(o,{sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"Last Sign In"}),e.jsx(o,{align:"center",sx:{fontWeight:"bold",bgcolor:"grey.50"},children:"Actions"})]})}),e.jsx(ne,{children:i.map(d=>e.jsxs(P,{hover:!0,children:[e.jsx(o,{sx:{minWidth:200},children:e.jsxs(n,{children:[e.jsx(t,{variant:"body2",fontWeight:"bold",sx:{wordBreak:"break-word"},children:d.full_name||"No name"}),e.jsx(t,{variant:"caption",color:"text.secondary",sx:{wordBreak:"break-all"},children:d.email||d.username||"No email available"}),d.username&&d.username!==d.email&&e.jsxs(t,{variant:"caption",color:"text.secondary",display:"block",sx:{wordBreak:"break-word"},children:["@",d.username]})]})}),e.jsx(o,{sx:{minWidth:120},children:X(d.accounttype)}),e.jsx(o,{sx:{minWidth:100},children:d.is_admin===!0?e.jsx(z,{label:"Admin",color:"error",size:"small",icon:e.jsx(K,{}),variant:"outlined"}):e.jsx(t,{variant:"body2",color:"text.secondary",children:"User"})}),e.jsxs(o,{sx:{minWidth:150},children:[e.jsx(t,{variant:"body2",sx:{wordBreak:"break-word"},children:d.institution||"Not specified"}),d.country&&e.jsx(t,{variant:"caption",color:"text.secondary",display:"block",children:d.country})]}),e.jsx(o,{sx:{minWidth:100},children:e.jsx(t,{variant:"body2",children:G(d.created_at)})}),e.jsx(o,{sx:{minWidth:100},children:e.jsx(t,{variant:"body2",children:G(d.last_sign_in_at)})}),e.jsx(o,{align:"center",sx:{minWidth:80},children:e.jsx(Ie,{title:"Edit User",children:e.jsx(H,{size:"small",onClick:()=>k(d),color:"primary",children:e.jsx(Ce,{})})})})]},d.id))})]})})}),r>1&&e.jsx(n,{sx:{display:"flex",justifyContent:"center",mt:3},children:e.jsx(Fe,{count:r,page:S,onChange:(d,a)=>A(a),color:"primary"})}),e.jsx(as,{open:I,user:f,onClose:()=>{x(!1),w(null)},onSave:oe})]})},ee=({title:i,value:s,subtitle:c,icon:u,color:p,change:j})=>{const y=V();return e.jsx(D,{elevation:0,variant:"outlined",sx:{height:"100%",borderRadius:2,transition:"all 0.3s ease","&:hover":{transform:"translateY(-2px)",boxShadow:y.shadows[4]}},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(n,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(n,{sx:{p:1.5,borderRadius:2,backgroundColor:L(p,.1),color:p,mr:2},children:u}),e.jsxs(n,{sx:{flexGrow:1},children:[e.jsx(t,{variant:"h4",component:"div",fontWeight:"bold",color:p,children:typeof s=="number"?s.toLocaleString():s}),e.jsx(t,{variant:"h6",color:"text.primary",children:i})]})]}),e.jsx(t,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:c}),j&&e.jsx(n,{sx:{display:"flex",alignItems:"center",mt:1},children:e.jsx(z,{size:"small",label:`${j.positive?"+":""}${j.value} ${j.period}`,color:j.positive?"success":"error",variant:"outlined"})})]})})},os=()=>{const i=V(),[s,c]=l.useState(null),[u,p]=l.useState(!0),[j,y]=l.useState(null);l.useEffect(()=>{b();const r=setInterval(b,5*60*1e3);return()=>clearInterval(r)},[]);const b=async()=>{try{y(null);const{data:r,error:v}=await W.rpc("get_user_statistics");if(v)throw v;c(r)}catch(r){console.error("Error fetching system statistics:",r),y(r.message||"Failed to load system statistics")}finally{p(!1)}};if(u)return e.jsx(n,{sx:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:300},children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsx(Z,{size:48}),e.jsx(t,{variant:"h6",sx:{mt:2},children:"Loading system statistics..."})]})});if(j)return e.jsxs(C,{severity:"error",sx:{mb:3},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Error Loading System Statistics"}),e.jsx(t,{children:j})]});if(!s)return e.jsx(C,{severity:"warning",children:e.jsx(t,{children:"No system statistics available."})});const S=[{title:"User Growth",current:s.users_last_7_days,previous:s.users_last_30_days-s.users_last_7_days,period:"7 days vs previous 23 days"},{title:"Active Users",current:s.active_users_last_7_days,previous:s.active_users_last_30_days-s.active_users_last_7_days,period:"7 days vs previous 23 days"}],A=[{type:"Standard",count:s.standard_users,color:i.palette.grey[600]},{type:"Pro",count:s.pro_users,color:i.palette.warning.main},{type:"Educational",count:s.edu_users,color:i.palette.info.main},{type:"Educational Pro",count:s.edu_pro_users,color:i.palette.secondary.main},{type:"Admin",count:s.admin_users,color:i.palette.error.main}];return e.jsxs(n,{children:[e.jsxs(n,{sx:{mb:4},children:[e.jsx(t,{variant:"h4",gutterBottom:!0,children:"System Statistics"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Detailed analytics and performance metrics"})]}),e.jsxs(h,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(h,{item:!0,xs:12,sm:6,md:4,lg:3,children:e.jsx(ee,{title:"Total Users",value:s.total_users,subtitle:"All registered accounts",icon:e.jsx(se,{}),color:i.palette.primary.main,change:{value:s.users_last_7_days,period:"this week",positive:!0}})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,lg:3,children:e.jsx(ee,{title:"Active Users",value:s.active_users_last_7_days,subtitle:"Active in last 7 days",icon:e.jsx(we,{}),color:i.palette.success.main})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,lg:3,children:e.jsx(ee,{title:"Total Datasets",value:s.total_datasets,subtitle:"User-created datasets",icon:e.jsx(Se,{}),color:i.palette.info.main})}),e.jsx(h,{item:!0,xs:12,sm:6,md:4,lg:3,children:e.jsx(ee,{title:"Data Adoption",value:`${(s.users_with_datasets/s.total_users*100).toFixed(1)}%`,subtitle:"Users with datasets",icon:e.jsx(Ae,{}),color:i.palette.warning.main})})]}),e.jsxs(h,{container:!0,spacing:3,children:[e.jsx(h,{item:!0,xs:12,md:6,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(t,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(se,{}),"Account Type Breakdown"]}),e.jsx(re,{children:e.jsxs(ie,{size:"small",children:[e.jsx(ae,{children:e.jsxs(P,{children:[e.jsx(o,{children:"Account Type"}),e.jsx(o,{align:"right",children:"Count"}),e.jsx(o,{align:"right",children:"Percentage"})]})}),e.jsx(ne,{children:A.map(r=>e.jsxs(P,{children:[e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(n,{sx:{width:12,height:12,borderRadius:"50%",backgroundColor:r.color}}),r.type]})}),e.jsx(o,{align:"right",sx:{fontWeight:"bold"},children:r.count.toLocaleString()}),e.jsxs(o,{align:"right",children:[(r.count/s.total_users*100).toFixed(1),"%"]})]},r.type))})]})})]})})}),e.jsx(h,{item:!0,xs:12,md:6,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(t,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Me,{}),"Growth Metrics"]}),e.jsx(n,{sx:{mt:3},children:S.map((r,v)=>{const I=r.previous>0?((r.current-r.previous)/r.previous*100).toFixed(1):"0",x=parseFloat(I)>=0;return e.jsxs(n,{sx:{mb:3},children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(t,{variant:"body1",fontWeight:"bold",children:r.title}),e.jsx(z,{size:"small",label:`${x?"+":""}${I}%`,color:x?"success":"error",variant:"outlined"})]}),e.jsxs(t,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Current: ",r.current," | Previous: ",r.previous]}),e.jsx(t,{variant:"caption",color:"text.secondary",children:r.period})]},v)})})]})})}),e.jsx(h,{item:!0,xs:12,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(t,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx($e,{}),"System Health Indicators"]}),e.jsxs(h,{container:!0,spacing:3,sx:{mt:1},children:[e.jsx(h,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsxs(t,{variant:"h4",color:"success.main",fontWeight:"bold",children:[(s.active_users_last_7_days/s.total_users*100).toFixed(1),"%"]}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Weekly Active Rate"})]})}),e.jsx(h,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsxs(t,{variant:"h4",color:"info.main",fontWeight:"bold",children:[(s.users_with_datasets/s.total_users*100).toFixed(1),"%"]}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Data Engagement"})]})}),e.jsx(h,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsxs(t,{variant:"h4",color:"warning.main",fontWeight:"bold",children:[((s.pro_users+s.edu_pro_users)/s.total_users*100).toFixed(1),"%"]}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Premium Adoption"})]})}),e.jsx(h,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"secondary.main",fontWeight:"bold",children:s.total_datasets>0?(s.total_datasets/s.users_with_datasets).toFixed(1):"0"}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Avg Datasets/User"})]})})]})]})})})]})]})},ls=()=>{const{user:i}=pe(),[s,c]=l.useState([]),[u,p]=l.useState(!0),[j,y]=l.useState(!1),[b,S]=l.useState(null),[A,r]=l.useState(null),[v,I]=l.useState(null),[x,f]=l.useState({title:"",message:"",type:"info",target_audience:"all",priority:0,expires_at:""}),w=async()=>{try{p(!0);const{data:a,error:m}=await W.from("notifications").select("*").order("created_at",{ascending:!1});if(m){r("Failed to fetch notifications"),console.error("Error fetching notifications:",m);return}c(a||[])}catch(a){r("Failed to fetch notifications"),console.error("Error:",a)}finally{p(!1)}};l.useEffect(()=>{w()},[]);const _=async()=>{if(!x.title.trim()||!x.message.trim()){r("Title and message are required");return}try{const a={title:x.title.trim(),message:x.message.trim(),type:x.type,target_audience:x.target_audience,priority:x.priority,expires_at:x.expires_at||null,created_by:i==null?void 0:i.id};if(b){const{error:m}=await W.from("notifications").update(a).eq("id",b.id);if(m)throw m;I("Notification updated successfully")}else{const{error:m}=await W.from("notifications").insert([a]);if(m)throw m;I("Notification created successfully")}y(!1),S(null),X(),w()}catch(a){r("Failed to save notification"),console.error("Error saving notification:",a)}},g=a=>{S(a),f({title:a.title,message:a.message,type:a.type,target_audience:a.target_audience,priority:a.priority,expires_at:a.expires_at?a.expires_at.split("T")[0]:""}),y(!0)},k=async a=>{try{const{error:m}=await W.from("notifications").update({is_active:!a.is_active}).eq("id",a.id);if(m)throw m;I(`Notification ${a.is_active?"deactivated":"activated"}`),w()}catch(m){r("Failed to update notification status"),console.error("Error:",m)}},oe=async a=>{if(confirm("Are you sure you want to delete this notification?"))try{const{error:m}=await W.from("notifications").delete().eq("id",a.id);if(m)throw m;I("Notification deleted successfully"),w()}catch(m){r("Failed to delete notification"),console.error("Error:",m)}},X=()=>{f({title:"",message:"",type:"info",target_audience:"all",priority:0,expires_at:""})},G=()=>{y(!1),S(null),X()},d=a=>{switch(a){case"success":return"success";case"warning":return"warning";case"error":return"error";default:return"info"}};return e.jsxs(n,{sx:{p:3},children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsx(t,{variant:"h4",children:"Notification Manager"}),e.jsx(T,{variant:"contained",startIcon:e.jsx(ke,{}),onClick:()=>y(!0),children:"Add Notification"})]}),A&&e.jsx(C,{severity:"error",sx:{mb:2},onClose:()=>r(null),children:A}),v&&e.jsx(C,{severity:"success",sx:{mb:2},onClose:()=>I(null),children:v}),e.jsx(re,{component:Y,children:e.jsxs(ie,{children:[e.jsx(ae,{children:e.jsxs(P,{children:[e.jsx(o,{children:"Title"}),e.jsx(o,{children:"Type"}),e.jsx(o,{children:"Audience"}),e.jsx(o,{children:"Priority"}),e.jsx(o,{children:"Status"}),e.jsx(o,{children:"Created"}),e.jsx(o,{children:"Actions"})]})}),e.jsx(ne,{children:s.map(a=>e.jsxs(P,{children:[e.jsxs(o,{children:[e.jsx(t,{variant:"body2",sx:{fontWeight:"bold"},children:a.title}),e.jsx(n,{sx:{maxWidth:300},children:e.jsx(_e,{text:a.message.length>100?a.message.substring(0,100)+"...":a.message,variant:"caption",color:"text.secondary",showYouTubePreview:!1})}),Q(a.message).length>0&&e.jsx(z,{label:`${Q(a.message).length} video link(s)`,size:"small",color:"error",variant:"outlined",sx:{mt:.5}})]}),e.jsx(o,{children:e.jsx(z,{label:a.type,color:d(a.type),size:"small"})}),e.jsx(o,{children:a.target_audience}),e.jsx(o,{children:a.priority}),e.jsx(o,{children:e.jsx(z,{label:a.is_active?"Active":"Inactive",color:a.is_active?"success":"default",size:"small"})}),e.jsx(o,{children:new Date(a.created_at).toLocaleDateString()}),e.jsxs(o,{children:[e.jsx(H,{onClick:()=>g(a),size:"small",children:e.jsx(Ce,{})}),e.jsx(H,{onClick:()=>k(a),size:"small",children:a.is_active?e.jsx(Oe,{}):e.jsx(He,{})}),e.jsx(H,{onClick:()=>oe(a),size:"small",color:"error",children:e.jsx(We,{})})]})]},a.id))})]})}),e.jsxs(ue,{open:j,onClose:G,maxWidth:"md",fullWidth:!0,children:[e.jsx(me,{children:b?"Edit Notification":"Add New Notification"}),e.jsx(je,{children:e.jsxs(h,{container:!0,spacing:2,sx:{mt:1},children:[e.jsx(h,{item:!0,xs:12,children:e.jsx(O,{fullWidth:!0,label:"Title",value:x.title,onChange:a=>f({...x,title:a.target.value})})}),e.jsx(h,{item:!0,xs:12,children:e.jsx(O,{fullWidth:!0,multiline:!0,rows:3,label:"Message",value:x.message,onChange:a=>f({...x,message:a.target.value}),helperText:"YouTube links will be automatically converted to clickable video links with previews"})}),x.message&&e.jsxs(h,{item:!0,xs:12,children:[e.jsx(t,{variant:"subtitle2",gutterBottom:!0,children:"Preview:"}),e.jsxs(Y,{variant:"outlined",sx:{p:2,backgroundColor:"grey.50",border:"1px dashed",borderColor:"grey.300"},children:[e.jsx(_e,{text:x.message,variant:"body2",color:"text.secondary",showYouTubePreview:!0}),Q(x.message).length>0&&e.jsxs(n,{sx:{mt:2},children:[e.jsx(Ye,{sx:{mb:1}}),e.jsxs(t,{variant:"caption",color:"text.secondary",children:["✓ ",Q(x.message).length," YouTube link(s) detected and will be enhanced"]})]})]})]}),e.jsx(h,{item:!0,xs:6,children:e.jsxs(de,{fullWidth:!0,children:[e.jsx(xe,{children:"Type"}),e.jsxs(he,{value:x.type,onChange:a=>f({...x,type:a.target.value}),children:[e.jsx(E,{value:"info",children:"Info"}),e.jsx(E,{value:"success",children:"Success"}),e.jsx(E,{value:"warning",children:"Warning"}),e.jsx(E,{value:"error",children:"Error"})]})]})}),e.jsx(h,{item:!0,xs:6,children:e.jsxs(de,{fullWidth:!0,children:[e.jsx(xe,{children:"Target Audience"}),e.jsxs(he,{value:x.target_audience,onChange:a=>f({...x,target_audience:a.target.value}),children:[e.jsx(E,{value:"all",children:"All Users"}),e.jsx(E,{value:"pro",children:"Pro Users"}),e.jsx(E,{value:"edu",children:"Educational Users"}),e.jsx(E,{value:"standard",children:"Standard Users"}),e.jsx(E,{value:"guest",children:"Guest Users"})]})]})}),e.jsx(h,{item:!0,xs:6,children:e.jsx(O,{fullWidth:!0,type:"number",label:"Priority",value:x.priority,onChange:a=>f({...x,priority:parseInt(a.target.value)||0}),helperText:"Higher numbers = higher priority"})}),e.jsx(h,{item:!0,xs:6,children:e.jsx(O,{fullWidth:!0,type:"date",label:"Expires On (Optional)",value:x.expires_at,onChange:a=>f({...x,expires_at:a.target.value}),InputLabelProps:{shrink:!0}})})]})}),e.jsxs(ge,{children:[e.jsx(T,{onClick:G,children:"Cancel"}),e.jsx(T,{onClick:_,variant:"contained",children:b?"Update":"Create"})]})]})]})},cs=()=>{const{user:i,refreshAdminStatus:s}=pe(),[c,u]=l.useState([]),[p,j]=l.useState(!0),[y,b]=l.useState(null),[S,A]=l.useState(!1),[r,v]=l.useState(""),[I,x]=l.useState(!1);l.useEffect(()=>{f()},[]);const f=async()=>{try{j(!0),b(null);const{data:g,error:k}=await W.rpc("get_admin_users");if(k)throw k;u(g||[])}catch(g){console.error("Error fetching admin users:",g),b(g.message||"Failed to load admin users")}finally{j(!1)}},w=async g=>{if(g===(i==null?void 0:i.id)){b("You cannot remove your own admin privileges");return}if(confirm("Are you sure you want to remove admin privileges from this user?"))try{const{error:k}=await W.rpc("update_user_admin_status",{target_user_id:g,new_admin_status:!1});if(k)throw k;await f()}catch(k){console.error("Error removing admin:",k),b(k.message||"Failed to remove admin privileges")}},_=g=>new Date(g).toLocaleDateString();return e.jsxs(n,{children:[e.jsxs(n,{sx:{mb:4},children:[e.jsx(t,{variant:"h4",gutterBottom:!0,children:"Admin Settings"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Manage admin users and system settings"})]}),y&&e.jsx(C,{severity:"error",sx:{mb:3},onClose:()=>b(null),children:y}),e.jsxs(h,{container:!0,spacing:3,children:[e.jsx(h,{item:!0,xs:12,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsxs(t,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(K,{}),"Admin Users"]}),e.jsxs(n,{sx:{display:"flex",gap:1},children:[e.jsx(T,{variant:"outlined",startIcon:e.jsx(te,{}),onClick:f,disabled:p,children:"Refresh"}),e.jsx(T,{variant:"contained",startIcon:e.jsx(ke,{}),onClick:()=>A(!0),children:"Add Admin"})]})]}),e.jsx(C,{severity:"warning",sx:{mb:3},children:e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Important:"})," Admin users have full system access. Only grant admin privileges to trusted users. You cannot remove your own admin privileges for security reasons."]})}),e.jsx(re,{component:Y,variant:"outlined",children:e.jsxs(ie,{children:[e.jsx(ae,{children:e.jsxs(P,{children:[e.jsx(o,{children:"User"}),e.jsx(o,{children:"Institution"}),e.jsx(o,{children:"Country"}),e.jsx(o,{children:"Last Updated"}),e.jsx(o,{align:"center",children:"Actions"})]})}),e.jsx(ne,{children:c.map(g=>e.jsxs(P,{children:[e.jsx(o,{children:e.jsxs(n,{children:[e.jsx(t,{variant:"body2",fontWeight:"bold",children:g.full_name||"No name"}),g.username&&e.jsxs(t,{variant:"caption",color:"text.secondary",children:["@",g.username]}),g.id===(i==null?void 0:i.id)&&e.jsx(z,{label:"You",size:"small",color:"primary",variant:"outlined",sx:{ml:1}})]})}),e.jsx(o,{children:g.institution||"Not specified"}),e.jsx(o,{children:g.country||"Not specified"}),e.jsx(o,{children:_(g.updated_at)}),e.jsx(o,{align:"center",children:g.id!==(i==null?void 0:i.id)&&e.jsx(H,{size:"small",color:"error",onClick:()=>w(g.id),title:"Remove Admin Privileges",children:e.jsx(We,{})})})]},g.id))})]})})]})})}),e.jsx(h,{item:!0,xs:12,md:6,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(t,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Ge,{}),"System Information"]}),e.jsxs(be,{children:[e.jsxs(F,{children:[e.jsx(le,{children:e.jsx(Te,{})}),e.jsx(B,{primary:"Admin Dashboard Version",secondary:"1.0.0"})]}),e.jsxs(F,{children:[e.jsx(le,{children:e.jsx(K,{})}),e.jsx(B,{primary:"Security Level",secondary:"High - RLS Enabled"})]}),e.jsxs(F,{children:[e.jsx(le,{children:e.jsx(Ee,{})}),e.jsx(B,{primary:"Total Admin Users",secondary:`${c.length} active`})]})]})]})})}),e.jsx(h,{item:!0,xs:12,md:6,children:e.jsx(D,{elevation:0,variant:"outlined",sx:{borderRadius:2},children:e.jsxs(R,{sx:{p:3},children:[e.jsxs(t,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(qe,{}),"Security Guidelines"]}),e.jsxs(be,{children:[e.jsx(F,{children:e.jsx(B,{primary:"Regular Review",secondary:"Review admin user list monthly"})}),e.jsx(F,{children:e.jsx(B,{primary:"Principle of Least Privilege",secondary:"Only grant admin access when necessary"})}),e.jsx(F,{children:e.jsx(B,{primary:"Account Security",secondary:"Ensure admin accounts use strong passwords"})}),e.jsx(F,{children:e.jsx(B,{primary:"Activity Monitoring",secondary:"Monitor admin actions and system changes"})})]})]})})})]}),e.jsxs(ue,{open:S,onClose:()=>A(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(me,{children:"Add New Admin User"}),e.jsx(je,{children:e.jsxs(n,{sx:{pt:2},children:[e.jsx(C,{severity:"info",sx:{mb:2},children:e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Note:"})," Adding admin users by email is not yet implemented. Use the User Management tab to promote existing users to admin, or manually update the database via Supabase dashboard."]})}),e.jsx(O,{fullWidth:!0,label:"User Email",type:"email",value:r,onChange:g=>v(g.target.value),placeholder:"Enter the email of the user to make admin",helperText:"Feature coming soon - use User Management tab instead",disabled:!0}),e.jsx(C,{severity:"warning",sx:{mt:2},children:e.jsx(t,{variant:"body2",children:"Admin privileges grant full system access. Only assign to trusted users."})})]})}),e.jsx(ge,{children:e.jsx(T,{onClick:()=>A(!1),children:"Close"})})]})]})};class q extends l.Component{constructor(c){super(c);ye(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(c){return{hasError:!0,error:c,errorInfo:null}}componentDidCatch(c,u){console.error("Admin Dashboard Error:",c,u),this.setState({error:c,errorInfo:u}),this.props.onError&&this.props.onError(c,u)}render(){var c;return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx(n,{sx:{p:3,width:"100%"},children:e.jsx(Y,{elevation:0,variant:"outlined",sx:{p:4,borderRadius:2},children:e.jsxs(fe,{spacing:3,alignItems:"center",textAlign:"center",children:[e.jsx(Ke,{sx:{fontSize:48,color:"error.main"}}),e.jsx(t,{variant:"h5",color:"error.main",gutterBottom:!0,children:"Something went wrong"}),e.jsx(t,{variant:"body1",color:"text.secondary",sx:{maxWidth:600},children:"An error occurred while loading this admin dashboard section. This might be due to a database connection issue or a temporary problem."}),e.jsx(C,{severity:"error",sx:{width:"100%",maxWidth:600},children:e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Error:"})," ",((c=this.state.error)==null?void 0:c.message)||"Unknown error occurred"]})}),e.jsxs(fe,{direction:"row",spacing:2,children:[e.jsx(T,{variant:"contained",startIcon:e.jsx(te,{}),onClick:this.handleReset,color:"primary",children:"Try Again"}),e.jsx(T,{variant:"outlined",onClick:()=>window.location.reload(),color:"secondary",children:"Reload Page"})]}),!1]})})}):this.props.children}}function ds(i){const{children:s,value:c,index:u,...p}=i;return e.jsx("div",{role:"tabpanel",hidden:c!==u,id:`admin-tabpanel-${u}`,"aria-labelledby":`admin-tab-${u}`,...p,style:{height:c===u?"auto":0,overflow:c===u?"visible":"hidden"},children:c===u&&e.jsx(n,{sx:{width:"100%",height:"100%"},children:s})})}function xs(i){return{id:`admin-tab-${i}`,"aria-controls":`admin-tabpanel-${i}`}}const fs=()=>{const i=V(),s=rs(),{isAdmin:c,canAccessAdminDashboard:u,loading:p,user:j}=pe(),[y,b]=l.useState(0),[S,A]=l.useState(!0),[r,v]=l.useState(!1);l.useEffect(()=>{if(!p){if(!j){s("/auth");return}if(!u){s("/dashboard");return}A(!1)}},[p,j,u,s]),l.useEffect(()=>{const w=_=>{(_.key==="F11"||(_.ctrlKey||_.metaKey)&&_.shiftKey&&_.key==="F")&&(_.preventDefault(),x())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[r]);const I=(w,_)=>{b(_)},x=()=>{v(!r)};if(l.useEffect(()=>(r?document.body.style.overflow="hidden":document.body.style.overflow="auto",()=>{document.body.style.overflow="auto"}),[r]),p||S)return e.jsx(ce,{maxWidth:"lg",sx:{py:4,display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh"},children:e.jsxs(n,{sx:{textAlign:"center"},children:[e.jsx(Z,{size:48}),e.jsx(t,{variant:"h6",sx:{mt:2},children:"Loading Admin Dashboard..."})]})});if(!c||!u)return e.jsx(ce,{maxWidth:"lg",sx:{py:4},children:e.jsxs(C,{severity:"error",sx:{mb:3},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:"Access Denied"}),e.jsx(t,{children:"You do not have permission to access the admin dashboard. Admin privileges are required."})]})});const f=[{label:"Overview",icon:e.jsx(es,{}),component:e.jsx(q,{children:e.jsx(is,{})})},{label:"User Management",icon:e.jsx(se,{}),component:e.jsx(q,{children:e.jsx(ns,{})})},{label:"System Statistics",icon:e.jsx(ss,{}),component:e.jsx(q,{children:e.jsx(os,{})})},{label:"Notifications",icon:e.jsx(ts,{}),component:e.jsx(q,{children:e.jsx(ls,{})})},{label:"Admin Settings",icon:e.jsx(Te,{}),component:e.jsx(q,{children:e.jsx(cs,{})})}];return e.jsx(n,{sx:{width:"100%",minHeight:"100vh",bgcolor:"background.default",position:r?"fixed":"relative",top:r?0:"auto",left:r?0:"auto",right:r?0:"auto",bottom:r?0:"auto",zIndex:r?1300:"auto",transition:"all 0.3s ease-in-out",overflow:r?"auto":"visible"},children:e.jsxs(ce,{maxWidth:r?!1:"xl",sx:{py:3,px:{xs:2,sm:3},maxWidth:r?"100%":void 0,width:r?"100%":void 0,transition:"all 0.3s ease-in-out"},children:[e.jsxs(n,{sx:{mb:r?2:3,transition:"margin 0.3s ease-in-out"},children:[e.jsxs(t,{variant:"h3",component:"h1",gutterBottom:!0,sx:{fontWeight:"bold",background:`linear-gradient(45deg, ${i.palette.primary.main}, ${i.palette.secondary.main})`,backgroundClip:"text",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",display:"flex",alignItems:"center",gap:2,fontSize:{xs:r?"1.5rem":"1.8rem",sm:r?"2rem":"2.5rem"},transition:"font-size 0.3s ease-in-out"},children:[e.jsx(K,{sx:{fontSize:{xs:r?"1.5rem":"2rem",sm:r?"2rem":"2.5rem"},color:i.palette.primary.main,transition:"font-size 0.3s ease-in-out"}}),"Admin Dashboard",r&&e.jsx(n,{sx:{ml:2,bgcolor:L(i.palette.primary.main,.1),color:i.palette.primary.main,fontSize:"0.75rem",px:1,py:.5,borderRadius:1,display:"inline-flex",alignItems:"center",fontWeight:"bold",animation:"fadeIn 0.3s ease-in-out"},children:"MAXIMIZED"})]}),!r&&e.jsx(t,{variant:"h6",color:"text.secondary",sx:{fontSize:{xs:"1rem",sm:"1.25rem"},transition:"opacity 0.3s ease-in-out"},children:"System administration and management console"})]}),!r&&e.jsx(C,{severity:"info",sx:{mb:3,transition:"opacity 0.3s ease-in-out"},children:e.jsxs(t,{variant:"body2",children:[e.jsx("strong",{children:"Admin Access:"})," You are logged in as an administrator. Please use these tools responsibly and follow your organization's policies."]})}),e.jsxs(Y,{elevation:0,variant:"outlined",sx:{borderRadius:2,overflow:"hidden",backgroundColor:L(i.palette.background.paper,r?.95:.8),backdropFilter:"blur(10px)",minHeight:r?"calc(100vh - 120px)":"70vh",transition:"all 0.3s ease-in-out",boxShadow:r?i.shadows[8]:i.shadows[1],"@keyframes fadeIn":{"0%":{opacity:0,transform:"translateY(-10px)"},"100%":{opacity:1,transform:"translateY(0)"}}},children:[e.jsxs(n,{sx:{borderBottom:1,borderColor:"divider",position:"relative",display:"flex",alignItems:"center"},children:[e.jsx(n,{sx:{flexGrow:1},children:e.jsx(Ve,{value:y,onChange:I,variant:"scrollable",scrollButtons:"auto",allowScrollButtonsMobile:!0,sx:{"& .MuiTab-root":{minHeight:{xs:60,sm:72},textTransform:"none",fontSize:{xs:"0.875rem",sm:"1rem"},fontWeight:500,px:{xs:1,sm:2},"&.Mui-selected":{color:i.palette.primary.main,fontWeight:600}},"& .MuiTabs-scrollButtons":{"&.Mui-disabled":{opacity:.3}}},children:f.map((w,_)=>e.jsx(Ze,{icon:w.icon,label:w.label,iconPosition:"start",...xs(_),sx:{"& .MuiTab-iconWrapper":{marginRight:1,marginBottom:0}}},_))})}),e.jsx(n,{sx:{position:"absolute",right:{xs:8,sm:16},top:"50%",transform:"translateY(-50%)",zIndex:1},children:e.jsx(Ie,{title:e.jsxs(n,{children:[e.jsx(t,{variant:"body2",children:r?"Exit fullscreen view":"Maximize for better visibility"}),e.jsx(t,{variant:"caption",sx:{opacity:.8},children:"Shortcut: F11 or Ctrl+Shift+F"})]}),placement:"left",TransitionComponent:Xe,children:e.jsx(H,{onClick:x,size:"medium",sx:{color:i.palette.text.secondary,backgroundColor:L(i.palette.background.paper,.8),backdropFilter:"blur(8px)",border:`1px solid ${L(i.palette.divider,.2)}`,transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:L(i.palette.primary.main,.1),color:i.palette.primary.main,transform:"scale(1.05)",boxShadow:i.shadows[4]},"&:active":{transform:"scale(0.95)"}},children:r?e.jsx(Je,{sx:{fontSize:"1.25rem"}}):e.jsx(Qe,{sx:{fontSize:"1.25rem"}})})})})]}),e.jsx(n,{sx:{overflow:"auto",maxHeight:r?"calc(100vh - 200px)":"calc(100vh - 300px)",transition:"max-height 0.3s ease-in-out"},children:f.map((w,_)=>e.jsx(ds,{value:y,index:_,children:e.jsx(n,{sx:{px:{xs:1,sm:r?3:2},py:2,transition:"padding 0.3s ease-in-out"},children:w.component})},_))})]}),e.jsx(n,{sx:{mt:3,textAlign:"center"},children:e.jsx(t,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"}},children:"DataStatPro Admin Dashboard - Use responsibly and in accordance with your organization's policies"})})]})})};export{fs as default};
