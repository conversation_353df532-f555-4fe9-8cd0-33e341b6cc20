import{u as Se,j as e,B as u,e as i,R as I,G as h,ai as U,b9 as H,ba as G,bb as y,f as Ie,bg as Me,ae as De,g as p,ao as Le,ap as We,aq as $e,ar as J,as as d,at as qe,k as K,l as z,i as Y,bf as Z,a6 as Pe,a7 as R,aX as Ee,W as Ue,cb as He,aB as Ge}from"./mui-libs-CfwFIaTD.js";import{r as f}from"./react-libs-Cr2nE3UY.js";import Ke from"./TTests-CqoYonWk.js";import ze from"./ANOVA-VqPPZ-TR.js";import Qe from"./NonParametricTests-CnQbYnts.js";import{a as Xe,D as ee}from"./index-Bpan7Tbe.js";import{c as _e,a as Je,b as Ye,m as ye,n as Ae}from"./descriptive-Djo0s6H4.js";import{j as te}from"./other-utils-CR9xr_gI.js";import{p as Ne}from"./anova-DbTY6dHK.js";import"./math-setup-BTRs7Kau.js";import{c as Be}from"./normality-CwHD6Rjl.js";import"./StatsCard-op8tGQ0a.js";import"./t-tests-DXw1R1jD.js";import"./non-parametric-Cf6Ds91x.js";import"./charts-recharts-d3-BEF1Y_jn.js";import"./supabase-lib-B3goak-P.js";import"./ml-tensorflow-D19WVUQh.js";import"./charts-plotly-BhN4fPIu.js";import"./math-lib-BOZ-XUok.js";import"./OneWayANOVA-ai9Q6gnX.js";import"./TwoWayANOVA-CiVErBBN.js";import"./twoWayANOVA-CIsn6bwj.js";import"./RepeatedMeasuresANOVA-DwrjssMW.js";import"./AnalysisSteps-CmfAFU5C.js";import"./PageTitle-DA3BXQ4x.js";import"./DatasetSelector-G08QHuaN.js";import"./VariableSelector-CPdlCsJ2.js";import"./repeatedMeasuresANOVA-B8bv3NGM.js";const Ze=()=>{const{datasets:A,currentDataset:l}=Xe(),N=Se(),[V,se]=f.useState((l==null?void 0:l.id)||""),[T,M]=f.useState([]),[D,Q]=f.useState(""),[x,ae]=f.useState(["normality","homogeneity","outliers"]),[L,Ve]=f.useState("both"),[re,oe]=f.useState(!1),[de,F]=f.useState(null),[ne,W]=f.useState(null),ie=(l==null?void 0:l.columns.filter(s=>s.type===ee.NUMERIC))||[],ce=(l==null?void 0:l.columns.filter(s=>s.type===ee.CATEGORICAL||s.type===ee.ORDINAL||s.type===ee.BOOLEAN))||[],Te=s=>{const t=s.target.value;se(t),M([]),Q(""),W(null),F(null)},we=s=>{const t=s.target.value;M(typeof t=="string"?[t]:t),W(null),F(null)},Ce=s=>{Q(s.target.value),W(null),F(null)},ke=s=>{const t=s.target.value;ae(typeof t=="string"?[t]:t)},me=()=>l&&T.length>0&&D&&x.length>0,he=s=>{if(!s||!s.df||s.df.length<2)return"Levene's test interpretation not available.";let t=`Levene's test for homogeneity of variances (alpha = ${s.alpha.toFixed(2)}): `;return t+=`F(${s.df[0]}, ${s.df[1]}) = ${s.statistic.toFixed(3)}, p = ${s.pValue.toFixed(3)}. `,s.rejected?t+="Assumption of equal variances is NOT met.":t+="Assumption of equal variances is met.",t},ue=(s,t,m)=>{let r="";s.isNormal?r+="Data appears normally distributed (p > 0.05). ":r+="Data does NOT appear normally distributed (p <= 0.05). ",Math.abs(t)<.5?r+="Approx. symmetric. ":Math.abs(t)<1?r+=`Moderately ${t>0?"positive":"negative"} skew. `:r+=`Heavily ${t>0?"positive":"negative"} skew. `;const b=m-3;return Math.abs(b)<.5?r+="Normal peak/tails.":b>0?r+="Leptokurtic (sharp peak, heavy tails).":r+="Platykurtic (flat peak, thin tails).",r},xe=(s,t)=>{if(s===0)return"No outliers detected.";const m=s/t*100;return`${s} outlier(s) detected (${m.toFixed(1)}%).`},Fe=()=>{var s,t,m,r,b,$,q,P,pe,je,fe,be;if(!me()){F("Please select dataset, at least one dependent variable, a factor variable, and assumptions.");return}oe(!0),F(null),W(null);try{const B=l.columns.find(c=>c.id===D);if(!B)throw new Error("Factor variable definition not found.");const ge=[...new Set(l.data.map(c=>c[B.name]))].filter(c=>c!=null&&String(c).trim()!=="");if(ge.length<2)throw new Error("Factor variable must have at least two distinct levels with data.");const j={dependentVariablesChecks:{},leveneTests:{}};for(const c of T){const w=l.columns.find(n=>n.id===c);if(!w)continue;j.dependentVariablesChecks[c]={name:w.name,groups:{},overallAssessment:{}};const E=[],ve=[];for(const n of ge){const a=l.data.filter(o=>o[B.name]===n).map(o=>o[w.name]).filter(o=>typeof o=="number"&&!isNaN(o));if(a.length===0){j.dependentVariablesChecks[c].groups[String(n)]={name:String(n),error:"No numeric data for this group."};continue}a.length>1&&(E.push(a),ve.push(String(n)));const g={name:String(n),n:a.length,mean:a.length>0?Ye(a):"N/A",median:a.length>0?Je(a):"N/A",sd:a.length>1?_e(a):"N/A"};if(x.includes("normality")&&a.length>=3){const o=Be(a,.05,["auto"]),C=ye(a),k=Ae(a);g.normality={isNormal:o.overallAssessment.isNormal,pValue:((s=o.tests.shapiroWilk)==null?void 0:s.pValue)||((t=o.tests.kolmogorovSmirnov)==null?void 0:t.pValue)||((m=o.tests.jarqueBera)==null?void 0:m.pValue)||NaN,statistic:((r=o.tests.shapiroWilk)==null?void 0:r.statistic)||((b=o.tests.kolmogorovSmirnov)==null?void 0:b.statistic)||(($=o.tests.jarqueBera)==null?void 0:$.statistic)||NaN,testUsed:o.recommendedTest,confidence:o.overallAssessment.confidence,skewness:C,kurtosis:k,interpretation:ue(o,C,k)}}else x.includes("normality")&&(g.normality={error:"Not enough data for normality test (min 3 required)."});if(x.includes("outliers")&&a.length>0){const o=[...a].sort((_,Oe)=>_-Oe),C=te.percentile(o,.25),k=te.percentile(o,.75),S=k-C,O=C-1.5*S,le=k+1.5*S,X=a.filter(_=>_<O||_>le);g.outliers={count:X.length,percentage:X.length/a.length*100,values:X,interpretation:xe(X.length,a.length)}}j.dependentVariablesChecks[c].groups[String(n)]=g}if(x.includes("homogeneity"))if(E.length>=2&&E.every(n=>n.length>0))try{const n={dependentVariableName:w.name,factorVariableName:B.name,groupsTested:ve};if(L==="both"||L==="mean"){const a=Ne(...E,{variant:"mean"});n.meanBased={statistic:a.statistic,pValue:a.pValue,df:a.df,rejected:a.rejected,alpha:a.alpha,method:a.method,interpretation:he(a)}}if(L==="both"||L==="median"){const a=Ne(...E,{variant:"median"});n.medianBased={statistic:a.statistic,pValue:a.pValue,df:a.df,rejected:a.rejected,alpha:a.alpha,method:a.method,interpretation:he(a)}}j.leveneTests[c]=n}catch(n){j.leveneTests[c]={error:`Levene's test error for ${w.name}: ${n.message}`}}else j.leveneTests[c]={error:`Not enough groups with sufficient data for ${w.name} for Levene's test.`};const v=l.data.map(n=>n[w.name]).filter(n=>typeof n=="number"&&!isNaN(n));if(v.length>0){if(x.includes("normality")&&v.length>=3){const n=Be(v,.05,["auto"]),a=ye(v),g=Ae(v);j.dependentVariablesChecks[c].overallAssessment.normality={isNormal:n.overallAssessment.isNormal,pValue:((q=n.tests.shapiroWilk)==null?void 0:q.pValue)||((P=n.tests.kolmogorovSmirnov)==null?void 0:P.pValue)||((pe=n.tests.jarqueBera)==null?void 0:pe.pValue)||NaN,statistic:((je=n.tests.shapiroWilk)==null?void 0:je.statistic)||((fe=n.tests.kolmogorovSmirnov)==null?void 0:fe.statistic)||((be=n.tests.jarqueBera)==null?void 0:be.statistic)||NaN,testUsed:n.recommendedTest,confidence:n.overallAssessment.confidence,skewness:a,kurtosis:g,interpretation:ue(n,a,g)}}else x.includes("normality")&&(j.dependentVariablesChecks[c].overallAssessment.normality={error:"Not enough data for overall normality test (min 3 required)."});if(x.includes("outliers")){const n=[...v].sort((O,le)=>O-le),a=te.percentile(n,.25),g=te.percentile(n,.75),o=g-a,C=a-1.5*o,k=g+1.5*o,S=v.filter(O=>O<C||O>k);j.dependentVariablesChecks[c].overallAssessment.outliers={count:S.length,percentage:S.length/v.length*100,values:S,interpretation:xe(S.length,v.length),hasOutliers:S.length>0}}}else x.includes("normality")&&(j.dependentVariablesChecks[c].overallAssessment.normality={error:"No numeric data for overall normality test."}),x.includes("outliers")&&(j.dependentVariablesChecks[c].overallAssessment.outliers={error:"No numeric data for overall outlier detection."})}W(j)}catch(B){F(`Error checking assumptions: ${B instanceof Error?B.message:String(B)}`)}finally{oe(!1)}};return e.jsxs(u,{p:3,children:[e.jsx(i,{variant:"h5",gutterBottom:!0,children:"Statistical Assumption Checker"}),e.jsxs(I,{elevation:2,sx:{p:2,mb:3},children:[e.jsxs(h,{container:!0,spacing:2,children:[e.jsx(h,{item:!0,xs:12,md:4,children:e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"dataset-select-label",children:"Dataset"}),e.jsx(G,{labelId:"dataset-select-label",value:V,label:"Dataset",onChange:Te,disabled:A.length===0,children:A.map(s=>e.jsx(y,{value:s.id,children:s.name},s.id))})]})}),e.jsx(h,{item:!0,xs:12,md:4,children:e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"dependent-variables-label",children:"Dependent Variables (Numeric)"}),e.jsx(G,{labelId:"dependent-variables-label",multiple:!0,value:T,label:"Dependent Variables (Numeric)",onChange:we,disabled:ie.length===0,renderValue:s=>s.map(t=>{var m;return((m=ie.find(r=>r.id===t))==null?void 0:m.name)||t}).join(", "),children:ie.map(s=>e.jsx(y,{value:s.id,children:s.name},s.id))})]})}),e.jsx(h,{item:!0,xs:12,md:4,children:e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"factor-variable-label",children:"Factor Variable (Categorical)"}),e.jsx(G,{labelId:"factor-variable-label",value:D,label:"Factor Variable (Categorical)",onChange:Ce,disabled:ce.length===0,children:ce.map(s=>e.jsx(y,{value:s.id,children:s.name},s.id))})]})})]}),e.jsx(u,{mt:2,children:e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"assumptions-label",children:"Assumptions to Check"}),e.jsxs(G,{labelId:"assumptions-label",multiple:!0,value:x,label:"Assumptions to Check",onChange:ke,renderValue:s=>s.join(", "),children:[e.jsx(y,{value:"normality",children:"Normality (Shapiro-Wilk per group)"}),e.jsx(y,{value:"homogeneity",children:"Homogeneity of Variances (Levene's Test per dependent variable)"}),e.jsx(y,{value:"outliers",children:"Outlier Detection (IQR method per group)"})]})]})}),x.includes("homogeneity")&&e.jsxs(u,{mt:2,children:[e.jsxs(U,{fullWidth:!0,margin:"normal",children:[e.jsx(H,{id:"levene-variant-label",children:"Levene's Test Variant"}),e.jsxs(G,{labelId:"levene-variant-label",value:L,label:"Levene's Test Variant",onChange:s=>Ve(s.target.value),children:[e.jsx(y,{value:"both",children:"Both (Mean-based and Median-based)"}),e.jsx(y,{value:"mean",children:"Mean-based (Classic Levene's Test)"}),e.jsx(y,{value:"median",children:"Median-based (Brown-Forsythe Test)"})]})]}),e.jsxs(i,{variant:"body2",color:"text.secondary",sx:{mt:1},children:[e.jsx("strong",{children:"Recommendation:"}),' Median-based (Brown-Forsythe) is more robust to non-normal distributions. Mean-based matches SPSS "Based on Mean" results, while median-based matches SPSS "Based on Median" results.']})]}),e.jsx(u,{mt:2,children:e.jsx(Ie,{variant:"contained",startIcon:e.jsx(Me,{}),onClick:Fe,disabled:re||!me(),children:"Check Assumptions"})})]}),re&&e.jsx(De,{sx:{display:"block",margin:"20px auto"}}),de&&e.jsx(p,{severity:"error",sx:{mb:3},children:de}),ne&&!re&&e.jsxs(u,{children:[Object.entries(ne.leveneTests).map(([s,t])=>{var m,r,b,$,q,P;return e.jsxs(I,{elevation:2,sx:{p:2,mb:3},children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Homogeneity of Variances (Levene's Test)"}),e.jsxs(i,{variant:"subtitle1",gutterBottom:!0,children:["For Dependent Variable: ",t.dependentVariableName," (across groups of ",t.factorVariableName,")"]}),t.error?e.jsx(p,{severity:"warning",children:t.error}):e.jsxs(e.Fragment,{children:[e.jsx(Le,{component:I,variant:"outlined",children:e.jsxs(We,{size:"small",children:[e.jsx($e,{children:e.jsxs(J,{children:[e.jsx(d,{children:"Test"}),e.jsx(d,{children:"F"}),e.jsx(d,{children:"df1"}),e.jsx(d,{children:"df2"}),e.jsx(d,{children:"p-value"})]})}),e.jsxs(qe,{children:[t.meanBased&&e.jsxs(J,{children:[e.jsx(d,{children:"Based on Mean"}),e.jsx(d,{children:(m=t.meanBased.statistic)==null?void 0:m.toFixed(3)}),e.jsx(d,{children:t.meanBased.df?t.meanBased.df[0]:"N/A"}),e.jsx(d,{children:t.meanBased.df?t.meanBased.df[1]:"N/A"}),e.jsx(d,{children:(r=t.meanBased.pValue)==null?void 0:r.toFixed(3)})]}),t.medianBased&&e.jsxs(J,{children:[e.jsx(d,{children:"Based on Median"}),e.jsx(d,{children:(b=t.medianBased.statistic)==null?void 0:b.toFixed(3)}),e.jsx(d,{children:t.medianBased.df?t.medianBased.df[0]:"N/A"}),e.jsx(d,{children:t.medianBased.df?t.medianBased.df[1]:"N/A"}),e.jsx(d,{children:($=t.medianBased.pValue)==null?void 0:$.toFixed(3)})]}),!t.meanBased&&!t.medianBased&&e.jsxs(J,{children:[e.jsx(d,{children:"Levene's"}),e.jsx(d,{children:(q=t.statistic)==null?void 0:q.toFixed(3)}),e.jsx(d,{children:t.df?t.df[0]:"N/A"}),e.jsx(d,{children:t.df?t.df[1]:"N/A"}),e.jsx(d,{children:(P=t.pValue)==null?void 0:P.toFixed(3)})]})]})]})}),t.meanBased&&e.jsxs(p,{severity:t.meanBased.rejected?"warning":"success",sx:{mt:1},children:[e.jsx("strong",{children:"Mean-based:"})," ",t.meanBased.interpretation]}),t.medianBased&&e.jsxs(p,{severity:t.medianBased.rejected?"warning":"success",sx:{mt:1},children:[e.jsx("strong",{children:"Median-based (Brown-Forsythe):"})," ",t.medianBased.interpretation]}),!t.meanBased&&!t.medianBased&&e.jsx(p,{severity:t.rejected?"warning":"success",sx:{mt:1},children:t.interpretation}),e.jsxs(i,{variant:"body2",color:"text.secondary",sx:{mt:2},children:[e.jsx("strong",{children:"Note:"})," The median-based test (Brown-Forsythe) is more robust to non-normal distributions. Both variants test the null hypothesis that all groups have equal variances."]})]})]},`levene-${s}`)}),Object.entries(ne.dependentVariablesChecks).map(([s,t])=>e.jsx(K,{sx:{mb:3},children:e.jsxs(z,{children:[e.jsxs(i,{variant:"h6",gutterBottom:!0,children:["Assumption Checks for: ",t.name]}),Object.entries(t.groups).map(([m,r])=>e.jsxs(I,{elevation:1,sx:{p:2,mb:2},children:[e.jsxs(i,{variant:"subtitle1",gutterBottom:!0,children:["Group: ",r.name||m]}),r.error&&e.jsx(p,{severity:"info",children:r.error}),!r.error&&e.jsxs(h,{container:!0,spacing:2,children:[e.jsx(h,{item:!0,xs:12,sm:4,children:e.jsxs(i,{variant:"body2",children:["N: ",r.n]})}),e.jsx(h,{item:!0,xs:12,sm:4,children:e.jsxs(i,{variant:"body2",children:["Mean: ",typeof r.mean=="number"?r.mean.toFixed(3):r.mean]})}),e.jsx(h,{item:!0,xs:12,sm:4,children:e.jsxs(i,{variant:"body2",children:["SD: ",typeof r.sd=="number"?r.sd.toFixed(3):r.sd]})}),r.normality&&e.jsxs(h,{item:!0,xs:12,children:[e.jsx(i,{variant:"subtitle2",children:"Normality:"}),r.normality.error?e.jsx(p,{severity:"info",children:r.normality.error}):e.jsxs(p,{severity:r.normality.isNormal?"success":"warning",children:[r.normality.interpretation," (p=",r.normality.pValue.toFixed(3),")"]})]}),r.outliers&&e.jsxs(h,{item:!0,xs:12,children:[e.jsx(i,{variant:"subtitle2",children:"Outliers:"}),e.jsxs(p,{severity:r.outliers.hasOutliers?"warning":"success",children:[r.outliers.interpretation,r.outliers.hasOutliers&&` Values: ${r.outliers.values.map(b=>b.toFixed(2)).join(", ")}`]})]})]})]},`${s}-${m}`)),t.overallAssessment&&Object.keys(t.overallAssessment).length>0&&e.jsxs(I,{elevation:1,sx:{p:2,mt:2,backgroundColor:N.palette.grey[100]},children:[e.jsxs(i,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:"bold"},children:["Overall Assessment for ",t.name," (Ignoring Factor Variable)"]}),t.overallAssessment.normality&&e.jsxs(u,{mb:1,children:[e.jsx(i,{variant:"subtitle2",children:"Overall Normality:"}),t.overallAssessment.normality.error?e.jsx(p,{severity:"info",children:t.overallAssessment.normality.error}):e.jsxs(p,{severity:t.overallAssessment.normality.isNormal?"success":"warning",children:[t.overallAssessment.normality.interpretation," (p=",t.overallAssessment.normality.pValue.toFixed(3),", KS Stat: ",t.overallAssessment.normality.statistic.toFixed(3),")"]})]}),t.overallAssessment.outliers&&e.jsxs(u,{children:[e.jsx(i,{variant:"subtitle2",children:"Overall Outliers:"}),t.overallAssessment.outliers.error?e.jsx(p,{severity:"info",children:t.overallAssessment.outliers.error}):e.jsxs(p,{severity:t.overallAssessment.outliers.hasOutliers?"warning":"success",children:[t.overallAssessment.outliers.interpretation,t.overallAssessment.outliers.hasOutliers&&t.overallAssessment.outliers.values.length>0&&` Values: ${t.overallAssessment.outliers.values.map(m=>m.toFixed(2)).join(", ")}`]})]})]})]})},`depVar-${s}`))]})]})},Tt=({initialTab:A=""})=>{const l=Se(),[N,V]=f.useState(0),[se,T]=f.useState(!0),M={ttest:0,anova:1,nonparametric:2,assumptions:3};f.useEffect(()=>{A&&M[A]!==void 0&&(V(M[A]),A!==""&&T(!1))},[A]);const D=(x,ae)=>{V(ae),T(!0)},Q=()=>{T(!1)};return e.jsxs(u,{sx:{width:"100%",height:"100%",display:"flex",flexDirection:"column"},children:[N===0&&se&&e.jsx(u,{sx:{mb:3},children:e.jsxs(h,{container:!0,spacing:3,children:[e.jsx(h,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(K,{elevation:1,sx:{backgroundColor:Y(l.palette.primary.main,.05),borderLeft:`4px solid ${l.palette.primary.main}`,height:"100%",transition:"all 0.2s","&:hover":{boxShadow:l.shadows[3],cursor:"pointer"}},onClick:()=>{Q()},children:e.jsx(Z,{sx:{height:"100%"},children:e.jsxs(z,{children:[e.jsx(i,{variant:"subtitle1",color:"primary",gutterBottom:!0,sx:{fontWeight:"medium"},children:"t-Tests"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Compare means between groups or against a known value. Use for comparing:"}),e.jsxs(i,{variant:"body2",component:"ul",sx:{pl:2},children:[e.jsx("li",{children:"One group against a known value"}),e.jsx("li",{children:"Two independent groups"}),e.jsx("li",{children:"Paired measurements (before/after)"})]})]})})})}),e.jsx(h,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(K,{elevation:1,sx:{backgroundColor:Y(l.palette.background.default,.5),height:"100%",transition:"all 0.2s","&:hover":{boxShadow:l.shadows[3]}},children:e.jsx(Z,{sx:{height:"100%",p:1},onClick:()=>V(1),children:e.jsxs(z,{children:[e.jsx(i,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:"medium"},children:"ANOVA Tests"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Compare means across multiple groups or factors. Use for:"}),e.jsxs(i,{variant:"body2",component:"ul",sx:{pl:2},children:[e.jsx("li",{children:"One-way ANOVA (3+ groups)"}),e.jsx("li",{children:"Two-way ANOVA (2 factors)"}),e.jsx("li",{children:"Repeated measures designs"})]})]})})})}),e.jsx(h,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(K,{elevation:1,sx:{backgroundColor:Y(l.palette.background.default,.5),height:"100%",transition:"all 0.2s","&:hover":{boxShadow:l.shadows[3]}},children:e.jsx(Z,{sx:{height:"100%",p:1},onClick:()=>V(2),children:e.jsxs(z,{children:[e.jsx(i,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:"medium"},children:"Non-parametric Tests"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Alternatives when assumptions are violated:"}),e.jsxs(i,{variant:"body2",component:"ul",sx:{pl:2},children:[e.jsx("li",{children:"Mann-Whitney U (vs. independent t-test)"}),e.jsx("li",{children:"Wilcoxon (vs. paired t-test)"}),e.jsx("li",{children:"Kruskal-Wallis (vs. ANOVA)"})]})]})})})}),e.jsx(h,{item:!0,xs:12,sm:6,lg:3,children:e.jsx(K,{elevation:1,sx:{backgroundColor:Y(l.palette.background.default,.5),height:"100%",transition:"all 0.2s","&:hover":{boxShadow:l.shadows[3]}},children:e.jsx(Z,{sx:{height:"100%",p:1},onClick:()=>V(3),children:e.jsxs(z,{children:[e.jsx(i,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:"medium"},children:"Assumption Checker"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Verify if your data meets test requirements:"}),e.jsxs(i,{variant:"body2",component:"ul",sx:{pl:2},children:[e.jsx("li",{children:"Normality tests"}),e.jsx("li",{children:"Homogeneity of variance"}),e.jsx("li",{children:"Sphericity & other assumptions"})]})]})})})})]})}),e.jsxs(I,{elevation:1,sx:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden",borderRadius:2},children:[e.jsxs(Pe,{value:N,onChange:D,"aria-label":"inferential statistics tabs",variant:"fullWidth",sx:{borderBottom:1,borderColor:"divider",backgroundColor:l.palette.background.paper},children:[e.jsx(R,{icon:e.jsx(Ee,{}),iconPosition:"start",label:"t-Tests"}),e.jsx(R,{icon:e.jsx(Ue,{}),iconPosition:"start",label:"ANOVA"}),e.jsx(R,{icon:e.jsx(He,{}),iconPosition:"start",label:"Non-parametric Tests"}),e.jsx(R,{icon:e.jsx(Ge,{}),iconPosition:"start",label:"Assumption Checker"})]}),e.jsxs(u,{sx:{flex:1,overflow:"auto",p:3,display:"flex",flexDirection:"column",height:"100%"},children:[N===0&&e.jsx(u,{sx:{flex:1,height:"100%"},children:e.jsx(Ke,{})}),N===1&&e.jsx(u,{sx:{flex:1,height:"100%"},children:e.jsx(ze,{})}),N===2&&e.jsx(u,{sx:{flex:1,height:"100%"},children:e.jsx(Qe,{})}),N===3&&e.jsx(u,{sx:{flex:1,height:"100%"},children:e.jsx(Ze,{})})]})]})]})};export{Tt as default};
