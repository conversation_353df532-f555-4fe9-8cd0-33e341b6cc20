import{j as m}from"./other-utils-CR9xr_gI.js";import{b as w,d as g,c as q}from"./descriptive-Djo0s6H4.js";const y=(s,o)=>{if(s.length<2)throw new Error("Sample size must be at least 2");const i=s.length,t=w(s),r=q(s,!0)/Math.sqrt(i),l=(t-o)/r,n=i-1,a=2*(1-m.studentt.cdf(Math.abs(l),n)),c=m.studentt.inv(.975,n),f=[t-c*r,t+c*r];return{t:l,df:n,pValue:a,mean:t,se:r,ci95:f}},C=(s,o,i)=>{if(s.length<2||o.length<2)throw new Error("Each group must have at least 2 observations");const t=s.length,e=o.length,r=w(s),l=w(o),n=g(s,!0),a=g(o,!0),c=r-l,{equalVariances:f=!0,alpha:v=.05,alternative:p="two-sided"}=i||{};let d,h,u,T;if(f){T="Student's t-test";const S=((t-1)*n+(e-1)*a)/(t+e-2);u=Math.sqrt(S*(1/t+1/e)),d=c/u,h=t+e-2}else{T="Welch's t-test",u=Math.sqrt(n/t+a/e),d=c/u;const S=Math.pow(n/t,2)/(t-1),j=Math.pow(a/e,2)/(e-1);h=Math.pow(n/t+a/e,2)/(S+j)}let M;p==="less"?M=m.studentt.cdf(d,h):p==="greater"?M=1-m.studentt.cdf(d,h):M=2*(1-m.studentt.cdf(Math.abs(d),h));const b=m.studentt.inv(1-v/2,h),D=[c-b*u,c+b*u],V=Math.sqrt(((t-1)*n+(e-1)*a)/(t+e-2)),E=Math.abs(c)/V;return{t:d,df:h,pValue:M,meanDifference:c,se:u,ci:D,cohensD:E,testType:T}},F=(s,o)=>{if(s.length!==o.length)throw new Error("Paired samples must have the same length");if(s.length<2)throw new Error("Sample size must be at least 2");const i=s.map((d,h)=>d-o[h]),t=i.length,e=w(i),r=q(i,!0),l=r/Math.sqrt(t),n=e/l,a=t-1,c=2*(1-m.studentt.cdf(Math.abs(n),a)),f=m.studentt.inv(.975,a),v=[e-f*l,e+f*l],p=Math.abs(e)/r;return{t:n,df:a,pValue:c,meanDifference:e,se:l,ci95:v,cohensD:p}};export{C as i,y as o,F as p};
