import{g as X,r as Y}from"./react-libs-Cr2nE3UY.js";import{p as Z}from"./mui-libs-CfwFIaTD.js";import{U as ee}from"./PivotAnalysisViewer-CUgNzlbS.js";function te(j,g){for(var c=0;c<g.length;c++){const p=g[c];if(typeof p!="string"&&!Array.isArray(p)){for(const o in p)if(o!=="default"&&!(o in j)){const w=Object.getOwnPropertyDescriptor(p,o);w&&Object.defineProperty(j,o,w.get?w:{enumerable:!0,get:()=>p[o]})}}}return Object.freeze(Object.defineProperty(j,Symbol.toStringTag,{value:"Module"}))}var S={exports:{}};(function(j,g){Object.defineProperty(g,"__esModule",{value:!0});var c=function(){function e(t,i){for(var r=0;r<i.length;r++){var a=i[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}return function(t,i,r){return i&&e(t.prototype,i),r&&e(t,r),t}}();g.default=J;var p=Y,o=A(p),w=Z,d=A(w),v=ee;function A(e){return e&&e.__esModule?e:{default:e}}function K(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function N(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function B(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function s(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,a=function(O){B(n,O);function n(){return K(this,n),N(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return c(n,[{key:"render",value:function(){var u=new v.PivotData(this.props),D=u.getRowKeys(),_=u.getColKeys(),y=r?_:D;y.length===0&&y.push([]);var m=r?D:_;m.length===0&&m.push([]);var f=this.props.aggregatorName,F=this.props.aggregators[f]([])().numInputs||0;F!==0&&(f+=" of "+this.props.vals.slice(0,F).join(", "));var b=y.map(function(h){var l=[],P=[],M=!0,G=!1,H=void 0;try{for(var U=m[Symbol.iterator](),W;!(M=(W=U.next()).done);M=!0){var C=W.value,$=parseFloat(u.getAggregator(r?C:h,r?h:C).value());l.push(isFinite($)?$:null),P.push(C.join("-")||" ")}}catch(V){G=!0,H=V}finally{try{!M&&U.return&&U.return()}finally{if(G)throw H}}var T={name:h.join("-")||f};return t.type==="pie"?(T.values=l,T.labels=P.length>1?P:[f]):(T.x=r?l:P,T.y=r?P:l),Object.assign(T,t)}),E=f,I=r?this.props.rows.join("-"):this.props.cols.join("-"),q=r?this.props.cols.join("-"):this.props.rows.join("-");I!==""&&(E+=" vs "+I),q!==""&&(E+=" by "+q);var x={title:E,hovermode:"closest",width:window.innerWidth/1.5,height:window.innerHeight/1.4-50};if(t.type==="pie"){var R=Math.ceil(Math.sqrt(b.length)),Q=Math.ceil(b.length/R);x.grid={columns:R,rows:Q},b.forEach(function(h,l){h.domain={row:Math.floor(l/R),column:l-R*Math.floor(l/R)},b.length>1&&(h.title=h.name)}),b[0].labels.length===1&&(x.showlegend=!1)}else x.xaxis={title:r?f:null,automargin:!0},x.yaxis={title:r?null:f,automargin:!0};return o.default.createElement(e,{data:b,layout:Object.assign(x,i,this.props.plotlyOptions),config:this.props.plotlyConfig,onUpdate:this.props.onRendererUpdate})}}]),n}(o.default.PureComponent);return a.defaultProps=Object.assign({},v.PivotData.defaultProps,{plotlyOptions:{},plotlyConfig:{}}),a.propTypes=Object.assign({},v.PivotData.propTypes,{plotlyOptions:d.default.object,plotlyConfig:d.default.object,onRendererUpdate:d.default.func}),a}function L(e){var t=function(i){B(r,i);function r(){return K(this,r),N(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return c(r,[{key:"render",value:function(){var O=new v.PivotData(this.props),n=O.getRowKeys(),k=O.getColKeys();n.length===0&&n.push([]),k.length===0&&k.push([]);var u={x:[],y:[],text:[],type:"scatter",mode:"markers"};n.map(function(_){k.map(function(y){var m=O.getAggregator(_,y).value();m!==null&&(u.x.push(y.join("-")),u.y.push(_.join("-")),u.text.push(m))})});var D={title:this.props.rows.join("-")+" vs "+this.props.cols.join("-"),hovermode:"closest",xaxis:{title:this.props.cols.join("-"),automargin:!0},yaxis:{title:this.props.rows.join("-"),automargin:!0},width:window.innerWidth/1.5,height:window.innerHeight/1.4-50};return o.default.createElement(e,{data:[u],layout:Object.assign(D,this.props.plotlyOptions),config:this.props.plotlyConfig,onUpdate:this.props.onRendererUpdate})}}]),r}(o.default.PureComponent);return t.defaultProps=Object.assign({},v.PivotData.defaultProps,{plotlyOptions:{},plotlyConfig:{}}),t.propTypes=Object.assign({},v.PivotData.propTypes,{plotlyOptions:d.default.object,plotlyConfig:d.default.object,onRendererUpdate:d.default.func}),t}function J(e){return{"Grouped Column Chart":s(e,{type:"bar"},{barmode:"group"}),"Stacked Column Chart":s(e,{type:"bar"},{barmode:"relative"}),"Grouped Bar Chart":s(e,{type:"bar",orientation:"h"},{barmode:"group"},!0),"Stacked Bar Chart":s(e,{type:"bar",orientation:"h"},{barmode:"relative"},!0),"Line Chart":s(e),"Dot Chart":s(e,{mode:"markers"},{},!0),"Area Chart":s(e,{stackgroup:1}),"Scatter Chart":L(e),"Multiple Pie Chart":s(e,{type:"pie",scalegroup:1,hoverinfo:"label+value",textinfo:"none"},{},!0)}}j.exports=g.default})(S,S.exports);var z=S.exports;const re=X(z),ie=te({__proto__:null,default:re},[z]);export{ie as P};
